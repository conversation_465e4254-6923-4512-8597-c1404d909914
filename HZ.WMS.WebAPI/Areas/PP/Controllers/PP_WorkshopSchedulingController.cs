using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.Http;
using DevExpress.DataAccess.Sql;
using DevExpress.XtraReports.UI;
using HZ.Core.Http;
using HZ.Core.Office;
using HZ.WMS.Application;
using HZ.WMS.Application.PP;
using HZ.WMS.Entity.PP.ViewModel;
using HZ.WMS.WebAPI.Controllers;
using SqlSugar;

namespace HZ.WMS.WebAPI.Areas.PP.Controllers
{
    /// <summary>
    /// 车间执行排产
    /// </summary>
    public class PP_WorkshopSchedulingController : ApiBaseController
    {
        private PP_ProductionOrderApp _app = new PP_ProductionOrderApp();
        private PP_AssignSerialNoViewApp _appView = new PP_AssignSerialNoViewApp();

        #region 获取数据

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]string keyword, [FromUri]string ProductionLineCode, [FromUri]string ProductionScheduler, [FromUri]string OrderNo, [FromUri]string ContractNo,

            [FromUri] DateTime[] dateValue, [FromUri] DateTime[] createDateValue)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0] == null ? DateTime.Now.Date : querDateTimes[0];
                DateTime toTime = querDateTimes[1] == null ? fromTime.AddDays(3) : querDateTimes[1];
                var querCreateDateTimes = FormatProcessor.QueryDateTimesFormat(createDateValue);
                DateTime createFromTime = querCreateDateTimes[0] == null ? DateTime.Now.Date : querCreateDateTimes[0];
                DateTime createToTime = querCreateDateTimes[1] == null ? fromTime.AddDays(3) : querCreateDateTimes[1];

                var itemsData = _app.GetPageScheduling(page, keyword, ProductionLineCode, ProductionScheduler, OrderNo, ContractNo, "", "",fromTime, toTime, createFromTime, createToTime);

                itemsData = _appView.HandleEapSerialNo(itemsData);

                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 打印机座（定子组件）身份卡

        /// <summary>
        /// 打印机座（定子组件）身份卡
        /// </summary>
        /// <param name="docNums">工单号</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult PrintStatorLable([FromUri]string[] docNums)
        {
            var result = new ResponseData();
            try
            {
                var docStr = string.Join(",", docNums);
                XtraReport report = XtraReport.FromFile(HttpContext.Current.Server.MapPath("~/") + ConfigurationManager.AppSettings["LabelTemplateUploadPath"]?.ToString() + "机座身份卡.repx", true);
                SqlDataSource dataSource = report.DataSource as SqlDataSource;
                QueryParameter parameter = new QueryParameter()
                {
                    Name = "@docNums",
                    Type = typeof(string),
                    Value = docStr
                };
                dataSource.Queries[0].Parameters[0] = parameter;
                return base.PrintToPDF(report);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 打印转子组件身份卡

        /// <summary>
        /// 打印转子组件身份卡
        /// </summary>
        /// <param name="docNums">工单号</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult PrintRotorLable([FromUri]string[] docNums)
        {
            var result = new ResponseData();
            try
            {
                var docStr = string.Join(",", docNums);
                XtraReport report = XtraReport.FromFile(HttpContext.Current.Server.MapPath("~/") + ConfigurationManager.AppSettings["LabelTemplateUploadPath"]?.ToString() + "转子身份卡.repx", true);
                SqlDataSource dataSource = report.DataSource as SqlDataSource;
                QueryParameter parameter = new QueryParameter()
                {
                    Name = "@docNums",
                    Type = typeof(string),
                    Value = docStr
                };
                dataSource.Queries[0].Parameters[0] = parameter;
                return base.PrintToPDF(report);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 打印入库标签

        /// <summary>
        /// 打印入库标签
        /// </summary>
        /// <param name="docNums">序列号</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult PrintStorageLabel([FromUri]List<string> docNums)
        {
            var result = new ResponseData();
            try
            {
                var list = _app.GetStorageLabel(docNums);
                var sourceList = new List<object>();
                foreach (DataRow item in list.Rows)
                {
                    sourceList.Add(new { SerialNo = item["SerialNo"], PartCode = item["PartCode"], ContractNo = item["ContractNo"] });
                }
                XtraReport report = XtraReport.FromFile(HttpContext.Current.Server.MapPath("~/") + ConfigurationManager.AppSettings["LabelTemplateUploadPath"]?.ToString() + "完工入库标签.repx", true);
                report.DataSource = sourceList;
                return base.PrintToPDF(report);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 导出主机排产单

        /// <summary>
        /// 导出主机排产单
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri]string keyword, [FromUri]string ProductionLineCode, [FromUri]string ProductionScheduler, [FromUri]string OrderNo, [FromUri]string ContractNo,  [FromUri]DateTime[] dateValue, [FromUri] DateTime[] createDateValue)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];
                Boolean createDateValueBool = false;
                if (createDateValue == null || createDateValue.Length == 0)
                {
                    createDateValueBool = true;
                }
                var querCreateDateTimes = FormatProcessor.QueryDateTimesFormat(createDateValue);
                DateTime createFromTime = querCreateDateTimes[0] == null ? DateTime.Now.Date : querCreateDateTimes[0];
                DateTime createToTime = querCreateDateTimes[1] == null ? fromTime.AddDays(3) : querCreateDateTimes[1];

                string tableName = "PP_ExportHostScheduling_View";

                if (ProductionScheduler == "101")
                {
                    tableName = "PP_ExportHostScheduling_View_sale";
                }
                else if (ProductionScheduler == "201")
                {
                    tableName = "PP_ExportHostScheduling_View_part";
                }

                string sql = "select * from " + tableName + " where StartTime >= @fromTime and StartTime < @toTime ";

                if (!string.IsNullOrEmpty(keyword))
                {
                    sql += "and (SerialNo like @keyword or ProductionOrderNo like @keyword or MaterialNo like @keyword or MaterialName like @keyword or Shipper like @keyword) ";
                }

                if (!string.IsNullOrEmpty(ContractNo))
                {
                    sql += "and ContractNo like @ContractNo ";
                }

                if (!string.IsNullOrEmpty(ProductionLineCode))
                {
                    sql += "and (AssemblyLineNo like @ProductionLineCode or ProductionLineCode like @ProductionLineCode) ";
                }

                if (!string.IsNullOrEmpty(ProductionScheduler))
                {
                    sql += "and ProductionScheduler = @ProductionScheduler ";
                }

                if (!createDateValueBool)
                {
                    sql += "and CTime >= @createFromTime and CTime < @createToTime ";
                }
                
                sql += "order by ProductionOrderNo asc, SerialNo asc ";
                SugarParameter[] param =
                {
                    new SugarParameter("@fromTime", fromTime),
                    new SugarParameter("@toTime", toTime),
                    new SugarParameter("@keyword", "%" + keyword + "%"),
                    new SugarParameter("@ContractNo", "%" + ContractNo + "%"),
                    new SugarParameter("@ProductionLineCode", "%" + ProductionLineCode + "%"),
                    new SugarParameter("@ProductionScheduler", ProductionScheduler),
                    new SugarParameter("@createFromTime", createFromTime),
                    new SugarParameter("@createToTime", createToTime),
                };


                List<PP_ExportHostScheduling_View> itemsData = _app.DbContext.Ado.SqlQuery<PP_ExportHostScheduling_View>(sql, param).ToList();

                // var itemsData = _app.ExportHostScheduling(null,
                //     w => (string.IsNullOrEmpty(keyword)
                //     || w.SerialNo.Contains(keyword)
                //     || w.ProductionOrderNo.Contains(keyword)
                //     || w.MaterialNo.Contains(keyword)
                //     || w.MaterialName.Contains(keyword)
                //     || w.Shipper.Contains(keyword)
                //     )
                //     && (string.IsNullOrEmpty(OrderNo) || w.OrderNo.Contains(OrderNo))
                //     && (string.IsNullOrEmpty(ContractNo) || w.ContractNo.Contains(ContractNo))
                //     && (string.IsNullOrEmpty(ProductionLineCode) || w.AssemblyLineNo.Contains(ProductionLineCode) || w.ProductionLineCode.Contains(ProductionLineCode))
                //     && (string.IsNullOrEmpty(ProductionScheduler) || w.ProductionScheduler == ProductionScheduler)
                //     && (createDateValueBool || (w.CTime >= createFromTime && w.CTime < createToTime))
                //     && (w.StartTime >= fromTime && w.StartTime < toTime)).ToList();
                itemsData = _app.HandleEapSerialNo(itemsData);
                List<ExcelColumn<PP_ExportHostScheduling_View>> columns = ExcelService.FetchDefaultColumnList<PP_ExportHostScheduling_View>();
                string[] ignoreField = new string[]
                {
                    "StartTime"
                    ,"ProductionOrderNo"
                    ,"ProductionLineCode"
                    ,"ProductionScheduler"
                };
                List<ExcelColumn<PP_ExportHostScheduling_View>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<PP_ExportHostScheduling_View> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }

                ExcelColumn<PP_ExportHostScheduling_View> column = columns[39];
                columns.RemoveAt(39);
                columns.Add(column);
                var dic = new Dictionary<string, List<PP_ExportHostScheduling_View>>();
                //混装线和标准线合并为常规线
                foreach (var item in itemsData)
                {
                    if (item.AssemblyLineNo.Contains("标准装配线") || item.AssemblyLineNo.Contains("混装装配线"))
                    {
                        var key = "常规装配线";
                        if (dic.Keys.Contains(key))
                        {
                            dic[key].Add(item);
                        }
                        else
                        {
                            dic.Add(key, new List<PP_ExportHostScheduling_View> { item });
                        }
                    }
                    else
                    {
                        var key = item.AssemblyLineNo;
                        if (dic.Keys.Contains(key))
                        {
                            dic[key].Add(item);
                        }
                        else
                        {
                            dic.Add(key, new List<PP_ExportHostScheduling_View> { item });
                        }
                    }
                }

                //排序
                var _key = $"常规装配线";
                if (dic.Keys.Contains(_key))
                {
                    dic[_key] = dic[_key].OrderByDescending(o => o.AssemblyLineNo).ToList();
                }
                dic = dic.OrderBy(o => o.Key).ToDictionary(p => p.Key, o => o.Value);

                //序号
                foreach (var item in dic.Keys)
                {
                    int line = 1;
                    foreach (var val in dic[item])
                    {
                        val.SequenceNo = line;
                        line++;
                    }
                }
                //文件名称
                string title = $"正式排产：{DateTime.Now.GetDateTimeFormats('M')[0].ToString()}主机装配指令单（车间{dateValue[0].GetDateTimeFormats('M')[0].ToString()}-{dateValue[1].GetDateTimeFormats('M')[0].ToString()}装配)";
                if (dateValue[0] == dateValue[1])
                {
                    title = $"正式排产：{DateTime.Now.GetDateTimeFormats('M')[0].ToString()}主机装配指令单（车间{dateValue[0].GetDateTimeFormats('M')[0].ToString()}装配)";
                }
                return ExportToExcelFileByGroup<string, PP_ExportHostScheduling_View>(dic, columns, title);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #region 导出主机总装线/巨通线/蒂森线

        /// <summary>
        /// 导出主机总装线/巨通线/蒂森线
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFileForHostLine([FromUri]string keyword, [FromUri]string ProductionLineCode, [FromUri]string ProductionScheduler, [FromUri]string OrderNo, [FromUri]string ContractNo, [FromUri] DateTime[] dateValue)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];

                var itemsData = _app.ExportHostLine(null, fromTime, toTime,
                    w => (string.IsNullOrEmpty(keyword)
                    || w.SerialNo.Contains(keyword)
                    || w.ProductionOrderNo.Contains(keyword)
                    || w.MaterialName.Contains(keyword)
                    || w.Shipper.Contains(keyword))
                    && (string.IsNullOrEmpty(ContractNo) || w.ContractNo.Contains(ContractNo))
                    && (string.IsNullOrEmpty(ProductionLineCode) || w.ProductionLineCode.Contains(ProductionLineCode) || w.AssemblyLineNo.Contains(ProductionLineCode))
                    && (string.IsNullOrEmpty(ProductionScheduler) || w.ProductionScheduler == ProductionScheduler)).ToList();
                List<ExcelColumn<PP_ExportHostLine_View>> columns = ExcelService.FetchDefaultColumnList<PP_ExportHostLine_View>();
                string[] ignoreField = new string[]
                {
                    "StartTime"
                    ,"ProductionOrderNo"
                    ,"ProductionLineCode"
                    ,"ProductionScheduler"
                };
                List<ExcelColumn<PP_ExportHostLine_View>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<PP_ExportHostLine_View> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                //return ExportToExcelFile<PP_ExportHostLine_View>(itemsData, columns, $"物料分解单：{DateTime.Now.GetDateTimeFormats('M')[0].ToString()}常规线主机物料分解明细");
                var dic = new Dictionary<string, List<PP_ExportHostLine_View>>();
                //混装线和标准线合并为常规线
                foreach (var item in itemsData)
                {
                    if (item.AssemblyLineNo.Contains("标准装配线") || item.AssemblyLineNo.Contains("混装装配线"))
                    {
                        var key = "常规装配线";
                        if (dic.Keys.Contains(key))
                        {
                            dic[key].Add(item);
                        }
                        else
                        {
                            dic.Add(key, new List<PP_ExportHostLine_View> { item });
                        }
                    }
                    else
                    {
                        var key = item.AssemblyLineNo;
                        if (dic.Keys.Contains(key))
                        {
                            dic[key].Add(item);
                        }
                        else
                        {
                            dic.Add(key, new List<PP_ExportHostLine_View> { item });
                        }
                    }
                }

                //排序
                var _key = $"常规装配线";
                if (dic.Keys.Contains(_key))
                {
                    dic[_key] = dic[_key].OrderByDescending(o => o.AssemblyLineNo).ToList();
                }
                dic = dic.OrderBy(o => o.Key).ToDictionary(p => p.Key, o => o.Value);

                //文件名称
                string title = $"物料分解单：{dateValue[0].GetDateTimeFormats('M')[0].ToString()}-{dateValue[1].GetDateTimeFormats('M')[0].ToString()}常规线主机物料分解明细";
                if (dateValue[0] == dateValue[1])
                {
                    title = $"物料分解单：{dateValue[0].GetDateTimeFormats('M')[0].ToString()}常规线主机物料分解明细";
                }
                return ExportToExcelFileByGroup<string, PP_ExportHostLine_View>(dic, columns, title);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #region 导出DT线

        /// <summary>
        /// 导出DT线
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFileForDTLine([FromUri]string keyword, [FromUri]string ProductionLineCode, [FromUri]string ProductionScheduler, [FromUri]string OrderNo, [FromUri]string ContractNo, [FromUri] DateTime[] dateValue)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];

                var itemsData = _app.ExportDTLine(null, fromTime, toTime,
                    w => (string.IsNullOrEmpty(keyword)
                     || w.SerialNo.Contains(keyword)
                    || w.ProductionOrderNo.Contains(keyword)
                    || w.MaterialName.Contains(keyword)
                    || w.Shipper.Contains(keyword))
                    && (string.IsNullOrEmpty(ContractNo) || w.ContractNo.Contains(ContractNo))
                    && (string.IsNullOrEmpty(ProductionLineCode) || w.ProductionLineCode.Contains(ProductionLineCode) || w.AssemblyLineNo.Contains(ProductionLineCode))
                    && (string.IsNullOrEmpty(ProductionScheduler) || w.ProductionScheduler == ProductionScheduler)).ToList();
                List<ExcelColumn<PP_ExportDTLine_View>> columns = ExcelService.FetchDefaultColumnList<PP_ExportDTLine_View>();
                string[] ignoreField = new string[]
                {
                    "StartTime"
                    ,"ProductionOrderNo"
                    ,"ProductionLineCode"
                    ,"ProductionScheduler"
                };
                List<ExcelColumn<PP_ExportDTLine_View>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<PP_ExportDTLine_View> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                //return ExportToExcelFile<PP_ExportDTLine_View>(itemsData, columns, "DT线");
                var dic = new Dictionary<string, List<PP_ExportDTLine_View>>();
                foreach (var item in itemsData)
                {
                    var key = item.AssemblyLineNo;
                    if (dic.Keys.Contains(key))
                    {
                        dic[key].Add(item);
                    }
                    else
                    {
                        dic.Add(key, new List<PP_ExportDTLine_View> { item });
                    }
                }

                //文件名称
                string title = $"物料分解单：{dateValue[0].GetDateTimeFormats('M')[0].ToString()}-{dateValue[1].GetDateTimeFormats('M')[0].ToString()}DT线主机物料分解明细";
                if (dateValue[0] == dateValue[1])
                {
                    title = $"物料分解单：{dateValue[0].GetDateTimeFormats('M')[0].ToString()}DT线主机物料分解明细";
                }
                return ExportToExcelFileByGroup<string, PP_ExportDTLine_View>(dic, columns, title);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #region 导出试制返修线

        /// <summary>
        /// 导出试制返修线
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFileForRepairLine([FromUri]string keyword, [FromUri]string ProductionLineCode, [FromUri]string ProductionScheduler, [FromUri]string OrderNo, [FromUri]string ContractNo, [FromUri] DateTime[] dateValue)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];

                var itemsData = _app.ExportRepairLine(null, fromTime, toTime,
                    w => (string.IsNullOrEmpty(keyword)
                    || w.SerialNo.Contains(keyword)
                    || w.ProductionOrderNo.Contains(keyword)
                    || w.MaterialName.Contains(keyword)
                    || w.Shipper.Contains(keyword))
                    && (string.IsNullOrEmpty(ContractNo) || w.ContractNo.Contains(ContractNo))
                    && (string.IsNullOrEmpty(ProductionLineCode) || w.ProductionLineCode.Contains(ProductionLineCode) || w.AssemblyLineNo.Contains(ProductionLineCode))
                    && (string.IsNullOrEmpty(ProductionScheduler) || w.ProductionScheduler == ProductionScheduler)).ToList();
                List<ExcelColumn<PP_ExportRepairLine_View>> columns = ExcelService.FetchDefaultColumnList<PP_ExportRepairLine_View>();
                string[] ignoreField = new string[]
                {
                    "StartTime"
                    ,"ProductionOrderNo"
                    ,"ProductionLineCode"
                    ,"ProductionScheduler"
                };
                List<ExcelColumn<PP_ExportRepairLine_View>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<PP_ExportRepairLine_View> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                //return ExportToExcelFile<PP_ExportRepairLine_View>(itemsData, columns, "试制返修线");
                var dic = new Dictionary<string, List<PP_ExportRepairLine_View>>();
                foreach (var item in itemsData)
                {
                    var key = item.AssemblyLineNo;
                    if (dic.Keys.Contains(key))
                    {
                        dic[key].Add(item);
                    }
                    else
                    {
                        dic.Add(key, new List<PP_ExportRepairLine_View> { item });
                    }
                }

                //文件名称
                string title = $"物料分解单：{dateValue[0].GetDateTimeFormats('M')[0].ToString()}-{dateValue[1].GetDateTimeFormats('M')[0].ToString()}试制返修线主机物料分解明细";
                if (dateValue[0] == dateValue[1])
                {
                    title = $"物料分解单：{dateValue[0].GetDateTimeFormats('M')[0].ToString()}试制返修线主机物料分解明细";
                }
                return ExportToExcelFileByGroup<string, PP_ExportRepairLine_View>(dic, columns, title);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion
    }
}