using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.SAP
{
    /// <summary>
    ///成本中心
    /// </summary>
    [SugarTable("XZ_SAP_CSKS")]
    public class XZ_SAP_CSKS
    {
        /// <summary>
        /// 成本中心
        /// </summary>
        [Description("成本中心")]
        public string KOSTL { get; set; }

        /// <summary>
        /// 成本中心描述
        /// </summary>
        [Description("成本中心描述")]
        public string LTEXT { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        [Description("状态")]
        public bool? Status { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [Description("创建时间")]
        public DateTime? CDate { get; set; }

    }
}
