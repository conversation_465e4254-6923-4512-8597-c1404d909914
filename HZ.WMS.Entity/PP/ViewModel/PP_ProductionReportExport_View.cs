using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.PP
{
    /// <summary>
    /// 生产报工
    /// </summary>
    public class PP_ProductionReportExport_View : PP_ProductionReport
    {
        
        /// <summary>
        /// 销售订单
        /// </summary>
        [Description("销售订单")]
        public string SalesOrderNo { get; set; }
        /// <summary>
        /// 销售订单行
        /// </summary>
        [Description("销售订单行")]
        public string SalesOrderLineNo { get; set; }
        /// <summary>
        /// 合同号
        /// </summary>
        [Description("合同号")]
        public string ZORD_CONT { get; set; }
        /// <summary>
        /// 客户订单号
        /// </summary>
        [Description("客户订单号")]
        public string CustomerOrderNum { get; set; }

        /// <summary>
        ///  EAP出场编号
        /// </summary>
        [Description("EAP出厂编号")]
        public string EapSerialNo { get; set; }

    }
}
