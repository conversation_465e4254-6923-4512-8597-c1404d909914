using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.SAP
{
    public class ZFGWMS001
    {
        /// <summary>
        /// 交货 (销售订单号)
        /// </summary>
        public string VBELN { get; set; }

        /// <summary>
        /// 创建对象的人员名称
        /// </summary>
        public string ERNAM { get; set; }
        /// <summary>
        /// 输入时间
        /// </summary>
        public DateTime ERZET { get; set; }
        /// <summary>
        /// 记录建立日期
        /// </summary>
        public DateTime ERDAT { get; set; }
        /// <summary>
        /// 装运点/收货点
        /// </summary>
        public string VSTEL { get; set; }
        /// <summary>
        /// 销售组织
        /// </summary>
        public string VKORG { get; set; }
        /// <summary>
        /// 交货类型
        /// </summary>
        public string LFART { get; set; }
        /// <summary>
        /// 装载日期
        /// </summary>
        public DateTime LDDAT { get; set; }
        /// <summary>
        /// 运输计划日期
        /// </summary>
        public DateTime TDDAT { get; set; }
        /// <summary>
        /// 交货日期
        /// </summary>
        public DateTime LFDAT { get; set; }
        /// <summary>
        /// 拣配日期
        /// </summary>
        public DateTime KODAT { get; set; }
        /// <summary>
        /// 销售与分销凭证中的开票冻结
        /// </summary>
        public string FAKSK { get; set; }
        /// <summary>
        /// 交货冻结（凭证抬头）
        /// </summary>
        public string LIFSK { get; set; }
        /// <summary>
        /// 收货方
        /// </summary>
        public string KUNNR { get; set; }
        /// <summary>
        /// 运输计划 － 时间（本地，与装运点相关） 
        /// </summary>
        public DateTime TDUHR { get; set; }
        /// <summary>
        /// 装载时间（与装运点相关的本地时间） 
        /// </summary>
        public DateTime LDUHR { get; set; }
        /// <summary>
        ///发货时间（本地，与工厂相关）
        /// </summary>
        public DateTime WAUHR { get; set; }
        /// <summary>
        /// 参考凭证编号
        /// </summary>
        public string XBLNR { get; set; }

    }
}
