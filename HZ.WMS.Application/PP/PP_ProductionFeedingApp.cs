using SqlSugar;
using HZ.WMS.Application.MD;
using HZ.WMS.Application.SAP;
using HZ.WMS.Application.Sys;
using HZ.WMS.Entity.PP;
using HZ.WMS.Entity.PP.Dto;
using HZ.WMS.Entity.SAP;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Application.PP
{
    /// <summary>
    /// 生产投料
    /// </summary>
    public class PP_ProductionFeedingApp : BaseApp<PP_ProductionFeeding>
    {
        private PP_ProductionFeedingDetailApp detailApp = new PP_ProductionFeedingDetailApp();
        private Sys_SAPCompanyInfoApp sapApp = new Sys_SAPCompanyInfoApp();

        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public PP_ProductionFeedingApp() : base()
        {
        }

        #endregion

        #region 增加

        public bool Add(PP_ProductionFeeding productionFeeding, List<PP_ProductionFeedingDetail> productionFeedingDetails)
        {
            var mark = false;
            try
            {
                //this.DbContext.Ado.BeginTran();
                this.DbContext.Ado.BeginTran();
                base.Insert(productionFeeding);
                detailApp.Insert(productionFeedingDetails);
                this.DbContext.Ado.CommitTran();
                mark = true;
            }
            catch (Exception ex)
            {
                mark = false;
                this.DbContext.Ado.RollbackTran();
                throw ex;
            }
            return mark;
        }

        #endregion

        #region 删除

        public bool Delete(string user, List<PP_ProductionFeeding> productionFeedings, List<PP_ProductionFeedingDetail> productionFeedingDetails)
        {
            var mark = false;
            try
            {
                //this.DbContext.Ado.BeginTran();
                this.DbContext.Ado.BeginTran();
                base.Delete(productionFeedings, user);
                detailApp.Delete(productionFeedingDetails, user);
                this.DbContext.Ado.CommitTran();
                mark = true;
            }
            catch (Exception ex)
            {
                mark = false;
                this.DbContext.Ado.RollbackTran();
                throw ex;
            }
            return mark;
        }

        #endregion

        #region 编辑

        public bool Edit(PP_ProductionFeeding_Dto productionFeeding, string user)
        {
            bool result = false;
            try
            {
                var entity = new PP_ProductionFeeding
                {
                    ID = productionFeeding.ID,
                    ScanningCode = productionFeeding.ScanningCode,
                    SerialNo = productionFeeding.SerialNo,
                    ProductionFeedingNo = productionFeeding.ProductionFeedingNo,
                    ProductionOrderNo = productionFeeding.ProductionOrderNo,
                    FactoryCode = productionFeeding.FactoryCode,
                    MaterialNo = productionFeeding.MaterialNo,
                    MaterialName = productionFeeding.MaterialName,
                    MovementType = productionFeeding.MovementType,
                    OrderType = productionFeeding.OrderType,
                    OrderQty = productionFeeding.OrderQty,
                    Unit = productionFeeding.Unit,
                    ContractNo = productionFeeding.ContractNo,
                    SalesOrderNo = productionFeeding.SalesOrderNo,
                    Shippers = productionFeeding.Shippers,
                    ProductionLineCode = productionFeeding.ProductionLineCode,
                    ProductionLineDes = productionFeeding.ProductionLineDes,
                    ProductionScheduler = productionFeeding.ProductionScheduler,
                    DeliveryTime = productionFeeding.DeliveryTime,
                    StartTime = productionFeeding.StartTime,
                    ManualPostTime = productionFeeding.ManualPostTime,
                    Remark = productionFeeding.Remark,
                    CUser = productionFeeding.CUser,
                    CTime = productionFeeding.CTime,
                    MUser = user,
                };

                var addList = new List<PP_ProductionFeedingDetail>();
                var updateList = new List<PP_ProductionFeedingDetail>();
                foreach (var item in productionFeeding.DetailList)
                {
                    item.ProductionFeedingNo = productionFeeding.ProductionFeedingNo;
                    if (string.IsNullOrEmpty(item.ID))
                    {
                        item.CUser = user;
                        addList.Add(item);
                    }
                    else
                    {
                        item.MUser = user;
                        updateList.Add(item);
                    }
                }
                var deleteList = detailApp.GetListByKeys(productionFeeding.DelDetailIds);
                if (deleteList != null && deleteList.Count > 0)
                {
                    deleteList.ForEach(item =>
                    {
                        item.DUser = user;
                        item.DTime = DateTime.Now;
                        item.IsDelete = true;
                    });
                    updateList.AddRange(deleteList);
                }

                //this.DbContext.Ado.BeginTran();
                this.DbContext.Ado.BeginTran();
                base.Update(entity);
                detailApp.Update(updateList);
                detailApp.Insert(addList);
                this.DbContext.Ado.CommitTran();
                result = true;
            }
            catch (Exception ex)
            {
                result = false;
                this.DbContext.Ado.RollbackTran();
                throw ex;
            }
            return result;
        }

        #endregion

        #region 生产投料过账

        public bool DoPost(List<PP_ProductionFeeding> entities, string operater, out string error_message)
        {
            error_message = "过账成功";
            try
            {
                //获取有效工单
                var statusList = new Sys_SAPCompanyInfoApp().ZFGWMS019("001", entities.Select(s => s.ProductionOrderNo).ToList());
                var noStatus = statusList.Where(w => w.Value == "1")?.Select(s => s.Key);
                if(noStatus != null && noStatus.Count() > 0)
                {
                    string strStatus = string.Join(",", noStatus);
                    error_message = strStatus + "无效工单不允许过账";
                    return false;
                }
                
                foreach (var entity in entities)
                {
                    int Line = 0;
                    List<ZFGWMS018> zFGWMs = new List<ZFGWMS018>();
                    //var ManualPostTime = entity.ManualPostTime ?? DateTime.Now;
                    var baseNo = entity.ProductionFeedingNo;
                    var detailList = detailApp.GetList(w => w.ProductionFeedingNo == entity.ProductionFeedingNo && string.IsNullOrEmpty(w.IsBackflush)).ToList();
                    if (detailList == null && detailList.Count < 1) continue;
                    foreach (var item in detailList)
                    {
                        if (entity.MovementType == "261")
                        {
                            //获取库位
                            var location = new MD_BinLocationApp().GetList(w => w.WhsCode == item.DeliverLocation).ToList().FirstOrDefault();
                            //调接口减本地库存
                            if (!string.IsNullOrEmpty(item.SalesOrderNo))
                            {
                                var mark = new MD_StockApp().StockOut(item.ComponentCode, location?.BinLocationCode, item.SalesOrderNo, Decimal.ToInt32(item.SalesOrderLineNo ?? 0), item.DemandQty, operater, out error_message);
                                //if (!mark) return mark;
                            }
                            else
                            {
                                var mark = new MD_StockApp().StockOut(item.ComponentCode, location?.BinLocationCode, item.DemandQty, operater, out error_message);
                                //if (!mark) return mark;
                            }
                        }
                        else
                        {
                            //加库存
                            var _location = new MD_BinLocationApp().GetList(w => w.WhsCode == item.DeliverLocation).ToList().FirstOrDefault();
                            var flg = new MD_StockApp().StockIn(
                                new Entity.MD.MD_Stock
                                {
                                    BarCode = "",
                                    BatchNum = "",
                                    ItemCode = item.ComponentCode,
                                    ItemName = item.MaterialName,
                                    Qty = item.DemandQty,
                                    Unit = item.ComponentUnit,
                                    RegionCode = _location?.RegionCode,
                                    RegionName = _location?.RegionName,
                                    BinLocationCode = _location?.BinLocationCode,
                                    BinLocationName = _location?.BinLocationName,
                                    WhsCode = _location?.WhsCode,
                                    WhsName = _location?.WhsName,
                                    SpecialStock = string.IsNullOrEmpty(item.SalesOrderNo) ? "" : "E",
                                    SaleNum = item.SalesOrderNo,
                                    SaleLine = Decimal.ToInt32(item.SalesOrderLineNo ?? 0),
                                }, operater, out error_message);
                            //if (!flg) return flg;
                        }
                        error_message = "过账成功";

                        Line++;
                        item.FeedingLineNo = Line;

                        zFGWMs.Add(new ZFGWMS018
                        {
                            ZNUM = Line,
                            RSNUM = Convert.ToInt32(item.ReservedNo),
                            RSPOS = Decimal.ToInt32(item.ComponentLineNo),
                            MATNR = item.ComponentCode,
                            WERKS = item.FactoryCode,
                            BWART = entity.MovementType,//261投料  262：退料
                            MENGE = item.DemandQty,
                            MEINS = item.ComponentUnit,
                            LGORT = item.DeliverLocation,
                            UMLGO = "",//无用字段
                            SOBKZ = item.SpecialInventory,
                            KDAUF = item.SalesOrderNo,
                            KDPOS = Decimal.ToInt32(item.SalesOrderLineNo ?? 0),
                            BWTAR = item.AssessmentType,
                            SGTXT = item.Remark,
                        });
                    }

                    var isPosted = false;
                    DateTime date = DateTime.Now;
                    //查询Sap账期时间
                    DateTime ManualPostTime = new SAPApp().GetSAPpostTime(Convert.ToDateTime(entity.ManualPostTime ?? DateTime.Now));
                    var result = sapApp.ZFGWMS018("001", baseNo, baseNo, ManualPostTime, zFGWMs, out isPosted, out error_message);

                    if (result != null && result.Count > 0)
                    {
                        List<PP_ProductionFeedingDetail> querydetails = new List<PP_ProductionFeedingDetail>();
                        foreach (var sap in result)
                        {
                            var querydetail = detailList.FirstOrDefault(x => x.ProductionFeedingNo == sap.DocNum && x.FeedingLineNo == sap.line);
                            if (querydetail != null)
                            {
                                querydetail.IsPosted = true;
                                querydetail.PostUser = operater;
                                querydetail.PostTime = date;
                                querydetail.ManualPostTime = ManualPostTime;
                                querydetail.MUser = operater;
                                querydetail.SapDocNum = sap.sapDocNum;
                                querydetail.SapLine = sap.sapline;
                                querydetails.Add(querydetail);
                            }
                        }

                        entity.IsPosted = true;
                        entity.PostUser = operater;
                        entity.PostTime = date;
                        entity.MUser = operater;
                        entity.MTime = date;
                        entity.ManualPostTime = ManualPostTime;

                        this.DbContext.Ado.BeginTran();
                        Update(entity);
                        detailApp.Update(querydetails);
                        this.DbContext.Ado.CommitTran();
                    }
                    else
                    {
                        error_message = "单号：" + baseNo + "，过账失败，失败原因：" + error_message;
                        return isPosted;
                    }
                }
            }
            catch (Exception ex)
            {
                this.DbContext.Ado.RollbackTran();
                error_message = ex.InnerException?.Message ?? ex.Message;
                return false;
            }
            return true;
        }

        #endregion
    }
}
