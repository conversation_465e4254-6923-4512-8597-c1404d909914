using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;


using HZ.WMS.Entity.MD;
using HZ.WMS.Application.MD;
using HZ.Core.Office;
using HZ.Core.Http;
using HZ.WMS.Entity.Sys;
using HZ.WMS.WebAPI.Controllers;

namespace HZ.WMS.WebAPI.Areas.MD.Controllers
{
    /// <summary>
    /// 供应商
    /// </summary>
    public class MD_CustomerController : ApiBaseController
    {
        private MD_CustomerApp _app = new MD_CustomerApp();

        #region 查询分页列表

        /// <summary>
        /// 查询分页列表 
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]string keyword)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetPageList(page, x => string.IsNullOrEmpty(keyword)
                || x.InternalID.Contains(keyword)
                || x.FamilyName.Contains(keyword)
                || x.FirstLineName.Contains(keyword)
                )?.ToList();

                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 数据同步
        /// <summary>
        /// 客户主数据同步
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult SyncCustomer()
        {
            var result = new ResponseData();
            try
            {
                _app.SyncCustomer();
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }



        #endregion

    }
}
