using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Web;
using System.Web.Http;
using DevExpress.DataAccess.Sql;
using DevExpress.XtraReports.UI;
using HZ.Core.Http;
using HZ.Core.Office;
using HZ.WMS.Application;
using HZ.WMS.Application.SD;
using HZ.WMS.Entity.SD;
using HZ.WMS.Entity.SD.Parameters;
using HZ.WMS.Entity.SD.ViewModel;
using HZ.WMS.Entity.Sys;
using HZ.WMS.WebAPI.Controllers;

namespace HZ.WMS.WebAPI.Areas.SD.Controllers
{
    /// <summary>
    /// 销售交货
    /// </summary>
    public class Sale_DeliveryController : ApiBaseController
    {
        #region 初始化

        private Sale_DeliveryApp _app = new Sale_DeliveryApp();

        #endregion

        #region PC

        #region 查询分页列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="page">分页信息</param>
        /// <param name="BaseNum">销售单号</param>
        /// <param name="CustomerName">客户名称</param>
        /// <param name="ItemCode">物料编号</param>
        /// <param name="BarCode">出厂编号</param>
        /// <param name="CONT">出厂编号</param>
        /// <param name="dateValue">日期区间</param>
        /// <param name="isPosted">是否过账 0:未过账 1：已过帐</param>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]string BaseNum, [FromUri]string CustomerName, 
            [FromUri]string ItemCode, [FromUri]string BarCode, [FromUri]string CONT, [FromUri] DateTime[] dateValue, [FromUri] DateTime[] deliveryDateValue, 
            [FromUri] bool? isPosted,[FromUri] string SAPmark, [FromUri] string  CUser,[FromUri] string BaseType, [FromUri] string BaseLine)
        {
            var result = new ResponseData();
            try
            {
                var itemsData3 = new List<SD_DeliveryMain_View>();
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];
                var queryDeliveryDateTimes = FormatProcessor.QueryDateTimesFormat(deliveryDateValue);
                DateTime fromDeliveryTime = queryDeliveryDateTimes[0];
                DateTime toDeliveryTime = queryDeliveryDateTimes[1];
                var itemsData = new List<SD_DeliveryScan>();
                if (SAPmark == "正常")
                {
                    itemsData = _app.GetList(t => ((string.IsNullOrEmpty(BaseNum) || t.BaseNum.Contains(BaseNum))
                                         && (string.IsNullOrEmpty(BaseLine) || t.BaseLine==Convert.ToInt32(BaseLine))
                                         && (string.IsNullOrEmpty(CustomerName) || t.CustomerName.Contains(CustomerName))
                                         && (string.IsNullOrEmpty(ItemCode) || t.ItemCode.Contains(ItemCode))
                                         && (string.IsNullOrEmpty(BarCode) || t.BarCode.Contains(BarCode))
                                         && (string.IsNullOrEmpty(CONT) || t.CONT.Contains(CONT))
                                         && (string.IsNullOrEmpty(CUser) || t.CUser.Contains(CUser))
                                         && (string.IsNullOrEmpty(t.SAPmark) || t.SAPmark == "S"))
                                         && (string.IsNullOrEmpty(BaseType) || t.BaseType.Contains(BaseType))
                                         && (t.CTime >= fromTime && t.CTime < toTime)
                                         && (t.DeliveryDate >= fromDeliveryTime && t.DeliveryDate < toDeliveryTime)
                                         && (isPosted == null || t.IsPosted == isPosted)).ToList();
                }
                else if(SAPmark == "错误")
                {

                    itemsData = _app.GetList(t => ((string.IsNullOrEmpty(BaseNum) || t.BaseNum.Contains(BaseNum))
                                               && (string.IsNullOrEmpty(BaseLine) || t.BaseLine == Convert.ToInt32(BaseLine))
                                               && (string.IsNullOrEmpty(CustomerName) || t.CustomerName.Contains(CustomerName))
                                               && (string.IsNullOrEmpty(ItemCode) || t.ItemCode.Contains(ItemCode))
                                               && (string.IsNullOrEmpty(BarCode) || t.BarCode.Contains(BarCode))
                                               && (string.IsNullOrEmpty(CONT) || t.CONT.Contains(CONT))
                                               && (string.IsNullOrEmpty(CUser) || t.CUser.Contains(CUser)))
                                               && (string.IsNullOrEmpty(BaseType) || t.BaseType.Contains(BaseType))
                                               && t.SAPmark == "E"
                                               && (t.CTime >= fromTime && t.CTime < toTime)
                                               && (t.DeliveryDate >= fromDeliveryTime && t.DeliveryDate < toDeliveryTime)
                                               && (isPosted == null || t.IsPosted == isPosted)).ToList();
                }
                else
                {
                    itemsData = _app.GetList(t => ((string.IsNullOrEmpty(BaseNum) || t.BaseNum.Contains(BaseNum))
                                             && (string.IsNullOrEmpty(BaseLine) || t.BaseLine == Convert.ToInt32(BaseLine))
                                             && (string.IsNullOrEmpty(CustomerName) || t.CustomerName.Contains(CustomerName))
                                             && (string.IsNullOrEmpty(ItemCode) || t.ItemCode.Contains(ItemCode))
                                             && (string.IsNullOrEmpty(BarCode) || t.BarCode.Contains(BarCode))
                                             && (string.IsNullOrEmpty(CONT) || t.CONT.Contains(CONT))
                                             && (string.IsNullOrEmpty(CUser) || t.CUser.Contains(CUser)))
                                             && (string.IsNullOrEmpty(BaseType) || t.BaseType.Contains(BaseType))
                                             && (t.CTime >= fromTime && t.CTime < toTime)
                                             && (t.DeliveryDate >= fromDeliveryTime && t.DeliveryDate < toDeliveryTime)
                                             && (isPosted == null || t.IsPosted == isPosted)).ToList();
                }

                var DocNums = itemsData.Select(x => x.DocNum).Distinct().ToArray();
                string strCodes = string.Join(",", DocNums);

                var itemsData2 = _app.GetSDDeliveryMainViewForSQL(page, strCodes);
                result.Data = new ResponsePageData { total = DocNums.Length, items = itemsData2 };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 查询明细分页列表

        /// <summary>
        /// 查询明细分页列表
        /// </summary>
        /// <param name="page">分页</param>
        /// <param name="DocNum">交货单号</param>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IHttpActionResult GetPageDetailList([FromUri]Pagination page, [FromUri]string DocNum)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetPageList(page, t => t.DocNum==DocNum).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion
        
        #region 查询打印信息

        /// <summary>
        /// 查询明细分页列表
        /// </summary>
        /// <param name="page">分页</param>
        /// <param name="DocNum">交货单号</param>
        /// <returns></returns>
        [HttpPost]
        [AllowAnonymous]
        public IHttpActionResult GetPrintInfo([FromBody]SD_DeliveryScanPrint print)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetList(t => print.DocNums.Contains(t.DocNum)).ToList();
                itemsData = _app.HandlePhone(itemsData);
                itemsData = _app.HandleUserName(itemsData);
                foreach (var deliveryScan in itemsData)
                {
                    deliveryScan.ItemName = RemoveLastPartIfQuestionMark(deliveryScan.ItemName);
                }
                result.Data = itemsData;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion
        
        #region 机型处理
        private string RemoveLastPartIfQuestionMark(string str)
        {
            // 判断最后一个字符是否为 '?'
            if (str.EndsWith("?"))
            {
                int lastIndex = str.LastIndexOf('-'); // 找到最后一个 '-' 的索引

                // 如果找到 '-'，则返回去掉最后一个 '-' 后面的部分
                if (lastIndex != -1)
                {
                    return str.Substring(0, lastIndex);
                }
            }
            return str; // 如果最后一位不是 '?', 或没有找到 '-', 返回原字符串
        }
        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="DocNums">根据交货单号数组进行删除</param>
        /// <returns></returns>
        [HttpDelete]
        //[AllowAnonymous]
        public IHttpActionResult Delete([FromBody]string[] DocNums)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                bool bDelete = _app.Delete(DocNums,GetCurrentUser().LoginAccount, out error_message);
                if (!bDelete)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 过账

        /// <summary>
        /// 过账
        /// </summary>
        /// <param name="DocNums">交货单号</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult DoPost([FromBody] string[] DocNums)
        {
            var result = new ResponseData();
            try
            {
                Sys_User sysUser = GetCurrentUser();
                string error_message = "";
                var details =_app.GetList(x => DocNums.Contains(x.DocNum) && x.IsPosted==false).ToList();
                bool postResult = _app.DoPost(details, sysUser.LoginAccount, out error_message);
                if (!postResult)//过账失败==2000，前台处理过账失败后重新刷新界面信息
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                    result.MessageParam = 2010;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2000;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }


        #endregion

        #region 冲销过账

        /// <summary>
        /// 冲销过账
        /// </summary>
        /// <param name="entities">接收的集合</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult PassPost([FromBody] string[] DocNums)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                Sys_User currentUser = GetCurrentUser();

                bool bPost = _app.PassPost(DocNums, currentUser.LoginAccount, out error_message);
                if (!bPost)//过账失败==2000，前台处理过账失败后重新刷新界面信息
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                    result.MessageParam = 2010;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2000;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }

        #endregion

        #region 导出

        /// <summary>
        /// 导出
        /// </summary>
        /// <param name="BaseNum">销售单号</param>
        /// <param name="CustomerName">客户名称</param>
        /// <param name="ItemCode">物料编号</param>
        /// <param name="BarCode">出厂编号</param>
        /// <param name="CONT">出厂编号</param>
        /// <param name="dateValue">日期区间</param>
        /// <param name="isPosted">是否过账 0:未过账 1：已过帐</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri]string BaseNum, [FromUri]string CustomerName, [FromUri]string ItemCode, 
            [FromUri]string BarCode, [FromUri]string CONT, [FromUri] DateTime[] dateValue, [FromUri] bool? isPosted,
            [FromUri] string SAPmark, [FromUri] string CUser, [FromUri] string BaseType, [FromUri] string BaseLine)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];
                var itemsData = new List<SD_DeliveryScan>();
                if (SAPmark == "正常")
                {
                    itemsData = _app.GetList(t => ((string.IsNullOrEmpty(BaseNum) || t.BaseNum.Contains(BaseNum))
                     && (string.IsNullOrEmpty(BaseLine) || t.BaseLine == Convert.ToInt32(BaseLine))
                                          && (string.IsNullOrEmpty(CustomerName) || t.CustomerName.Contains(CustomerName))
                                          && (string.IsNullOrEmpty(ItemCode) || t.ItemCode.Contains(ItemCode))
                                          && (string.IsNullOrEmpty(BarCode) || t.BarCode.Contains(BarCode))
                                          && (string.IsNullOrEmpty(CONT) || t.CONT.Contains(CONT))
                                          && (string.IsNullOrEmpty(CUser) || t.CUser.Contains(CUser))
                                          && (string.IsNullOrEmpty(BaseType) || t.BaseType.Contains(BaseType))
                                          && (string.IsNullOrEmpty(t.SAPmark) || t.SAPmark == "S"))
                                          && (t.CTime >= fromTime && t.CTime <= toTime)
                                          && (isPosted == null || t.IsPosted == isPosted)).ToList();
                }
                else if (SAPmark == "错误")
                {

                    itemsData = _app.GetList(t => ((string.IsNullOrEmpty(BaseNum) || t.BaseNum.Contains(BaseNum))
                     && (string.IsNullOrEmpty(BaseLine) || t.BaseLine == Convert.ToInt32(BaseLine))
                                                && (string.IsNullOrEmpty(CustomerName) || t.CustomerName.Contains(CustomerName))
                                                && (string.IsNullOrEmpty(ItemCode) || t.ItemCode.Contains(ItemCode))
                                                && (string.IsNullOrEmpty(BarCode) || t.BarCode.Contains(BarCode))
                                                && (string.IsNullOrEmpty(CONT) || t.CONT.Contains(CONT))
                                                && (string.IsNullOrEmpty(CUser) || t.CUser.Contains(CUser)))
                                                && (string.IsNullOrEmpty(BaseType) || t.BaseType.Contains(BaseType))
                                                && t.SAPmark == "E"
                                                && (t.CTime >= fromTime && t.CTime <= toTime)
                                                && (isPosted == null || t.IsPosted == isPosted)).ToList();
                }
                else
                {
                    itemsData = _app.GetList(t => ((string.IsNullOrEmpty(BaseNum) || t.BaseNum.Contains(BaseNum))
                     && (string.IsNullOrEmpty(BaseLine) || t.BaseLine == Convert.ToInt32(BaseLine))
                                              && (string.IsNullOrEmpty(CustomerName) || t.CustomerName.Contains(CustomerName))
                                              && (string.IsNullOrEmpty(ItemCode) || t.ItemCode.Contains(ItemCode))
                                              && (string.IsNullOrEmpty(BarCode) || t.BarCode.Contains(BarCode))
                                              && (string.IsNullOrEmpty(CONT) || t.CONT.Contains(CONT))
                                              && (string.IsNullOrEmpty(CUser) || t.CUser.Contains(CUser)))
                                              && (string.IsNullOrEmpty(BaseType) || t.BaseType.Contains(BaseType))
                                              && (t.CTime >= fromTime && t.CTime <= toTime)
                                              && (isPosted == null || t.IsPosted == isPosted)).ToList();
                }
                //var itemsData = _app.GetList().ToList();
                List<ExcelColumn<SD_DeliveryScan>> columns = ExcelService.FetchDefaultColumnList<SD_DeliveryScan>();
                string[] ignoreField = new string[] 
                {
                    "DeliveryScanID", "Line","BaseEntry","BaseType","SalesOrganization","BatchNum","ProjectCategory","IsDelivery",
                    "SapLine","CompanyCode","FactoryCode","ProfitCenter","ProjectStatus","VKORG","VSTEL","VGBEL","VGPOS",
                    "IsDelete", "DTime", "DUser",
                    "ItmsGrpCode",
                    "ItmsGrpName",
                    "MUser",
                    "MTime","BaseEntry","WhsCode","BinLocationCode","Remark"
                };

                List<ExcelColumn<SD_DeliveryScan>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<SD_DeliveryScan> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                columns.ForEach((column) =>
                {
                    if (column.ColumnName == "IsDelete")
                    {
                        column.Formattor = ExcelExportFormatter.IsOrNoFormatterForBoolean;
                    }
                    if (column.ColumnName == "IsPosted")
                    {
                        column.Formattor = ExcelExportFormatter.IsOrNoFormatterForBoolean;
                    }
                });

                return ExportToExcelFile(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #region 手动生成托运单

        /// <summary>
        /// 手动生成托运单
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult ManualMakeConsignmentNote([FromBody]SD_DeliveryScan entity)
        {
            var result = new ResponseData();
            try
            {
                //entity.MUser = GetCurrentUser().LoginAccount;
                //entity.MTime = DateTime.Now;
                //result.Data = _app.Update(entity);
                //result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 打印

        /// <summary>
        /// 打印
        /// </summary>
        /// <param name="docNums">单号数组</param>
        /// <param name="templateCode">模板</param>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IHttpActionResult Print([FromUri]string[] docNums)
        {
            var result = new ResponseData();
            string templateCode = "销售送货单.repx";
            if (docNums == null || docNums.Length == 0 || string.IsNullOrEmpty(templateCode))
            {
                result.Code = (int)WMSStatusCode.Failed;
            }
            else
            {
                XtraReport report =
                    XtraReport.FromFile(HttpContext.Current.Server.MapPath("~/") + ConfigurationManager.AppSettings["LabelTemplateUploadPath"]?.ToString() + templateCode, true);
                SqlDataSource dataSource = report.DataSource as SqlDataSource;
                //switch (templateCode)
                //{
                //    case "PP_BarCodeReturn":
                //    case "PP_BarCode":
                //    case "MM_BarCode":
                QueryParameter parameter = new QueryParameter()
                {
                    Name = "docNum",
                    Type = typeof(List<string>),
                    Value = docNums.ToList()
                };

                dataSource.Queries[0].Parameters[0] = parameter;

                
                //dataSource.Fill();
                return base.PrintToPDF(report);

                //default:
                //    break;


            }
            return Json(result);
        }
        
        /// <summary>
        /// 雷登打印
        /// </summary>
        /// <param name="docNums">单号数组</param>
        /// <param name="templateCode">模板</param>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IHttpActionResult LdPrint([FromUri]string[] docNums)
        {
            var result = new ResponseData();
            string templateCode = "销售送货单(雷登).repx";
            if (docNums == null || docNums.Length == 0 || string.IsNullOrEmpty(templateCode))
            {
                result.Code = (int)WMSStatusCode.Failed;
            }
            else
            {
                XtraReport report =
                    XtraReport.FromFile(HttpContext.Current.Server.MapPath("~/") + ConfigurationManager.AppSettings["LabelTemplateUploadPath"]?.ToString() + templateCode, true);
                SqlDataSource dataSource = report.DataSource as SqlDataSource;
                QueryParameter parameter = new QueryParameter()
                {
                    Name = "docNum",
                    Type = typeof(List<string>),
                    Value = docNums.ToList()
                };

                dataSource.Queries[0].Parameters[0] = parameter;

                
                return base.PrintToPDF(report);


            }
            return Json(result);
        }

        /// <summary>
        /// 打印
        /// </summary>
        /// <param name="docNums">单号数组</param>
        /// <param name="templateCode">模板</param>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IHttpActionResult Print_SettlementAdd([FromUri]string[] docNums)
        {
            var result = new ResponseData();
            string templateCode = "销售送货单(结算地址).repx";
            if (docNums == null || docNums.Length == 0 || string.IsNullOrEmpty(templateCode))
            {
                result.Code = (int)WMSStatusCode.Failed;
            }
            else
            {
                //MD_LabelTempleteApp _labelTemplateApp = new MD_LabelTempleteApp();
                //MD_LabelTemplete template = _labelTemplateApp.GetFirstEntity(x => x.Remark == templateCode);
                //if (template == null)
                //{
                //    result.Code = (int)WMSStatusCode.Failed;
                //}
                //else
                //{
                XtraReport report =
                    XtraReport.FromFile(HttpContext.Current.Server.MapPath("~/") + ConfigurationManager.AppSettings["LabelTemplateUploadPath"]?.ToString() + templateCode, true);
                SqlDataSource dataSource = report.DataSource as SqlDataSource;
                //switch (templateCode)
                //{
                //    case "PP_BarCodeReturn":
                //    case "PP_BarCode":
                //    case "MM_BarCode":
                QueryParameter parameter = new QueryParameter()
                {
                    Name = "docNum",
                    Type = typeof(List<string>),
                    Value = docNums.ToList()
                };

                dataSource.Queries[0].Parameters[0] = parameter;
                //dataSource.Fill();
                return base.PrintToPDF(report);

                //default:
                //    break;


            }
            return Json(result);
        }


        #endregion
        
        #region 导入

        /// <summary>
        /// 导入
        /// </summary>
        /// <param name="entitys">接收的导入集合</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult ImportExcelToData([FromBody]List<SD_DeliveryScanImport> entitys)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                if (_app.ImportExcelToBaseData(entitys, GetCurrentUser().LoginAccount, out error_message))
                {
                    result.Data = true;
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                }
                else
                {
                    result.Data = false;
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
            return Json(result);
        }

        #endregion

        #region 导入模板

        /// <summary>
        /// 导入模板
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelModel()
        {
            var result = new ResponseData();
            try
            {
                var columns = ExcelService.FetchDefaultColumnList<SD_DeliveryScan>();
                string modelName = "销售交货-导入模板";
                //排除不需要的列名称 字符串
                string[] ignoreField = new string[]
                {
                    "DeliveryScanID", "DocNum","Line" ,"ShippingPlanDocNum", "BaseEntry", "SalesOrganization", "ItemName","ItmsGrpCode", "ItmsGrpName",
                    "WhsName","RegionCode","RegionName", "BinLocationCode", "BinLocationName", "IsDelivery",
                    "IsPosted", "PostUser", "PostTime", "SapDocNum","SapLine",
                    "CompanyCode", "FactoryCode", "ProfitCenter","ProjectStatus","VKORG","VSTEL",
                    "VGBEL","VGPOS","SAPmark","SAPmessage","IsDelete", "CUser", "CTime",
                    "MUser", "MTime", "DUser", "DTime"
                };
                var itemsData = new List<SD_DeliveryScan>() { };
                var ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<SD_DeliveryScan> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                //需要格式转换的字段
                //columns.ForEach((column) =>
                //{
                //    if (column.ColumnName == "CTime")
                //    {
                //        column.Formattor = ExcelExportFormatter.DateTimeFormatter;
                //    }
                //});
                return ExportToExcelFile<SD_DeliveryScan>(itemsData, columns, GetCurrentUser().UserName + "_" + modelName);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion
        
        #endregion

        #region Mobile

        #region 查询交运计划单号是否有效

        /// <summary>
        /// 查询交运计划单号是否有效
        /// </summary>
        /// <param name="DocNum">计划单号</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetDocNumForShippingPlan([FromUri] string DocNum)
        {
            string error_message;
            var result = new ResponseData();
            try
            {
                if (!_app.GetDocNumForShippingPlan(DocNum,out error_message))
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
           
            return Json(result);
        }

        #endregion

        #region 查询销售发运计划信息

        /// <summary>
        /// 查询销售发运计划信息
        /// </summary>
        /// <param name="DocNum">计划单号</param>
        /// <returns></returns>
        [HttpGet]
        //[AllowAnonymous]
        public IHttpActionResult GetShippingPlanForMobile([FromUri] string DocNum)
        {
            string error_message;
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetShippingPlanForMobile(DocNum,out error_message);
                if (itemsData != null && itemsData.Count > 0)
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Data = itemsData;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 保存

        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="queryView">列表</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Save([FromBody]SD_DeliveryScanParameters Parameters)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                string type = ""; //类型 1:提交失败 2：过账失败
                Sys_User currentUser = GetCurrentUser();

                bool bSubmit = _app.Save(Parameters.entities, Parameters.ManualPostTime, currentUser.LoginAccount, out error_message, out type);
                if (!bSubmit && type == "1")
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
                else if (!bSubmit && type == "2")
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }

        #endregion

        #endregion

    }
}

