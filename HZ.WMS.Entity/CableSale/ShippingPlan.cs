using System;
using System.ComponentModel;
using HZ.WMS.Entity;
using SqlSugar;

namespace AOS.WMS.Entity.CableProduce
{
    [SugarTable("SD_Cable_Sale_OrderInfo")]
    public class ShippingPlan : BaseEntity
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Description("主键ID")]
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }
        
        /// <summary>
        /// 发运单号
        /// </summary>
        [Description("发运单号")]
        public string ShipmentNum { get; set; }
        
        /// <summary>
        /// 发运状态
        /// </summary>
        [Description("发运状态")]
        public int ShipmentStatus { get; set; }

        /// <summary>
        /// 订单编号
        /// </summary>
        [Description("订单编号")]
        public string OrderNum { get; set; }

        /// <summary>
        /// 订单行号
        /// </summary>
        [Description("订单行号")]
        public int? LineNum { get; set; }

        /// <summary>
        /// 订单状态
        /// </summary>
        [Description("订单状态")]
        public int Status { get; set; }

        /// <summary>
        /// 订单类型(1:主机订单 2:部件订单 3:售后部件 4:售后主机)
        /// </summary>
        [Description("订单类型")]
        public int OrderType { get; set; }

        /// <summary>
        /// 同步SAP销售单号
        /// </summary>
        [Description("同步SAP销售单号")]
        public string SapNo { get; set; }
        
        /// <summary>
        /// 同步SAP销售单号
        /// </summary>
        [Description("同步SAP销售行号")]
        public int? SapLine { get; set; }
        
        /// <summary>
        /// 主件号
        /// </summary>
        [Description("主件号")]
        public string PartCode { get; set; }
        
        /// <summary>
        /// 计划发货时间
        /// </summary>
        [Description("计划发货时间")]
        public DateTime DeliveryDate { get; set; }
        
        /// <summary>
        /// 计划发货时间
        /// </summary>
        [Description("计划发货时间")]
        public DateTime ShipmentDate { get; set; }
        
        /// <summary>
        /// 订单类型名称
        /// </summary>
        [Description("订单类型名称")]
        public string OrderTypeName { get; set; }
        
        /// <summary>
        /// 电梯类型
        /// </summary>
        [Description("电梯类型")]
        public string ElevatorType { get; set; }
        
        /// <summary>
        /// 合同号
        /// </summary>
        [Description("合同号")]
        public string ContractNo { get; set; }
        
        /// <summary>
        /// 客户名称
        /// </summary>
        [Description("客户名称")]
        public string CustomerName { get; set; }

        /// <summary>
        /// 客户地址
        /// </summary>
        [Description("客户地址")]
        public string CustomerAddress { get; set; }
        
        /// <summary>
        /// 批次
        /// </summary>
        [Description("批次")]
        public string BatchNum { get; set; }

        /// <summary>
        /// 客户代码
        /// </summary>
        [Description("客户代码")]
        public string CustomerCode { get; set; }

        /// <summary>
        /// 结算地址
        /// </summary>
        [Description("结算地址")]
        public string SettlementAdd { get; set; }
        
        /// <summary>
        /// 木箱尺寸
        /// </summary>
        [Description("木箱尺寸")]
        [SugarColumn(IsIgnore = true)]
        public string WoodenBoxSize { get; set; }

        /// <summary>
        /// 木盘尺寸
        /// </summary>
        [Description("木盘尺寸")]
        [SugarColumn(IsIgnore = true)]
        public string WoodenTraySize { get; set; }

        /// <summary>
        /// 木箱刷字
        /// </summary>
        [Description("木箱刷字")]
        [SugarColumn(IsIgnore = true)]
        public string BoxPrint { get; set; }
        
        /// <summary>
        /// 客户订单号
        /// </summary>
        [Description("客户订单号")]
        public string CustomerOrderNum { get; set; }

        /// <summary>
        /// 顺序号
        /// </summary>
        [Description("顺序号")]
        public string SerialNo { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        [Description("项目名称")]
        public string ProjectDisc { get; set; }
        
        /// <summary>
        /// 设置发运时间
        /// </summary>
        [Description("设置发运时间")]
        public DateTime? SetShipmentDate { get; set; }

        /// <summary>
        /// 发运下载标记
        /// </summary>
        [Description("发运下载标记")]
        public int ShipmentDownFlag { get; set; }

        /// <summary>
        /// 设置发运用户
        /// </summary>
        [Description("设置发运用户")]
        public string SetShipmentUser { get; set; }
        
        
    }
}