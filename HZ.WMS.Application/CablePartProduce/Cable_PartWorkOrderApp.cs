using System.Collections.Generic;
using System.Linq;
using AOS.Core.Utilities;
using AOS.OMS.Entity.Sale;
using AOS.WMS.Entity.CableProduce;
using HZ.Core.Http;
using HZ.WMS.Entity;
using LinqKit;

namespace HZ.WMS.Application.PP
{
    /// <summary>
    /// 生产订单主表
    /// </summary>
    public class Cable_PartWorkOrderApp : BaseApp<SD_Cable_Sale_PartOrderInfo>
    {
        private BaseApp<BaseEntity> _baseApp = new BaseApp<BaseEntity>();

        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public Cable_PartWorkOrderApp() : base("DbConnectionForOMS")
        {
        }

        #endregion

        /**
         * 获取 计划订单列表
         */
        public List<SD_Cable_Sale_PartOrderInfo> GetPageList(Pagination page, Cable_PartWorkOrderListReq workOrderListReq)
        {
            var searchCondition = PredicateBuilder.New<SD_Cable_Sale_PartOrderInfo>(t => (
                (string.IsNullOrEmpty(workOrderListReq.ContractNo) ||
                 t.ContractNo.Equals(workOrderListReq.ContractNo)) &&
                (string.IsNullOrEmpty(workOrderListReq.SapNo) || t.SapNo.Equals(workOrderListReq.SapNo)) &&
                (string.IsNullOrEmpty(workOrderListReq.CustomerName) || t.CustomerName.Contains(workOrderListReq.CustomerName))&&
                (string.IsNullOrEmpty(workOrderListReq.ElectricCargoElevator) || t.ElectricCargoElevator.Contains(workOrderListReq.ElectricCargoElevator))&&
                (string.IsNullOrEmpty(workOrderListReq.ConstructionModule) || t.ConstructionModule.Contains(workOrderListReq.ConstructionModule))
            ));
            if (workOrderListReq.CreateDate != null && workOrderListReq.CreateDate.Length > 1)
            {
                var startTime = DateUtil.GetStartTime(workOrderListReq.CreateDate[0]);
                var endTime = DateUtil.GetEndTime(workOrderListReq.CreateDate[1]);
                var additionalCondition = PredicateBuilder.New<SD_Cable_Sale_PartOrderInfo>(order =>
                    order.CTime >= startTime && order.CTime <= endTime);
                searchCondition = searchCondition.And(additionalCondition);
            }
            if (workOrderListReq.DetailNo != null && workOrderListReq.DetailNo == 1)
            {
                var additionalCondition = PredicateBuilder.New<SD_Cable_Sale_PartOrderInfo>(order =>
                    order.DetailNo > 2);
                searchCondition = searchCondition.And(additionalCondition);
            }
            if (workOrderListReq.DetailNo != null && workOrderListReq.DetailNo == 2)
            {
                var additionalCondition = PredicateBuilder.New<SD_Cable_Sale_PartOrderInfo>(order =>
                    order.DetailNo <= 2);
                searchCondition = searchCondition.And(additionalCondition);
            }
            if (workOrderListReq.DeliveryDate != null && workOrderListReq.DeliveryDate.Length > 1)
            {
                var startTime = DateUtil.GetStartTime(workOrderListReq.DeliveryDate[0]);
                var endTime = DateUtil.GetEndTime(workOrderListReq.DeliveryDate[1]);
                var additionalCondition = PredicateBuilder.New<SD_Cable_Sale_PartOrderInfo>(order =>
                    order.DeliveryDate >= startTime && order.DeliveryDate <= endTime);
                searchCondition = searchCondition.And(additionalCondition);
            }

            if (workOrderListReq.ShipmentDate != null && workOrderListReq.ShipmentDate.Length > 1)
            {
                var startTime = DateUtil.GetStartTime(workOrderListReq.ShipmentDate[0]);
                var endTime = DateUtil.GetEndTime(workOrderListReq.ShipmentDate[1]);
                var additionalCondition = PredicateBuilder.New<SD_Cable_Sale_PartOrderInfo>(order =>
                    order.SetShipmentDate >= startTime && order.SetShipmentDate <= endTime);
                searchCondition = searchCondition.And(additionalCondition);
            }
            
            var list = GetPageList(page, searchCondition);

            var ids = list.Select(t => t.Id).ToList();
            var detailsList = DbContextForOMS.Queryable<SD_Cable_Sale_PartOrderDetails>()
                .Where(t => ids.Contains(t.Pid) && t.IsDelete == false).OrderBy(t => t.CTime).ToList();
            var detailMap = detailsList.GroupBy(t => t.Pid).ToDictionary(t => t.Key, t => t.ToList());
            foreach (var cableWorkOrder in list)
            {
                if (detailMap.ContainsKey(cableWorkOrder.Id))
                {
                    cableWorkOrder.OrderDetailList = detailMap[cableWorkOrder.Id];
                }
            }
            return list;
        }

    }
}