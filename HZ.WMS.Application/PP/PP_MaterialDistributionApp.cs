using SqlSugar;
using HZ.WMS.Application.MD;
using HZ.WMS.Application.SAP;
using HZ.WMS.Application.Sys;
using HZ.WMS.Entity.PP;
using HZ.WMS.Entity.PP.Dto;
using HZ.WMS.Entity.SAP;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Application.PP
{
    /// <summary>
    /// 生产物料配送单
    /// </summary>
    public class PP_MaterialDistributionApp : BaseApp<PP_MaterialDistribution>
    {
        private PP_MaterialDistributionDetailApp detailApp = new PP_MaterialDistributionDetailApp();
        private Sys_SAPCompanyInfoApp sapApp = new Sys_SAPCompanyInfoApp();
        private MD_StockApp stockApp = new MD_StockApp();

        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public PP_MaterialDistributionApp() : base()
        {
        }

        #endregion

        #region 添加

        public bool Add(List<PP_MaterialDistribution> materials, List<PP_MaterialDistributionDetail> details)
        {
            var mark = false;
            try
            {
                //this.DbContext.Ado.BeginTran();
                this.DbContext.Ado.BeginTran();
                Insert(materials);
                detailApp.Insert(details);
                this.DbContext.Ado.CommitTran();
                mark = true;
            }
            catch (Exception ex)
            {
                mark = false;
                this.DbContext.Ado.RollbackTran();
                throw ex;
            }
            return mark;
        }

        #endregion

        #region 编辑

        public bool Edit(PP_MaterialDistribution_Dto disDto, string user)
        {
            bool result = false;
            try
            {
                var entity = this.GetFirstEntity(w => w.DeliveryOrderNo == disDto.DeliveryOrderNo);
                if(entity != null && entity.ManualPostTime != disDto.ManualPostTime)
                {
                    entity.ManualPostTime = disDto.ManualPostTime;
                    entity.MUser = user;
                    this.Update(entity);
                }
                var addList = new List<PP_MaterialDistributionDetail>();
                var updateList = new List<PP_MaterialDistributionDetail>();
                foreach (var item in disDto.details)
                {
                    if(string.IsNullOrEmpty(item.ID))
                    {
                        item.DeliveryOrderNo = disDto.DeliveryOrderNo;
                        item.ManualPostTime = disDto.ManualPostTime;
                        item.CUser = user;
                        addList.Add(item);
                    }
                    else
                    {
                        item.MUser = user;
                        item.ManualPostTime = disDto.ManualPostTime;
                        updateList.Add(item);
                    }
                }
                var deleteList = detailApp.GetListByKeys(disDto.DelDetailIds);
                if (deleteList != null && deleteList.Count > 0)
                {
                    deleteList.ForEach(item =>
                    {
                        item.DUser = user;
                        item.DTime = DateTime.Now;
                        item.IsDelete = true;
                    });
                    updateList.AddRange(deleteList);
                }

                //this.DbContext.Ado.BeginTran();
                this.DbContext.Ado.BeginTran();
                detailApp.Update(updateList);
                detailApp.Insert(addList);
                this.DbContext.Ado.CommitTran();
                result = true;
            }
            catch (Exception ex)
            {
                result = false;
                this.DbContext.Ado.RollbackTran();
                throw ex;
            }
            return result;
        }

        #endregion

        #region 删除

        public bool Delete(List<string> deliveryNo, string deleteUser)
        {
            var mark = false;
            try
            {
                //this.DbContext.Ado.BeginTran();
                this.DbContext.Ado.BeginTran();
                detailApp.Delete(w => deliveryNo.Contains(w.DeliveryOrderNo), deleteUser);
                base.Delete(w => deliveryNo.Contains(w.DeliveryOrderNo), deleteUser);
                this.DbContext.Ado.CommitTran();
                mark = true;
            }
            catch (Exception ex)
            {
                mark = false;
                this.DbContext.Ado.RollbackTran();
                throw ex;
            }
            return mark;
        }

        #endregion

        #region 生产物料配送过账

        public bool DoPost(List<PP_MaterialDistribution> entities, string operater, out string error_message)
        {
            error_message = "过账成功";
            try
            {
                foreach (var entity in entities)
                {
                    //SAP过账
                    int Line = 0;
                    List<ZFGWMS003> zFGWMs = new List<ZFGWMS003>();
                    var deliveryNo = entity.DeliveryOrderNo;
                    var detailList = detailApp.GetList(w => w.DeliveryOrderNo == entity.DeliveryOrderNo).ToList();
                    foreach (var item in detailList)
                    {
                        //转出仓库同转入仓库时不过账，只更新WMS过账状态
                        if (item.DeliverLocation == item.OutWarehouse)
                        {
                            item.IsDoPost = true;
                            item.DoPostMan = operater;
                            item.DoPostTime = DateTime.Now;
                            item.ManualPostTime = DateTime.Now;
                            item.MUser = operater;
                            detailApp.Update(item);
                        }
                        else
                        {
                            //获取库位
                            var location = new MD_BinLocationApp().GetFirstEntity(w => w.WhsCode == item.OutWarehouse);
                            //调接口减本地库存
                            if (item.SpecialInventory?.ToUpper() == "E")
                            {
                                var mark = stockApp.StockOut(item.ComponentCode, location?.BinLocationCode, item.SalesOrderNo, Decimal.ToInt32(item.SalesOrderLineNo ?? 0), item.DemandQty, operater, out error_message);
                                //if (!mark) return mark;
                            }
                            else
                            {
                                var mark = stockApp.StockOut(item.ComponentCode, location?.BinLocationCode, item.DemandQty, operater, out error_message);
                                //if (!mark) return mark;
                            }
                            //加库存
                            var _location = new MD_BinLocationApp().GetFirstEntity(w => w.WhsCode == item.DeliverLocation);
                            var flg = new MD_StockApp().StockIn(
                                new Entity.MD.MD_Stock
                                {
                                    ItemCode = item.ComponentCode,
                                    ItemName = item.MaterialName,
                                    Qty = item.DemandQty,
                                    Unit = item.ComponentUnit,
                                    RegionCode = _location?.RegionCode,
                                    RegionName = _location?.RegionName,
                                    BinLocationCode = _location?.BinLocationCode,
                                    BinLocationName = _location?.BinLocationName,
                                    WhsCode = _location?.WhsCode,
                                    WhsName = _location?.WhsName,
                                    SaleNum = item.SalesOrderNo,
                                    SaleLine = Decimal.ToInt32(item.SalesOrderLineNo ?? 0),
                                    AssessType = item.AssessmentType,
                                    SpecialStock = item.SpecialInventory,
                                }, operater, out error_message);
                            //if (!flg) return flg;
                            error_message = "过账成功";

                            Line++;
                            item.DeliveryLineNo = Line;

                            zFGWMs.Add(new ZFGWMS003
                            {
                                ZNUM = Line,
                                MATNR = item.ComponentCode,
                                WERKS = item.FactoryCode ?? "2002",
                                BWART = "311",
                                MENGE = item.DemandQty,
                                MEINS = item.ComponentUnit,
                                LGORT = item.OutWarehouse,
                                UMLGO = item.DeliverLocation,
                                SOBKZ = item.SpecialInventory,
                                KDAUF = item.SalesOrderNo,
                                KDPOS = Decimal.ToInt32(item.SalesOrderLineNo ?? 0),
                                BWTAR = item.AssessmentType,
                            });
                        }
                    }

                    if(zFGWMs != null && zFGWMs.Count > 0)
                    {
                        var isPosted = false;
                        DateTime date = DateTime.Now;
                        //查询Sap账期时间
                        DateTime ManualPostTime = new SAPApp().GetSAPpostTime(Convert.ToDateTime(entity.ManualPostTime));
                        var result = sapApp.ZFGWMS003("001", deliveryNo, entity.Remark, ManualPostTime, zFGWMs, out isPosted, out error_message);
                        //更新本地库
                        if (result != null && result.Count > 0)
                        {
                            List<PP_MaterialDistributionDetail> querydetails = new List<PP_MaterialDistributionDetail>();
                            foreach (var sap in result)
                            {
                                var querydetail = detailList.FirstOrDefault(x => x.DeliveryOrderNo == sap.DocNum && x.DeliveryLineNo == sap.line);
                                if (querydetail != null)
                                {
                                    querydetail.IsDoPost = true;
                                    querydetail.DoPostMan = operater;
                                    querydetail.DoPostTime = date;
                                    querydetail.ManualPostTime = ManualPostTime;
                                    querydetail.MUser = operater;
                                    querydetail.SapDocNum = sap.sapDocNum;
                                    querydetail.SapLine = sap.sapline;
                                    querydetails.Add(querydetail);
                                }
                            }

                            entity.IsDoPost = true;
                            entity.DoPostMan = operater;
                            entity.DoPostTime = date;
                            entity.MUser = operater;
                            entity.MTime = date;
                            entity.ManualPostTime = ManualPostTime;

                            this.DbContext.Ado.BeginTran();
                            Update(entity);
                            detailApp.Update(querydetails);
                            this.DbContext.Ado.CommitTran();
                        }
                        else
                        {
                            error_message = "单号：" + deliveryNo + "，过账失败，失败原因：" + error_message;
                            return isPosted;
                        }
                    }
                    else
                    {
                        entity.IsDoPost = true;
                        entity.DoPostMan = operater;
                        entity.DoPostTime = DateTime.Now;
                        entity.MUser = operater;
                        entity.MTime = DateTime.Now;
                        entity.ManualPostTime = DateTime.Now;
                        Update(entity);
                    }
                }
            }
            catch (Exception ex)
            {
                error_message = ex.InnerException?.Message ?? ex.Message;
                this.DbContext.Ado.RollbackTran();
                return false;
            }
            return true;
        }

        #endregion
    }
}
