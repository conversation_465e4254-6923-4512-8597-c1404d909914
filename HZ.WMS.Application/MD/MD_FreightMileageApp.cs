using SqlSugar;
using HZ.WMS.Entity.MD;
using System;
using System.Collections.Generic;
using System.Linq;

namespace HZ.WMS.Application.MD
{
    /// <summary>
    /// 销售运费里程
    /// </summary>
    public class MD_FreightMileageApp :  BaseApp<MD_FreightMileage>
    {
        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public MD_FreightMileageApp() : base(){}

        #endregion

        public List<MD_FreightMileage> GetSupplierList()
        {
          
            return DbContext.Ado.SqlQuery<MD_FreightMileage>(@"select distinct SupplierCode,SupplierName
                                                                                      from [dbo].[MD_FreightMileage] ").ToList();
        
        }

    }
}
