/// <XRTypeInfo>
///   <AssemblyFullName>DevExpress.XtraReports.v16.1, Version=16.1.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</AssemblyFullName>
///   <AssemblyLocation>C:\Windows\Microsoft.Net\assembly\GAC_MSIL\DevExpress.XtraReports.v16.1\v4.0_16.1.5.0__b88d1754d700e49a\DevExpress.XtraReports.v16.1.dll</AssemblyLocation>
///   <TypeName>DevExpress.XtraReports.UI.XtraReport</TypeName>
///   <Localization>zh-CN</Localization>
///   <Version>16.1</Version>
///   <Resources>
///     <Resource Name="XtraReportSerialization.PPStockingWave">
/// zsrvvgEAAACRAAAAbFN5c3RlbS5SZXNvdXJjZXMuUmVzb3VyY2VSZWFkZXIsIG1zY29ybGliLCBWZXJzaW9uPTQuMC4wLjAsIEN1bHR1cmU9bmV1dHJhbCwgUHVibGljS2V5VG9rZW49Yjc3YTVjNTYxOTM0ZTA4OSNTeXN0ZW0uUmVzb3VyY2VzLlJ1bnRpbWVSZXNvdXJjZVNldAIAAAACAAAAAAAAAFBBRFBBRFAg898HOIXbfgAAAABRAAAAaAEAAExwAGkAYwB0AHUAcgBlAEIAbwB4ADEALgBJAG0AYQBnAGUAJgBTAHkAcwB0AGUAbQAuAEQAcgBhAHcAaQBuAGcALgBJAG0AYQBnAGUAAAAAAE5zAHEAbABEAGEAdABhAFMAbwB1AHIAYwBlADEALgBSAGUAcwB1AGwAdABTAGMAaABlAG0AYQBTAGUAcgBpAGEAbABpAHoAYQBiAGwAZQADPQAAIP48AAD/2P/gABBKRklG
/// AAEBAQAAAAAAAP/bAEMACAYGBwYFCAcHBwkJCAoMFA0MCwsMGRITDxQdGh8eHRocHCAkLicgIiwjHBwoNyksMDE0NDQfJzk9ODI8LjM0Mv/bAEMBCQkJDAsMGA0NGDIhHCEyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMv/AABEIAGQDIAMBIgACEQEDEQH/xAAfAAABBQEBAQEBAQAAAAAAAAAAAQIDBAUGBwgJCgv/xAC1EAACAQMDAgQDBQUEBAAAAX0BAgMABBEFEiExQQYTUWEHInEUMoGRoQgjQrHBFVLR8CQzYnKCCQoWFxgZGiUmJygpKjQ1Njc4OTpDREVGR0hJSlNUVVZXWFlaY2RlZmdoaWpzdHV2d3h5eoOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4eLj5OXm5+jp6vHy
/// 8/T19vf4+fr/xAAfAQADAQEBAQEBAQEBAAAAAAAAAQIDBAUGBwgJCgv/xAC1EQACAQIEBAMEBwUEBAABAncAAQIDEQQFITEGEkFRB2FxEyIygQgUQpGhscEJIzNS8BVictEKFiQ04SXxFxgZGiYnKCkqNTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqCg4SFhoeIiYqSk5SVlpeYmZqio6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2dri4+Tl5ufo6ery8/T19vf4+fr/2gAMAwEAAhEDEQA/APfa5Pxv4Pg8T6cDGEjv4R+5kx1H90+1dbSVUJuElKJFSCqRcWfLF1az2t3Jb3EZjmiYq6N1BFQk5r3Hx/4HXX7c6hYqBqMQ6f8APVR/Cff0rw90aJirKQynBU9Qa9/DV1WjfqfN4nDyoSs9hlFFFdJynonw88cNpMqaTqUn+hSNiKRv+WLHsf8A
/// ZP6V7SOa+U69X+G/jkkxaFqkuTwtrKx/8cP9PyrysbhL/vIfM9fAYz/l3P5f5HrFeb/Erxn/AGZC2jafJ/pso/esp/1Sn+p/lW/428WQ+GNKyjK99MCsEfv/AHj7CvIfCvh688Za+8ly7GFX8y7nJ5OewPqa58NQVva1PhR1YuvJv2NP4mbPw58Gf2vcLq2oRE2MB/dq3/LRwf8A0EUfEbxmNYuzpNg+bGBv3jqf9Y4/oK3PiB4pj0TT08NaORHJs2SlP+WSY+6Pc/y+tZHw48FnUp01fUYv9Djb9zG3/LRh3PsP1NdCldvEVduiOS3/ADD0t/tM3Ph94Uj0PTm8QasojuChaPeP9TH6n3P8q4Lxr4rm8T6qfLJSxhyIY/X/AGj7mug+JPjL+05m0fT5P9EhbEzqeJHHb6D+dedAfnXRhqUpS9tU3e3kjHE1oxj7Gnst/NiUUUV2nni17F8NvBf2KKPXNQj/ANIkXNtGf+Wan+L6n+Vc
/// /wDDnwadXu11W/j/ANBgb92pH+tcf0H+e9e1BQAOMAV5OOxV/wB3D5ns5fhP+Xs/keVfGnpon/bf/wBp15TXq3xp/wCYJ/23/wDadeU12YD+BH5nHmH8eX9dBtFFFdhxHtXwf/5Fq7/6+T/6CK9Eb7h+led/B/8A5Fq7/wCvk/8AoIr0RvuH6V83iv40j6bCfwF6HytP/wAfEn+8f51GOhqSf/j4k/3j/OowOtfQxPm2xKKKKZIUUUUwCiiigAooooAKKKKAPXvg3cyyWepQO5McboyqT0JznH5V6lXlHwZ+5qv1j/ka9Xr53F/xpH0uBf7iJ86+Pf8AkdtT/wCun9BXNV0vj3/kdtT/AOug/kK5qvco/wAKPoj5/E/xJerEooorQyPqHQ/+QHY/9e8f/oIq+KoaH/yA7H/r3j/9BFXxXy73ProfCgooopFC0UUUDCiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACii
/// igAopKKAFopKWgAoozRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAFFFFACV5h8RvAxvFk1vS4v9JUZniUf6wf3h7+vrXqFIQMGtKVWVKXNEyrUY1Y8sj5PAzS16b8R/Av2Z5Nb0qL9yx3XEKj7p/vAenrXmVfQUa0asbo+Zr0ZUZ8rEpQSCCDgjvSUVsYm1AmpeKtdtrZ5zNdSgRq8h+6oH9BzXqmr6hYfDjwtFp+ngNfOpEeRks3d2/z6CvFYJpLeZJonZJEYMjKcEEdDW1bx6l408RxxSS+bdTEBnPARB1OPQCuKtR5mnJ2guh2UK/Imoq8n1L3hDw1deMdckmupJDao++5mPVmJ+6D6mu38f+K4tE09fD+kER3BTbIU/wCWUeOg9z/KtDWdS0/4deF4rGxVTdMMRK3Vm7u3+fQV4nc3E11PJcTuzzSMWdyeprKlB4ifPL4Vsjor
/// SWHhyR+J7shJJOTzRR1or0Tyxa6Lwd4Vm8T6t5ZDJZRfNPKPT0HuazNG0e613VIrC0TLueW7KO5PsK+ivD+hWnhzSYrG1XheXc9XbuTXDjcT7NcsXqehgcL7V80ti9aWkNlaxW1vGEhiUKqjsBU9AorxL3ep9AkkrI8n+NP/ADBP+2//ALTrymvV/jT/AMwT/tv/AO068or6DAfwI/M+czD+PL+ug2iiiuw4j2r4P/8AIt3f/Xyf/QRXojfdP0rzv4P/APIt3X/Xyf8A0EV6IOlfNYr+NI+nwn8BHzHNo+qfaJMaZe/eP/LBvX6Uz+x9V/6Bl7/34b/CvqDA9KNo9BXUsyl/Kcf9lR/m/A+Xv7H1X/oGXv8A34b/AAo/sfVf+gZe/wDfhv8ACvqHaPQUbR6Cn/akv5Rf2VH+b8D5f/sbVf8AoGXv/fhv8KP7F1X/AKBl7/35b/CvqDaPQUYHtT/tSX8of2VH+b8D5f8A7E1X/oGXv/fl
/// v8KqzW1xaybLiCWF8Z2yIVP617j408e22gRmzsylxqLDpnKx+7f4V4ne3tzqF291dzNLM5+Zm7120K1SquaUbI4MTQp0naMrlWiiius5BaKK3vDHhW/8T3vl2o2QIf3szD5VH9T7VnOcYR5pFU6cqkuWKO9+DcLLa6nKfus6KPwBr1Ssrw/odr4f0qKwtFxGnJY9WPcmtWvnq9RVKjkj6fDUnTpKLPnXx7/yO2p/9dB/IVzVdL49/wCR21P/AK6D+Qrmq9+j/Cj6I+bxP8SXqxKKKK0Mj6h0P/kB2P8A17x/+gitDvWfof8AyA7H/r3j/wDQRWh3r5d7n10PhQUUUGkWNdwilmICgZJNZv8AwkOj/wDQUs/+/wCv+NS6wcaNen/pg/8A6Ca+X668NhlWvd2scWLxToWsrn03/wAJDo//AEFLL/v+v+NH/CQ6P/0FLL/v+v8AjXzJijFdf9mR/nOL+05/yn03/wAJDo//AEFLL/v+v+NH
/// /CQ6P/0FLL/v+v8AjXzJRR/Zkf5gWaTf2T6b/wCEh0f/AKCll/3/AF/xo/4SHR/+gpZf9/1/xr5koo/syP8AMH9qT/lPpv8A4SHR/wDoKWX/AH/X/Gj/AISHR/8AoKWX/f8AX/GvmSij+zI/zB/ac/5T6ittVsL1ylreW87gZKxyBj+lXK8S+EP/ACNF1/16n/0Ja9trz69JUZ8qdz08NWdWHM1YqXWqWNkypdXcELMMgSOFyPxqv/wkWj/9BOz/AO/6/wCNeU/F/wD5GCz/AOvf/wBmNedV10cFGpBTctzir4+VObion03/AMJFo/8A0E7P/v8Ar/jR/wAJFo//AEE7P/v+v+NfMlFaf2dH+Yx/tSf8p9N/8JFo/wD0E7P/AL/r/jR/wkWj/wDQTs/+/wCv+NfMlGKP7Oj/ADB/ak/5T6b/AOEi0f8A6Cdn/wB/1/xo/wCEi0f/AKCdn/3/AF/xr5koPHaq/syP8wf2pL+U+oLfWdOu
/// pfLt723lfGdqSgn8qvV8qQytBKksTskincrKcEGvpfw7dzX/AIc066uRiaW3R39yQOfxrjxWF9gk073OzCYv6xdNWsalFc74x8RHw14flvY0DzkiOJW6bj3PsK8Wk8d+J5HLnVplBOcKAAP0qaOGnVV0XXxcKL5XufRdFfOX/CdeJf8AoMz/AJD/AAo/4TrxN/0GZ/yH+Fbf2fU7ow/tOn2Z9D3N1BaQma4mjhjHV5GCgfiao/8ACQ6P/wBBaz/7/L/jXz5f+KNc1W0Nre6lLNAxBKOBgkcjtWRWkMtbXvSMp5o7+5H7z6oguYrmISwSpJG3RkOQfxqavmmz8V63p1olpZ6lNDBGCERcYGTn0qf/AITnxN/0Gbj8h/hUPL590Wszh1iz6Qor5v8A+E68Tf8AQZuP0/wp8Xj7xPDMsn9rSvt/hcAg/UYpf2fU6NFf2nS7M+jM1Vu9RtLFVa6uYYQ3QyOFz+dZfhPXf+Ej8PW9+yBJGysi
/// r0DDg4pPFegR+ItCmsmC+bgvCx/hcdPwPQ/WuPltLlkdvPzU+eGpc/4SHR/+grZf9/1/xrRR1dQykEHkEd6+VpIZIZpIpVKyxsUYHsRwa9q+FniA6noTafO2bixIUZ6mM52/lgj8q6sTg/ZQ507nFhsb7SfJJWO8uLmG0gaaeVI416s5wBVD/hItG/6Ctl/3/X/Gsj4jf8iPqH+6v/oQr58pYXCKtFyvYrFYyVGSilc+mv8AhItH/wCgrZf9/wBf8aP+Ei0f/oK2X/f9f8a+ZaK6f7NX8xy/2pLrE+mv+Ei0f/oK2X/f9f8AGj/hItH/AOgrZf8Af9f8a+ZaMU/7NX8wf2pLpE+noNb0y5mEUGoWssjdFSVST+Ga0K+dfAP/ACPGmf77f+gmvortXDiaKoy5U7nfhMQ68XJqwtV7i5htImluJkijHVnOAKsV4n8TfFQ1K+/sazkDW9u2ZmU8NJ6fQfzqaNF1ZcqLxNdUYczPV/8AhItH
/// /wCgpZ/9/wBf8avW9xFcwrLDIskbDKspyDXzV4f0aXXtctrCEf6w5kf+4g6n8q9K8Z+K9T8HXljpWkLAlstspAkTcRgkevtXRVwfJNU4O7ZzUcbzQdSaskeo0V4T/wALY8Sjvaf9+f8A69I3xW8SkEH7JyP+eP8A9ej+zq3Ww/7To+Z7KfEGkDrqVn/3+X/Gp7TU7K+LC1uoZiuNwjcNtz64r5edmkkZ2+82Sa1/D/ijUfDDTvp/k5n2h/NXd0zjHPvWssudrp6nPHM7v3lofSmadXgw+LHib/pz/wC/X/16X/hbPiT1s/8Av1/9esf7Pq+R0f2lRPeKKKK4j0AooooAjdFkRkZQykYIPevDviD4JbQrp9SsEzp0zEso/wCWDen+6e1e6V5n8TfGEdraPodmVe5mUic9QiHt9T+ldWDnUjVSh8zix0ISpNy+R45SVbk0y+h0+G/ktnW0lYokvYkVVIr3009j5xpoBVizu57C7iuraQpN
/// E25WFV6UHsaGk9GJNp3Rf1jWLvXNQe8vX3StgYHCqB0AHYVRptOzRGKirIqUnJ3Y2pIIJbmeOCCN5JZGCqiDJJNR17H8NvBY06FdZ1KIfa5f9ShH+rU/xfU/oKxxFdUYXZthcO60+U6DwP4Si8M6WGkAa+nAMz9cf7I9hXWd6QClr56c3OXMz6WnBU4qMRaKKKk0PJ/jT/zBP+2//tOvKK9X+NP/ADBP+2//ALTryivoMB/Aj8z5vMP48v66DaKKK7DiPavg/wD8i3df9fJ/9BFejDpXnPwf/wCRbuv+vk/+givRh0r5rF/xpH0+D/gxFooornOoKKKjmnit4WlmdUjUZZmOABQA+vLvG/xKW1Mmm6HIrzcrLcjkJ6hfU+9Y/jb4kPqZk07RnaOy6STjIaT2Hov86865z16969XCYG/v1PuPHxeP+xT+8JJJJZWllcvI5yzE8k03NDcUuK9Y8cKKK9J8E/DiS/KajrUbR23DR254Z/dv
/// Qe3esa1aNKN5GlGhOtLliY3g3wJd+I5VuZw0GmqeZP4pPZf8a9y07TbTS7OO1s4UihQYCqP1qxBBHbwrFCipGowFUYA+lSV4dfESqvXY+hw+FjRWm4o6UtAornOs+dPHv/I7an/10H8hXNV0vj3/AJHbU/8AroP5Cuar6Wj/AAo+iPk8T/El6sSiiitDI+odD/5Adj/17x/+gitDvWfof/IDsf8Ar3j/APQRWh3r5d7n10PhQUUUUixjorqVcAqRyD3rN/4RvRP+gRYf+Ayf4VqV5l4w+Jk2j6pJp2lwRTSQnE0kwJAPoACOnrWlKnOpLlgY1qkKceaZ3H/CN6H/ANAiw/8AAZP8K5X4i6Npln4LvJrbTrSCQNHh4oFU/fHcCuN/4W74h/54WH/fpv8A4qs7XPiFq2v6ZLpt3FaLFJtLGNGB4II6n2rtpYOvGak9kzz62Mw7ptRWrXY5KvdfAWiaVd+DbCa402zllZW3PJArMfmPUkV4
/// VXX6N8RdZ0XS4dPtYbNooQQpdGJ5OfWu3GUp1IpQOHBVYU5t1NhfiVaQWPi+SK2gihi8iM7Y0CrnHoKxvC0ST+K9KilRHR7lFZHXIIz0INN13XbrxHqbaheJCsxUJiIEDA+pNVNPvJNN1K3voQDJbyCRQwyMg55rWMJKhyPexnKpH23Mtrn0j/wjWh/9Aiw/8Bk/wo/4RvQ/+gPYf+A6f4V5H/wt7xF/zwsP+/Tf/FUf8Le8Rf8APCw/79N/8VXlfUq/9M9b6/hu34HsdrpGnWMhktLG2gcjBaKJVJH4Crtch4A8S3vifTLq6vUhVo5vLURKQMbQe5PrXX1yTjKMnGW6O6lKMoqUNildaTp184ku7G2uHAwGmiVyB+IqD/hGtD/6BFh/4DJ/hWpRxU3fcpxT3Rl/8I1of/QIsP8AwGT/AAo/4RrQ/wDoEWH/AIDJ/hWpxScUXl3FyR7GZ/wjWh/9Aiw/8Bk/wrG8QDwn4dsTc3umWAJ4
/// SJbdCzn0AxS+NfF6eFbCNki868nJWKM9BjqT7V4VqmrXmsXr3l9O0szdz0UegHYV24XCzq6t2RwYzEwpe7FJssa9rQ1i982KxtbOFeEigiVcD3IHJrKOTjjLHgAd6sWNhd6peJbWUDzTt0VR/nAr2rwZ8PbbQwl7fhbjUMcE8rH7L7+9ejVrU8PGy+48ujh6mInf72c14M+GjzmPUNdTbFw0doerehf0+levqqqgVRhQMACnUV4tWtKq7yPfoUIUY2iee/F3/kVYv+vhf5GvEq+qpreG4TZNFHKuc4dQR+tcn4u8DweIrS2itpIbIxOWZlhHzAjGOMV14XFxprkkvmcWMwUqr9pF/I4fRvGPhOy0iztb3QUnuYogskhto2yfXJ5NX/8AhO/BP/Qtp/4CxVVb4VwIdreJLYMOxQD/ANmpP+FV2/8A0Mlr/wB8j/4qtn9Vbu2/xOdLFJWsvwLf/Cd+Cv8AoW0/8BYqP+E78Ff9C2n/AICx
/// VU/4VXb/APQyWv8A3yP/AIqj/hVdv/0Mlr/3yP8A4qlbCd3+I/8Aauy/At/8J34J/wChbT/wFio/4TvwT/0Laf8AgLFVT/hVdv8A9DJa/wDfI/8AiqP+FV23/QyWv/fI/wDiqXLhe7/EP9q7L8CDX/F/hbUdEubXT9CSC6kXEcn2eNdpyO45rzyvSv8AhVdv/wBDJbf98j/4quw8F+D7HRbK4iee21EvIGD+WPl46d60VejRj7mpLw1avL37L7iL4T/8iUn/AF3k/nXc9qjihht4/LhiSNP7qAAfpWD4u1u+8P6Sb+0s0uljb96pYgqvr+deVJurUbXU9aCVGmk+h5T8TNIOn+LZJoY/3d4gl+UfxdG/UZ/Go/hveyad4xtkZWVLpTC2VOOeR+oFbZ+Md2eujwEehlP+FeleH9Wtte0e21G3QKsqjK/3GHUfga9GpVqU6PJUj5XuebTo0qlbnhLrtY0bm3guoTDcRJLE3VJFDKfqDVH/
/// AIRvQ/8AoD6f/wCAyf4VqdqWvMUmtj2HCL6GS3hvQ9rf8SjT+n/Psn+FfNl1tF5cKqhVEjAKO3NfUrfdb6V8tXf/AB/3H/XRv5mvTy56yv5Hj5nFLlt5noPwmsLO/vdTW8tYLgJHGV86MPt5bpkV6n/wjeif9AfT/wDwGT/CvNPgz/x/6t/1zj/m1evDpXPjpNV3ZnVgIRdBXRQh0LSraZZoNMs4pV5V44FVh9CBWjSUtcd7ncklseafEvxpc6UP7GsQ0c80YaScdVU54X3968gggmu50ghVpZZW2qqjJYmvRPiHpN/rPj2O1sLZppDbJ06Ly3JPQCp5bK2+GWmQ3Q8m6164O1fMGUjXvgDB9s5717FGpTpUkoK8meHiITq1ZObtGP8AWh1/gTwknhrTC8wDX1wAZW/ujsg+nf3p3ibwJp/ie/ivLq5uYnjj8sCIrgjJPcH1rgV+LniB2VVs9PLMcKPLfk/991a+I3iPWIrLTdNmkW3n
/// mthNdLB8oLE/c6k4GPXmub2Nf2qbdmzpVeh7FpK6Rr/8Kh0L/oJX3/fSf/E01/hHoSqSNUveP9tP8K8lhjurptluk0rYyRGC38qjcTRSvHKJEZTgq+Qfyru9hW/5+fgcP1ihb+H+IkybJnXd0JGfxrrPAvhKy8UT30d5czwC3CFfJKjOc5zkH0rmIrO6uU3wW00ig4ZkQsB+VQqzIepBxziuiac4tRlZnLBqMlJq67HsX/Co9C/6Cl7/AN9R/wDxNL/wqLQf+gpef99x/wDxNeR/Zr3yfP8AJuPJ679jbfz6VF5jf3m/OuZYes/+Xn4HWsTRW9M+rKKKK8M+hCiisnXtctfD+lS310flQfKoPLt2AppN6ImUlFXZleN/FsPhnSiEYNfTAiGP0/2j7CvJPCnhy78Y6+0ty8jW6v5l1OTySecA+pquDqvjvxRzkzzt1/hiT/ACvS9d1Ox+HfhiLTNPCtfSphM9Se7t/T/61emo+wioQ1nL
/// 8DyJT+sTdSekF+Jk/EjxFZ2OnJ4Y02KIhVUSYGREo6KPfj/Oa8wlsrmC2gupYJEt58+VIV4fHXFdJ4P8MXPi7Wnnumc2iPvuJT1cnnaD6mur+J+uafbWEfhy0t4nkj2lvl4gA6AehI/St6c1SkqMNX1MKkfbRlWk7LZHk9FFFdx5wUUUVQHoPw38HrrFx/a16oazt32ojc73HqPQZ/GvbhwOn0r538HeLbjwtqIYlpLGU/voh/6EPcV9AWN7b6jZxXdrKssEqhkdehFeFj1UVS8tuh7+Wyh7O0d+pYFLRRXAeiLRSUUxnlHxp/5gn/bf/wBp15RXq/xp/wCYJ/23/wDadeUV9BgP4EfmfN5h/Hl/XQbRRRXYcR7V8H/+Rbuv+vk/+givRh0rzn4P/wDIt3X/AF8n/wBBFejDpXzWL/jSPp8H/BiFL2pKxfEPiOx8OWLXN5J14SNfvO3oBWEYuTsjonNQV2XtS1O00mykvL2ZYoYxksx/
/// QeteFeMvHV54mlNvDug04HiMHmT3b/Cs7xN4pv8AxLfebdsUgU/u7dT8qD+p96w69rC4NU/enq/yPCxeOlU92GiG9KKKK9A80WpIonnlWKJC8jnCqo5J9BU2nadd6pepZ2UJmnc4VR/M+g969x8G+ArTw5Et1dBZ9RYcyEZEfsv+Nc2IxMKK8+x14fCyrS02MbwV8N0sfL1HWUD3XDR255WP3Pqf5V6WowMdBS0V4NWrKpLmke/RowpRtEWiiiszYWiiimM+dPHv/I7an/10H8hXNV0vj3/kdtT/AOug/kK5qvpaP8KPoj5PE/xJerEooorQyPqHQ/8AkB2P/XvH/wCgitDvWfof/IDsf+veP/0EVod6+Xe59dD4UFFFFIsTvXzX4vH/ABWOsf8AX0386+lK+a/GH/I46x/19N/OvRy3+I/Q8vNf4cfUb4b8PzeJdW/s+CdIX8tpNzgkcY9PrW34h+HF94d0abU7i/gmjixlEUgnJA7/
/// AFqX4Uf8jov/AF7Sf0r0P4okf8ILeAnGXjH/AI+tdFevONeME9NDloYeEsPKbWqPA+oruNC+Gt7rujwajHfwRLMCQjKxIwSP6Vw9fQvw9Vk8D6aGGDsb/wBCNaY2tKlBOLM8BRjVqNS2PE/EegTeHNWbTp5klcIr7kBA5+tUdMsm1PU7awV1je4kWNWbsScZrrfisjL4yJPR7dGH5kf0rB8JH/ir9I/6+U/9CFaRqSdHn62Mp04xruHS501/8KtR03TZ7xtRt2WCNpCoRskAZxXAZwa+lvFH/Iqar/16yf8AoJr5prLA1p1b8xtj6EKTXKtz2f4Of8i7f/8AX3/7Itd7fX9tptnJd3cqxQRjLux4HNcD8HP+Rdv/APr7/wDZFroPiD/yIuqf9c1/9DFebiFzYhxfVnqYduGFUl2H/wDCf+F/+gzb/r/hR/wn/hf/AKDNv+Tf4V864pMV2/2dT7nn/wBp1ex9F/8ACf8Ahf8A6DNv+Tf4
/// Uf8ACf8Ahf8A6DNv+Tf4V86YoxR/Z1Puw/tOp2PQPidrmna1eae+n3iXCxI4bZngnGK4JFDSKrNtUkAt6D1pmKXrXdSpqnBQRwVajnNzfU9t8Oap4H8M2gitdUgMzAebMytvc/lx9K3f+E/8L/8AQYh/Jv8ACvnXFFccsBCTvKTudscwnBWjFH0V/wAJ94X/AOgxD+Tf4Vp6Vruma2sh067S4ERAfbn5c5x1+hr5ir1v4Mf8e+r/AO/F/Jq58Tg4UqblFs6sNjqlWooSSPVK8/8AitrF3puhW9vZyGI3UhSR1ODtA5A+ua9Ary/4yf8AIO0v/rs38hXLhIqVWKZ2YuTjRkzyEsxOSzE+ua1F0HWXQMml3rKRkMI25FZVdfD8TPEsEMcUc9uFQBR+4XoBXu1HUXwJfM+epuL+Ntehif8ACPa3/wBAq9/78tR/wj2uf9Aq9/78tXQf8LT8Uf8APxb/APfhaP8Ahafij/n4t/8AvwtZ3xHZ
/// ff8A8A1th/5n93/BOf8A+Ee1z/oFXv8A35amSaDrMUbSSaZdqijLM0TACuj/AOFp+KP+fi3/AO/C1DdfEnxHeWstvNPbmORCjfuVHBGDRevfVL7xNULaSf3f8E5LLepr2P4N/wDIG1L/AK+B/wCg147k+te0fCG0mg8O3U8iFVnuMx5HUBQM/nn8qzx9lRNMuu66PRcCori2iureSCZA8cilWU9walorwj6E+ZPEWkvoevXenv0ikO0+qnlT+WK7r4Q6w0d7d6PK/wC7lXzogT0YcMPxGPyrovGnw9k8T6vHfwXcduwiCOGQndg8H8qoeHfhpf6Fr1rqP9pwuIWyyCMjcpGCOvvXrzxNKpQ5Zb2/E8eGGq0sRzQWl/wD4i+LtZ0DWbe30+4WON4d5BjDc5I71xv/AAszxT/z/R/9+V/wr0jxj4BbxTqMV2NQ+zeXF5e3yt2eSc9R61z3/CmX/wCg1/5L/wD2VTh6mGVNKdr+g8RTxLqN
/// wvb1OY/4WZ4p/wCf6P8A78LXJOxaRnPViSfrXo2tfC4aNpFzqEmshhBGX2+Rjcew+9615x39a7cP7GSbpHBiI1otRqv8bmroniLUvDjzPpsqxNMAHygbIHTr9a2v+Fm+Kv8An9j/AO/C/wCFY3hvRU1/XYtNe6+zmUNtkCbuQM4xkV33/CmD/wBBs/8AgP8A/ZVNapQjL94lf0LoQxEo/um7HNf8LN8U/wDP7H/34X/CvZvDN7PqXh2xvLlg00sQZiBjJrz/AP4Uw3/Qb/8AJf8A+yr0fRdM/sfRrXT/ADfNEEYTftxnFedi50HFez39D0sHDERk/a7Eet6zYeH7GXUL1lQAY4HzOeyj1NfPOva3c+INXlv7k4LcImeEXsBXV/FqaZ/FUUTSMYkt1KoTwCSckCuBrtwOHjGKn1Zw47EOU3DojvfhdoEOra1Jf3GGj0/ayoR952ztP0G0n8ql+Lwx4ltP+vUf+hNWr8GOms/9sf8A2eqn
/// xW0++u/Edq9raTzKLYAtGhYA7j6Vlzt41p9P8jT2a+pJxWrf6lP4SyRx+J7nzGA/0U/eOP4lrn/G77vGWqlfmXzuPyFUDourLyNOvB/2xb/Cm/2Pqp66ddn/ALYt/hXXGKVZ1L7o5G5ukqfLsz1D4TyxJ4b1Deyq3nn7xA/hryJz+9f/AHjV5dH1YHI0+8B9om/wo/sXVj/zDLv/AL9N/hSpwUJylfcdSc5wjHl2PUBLF/wpRo9y7vs5+XIz9/0rx8gVf/sXV+n9nXmP+uTf4VqaJ4N1rWb+KD7DPbwFgZZ5UKhV79ep9qKfJRTblu7jqSqVmko7I+jaKKSvnz6UgurqGytpLieQJFGpZmPQCvAPFXiK78Y64sVsjm3V9ltAOrEnqR6mtv4jeM21e6Ok6fKfsULfvXX/AJaOO30H862/AXheDw/pr+JNZHlTGMvGrj/Upjr/ALx/z1r0aNNUIe0n8T2R5Neo8RU9lB+6t2XtMsbD4beF
/// nvr3a9/KoMnq79o19h/9evM4YtU8deJzuJaeZsu38MKf0AFTeJtevvGniBI4EYxF9lpb/Xv9T3r0zS7HTvhv4Ve7uyHu2UGVh1kbsi+w/wATWl3RXM9akvwMmlWfJHSERNc1Sx+HfhmKw09VN4yEQqepPeRv8+1eIzzy3M8s8zs8srFndjkknvVvWtYutc1Wa/u3/eSHhc8IvZR7CqFdmGoezjd/E9zkxVf2kuWOkVsJRRUkEEtzMkEEbSTSMFRFHLE10bHIR0VNdW8tpcPbXETRTxHDow5BqGquNqwtdl4D8ayeHbw2t27HTpT8w/55N/eA9PUVxtOrKrTVWPKzSlVdKXNE+p4J454klicPG4DKwPBB71NXivw98cnS5F0nU5T9jdv3UrH/AFJPb/dP6V7QjAgc59D614FejKjLlkfSUK8a0eZD6KKKwOg8n+NP/ME/7b/+068or1f40/8AME/7b/8AtOvKK+gwH8CPzPm8w/jy/roN
/// ooorsOI9r+D/APyLV3/18n/0EV6IOled/B7/AJFq7/6+T/6CK7PXNRGkaHd37KXEERfb6ntXzeJV68l5n02GfLh4vyMbxb40sfDNuUOJr51zHAG/VvQfzrwvVtYvdbvnvL+dpZG6DPCj0A7Cq17e3GoXclzcytJPKcux7moK9fDYWNJX6niYnFyrPsuwlFFFdhyC1q6D4dv/ABFfi2soiQD+8lI+VB6msuPZ5g8zd5efm29ce1el6P8AEvSNCsVs7HQJY4h1PnDLH1Jxya5q86kY/u1dnRh6dKUv3jsj0Hwv4RsPDFoI7ZQ87AebOy/M5/oPauhxXlv/AAue2/6A8v8A3+H+FL/wue3/AOgPL/3+H+FePLDYib5pLU9qGLw0FyxloepUleXf8Lnt/wDoDy/9/h/hR/wue3/6A8v/AH+H+FL6nW/lL+vYf+Y9Sory3/hc9v8A9AeX/v8AD/Cj/hc9v/0B5f8Av8P8KPqdbsH17D/zHqVL
/// XIeEPG8fi2e6iSye3+zqrEs4bOc+3tXXVhOEoPlkdMKkakeaOx86+Pf+R21P/roP5CuarpfHv/I7an/10H8hXNV9HR/hR9EfLYn+JL1YlFFFaGR9Q6H/AMgOx/694/8A0EVoVn6H/wAgOx/694//AEEVoV8u9z6+HwoKKKKRQV81eMP+Rx1j/r6b+dfStfNXi/8A5HLWP+vpv516OW/xH6Hl5r/Dj6m78K2C+MwT/wA+0n9K0fih4sg1F4dHsZEljifzJ5FORuGRtHrivOoppYHYxSPGWXaWRiMj0+lLDHJPKsMMbyyucKqDJNd8sPF1vaS6HnQxElS9lFbjrO0mvryG0t13zTOERfUmvpvS7FNM0u1sYvuQRrGD64HWuH+H3gX+xU/tXUlH251wkfXygf8A2Y/pXoteZja6qystkergMO6UeaW7PLPi/ozSW1prESZ8r9zMR/dJyp/PP515VbTy2l1FcwttkjcOp9wc19P3tlDqFnLa
/// 3CB4ZVKup7g14L4u8E3vhq7d0V59PY/JNj7vs3of5104HERcfZSOXH4aSn7WJ6gfE1p4l8CalNbuBKLOQSxE/MjbTn8PevBD1pyTSQkmGV42I2naSMg9RTa68PQVGTs9GcWIxLrct+h7N8HP+QBf/wDX3/7ItejsodSrAEHgg9684+Dn/IAv/wDr7/8AZFr0ivGxX8aR7uE/gRIfslv/AM8Iv++BR9kt/wDnhF/3wKmorC50WRD9kt/+eEX/AHwKPstv/wA8Iv8AvgVNRSuFkeO/GCKNNR0oJGozHJnAx3Fec2Y/0+Ef9NF/nXpPxj/5COlf9c5P5ivNrP8A4/4P+uq/zr3sJ/u6PncX/vDXofUC2sO0fuY+n90U4WsGP9TH/wB8ipE+6PpTq8Jtn0KgiH7LB/zxT/vkU9Ikj+4iqPYYp9FK7K5V2ErzvxZ4r8GXVwtlqkcl81ux/wBUp2o3QjORXba1I8Oi30sZIdIHZT6EKa+YdzHL
/// Ekk9Sa7cFh/attvY4MfiHSiopJ37non9r/Db/oC3P6//ABVH9r/Db/oC3P6//FVg+FvBV34qtp5ba5ig8lwjCUHnIz2roP8AhTuq/wDQRs/yb/CuyfsYO0pu/qzhh7eavGCt6Df7X+G3/QFuf1/+Ko/tf4bf9AW5/X/4qszxD8Or/wAO6RLqM95bSpGVBVA2eTj09642rp0qdRXjN/eZVKk6btOCT9D0X+1vht/0Bbj9f/iqP7W+G3/QFuf1/wDiqztC+G9/r2kwahDfW0aTAkKwYkYJHp7Vp/8ACndV/wCgjafk3+FZN0E7c7+9msVXkrqC+4I9a+G8Thl0S4JHqpP6bq6OD4q+GreJYoba6jjUYVEiAAHsM1534p8F3XhWK3kuLqGbz3KARgjGPrXMVp9WpVY812/mR9aq0ZNWSfofVkMomiSRQdrqGGfen1V0/wD5Btt/1yX+VWq8Vn0C2uec/FDxLf6NFZWunXJgnmZnZl6hRgD8
/// yT+VecL408Tu6qutXG5iABkdfyq/8S9SF/4xnjVvktVWED3HJ/U1neCNOOp+L9OhwGRJfNbPovzf0A/GvZo0IQoc8l0ueBXrVJ4jlg+tj6E06OaHT7eO4kMkqxqHc9WbHJq3TQKpatqtroumzX124SKNc+5PoPevGfvOyPdbUI3fQ4H4t62sWnQaPE372dvMkweiDt+J/lXj/StDW9Xn17V59RuD80rfKvZFHQD6Cr/hfwpd+JprtYfkSCMneehf+Ffxr6ChCOHpLm+Z87XqPEVnymTp19Lpmo297AcSQSBx74PSvpjT72LUbCC8gO6KZA6n2NfMEkUsEzxTIUkjYqyt1BHUV6V8LfFq28g0G7k+V2LWzseAx6p+PUe9c+Po88OeO6/I3wFf2dTkns/zPYcUYFGaWvGPeOO1fwLa674mXVL+QyQJGqLbgYDEZ+8e456V4jr0KQeJNVijUJHHeTIqLwAA5AAFfT1fMviT/katY/6/pv8A
/// 0Nq9TL5ylNq+yPHzKlCEU4rVs9C+DHTWf+2P/s9a/jfx3d+F9Vhs7a0gmEkIk3SE+pHb6VkfBg8az/2x/wDZ63PGXgiHxRqcF22qpamKLy9nlB88k9dw9ayq8n1p+02/4BpS5/qa9nv/AME5T/hcOrf9A6z/ADek/wCFw6t/0DrP/wAf/wAauf8ACoLb/oYV/wDAYf8AxdPX4OQOcL4gDH2tgf8A2et/9hXT8zFfXns/xRQ/4XDq3/QNs/zf/Gj/AIXDq3/QOtPzf/Grp+D9urlT4hUEetsP/i6Vfg9E/wBzxCG+lqD/AOz0XwXb8x2x/f8AFFH/AIXDq3/QOtPzf/GgfGHVv+gdafhu/wAavt8HIkGX8Q49M2oH/s9A+EFu33fEIPstsD/7PRfBdvzDlx/f8UetV5n8SvGp0+FtE02X/SpB+/kU/wCrU/w/U/yre8c+L4vDGmFYiGv5wRCnp/tH2FeUeEfDV34w1x5bpnNsjb7mYnJc
/// nnbn1Nc2GopL2tTZHRi8RJ/uaXxM2fhx4MGp3C6zqEeLOE5hRv8Alqw7n/ZH86g+IvjL+2b06ZYufsMLfMw6TMP6Dt+dbvxC8Vw6VZDw3oxVCE2zNFx5a/3R7nvWd8NvBv26Rdb1GPFrG2YImHDsP4j7D+ddKf8AzEVPkjmcdsPS/wC3mbfgPwrB4c0uTX9Y2x3LJvXf/wAsI8fzP/1q8/8AGXiqbxNq7SZK2UJIt4v/AGY+5rd+I/jM6tdNpNi/+gQN+8Yf8tW/+JH6159nJrbD0pN+2qbv8DDE1oxj7Glst/Nid6KKK7jzx1eyfDjwZ/ZsC61qMeLyVf3CEf6pT3+p/QVz/wAN/Bf9p3C6xqMf+hxNmFGHEjjv9B+pr2njpXk47Ff8u4fM9jL8J/y9n8jgfiB4JGu2r6hYIF1KJeg/5bKP4T7+h/CvEZI3ikaN0KupwwI5Br6sIxyK8w+IvgU3avrOlxZuFBM8Kj/WD1Hv6+tTgsXy
/// fu57F47B3XtIbnj9LSUtewjxQr1j4c+Os+VoeqSfN922mY9fRD/T8q8npQSCCDgjoRWOIoKtGzN8PXlRnzI+rQaU9K85+Hnjn+1oV0vUpQL6Mfu5GP8ArlH/ALMP1r0avn6tOVKXLI+ko1o1Y80Tyj40/wDME/7b/wDtOvKK9X+NP/ME/wC2/wD7TryivcwH8CPzPAzD+PL+ug2iiiuw4j2v4P8A/ItXf/Xyf/QRXReO/wDkSNW/64H+Yrnfg/8A8i1d/wDXyf8A0EV0Xjv/AJEfVv8Argf5ivnqv+9fNH0dL/dPkz5yooor3z5wKKKKYBRmiigAooooAKKKKACiiikB6h8Gf+P7Vv8ArnH/ADavYK8g+DP/AB/at/1zj/m1ev14GO/jv5fkfSZf/u8T518e/wDI7an/ANdB/IVzVdL49/5HbU/+ug/kK5qvao/wo+iPAxP8SXqxKKKK0Mj6h0P/AJAdj/17x/8AoIrQrP0P/kB2P/Xv
/// H/6CK0K+Xe59fD4UFFFFIoK+avF//I5ax/19N/OvpWvmrxh/yOOsf9fTfzr0ct/iP0PLzX+HH1LXgjQbXxF4iWyvN/leU0h2NgkjH+Ne4aL4X0fQlzY2MUchGDJjLn/gR5r570bWr3QtQ+12Los20pll3DB//VXRf8LO8T/8/UH/AH5FdGJw9apL3XocuEr0KUffWp75iivA/wDhaHif/n6t/wDvwKP+FoeJ/wDn6t/+/Arl/s+r5HZ/adLsz3yo5I0mQo6hlYYIIyDXg/8AwtDxP/z9W/8A34FH/C0PE/8Az82//fgUv7OrdGg/tOi90zvPFfgDQP7KvtQgtfs08ULyAQttUkAn7vT8q8Rrq7v4jeIr2zmtZ54GilQowEI5BGDXKV6OEp1KcWqjuebi6lKo06asey/Bz/kAX/8A19/+yLXpGea4z4Z6RNpPhVDcJslupDOVPYEAD9BmuzxzXjYiSlVk0e5hU1Rin2FooryXxz4513RP
/// E82n2U0SwJGjANGCckc81NKlKrLliVWrRpR5pHrVFeAj4oeJ/wDn5g/78inf8LQ8T/8APxB/35FdX9n1fI4/7Tpdmbvxl/5CWlf9c5P5ivN7P/j/AIP+uq/zq9r3iPUvEc0MmourNEpC7UC4B61L4U0eXXPElpbRKWRXEkrdlQHJP9Pxr0qUfY0LT6HmVZKvXvDqfSa/dFOpB0pa+fPpEFFFFAzM17/kAah/17yf+gmvmP8Ahr6c17/kAah/17yf+gmvmPtXq5ZtI8XNd4ly01fUdOUpZX11bK5yywysgJ98GrP/AAk+v/8AQb1H/wACpP8AGt3wR4w0zw1a3UN/ZSXJlcMpRVOABjvXV/8AC1vDn/QFuP8AviP/ABrarJqTSp38zmpRi4q9W3lqeY3WuavewmG61O9niJBKSzuynHsTVCvQfFfjzSNe0GWwstMmt5XZWDuqADBz2Oa89rooN8usbGGIspWUubzNG313WLOBYLXVb2CF
/// Puxx3DqB9ADU3/CUa/8A9BvUv/AqT/Guw8M+P9I0XQbaxutMmnliDbnRUwSWJ7nPetj/AIWt4f8A+gJcf98R/wCNc85SUn+6v9x004xa1q2+88svNV1HUAgvb+6ughyonmZ8fTJqnXZeN/F2neJIrOOxsZLcwuxYuqjIIx2rnNH0yfWtWttPgUs0rgN/sr3P4CumD/d3kuU56kW6nLF83mfS1h/yDrb/AK5L/Kn3VwttbSzv9yJC7H2AzT4U8qNIx0VQK5f4jaiNO8GXpDYecCBcf7Rwf0zXz0YuU+VdT6WpLkpuT6I8Fvbl72/ubuQ7nnlaQn3JJr0X4O6aJL/UNRYcRIsKn3bk/oB+deY1758MdNFh4NgkK4a6YzH6HgfoBXsY6XJQt30PCwEPaYjmfTU6LVtasNDsjdX9wkUQ6ZPLH0A7mvB/F/jG78VXgBBisYj+5hz/AOPN6n+Vb/xfdj4islJJUW2cf8CNcjoXhzUvEV75FlCd
/// o/1krDCoPc/0rPCUadOCrT3/ACNsZiKlSo6MCvpOk3euahFY2cZaaQ9xwo7k+gFfQ3hzQLbw7o8VlAAWX5pJO7uepNV/C3hOx8L2Ait1D3Dj99OR8zn+g9q6LFcmKxLrOy2OzB4RUY80viZ5P8TPBbM7a7psWTj/AEmJR/4+P6/nXlS/IQ68EdCO1fVhGRXk/jb4aMWfUdBjzn5pLRR+qf4flXRg8XFL2dT7znxmDbftKf3FrwR8R47xI9M1mUJcgYjnY8P7H0Pv3r05Tlc18qSRtHIUlVo5EOCp4INfR3gtt/g7Sz/07rWWOoRp2nHZmuAxMql4S6G9XzL4k/5GnWP+v6b/ANDavpodK+ZPEn/I06x/1+zf+htTy3+I/QnNfgj6nf8Awhl8m116Q/wrE35eZXmktxJLcSPvb5mJ+8e5rt/AVz9j8KeLZ84K2yAH3IkA/U1wX8JrupwXtqkn5fkedVl+5pr1/M39fWW20zQF8x9z2Rk+
/// 8ed0jEfpiul+D7u/ia93OxH2Q8H/AH1rO+IVv9kl0KH/AJ5abHGD9Kx/C/ie48K6jNd29vHO0sRjIkJAHIOePpUuHtcM+Xd/5lwkqWIXNsv8jc+KVm1l4wadWZVu4lkHOBkDaf5Z/Gk+F2pta+MI4JJCUuomjwT36j+X61ck+Ll7NgyaNYuQMZO401Pivdo4ePRbFWHRhkEVnyVnS9nydO5o50VW9pGXW+zNz4xRMttplym777xnH0BH8jXHfDu9eDxvYAsSsu5CCfVTWp4g8UXPjDwXczT2scDWF1Ew2EnIYMvf6iuS8O3P2PxNps+cKtyuT7EgVVKk/q8qct1cVaqvrCnHbRieINTutV124uLuTe+4qvooB4A9BXtTxR+F/h1PJpaLE8VsJQxGSXPUn1NFFZ4r+DTNcJ/Gqeh41odsms+JbWC9Z5FuJQZW3fM2TzzXr/xEvZtF8GrFp5ECuRB8gxtTpgenFFFGL+OmicH8NU8IYnOM
/// 8daaKKK9XoeUha0tCs4r7WbO2m3eXJOisFOMgmiisqnwMuHxo+lra3itbeO3gjWOGNQFRRwBVgUUV809z6yIUYoopFHgPxK0u00zxP8A6JH5azxiVkH3QxJzgdq46iivpMP/AA4+h8riP4svUKKKK3MR8UslvNHPC5SVCHVlOCDX0l4Vvp9S8MadeXJBmmhDOQMZNFFeTmPQ9fKt5HA/Gn/mCf8Abf8A9p15TRRXXgP4EfmcmYfx3/XQbRRRXYcR7X8H/wDkWrv/AK+T/wCgiui8d/8AIj6t/wBcD/MUUV89V/3r5o+jpf7p8mfOVFFFe+fOBRRRTAKKKKACiiigAooooAKUdaKKTBnqHwY/4/tV/wByP+bV6/RRXgY7+O/l+R9Jl/8Au8fn+Z86+Pf+R21P/roP5Cuaoor2qP8ACj6I8DE/xJerEooorQyPqHQ/+QHY/wDXvH/6CK0KKK+Xe59fD4UFFFFIoK+avGH/ACOOsf8AX038
/// 6KK9HLf4j9Dy81/hx9TD6GiiivYR4bCiiigYUUUUALXqXw98J6VeRx6jdRvPKmGVJCCgPPOMc/jRRWGM/gs2wv8AFR62oAGAMAU6iivnEfTrYD0rwL4o/wDI83P/AFyj/wDQRRRXfl/8X5HBmX8H5nFiloor2jwC5plst5fRxSM4VmGdpwa+hPDHh7TtB09UsYdrSANJIxy7n3NFFefmLfKj0cuS9ozoBS0UV4574UUUUAZmvf8AIA1D/r3k/wDQTXzGfu0UV6uWbSPFzXeI2iiivWR5DCiiigBaKKKQye0iWe5WN87T1xX0D4S8NaZodkJLOE+dKo3zO25z7Z7D6UUV5+Yt+zR6GW/xDpgK8u+M0zrZ6TCD+7eSRmHqQFx/M0UV52C/jxPUxv8AAkeR19RaVDHbaRaQxLtjjhRVHoAKKK7Mz+z8zhyr4pfI47xJoFjrvji1jvxI8cdqGCB8BjuPXvXZWNlbWFstvaQJDEgwqIMAUUVx
/// 1X+7j6HbRX76XqW8UUUVyo7WFGKKKBHK+K/CWj6xayTXFsEnVSRNF8r/AInv+NXfByCPwrp8a/dWEAUUV0zbdBX7nJBf7S/Q3a+ZPEn/ACNOsf8AX7N/6G1FFdGWfxGc+a/BH1NjQZGj8A+Kipxu+yqfoXauZtRm5hB6F1/nRRXpw3qev6I8ur8MPT9T3vxxoNhqmhE3Mbb7VC0LqcFTj1/AV4DMoRmUdAxHNFFc+X/AzfMf4iIqdRRXoHCzr/DqiTwH4n3c48oj8DXJB2j8t1OGBzmiisKfxS9f0RrV2j6fqz//2QGACFBFUmhkR0ZUWlhRZ1RtRnRaVDBpYzNGc1JHRjBZVk52ZFhKalpURWlQanhXYVdWM0lFNWhiV1U5SWxORVgwUmxiR2wyWlhKNVgxWnBaWGNpUGp4R2FXVnNaQ0JPWVcxbFBTSkViMk5PZFcwaUlGUjVjR1U5SWxOMGNtbHVaeUlnTHo0OFJtbGxiR1FnVG1G
/// dFpUMGlRM1Z6ZEc5dFpYSkRiMlJsSWlCVWVYQmxQU0pUZEhKcGJtY2lJQzgrUEVacFpXeGtJRTVoYldVOUlrTjFjM1J2YldWeVRtRnRaU0lnVkhsd1pUMGlVM1J5YVc1bklpQXZQanhHYVdWc1pDQk9ZVzFsUFNKRGRYTjBiMjFsY2tGa1pDSWdWSGx3WlQwaVUzUnlhVzVuSWlBdlBqeEdhV1ZzWkNCT1lXMWxQU0pDWVhObFRuVnRJaUJVZVhCbFBTSlRkSEpwYm1jaUlDOCtQRVpwWld4a0lFNWhiV1U5SWtOUFRsUWlJRlI1Y0dVOUlsTjBjbWx1WnlJZ0x6NDhSbWxsYkdRZ1RtRnRaVDBpU1hSbGJVTnZaR1VpSUZSNWNHVTlJbE4wY21sdVp5SWdMejQ4Um1sbGJHUWdUbUZ0WlQwaVNYUmxiVTVoYldVaUlGUjVjR1U5SWxOMGNtbHVaeUlnTHo0OFJtbGxiR1FnVG1GdFpU
/// MGlSR1ZzYVhabGNubFRZMkZ1VVhSNUlpQlVlWEJsUFNKRVpXTnBiV0ZzSWlBdlBqeEdhV1ZzWkNCT1lXMWxQU0pVWld4bGNHaHZibVVpSUZSNWNHVTlJbE4wY21sdVp5SWdMejQ4Um1sbGJHUWdUbUZ0WlQwaVNYUmxiVTVoYldVeElpQlVlWEJsUFNKVGRISnBibWNpSUM4K1BFWnBaV3hrSUU1aGJXVTlJa04xYzNSdmJXVnlUM0prWlhKT2RXMGlJRlI1Y0dVOUlsTjBjbWx1WnlJZ0x6NDhSbWxsYkdRZ1RtRnRaVDBpUTFWelpYSWlJRlI1Y0dVOUlsTjBjbWx1WnlJZ0x6NDhSbWxsYkdRZ1RtRnRaVDBpUW1GeVEyOWtaU0lnVkhsd1pUMGlVM1J5YVc1bklpQXZQanhHYVdWc1pDQk9ZVzFsUFNKUWNtOXFaV04wSWlCVWVYQmxQU0pUZEhKcGJtY2lJQzgrUEVacFpXeGtJ
/// RTVoYldVOUlsTmxkSFJzWlcxbGJuUkJaR1FpSUZSNWNHVTlJbE4wY21sdVp5SWdMejQ4Um1sbGJHUWdUbUZ0WlQwaVFtRnpaVTUxYld4cGJtVWlJRlI1Y0dVOUlsTjBjbWx1WnlJZ0x6NDhMMVpwWlhjK1BDOUVZWFJoVTJWMFBnPT0=</Resource>
///   </Resources>
/// </XRTypeInfo>
namespace XtraReportSerialization {
    
    public class PPStockingWave : DevExpress.XtraReports.UI.XtraReport {
        private DevExpress.XtraReports.UI.XRTableCell tableCell5;
        private DevExpress.XtraReports.UI.XRTableCell tableCell7;
        private DevExpress.XtraReports.UI.XRTableRow tableRow3;
        private DevExpress.XtraReports.UI.XRTableCell tableCell18;
        private DevExpress.XtraReports.UI.XRTableCell tableCell19;
        private DevExpress.XtraReports.UI.XRTableCell tableCell3;
        private DevExpress.XtraReports.UI.XRLabel label2;
        private DevExpress.XtraReports.UI.GroupHeaderBand GroupHeader2;
        private DevExpress.DataAccess.Sql.SqlDataSource sqlDataSource1;
        private DevExpress.XtraReports.UI.XRTableCell tableCell1;
        private DevExpress.XtraReports.UI.XRLabel label10;
        private DevExpress.XtraReports.UI.XRLabel label17;
        private DevExpress.XtraReports.UI.XRLabel label1;
        private DevExpress.XtraReports.UI.XRTableCell tableCell13;
        private DevExpress.XtraReports.UI.XRLabel label8;
        private DevExpress.XtraReports.UI.XRTableCell tableCell12;
        private DevExpress.XtraReports.UI.GroupHeaderBand GroupHeader1;
        private DevExpress.XtraReports.UI.XRTableCell tableCell30;
        private DevExpress.XtraReports.UI.XRTableCell tableCell2;
        private DevExpress.XtraReports.UI.XRTableRow tableRow2;
        private DevExpress.XtraReports.UI.XRLabel label19;
        private DevExpress.XtraReports.UI.XRTableCell tableCell22;
        private DevExpress.XtraReports.UI.XRTableRow tableRow1;
        private DevExpress.XtraReports.UI.XRTableCell tableCell4;
        // private DevExpress.XtraReports.UI.XRTableCell tableCell6;
        private DevExpress.XtraReports.UI.XRTableCell tableCell8;
        private DevExpress.XtraReports.UI.XRTableCell tableCell10;
        private DevExpress.XtraReports.UI.XRTableCell tableCell14;
        private DevExpress.XtraReports.UI.CalculatedField calculatedField2;
        private DevExpress.XtraReports.UI.XRTable table2;
        private DevExpress.XtraReports.UI.XRTableRow tableRow4;
        private DevExpress.XtraReports.UI.XRTableCell tableCell23;
        private DevExpress.XtraReports.UI.BottomMarginBand BottomMargin;
        private DevExpress.XtraReports.UI.GroupFooterBand GroupFooter1;
        private DevExpress.XtraReports.UI.XRLabel label9;
        private DevExpress.XtraReports.UI.XRTableCell tableCell28;
        private DevExpress.XtraReports.UI.XRTableCell tableCell9;
        private DevExpress.XtraReports.UI.TopMarginBand TopMargin;
        private DevExpress.XtraReports.UI.XRPictureBox pictureBox1;
        private DevExpress.XtraReports.UI.XRLabel label6;
        private DevExpress.XtraReports.UI.XRLabel label4;
        private DevExpress.XtraReports.UI.XRTable table3;
        private DevExpress.XtraReports.UI.XRTableRow tableRow5;
        private DevExpress.XtraReports.UI.XRTableCell tableCell31;
        private DevExpress.XtraReports.UI.XRTableCell tableCell26;
        // private DevExpress.XtraReports.UI.XRTableCell tableCell29;
        private DevExpress.XtraReports.UI.XRTableCell tableCell11;
        private DevExpress.XtraReports.UI.XRLabel label3;
        private DevExpress.XtraReports.UI.XRTable table1;
        private DevExpress.XtraReports.Parameters.Parameter docNum;
        private DevExpress.XtraReports.UI.CalculatedField calculatedField1;
        private DevExpress.XtraReports.UI.DetailBand Detail;
        private System.Resources.ResourceManager _resources;
        private string _resourceString;
        public PPStockingWave() {
            this._resourceString = DevExpress.XtraReports.Serialization.XRResourceManager.GetResourceFor("XtraReportSerialization.PPStockingWave");
            this.InitializeComponent();
        }
        private System.Resources.ResourceManager resources {
            get {
                if (_resources == null) {
                    this._resources = new DevExpress.XtraReports.Serialization.XRResourceManager(this._resourceString);
                }
                return this._resources;
            }
        }
        private void InitializeComponent() {
            DevExpress.DataAccess.ConnectionParameters.MsSqlConnectionParameters msSqlConnectionParameters1 = new DevExpress.DataAccess.ConnectionParameters.MsSqlConnectionParameters();
            DevExpress.DataAccess.Sql.SelectQuery selectQuery1 = new DevExpress.DataAccess.Sql.SelectQuery();
            DevExpress.DataAccess.Sql.Column column1 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression1 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Table table4 = new DevExpress.DataAccess.Sql.Table();
            DevExpress.DataAccess.Sql.Column column2 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression2 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column3 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression3 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column4 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression4 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column5 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression5 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column6 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression6 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column7 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression7 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column8 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression8 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column9 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression9 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column10 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression10 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column11 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression11 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column12 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression12 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column13 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression13 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column14 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression14 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column15 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression15 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column16 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression16 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column17 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression17 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.QueryParameter queryParameter1 = new DevExpress.DataAccess.Sql.QueryParameter();
            DevExpress.XtraReports.UI.XRSummary summary1 = new DevExpress.XtraReports.UI.XRSummary();
            this.tableCell5 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell7 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableRow3 = new DevExpress.XtraReports.UI.XRTableRow();
            this.tableCell3 = new DevExpress.XtraReports.UI.XRTableCell();
            this.label2 = new DevExpress.XtraReports.UI.XRLabel();
            this.GroupHeader2 = new DevExpress.XtraReports.UI.GroupHeaderBand();
            this.sqlDataSource1 = new DevExpress.DataAccess.Sql.SqlDataSource();
            this.tableCell1 = new DevExpress.XtraReports.UI.XRTableCell();
            this.label10 = new DevExpress.XtraReports.UI.XRLabel();
            this.tableCell19 = new DevExpress.XtraReports.UI.XRTableCell();
            this.label17 = new DevExpress.XtraReports.UI.XRLabel();
            this.label1 = new DevExpress.XtraReports.UI.XRLabel();
            this.tableCell13 = new DevExpress.XtraReports.UI.XRTableCell();
            this.label8 = new DevExpress.XtraReports.UI.XRLabel();
            this.tableCell12 = new DevExpress.XtraReports.UI.XRTableCell();
            this.GroupHeader1 = new DevExpress.XtraReports.UI.GroupHeaderBand();
            this.tableCell30 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell2 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableRow2 = new DevExpress.XtraReports.UI.XRTableRow();
            this.label19 = new DevExpress.XtraReports.UI.XRLabel();
            this.tableCell22 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableRow1 = new DevExpress.XtraReports.UI.XRTableRow();
            this.calculatedField2 = new DevExpress.XtraReports.UI.CalculatedField();
            this.table2 = new DevExpress.XtraReports.UI.XRTable();
            this.BottomMargin = new DevExpress.XtraReports.UI.BottomMarginBand();
            this.tableRow4 = new DevExpress.XtraReports.UI.XRTableRow();
            this.GroupFooter1 = new DevExpress.XtraReports.UI.GroupFooterBand();
            // this.tableCell6 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell28 = new DevExpress.XtraReports.UI.XRTableCell();
            this.label9 = new DevExpress.XtraReports.UI.XRLabel();
            this.tableCell9 = new DevExpress.XtraReports.UI.XRTableCell();
            this.TopMargin = new DevExpress.XtraReports.UI.TopMarginBand();
            // this.tableCell29 = new DevExpress.XtraReports.UI.XRTableCell();
            this.table1 = new DevExpress.XtraReports.UI.XRTable();
            this.label4 = new DevExpress.XtraReports.UI.XRLabel();
            this.tableCell18 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableRow5 = new DevExpress.XtraReports.UI.XRTableRow();
            this.tableCell4 = new DevExpress.XtraReports.UI.XRTableCell();
            this.docNum = new DevExpress.XtraReports.Parameters.Parameter();
            this.calculatedField1 = new DevExpress.XtraReports.UI.CalculatedField();
            this.tableCell11 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell10 = new DevExpress.XtraReports.UI.XRTableCell();
            this.pictureBox1 = new DevExpress.XtraReports.UI.XRPictureBox();
            this.table3 = new DevExpress.XtraReports.UI.XRTable();
            this.label6 = new DevExpress.XtraReports.UI.XRLabel();
            this.tableCell31 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell8 = new DevExpress.XtraReports.UI.XRTableCell();
            this.Detail = new DevExpress.XtraReports.UI.DetailBand();
            this.tableCell26 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell23 = new DevExpress.XtraReports.UI.XRTableCell();
            this.label3 = new DevExpress.XtraReports.UI.XRLabel();
            this.tableCell14 = new DevExpress.XtraReports.UI.XRTableCell();
            ((System.ComponentModel.ISupportInitialize)(this.table2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.table1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.table3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // tableCell5
            // 
            this.tableCell5.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
                        | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell5.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "SD_Delivery_Ld_View.CONT")});
            this.tableCell5.Dpi = 254F;
            this.tableCell5.Name = "tableCell5";
            this.tableCell5.StylePriority.UseBorders = false;
            this.tableCell5.StylePriority.UseTextAlignment = false;
            this.tableCell5.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell5.Weight = 0.49850216910746808D;
            // 
            // tableCell7
            // 
            this.tableCell7.Dpi = 254F;
            this.tableCell7.Name = "tableCell7";
            this.tableCell7.StylePriority.UseTextAlignment = false;
            this.tableCell7.Text = "价格";
            this.tableCell7.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell7.Weight = 0.32828563237796282D;
            // 
            // tableRow3
            // 
            this.tableRow3.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
                        this.tableCell18,
                        this.tableCell19});
            this.tableRow3.Dpi = 254F;
            this.tableRow3.Name = "tableRow3";
            this.tableRow3.Weight = 1D;
            // 
            // tableCell3
            // 
            this.tableCell3.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "SD_Delivery_Ld_View.CustomerName")});
            this.tableCell3.Dpi = 254F;
            this.tableCell3.Name = "tableCell3";
            this.tableCell3.StylePriority.UseTextAlignment = false;
            this.tableCell3.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.tableCell3.Weight = 2.81067648867478D;
            // 
            // label2
            // 
            this.label2.BorderWidth = 0F;
            this.label2.Dpi = 254F;
            this.label2.Font = new System.Drawing.Font("宋体", 16F);
            this.label2.LocationFloat = new DevExpress.Utils.PointFloat(232.6217F, 170.3917F);
            this.label2.Multiline = true;
            this.label2.Name = "label2";
            this.label2.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.label2.SizeF = new System.Drawing.SizeF(1702.858F, 67.73335F);
            this.label2.StylePriority.UseBorderWidth = false;
            this.label2.StylePriority.UseFont = false;
            this.label2.StylePriority.UseTextAlignment = false;
            this.label2.Text = "送货单";
            this.label2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // GroupHeader2
            // 
            this.GroupHeader2.Dpi = 254F;
            this.GroupHeader2.GroupFields.AddRange(new DevExpress.XtraReports.UI.GroupField[] {
                        new DevExpress.XtraReports.UI.GroupField("SettlementAdd", DevExpress.XtraReports.UI.XRColumnSortOrder.Ascending)});
            this.GroupHeader2.HeightF = 0F;
            this.GroupHeader2.Name = "GroupHeader2";
            // 
            // sqlDataSource1
            // 
            this.sqlDataSource1.ConnectionName = "10.18.0.86_XZ_WMS_Connection";
            msSqlConnectionParameters1.AuthorizationType = DevExpress.DataAccess.ConnectionParameters.MsSqlAuthorizationType.SqlServer;
            msSqlConnectionParameters1.DatabaseName = "XZ_WMS";
            msSqlConnectionParameters1.ServerName = "10.18.0.86";
            this.sqlDataSource1.ConnectionParameters = msSqlConnectionParameters1;
            this.sqlDataSource1.Name = "sqlDataSource1";
            columnExpression1.ColumnName = "DocNum";
            table4.MetaSerializable = "30|30|125|380";
            table4.Name = "SD_Delivery_Ld_View";
            columnExpression1.Table = table4;
            column1.Expression = columnExpression1;
            columnExpression2.ColumnName = "CustomerCode";
            columnExpression2.Table = table4;
            column2.Expression = columnExpression2;
            columnExpression3.ColumnName = "CustomerName";
            columnExpression3.Table = table4;
            column3.Expression = columnExpression3;
            columnExpression4.ColumnName = "CustomerAdd";
            columnExpression4.Table = table4;
            column4.Expression = columnExpression4;
            columnExpression5.ColumnName = "BaseNum";
            columnExpression5.Table = table4;
            column5.Expression = columnExpression5;
            columnExpression6.ColumnName = "CONT";
            columnExpression6.Table = table4;
            column6.Expression = columnExpression6;
            columnExpression7.ColumnName = "ItemCode";
            columnExpression7.Table = table4;
            column7.Expression = columnExpression7;
            columnExpression8.ColumnName = "ItemName";
            columnExpression8.Table = table4;
            column8.Expression = columnExpression8;
            columnExpression9.ColumnName = "DeliveryScanQty";
            columnExpression9.Table = table4;
            column9.Expression = columnExpression9;
            columnExpression10.ColumnName = "Telephone";
            columnExpression10.Table = table4;
            column10.Expression = columnExpression10;
            columnExpression11.ColumnName = "ItemName1";
            columnExpression11.Table = table4;
            column11.Expression = columnExpression11;
            columnExpression12.ColumnName = "CustomerOrderNum";
            columnExpression12.Table = table4;
            column12.Expression = columnExpression12;
            columnExpression13.ColumnName = "CUser";
            columnExpression13.Table = table4;
            column13.Expression = columnExpression13;
            columnExpression14.ColumnName = "BarCode";
            columnExpression14.Table = table4;
            column14.Expression = columnExpression14;
            columnExpression15.ColumnName = "Project";
            columnExpression15.Table = table4;
            column15.Expression = columnExpression15;
            columnExpression16.ColumnName = "SettlementAdd";
            columnExpression16.Table = table4;
            column16.Expression = columnExpression16;
            columnExpression17.ColumnName = "Price";
            columnExpression17.Table = table4;
            column17.Expression = columnExpression17;
            selectQuery1.Columns.Add(column1);
            selectQuery1.Columns.Add(column2);
            selectQuery1.Columns.Add(column3);
            selectQuery1.Columns.Add(column4);
            selectQuery1.Columns.Add(column5);
            selectQuery1.Columns.Add(column6);
            selectQuery1.Columns.Add(column7);
            selectQuery1.Columns.Add(column8);
            selectQuery1.Columns.Add(column9);
            selectQuery1.Columns.Add(column10);
            selectQuery1.Columns.Add(column11);
            selectQuery1.Columns.Add(column12);
            selectQuery1.Columns.Add(column13);
            selectQuery1.Columns.Add(column14);
            selectQuery1.Columns.Add(column15);
            selectQuery1.Columns.Add(column16);
            selectQuery1.Columns.Add(column17);
            selectQuery1.FilterString = "[SD_Delivery_Ld_View.DocNum] In (?docNum)";
            selectQuery1.Name = "SD_Delivery_Ld_View";
            queryParameter1.Name = "docNum";
            queryParameter1.Type = typeof(DevExpress.DataAccess.Expression);
            queryParameter1.Value = new DevExpress.DataAccess.Expression("[Parameters.docNum]", typeof(string));
            selectQuery1.Parameters.Add(queryParameter1);
            selectQuery1.Tables.Add(table4);
            this.sqlDataSource1.Queries.AddRange(new DevExpress.DataAccess.Sql.SqlQuery[] {
                        selectQuery1});
            this.sqlDataSource1.ResultSchemaSerializable = resources.GetString("sqlDataSource1.ResultSchemaSerializable");
            // 
            // tableCell1
            // 
            this.tableCell1.Dpi = 254F;
            this.tableCell1.Name = "tableCell1";
            this.tableCell1.Text = "收货单位";
            this.tableCell1.Weight = 0.67535487419784179D;
            // 
            // label10
            // 
            this.label10.BorderDashStyle = DevExpress.XtraPrinting.BorderDashStyle.Dot;
            this.label10.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.label10.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "SD_Delivery_Ld_View.CUser")});
            this.label10.Dpi = 254F;
            this.label10.LocationFloat = new DevExpress.Utils.PointFloat(879.9299F, 111.9717F);
            this.label10.Name = "label10";
            this.label10.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.label10.SizeF = new System.Drawing.SizeF(393.7577F, 58.42001F);
            this.label10.StylePriority.UseBorderDashStyle = false;
            this.label10.StylePriority.UseBorders = false;
            this.label10.StylePriority.UseTextAlignment = false;
            this.label10.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // tableCell19
            // 
            this.tableCell19.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "SD_Delivery_Ld_View.Telephone")});
            this.tableCell19.Dpi = 254F;
            this.tableCell19.Name = "tableCell19";
            this.tableCell19.StylePriority.UseTextAlignment = false;
            this.tableCell19.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.tableCell19.Weight = 3.46029065640119D;
            // 
            // label17
            // 
            this.label17.Dpi = 254F;
            this.label17.LocationFloat = new DevExpress.Utils.PointFloat(1.058317F, 118.9566F);
            this.label17.Name = "label17";
            this.label17.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.label17.SizeF = new System.Drawing.SizeF(200.5052F, 58.42F);
            this.label17.StylePriority.UseTextAlignment = false;
            this.label17.Text = "客户签收：";
            this.label17.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // label1
            // 
            this.label1.Dpi = 254F;
            this.label1.LocationFloat = new DevExpress.Utils.PointFloat(0F, 238.1251F);
            this.label1.Name = "label1";
            this.label1.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.label1.SizeF = new System.Drawing.SizeF(232.6217F, 59.79564F);
            this.label1.StylePriority.UseTextAlignment = false;
            this.label1.Text = "制表日期:";
            this.label1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // tableCell13
            // 
            this.tableCell13.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
                        | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell13.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "SD_Delivery_Ld_View.Project")});
            this.tableCell13.Dpi = 254F;
            this.tableCell13.Name = "tableCell13";
            this.tableCell13.StylePriority.UseBorders = false;
            this.tableCell13.StylePriority.UseTextAlignment = false;
            this.tableCell13.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell13.Weight = 0.45558942407780745D;
            // 
            // label8
            // 
            this.label8.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "SD_Delivery_Ld_View.CUser")});
            this.label8.Dpi = 254F;
            this.label8.LocationFloat = new DevExpress.Utils.PointFloat(1635.272F, 238.1251F);
            this.label8.Name = "label8";
            this.label8.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.label8.SizeF = new System.Drawing.SizeF(274.2783F, 59.7957F);
            this.label8.StylePriority.UseTextAlignment = false;
            this.label8.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // tableCell12
            // 
            this.tableCell12.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
                        | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell12.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "SD_Delivery_Ld_View.BarCode")});
            this.tableCell12.Dpi = 254F;
            this.tableCell12.Name = "tableCell12";
            this.tableCell12.StylePriority.UseBorders = false;
            this.tableCell12.StylePriority.UseTextAlignment = false;
            this.tableCell12.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell12.Weight = 0.56137855644444945D;
            // 
            // GroupHeader1
            // 
            this.GroupHeader1.Dpi = 254F;
            this.GroupHeader1.GroupFields.AddRange(new DevExpress.XtraReports.UI.GroupField[] {
                        new DevExpress.XtraReports.UI.GroupField("CustomerCode", DevExpress.XtraReports.UI.XRColumnSortOrder.Ascending)});
            this.GroupHeader1.HeightF = 0F;
            this.GroupHeader1.Level = 1;
            this.GroupHeader1.Name = "GroupHeader1";
            this.GroupHeader1.PageBreak = DevExpress.XtraReports.UI.PageBreak.AfterBand;
            // 
            // tableCell30
            // 
            this.tableCell30.Dpi = 254F;
            this.tableCell30.Multiline = true;
            this.tableCell30.Name = "tableCell30";
            this.tableCell30.StylePriority.UseTextAlignment = false;
            this.tableCell30.Text = "部件名称";
            this.tableCell30.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell30.Weight = 0.74881425498041909D;
            // 
            // tableCell2
            // 
            this.tableCell2.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "SD_Delivery_Ld_View.CustomerCode")});
            this.tableCell2.Dpi = 254F;
            this.tableCell2.Name = "tableCell2";
            this.tableCell2.Weight = 0.65070760140181672D;
            // 
            // tableRow2
            // 
            this.tableRow2.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
                        this.tableCell1,
                        this.tableCell2,
                        this.tableCell3});
            this.tableRow2.Dpi = 254F;
            this.tableRow2.Name = "tableRow2";
            this.tableRow2.Weight = 1D;
            // 
            // label19
            // 
            this.label19.BorderDashStyle = DevExpress.XtraPrinting.BorderDashStyle.Dot;
            this.label19.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.label19.Dpi = 254F;
            this.label19.LocationFloat = new DevExpress.Utils.PointFloat(171.9301F, 118.9566F);
            this.label19.Name = "label19";
            this.label19.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.label19.SizeF = new System.Drawing.SizeF(393.7577F, 58.42F);
            this.label19.StylePriority.UseBorderDashStyle = false;
            this.label19.StylePriority.UseBorders = false;
            this.label19.StylePriority.UseTextAlignment = false;
            this.label19.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // tableCell22
            // 
            this.tableCell22.Dpi = 254F;
            this.tableCell22.Name = "tableCell22";
            this.tableCell22.Text = "客户地址";
            this.tableCell22.Weight = 0.67644873420149532D;
            // 
            // tableRow1
            // 
            this.tableRow1.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
                        this.tableCell4,
                        this.tableCell5,
                        // this.tableCell6,
                        this.tableCell8,
                        this.tableCell12,
                        this.tableCell13,
                        this.tableCell10,
                        this.tableCell14});
            this.tableRow1.Dpi = 254F;
            this.tableRow1.Name = "tableRow1";
            this.tableRow1.Weight = 1D;
            // 
            // calculatedField2
            // 
            this.calculatedField2.Name = "calculatedField2";
            // 
            // table2
            // 
            this.table2.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
                        | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.table2.Dpi = 254F;
            this.table2.LocationFloat = new DevExpress.Utils.PointFloat(0.5291587F, 297.9207F);
            this.table2.Name = "table2";
            this.table2.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
                        this.tableRow2,
                        this.tableRow4,
                        this.tableRow3});
            this.table2.SizeF = new System.Drawing.SizeF(1937.068F, 269.8752F);
            this.table2.StylePriority.UseBorders = false;
            this.table2.StylePriority.UseTextAlignment = false;
            this.table2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // BottomMargin
            // 
            this.BottomMargin.Dpi = 254F;
            this.BottomMargin.HeightF = 56F;
            this.BottomMargin.Name = "BottomMargin";
            // 
            // tableRow4
            // 
            this.tableRow4.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
                        this.tableCell22,
                        this.tableCell23});
            this.tableRow4.Dpi = 254F;
            this.tableRow4.Name = "tableRow4";
            this.tableRow4.Weight = 1D;
            // 
            // GroupFooter1
            // 
            this.GroupFooter1.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
                        this.label9,
                        this.label10,
                        this.label19,
                        this.label17});
            this.GroupFooter1.Dpi = 254F;
            this.GroupFooter1.HeightF = 196F;
            this.GroupFooter1.Level = 1;
            this.GroupFooter1.Name = "GroupFooter1";
            // 
            // tableCell6
            // 
            // this.tableCell6.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            //             | DevExpress.XtraPrinting.BorderSide.Right) 
            //             | DevExpress.XtraPrinting.BorderSide.Bottom)));
            // this.tableCell6.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
            //             new DevExpress.XtraReports.UI.XRBinding("Text", null, "SD_Delivery_Ld_View.CustomerOrderNum")});
            // this.tableCell6.Dpi = 254F;
            // this.tableCell6.Name = "tableCell6";
            // this.tableCell6.StylePriority.UseBorders = false;
            // this.tableCell6.StylePriority.UseTextAlignment = false;
            // this.tableCell6.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // this.tableCell6.Weight = 0.39904000567902637D;
            // 
            // tableCell28
            // 
            this.tableCell28.Dpi = 254F;
            this.tableCell28.Name = "tableCell28";
            this.tableCell28.StylePriority.UseTextAlignment = false;
            this.tableCell28.Text = "订单数量";
            this.tableCell28.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell28.Weight = 0.27120491956351933D;
            // 
            // label9
            // 
            this.label9.Dpi = 254F;
            this.label9.LocationFloat = new DevExpress.Utils.PointFloat(678.8955F, 111.9717F);
            this.label9.Name = "label9";
            this.label9.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.label9.SizeF = new System.Drawing.SizeF(200.5052F, 58.42001F);
            this.label9.StylePriority.UseTextAlignment = false;
            this.label9.Text = "制表人：";
            this.label9.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // tableCell9
            // 
            this.tableCell9.Dpi = 254F;
            this.tableCell9.Name = "tableCell9";
            this.tableCell9.StylePriority.UseTextAlignment = false;
            this.tableCell9.Text = "出厂编号";
            this.tableCell9.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell9.Weight = 0.56075597546483158D;
            // 
            // TopMargin
            // 
            this.TopMargin.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
                        this.pictureBox1,
                        this.label8,
                        this.label6,
                        this.label4,
                        this.table3,
                        this.table2,
                        this.label1,
                        this.label2,
                        this.label3});
            this.TopMargin.Dpi = 254F;
            this.TopMargin.HeightF = 721.2543F;
            this.TopMargin.Name = "TopMargin";
            // 
            // tableCell29
            // 
            // this.tableCell29.Dpi = 254F;
            // this.tableCell29.Name = "tableCell29";
            // this.tableCell29.StylePriority.UseTextAlignment = false;
            // this.tableCell29.Text = "客户订单号";
            // this.tableCell29.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // this.tableCell29.Weight = 0.39859722841520973D;
            // 
            // table1
            // 
            this.table1.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.table1.Dpi = 254F;
            this.table1.LocationFloat = new DevExpress.Utils.PointFloat(1.587476F, 0F);
            this.table1.Name = "table1";
            this.table1.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
                        this.tableRow1});
            this.table1.SizeF = new System.Drawing.SizeF(1933.892F, 153.4583F);
            this.table1.StylePriority.UseBorders = false;
            // 
            // label4
            // 
            this.label4.BorderWidth = 0F;
            this.label4.Dpi = 254F;
            this.label4.Font = new System.Drawing.Font("宋体", 20F);
            this.label4.LocationFloat = new DevExpress.Utils.PointFloat(398.4127F, 75.67085F);
            this.label4.Multiline = true;
            this.label4.Name = "label4";
            this.label4.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.label4.SizeF = new System.Drawing.SizeF(1540.243F, 94.72088F);
            this.label4.StylePriority.UseBorderWidth = false;
            this.label4.StylePriority.UseFont = false;
            this.label4.StylePriority.UseTextAlignment = false;
            this.label4.Text = "浙江西子富沃德电机有限公司";
            this.label4.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // tableCell18
            // 
            this.tableCell18.Dpi = 254F;
            this.tableCell18.Name = "tableCell18";
            this.tableCell18.Text = "联系人/联系电话";
            this.tableCell18.Weight = 0.67644830787324861D;
            // 
            // tableRow5
            // 
            this.tableRow5.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
                        this.tableCell31,
                        this.tableCell26,
                        // this.tableCell29,
                        this.tableCell30,
                        this.tableCell9,
                        this.tableCell11,
                        this.tableCell28,
                        this.tableCell7});
            this.tableRow5.Dpi = 254F;
            this.tableRow5.Name = "tableRow5";
            this.tableRow5.Weight = 1D;
            // 
            // tableCell4
            // 
            this.tableCell4.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
                        | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell4.Dpi = 254F;
            this.tableCell4.Name = "tableCell4";
            this.tableCell4.StylePriority.UseBorders = false;
            this.tableCell4.StylePriority.UseTextAlignment = false;
            summary1.Func = DevExpress.XtraReports.UI.SummaryFunc.RecordNumber;
            summary1.Running = DevExpress.XtraReports.UI.SummaryRunning.Group;
            this.tableCell4.Summary = summary1;
            this.tableCell4.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell4.Weight = 0.21554828280498983D;
            // 
            // docNum
            // 
            this.docNum.Description = "docNum";
            this.docNum.MultiValue = true;
            this.docNum.Name = "docNum";
            // 
            // calculatedField1
            // 
            this.calculatedField1.Expression = "Now()";
            this.calculatedField1.Name = "calculatedField1";
            // 
            // tableCell11
            // 
            this.tableCell11.Dpi = 254F;
            this.tableCell11.Name = "tableCell11";
            this.tableCell11.StylePriority.UseTextAlignment = false;
            this.tableCell11.Text = "项目名称";
            this.tableCell11.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell11.Weight = 0.45508359033724893D;
            // 
            // tableCell10
            // 
            this.tableCell10.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
                        | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell10.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "SD_Delivery_Ld_View.DeliveryScanQty", "{0:#.##}")});
            this.tableCell10.Dpi = 254F;
            this.tableCell10.Name = "tableCell10";
            this.tableCell10.StylePriority.UseBorders = false;
            this.tableCell10.StylePriority.UseTextAlignment = false;
            this.tableCell10.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell10.Weight = 0.27062737048195828D;
            // 
            // pictureBox1
            // 
            this.pictureBox1.Dpi = 254F;
            this.pictureBox1.Image = ((System.Drawing.Image)(resources.GetObject("pictureBox1.Image")));
            this.pictureBox1.LocationFloat = new DevExpress.Utils.PointFloat(3.174911F, 75.67085F);
            this.pictureBox1.Name = "pictureBox1";
            this.pictureBox1.SizeF = new System.Drawing.SizeF(314.1078F, 94.72088F);
            this.pictureBox1.Sizing = DevExpress.XtraPrinting.ImageSizeMode.Squeeze;
            // 
            // table3
            // 
            this.table3.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.table3.Dpi = 254F;
            this.table3.LocationFloat = new DevExpress.Utils.PointFloat(1.058357F, 567.7959F);
            this.table3.Name = "table3";
            this.table3.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
                        this.tableRow5});
            this.table3.SizeF = new System.Drawing.SizeF(1934.422F, 153.4584F);
            this.table3.StylePriority.UseBorders = false;
            // 
            // label6
            // 
            this.label6.Dpi = 254F;
            this.label6.LocationFloat = new DevExpress.Utils.PointFloat(1465.621F, 236.7495F);
            this.label6.Name = "label6";
            this.label6.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.label6.SizeF = new System.Drawing.SizeF(169.1219F, 59.79568F);
            this.label6.StylePriority.UseTextAlignment = false;
            this.label6.Text = "制单人：";
            this.label6.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // tableCell31
            // 
            this.tableCell31.Dpi = 254F;
            this.tableCell31.Name = "tableCell31";
            this.tableCell31.StylePriority.UseTextAlignment = false;
            this.tableCell31.Text = "序号";
            this.tableCell31.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell31.Weight = 0.21704693885311971D;
            // 
            // tableCell8
            // 
            this.tableCell8.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
                        | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell8.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "SD_Delivery_Ld_View.ItemName")});
            this.tableCell8.Dpi = 254F;
            this.tableCell8.Multiline = true;
            this.tableCell8.Name = "tableCell8";
            this.tableCell8.StylePriority.UseBorders = false;
            this.tableCell8.StylePriority.UseTextAlignment = false;
            this.tableCell8.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell8.Weight = 0.74964636422781517D;
            // 
            // Detail
            // 
            this.Detail.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
                        this.table1});
            this.Detail.Dpi = 254F;
            this.Detail.HeightF = 153F;
            this.Detail.Name = "Detail";
            // 
            // tableCell26
            // 
            this.tableCell26.Dpi = 254F;
            this.tableCell26.Name = "tableCell26";
            this.tableCell26.StylePriority.UseTextAlignment = false;
            this.tableCell26.Text = "合同号";
            this.tableCell26.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell26.Weight = 0.49794892984420713D;
            // 
            // tableCell23
            // 
            this.tableCell23.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "SD_Delivery_Ld_View.CustomerAdd")});
            this.tableCell23.Dpi = 254F;
            this.tableCell23.Name = "tableCell23";
            this.tableCell23.StylePriority.UseTextAlignment = false;
            this.tableCell23.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.tableCell23.Weight = 3.4602902300729435D;
            // 
            // label3
            // 
            this.label3.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "calculatedField1", "{0:yyyy-MM-dd}")});
            this.label3.Dpi = 254F;
            this.label3.LocationFloat = new DevExpress.Utils.PointFloat(231.5633F, 238.1251F);
            this.label3.Name = "label3";
            this.label3.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.label3.SizeF = new System.Drawing.SizeF(351.8959F, 58.42F);
            this.label3.StylePriority.UseTextAlignment = false;
            this.label3.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // tableCell14
            // 
            this.tableCell14.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
                        | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell14.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "SD_Delivery_Ld_View.Price")});
            this.tableCell14.Dpi = 254F;
            this.tableCell14.Name = "tableCell14";
            this.tableCell14.StylePriority.UseBorders = false;
            this.tableCell14.StylePriority.UseTextAlignment = false;
            this.tableCell14.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell14.Weight = 0.32952828282048285D;
            // 
            // PPStockingWave
            // 
            this.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
                        this.TopMargin,
                        this.Detail,
                        this.BottomMargin,
                        this.GroupHeader1,
                        this.GroupFooter1,
                        this.GroupHeader2});
            this.CalculatedFields.AddRange(new DevExpress.XtraReports.UI.CalculatedField[] {
                        this.calculatedField1,
                        this.calculatedField2});
            this.ComponentStorage.AddRange(new System.ComponentModel.IComponent[] {
                        this.sqlDataSource1});
            this.DataMember = "SD_Delivery_Ld_View";
            this.DataSource = this.sqlDataSource1;
            this.Dpi = 254F;
            this.Font = new System.Drawing.Font("宋体", 10.2F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Margins = new System.Drawing.Printing.Margins(74, 87, 721, 56);
            this.Name = "PPStockingWave";
            this.PageHeight = 2400;
            this.PageWidth = 2100;
            this.PaperKind = System.Drawing.Printing.PaperKind.Custom;
            this.Parameters.AddRange(new DevExpress.XtraReports.Parameters.Parameter[] {
                        this.docNum});
            this.ReportUnit = DevExpress.XtraReports.UI.ReportUnit.TenthsOfAMillimeter;
            this.SnapGridSize = 31.75F;
            this.Version = "16.1";
            ((System.ComponentModel.ISupportInitialize)(this.table2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.table1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.table3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();
        }
    }
}
