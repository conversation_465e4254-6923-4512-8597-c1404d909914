using HZ.Core.Http;
using HZ.Core.Office;
using HZ.WMS.Application;
using HZ.WMS.Application.MM;
using HZ.WMS.Entity.MM;
using HZ.WMS.Entity.MM.Parameters;
using HZ.WMS.Entity.Sys;
using HZ.WMS.WebAPI.Controllers;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Web;
using System.Web.Http;

namespace HZ.WMS.WebAPI.Areas.MM.Controllers
{
    /// <summary>
    /// 磁材委外入库
    /// </summary>
    public class MM_MagnetsWarehousingController : ApiBaseController
    {
        #region 初始化

        private MM_MagnetsWarehousingApp _app = new MM_MagnetsWarehousingApp();

        #endregion

        #region PC

        #region 查询分页列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="page">分页</param>
        /// <param name="keyword">参数</param>
        /// <param name="dateValue">日期区间</param>
        /// <param name="isPosted">是否过账</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]string keyword, [FromUri] DateTime[] dateValue, [FromUri] bool? isPosted)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];

                var itemsData = _app.GetPageList(page, t => (string.IsNullOrEmpty(keyword)
                        || t.DocNum.Contains(keyword)
                        || t.CUser.Contains(keyword)
                        || t.ItemCode.Contains(keyword) || t.ItemName.Contains(keyword)
                        || t.SupplierCode.Contains(keyword) || t.SupplierName.Contains(keyword)
                        || t.WhsCode.Contains(keyword) || t.WhsName.Contains(keyword)
                        || t.RegionCode.Contains(keyword) || t.RegionName.Contains(keyword)
                        || t.BinLocationCode.Contains(keyword) || t.BinLocationName.Contains(keyword)
                        || t.PostUser.Contains(keyword)
                ) && (t.CTime >= fromTime && t.CTime <= toTime)
                && (isPosted == null || t.IsPosted == isPosted)).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="ids">根据ID进行删除</param>
        /// <returns></returns>
        [HttpDelete]
        //[AllowAnonymous]
        public IHttpActionResult Delete([FromBody]string[] ids)
        {
            //[FromBody]string[] ids
            var result = new ResponseData();
            try
            {
                string error_message = "";
                bool bDelete = _app.Deletes(ids, GetCurrentUser().LoginAccount, out error_message);
                if (!bDelete || !string.IsNullOrEmpty(error_message))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }

            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 导出

        /// <summary>
        /// 导出
        /// </summary>
        /// <param name="keyword"></param>
        /// <param name="dateValue"></param>
        /// <param name="isPosted">是否过账</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] string keyword, [FromUri] DateTime[] dateValue, [FromUri] bool? isPosted)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];
                var itemsData =_app.GetList().Where(t => string.IsNullOrEmpty(keyword)
                        || t.DocNum.Contains(keyword) 
                        || t.ItemCode.Contains(keyword) || t.SupplierCode.Contains(keyword)
                        || t.SupplierName.Contains(keyword) || t.ItemName.Contains(keyword)
                        || t.WhsCode.Contains(keyword) || t.WhsName.Contains(keyword)
                        || t.RegionCode.Contains(keyword) //|| t.PostUser.Contains(keyword) 
                        || t.CUser.Contains(keyword) || t.RegionCode.Contains(keyword)
                        || t.RegionName.Contains(keyword) || t.BinLocationCode.Contains(keyword)
                        || t.BinLocationName.Contains(keyword)
                        && (isPosted == null || t.IsPosted == isPosted)).Where(x => x.CTime >= fromTime && x.CTime <= toTime && !x.IsDelete).ToList();
                //var itemsData = _app.GetList().ToList();
                List<ExcelColumn<MM_MagnetsWarehousing>> columns = ExcelService.FetchDefaultColumnList<MM_MagnetsWarehousing>();
                string[] ignoreField = new string[]
                {
                    //"SubcontractingApplicationID",
                    //"DocNum",
                    //"SubcontractingApplicationNum",
                    //"ItemCode",
                    //"ItemName",
                    //"SupplierCode",
                    //"SupplierName",
                    //"SubcontractingApplicationQty",
                    //"Unit",
                    //"WhsCode",
                    //"WhsName",
                    //"RegionCode",
                    //"RegionName",
                    //"BinLocationCode",
                    //"BinLocationName",
                    //"Remark",
                    //"CUser",
                    //"CTime"
                    
                };
                List<ExcelColumn<MM_MagnetsWarehousing>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<MM_MagnetsWarehousing> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                //columns.ForEach((column) =>
                //{
                //    if (column.ColumnName == "IsPosted")
                //    {
                //        column.Formattor = ExcelExportFormatter.IsOrNoFormatter;
                //    }
                //});

                return ExportToExcelFile<MM_MagnetsWarehousing>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion
        
        #region 手动过账

        /// <summary>
        ///手动过账
        /// </summary>
        /// <param name="entities"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult DoPost([FromBody]List<MM_MagnetsWarehousing> entities)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                Sys_User currentUser = GetCurrentUser();
                bool bSubmit = _app.DoPost(entities, currentUser.LoginAccount, out error_message);

                if (!bSubmit)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }
        #endregion

        #endregion

        #region Mobile

        #region 查询磁材委外入库信息

        /// <summary>
        /// 查询磁材委外入库信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetStockInfo([FromUri] string BarCode)
        {
            string error_message;
            var result = new ResponseData();
            if (_app.CheckDate(BarCode, out error_message))//校验
            {
                try
                {
                    var itemsData = _app.GetStockInfo(BarCode).ToList();
                    result.Data = itemsData;
                    result.Code = (int)WMSStatusCode.Success;
                }
                catch (Exception ex)
                {
                    result.Code = (int)WMSStatusCode.UnHandledException;
                    result.Message = ex.InnerException?.Message ?? ex.Message;
                }
            }
            else
            {
                result.Code = (int)WMSStatusCode.Failed;
                result.Message = error_message;
            }
            return Json(result);
        }

        #endregion

        #region 保存

        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="Parameters">磁材委外入库列表</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Save([FromBody]MM_MagnetsWarehousingParameters Parameters)
        {

            var result = new ResponseData();
            try
            {
                string error_message = "";
                Sys_User currentUser = GetCurrentUser();

                bool bSubmit = _app.Save(Parameters.mowList, Parameters.ManualPostTime, currentUser, out error_message);

                if (!bSubmit || !string.IsNullOrEmpty(error_message))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }
        #endregion

        #endregion

    }
}