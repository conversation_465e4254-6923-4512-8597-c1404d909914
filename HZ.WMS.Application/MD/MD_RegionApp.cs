using SqlSugar;
using System;
using System.Collections.Generic;

using HZ.Core.Security;
using HZ.WMS.Entity.MD;
using HZ.Core.Http;

namespace HZ.WMS.Application.MD
{
    /// <summary>
    /// 仓库(warehouse)->区域(region*)
    /// </summary>

    public class MD_RegionApp : BaseApp<MD_Region>
    {
        private MD_BinLocationApp _binLocationApp = new MD_BinLocationApp();

        /// <summary>
        /// 质量管控仓（区域）
        /// </summary>
        public const string INSPECTION_REGIONCODE = "QM1";  //质量管控仓

        /// <summary>
        /// 
        /// </summary>
        public const string MATERIAL_REGIONCODE = "RW1";    //物品仓

        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public MD_RegionApp() : base()
        {
        }




        #endregion

        #region 获取特定区域下的所有库位

        /// <summary>
        /// 获取指定区域下的所有库位
        /// </summary>
        /// <param name="regionCode"></param>
        /// <returns></returns>
        public List<MD_BinLocation> GetRegionBinLocation(string regionCode)
        {
            return _binLocationApp.GetList(x => x.RegionCode == regionCode).ToList();
        }

        #endregion

        #region 获取库位所属区域

        /// <summary>
        /// 获取库位所属区域
        /// </summary>
        /// <param name="binLocationCode"></param>
        /// <returns></returns>
        public MD_Region GetRegionByBinLocation(string binLocationCode)
        {
            MD_BinLocation binLocation = _binLocationApp.GetFirstEntity(x => x.BinLocationCode == binLocationCode);
            if (binLocation != null)
            {
                return GetFirstEntity(x => x.RegionCode == binLocation.RegionCode);
            }
            else
            {
                throw new Exception("Common.NotExistBinLocation");
            }
            
        }

        #endregion

        


    }
}

