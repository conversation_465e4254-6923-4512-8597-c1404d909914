using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.SAP.View
{
    /// <summary>
    /// SAP销售订单信息
    /// </summary>
    public class SAP_VBAK_View
    {
        /// <summary>
        /// 销售凭证
        /// </summary>
        public string VBELN { get; set; }

        /// <summary>
        /// 创建日期
        /// </summary>
        public DateTime? ERDAT { get; set; }

        /// <summary>
        /// 时间
        /// </summary>
        public string ERZET { get; set; }

        /// <summary>
        /// 凭证日期
        /// </summary>
        public DateTime? AUDAT { get; set; }

        /// <summary>
        /// 销售凭证类型
        /// </summary>
        public string AUART { get; set; }

        /// <summary>
        /// 销售组织
        /// </summary>
        public string VKORG { get; set; }

        /// <summary>
        /// 分销渠道
        /// </summary>
        public string VTWEG { get; set; }

        /// <summary>
        /// 产品组
        /// </summary>
        public string SPART { get; set; }

        /// <summary>
        /// 客户参考
        /// </summary>
        public string BSTNK { get; set; }

        /// <summary>
        /// 售达方
        /// </summary>
        public string KUNNR { get; set; }

        /// <summary>
        /// 项目
        /// </summary>
        public int? POSNR { get; set; }

        /// <summary>
        /// 物料
        /// </summary>
        public string MATNR { get; set; }

        /// <summary>
        /// 项目类别
        /// </summary>
        public string PSTYV { get; set; }

        /// <summary>
        /// 净值 
        /// </summary>
        public decimal? NETWR { get; set; }

        /// <summary>
        /// 凭证货币
        /// </summary>
        public string WAERK { get; set; }

        /// <summary>
        /// 订单数量
        /// </summary>
        public decimal? KWMENG { get; set; }

        /// <summary>
        /// 参考凭证
        /// </summary>
        public string VGBEL { get; set; }

        /// <summary>
        /// 参考项目
        /// </summary>
        public int? VGPOS { get; set; }

        /// <summary>
        /// 库存地点
        /// </summary>
        public string LGORT { get; set; }

        /// <summary>
        /// 承诺的交货日期
        /// </summary>
        public DateTime? CMTD_DELIV_DATE { get; set; }

        /// <summary>
        /// 销售交货批
        /// </summary>
        public int? ZDELBA { get; set; }

        /// <summary>
        /// 合同编号
        /// </summary>
        public string ZORD_CONT { get; set; }

        /// <summary>
        /// 生产主机编号/客户出厂编号
        /// </summary>
        public string ZORD_OUTNO { get; set; }

        /// <summary>
        /// 装运点/收货点
        /// </summary>
        public string VSTEL { get; set; }

        /// <summary>
        /// 利润中心
        /// </summary>
        public string PRCTR { get; set; }

        /// <summary>
        /// 客户名称
        /// </summary>
        public string KUNNRNAME1 { get; set; }

        /// <summary>
        /// 客户地址
        /// </summary>
        public string KUNNRADDRESS { get; set; }

        /// <summary>
        /// 物料描述
        /// </summary>
        public string MAKTX { get; set; }

        /// <summary>
        /// 物料描述
        /// </summary>
        public decimal? NTGEW { get; set; }

        /// <summary>
        /// 基本单位欸
        /// </summary>
        public string MEINS { get; set; }
        
    }
}
