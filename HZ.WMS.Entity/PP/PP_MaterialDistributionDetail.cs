using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.PP
{
    /// <summary>
    /// 生产物料配送单子表
    /// </summary>
    [SugarTable("PP_MaterialDistributionDetail")]
    public class PP_MaterialDistributionDetail : BaseEntity
    {
        /// <summary>
        /// 主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string ID { get; set; }
        /// <summary>
        /// 配送单号
        /// </summary>
        [Description("配送单号")]
        public string DeliveryOrderNo { get; set; }
        /// <summary>
        /// 配送单行号
        /// </summary>
        [Description("配送单行号")]
        public int? DeliveryLineNo { get; set; }
        /// <summary>
        /// 生产订单
        /// </summary>
        [Description("生产订单")]
        public string ProductionOrderNo { get; set; }
        /// <summary>
        /// 序列号
        /// </summary>
        [Description("出厂编号")]
        public string SerialNo { get; set; }
        /// <summary>
        /// 合同编号
        /// </summary>
        [Description("合同编号")]
        public string ContractNo { get; set; }
        /// <summary>
        /// 开始日期(装配日期)
        /// </summary>
        [Description("装配日期")]
        public DateTime? StartTime { get; set; }
        /// <summary>
        /// 线体编码
        /// </summary>
        [Description("线体编码")]
        public string ProductionLineCode { get; set; }
        /// <summary>
        /// 线体描述
        /// </summary>
        [Description("装配线")]
        public string ProductionLineDes { get; set; }
        /// <summary>
        /// 配置描述
        /// </summary>
        [Description("配置描述")]
        public string ItemName { get; set; }
        /// <summary>
        /// 组件行项目号
        /// </summary>
        [Description("组件行项目号")]
        public decimal ComponentLineNo { get; set; }
        /// <summary>
        /// 组件编码
        /// </summary>
        [Description("物料件号")]
        public string ComponentCode { get; set; }
        /// <summary>
        /// 物料描述
        /// </summary>
        [Description("物料描述")]
        public string MaterialName { get; set; }
        /// <summary>
        /// 需求数量
        /// </summary>
        [Description("配送数量")]
        public decimal DemandQty { get; set; }
        /// <summary>
        /// 供应商数量
        /// </summary>
        [Description("供应商数量")]
        public string Supplier { get; set; }
        /// <summary>
        /// 自制数量
        /// </summary>
        [Description("自制数量")]
        public decimal? HomemadeQty { get; set; }
        /// <summary>
        /// 客户
        /// </summary>
        [Description("客户")]
        public string Customer { get; set; }
        /// <summary>
        /// 生产批次
        /// </summary>
        [Description("生产批次")]
        public string ProductionBatch { get; set; }
        /// <summary>
        /// 生产排序
        /// </summary>
        [Description("生产排序")]
        public decimal? ProductionSequencing { get; set; }
        /// <summary>
        /// 工厂
        /// </summary>
        [Description("工厂")]
        public string FactoryCode { get; set; }
        /// <summary>
        /// 销售订单
        /// </summary>
        [Description("销售订单")]
        public string SalesOrderNo { get; set; }
        /// <summary>
        /// 销售订单行项目
        /// </summary>
        [Description("销售订单行项目")]
        public decimal? SalesOrderLineNo { get; set; }
        /// <summary>
        /// 员工号
        /// </summary>
        [Description("员工号")]
        public string EmployeeNumber { get; set; }
        /// <summary>
        /// 员工姓名
        /// </summary>
        [Description("员工姓名")]
        public string EmployeeName { get; set; }
        /// <summary>
        /// 组件单位
        /// </summary>
        [Description("单位")]
        public string ComponentUnit { get; set; }
        /// <summary>
        /// 转出仓库
        /// </summary>
        [Description("转出仓库")]
        public string OutWarehouse { get; set; }
        /// <summary>
        /// 发料库存地
        /// </summary>
        [Description("转入仓库")]
        public string DeliverLocation { get; set; }
        /// <summary>
        /// 物料组
        /// </summary>
        [Description("物料组")]
        public string MaterialGroupCode { get; set; }
        /// <summary>
        /// 物料组描述
        /// </summary>
        [Description("物料组描述")]
        public string MaterialGroupDes { get; set; }
        /// <summary>
        /// 特殊库存标识
        /// </summary>
        [Description("特殊库存标识")]
        public string SpecialInventory { get; set; }
        /// <summary>
        /// 评估类别，为B时评估类型为01自制02外购
        /// </summary>
        [Description("评估类别")]
        public string AssessmentCategory { get; set; }
        /// <summary>
        /// 评估类型，01自制02外购
        /// </summary>
        [Description("评估类型")]
        public string AssessmentType { get; set; }
        /// <summary>
        /// 是否过账
        /// </summary>
        [Description("是否过账")]
        public bool? IsDoPost { get; set; }
        /// <summary>
        /// 手动过账时间
        /// </summary>
        [Description("手动过账时间")]
        public DateTime? ManualPostTime { get; set; }
        /// <summary>
        /// 过账时间
        /// </summary>
        [Description("过账时间")]
        public DateTime? DoPostTime { get; set; }
        /// <summary>
        /// 过账人
        /// </summary>
        [Description("过账人")]
        public string DoPostMan { get; set; }
        /// <summary> 
        /// sap生成单号
        /// </summary> 
        [Description("sap生成单号")]
        public string SapDocNum { get; set; }
        /// <summary> 
        /// sap生成行号
        /// </summary> 
        [Description("sap生成行号")]
        public int? SapLine { get; set; }
    }
}
