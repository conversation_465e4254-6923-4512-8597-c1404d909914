using System.Collections.Generic;
using System.ComponentModel;

namespace AOS.OMS.Entity.Sale
{
    public class CreateProdOrder
    {
        
        /// <summary>
        /// 状态
        /// </summary>  
        [Description("状态")]
        public string ETYPE { get; set; }
        
        /// <summary>
        /// 消息
        /// </summary>  
        [Description("消息")]
        public string EMSG { get; set; }
        
        /// <summary>
        /// 消息
        /// </summary>  
        [Description("消息")]
        public List<FG001060Model> DataIn { get; set; }
        
        /// <summary>
        /// 消息
        /// </summary>  
        [Description("消息")]
        public List<FG001060Model> DataOut { get; set; }
        
        public class FG001060Model
        {
        
            /// <summary>
            /// 工厂
            /// </summary>  
            [Description("工厂")]
            public string WERKS { get; set; }
        
            /// <summary>
            /// 销售订单号
            /// </summary>  
            [Description("销售订单号")]
            public string VBELN { get; set; }
        
            /// <summary>
            /// 销售订单行
            /// </summary>  
            [Description("销售订单行")]
            public string POSNR { get; set; }
        
            /// <summary>
            /// 消息类型
            /// </summary>  
            [Description("消息类型")]
            public string ETYPE { get; set; }

            /// <summary>
            /// 消息文本
            /// </summary>  
            [Description("消息文本")]
            public string EMSG { get; set; }

            /// <summary>
            /// 物料编码
            /// </summary>  
            [Description("物料编码")]
            public string MATNR { get; set; }

            /// <summary>
            /// 生产订单号
            /// </summary>  
            [Description("生产订单号")]
            public string AUFNR { get; set; }
        
        }
        
    }
}