using System;

namespace HZ.WMS.Entity.Produce.Res
{
    /// <summary>
    /// 工序信息响应
    /// </summary>
    public class WorkProcessRes
    {
        /// <summary>
        /// 订单号
        /// </summary>
        public string AUFNR { get; set; }

        /// <summary>
        /// 工序号
        /// </summary>
        public decimal VORNR { get; set; }

        /// <summary>
        /// 工作中心
        /// </summary>
        public string ARBPL { get; set; }

        /// <summary>
        /// 控制码
        /// </summary>
        public string STEUS { get; set; }

        /// <summary>
        /// 工序短文本
        /// </summary>
        public string LTXA1 { get; set; }

        /// <summary>
        /// 工作中心名称
        /// </summary>
        public string WorkCenterName { get; set; }

        /// <summary>
        /// 状态 1正常  2等待报工
        /// </summary>
        public int Status { get; set; }
    }
} 