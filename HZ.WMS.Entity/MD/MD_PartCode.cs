using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.MD
{
    /// <summary>
    /// 部件代码
    /// </summary>
    public class MD_PartCode : BaseEntity
    {
        /// <summary>
        /// 主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string ID { get; set; }
        /// <summary>
        /// SAP代码
        /// </summary>
        public string SAPCode { get; set; }
        /// <summary>
        /// 客户代码
        /// </summary>
        public string CustomerCode { get; set; }
        /// <summary>
        /// 客户名称
        /// </summary>
        public string CustomerName { get; set; }
        /// <summary>
        /// 客户指定代码
        /// </summary>
        public string CustomCode { get; set; }
        /// <summary>
        /// 年份
        /// </summary>
        public string OldYear { get; set; } = DateTime.Now.Year.ToString();
        /// <summary>
        /// 流水号
        /// </summary>
        public int OldSerial { get; set; } = 0;
        /// <summary>
        /// 年份
        /// </summary>
        public string NewYear { get; set; } = DateTime.Now.Year.ToString();
        /// <summary>
        /// 流水号
        /// </summary>
        public int NewSerial { get; set; } = 0;
    }
}
