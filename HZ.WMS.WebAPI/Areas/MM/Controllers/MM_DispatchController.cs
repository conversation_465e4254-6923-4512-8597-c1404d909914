using HZ.Core.Http;
using HZ.Core.Office;
using HZ.WMS.Application;
using HZ.WMS.Application.MM;
using HZ.WMS.Entity.MM;
using HZ.WMS.Entity.MM.Parameters;
using HZ.WMS.Entity.Sys;
using HZ.WMS.WebAPI.Controllers;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Web;
using System.Web.Http;

namespace HZ.WMS.WebAPI.Areas.MM.Controllers
{
    /// <summary>
    /// 委外发料
    /// </summary>
    public class MM_DispatchController : ApiBaseController
    {
        #region 初始化

        private MM_DispatchApp _app = new MM_DispatchApp();

        #endregion

        #region PC

        #region 查询分页列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="page">分页</param>
        /// <param name="keyword">参数</param>
        /// <param name="dateValue">日期区间</param>
        /// <param name="isPosted">是否过账</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]string keyword, [FromUri] DateTime[] dateValue, [FromUri] bool? isPosted)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];

                var itemsData = _app.GetPageList(page, t => (string.IsNullOrEmpty(keyword)
                        || t.DocNum.Contains(keyword) || t.SubcontractingApplicationNum.Contains(keyword)
                        || t.CUser.Contains(keyword) || t.PurchaseNum.Contains(keyword)
                        || t.ItemCode.Contains(keyword) || t.ItemName.Contains(keyword)
                        || t.SupplierCode.Contains(keyword) || t.SupplierName.Contains(keyword)
                        || t.WhsCode.Contains(keyword) || t.WhsName.Contains(keyword)
                        || t.RegionCode.Contains(keyword) || t.RegionName.Contains(keyword)
                        || t.BinLocationCode.Contains(keyword) || t.BinLocationName.Contains(keyword)
                        || t.PostUser.Contains(keyword)
                ) && (t.CTime >= fromTime && t.CTime <= toTime)
                && (isPosted == null || t.IsPosted == isPosted)).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="ids">根据ID进行删除</param>
        /// <returns></returns>
        [HttpDelete]
        //[AllowAnonymous]
        public IHttpActionResult Delete([FromBody]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                bool bDelete = _app.Deletes(ids, GetCurrentUser().LoginAccount, out error_message);
                if (!bDelete || !string.IsNullOrEmpty(error_message))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }

            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 导出

        /// <summary>
        /// 导出
        /// </summary>
        /// <param name="keyword">参数</param>
        /// <param name="dateValue">日期区间</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] string keyword, [FromUri] DateTime[] dateValue, [FromUri] bool? isPosted)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];
                var itemsData =_app.GetList().Where(t => string.IsNullOrEmpty(keyword)
                        || t.DocNum.Contains(keyword) || t.SubcontractingApplicationNum.Contains(keyword)
                        || t.ItemCode.Contains(keyword) || t.SupplierCode.Contains(keyword)
                        || t.SupplierName.Contains(keyword) || t.ItemName.Contains(keyword)
                        || t.WhsCode.Contains(keyword) || t.WhsName.Contains(keyword)
                        || t.RegionCode.Contains(keyword) //|| t.PostUser.Contains(keyword) 
                        || t.CUser.Contains(keyword) || t.RegionCode.Contains(keyword)
                        || t.RegionName.Contains(keyword) || t.BinLocationCode.Contains(keyword)
                        || t.BinLocationName.Contains(keyword) && (isPosted == null || t.IsPosted == isPosted)).
                        Where(x => x.CTime >= fromTime && x.CTime <= toTime && !x.IsDelete).ToList();
                //var itemsData = _app.GetList().ToList();
                var columns = ExcelService.FetchDefaultColumnList<MM_Dispatch>();
                string[] ignoreField = new string[]
                {
                    "OutsourcingDispatchID",
                    "SubcontractingApplicationDetailID",
                    "Line",
                    "BaseNum",
                    "BaseLine",
                    "PurchaseNum",
                    "PurchaseLine",
                    "BaseEntry",
                    "BarCode",
                    "SubcontractingApplicationLine",
                    "CompanyCode",
                    "FactoryCode",
                    "MovementType",
                    "SpecialInventory", "IsDelete","MUser","MTime", "DUser", "DTime",
                    "SAPmark","SAPmessage "
                };
                var ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<MM_Dispatch> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                columns.ForEach((column) =>
                {
                    if (column.ColumnName == "IsPosted")
                    {
                        column.Formattor = ExcelExportFormatter.IsOrNoFormatterForBoolean;
                    }
                });

                return ExportToExcelFile(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #region 打印

        /// <summary>
        /// 打印
        /// </summary>
        /// <param name="docNums">单号数组</param>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IHttpActionResult Print([FromUri]string[] docNums)
        {
            var result = new ResponseData();
            string templateCode = "委外发料单.repx";
            if (docNums == null || docNums.Length == 0 || string.IsNullOrEmpty(templateCode))
            {
                result.Code = (int)WMSStatusCode.Failed;
            }
            else
            {
                //MD_LabelTempleteApp _labelTemplateApp = new MD_LabelTempleteApp();
                //MD_LabelTemplete template = _labelTemplateApp.GetFirstEntity(x => x.Remark == templateCode);
                //if (template == null)
                //{
                //    result.Code = (int)WMSStatusCode.Failed;
                //}
                //else
                //{
                DevExpress.XtraReports.UI.XtraReport report =
                    DevExpress.XtraReports.UI.XtraReport.FromFile(HttpContext.Current.Server.MapPath("~/") + ConfigurationManager.AppSettings["LabelTemplateUploadPath"]?.ToString() + templateCode, true);
                DevExpress.DataAccess.Sql.SqlDataSource dataSource = report.DataSource as DevExpress.DataAccess.Sql.SqlDataSource;
                //switch (templateCode)
                //{
                //    case "PP_BarCodeReturn":
                //    case "PP_BarCode":
                //    case "MM_BarCode":
                DevExpress.DataAccess.Sql.QueryParameter parameter = new DevExpress.DataAccess.Sql.QueryParameter()
                {
                    Name = "docNums",
                    Type = typeof(List<string>),
                    Value = docNums.ToList()
                };

                dataSource.Queries[0].Parameters[0] = parameter;
                //dataSource.Fill();
                return base.PrintToPDF(report);

                //default:
                //    break;


            }
            return Json(result);
        }

        #endregion

        #region 过账

        /// <summary>
        ///过账
        /// </summary>
        /// <param name="entities">集合</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult DoPost([FromBody]List<MM_Dispatch> entities)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                Sys_User currentUser = GetCurrentUser();
                bool bPost = _app.DoPost(entities, currentUser.LoginAccount, out error_message);
                if (!bPost)//过账失败==2000，前台处理过账失败后重新刷新界面信息
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2010;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2000;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }

        #endregion

        #region 冲销过账

        /// <summary>
        /// 过账SAP
        /// </summary>
        /// <param name="entities">主信息</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult PassPost([FromBody] List<MM_Dispatch> entities)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";


                Sys_User currentUser = GetCurrentUser();
                bool bPost = _app.PassPost(entities, currentUser.LoginAccount, out error_message);
                if (!bPost)//过账失败==2000，前台处理过账失败后重新刷新界面信息
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2010;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2000;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }

        #endregion

        #endregion

        #region Mobile

        #region 查询委外申请单信息

        /// <summary>
        /// 查询委外申请单信息
        /// </summary>
        /// <param name="DocNum">单号</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetSubcontractingApplicationInfo([FromUri] string DocNum)
        {
            string error_message;
            var result = new ResponseData();
            if (_app.CheckDate(DocNum, out error_message))//校验
            {
                try
                {
                    var itemsData = _app.GetDispatchForPickingApplyInfo(DocNum, out error_message).ToList();
                    if (itemsData != null && itemsData.Count > 0)
                    {
                        result.Data = itemsData;
                        result.Code = (int)WMSStatusCode.Success;
                    }
                    else
                    {
                        result.Message=error_message;
                        result.Code = (int)WMSStatusCode.Success;
                    }
                }
                catch (Exception ex)
                {
                    result.Code = (int)WMSStatusCode.UnHandledException;
                    result.Message = ex.InnerException?.Message ?? ex.Message;
                }
            }
            else
            {
                result.Code = (int)WMSStatusCode.Success;
                result.Message = error_message;
            }
            return Json(result);
        }

        #endregion

        #region 保存

        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="Parameters">委外发料列表</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Save([FromBody]MM_DispatchParameters Parameters)
        {

            var result = new ResponseData();
            try
            {
                string error_message = "";
                string type = ""; //类型 1:提交失败 2：过账失败
                Sys_User currentUser = GetCurrentUser();
                bool bSubmit = _app.Save(Parameters, currentUser.LoginAccount, out error_message, out type);
                if (!bSubmit && type == "1")
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
                else if (!bSubmit && type == "2")
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }

        #endregion

        #endregion

    }
}