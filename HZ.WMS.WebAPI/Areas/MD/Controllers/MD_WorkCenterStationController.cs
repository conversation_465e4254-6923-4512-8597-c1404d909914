using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;
using HZ.Core.Http;
using HZ.Core.Office;
using HZ.WMS.Application.MD;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.MD.Import;
using HZ.WMS.WebAPI.Controllers;

namespace HZ.WMS.WebAPI.Areas.MD.Controllers
{
    /// <summary>
    /// 工作中心站点
    /// </summary>
    public class MD_WorkCenterStationController : ApiBaseController
    {
        private MD_WorkCenterStationApp _app = new MD_WorkCenterStationApp();

        #region 查询分页列表

        /// <summary>
        /// 查询分页列表 
        /// </summary>
        /// <param name="page">分页</param>
        /// <param name="req">查询参数</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]MD_WorkCenterStationListReq req)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetPageList(page, x => (string.IsNullOrEmpty(req.WorkCenterName) 
                    || x.WorkCenterName.Contains(req.WorkCenterName))
                    && (string.IsNullOrEmpty(req.WorkCenterCode)
                    || x.WorkCenterCode.Contains(req.WorkCenterCode))
                    && (string.IsNullOrEmpty(req.StationCode)
                    || x.StationCode.Contains(req.StationCode))
                    && (string.IsNullOrEmpty(req.StationName)
                    || x.StationName.Contains(req.StationName)));
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取详情
        /// <summary>
        /// 获取详情
        /// </summary>
        /// <param name="ID">记录ID</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetEntity([FromUri]string ID)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.GetEntityByKey(ID);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 添加

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="entity">实体</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Insert([FromBody]MD_WorkCenterStation entity)
        {
            var result = new ResponseData();
            try
            {
                entity.CUser = GetCurrentUser().LoginAccount;
                entity.CTime = DateTime.Now;
                
                result.Data = _app.Insert(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 修改

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="entity">实体</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Update([FromBody]MD_WorkCenterStation entity)
        {
            var result = new ResponseData();
            try
            {
                entity.MUser = GetCurrentUser().LoginAccount;
                entity.MTime = DateTime.Now;
                
                result.Data = _app.Update(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除单条记录
        /// </summary>
        /// <param name="ID">记录ID</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult DeleteSingle([FromBody]string ID)
        {
            var result = new ResponseData();
            try
            {
                // 检查是否被报工站点引用
                if (_app.IsReferencedByReportStation(ID))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "该工作中心站点已被报工站点引用，无法删除";
                    return Json(result);
                }
                
                result.Data = _app.DeleteByKey(ID, GetCurrentUser().LoginAccount);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 批量删除
        /// </summary>
        /// <param name="ids">记录ID数组</param>
        /// <returns></returns>
        [HttpDelete]
        public IHttpActionResult Delete([FromBody]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                // 检查是否有被报工站点引用的记录
                var referencedIds = _app.GetReferencedWorkCenterStations(ids);
                if (referencedIds.Count > 0)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "以下工作中心站点已被报工站点引用，无法删除";
                    result.Data = referencedIds;
                    return Json(result);
                }
                
                result.Data = _app.DeleteByKeys(ids, GetCurrentUser().LoginAccount);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 导出模板

        /// <summary>
        /// 导出模板
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelModel()
        {
            var result = new ResponseData();
            try
            {
                var columns = ExcelService.FetchDefaultColumnList<MD_WorkCenterStation>();
                string modelName = "工作中心站点-导入模板";
                //排除不需要的列名称 字符串
                string[] ignoreField = new string[]
                {
                    "Id", "IsDelete", "CUser", "CTime",
                    "MUser", "MTime", "DUser", "DTime"
                };
                var itemsData = new List<MD_WorkCenterStation>() { };
                var ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<MD_WorkCenterStation> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }

                return ExportToExcelFile<MD_WorkCenterStation>(itemsData, columns,
                    GetCurrentUser().UserName + "_" + modelName);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        /// <summary>
        /// 导出数据
        /// </summary>
        /// <param name="req">查询参数</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] MD_WorkCenterStationListReq req)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetList(x => (string.IsNullOrEmpty(req.WorkCenterName) 
                    || x.WorkCenterName.Contains(req.WorkCenterName))
                    && (string.IsNullOrEmpty(req.WorkCenterCode)
                    || x.WorkCenterCode.Contains(req.WorkCenterCode))
                    && (string.IsNullOrEmpty(req.StationCode)
                    || x.StationCode.Contains(req.StationCode))
                    && (string.IsNullOrEmpty(req.StationName)
                    || x.StationName.Contains(req.StationName))).ToList();
                List<ExcelColumn<MD_WorkCenterStation>> columns = ExcelService.FetchDefaultColumnList<MD_WorkCenterStation>();
                string[] ignoreField = new string[] { "IsDelete", "DTime", "DUser", "MUser", "MTime" };

                List<ExcelColumn<MD_WorkCenterStation>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<MD_WorkCenterStation> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }

                return ExportToExcelFile<MD_WorkCenterStation>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #region 导入

        /// <summary>
        /// 导入
        /// </summary>
        /// <param name="entitys">接收的导入集合</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult ImportExcelToData([FromBody]List<MD_WorkCenterStationImport> entitys)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                if (_app.ImportExcelToBaseData(entitys, GetCurrentUser().LoginAccount, out error_message))
                {
                    result.Data = true;
                    result.Code = (int)WMSStatusCode.Success;
                }
                else
                {
                    result.Data = false;
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
            return Json(result);
        }

        #endregion
    }
} 