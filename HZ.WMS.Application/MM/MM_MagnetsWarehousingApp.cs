using SqlSugar;
using HZ.Core.Http;
using HZ.WMS.Application.MD;
using HZ.WMS.Application.Sys;
using HZ.WMS.Entity;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.MM;
using HZ.WMS.Entity.SAP;
using HZ.WMS.Entity.SAP.View;
using HZ.WMS.Entity.Sys;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HZ.WMS.Application.MM
{
    /// <summary>
    /// 磁材委外入库方法层
    /// </summary>
    public class MM_MagnetsWarehousingApp : BaseApp<MM_MagnetsWarehousing>
    {
        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public MM_MagnetsWarehousingApp() : base()
        {}

        BaseApp<BaseEntity> _baseApp = new BaseApp<BaseEntity>();
        Sys_SwithConfigApp _switchApp = new Sys_SwithConfigApp();
        MD_StockApp _stockApp = new MD_StockApp();

        #endregion

        #region PC

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="opUser"></param>
        /// <param name="error_message"></param>
        public bool Deletes(string[] ids, string opUser, out string error_message)
        {
            bool bDeleted = true;
            List<MM_MagnetsWarehousing> entities = GetListByKeys(ids);
            if (CheckDelete(entities, out error_message))
            {
                try
                {
                    DbContext.Ado.BeginTran();

                    Delete(entities, opUser);

                    DbContext.Ado.CommitTran();//提交事务
                }
                catch (Exception ex)
                {
                    bDeleted = false;
                    error_message = ex.Message;

                    if(DbContext.Ado.IsAnyTran())
                        DbContext.Ado.RollbackTran();//失败回滚
                }
            }
            else
            {
                bDeleted = false;
            }
            return bDeleted;
        }

        /// <summary>
        /// 删除校验
        /// </summary>
        /// <param name="entities"></param>
        /// <param name="error_message"></param>
        /// <returns></returns>
        private bool CheckDelete(List<MM_MagnetsWarehousing> entities, out string error_message)
        {
            bool isPass = true;
            error_message = "";
            foreach (MM_MagnetsWarehousing ow in entities)
            {
                if (ow.IsPosted == true)
                {
                    error_message = "ui.Message.PODeletePostingWarning"; // "已过账数据不允许删除!";
                    isPass = false;
                    break;
                }

                //    // 入库校验
                //    //if(_stockApp.CheckStockLocked(po.ItemCode, po.OutBinLocationCode, po.OutRegionCode))
                //    //{
                //    //    //throw new Exception("Common.error", new Exception("Common.StockIsLocked"));
                //    //    error_message = "Common.StockIsLocked"; //库存锁定，不允许出入库
                //    //    return false;
                //    //}
            }

            return isPass;
        }


        #endregion

        #region 过账

        /// <summary>
        /// 
        /// </summary>
        /// <param name="entities"></param>
        /// <param name="error_message"></param>
        /// <returns></returns>
        public bool DoPost(List<MM_MagnetsWarehousing> entities, string opUser, out string error_message)
        {
            error_message = "过账失败";
            return false;

            //// 插入收货记录成功才能更新库存
            //string errorMsg = string.Empty;
            //MD_Stock stock = new MD_Stock();
            ////stock.BarCode = query.BarCode;
            ////stock.BatchNum = query.BatchNum;
            //stock.BinLocationCode = query.BinLocationCode;
            //stock.BinLocationName = query.BinLocationName;
            ////stock.BoqueryBarCode = query.BoxBarCode;
            //stock.ItemCode = query.ItemCode;
            //stock.ItemName = query.ItemName;
            //stock.ItmsGrpCode = query.ItmsGrpCode;
            //stock.ItmsGrpName = query.ItmsGrpName;
            ////stock.PTime = query.PTime;
            //stock.Qty = query.PurchaseReceiptQty;
            //stock.RegionCode = query.RegionCode;
            //stock.RegionName = query.RegionName;
            //stock.Unit = query.Unit;
            //stock.IsDelete = false;
            ////user = query.CUser;
            //if (!_stockApp.StockIn(stock, opUser, out errorMsg))
            //{
            //    throw new Exception("Common.error", new Exception(errorMsg));
            //}

        }


        #endregion

        #endregion

        #region Mobile

        #region 查询库存信息

        /// <summary>
        /// 校验
        /// 1、扫描的条码号无效 
        /// 2、
        /// </summary>
        /// <param name="BarCode">条码号</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool CheckDate(string BarCode, out string error_message)
        {
            error_message = "";
            var query = _stockApp.GetList(x => (string.IsNullOrEmpty(BarCode) || x.BarCode == BarCode)).ToList().FirstOrDefault();
            if (query == null)
            {
                error_message = "扫描的条码号无效，请检查"; // "扫描的条码号无效!";
                return false;
            }
            //MM_SubcontractingApplication subcontractingapplication = DbContext.Queryable<MM_SubcontractingApplication>()
            //       .Where(x => !x.IsDelete)   //排除已经逻辑删除的记录
            //       .Where(x => (string.IsNullOrEmpty(DocNum) || x.DocNum == DocNum)).ToList().FirstOrDefault();

            //if (subcontractingapplication == null)
            //{
            //    error_message = "扫描/录入的申请单号无效单据，请检查数据！"; // "扫描的报检单号无效!";
            //    return false;
            //}
            //校验扫描的单号在SRM库中是否为有效单据
            //string sql = @" SELECT *FROM XZSRM.dbo.P_InspectionDetail where InspectionNo=@InspectionNo and ItemCode=@ItemCode and IsDelete=0 ";
            //var Inspection = DbContext.SqlQuery<SRM_InspectionDetail>(sql, SugarParameter.Create("@InspectionNo", keyword),SugarParameter.Create("@ItemCode",ItemCode)).ToList().FirstOrDefault();
            //if (Inspection == null)
            //{
            //    //ui.Message.TheScannedInspectionOrderNumberInvalid
            //    error_message = "扫描/录入的报检单号/物料编号是无效单据，请检查数据！"; // "扫描的报检单号无效!";
            //    return false;
            //}

            //校验扫描的报检单号在WMS库中是否已存在记录
            //QM_PurchaseInspection PurchaseInspection = GetList(x => (string.IsNullOrEmpty(keyword) || x.InspectionNum == keyword) &&
            //                                                  string.IsNullOrEmpty(ItemCode) || x.ItemCode == ItemCode).ToList().FirstOrDefault();
            //if (PurchaseInspection != null)
            //{
            //    //ui.Message.TheScannedInspectionOrderNumberAlreadyExists
            //    error_message = "扫描/录入的报检单号/物料编号已存在记录，请检查数据！"; // "扫描的报检单号已存在!";
            //    return false;
            //}

            //string PurchaseInspectionSql = @" SELECT *FROM XZWMS.dbo.QM_PurchaseInspection where InspectionNum=@InspectionNum and ItemCode=@ItemCode and IsDelete=0 ";
            //var PurchaseInspection = DbContext.SqlQuery<QM_PurchaseInspection>(PurchaseInspectionSql, SugarParameter.Create("@InspectionNum", keyword),SugarParameter.Create("ItemCode",ItemCode)).ToList().FirstOrDefault();
            //if (PurchaseInspection != null)
            //{
            //    //ui.Message.TheScannedInspectionOrderNumberAlreadyExists
            //    error_message = "扫描/录入的报检单号/物料编号已存在记录，请检查数据！"; // "扫描的报检单号已存在!";
            //    return false;
            //}
            return true;

            //return error_message;
        }

        /// <summary>
        /// 查询库存信息
        /// </summary>
        /// <param name="BarCode">条码号</param>
        /// <returns></returns>
        public ISugarQueryable<MD_Stock> GetStockInfo(string BarCode)
        {
            var query = _stockApp.GetList(x => (string.IsNullOrEmpty(BarCode) || x.BarCode == BarCode));
            return query;
            //string sql = @"SELECT  
            //             A.DetailID,A.DocNum,A.BaseLine,A.ItemCode,A.ItemName,A.SupplierCode,A.SupplierName,
            //             CASE 
            //              WHEN b.OutsourcingDispatchQty<>0 THEN a.SubcontractingApplicationQty-b.OutsourcingDispatchQty
            //             ELSE a.SubcontractingApplicationQty END SubcontractingApplicationQty,
            //             A.Unit,A.WhsCode,A.WhsName,A.RegionCode,A.RegionName,A.BinLocationCode,A.BinLocationName,A.Remark,A.IsDelete,
            //             A.CUser,A.CTime,A.MUser,A.MTime,A.DUser,A.DTime,A.PurchaseOrder,A.SubcontractingApplicationNum 
            //               FROM dbo.MM_SubcontractingApplicationDetail A
            //               LEFT JOIN
            //               (
            //               SELECT 
            //                 SubcontractingApplicationDetailID,SubcontractingApplicationNum,
            //                 SUM(OutsourcingDispatchQty) OutsourcingDispatchQty
            //               FROM dbo.MM_OutsourcingDispatch WHERE IsDelete=0
            //               GROUP BY SubcontractingApplicationDetailID,SubcontractingApplicationNum
            //                )
            //                B ON b.SubcontractingApplicationNum=a.DocNum
            //                AND b.SubcontractingApplicationDetailID=a.DetailID
            //                WHERE a.IsDelete=0 AND A.DocNum=@DocNum ";
            //var query = DbContext.Queryable<MM_SubcontractingApplicationDetail>()
            //       .Where(x => !x.IsDelete && x.DocNum== DocNum )   //排除已经逻辑删除的记录
            //       //.Where(condition)
            //       .OrderBy("CTime desc");
            //return query;
            //string sql = @" SELECT *FROM XZWMS.dbo.QM_PurchaseInspection WHERE InspectionNum=@InspectionNum AND ItemCode=@ItemCode 
            //                AND IsDelete=0 AND InspectionItem !='2' ";

            //var list = DbContext.SqlQuery<MD_Stock>(sql, new SugarParameter("@DocNum", DocNum)).ToList();
            //return list;

            //var materialEntity = srm_InspectionApp.GetFirstEntityByFieldValue("ItemCode", keyword);
            //return materialEntity;

        }

        #endregion

        #region 保存

        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="entities">委外入库集合</param>
        /// <param name="ManualPostTime">手动过账时间</param>query.
        /// <param name="user">用户信息</param>
        /// <param name="error_message">错误消息返回</param>
        public bool Save(List<MM_MagnetsWarehousing> entities, DateTime ManualPostTime, Sys_User user, out string error_message)
        {
            error_message = "";
            try
            {
                string DocNum = _baseApp.GetNewDocNum(DocType.MM, DocFixedNumDef.MM_MagnetsWarehousing);

                //插入表信息 
                entities.ForEach(x=>
                {
                    x.ManualPostTime = ManualPostTime;
                    x.DocNum = DocNum;
                    x.IsDelete = false;
                    x.IsPosted = false;
                    x.CUser = user.LoginAccount;
                    x.CTime = DateTime.Now;
                });

                DbContext.Ado.BeginTran();

                //批量插入
                Insert(entities);

                DbContext.Ado.CommitTran();

                //是否自动过账判定
                if (_switchApp.IsMM_MagnetsWarehousingAutoPost)
                {
                    return DoPost(entities, user.LoginAccount, out error_message);
                }

                return true;
            }
            catch (Exception ex)
            {
                if(DbContext.Ado.IsAnyTran())
                    DbContext.Ado.RollbackTran();

                error_message = ex.Message;
                return false;
            }
        }

        #endregion

        #endregion






    }
}
