using System;
using System.ComponentModel;
using SqlSugar;

namespace HZ.WMS.Entity.Produce
{
    /// <summary>
    /// 生产报工
    /// </summary>
    public class Produce_Report : BaseEntity
    {
        /// <summary>
        /// 主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 生产报工单号
        /// </summary>
        [Description("生产报工单号")]
        public string ProduceReportNo { get; set; }

        /// <summary>
        /// 出厂编号
        /// </summary>
        [Description("出厂编号")]
        public string SerialNo { get; set; }

        /// <summary>
        /// 生产订单号
        /// </summary>
        [Description("生产订单号")]
        public string ProduceOrderNo { get; set; }

        /// <summary>
        /// 主机生产订单号
        /// </summary>
        [Description("主机生产订单号")]
        public string HostProduceOrderNo { get; set; }

        /// <summary>
        /// 生产调度员
        /// </summary>
        [Description("生产调度员")]
        public string ProduceScheduler { get; set; }

        /// <summary>
        /// 物料编码
        /// </summary>
        [Description("物料编码")]
        public string MaterialCode { get; set; }

        /// <summary>
        /// 物料描述
        /// </summary>
        [Description("物料描述")]
        public string MaterialName { get; set; }

        /// <summary>
        /// 订单类型
        /// </summary>
        [Description("订单类型")]
        public int? OrderType { get; set; }

        /// <summary>
        /// 订单数量
        /// </summary>
        [Description("订单数量")]
        public decimal? OrderQty { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [Description("单位")]
        public string Unit { get; set; }

        /// <summary>
        /// 收货库存地点
        /// </summary>
        [Description("收货库存地点")]
        public string ReceiveLocation { get; set; }

        /// <summary>
        /// 报工数量
        /// </summary>
        [Description("报工数量")]
        public decimal? ReportTotal { get; set; }

        /// <summary>
        /// 合格数量
        /// </summary>
        [Description("合格数量")]
        public decimal? QualifiedQty { get; set; }

        /// <summary>
        /// 不合格数量
        /// </summary>
        [Description("不合格数量")]
        public decimal? UnqualifiedQty { get; set; }

        /// <summary>
        /// 不合格备注
        /// </summary>
        [Description("不合格备注")]
        public string UnqualifiedRemarks { get; set; }

        /// <summary>
        /// 装配日期
        /// </summary>
        [Description("装配日期")]
        public DateTime? AssemblDate { get; set; }

        /// <summary>
        /// 发货单位
        /// </summary>
        [Description("发货单位")]
        public string CustomerName { get; set; }

        /// <summary>
        /// 物料组编码
        /// </summary>
        [Description("物料组编码")]
        public string MaterialGroupCode { get; set; }

        /// <summary>
        /// 物料组描述
        /// </summary>
        [Description("物料组描述")]
        public string MaterialGroupDes { get; set; }

        /// <summary>
        /// 是否完成
        /// </summary>
        [Description("是否完成")]
        public bool? IsCompleted { get; set; }

        /// <summary>
        /// 是否过账
        /// </summary>
        [Description("是否过账")]
        public bool? IsPosted { get; set; }

        /// <summary>
        /// 过账时间
        /// </summary>
        [Description("过账时间")]
        public DateTime? PostTime { get; set; }

        /// <summary>
        /// 过账人
        /// </summary>
        [Description("过账人")]
        public string PostUser { get; set; }

        /// <summary>
        /// 手动过账时间
        /// </summary>
        [Description("手动过账时间")]
        public DateTime? ManualPostTime { get; set; }

        /// <summary>
        /// SAP单号
        /// </summary>
        [Description("SAP单号")]
        public string SapNo { get; set; }

        /// <summary>
        /// SAP行号
        /// </summary>
        [Description("SAP行号")]
        public string SapLine { get; set; }

        /// <summary>
        /// 当前站点代码
        /// </summary>
        [Description("当前站点代码")]
        public string CurrentStationCode { get; set; }

        /// <summary>
        /// 工作中心名称
        /// </summary>
        [Description("工作中心名称")]
        public string WorkCenterName { get; set; }

        /// <summary>
        /// 工作中心编码
        /// </summary>
        [Description("工作中心编码")]
        public string WorkCenterCode { get; set; }

        /// <summary>
        /// CA状态
        /// </summary>
        [Description("CA状态")]
        public int? CaStatus { get; set; }

        /// <summary>
        /// CA流程编号
        /// </summary>
        [Description("CA流程编号")]
        public string CaSequenceNo { get; set; }

        /// <summary>
        /// 排产ID
        /// </summary>
        [Description("排产ID")]
        public string ProduceSchedulingId { get; set; }

        /// <summary>
        /// 过账消息
        /// </summary>
        [Description("过账消息")]
        public string PostMessage { get; set; }
    }
}