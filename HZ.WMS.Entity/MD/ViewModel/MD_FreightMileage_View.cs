using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;
using System.ComponentModel;

namespace HZ.WMS.Entity.MD.ViewModel
{
    /// <summary>
    /// 销售运费里程
    /// </summary>
    public class MD_FreightMileage_View
    {
        /// <summary>
        /// 运费结算地址
        /// </summary>
        [Description("运费结算地址")]
        public string SettlementAdd { get; set; }


        /// <summary>
        /// 供应商编号
        /// </summary>
        [Description("供应商编号")]
        public string SupplierCode { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        [Description("供应商")]
        public string SupplierName { get; set; }

        /// <summary>
        /// 结算里程
        /// </summary>
        [Description("结算里程")]
        public int? Mileage { get; set; }

        /// <summary>
        /// 是否直发
        /// </summary>
        [Description("是否直发")]
        public bool IsZF { get; set; }

        /// <summary>
        /// 发货人编号
        /// </summary>
        [Description("发货人编号")]
        public string UserCode { get; set; }


        /// <summary>
        /// 发货人名称
        /// </summary>
        [Description("发货人名称")]
        public string UserName { get; set; }


    }
}


