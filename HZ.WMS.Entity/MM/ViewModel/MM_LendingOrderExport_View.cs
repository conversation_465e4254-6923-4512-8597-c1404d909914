using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.MM.ViewModel
{
    /// <summary>
    /// 借出单导出视图
    /// </summary>
    public class MM_LendingOrderExport_View : MM_LendingOrderDetail
    {
        /// <summary>
        /// 主表备注
        /// </summary>
        [Description("备注")]
        public string ZRemark { get; set; }

        /// <summary>
        /// 借出单类型 0：手动创建 1：自动创建
        /// </summary>
        [Description("借出单类型")]
        public string LendingType { get; set; }

        /// <summary>
        /// 状态 0:未审核 1：审核中 2：已审核 3：已取消
        /// </summary>
        [Description("状态")]
        public string Status { get; set; }

        /// <summary>
        /// ManualPostTime
        /// </summary>
        [Description("手动过账时间")]
        public DateTime? ManualPostTime { get; set; }

        /// <summary>
        /// 交易对象名称
        /// </summary>
        [Description("交易对象名称")]
        public string TradingPartnersName { get; set; }

        /// <summary>
        /// 对象编号
        /// </summary>
        [Description("对象编号")]
        public string PartnersNum { get; set; }

        /// <summary>
        /// 对象全称
        /// </summary>
        [Description("对象全称")]
        public string PartnersName { get; set; }

        /// <summary>
        /// 办理人员编号
        /// </summary>
        [Description("办理人员编号")]
        public string HandlenCode { get; set; }

        /// <summary>
        /// 办理人员
        /// </summary>
        [Description("办理人员")]
        public string HandlenName { get; set; }

        /// <summary>
        /// 归还日期
        /// </summary>
        [Description("归还日期")]
        public DateTime? ReturnDate { get; set; }

        /// <summary> 
        /// "审核人
        /// </summary> 
        [Description("审核人")]
        public string AuditUser { get; set; }

        /// <summary> 
        /// 取消人
        /// </summary> 
        [Description("取消人")]
        public string RejectUser { get; set; }

    }
    
}
