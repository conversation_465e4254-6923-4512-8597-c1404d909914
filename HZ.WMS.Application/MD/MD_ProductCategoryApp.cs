using HZ.Core.Http;
using HZ.WMS.Entity.MD;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace HZ.WMS.Application.MD
{
    /// <summary>
    /// 没有out_message消息的函数，意味着只在最前端做未处理异常捕捉
    /// </summary>

    public class MD_ProductCategoryApp : BaseApp<MD_ProductCategory>
    {
        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public MD_ProductCategoryApp() : base()
        {
        }




        #endregion

        #region 获取列表

        /// <summary>
        /// 获取列表
        /// </summary>
        /// <returns></returns>
        public List<MD_ProductCategory> GetList()
        {
            return base.GetList().ToList();
        }

        #endregion

        #region 获取分页列表

        /// <summary>
        /// 分页查询
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyWard"></param>
        /// <returns></returns>
        public List<MD_ProductCategory> GetPageList(Pagination page)
        {
            // 无关键字查询所有数据
            var query = base.GetPageList(page);
            return query.ToList();
        }

        #endregion

        #region 添加

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public MD_ProductCategory Insert(MD_ProductCategory entity)
        {
            return base.Insert(entity);
        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public int Delete(MD_ProductCategory entity)
        {
            return base.Delete(entity);
        }

        #endregion

        #region 批量删除

        /// <summary>
        /// 批量删除
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public int DeleteByIDS(object[] ids, string deleteUser)
        {
            return base.DeleteByKeys(ids, deleteUser);
        }

        #endregion

        #region 更新

        /// <summary>
        /// 更新，只能修改备注，是否已读，已读时间，修改时间，修改人Id这几个字段
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public int Update(MD_ProductCategory entity)
        {
            return base.Update(entity);
        }

        #endregion

        /// <summary>
        /// 从接口查询产品类别信息
        /// </summary>
        /// <param name="productCategoryIDs"></param>
        /// <returns></returns>
        public List<MD_ProductCategory> GetListSAP(string[] productCategoryIDs = null)
        {
            List<MD_ProductCategory> result = new List<MD_ProductCategory>();

            //var rs = ProductCategoryService.GetProductCategoriesByID(productCategoryIDs);
            //if (rs == null)
            //{
            //    return null;
            //}
            //for (int i = 0; i < rs.Count; i++)
            //{
            //    var entity = new MD_ProductCategory();
            //    entity.ID = Guid.NewGuid().ToString();
            //    entity.ProductCategoryID = rs[i].InternalID;
            //    entity.ProductCategoryDesc = ((Dictionary<string, string>)rs[i].Description).ToList().FirstOrDefault().Value + "";
            //    result.Add(entity);
            //}
            return result;
        }


        /// <summary>
        /// 抓取数据到本地
        /// </summary>
        /// <param name="productCategoryIDs"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        public Task GrabData(string[] productCategoryIDs = null, string user = null)
        {
            return Task.Run(() =>
            {
                var list = this.GetListSAP(productCategoryIDs);
                if (list?.Count > 0)
                {
                    list.ForEach(x =>
                    {
                        x.ID = Guid.NewGuid().ToString();
                        x.CTime = DateTime.Now;
                        x.CUser = user;
                        x.IsDelete = false;
                    });
                    try
                    {
                        DbContext.UseTran(() =>
                        {
                            DbContext.Deleteable<MD_ProductCategory>(x => true).ExecuteCommand();
                            DbContext.Insertable(list).ExecuteCommand();
                        });
                    }
                    catch (System.Exception ex)
                    {
                        if (DbContext.Ado.IsAnyTran())
                        {
                            DbContext.Ado.RollbackTran();
                        }
                        throw ex;
                    }
                }
            });
        }
    }
}

