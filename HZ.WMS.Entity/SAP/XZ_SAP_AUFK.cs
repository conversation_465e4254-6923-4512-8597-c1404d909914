using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.SAP
{
    /// <summary>
    /// 内部订单
    /// </summary>
    public class XZ_SAP_AUFK
    {
        /// <summary>
        /// 订单号
        /// </summary>
        [Description("订单号")]
        public string AUFNR { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        [Description("描述")]
        public string KTEXT { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        [Description("状态")]
        public bool? Status { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [Description("创建时间")]
        public DateTime? CDate { get; set; }
    }
}
