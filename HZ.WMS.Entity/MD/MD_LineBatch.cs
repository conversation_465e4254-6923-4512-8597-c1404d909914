using SqlSugar;

namespace HZ.WMS.Entity.MD
{
    /// <summary>
    /// 物料主数据
    /// </summary>
    [SugarTable("MD_LineBatch")]
    public class MD_LineBatch : BaseEntity
    {
        
        /// <summary> 
        /// Id
        /// </summary> 
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 客户名称
        /// </summary>
        public string LineName { get; set; }

        /// <summary>
        /// 客户代码
        /// </summary>
        public int BatchNo { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public int? StartTime { get; set; }
        
    }
}