using SqlSugar;
using HZ.WMS.Entity.PP;
using HZ.WMS.Entity.PP.Dto;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Application.PP
{
    /// <summary>
    /// 装箱材料分拣
    /// </summary>
    public class PP_PackSortingApp : BaseApp<PP_PackSorting>
    {
        private PP_PackSortingDetailApp detailApp = new PP_PackSortingDetailApp();

        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public PP_PackSortingApp() : base()
        {
        }

        #endregion

        #region 增加

        public bool Add(PP_PackSorting_Dto entity, string user)
        {
            var mark = false;
            try
            {
                var pack = new PP_PackSorting
                {
                    ScanningCode = entity.ScanningCode,
                    SerialNo = entity.SerialNo,
                    PackSortingNo = entity.PackSortingNo,
                    ProductionOrderNo = entity.ProductionOrderNo,
                    FactoryCode = entity.FactoryCode,
                    MaterialNo = entity.MaterialNo,
                    MaterialName = entity.MaterialName,
                    OrderType = entity.OrderType,
                    OrderQty = entity.OrderQty,
                    Unit = entity.Unit,
                    ContractNo = entity.ContractNo,
                    SalesOrderNo = entity.SalesOrderNo,
                    Shippers = entity.Shippers,
                    ProductionLineCode = entity.ProductionLineCode,
                    ProductionLineDes = entity.ProductionLineDes,
                    ProductionScheduler = entity.ProductionScheduler,
                    DeliveryTime = entity.DeliveryTime,
                    StartTime = entity.StartTime,
                    Remark = entity.Remark,
                    CUser = user,
                    CTime = DateTime.Now
                };
                var detailList = entity.DetailList;
                detailList.ForEach(item =>
                {
                    item.PackSortingNo = entity.PackSortingNo;
                    item.CUser = user;
                });

                //this.DbContext.Ado.BeginTran();
                this.DbContext.Ado.BeginTran();
                base.Insert(pack);
                detailApp.Insert(detailList);
                this.DbContext.Ado.CommitTran();
                mark = true;
            }
            catch (Exception ex)
            {
                mark = false;
                this.DbContext.Ado.RollbackTran();
                throw ex;
            }
            return mark;
        }

        #endregion

        #region 编辑

        public bool Edit(PP_PackSorting_Dto entity, string user)
        {
            bool result = false;
            try
            {
                var pack = new PP_PackSorting
                {
                    ID = entity.ID,
                    ScanningCode = entity.ScanningCode,
                    SerialNo = entity.SerialNo,
                    PackSortingNo = entity.PackSortingNo,
                    ProductionOrderNo = entity.ProductionOrderNo,
                    FactoryCode = entity.FactoryCode,
                    MaterialNo = entity.MaterialNo,
                    MaterialName = entity.MaterialName,
                    OrderType = entity.OrderType,
                    OrderQty = entity.OrderQty,
                    Unit = entity.Unit,
                    ContractNo = entity.ContractNo,
                    SalesOrderNo = entity.SalesOrderNo,
                    Shippers = entity.Shippers,
                    ProductionLineCode = entity.ProductionLineCode,
                    ProductionLineDes = entity.ProductionLineDes,
                    ProductionScheduler = entity.ProductionScheduler,
                    DeliveryTime = entity.DeliveryTime,
                    StartTime = entity.StartTime,
                    Remark = entity.Remark,
                    CUser = entity.CUser,
                    CTime = entity.CTime,
                    MUser = user,
                };

                var addList = new List<PP_PackSortingDetail>();
                var updateList = new List<PP_PackSortingDetail>();
                foreach (var item in entity.DetailList)
                {
                    item.PackSortingNo = entity.PackSortingNo;
                    if (string.IsNullOrEmpty(item.ID))
                    {
                        item.CUser = user;
                        addList.Add(item);
                    }
                    else
                    {
                        item.MUser = user;
                        updateList.Add(item);
                    }
                }
                var deleteList = detailApp.GetListByKeys(entity.DelDetailIds);
                if (deleteList != null && deleteList.Count > 0)
                {
                    deleteList.ForEach(item =>
                    {
                        item.DUser = user;
                        item.DTime = DateTime.Now;
                        item.IsDelete = true;
                    });
                    updateList.AddRange(deleteList);
                }

                //this.DbContext.Ado.BeginTran();
                this.DbContext.Ado.BeginTran();
                base.Update(entity);
                detailApp.Update(updateList);
                detailApp.Insert(addList);
                this.DbContext.Ado.CommitTran();
                result = true;
            }
            catch (Exception ex)
            {
                result = false;
                this.DbContext.Ado.RollbackTran();
                throw ex;
            }
            return result;
        }

        #endregion
    }
}
