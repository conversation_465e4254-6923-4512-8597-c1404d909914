using SqlSugar;
using HZ.Core.Http;
using HZ.WMS.Application.MD;
using HZ.WMS.Application.SAP;
using HZ.WMS.Application.Sys;
using HZ.WMS.Entity;
using HZ.WMS.Entity.PP;
using HZ.WMS.Entity.PP.ViewModel;
using HZ.WMS.Entity.SD;
using HZ.WMS.Entity.Sys;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Linq.Expressions;
using System.Net;
using System.Net.Mail;
using System.Text;
using System.Threading.Tasks;
using HZ.Core;
using HZ.WMS.Application.SD;

namespace HZ.WMS.Application.PP
{
    /// <summary>
    /// 生产报工
    /// </summary>
    public class PP_ProductionReportApp : BaseApp<PP_ProductionReport>
    {
        private PP_ProductionOrderApp orderApp = new PP_ProductionOrderApp();
        private PP_ProductionFeedingApp _app = new PP_ProductionFeedingApp();
        private SD_ShippingPlanDetailApp _planDetailApp = new SD_ShippingPlanDetailApp();
        private SD_DeliveryScanApp _deliveryScanApp = new SD_DeliveryScanApp();
        private Sys_SAPCompanyInfoApp sapApp = new Sys_SAPCompanyInfoApp();
        private BaseApp<BaseEntity> _baseApp = new BaseApp<BaseEntity>();
        Sys_MailServerConfigApp _mailConfigApp = new Sys_MailServerConfigApp();
        Sys_DictionaryApp _dictionaryApp = new Sys_DictionaryApp();

        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public PP_ProductionReportApp() : base()
        {
        }

        #endregion

        #region 根据订单号查工序

        /// <summary>
        /// 根据订单号查工序
        /// </summary>
        /// <param name="orderNo"></param>
        /// <returns></returns>
        public DataTable GetWorkingProcedure(string orderNo)
        {
            return DbContext.Ado.GetDataTable($"select distinct VORNR WorkingProcedureCode,LTXA1 WorkingProcedureDes from XZ_SAP.dbo.XZ_SAP_AFVC where AUFNR = '{orderNo}' and Status = 0 order by VORNR");
        }

        #endregion

        #region 根据工作中心获取工序

        /// <summary>
        /// 根据工作中心获取工序
        /// </summary>
        /// <param name="LineNo"></param>
        /// <returns></returns>
        public DataTable GetWorkingProcedureByLine(string LineNo)
        {
            return DbContext.Ado.GetDataTable($"select distinct VORNR WorkingProcedureCode,LTXA1 WorkingProcedureDes from XZ_SAP.dbo.XZ_SAP_AFVC where ARBPL = '{LineNo}' and Status = 0 order by VORNR");
        }

        #endregion

        #region 获取所有未启用序列号的生产订单

        public IEnumerable<PP_ProductionOrder> GetOrderNoSerialNo()
        {
            var list = DbContext.Ado.SqlQuery<PP_ProductionOrder>(@"select distinct 
                                                                '' as SerialNo,
                                                                a.FEVOR as ProductionScheduler,
                                                                a.AUFNR1 as HostProductionOrderNo,
                                                                a.AUFNR as ProductionOrderNo,
                                                                a.WERKS as FactoryCode,
                                                                e.ARBPL as ProductionLineCode,
                                                                (select right (i.KTEXT,CHARINDEX('-',REVERSE(i.KTEXT)) - 1)) as ProductionLineDes,--装配线
                                                                a.MATNR as MaterialNo,
                                                                case when a.DAUAT = 'ZP04' then a.KTEXT else c.MAKTX end as MaterialName,
                                                                a.DAUAT as OrderType,
                                                                a.GAMNG as OrderQty,
                                                                a.GMEIN as Unit,
                                                                a.SEQNR as ProductionBatch,
                                                                a.LGORT as ReceivingLocation,
                                                                a.TXT04 as OrderStatus,
                                                                a.KDAUF as SalesOrderNo,
                                                                a.KDPOS as SalesOrderLineNo,
                                                                a.GSTRP as StartTime,
                                                                a.GLTRP as EndTime,
                                                                f.KUNNR as Customer,
																h.NAME1 as Shippers,
                                                                a.BWTAR as AssessmentType,
                                                                a.Status as IsDelete
                                                                from XZ_SAP.dbo.XZ_SAP_AFKO a
                                                                left join XZ_SAP.dbo.XZ_SAP_MARC c on c.WERKS = a.WERKS and c.MATNR = a.MATNR and c.Status = 0 and c.LVORM = '' and c.LVORA = ''
                                                                left join XZ_WMS.dbo.MD_Item d on d.ItemCode = a.MATNR and d.IsDelete = 0
                                                                left join XZ_SAP.dbo.XZ_SAP_AFVC e on e.AUFNR = a.AUFNR and e.VORNR = (select min(VORNR) VORNR from XZ_SAP.dbo.XZ_SAP_AFVC where AUFNR = a.AUFNR) and e.Status = 0 
                                                                left join XZ_SAP.dbo.XZ_SAP_VBAK f on f.Status = 0 and f.VBELN = a.KDAUF
                                                                left join XZ_SAP.dbo.XZ_SAP_KNA1 h on h.KUNNR = f.KUNNR and h.Status = 0-- and h.WAERS = a.WERKS
                                                                --SAP工作中心
                                                                left join XZ_SAP.dbo.XZ_SAP_CRHD i on i.WERKS = a.WERKS and i.ARBPL = e.ARBPL
																where a.Status = 0 and a.XLOEK <> 'X' and (d.StockManWay != 2 or d.StockManWay is null) ");

            if (list == null || list.Count() < 1)
            {
                return list;
            }
            //获取有效工单
            var statusList = new Sys_SAPCompanyInfoApp().GetSapValidOrder("001", list.Select(s => s.ProductionOrderNo).ToList());
            return list.Where(w => statusList.Contains(w.ProductionOrderNo));
        }

        #endregion

        #region 获取所有未启用序列号的生产订单根据单号

        public IEnumerable<PP_ProductionOrder> GetOrderNoSerialNoByNo(string ProductionOrderNo)
        {
            List<string> lists = new List<string>();
            lists.Add(ProductionOrderNo);
            //获取有效工单
            var statusList = new Sys_SAPCompanyInfoApp().GetSapValidOrder("001", lists);
            if (statusList == null || statusList.Count() == 0 )
            {
                return null;
            }

                    var list = DbContext.Ado.SqlQuery<PP_ProductionOrder>(@"select distinct 
                                                                '' as SerialNo,
                                                                a.FEVOR as ProductionScheduler,
                                                                a.AUFNR1 as HostProductionOrderNo,
                                                                a.AUFNR as ProductionOrderNo,
                                                                a.WERKS as FactoryCode,
                                                                e.ARBPL as ProductionLineCode,
                                                                (select right (i.KTEXT,CHARINDEX('-',REVERSE(i.KTEXT)) - 1)) as ProductionLineDes,--装配线
                                                                a.MATNR as MaterialNo,
                                                                case when a.DAUAT = 'ZP04' then a.KTEXT else c.MAKTX end as MaterialName,
                                                                a.DAUAT as OrderType,
                                                                a.GAMNG as OrderQty,
                                                                a.GMEIN as Unit,
                                                                a.SEQNR as ProductionBatch,
                                                                a.LGORT as ReceivingLocation,
                                                                a.TXT04 as OrderStatus,
                                                                a.KDAUF as SalesOrderNo,
                                                                a.KDPOS as SalesOrderLineNo,
                                                                a.KDPOS as SalesOrderLineNo,
                                                                a.GSTRP as StartTime,
                                                                a.GLTRP as EndTime,
                                                                f.KUNNR as Customer,
																h.NAME1 as Shippers,
                                                                a.BWTAR as AssessmentType,
                                                                a.Status as IsDelete
                                                                from XZ_SAP.dbo.XZ_SAP_AFKO a
                                                                left join XZ_SAP.dbo.XZ_SAP_MARC c on c.WERKS = a.WERKS and c.MATNR = a.MATNR and c.Status = 0 and c.LVORM = '' and c.LVORA = ''
                                                                left join XZ_WMS.dbo.MD_Item d on d.ItemCode = a.MATNR and d.IsDelete = 0
                                                                left join XZ_SAP.dbo.XZ_SAP_AFVC e on e.AUFNR = a.AUFNR and e.VORNR = (select min(VORNR) VORNR from XZ_SAP.dbo.XZ_SAP_AFVC where AUFNR = a.AUFNR) and e.Status = 0 
                                                                left join XZ_SAP.dbo.XZ_SAP_VBAK f on f.Status = 0 and f.VBELN = a.KDAUF
                                                                left join XZ_SAP.dbo.XZ_SAP_KNA1 h on h.KUNNR = f.KUNNR and h.Status = 0-- and h.WAERS = a.WERKS
                                                                --SAP工作中心
                                                                left join XZ_SAP.dbo.XZ_SAP_CRHD i on i.WERKS = a.WERKS and i.ARBPL = e.ARBPL
																where a.Status = 0 and a.XLOEK <> 'X' and isnull(d.StockManWay,0) != 2 and a.AUFNR='" + ProductionOrderNo + "'");

            if (list == null || list.Count() < 1)
            {
                return list;
            }
            //获取有效工单
            //var statusList = new Sys_SAPCompanyInfoApp().GetSapValidOrder("001", list.Select(s => s.ProductionOrderNo).ToList());
            //return list.Where(w => statusList.Contains(w.ProductionOrderNo));
            return list;
        }

        #endregion

        #region 判断延时/同步报工

        public ISugarQueryable<PP_ReportMark_View> GetReportView(Expression<Func<PP_ReportMark_View, bool>> condition = null)
        {
            return DbContext.Queryable<PP_ReportMark_View>().Where(condition);
        }

        #endregion

        #region 获取延时报工

        public ISugarQueryable<PP_DelayReport_View> GetDelayView(Expression<Func<PP_DelayReport_View, bool>> condition = null)
        {
            return DbContext.Queryable<PP_DelayReport_View>().Where(condition);
        }

        #endregion

        #region 倒冲物料

        public bool DoBackflush(string orderNo,List<PP_ProductionOrderDetail_View> entities, string operater, out string error_message)
        {
            error_message = "";
            if (entities == null || entities.Count < 1) return true;

            //投料逻辑
            var order = orderApp.GetSapOrderList(w => w.ProductionOrderNo == orderNo).ToList().FirstOrDefault();
            var docNum = _baseApp.GetNewDocNum(DocType.PP, DocFixedNumDef.PP_ProductionFeeding);
            var entity =
                new PP_ProductionFeeding
                {
                    SerialNo = order.SerialNo,
                    ProductionFeedingNo = docNum,
                    ProductionOrderNo = order.ProductionOrderNo,
                    FactoryCode = order.FactoryCode,
                    MaterialNo = order.MaterialNo,
                    MaterialName = order.MaterialName,
                    MovementType = "261",
                    OrderType = order.OrderType,
                    OrderQty = order.OrderQty,
                    Unit = order.Unit,
                    StartTime = order.StartTime,
                    ContractNo = order.ContractNo,
                    SalesOrderNo = order.SalesOrderNo,
                    Shippers = order.Shippers,
                    ProductionLineCode = order.ProductionLineCode,
                    ProductionLineDes = order.ProductionLineDes,
                    DeliveryTime = order.DeliveryTime,
                    IsPosted = true,
                    PostUser = operater,
                    PostTime = DateTime.Now,
                    ManualPostTime = DateTime.Now,
                    Remark = "倒冲物料，系统分配",
                    CUser = operater,
                };

            var detailList = new List<PP_ProductionFeedingDetail>();
            entities.ForEach(view =>
            {
                detailList.Add(
                    new PP_ProductionFeedingDetail
                    {
                        ProductionFeedingNo = docNum,
                        ProductionOrderNo = view.ProductionOrderNo,
                        ReservedNo = view.ReservedNo == null ? "" : view.ReservedNo.ToString(),
                        ComponentLineNo = view.ComponentLineNo,
                        ComponentCode = view.ComponentCode,
                        MaterialName = view.MaterialName,
                        DemandQty = view.DemandQty,
                        ComponentUnit = view.ComponentUnit,
                        MovementType = "261",
                        FactoryCode = order.FactoryCode,
                        DeliverLocation = view.DeliverLocation,
                        SalesOrderNo = view.SalesOrderNo,
                        SalesOrderLineNo = view.SalesOrderLineNo,
                        AssessmentCategory = "",
                        AssessmentType = "",
                        SpecialInventory = view.SpecialInventory,
                        IsBackflush = view.IsBackflush,
                        IsPosted = true,
                        PostUser = operater,
                        PostTime = DateTime.Now,
                        ManualPostTime = DateTime.Now,
                        Remark = "倒冲物料，系统分配",
                        CUser = operater,
                    });
            });

            if (!_app.Add(entity, detailList))
            {
                return false;
            }

            var updateList = new List<PP_ProductionFeedingDetail>();
            //扣库存
            //foreach (var item in detailList)
            //{
            //    var mark = true;
            //    //获取库位
            //    var location = new MD_BinLocationApp().GetList(w => w.WhsCode == item.DeliverLocation).ToList().FirstOrDefault();
            //    //判断是不是副产品
            //    if(item.DemandQty < 0)
            //    {
            //        //调接口加库存
            //        mark = new MD_StockApp().StockIn(
            //            new Entity.MD.MD_Stock
            //            {
            //                BarCode = "",
            //                BatchNum = "",
            //                //BatchNum = order.ProductionBatch == null ? "" : order.ProductionBatch.ToString(),
            //                ItemCode = item.ComponentCode,
            //                ItemName = item.MaterialName,
            //                Qty = -item.DemandQty,
            //                Unit = item.ComponentUnit,
            //                AssessType = item.AssessmentType,
            //                RegionCode = location?.RegionCode,
            //                RegionName = location?.RegionName,
            //                BinLocationCode = location?.BinLocationCode,
            //                BinLocationName = location?.BinLocationName,
            //                WhsCode = location?.WhsCode,
            //                WhsName = location?.WhsName,
            //                SaleNum = item.SalesOrderNo ?? "",
            //                SaleLine = Decimal.ToInt32(item.SalesOrderLineNo ?? 0),
            //            }, operater, out error_message);
            //        //if (!flg) return flg;
            //    }
            //    else
            //    {
            //        //调接口减本地库存
            //        if (!string.IsNullOrEmpty(item.SalesOrderNo.Trim()))
            //        {
            //            mark = new MD_StockApp().StockOut(item.ComponentCode, location?.BinLocationCode, item.SalesOrderNo, Decimal.ToInt32(item.SalesOrderLineNo ?? 0), item.DemandQty, operater, out error_message);
            //        }
            //        else
            //        {
            //            mark = new MD_StockApp().StockOut(item.ComponentCode, location?.BinLocationCode, item.DemandQty, operater, out error_message);
            //        }
            //    }
            //    if (!mark)
            //    {
            //        item.Remark = $"倒冲物料，系统分配；过账失败：{error_message}";
            //        item.IsPosted = false;
            //        item.MUser = operater;
            //        updateList.Add(item);
            //    }
            //}
            if (updateList.Count > 0)
            {
                new PP_ProductionFeedingDetailApp().UpdateWithTran(updateList);
            }

            return true;
        }

        #endregion

        #region 生产报工过账

        public bool RealPost(PP_ProductionReport entity, string operater, out string error_message)
        {
            string ACTID = "I";
            var isPosted = false;
            DateTime date = DateTime.Now;
            //查询Sap账期时间
            DateTime ManualPostTime = new SAPApp().GetSAPpostTime(Convert.ToDateTime(entity.ManualPostTime ?? date));
            var result = sapApp.ZFGWMS017("001", ACTID, entity.ProductionOrderNo,
                entity.ProductionReportNo + entity.ProductionOrderNo + ACTID, entity.QualifiedQty,
                entity.UnqualifiedQty, ManualPostTime, out isPosted, out error_message);

            if (result != null && result.Count > 0)
            {
                entity.IsPosted = true;
                entity.PostUser = operater;
                entity.PostTime = date;
                entity.MUser = operater;
                entity.MTime = date;
                entity.ManualPostTime = ManualPostTime;
                entity.SapDocNum = result.ToList().FirstOrDefault().sapDocNum;

                if (string.IsNullOrEmpty(entity.ID))
                {
                    Insert(entity);
                }
                else
                {
                    Update(entity);
                }

                isPosted = true;

                //WMS加库存
                //var order = orderApp.GetSapOrderList(w => w.ProductionOrderNo == entity.ProductionOrderNo).ToList().FirstOrDefault();
                //if(order != null)
                //{
                //    var _location = new MD_BinLocationApp().GetFirstEntity(w => w.WhsCode == order.ReceivingLocation);
                //    var flg = new MD_StockApp().StockIn(
                //        new Entity.MD.MD_Stock
                //        {
                //            BarCode = entity.SerialNo ?? "",
                //            BatchNum = "",
                //            //BatchNum = order.ProductionBatch == null ? "" : order.ProductionBatch.ToString(),
                //            ItemCode = entity.MaterialNo,
                //            ItemName = entity.MaterialName,
                //            Qty = entity.QualifiedQty,
                //            Unit = entity.Unit,
                //            AssessType = entity.AssessmentType,
                //            RegionCode = _location?.RegionCode,
                //            RegionName = _location?.RegionName,
                //            BinLocationCode = _location?.BinLocationCode,
                //            BinLocationName = _location?.BinLocationName,
                //            WhsCode = _location?.WhsCode,
                //            WhsName = _location?.WhsName,
                //            SaleNum = order.SalesOrderNo ?? "",
                //            SaleLine = Decimal.ToInt32(order.SalesOrderLineNo ?? 0),
                //        }, operater, out error_message);
                //    //if (!flg) return flg;
                //    error_message = "过账成功";//不管WMS库存是否报错
                //}
                
                error_message = "过账成功"; //不管WMS库存是否报错
            }
            else
            {
                error_message = "单号：" + entity.ProductionReportNo + "，过账失败，失败原因：" + error_message;
                return isPosted;
            }

            return isPosted;
        }

        public bool DoPost(List<PP_ProductionReport> entitie, string operater, out string error_message)
        {
            error_message = "";
            try
            {
                var IDs = entitie.Select(x => x.ID).Distinct().ToArray();

                List<PP_ProductionReport> entities = GetList(x => IDs.Contains(x.ID) && x.IsPosted == false).ToList();

                //不能延时报工数据
                var noDelayList = new List<PP_ProductionReport>();
                //不能倒冲的数据
                var noBackflushList = new List<PP_ProductionReport>();
                var backflushList = new List<PP_ProductionOrderDetail_View>();

                //获取本地所有订单
                var nos = entities.Select(s => s.ProductionOrderNo);
                //获取有效工单
                var statusList = new Sys_SAPCompanyInfoApp().ZFGWMS019("001", nos.ToList());
                var noStatus = statusList.Where(w => w.Value == "1")?.Select(s => s.Key);
                if (noStatus.Count() > 0)
                {
                    string strStatus = string.Join(",", noStatus);
                    error_message = strStatus + "无效工单不允许过账";
                    return false;
                }

                foreach (var entity in entities)
                {
                    //同步报工
                    if ((entity.OrderType == "ZP01" || entity.OrderType == "ZP02") &&
                        entity.ProductionScheduler == "101")
                    {
                        var report = this.GetReportView(w => w.ProductionOrderNo == entity.ProductionOrderNo && w.Mark.ToUpper() == "X").ToList().FirstOrDefault();
                        if(report != null)
                        {
                            //延时报工
                            var delayList = this.GetDelayView(w => w.HostProductionOrderNo == entity.ProductionOrderNo)
                                .ToList();
                            foreach (var delay in delayList)
                            {
                                PP_ProductionReport order = this.GetFirstEntity(w =>
                                    w.SerialNo == delay.SerialNo && w.ProductionOrderNo == delay.ProductionOrderNo &&
                                    w.IsCompleted == true);
                                if (order == null)
                                {
                                    //子表需求数量
                                    var children = orderApp.GetSapOrderDetailList(
                                        w => w.ProductionOrderNo == delay.HostProductionOrderNo 
                                        && w.ComponentCode == delay.MaterialNo
                                        && (string.IsNullOrEmpty(w.IsVirtual) || string.IsNullOrEmpty(w.IsVirtual.Trim()))
                                        && (string.IsNullOrEmpty(w.IsDeleteSAP) || string.IsNullOrEmpty(w.IsDeleteSAP.Trim()))).ToList().FirstOrDefault();
                                    if (children == null)
                                    {
                                        error_message =
                                            $"订单{entity.ProductionOrderNo}同步报工失败！在SAP生产订单物料表中间库查不到其组件{delay.ProductionOrderNo}（{delay.MaterialNo}）";
                                        return false;
                                    }

                                    //主表订单数量
                                    var parent = orderApp.GetSapOrderList(w => w.ProductionOrderNo == delay.HostProductionOrderNo).ToList().FirstOrDefault();
                                    if (parent == null)
                                    {
                                        error_message =
                                            $"订单{entity.ProductionOrderNo}（{entity.MaterialNo}）同步报工失败！在SAP生产订单表中间库查不到此订单";
                                        return false;
                                    }

                                    order = new PP_ProductionReport
                                    {
                                        ProductionReportNo = _baseApp.GetNewDocNum(DocType.PP, DocFixedNumDef.PP_ProductionReport),
                                        ScanningCode = "",
                                        SerialNo = delay.SerialNo,
                                        ProductionOrderNo = delay.ProductionOrderNo,
                                        HostProductionOrderNo = delay.HostProductionOrderNo,
                                        ProductionScheduler = delay.ProductionScheduler,
                                        OrderType = delay.OrderType,
                                        MaterialNo = delay.MaterialNo,
                                        MaterialName = delay.MaterialName,
                                        WorkingProcedureCode = delay.WorkingProcedureCode.ToString(),
                                        WorkingProcedureDes = delay.WorkingProcedureDes,
                                        EmployeeNumber = operater,
                                        EmployeeName = operater,
                                        Shippers = delay.Shippers,
                                        OrderQty = delay.OrderQty,
                                        Unit = delay.Unit,
                                        ReceivingLocation = delay.ReceivingLocation,
                                        ReportTotal =
                                            children.DemandQty / parent.OrderQty *
                                            entity.QualifiedQty, //=>子表需求数量/主表订单数量*实际报工数量
                                        QualifiedQty =
                                            children.DemandQty / parent.OrderQty *
                                            entity.QualifiedQty, //=>子表需求数量/主表订单数量*实际报工数量
                                        UnqualifiedQty = 0,
                                        UnqualifiedRemarks = "",
                                        AssessmentType = delay.AssessmentType,
                                        StartTime = delay.StartTime,
                                        ProductionLineCode = delay.ProductionLineCode,
                                        ProductionLineDes = delay.ProductionLineDes,
                                        MaterialGroupCode = delay.MaterialGroupCode,
                                        MaterialGroupDes = delay.MaterialGroupDes,
                                        ReportType = 0,
                                        IsCompleted = true,
                                        CUser = operater,
                                    };
                                }

                                if (order != null && order.IsPosted == true) continue;
                                //过账
                                if (!this.RealPost(order, operater, out error_message))
                                {
                                    if (error_message.Contains("大于允许报工的数量") || error_message.Contains("累加报工数量大于"))
                                        continue;

                                    error_message = $"订单：{delay.ProductionOrderNo}延时报工失败！{error_message}";
                                    return false;
                                }

                                //倒冲物料
                                var details = orderApp.GetSapOrderDetailList(
                                    w => w.ProductionOrderNo == delay.ProductionOrderNo
                                         && w.IsBackflush.ToUpper() == "X"
                                         && (string.IsNullOrEmpty(w.IsVirtual) ||
                                             string.IsNullOrEmpty(w.IsVirtual.Trim()))
                                         && (string.IsNullOrEmpty(w.IsDeleteSAP) ||
                                             string.IsNullOrEmpty(w.IsDeleteSAP.Trim()))). ToList();
                                details.ForEach(de =>
                                {
                                    de.DemandQty =
                                        de.DemandQty / order.OrderQty * order.QualifiedQty; //子表需求数量/主表订单数量*实际报工数量
                                    if (de.MovementType == "531") de.DemandQty = -de.DemandQty; //副产品
                                });
                                //倒冲
                                if (!DoBackflush(delay.ProductionOrderNo, details, operater, out error_message))
                                {
                                    //返回失败物料
                                    error_message = $"订单：{delay.ProductionOrderNo}的物料倒冲失败，失败原因：{error_message}";
                                    return false;
                                }
                            }
                        }

                        //过账
                        if (!this.RealPost(entity, operater, out error_message))
                        {
                            error_message = $"订单：{entity.ProductionOrderNo}同步报工失败！{error_message}";
                            return false;
                        }
                    }
                    else if ((entity.OrderType == "ZP01" || entity.OrderType == "ZP02") &&
                             (entity.ProductionScheduler == "102" || entity.ProductionScheduler == "103"))
                    {
                        //延时报工
                        var view = this.GetReportView(w => w.ProductionOrderNo == entity.HostProductionOrderNo && w.Mark.ToUpper() == "X").ToList().FirstOrDefault();
                        if (view != null)
                        {
                            if (string.IsNullOrEmpty(error_message))
                                error_message = $"工单{entity.ProductionOrderNo}是延时报工，不能单独过账";
                            continue;
                        }
                        else
                        {
                            //过账
                            if (!this.RealPost(entity, operater, out error_message))
                            {
                                error_message = $"订单：{entity.ProductionOrderNo}报工失败！{error_message}";
                                return false;
                            }

                            //倒冲物料
                            var details = orderApp.GetSapOrderDetailList(
                                w => w.ProductionOrderNo == entity.ProductionOrderNo
                                     && w.IsBackflush.ToUpper() == "X"
                                     && (string.IsNullOrEmpty(w.IsVirtual) || string.IsNullOrEmpty(w.IsVirtual.Trim()))
                                     && (string.IsNullOrEmpty(w.IsDeleteSAP) ||
                                         string.IsNullOrEmpty(w.IsDeleteSAP.Trim()))).ToList();
                            details.ForEach(item =>
                            {
                                item.DemandQty =
                                    item.DemandQty / entity.OrderQty * entity.QualifiedQty; //子表需求数量/主表订单数量*实际报工数量
                                if (item.MovementType == "531") item.DemandQty = -item.DemandQty; //副产品
                            });
                        }
                    }
                    else
                    {
                        //过账
                        if (!this.RealPost(entity, operater, out error_message))
                        {
                            error_message = $"订单：{entity.ProductionOrderNo}报工失败！{error_message}";
                            return false;
                        }
                    }

                    //判断自己是不是倒冲物料
                    var bush = orderApp.GetSapOrderDetailList(
                            w => w.ProductionOrderNo == entity.HostProductionOrderNo
                            && w.ComponentCode == entity.MaterialNo
                            && w.IsBackflush.ToUpper() == "X"
                            && (string.IsNullOrEmpty(w.IsVirtual) || string.IsNullOrEmpty(w.IsVirtual.Trim()))
                            && (string.IsNullOrEmpty(w.IsDeleteSAP) || string.IsNullOrEmpty(w.IsDeleteSAP.Trim()))).ToList().FirstOrDefault();
                    if (bush != null)
                    {
                        if (nos.Contains(bush.ProductionOrderNo))
                        {
                            bush.DemandQty =
                                bush.DemandQty / entity.OrderQty * entity.QualifiedQty; //子表需求数量/主表订单数量*实际报工数量
                            if (bush.MovementType == "531") bush.DemandQty = -bush.DemandQty; //副产品
                            backflushList.Add(bush);
                        }
                        else
                        {
                            noBackflushList.Add(entity);
                        }
                    }

                    //查询物料明细里的倒冲物料
                    var _bushList = orderApp.GetSapOrderDetailList(
                            w => w.ProductionOrderNo == entity.ProductionOrderNo
                                 && w.IsBackflush.ToUpper() == "X"
                                 && (string.IsNullOrEmpty(w.IsVirtual) || string.IsNullOrEmpty(w.IsVirtual.Trim()))
                                 && (string.IsNullOrEmpty(w.IsDeleteSAP) || string.IsNullOrEmpty(w.IsDeleteSAP.Trim())))
                        .ToList();
                    if (_bushList != null && _bushList.Count > 0)
                    {
                        _bushList.ForEach(item =>
                        {
                            item.DemandQty =
                                item.DemandQty / entity.OrderQty * entity.QualifiedQty; //子表需求数量/主表订单数量*实际报工数量
                            if (item.MovementType == "531") item.DemandQty = -item.DemandQty; //副产品
                        });
                        backflushList.AddRange(_bushList);
                    }
                }

                //倒冲
                var group = backflushList.GroupBy(g => g.ProductionOrderNo);
                foreach (var item in group)
                {
                    if (!DoBackflush(item.Key, item.ToList(), operater, out error_message))
                    {
                        //返回失败物料
                        error_message = $"订单：{item.Key}的物料倒冲失败，失败原因：{error_message}";
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                error_message = ex.InnerException?.Message ?? ex.Message;
                return false;
            }

            return true;
        }

        #endregion


        /// <summary>
        /// 查询
        /// </summary>
        /// <param name="strCodes"></param>
        /// <returns></returns>
        public List<PP_ProductionReportExport_View> GetProductionReportExport(string strCodes)
        {

            var itemsData = DbContext.Queryable<PP_ProductionReportExport_View>().Where(x => strCodes.Contains(x.ID)).ToList();

            return itemsData;
        }

        #region 取消过账状态

        /// <summary>
        /// 取消过账状态
        /// </summary>
        /// <param name="DocNums">单号数组</param>
        /// <param name="opUser">当前用户</param>
        /// <param name="error_message">错误消息</param>
        /// <param name="type">类型 1:提交失败 2：过账失败</param>
        public bool Audits(string[] IDs, string opUser)
        {
            bool bDeleted = true;
            var entities = GetListByKeys(IDs);
            DateTime dt = DateTime.Now;
            try
            {
                entities.ForEach(x =>
                {
                    x.IsPosted = false;
                    x.PostTime = null;
                    x.PostUser = "";
                    x.SapDocNum = "";
                    x.Remark += opUser + "取消过账状态" + dt.ToString();
                });
                DbContext.Ado.BeginTran();//开启事务

                Update(entities);

                DbContext.Ado.CommitTran();//提交事务

            }
            catch (Exception ex)
            {
                bDeleted = false;
                if (DbContext.Ado.IsAnyTran())
                    DbContext.Ado.RollbackTran();//失败回滚
            }

            return bDeleted;
        }

        #endregion

        #region 导入

        /// <summary>
        /// 导入
        /// </summary>
        /// <param name="excelList">接收的集合</param>
        /// <param name="opUser">当前用户</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool ImprotExcelToBaseData(List<SD_DeliveryScanImport> excelList, string opUser,
            out string error_message)
        {
            error_message = "";

            var flag = true;
            DateTime date = DateTime.Now;
            Sys_DictionaryApp DictionaryApp = new Sys_DictionaryApp();
            var querydetail = new List<Sys_Dictionary>();
            int i = 0;
            try
            {
                //暂时只进行插入操作，有需要在做更新操作
                foreach (var x in excelList)
                {
                    ////校验是否存在 客户编号+客户件号
                    //var isHaveData = _detailApp.GetFirstEntity(v => v.CustomerCode == item.CustomerCode &&
                    //                                          v.CustomerItemCode == item.CustomerItemCode);
                    i += 1;
                    var z = new Sys_Dictionary();
                    z.TypeCode = "PP999";
                    z.EnumKey = i;
                    z.EnumValue = x.销售订单号;
                    z.EnumValue1 = x.销售单行号.ToString();

                    z.CTime = date;
                    querydetail.Add(z);
                }

                ;

                DictionaryApp.Insert(querydetail);
                //更新
                base.SqlQuery("PROC_UpdateProductionReport", CommandType.StoredProcedure);
            }
            catch (Exception ex)
            {
                if (this.DbContext.Ado.IsAnyTran())
                    this.DbContext.Ado.RollbackTran();
                throw ex;
            }

            return flag;
        }

        #endregion

        public void SetShippingToDelivery(DateTime[] dateValue)
        {
            DateTime now = DateTime.Now;
            DateTime startTime = new DateTime(dateValue[0].Year, dateValue[0].Month, dateValue[0].Day,  0, 0, 0);
            DateTime endTime = new DateTime(dateValue[1].Year, dateValue[1].Month, dateValue[1].Day, 23, 59, 59);
            var list = GetList(t => t.PostTime >= startTime && t.PostTime <= endTime && t.IsDelete == false && t.ProductionScheduler == "101").ToList();
            var productionOrderNos = list.Select(t => t.ProductionOrderNo).ToList();
            var productionReportMap = list.ToDictionary(t => t.ProductionOrderNo, t => t);
            const int batchSize = 500;
            var productionOrderList = new List<PP_ProductionOrder>();
            // 分块处理
            for (int i = 0; i < productionOrderNos.Count; i += batchSize)
            {
                // 获取当前批次
                var currentBatch = productionOrderNos
                    .Skip(i)
                    .Take(batchSize)
                    .ToList();
                // 查询当前批次并合并结果
                var batchResult = orderApp.GetList(t => 
                    currentBatch.Contains(t.ProductionOrderNo) && 
                    t.IsDelete == false
                ).ToList();
                productionOrderList.AddRange(batchResult);
            }
            productionOrderList = productionOrderList.Where(t => !string.IsNullOrEmpty(t.SalesOrderNo)).ToList();
            // 用于获取销售订单号 订单行
            var productionOrderMap =
                productionOrderList.ToDictionary(t => t.SalesOrderNo + t.SalesOrderLineNo, t => t.ProductionOrderNo);
            var salesOrderNos = productionOrderList.Select(t => t.SalesOrderNo).ToList();
            var saleOrderLineNos = productionOrderList.Select(t => t.SalesOrderNo + t.SalesOrderLineNo).ToList();
            List<SD_DeliveryScanImport> importList = new List<SD_DeliveryScanImport>();
            var planDetails = new List<SD_ShippingPlanDetail>(); // 替换为你的实际类型
            for (int i = 0; i < salesOrderNos.Count; i += batchSize)
            {
                // 获取当前批次
                var currentBatch = salesOrderNos
                    .Skip(i)
                    .Take(batchSize)
                    .ToList();
                // 查询当前批次
                var batchResult = _planDetailApp.GetList(t => 
                    currentBatch.Contains(t.SalesOrderNumber) && 
                    t.SalesOrderType == "主机" && 
                    t.IsDelete == false
                ).ToList();
                planDetails.AddRange(batchResult);
            }
            var planList = planDetails.Where(t => saleOrderLineNos.Contains(t.SalesOrderNumber + t.SalesLine)).ToList();
            foreach (var plan in planList)
            {
                productionOrderMap.TryGetValue(plan.SalesOrderNumber + plan.SalesLine, out string productionOrderNo);
                productionReportMap.TryGetValue(productionOrderNo, out PP_ProductionReport productionReport);
                if (productionReport == null) continue;
                SD_DeliveryScanImport deliveryScanImport = new SD_DeliveryScanImport();
                deliveryScanImport.客户订单号 = plan.CustomerOrderNum;
                deliveryScanImport.销售订单号 = plan.SalesOrderNumber;
                deliveryScanImport.销售单行号 = plan.SalesLine;
                deliveryScanImport.销售订单类型 = plan.SalesType;
                deliveryScanImport.交货日期 = plan.DeliveryDate;
                deliveryScanImport.客户编号 = plan.CustomerCode;
                deliveryScanImport.客户名称 = plan.CustomerName;
                deliveryScanImport.客户地址 = plan.CustomerAdd;
                deliveryScanImport.物料编号 = plan.ItemCode;
                deliveryScanImport.交货数量 = plan.ShippingPlanDetailQty;
                deliveryScanImport.库存单位 = productionReport.Unit;
                deliveryScanImport.仓库编号 = productionReport.ReceivingLocation;
                deliveryScanImport.过账时间 = productionReport.PostTime;
                deliveryScanImport.合同单号 = plan.CONT;
                deliveryScanImport.结算地址 = plan.SettlementAdd;
                importList.Add(deliveryScanImport);
            }
            _deliveryScanApp.ImportExcelToBaseData(importList, "sys", out string message);
            var contractNos = importList.Select(t => t.合同单号).ToList();
            var mailList = _dictionaryApp
                .GetList(t => t.TypeCode.Equals("MailConfig") && t.EnumValue.Equals("Delivery"))
                .ToList();
            if (mailList.Count > 0)
            {
                var mails = mailList[0].EnumValue1.Split(',');
                if (mails.Length > 0)
                {
                    sendMail(mails, contractNos);
                }
            }
        }

        #region 发送邮件

        private bool sendMail(string[] mails,List<string> successList)
        {
            // 整合图文邮件
            Sys_MailServerConfig mailConfig = _mailConfigApp.GetMailServerConfig();
            Email myMail = new Email(mailConfig.MailServerHost, (int)mailConfig.MailServerPort,
                mailConfig.MailServerAccount, mailConfig.MailServerPassword);
            myMail.SenderDisplayName = mailConfig.SenderDisplayName ?? mailConfig.MailServerAccount;
            // 创建邮件消息
            MailMessage msg = new MailMessage
            {
                From = new MailAddress(mailConfig.MailServerAccount, mailConfig.MailServerAccount, Encoding.UTF8),
                Subject = DateTime.Now.Year + "年" + DateTime.Now.Month + "月" + DateTime.Now.Day + "日发运报告",
                BodyEncoding = Encoding.UTF8,
                IsBodyHtml = true,
                Priority = MailPriority.Normal
            };
            foreach (var mail in mails)
            {
                msg.To.Add(mail);
            }
            // 生成HTML表格内容
            string shippingHtml = GenerateHtmlTable(successList);
            // 创建HTML视图
            string htmlBody = $@"
                    <html>
                    <head>
                        <style>
                            table {{
                                width: 100%;
                                border-collapse: collapse;
                            }}
                            th, td {{
                                padding: 12px;
                                border: 1px solid #ddd;
                                text-align: left;
                            }}
                            th {{
                                background-color: #f2f2f2;
                            }}
                            tr:hover {{
                                background-color: #f5f5f5;
                            }}
                        </style>
                    </head>
                    <body>
                        <h1>发运处理明细</h1>
                        {shippingHtml}
                    </body>
                    </html>";
            AlternateView avHtml = AlternateView.CreateAlternateViewFromString(htmlBody, null, "text/html");
            msg.AlternateViews.Add(avHtml);
            // 发送邮件
            using (SmtpClient client = new SmtpClient(mailConfig.MailServerHost, (int)mailConfig.MailServerPort))
            {
                client.Credentials =
                    new NetworkCredential(mailConfig.MailServerAccount, mailConfig.MailServerPassword);
                client.EnableSsl = true;
                client.Send(msg);
            }
            return true;
        }

        private string GenerateHtmlTable(List<string> successList)
        {
            int size = 0;
            if (successList.Count > size)
            {
                size = successList.Count;
            }
            StringBuilder sb = new StringBuilder();
            sb.Append("<table>");
            sb.Append("<tr><th>序号</th><th>成功合同</th></tr>");
            for (int i = 0; i < size; i++)
            {
                string successStr = "";
                if (successList.Count > i)
                {
                    successStr = successList[i];
                }
                sb.Append("<tr>");
                sb.AppendFormat("<td>{0}</td>", i + 1);
                sb.AppendFormat("<td>{0}</td>", successStr);
                sb.Append("</tr>");
            }
            sb.Append("</table>");
            return sb.ToString();
        }

        #endregion
    }
}