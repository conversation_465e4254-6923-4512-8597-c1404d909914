using System.ComponentModel;

namespace HZ.WMS.Entity.SD
{
    /// <summary>
    /// 发运计划
    /// </summary>
    public class SetSortAndBatchReq
    {
        /// <summary> 
        /// Id
        /// </summary> 
        [Description("Id")]
        public string Id { get; set; }

        /// <summary> 
        /// 生产序号
        /// </summary> 
        [Description("生产序号")]
        public decimal ProduceSort { get; set; }

        /// <summary> 
        /// 生产批次
        /// </summary> 
        [Description("生产批次")]
        public int ProduceBatch { get; set; }
    }
}