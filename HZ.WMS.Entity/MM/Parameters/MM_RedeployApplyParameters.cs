using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.MM.Parameters
{
    /// <summary>
    /// 接收参数的类
    /// </summary>
    public class MM_RedeployApplyParameters
    {
        /// <summary>
        /// 调拨申请单明细
        /// </summary>
        public List<MM_RedeployApplyDetail> DetailedList { get; set; }

        /// <summary>
        /// 删除明细数组
        /// </summary>
        public string[] deldetailArray { get; set; }

        /// <summary>
        /// 手动过账时间
        /// </summary>
        public DateTime? ManualPostTime { get; set; }

        /// <summary>
        /// 调拨申请单号
        /// </summary>
        public string DocNum { get; set; }

        /// <summary>
        /// 办理人员编号
        /// </summary>
        public string HandlenCode { get; set; }

        /// <summary>
        /// 办理人员
        /// </summary>
        public string HandlenName { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }


        /// <summary>
        /// 部门
        /// </summary>
        public string Department { get; set; }

        /// <summary>
        /// 公司代码
        /// </summary>
        public string FactoryCode { get; set; }
    }
}
