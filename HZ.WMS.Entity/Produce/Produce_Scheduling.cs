using System;
using System.Collections.Generic;
using System.ComponentModel;
using HZ.WMS.Entity.Produce.Res;
using SqlSugar;

namespace HZ.WMS.Entity.SD
{
    /// <summary>
    /// 排产信息
    /// </summary>
    [SugarTable("Produce_Scheduling")]
    public class Produce_Scheduling : BaseEntity
    {
        
        /// <summary> 
        /// ID
        /// </summary> 
        [SugarColumn(IsPrimaryKey = true)]
        [Description("ID")]
        public string Id { get; set; }
        
        /// <summary> 
        /// wms单号
        /// </summary> 
        [Description("发运单号")]
        public string DocNum { get; set; }
        
        /// <summary> 
        /// wms行号
        /// </summary> 
        [Description("发运行号")]
        public int? DocLine { get; set; }
        
        /// <summary> 
        /// 订单类型
        /// </summary> 
        [Description("订单类型")]
        public int? OrderType { get; set; }
        
        /// <summary> 
        /// Sap订单号
        /// </summary> 
        [Description("Sap订单号")]
        public string SaleSapNo { get; set; }
        
        /// <summary> 
        /// Sap行号
        /// </summary> 
        [Description("Sap行号")]
        public int? SaleSapLine { get; set; }
        
        /// <summary> 
        /// 客户订单号
        /// </summary> 
        [Description("客户订单号")]
        public string CustomerOrderNo { get; set; }
        
        /// <summary> 
        /// 合同号
        /// </summary> 
        [Description("合同号")]
        public string ContractNo { get; set; }
        
        /// <summary> 
        /// 出厂编号
        /// </summary> 
        [Description("出厂编号")]
        public string FactoryNo { get; set; }
        
        /// <summary> 
        /// 物料编号
        /// </summary> 
        [Description("物料编号")]
        public string ItemCode { get; set; }

        /// <summary> 
        /// 物料名称
        /// </summary> 
        [Description("物料名称")]
        public string ItemName { get; set; }

        /// <summary> 
        /// 客户件号
        /// </summary> 
        [Description("客户件号")]
        public string CustomerPart { get; set; }
        
        /// <summary> 
        /// 客户编号
        /// </summary> 
        [Description("客户编号")]
        public string CustomerCode { get; set; }

        /// <summary> 
        /// 客户名称
        /// </summary> 
        [Description("客户名称")]
        public string CustomerName { get; set; }
        
        /// <summary> 
        /// 客户地址
        /// </summary> 
        [Description("客户地址")]
        public string CustomerAdd { get; set; }

        /// <summary>
        /// 结算地址
        /// </summary>
        [Description("结算地址")]
        public string SettlementAdd { get; set; }
        
        /// <summary> 
        /// 数量
        /// </summary> 
        [Description("数量")]
        public int? Quantity { get; set; }
        
        /// <summary> 
        /// 单位
        /// </summary> 
        [Description("单位")]
        public string Unit { get; set; }
        
        /// <summary> 
        /// 重量
        /// </summary> 
        [Description("重量")]
        public string Weight { get; set; }
        
        /// <summary> 
        /// 计划装配日期
        /// </summary> 
        [Description("计划装配日期")]
        public DateTime? PlanAssemblyDate { get; set; }

        /// <summary>
        /// 计划入库时间
        /// </summary>
        [Description("计划入库时间")]
        public DateTime? PlanStockTime { get; set; }

        /// <summary>
        /// 实际入库时间
        /// </summary>
        [Description("实际入库时间")]
        public DateTime? ActualStockTime { get; set; }
        
        /// <summary>
        /// 生产备注
        /// </summary>
        [Description("生产备注")]
        public string ProductionRemark { get; set; }

        /// <summary>
        /// 状态 0-未排产 1-预排产 2-正式排产 3-生产中
        /// </summary>
        [Description("状态")]
        public int? Status { get; set; }
        
        /// <summary>
        /// 生产线
        /// </summary>
        [Description("生产线")]
        public string ProduceLine { get; set; }
        
        /// <summary>
        /// 工作中心编码
        /// </summary>
        [Description("工作中心编码")]
        public string ProduceLineCode { get; set; }
        
        /// <summary>
        /// 生产版本
        /// </summary>
        [Description("生产版本")]
        public string ProduceVersion { get; set; }
        
        /// <summary>
        /// 顺序号
        /// </summary>
        [Description("顺序号")]
        public string SequenceNo { get; set; }
        
        /// <summary>
        /// 催货日期
        /// </summary>
        [Description("催货日期")]
        public DateTime? ExpeditingDate { get; set; }
        
        /// <summary>
        /// 编号
        /// </summary>
        [Description("编号")]
        public string SerialNo { get; set; }
        
        /// <summary> 
        /// 批次号
        /// </summary> 
        [Description("批次号")]
        public string BatchNo { get; set; }
        
        /// <summary> 
        /// 订单ID
        /// </summary> 
        [Description("订单ID")]
        public string OrderId { get; set; }
        
        /// <summary> 
        /// PCS ID
        /// </summary> 
        [Description("PCS ID")]
        public string PcsId { get; set; }
        
        /// <summary> 
        /// 排产日期
        /// </summary> 
        [Description("排产日期")]
        public DateTime? ProduceSchedulingDate { get; set; }
        
        /// <summary> 
        /// 计划开始时间
        /// </summary> 
        [Description("计划开始时间")]
        public DateTime? PlanStartTime { get; set; }
        
        /// <summary> 
        /// 计划结束时间
        /// </summary> 
        [Description("计划结束时间")]
        public DateTime? PlanEndTime { get; set; }
        
        /// <summary> 
        /// 交货日期
        /// </summary> 
        [Description("交货日期")]
        public DateTime? DeliveryDate { get; set; }
        
        /// <summary> 
        /// 产品型号
        /// </summary> 
        [Description("产品型号")]
        public string ProduceModel { get; set; }
        
        /// <summary> 
        /// Sap产品型号
        /// </summary> 
        [Description("Sap产品型号")]
        public string SapProductModel { get; set; }
        
        /// <summary> 
        /// 生产序号
        /// </summary> 
        [Description("生产序号")]
        public decimal? ProduceSort { get; set; }
        
        /// <summary> 
        /// 生产批次
        /// </summary> 
        [Description("生产批次")]
        public int? ProduceBatch { get; set; }
        
        /// <summary>
        /// 盘车装置
        /// </summary>
        [Description("盘车装置")]
        public string TurningGear { get; set; }
        
        /// <summary>
        /// 盘车位置
        /// </summary>
        [Description("盘车位置")]
        public string TurningPosition { get; set; }
        
        /// <summary>
        /// 制动器电压DC
        /// </summary>
        [Description("制动器电压DC")]
        public string BrakeVoltage { get; set; }
        
        /// <summary>
        /// 接线方式
        /// </summary>
        [Description("接线方式")]
        public string JunctionMethod { get; set; }
        
        /// <summary>
        /// 编码器
        /// </summary>
        [Description("编码器")]
        public string Encoder { get; set; }
        
        /// <summary>
        /// 编码器前置
        /// </summary>
        [Description("编码器前置")]
        public string EncoderPre { get; set; }
        
        /// <summary>
        /// 编码器接头
        /// </summary> 
        [Description("编码器接头")]
        public string EncoderConnector { get; set; }
        
        /// <summary>
        /// 编码器线长
        /// </summary>
        [Description("编码器线长")]
        public string EncoderLineLength { get; set; }
        
        /// <summary>
        /// 软管
        /// </summary>
        [Description("软管")]
        public string Hose { get; set; }
        
        /// <summary>
        /// 变频器
        /// </summary>
        [Description("变频器")]
        public string FrequencyTransformer { get; set; }
        
        /// <summary>
        /// 主机护罩
        /// </summary>
        [Description("主机护罩")]
        public string HostCover { get; set; }
        
        /// <summary>
        /// 远程松闸
        /// </summary>
        [Description("远程松闸")]
        public string RemoteDeclutch { get; set; }
        
        /// <summary>
        /// 松闸线长
        /// </summary>
        [Description("松闸线长")]
        public string DeclutchLineLength { get; set; }
        
        /// <summary>
        /// 主机颜色
        /// </summary>
        [Description("主机颜色")]
        public string HostColor { get; set; }
        
        /// <summary>
        /// 额定载重
        /// </summary>
        [Description("额定载重")]
        public string RatedLoad { get; set; }
        
        /// <summary>
        /// 额定梯速
        /// </summary>
        [Description("额定梯速")]
        public string RatedElevatorSpeed { get; set; }
        
        /// <summary>
        /// 额定电压
        /// </summary>
        [Description("额定电压")]
        public string RatedVoltage { get; set; }
        
        /// <summary>
        /// 主机铭牌
        /// </summary>
        [Description("主机铭牌")]
        public string HostNameplate { get; set; }
        
        /// <summary>
        /// 曳引比
        /// </summary>
        [Description("曳引比")]
        public string TractionRatio { get; set; }
        
        /// <summary>
        /// 客户型号
        /// </summary>  
        [Description("客户型号")]
        public string CustomerModel { get; set; }
        
        /// <summary>
        /// 刷字
        /// </summary>
        [Description("刷字")]
        public string Brush { get; set; }
        
        /// <summary>
        /// 是否出口
        /// </summary> 
        [Description("是否出口")]
        public string IsExport { get; set; }
        
        /// <summary>
        /// 项目名称
        /// </summary> 
        [Description("项目名称")]
        public string ProjectName { get; set; }
        
        /// <summary>
        /// 履行备注
        /// </summary>
        [Description("履行备注")]
        public string PerformRemark { get; set; }
        
        /// <summary>
        /// 订单备注
        /// </summary>
        [Description("订单备注")]
        public string OrderRemark { get; set; }
        
        /// <summary>
        /// 明细备注
        /// </summary>
        [Description("明细备注")]
        public string DetailRemark { get; set; }
        
        /// <summary>
        /// 非标接收编号
        /// </summary>  
        [Description("非标接收编号")]
        public string NonstandardNo { get; set; }
        
        /// <summary>
        /// 部件代码
        /// </summary>  
        [Description("部件代码")]
        public string PartCode { get; set; }
        
        /// <summary>
        /// 上行超速保护代码
        /// </summary>  
        [Description("上行超速保护代码")]
        public string UpOverSpeedCode { get; set; }
        
        /// <summary>
        /// 意外移动保护代码
        /// </summary>  
        [Description("意外移动保护代码")]
        public string AccidentMoveCode { get; set; }
        
        /// <summary>
        /// 入库时间
        /// </summary>  
        [Description("入库时间")]
        public DateTime? InStoreTime { get; set; }
        
        /// <summary>
        /// 入库时间
        /// </summary>  
        [Description("计划订单消息")]
        public string PlanMsg { get; set; }
        
        /// <summary>
        /// 入库时间
        /// </summary>  
        [Description("采购申请消息")]
        public string PurchaseApplyMsg { get; set; }
        
        /// <summary>
        /// 采购申请单号
        /// </summary>  
        [Description("采购申请单号")]
        public string PurchaseApplyNo { get; set; }
        
        /// <summary>
        /// 生产消息
        /// </summary>  
        [Description("生产消息")]
        public string ProduceMsg { get; set; }
        
        /// <summary>
        /// 采购消息
        /// </summary>  
        [Description("采购消息")]
        public string PurchaseMsg { get; set; }
        
        /// <summary>
        /// 计划订单号
        /// </summary>  
        [Description("计划订单号")]
        public string PlanOrderNo { get; set; }
        
        /// <summary>
        /// 计划订单行号
        /// </summary>  
        [Description("计划订单行号")]
        public string PurchaseApplyLine { get; set; }
        
        /// <summary>
        /// 生产订单号
        /// </summary>  
        [Description("生产订单号")]
        public string ProduceOrderNo { get; set; }
        
        /// <summary>
        /// 采购订单号
        /// </summary>  
        [Description("采购订单号")]
        public string PurchaseOrderNo { get; set; }
        
        /// <summary>
        /// 采购订单行
        /// </summary>  
        [Description("采购订单行")]
        public string PurchaseOrderLine { get; set; }
        
        /// <summary>
        /// 计划订单状态
        /// </summary>  
        [Description("计划订单状态")]
        public string PlanStatus { get; set; }
        
        /// <summary>
        /// 采购申请状态
        /// </summary>  
        [Description("采购申请状态")]
        public string PurchaseApplyStatus { get; set; }
        
        /// <summary>
        /// 生产订单状态
        /// </summary>  
        [Description("生产订单状态")]
        public string ProduceStatus { get; set; }
        
        /// <summary>
        /// 采购订单状态
        /// </summary>  
        [Description("采购订单状态")]
        public string PurchaseStatus { get; set; }
        
        /// <summary>
        /// 计划订单操作时间
        /// </summary>  
        [Description("计划订单操作时间")]
        public DateTime? PlanTime { get; set; }
        
        /// <summary>
        /// 采购申请操作时间
        /// </summary>  
        [Description("采购申请操作时间")]
        public DateTime? PurchaseApplyTime { get; set; }
        
        /// <summary>
        /// 生产订单操作时间
        /// </summary>  
        [Description("生产订单操作时间")]
        public DateTime? ProduceTime { get; set; }
        
        /// <summary>
        /// 采购订单操作时间
        /// </summary>  
        [Description("采购订单操作时间")]
        public DateTime? PurchaseTime { get; set; }
        
        // 在Produce_Scheduling类中添加以下属性
        [Description("销售BOM数据")]
        [SugarColumn(IsIgnore = true)] // 表示该字段不映射到数据库
        public List<SaleBomRes> SaleBoms { get; set; }
        
    }
}
