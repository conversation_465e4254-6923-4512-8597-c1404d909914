using HZ.Core.Http;
using HZ.WMS.Application.MM;
using HZ.WMS.Entity.MM;
using HZ.WMS.Entity.Sys;
using HZ.WMS.WebAPI.Controllers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Http;

namespace HZ.WMS.WebAPI.Areas.MM.Controllers
{
    /// <summary>
    /// 物料标签扫描
    /// </summary>
    public class MM_BarCodeScanController : ApiBaseController
    {
        private MM_BarCodeScanApp _app = new MM_BarCodeScanApp();

        #region 查询物料标签

        /// <summary>
        /// 查询物料标签
        /// </summary>
        /// <param name="BarCode">条码号</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetBarCodeInfo([FromUri] string BarCode)
        {
            string error_message;
            var result = new ResponseData();
            //if (_app.CheckDate(BarCode, out error_message))//校验
            //{
                try
                {
                    var itemsData = _app.GetBarCodeInfo(BarCode,out error_message);
                    if (itemsData == null)
                    {
                        result.Code = (int)WMSStatusCode.Success;
                        result.Message = error_message;
                    }
                    else
                    {
                        result.Data = itemsData;
                        result.Code = (int)WMSStatusCode.Success;
                    }
                }
                catch (Exception ex)
                {
                    result.Code = (int)WMSStatusCode.UnHandledException;
                    result.Message = ex.InnerException?.Message ?? ex.Message;
                }
            //}
            //else
            //{
            //    result.Code = (int)WMSStatusCode.Failed;
            //    result.Message = error_message;
            //}
            return Json(result);
        }

        #endregion

        #region 保存

        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Save([FromBody]List<MM_BarCodeScan> list)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                Sys_User currentUser = GetCurrentUser();
                bool bSubmit = _app.Save(list, currentUser.LoginAccount, out error_message);
                if (!bSubmit)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion


    }
}