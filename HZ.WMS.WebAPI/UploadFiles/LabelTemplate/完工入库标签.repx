/// <XRTypeInfo>
///   <AssemblyFullName>DevExpress.XtraReports.v16.1, Version=16.1.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</AssemblyFullName>
///   <AssemblyLocation>D:\Dawn\Aos\Project\XZFWD\Svn\WMS\01开发域\03编码实现\Code\04Report\WindowsFormsApplication1\WindowsFormsApplication1\bin\Debug\DevExpress.XtraReports.v16.1.dll</AssemblyLocation>
///   <TypeName>DevExpress.XtraReports.UI.XtraReport</TypeName>
///   <Localization>zh-CN</Localization>
///   <Version>16.1</Version>
///   <Resources>
///     <Resource Name="XtraReportSerialization.FinishWarehousing">
/// zsrvvgEAAACRAAAAbFN5c3RlbS5SZXNvdXJjZXMuUmVzb3VyY2VSZWFkZXIsIG1zY29ybGliLCBWZXJzaW9uPTQuMC4wLjAsIEN1bHR1cmU9bmV1dHJhbCwgUHVibGljS2V5VG9rZW49Yjc3YTVjNTYxOTM0ZTA4OSNTeXN0ZW0uUmVzb3VyY2VzLlJ1bnRpbWVSZXNvdXJjZVNldAIAAAABAAAAAAAAAFBBRFBBRFBIXnLBAAAAAOcAAAAmJAB0AGgAaQBzAC4AUwBjAHIAaQBwAHQAcwBTAG8AdQByAGMAZQAAAAAAAekCDQpwcml2YXRlIHZvaWQgRGV0YWlsX0JlZm9yZVByaW50KG9iamVjdCBzZW5kZXIsIFN5c3RlbS5EcmF3aW5nLlByaW50aW5nLlByaW50RXZlbnRBcmdzIGUpIHsNCglTY2FubmluZ0NvZGUuRGF0YUJpbmRpbmdzLkFkZCgiVGV4dCIsdGhpcy5EYXRh
/// U291cmNlLCJTZXJpYWxObyIpOw0KCVNlcmlhbE5vLkRhdGFCaW5kaW5ncy5BZGQoIlRleHQiLHRoaXMuRGF0YVNvdXJjZSwiU2VyaWFsTm8iKTsNCglQYXJ0Q29kZS5EYXRhQmluZGluZ3MuQWRkKCJUZXh0Iix0aGlzLkRhdGFTb3VyY2UsIlBhcnRDb2RlIik7DQoJQ29udHJhY3ROby5EYXRhQmluZGluZ3MuQWRkKCJUZXh0Iix0aGlzLkRhdGFTb3VyY2UsIkNvbnRyYWN0Tm8iKTsNCn0NCg==</Resource>
///   </Resources>
/// </XRTypeInfo>
namespace XtraReportSerialization {
    
    public class FinishWarehousing : DevExpress.XtraReports.UI.XtraReport {
        private DevExpress.XtraReports.UI.TopMarginBand TopMargin;
        private DevExpress.XtraReports.UI.XRLabel PartCode;
        private DevExpress.XtraReports.UI.XRLabel ContractNo;
        private DevExpress.XtraReports.UI.BottomMarginBand BottomMargin;
        private DevExpress.XtraReports.UI.XRLabel SerialNo;
        private DevExpress.XtraReports.Parameters.Parameter paramBarCode;
        private DevExpress.XtraReports.UI.DetailBand Detail;
        private DevExpress.XtraReports.UI.XRBarCode ScanningCode;
        private System.Resources.ResourceManager _resources;
        private string _resourceString;
        public FinishWarehousing() {
            this._resourceString = DevExpress.XtraReports.Serialization.XRResourceManager.GetResourceFor("XtraReportSerialization.FinishWarehousing");
            this.InitializeComponent();
        }
        private System.Resources.ResourceManager resources {
            get {
                if (_resources == null) {
                    this._resources = new DevExpress.XtraReports.Serialization.XRResourceManager(this._resourceString);
                }
                return this._resources;
            }
        }
        private void InitializeComponent() {
            DevExpress.XtraPrinting.BarCode.QRCodeGenerator qRCodeGenerator1 = new DevExpress.XtraPrinting.BarCode.QRCodeGenerator();
            this.TopMargin = new DevExpress.XtraReports.UI.TopMarginBand();
            this.PartCode = new DevExpress.XtraReports.UI.XRLabel();
            this.ContractNo = new DevExpress.XtraReports.UI.XRLabel();
            this.BottomMargin = new DevExpress.XtraReports.UI.BottomMarginBand();
            this.SerialNo = new DevExpress.XtraReports.UI.XRLabel();
            this.paramBarCode = new DevExpress.XtraReports.Parameters.Parameter();
            this.Detail = new DevExpress.XtraReports.UI.DetailBand();
            this.ScanningCode = new DevExpress.XtraReports.UI.XRBarCode();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // TopMargin
            // 
            this.TopMargin.Dpi = 254F;
            this.TopMargin.HeightF = 0F;
            this.TopMargin.Name = "TopMargin";
            this.TopMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 254F);
            this.TopMargin.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // PartCode
            // 
            this.PartCode.Dpi = 254F;
            this.PartCode.Font = new System.Drawing.Font("宋体", 10F);
            this.PartCode.LocationFloat = new DevExpress.Utils.PointFloat(213.7832F, 100.0648F);
            this.PartCode.Name = "PartCode";
            this.PartCode.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.PartCode.SizeF = new System.Drawing.SizeF(416.2168F, 50.48247F);
            this.PartCode.StylePriority.UseFont = false;
            this.PartCode.StylePriority.UseTextAlignment = false;
            this.PartCode.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // ContractNo
            // 
            this.ContractNo.Dpi = 254F;
            this.ContractNo.Font = new System.Drawing.Font("宋体", 10F);
            this.ContractNo.LocationFloat = new DevExpress.Utils.PointFloat(213.7832F, 150.5473F);
            this.ContractNo.Name = "ContractNo";
            this.ContractNo.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.ContractNo.SizeF = new System.Drawing.SizeF(415.8781F, 63.71164F);
            this.ContractNo.StylePriority.UseFont = false;
            this.ContractNo.StylePriority.UseTextAlignment = false;
            this.ContractNo.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // BottomMargin
            // 
            this.BottomMargin.Dpi = 254F;
            this.BottomMargin.HeightF = 0F;
            this.BottomMargin.Name = "BottomMargin";
            this.BottomMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 254F);
            this.BottomMargin.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // SerialNo
            // 
            this.SerialNo.Dpi = 254F;
            this.SerialNo.Font = new System.Drawing.Font("宋体", 12F);
            this.SerialNo.LocationFloat = new DevExpress.Utils.PointFloat(213.7832F, 36.35318F);
            this.SerialNo.Name = "SerialNo";
            this.SerialNo.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 0, 0, 254F);
            this.SerialNo.SizeF = new System.Drawing.SizeF(415.8781F, 63.71164F);
            this.SerialNo.StylePriority.UseFont = false;
            this.SerialNo.StylePriority.UseTextAlignment = false;
            this.SerialNo.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // paramBarCode
            // 
            this.paramBarCode.Description = "条码号";
            this.paramBarCode.MultiValue = true;
            this.paramBarCode.Name = "paramBarCode";
            this.paramBarCode.ValueInfo = "M201911140017|M201911140001|M201911140014";
            // 
            // Detail
            // 
            this.Detail.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
                        this.ContractNo,
                        this.PartCode,
                        this.SerialNo,
                        this.ScanningCode});
            this.Detail.Dpi = 254F;
            this.Detail.HeightF = 216.9047F;
            this.Detail.Name = "Detail";
            this.Detail.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 254F);
            this.Detail.PageBreak = DevExpress.XtraReports.UI.PageBreak.BeforeBand;
            this.Detail.Scripts.OnBeforePrint = "Detail_BeforePrint";
            this.Detail.SortFields.AddRange(new DevExpress.XtraReports.UI.GroupField[] {
                        new DevExpress.XtraReports.UI.GroupField("BarCode", DevExpress.XtraReports.UI.XRColumnSortOrder.Ascending)});
            this.Detail.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // ScanningCode
            // 
            this.ScanningCode.Alignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.ScanningCode.AutoModule = true;
            this.ScanningCode.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.ScanningCode.Dpi = 254F;
            this.ScanningCode.Font = new System.Drawing.Font("宋体", 7.8F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.ScanningCode.LocationFloat = new DevExpress.Utils.PointFloat(0F, 36.35315F);
            this.ScanningCode.Module = 5.08F;
            this.ScanningCode.Name = "ScanningCode";
            this.ScanningCode.Padding = new DevExpress.XtraPrinting.PaddingInfo(25, 25, 0, 0, 254F);
            this.ScanningCode.ShowText = false;
            this.ScanningCode.SizeF = new System.Drawing.SizeF(213.7832F, 152.5522F);
            this.ScanningCode.StylePriority.UseBorders = false;
            this.ScanningCode.StylePriority.UseFont = false;
            this.ScanningCode.StylePriority.UseTextAlignment = false;
            qRCodeGenerator1.CompactionMode = DevExpress.XtraPrinting.BarCode.QRCodeCompactionMode.Byte;
            this.ScanningCode.Symbology = qRCodeGenerator1;
            this.ScanningCode.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // FinishWarehousing
            // 
            this.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
                        this.Detail,
                        this.TopMargin,
                        this.BottomMargin});
            this.Dpi = 254F;
            this.Margins = new System.Drawing.Printing.Margins(0, 0, 0, 0);
            this.Name = "FinishWarehousing";
            this.PageHeight = 240;
            this.PageWidth = 630;
            this.PaperKind = System.Drawing.Printing.PaperKind.Custom;
            this.Parameters.AddRange(new DevExpress.XtraReports.Parameters.Parameter[] {
                        this.paramBarCode});
            this.ReportUnit = DevExpress.XtraReports.UI.ReportUnit.TenthsOfAMillimeter;
            this.ScriptsSource = resources.GetString("$this.ScriptsSource");
            this.SnapGridSize = 31.75F;
            this.Version = "16.1";
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();
        }
    }
}
