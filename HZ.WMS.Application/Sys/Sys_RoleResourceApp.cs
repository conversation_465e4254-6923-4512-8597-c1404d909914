using SqlSugar;
using System;
using System.Collections.Generic;

using HZ.Core.Security;
using HZ.WMS.Entity.Sys;
using HZ.Core.Http;

namespace HZ.WMS.Application.Sys
{
    /// <summary>
    /// 没有out_message消息的函数，意味着只在最前端做未处理异常捕捉
    /// </summary>

    public class Sys_RoleResourceApp : BaseApp<Sys_RoleResource>
    {
		#region 默认构造函数
        
        /// <summary>
        /// 默认构造函数
        /// </summary>
        public Sys_RoleResourceApp() : base()
        {
        }

        
        

        #endregion

        #region 重置角色权限

        /// <summary>
        /// 重置角色权限菜单
        /// </summary>
        /// <param name="roleId">角色</param>
        /// <param name="resourceIds">资源菜单ID清单</param>
        /// <param name="operateUser">操作者</param>
        /// <returns></returns>

        public bool ResetRoleResources(string roleId,string[] resourceIds,string operateUser)
        {
            List<Sys_RoleResource> list = new List<Sys_RoleResource>();
            base.DbContext.Ado.BeginTran();
            base.HardDelete(t => t.RoleID == roleId);


            foreach (string resourceId in resourceIds)
            {
                Sys_RoleResource srr = new Sys_RoleResource();
                srr.ResourceID = resourceId;
                srr.RoleID = roleId;
                srr.CUser = operateUser;
                srr.MUser = operateUser;
                list.Add(srr);
            }
            base.Insert(list);
            base.DbContext.Ado.CommitTran();
            return true;

        }


        #endregion

    }
}

