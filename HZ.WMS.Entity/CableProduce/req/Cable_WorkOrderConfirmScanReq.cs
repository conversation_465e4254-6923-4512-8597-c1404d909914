using System.ComponentModel;
using HZ.WMS.Entity;

namespace AOS.WMS.Entity.CableProduce
{
    public class Cable_WorkOrderConfirmScan : BaseEntity
    {
        
        /// <summary>
        /// Id
        /// </summary>
        [Description("Id")]
        public string Id { get; set; }
        
        /// <summary>
        /// 类型
        /// </summary>
        [Description("类型 1扫描状态 2井道扫描")]
        public int Type { get; set; }
        
    }
}