using SqlSugar;
using HZ.Core.Http;
//
using HZ.WMS.Application.MD;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.Sys;
using HZ.WMS.WebAPI.Controllers;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Web;
using System.Web.Http;
using HZ.Core.Office;
using HZ.WMS.Application;

namespace HZ.WMS.WebAPI.Areas.MD.Controllers
{
    /// <summary>
    /// 库位管理
    /// </summary>
    public class MD_BinLocationController : ApiBaseController
    {
        private MD_BinLocationApp _app = new MD_BinLocationApp();
        private MD_RegionApp _regionApp = new MD_RegionApp();
        private MD_WarehouseApp _warehouseApp = new MD_WarehouseApp();
        private MD_LabelTempleteApp _labelTemplateApp = new MD_LabelTempleteApp();

        #region 查询分页列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]string keyword)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetPageList(page, x => string.IsNullOrEmpty(keyword)
                                                        || x.BinLocationCode.Contains(keyword)
                                                        || x.BinLocationName.Contains(keyword)
                                                        || x.RegionCode.Contains(keyword)
                                                        || x.RegionName.Contains(keyword)
                                                        || x.WhsCode.Contains(keyword)
                                                        || x.WhsName.Contains(keyword)
                                                        || x.Remark.Contains(keyword))?.ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 添加
        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Add([FromBody]MD_BinLocation entity)
        {
            var result = new ResponseData();
            try
            {
                Sys_User currLoginUser = GetCurrentUser();
                entity.CUser = currLoginUser.LoginAccount;
                entity.MUser = currLoginUser.LoginAccount;
                entity.CTime = DateTime.Now;
                entity.MTime = DateTime.Now;
                result.Data = _app.Insert(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取详情
        /// <summary>
        /// 获取详情
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetEntity([FromUri]string ID)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.GetEntityByKey(ID);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Update([FromBody]MD_BinLocation entity)
        {
            var result = new ResponseData();
            try
            {
                entity.MUser = GetCurrentUser().LoginAccount;
                entity.MTime = DateTime.Now;
                result.Data = _app.Update(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除（包括批量）

        /// <summary>
        /// 删除
        /// </summary>
        /// <returns></returns>
        [HttpDelete]
        public IHttpActionResult Delete([FromBody]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.DeleteByKeys(ids, GetCurrentUser().LoginAccount);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 查询列表



        /// <summary>
        /// 查询列表
        /// </summary>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetList([FromUri]string keyword)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetList(x => string.IsNullOrEmpty(keyword)
                    || x.BinLocationCode.Contains(keyword)
                    || x.BinLocationName.Contains(keyword)
                    || x.RegionCode.Contains(keyword)
                    || x.RegionName.Contains(keyword)
                    || x.WhsCode.Contains(keyword)
                    || x.WhsName.Contains(keyword)
                    || x.Remark.Contains(keyword)).ToList();
                result.Data = itemsData;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 根据库位拉取相关信息(仓库-区域-库位)
        /// <summary>
        /// 
        /// </summary>
        /// <param name="binLocationCode"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetRelationInfo([FromUri] string binLocationCode)
        {
            var result = new ResponseData();
            try
            {
                MD_Warehouse warehouse = _warehouseApp.GetWarehouseByBinLocationCode(binLocationCode);
                MD_Region region = _regionApp.GetRegionByBinLocation(binLocationCode);
                MD_BinLocation binLocation = _app.GetFirstEntity(x => x.BinLocationCode == binLocationCode);
                result.Data = new { Warehouse = warehouse, Region = region, BinLocation = binLocation };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }


        #endregion

        #region 获取所有库位信息

        /// <summary>
        /// 获取所有库位信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetBinLocationAll()
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.GetList()?.ToList();
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion


        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <param name="fromTime"></param>
        /// <param name="toTime"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPage([FromUri]Pagination page, [FromUri]string keyword, [FromUri]string fromTime, [FromUri]string toTime)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = new List<MD_BinLocation>();
                if (!string.IsNullOrEmpty(fromTime))
                {
                    itemsData = _app.GetPageList(page, x => x.CTime >= Convert.ToDateTime(fromTime).Date
                                                            && (string.IsNullOrEmpty(keyword)
                                                                || x.BinLocationCode.Contains(keyword)
                                                                || x.BinLocationName.Contains(keyword)
                                                                || x.RegionCode.Contains(keyword)
                                                                || x.RegionName.Contains(keyword)
                                                                || x.WhsCode.Contains(keyword)
                                                                || x.WhsName.Contains(keyword)
                                                                || x.Remark.Contains(keyword)));
                }
                else
                {
                    return GetPageList(page, keyword);
                }

                if (!string.IsNullOrEmpty(toTime))
                {
                    itemsData = _app.GetPageList(page, x => x.CTime >= Convert.ToDateTime(fromTime).Date && x.CTime < Convert.ToDateTime(toTime).Date.AddDays(1)
                    && (string.IsNullOrEmpty(keyword)
                    || x.BinLocationCode.Contains(keyword)
                    || x.BinLocationName.Contains(keyword)
                    || x.RegionCode.Contains(keyword)
                    || x.RegionName.Contains(keyword)
                    || x.WhsCode.Contains(keyword)
                    || x.WhsName.Contains(keyword)
                    || x.Remark.Contains(keyword)));
                }
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 获取详情
        /// </summary>
        /// <param name="binLocationCode"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetEntityByCode([FromUri]string binLocationCode)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.GetFirstEntityByFieldValue("binLocationCode", binLocationCode);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }



        /// <summary>
        /// 获取区域内库位
        /// </summary>
        /// <param name="page"></param>
        /// <param name="regionCode"></param>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageByRegionCode([FromUri]Pagination page, [FromUri]string regionCode, [FromUri]string keyword)
        {
            var result = new ResponseData();
            try
            {
                List<MD_BinLocation> query = null;
                query = _app.GetPageList(page, x => (string.IsNullOrEmpty(regionCode) || x.RegionCode == regionCode)
                && (string.IsNullOrEmpty(keyword)
                || x.BinLocationCode.Contains(keyword)
                || x.BinLocationName.Contains(keyword)
                || x.WhsCode.Contains(keyword)
                || x.WhsName.Contains(keyword)
                || x.Remark.Contains(keyword)));
                var itemsData = query?.OrderBy(x => x.BinLocationCode).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #region 标签打印

        /// <summary>
        /// 删除
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Print(string[] binlocationCodes)
        {
            var result = new ResponseData();
            try
            {
                MD_LabelTemplete template = _labelTemplateApp.GetFirstEntity(x => x.TempleteType == 10);     //库位标签
                // Create a report instance.
                DevExpress.XtraReports.UI.XtraReport report =
                    DevExpress.XtraReports.UI.XtraReport.FromFile(HttpContext.Current.Server.MapPath("~/") + ConfigurationManager.AppSettings["LabelTemplateUploadPath"]?.ToString() + template.TempleteFile, true);

                //DevExpress.DataAccess.Sql.SqlDataSource dataSource = report.DataSource as DevExpress.DataAccess.Sql.SqlDataSource;
                //DevExpress.DataAccess.Sql.QueryParameter parameter = new DevExpress.DataAccess.Sql.QueryParameter()
                //{
                //    Name = "parmBinLocationCode",
                //    Type = typeof(List<string>),
                //    Value = binlocationCodes.ToList()
                //};
                //dataSource.Queries[0].Parameters[0] = parameter;
                //dataSource.Fill();

                DevExpress.XtraReports.Parameters.Parameter binLocationParam = new DevExpress.XtraReports.Parameters.Parameter()
                {
                    Name = "parmBinLocationCode",
                    MultiValue = true,
                    Type = typeof(List<string>),
                    Value = binlocationCodes.ToList()
                };

                report.Parameters[0] = binLocationParam;

                return base.PrintToPDF(report);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 导出

        /// <summary>
        /// 
        /// </summary>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] string keyword)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetList().Where(t => string.IsNullOrEmpty(keyword)
                || t.BinLocationCode.Contains(keyword)
                                                        || t.BinLocationName.Contains(keyword)
                                                        || t.RegionCode.Contains(keyword)
                                                        || t.RegionName.Contains(keyword)
                                                        || t.WhsCode.Contains(keyword)
                                                        || t.WhsName.Contains(keyword)
                                                        || t.Remark.Contains(keyword)).ToList();
                //var itemsData = _app.GetList().ToList();
                List<ExcelColumn<MD_BinLocation>> columns = ExcelService.FetchDefaultColumnList<MD_BinLocation>();
                string[] ignoreField = new string[] { "IsFreeTax", "IsConsign", "IsDelete", "DTime", "DUser" };

                List<ExcelColumn<MD_BinLocation>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<MD_BinLocation> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }

                //columns.ForEach((column) =>
                //{
                //    if (column.ColumnName == "OrganizationID")
                //    {
                //        column.Formattor = ExcelExportFormatter.OrganizationFormatter;
                //    }

                //    if (column.ColumnName == "IsEnable")
                //    {
                //        column.Formattor = ExcelExportFormatter.IsEnableFormatter;
                //    }

                //    if (column.ColumnName == "Gender")
                //    {
                //        column.Formattor = ExcelExportFormatter.GenderFormatter;
                //    }
                //});

                return ExportToExcelFile<MD_BinLocation>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }

        }
        #endregion

        /// <summary>
        /// 获取区域内库位
        /// </summary>
        /// <param name="regionCode"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetListByRegionCode([FromUri]string regionCode = null)
        {
            var result = new ResponseData();
            try
            {
                ISugarQueryable<MD_BinLocation> query = null;
                query = _app.GetList(x => (string.IsNullOrEmpty(regionCode) || x.RegionCode == regionCode));
                var itemsData = query?.OrderBy(x => x.BinLocationCode)?.ToList();
                result.Data = itemsData;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

    }
}

