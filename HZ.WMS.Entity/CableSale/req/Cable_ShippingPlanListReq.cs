using System;
using System.ComponentModel;
using HZ.Core.Utilities;
using HZ.WMS.Entity;

namespace AOS.WMS.Entity.CableProduce
{
    public class Cable_ShippingPlanListReq : BaseEntity
    {
        /// <summary>
        /// 选中ID
        /// </summary>
        [Description("选中ID")]
        public string[] Ids { get; set; }
        
        /// <summary>
        /// 销售单号
        /// </summary>
        [Description("销售单号")]
        public string SapNo { get; set; }
        
        /// <summary>
        /// 客户名称
        /// </summary>
        [Description("客户名称")]
        public string CustomerName { get; set; }
        
        /// <summary>
        /// 客户单号
        /// </summary>
        [Description("客户单号")]
        public string CustomerOrderNum { get; set; }
        
        /// <summary>
        /// 订单类型名称
        /// </summary>
        [Description("订单类型名称")]
        public string OrderTypeName { get; set; }
        
        /// <summary>
        /// 电梯类型
        /// </summary>
        [Description("电梯类型")]
        public string ElevatorType { get; set; }

        /// <summary>
        /// 销售行号
        /// </summary>
        [Description("销售行号")]
        public int? SapLine { get; set; }

        /// <summary>
        /// 合同号
        /// </summary>
        [Description("合同号")]
        public string ContractNo { get; set; }

        /// <summary>
        /// 下载标记
        /// </summary>
        [Description("下载标记")]
        public int? ShipmentDownFlag { get; set; }
        
        /// <summary>
        /// 装配时间
        /// </summary>
        [Description("装配时间")]
        public DateTime[] ShipmentDate { get; set; }
        
        /// <summary>
        /// 创建时间
        /// </summary>
        [Description("创建时间")]
        public DateTime[] CreateDate { get; set; }

    }
}