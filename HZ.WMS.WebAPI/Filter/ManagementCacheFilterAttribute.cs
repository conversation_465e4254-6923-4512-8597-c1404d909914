using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Runtime.Caching;
using System.Web.Http;
using System.Web.Http.Controllers;
using System.Web.Http.Filters;

namespace HZ.WMS.WebAPI.Filter
{
    /// <summary>
    /// 缓存服务--防止重复提交
    /// </summary>
    public class ManagementCacheFilterAttribute : ActionFilterAttribute
    {
        private static readonly ObjectCache WebApiCache = MemoryCache.Default;
        private static readonly List<string> ignoreList = new List<string>();

        /// <summary>
        /// Action调用前执行的方法
        /// </summary>
        /// <param name="actionContext"></param>
        public override void OnActionExecuting(HttpActionContext actionContext)
        {
            CacheItemPolicy policy = new CacheItemPolicy();
            policy.SlidingExpiration = TimeSpan.FromMilliseconds(1000);
            string requestUrl = actionContext.Request.RequestUri.ToString();
            var token = actionContext.Request.Headers.SingleOrDefault(x => x.Key.ToLower() == "x-token").Value?.ToList().FirstOrDefault();
            var tokenCache = WebApiCache.Get(requestUrl);
            string requestMethod = actionContext.Request.Method.ToString();
            if (ignoreList.Count == 0)
            {
                ignoreList.Add("/Produce/Produce_Scheduling/GetPageList");
            }

            if ("POST".Equals(requestMethod.ToUpper()) && !IfIgnore(requestUrl))
            {
                if (tokenCache == null)
                {
                    WebApiCache.Add(requestUrl, token, policy);
                }
                else
                {
                    if (tokenCache.Equals(token))
                    {
                        actionContext.Response = actionContext.Request.CreateErrorResponse(HttpStatusCode.InternalServerError, new HttpError("您点的太快了，请休息一下再点吧！"));
                    }
                }
            }
        }
        
        private bool IfIgnore(string requestUrl)
        {
            for (var i = 0; i < ignoreList.Count; i++)
            {
                if (requestUrl.Contains(ignoreList[i]))
                {
                    return true;
                }
            }
            return false;
        }
    }
}