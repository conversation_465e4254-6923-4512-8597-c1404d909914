using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;
using System.ComponentModel;

namespace HZ.WMS.Entity.MD
{
    /// <summary>
    /// 销售客户地址
    /// </summary>
    public class MD_CustomerAdd : BaseEntity
    {
        /// <summary>
        /// ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Description("ID")]
        public string CustomerId { get; set; }

        /// <summary>
        /// 客户编号
        /// </summary>
        [Description("客户编号")]
        public string CustomerCode  { get; set; }

        /// <summary>
        /// 客户名称
        /// </summary>
        [Description("客户")]
        public string CustomerName  { get; set; }

        /// <summary>
        /// 是否默认
        /// </summary>
        [Description("是否默认")]
        public bool? IsDefault { get; set; } = false;

        /// <summary>
        /// 客户地址
        /// </summary>
        [Description("客户地址")]
        public string CustomerAdd  { get; set; }

        /// <summary>
        /// 联系人
        /// </summary>
        [Description("联系人")]
        public string Contact { get; set; }

        /// <summary>
        /// 联系方式
        /// </summary>
        [Description("联系方式")]
        public string Telephone { get; set; }

        /// <summary>
        /// 结算地址
        /// </summary>
        [Description("结算地址")]
        public string SettlementAdd { get; set; }

    }
}
