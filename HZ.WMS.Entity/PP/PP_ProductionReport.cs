using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.PP
{
    /// <summary>
    /// 生产报工
    /// </summary>
    public class PP_ProductionReport : BaseEntity
    {
        /// <summary>
        /// 主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string ID { get; set; }
        /// <summary>
        /// 生产报工单号
        /// </summary>
        [Description("生产报工单号")]
        public string ProductionReportNo { get; set; }
        /// <summary>
        /// 扫描码
        /// </summary>
        [Description("扫描码")]
        public string ScanningCode { get; set; }
        /// <summary>
        /// 序列号
        /// </summary>
        [Description("序列号")]
        public string SerialNo { get; set; }
        /// <summary>
        /// 生产订单
        /// </summary>
        [Description("生产订单")]
        public string ProductionOrderNo { get; set; }
        /// <summary>
        /// 主机生产订单
        /// </summary>
        [Description("主机生产订单")]
        public string HostProductionOrderNo { get; set; }
        /// <summary>
        /// 生产调度员,101是主机，组件
        /// </summary>
        [Description("生产调度员")]
        public string ProductionScheduler { get; set; }
        /// <summary>
        /// 订单类型
        /// </summary>
        [Description("订单类型")]
        public string OrderType { get; set; }
        /// <summary>
        /// 物料编码
        /// </summary>
        [Description("物料编码")]
        public string MaterialNo { get; set; }
        /// <summary>
        /// 物料描述
        /// </summary>
        [Description("物料描述")]
        public string MaterialName { get; set; }
        /// <summary>
        /// 工序编号
        /// </summary>
        [Description("工序编号")]
        public string WorkingProcedureCode { get; set; }
        /// <summary>
        /// 工序描述
        /// </summary>
        [Description("工序描述")]
        public string WorkingProcedureDes { get; set; }
        /// <summary>
        /// 员工号
        /// </summary>
        [Description("员工号")]
        public string EmployeeNumber { get; set; }
        /// <summary>
        /// 员工姓名
        /// </summary>
        [Description("员工姓名")]
        public string EmployeeName { get; set; }
        /// <summary>
        /// 发货单位
        /// </summary>
        [Description("发货单位")]
        public string Shippers { get; set; }
        /// <summary>
        /// 订单数量
        /// </summary>
        [Description("订单数量")]
        public decimal OrderQty { get; set; }
        /// <summary>
        /// 单位
        /// </summary>
        [Description("单位")]
        public string Unit { get; set; }
        /// <summary>
        ///  收货库存地点
        /// </summary>
        [Description("收货库存地点")]
        public string ReceivingLocation { get; set; }
        /// <summary>
        /// 报工总数
        /// </summary>
        [Description("报工数量")]
        public decimal ReportTotal { get; set; }
        /// <summary>
        /// 合格数量
        /// </summary>
        [Description("合格数量")]
        public decimal QualifiedQty { get; set; }
        /// <summary>
        /// 不合格数量
        /// </summary>
        [Description("不合格数量")]
        public decimal UnqualifiedQty { get; set; }
        /// <summary>
        /// 不合格备注
        /// </summary>
        [Description("不合格备注")]
        public string UnqualifiedRemarks { get; set; }
        /// <summary>
        /// 评估类型，01自制02外购
        /// </summary>
        [Description("评估类型")]
        public string AssessmentType { get; set; }
        /// <summary>
        /// 开始日期(装配日期)
        /// </summary>
        [Description("装配日期")]
        public DateTime? StartTime { get; set; }
        /// <summary>
        /// 线体编码
        /// </summary>
        [Description("线体编码")]
        public string ProductionLineCode { get; set; }
        /// <summary>
        /// 线体描述
        /// </summary>
        [Description("线体描述")]
        public string ProductionLineDes { get; set; }
        /// <summary>
        /// 物料组
        /// </summary>
        [Description("物料组")]
        public string MaterialGroupCode { get; set; }
        /// <summary>
        /// 物料组描述
        /// </summary>
        [Description("物料组描述")]
        public string MaterialGroupDes { get; set; }
        /// <summary>
        /// 报工类型,0:生产报工，1:物料报工
        /// </summary>
        [Description("报工类型")]
        public int? ReportType { get; set; }
        /// <summary>
        /// 是否完成
        /// </summary>
        [Description("是否完成")]
        public bool? IsCompleted { get; set; }
        /// <summary>
        /// 是否过账
        /// </summary>
        [Description("是否过账")]
        public bool? IsPosted { get; set; } = false;
        /// <summary>
        /// 过账人
        /// </summary>
        [Description("过账人")]
        public string PostUser { get; set; }
        /// <summary>
        /// 过账时间
        /// </summary>
        [Description("过账时间")]
        public DateTime? PostTime { get; set; }
        /// <summary>
        /// 手动过账时间
        /// </summary>
        [Description("手动过账时间")]
        public DateTime? ManualPostTime { get; set; }
        /// <summary> 
        /// sap生成单号
        /// </summary> 
        [Description("sap生成单号")]
        public string SapDocNum { get; set; }
    }
}
