using System;
using System.Linq;
using System.Web.Http;
using AOS.OMS.Application.Basic;
using AOS.OMS.Entity.Basic;
using HZ.Core.Http;
using HZ.WMS.WebAPI.Controllers;

namespace AOS.OMS.WebAPI.Areas.CableBasic
{
    /// <summary>
    /// 客户件号属性
    /// </summary>
    public class CustomerPartController : ApiBaseController
    {
        private CustomerPartApp _app = new CustomerPartApp();


        #region 查询所有信息

        /// <summary>
        /// 查询所有信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetAllList()
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.DbContextForOMS.Queryable<CustomerPart>().Where(t => t.IsDelete == false 
                    // && (string.IsNullOrEmpty(partNos) || partNos.Split(',').Contains(t.PartNo))
                    )?.ToList();
                var ids = itemsData.Select(t => t.Id).ToList();
                var childs = _app.DbContextForOMS.Queryable<CustomerPartParams>()
                    .Where(t => t.IsDelete == false && ids.Contains(t.Pid)).ToList();
                itemsData.ForEach(t => { t.CustomerPartParamsList = childs.Where(x => x.Pid == t.Id).ToList(); });
                result.Data = itemsData;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion
    }
}