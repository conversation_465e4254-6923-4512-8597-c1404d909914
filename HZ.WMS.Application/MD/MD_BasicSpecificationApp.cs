using System;
using System.Collections.Generic;
using AOS.Core.Utilities;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.MD.Import;

namespace HZ.WMS.Application.MD
{
    /// <summary>
    /// 基础规格表
    /// </summary>
    public class MD_BasicSpecificationApp : BaseApp<MD_BasicSpecification>
    {
        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public MD_BasicSpecificationApp() : base(){}

        #endregion
        
        #region 导入

        /// <summary>
        /// 导入
        /// </summary>
        /// <param name="excelList">接收的集合</param>
        /// <param name="opUser">当前用户</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool ImportExcelToBaseData(List<MD_BasicSpecificationImport> excelList, string opUser, out string error_message)
        {
            error_message = "";
            var flag = true;
            DateTime date = DateTime.Now;
            var entityList = new List<MD_BasicSpecification>();

            try
            {
                foreach (var item in excelList)
                {
                    if (string.IsNullOrEmpty(item.ProduceModel) && string.IsNullOrEmpty(item.SeriesModel) && string.IsNullOrEmpty(item.Model))
                    {
                        error_message = "产品型号、系列型号或型号至少填写一个";
                        return false;
                    }

                    // 使用BeanUtil.Copy进行对象映射
                    var entity = BeanUtil.Copy<MD_BasicSpecification>(item);
                    entity.Id = Guid.NewGuid().ToString();
                    entity.CUser = opUser;
                    entity.CTime = date;
                    entity.IsDelete = false;

                    entityList.Add(entity);
                }

                if (entityList.Count > 0)
                {
                    Insert(entityList);
                }

                return flag;
            }
            catch (Exception ex)
            {
                error_message = ex.Message;
                return false;
            }
        }

        #endregion
    }
}
