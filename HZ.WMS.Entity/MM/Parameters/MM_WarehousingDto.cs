using HZ.WMS.Entity.MM.ViewModel;
using HZ.WMS.Entity.SAP.View;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.MM.Parameters
{
    /// <summary>
    /// 委外入库保存接收参数的类
    /// </summary>
    public class MM_WarehousingDto
    {
        /// <summary>
        /// 报检单号
        /// </summary>
        public string InspectionNum { get; set; }

        /// <summary>
        /// 报检单行号
        /// </summary>
        public int InspectionLine { get; set; }

        /// <summary>
        /// 采购单号
        /// </summary>
        public string BaseNum { get; set; }

        /// <summary>
        /// 采购单行号
        /// </summary>
        public int BaseLine { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public decimal InspectionQty { get; set; }

        /// <summary>
        /// 物料编号
        /// </summary>
        public string ItemCode { get; set; }

        /// <summary>
        /// 物料名称
        /// </summary>
        public string ItemName { get; set; }

        /// <summary>
        /// 库位编号
        /// </summary>
        public string BinLocationCode { get; set; }

        /// <summary>
        /// 库位
        /// </summary>
        public string BinLocationName { get; set; }

        /// <summary>
        /// 区域编号
        /// </summary>
        public string RegionCode { get; set; }

        /// <summary>
        /// 区域
        /// </summary>
        public string RegionName { get; set; }

        /// <summary>
        /// 仓库编号
        /// </summary>
        public string WhsCode { get; set; }

        /// <summary>
        /// 仓库
        /// </summary>
        public string WhsName { get; set; }

        /// <summary>
        /// 销售订单号
        /// </summary>
        public string SaleNo { get; set; }

        /// <summary>
        /// 销售订单行号
        /// </summary>
        public decimal? SaleLineNo { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }

        /// <summary>
        /// 采购组件
        /// </summary>
        public List<MM_WarehousingForRESBM_View> Resbm { get; set; }

    }
}
