using AOS.WMS.Entity.Sys;
using HZ.WMS.Application;

namespace AOS.WMS.Application.Store
{
    /// <summary>
    /// 没有out_message消息的函数，意味着只在最前端做未处理异常捕捉
    /// </summary>

    public class MM_StockTodoDetailApp : BaseApp<MM_StockTodoDetail>
    {
		#region 默认构造函数
        
        /// <summary>
        /// 默认构造函数
        /// </summary>
        public MM_StockTodoDetailApp() : base()
        {
        }

        #endregion

    }
}

