using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.SD.Parameters
{
    /// <summary>
    /// 接收参数的类
    /// </summary>
    public class SD_ShippingPlanParameters
    {
        /// <summary>
        /// 单据号
        /// </summary>
        public string DocNum { get; set; }

        /// <summary>
        /// 单据号
        /// </summary>
        public string id { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }

        /// <summary>
        /// 交运日期
        /// </summary>
        public DateTime DeliveryDate { get; set; }

        /// <summary>
        /// 删除明细数组
        /// </summary>
        public string[] deleteDetailArray { get; set; }

        /// <summary>
        /// 交运计划明细
        /// </summary>
        public List<SD_ShippingPlanDetail> detaileds { get; set; }

        /// <summary>
        /// 交运计划
        /// </summary>
        public List<SD_ShippingPlan> entities { get; set; }

    }
}
