//Chloe框架
using SqlSugar;
using System;


namespace HZ.WMS.Entity.MD
{
    /// <summary>
    /// 
    /// </summary>
    [SugarTable("MD_Stock")]
    public class MD_StockView : BaseEntity
    {
        /// <summary> 
        /// 库存ID
        /// </summary> 
        public string StockID { get; set; }
        /// <summary> 
        /// 供应商编号
        /// </summary> 
        public string SupplierCode { get; set; }
        /// <summary> 
        /// 供应商名称
        /// </summary> 
        public string SupplierName { get; set; }
        /// <summary> 
        /// 条码
        /// </summary> 
        public string BarCode { get; set; }
        /// <summary> 
        /// 批次
        /// </summary> 
        public string BatchNum { get; set; }
        /// <summary> 
        /// 供应商批次
        /// </summary> 
        public string SupplierBatch { get; set; }
        /// <summary> 
        /// 生产日期
        /// </summary> 
        public DateTime? PTime { get; set; }
        /// <summary> 
        /// 物料编号
        /// </summary> 
        public string ItemCode { get; set; }
        /// <summary> 
        /// 物料名称
        /// </summary> 
        public string ItemName { get; set; }
        /// <summary> 
        /// 物料组编号
        /// </summary> 
        public string ItmsGrpCode { get; set; }
        /// <summary> 
        /// 物料组名称
        /// </summary> 
        public string ItmsGrpName { get; set; }
        /// <summary> 
        /// 库存数量
        /// </summary> 
        public decimal? Qty { get; set; }
        /// <summary> 
        /// 库存单位
        /// </summary> 
        public string Unit { get; set; }
        /// <summary> 
        /// 区域编号
        /// </summary> 
        public string RegionCode { get; set; }
        /// <summary> 
        /// 区域名称
        /// </summary> 
        public string RegionName { get; set; }
        /// <summary> 
        /// 库位编号
        /// </summary> 
        public string BinLocationCode { get; set; }
        /// <summary> 
        /// 库位名称
        /// </summary> 
        public string BinLocationName { get; set; }

        /// <summary>
        /// 仓库编号
        /// </summary>
        public string WhsCode { get; set; }
        /// <summary>
        /// 仓库名称
        /// </summary>
        public string WhsName { get; set; }

        /// <summary>
        /// 箱码
        /// </summary>
        public string BoxBarcode { get; set; }

    }
}


