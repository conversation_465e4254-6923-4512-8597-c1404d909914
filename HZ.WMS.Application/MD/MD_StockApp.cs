using SqlSugar;
using HZ.Core.Http;
using HZ.WMS.Application.MM;
using HZ.WMS.Application.Sys;
using HZ.WMS.Entity;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.MD.ViewModel;
using HZ.WMS.Entity.MM;
using HZ.WMS.Entity.PP.ViewModel;
using HZ.WMS.Entity.Sys;
using HZ.WMS.Entity.VW;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;

namespace HZ.WMS.Application.MD
{
    /// <summary>
    /// 没有out_message消息的函数，意味着只在最前端做未处理异常捕捉
    /// </summary>

    public class MD_StockApp : BaseApp<MD_Stock>
    {
        #region 初始化

        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public MD_StockApp() : base() { }

        #endregion

        private MD_WarehouseApp _warehouseApp = new MD_WarehouseApp();
        private MD_RegionApp _regionApp = new MD_RegionApp();
        private MD_BinLocationApp _binLocationApp = new MD_BinLocationApp();

        private BaseApp<BaseEntity> _baseApp = new BaseApp<BaseEntity>();
        Sys_SAPCompanyInfoApp _SAPCompanyInfoApp = new Sys_SAPCompanyInfoApp();

        //private MD_StockApp _stockApp = new MD_StockApp();

        #endregion

        #region 分页查询

        /// <summary>
        /// 分页查询
        /// </summary>
        /// <param name="page">分页</param>
        /// <param name="keyword">参数</param>
        /// <param name="currLoginUser"></param>
        /// <returns></returns>
        public List<MD_StockMain_View> GetPageToMain(Pagination page, string keyword, Sys_User currLoginUser)
        {
            if (string.IsNullOrEmpty(page.Sort))
            {
                page.Sort = "BinLocationCode desc";
            }
            else
            {
                page.Sort = page.Sort.Replace("ascending", "asc").Replace("descending", "desc");
            }
            var query = this.DbContext.Queryable<MD_StockMain_View>()
                 .Where(x =>
                     (string.IsNullOrEmpty(keyword)
                     || x.ItemCode.Contains(keyword)
                     || x.ItemName.Contains(keyword)
                     || x.RegionCode.Contains(keyword)
                     || x.RegionName.Contains(keyword)
                     || x.BinLocationCode.Contains(keyword)))//&& (currLoginUser.IsSupplier == false || x.BinLocationCode == "JS-" + currLoginUser.LoginAccount))
                     .OrderBy(page.Sort);
            //List<MD_StockMain_View> itemsData = query.ToList();
            int total = query.Count();
            page.Total = total;
            //var itemPageData = new List<MD_StockMain_View>();
            if (page.PageSize > total)
            {
                return query.ToPageList(page.PageNumber, total);
            }
            return query.ToPageList(page.PageNumber, page.PageSize);
        }

        #endregion

        #region 库位移动(A->B)

        /// <summary>
        /// 库位移动
        /// </summary>
        /// <param name="barCode"></param>
        /// <param name="fromBinLocation"></param>
        /// <param name="toBinLocation"></param>
        /// <param name="moveQty"></param>
        /// <param name="unitCode"></param>
        /// <param name="opUser"></param>
        /// <param name="error_message"></param>
        public bool StockMove(string ItemCode, string SaleNum, int SaleLine, string fromBinLocation, string toBinLocation, decimal? moveQty, string opUser, out string error_message)
        {
            bool isMoveSuccess = true;
            error_message = "";
            MD_Stock sourceStock = GetFirstEntity(x => x.ItemCode == ItemCode && x.BinLocationCode == fromBinLocation
                    && x.SaleNum == SaleNum && x.SaleLine == SaleLine);

            if (ValidateStockOut(ItemCode, fromBinLocation, SaleNum, SaleLine, moveQty, out error_message))
            {
                //出库和入库库位都要做校验
                //校验是否在盘点计划中
                //MD_Region toRegion = new MD_RegionApp().GetRegionByBinLocation(toBinLocation);
                //if (toRegion == null)
                //{
                //    throw new Exception("Common.error", new Exception("Common.NotExistBinLocation"));
                //}
                //if (CheckStockLocked(sourceStock.ItemCode, toBinLocation, toRegion.RegionCode))//入库库位
                //{
                //    throw new Exception("Common.error", new Exception("Common.StockIsLocked"));
                //    //error_message = "Common.StockIsLocked";
                //    //return false;
                //}


                MD_BinLocation destBinLocation = _binLocationApp.GetFirstEntity(x => x.BinLocationCode == toBinLocation);

                MD_Stock destStock = sourceStock.Clone() as MD_Stock;
                destStock.StockID = Guid.NewGuid().ToString();
                destStock.Qty = moveQty;
                destStock.Unit = sourceStock.Unit;
                destStock.PTime = sourceStock.PTime;
                destStock.ItmsGrpCode = sourceStock.ItmsGrpCode;
                destStock.ItmsGrpName = sourceStock.ItmsGrpName;
                destStock.SupplierBatch = sourceStock.SupplierBatch;
                destStock.BatchNum = sourceStock.BatchNum;
                destStock.WhsCode = destBinLocation.WhsCode;
                destStock.WhsName = destBinLocation.WhsName;
                destStock.RegionCode = destBinLocation.RegionCode;
                destStock.RegionName = destBinLocation.RegionName;
                destStock.BinLocationCode = destBinLocation.BinLocationCode;
                destStock.BinLocationName = destBinLocation.BinLocationName;
                destStock.CUser = opUser;
                destStock.MUser = opUser;


                try
                {
                    //DbContext.Ado.BeginTran();
                    #region 出库
                    if (sourceStock.Qty == moveQty)
                    {
                        //全部移走
                        sourceStock.Qty = 0;
                        sourceStock.DUser = opUser;
                        sourceStock.DTime = DateTime.Now;
                        Delete(sourceStock);
                    }
                    else
                    {
                        sourceStock.Qty -= moveQty;
                        sourceStock.MUser = opUser;
                        sourceStock.MTime = DateTime.Now;

                        Update(sourceStock);
                    }
                    #endregion

                    #region 入库
                    MD_Stock existStock = GetFirstEntity(x => x.ItemCode == ItemCode && x.BinLocationCode == fromBinLocation
                    && x.SaleNum == SaleNum && x.SaleLine == SaleLine);
                    if (existStock != null)
                    {
                        existStock.Qty += moveQty;
                        existStock.MUser = opUser;
                        existStock.MTime = DateTime.Now;
                        Update(existStock);
                    }
                    else
                    {
                        if (destStock.PTime == null)
                            destStock.PTime = DateTime.Now;

                        Insert(destStock);
                    }
                    #endregion

                    //DbContext.Ado.CommitTran();

                    isMoveSuccess = true;
                }
                catch (Exception ex)
                {
                    error_message = ex.Message;
                    //DbContext.Ado.RollbackTran();
                    return false;
                }

            }
            else
            {
                isMoveSuccess = false;
            }


            return isMoveSuccess;


        }


        /// <summary>
        /// 库位移动
        /// </summary>
        /// <param name="barCode"></param>
        /// <param name="fromBinLocation"></param>
        /// <param name="toBinLocation"></param>
        /// <param name="moveQty"></param>
        /// <param name="unitCode"></param>
        /// <param name="opUser"></param>
        /// <param name="error_message"></param>
        public bool StockMove(string BarCode, string fromBinLocation, string toBinLocation, decimal? moveQty, string opUser, out string error_message)
        {
            bool isMoveSuccess = true;
            error_message = "";
            MD_Stock sourceStock = GetFirstEntity(x => x.BarCode == BarCode && x.BinLocationCode == fromBinLocation);

            if (ValidateStockOut(BarCode, fromBinLocation, moveQty, out error_message, null))
            {
                //出库和入库库位都要做校验
                //校验是否在盘点计划中
                //MD_Region toRegion = new MD_RegionApp().GetRegionByBinLocation(toBinLocation);
                //if (toRegion == null)
                //{
                //    throw new Exception("Common.error", new Exception("Common.NotExistBinLocation"));
                //}
                //if (CheckStockLocked(sourceStock.ItemCode, toBinLocation, toRegion.RegionCode))//入库库位
                //{
                //    throw new Exception("Common.error", new Exception("Common.StockIsLocked"));
                //    //error_message = "Common.StockIsLocked";
                //    //return false;
                //}


                MD_BinLocation destBinLocation = _binLocationApp.GetFirstEntity(x => x.BinLocationCode == toBinLocation);

                MD_Stock destStock = sourceStock.Clone() as MD_Stock;
                destStock.StockID = Guid.NewGuid().ToString();
                destStock.Qty = moveQty;
                destStock.Unit = sourceStock.Unit;
                destStock.PTime = sourceStock.PTime;
                destStock.ItmsGrpCode = sourceStock.ItmsGrpCode;
                destStock.ItmsGrpName = sourceStock.ItmsGrpName;
                destStock.SupplierBatch = sourceStock.SupplierBatch;
                destStock.BatchNum = sourceStock.BatchNum;
                destStock.WhsCode = destBinLocation.WhsCode;
                destStock.WhsName = destBinLocation.WhsName;
                destStock.RegionCode = destBinLocation.RegionCode;
                destStock.RegionName = destBinLocation.RegionName;
                destStock.BinLocationCode = destBinLocation.BinLocationCode;
                destStock.BinLocationName = destBinLocation.BinLocationName;
                destStock.CUser = opUser;
                destStock.MUser = opUser;


                try
                {
                    //DbContext.Ado.BeginTran();
                    #region 出库
                    if (sourceStock.Qty == moveQty)
                    {
                        //全部移走
                        sourceStock.Qty = 0;
                        sourceStock.DUser = opUser;
                        sourceStock.DTime = DateTime.Now;
                        Delete(sourceStock);
                    }
                    else
                    {
                        sourceStock.Qty -= moveQty;
                        sourceStock.MUser = opUser;
                        sourceStock.MTime = DateTime.Now;

                        Update(sourceStock);
                    }
                    #endregion

                    #region 入库
                    MD_Stock existStock = GetFirstEntity(x => x.BarCode == BarCode && x.BinLocationCode == fromBinLocation);
                    if (existStock != null)
                    {
                        existStock.Qty += moveQty;
                        existStock.MUser = opUser;
                        existStock.MTime = DateTime.Now;
                        Update(existStock);
                    }
                    else
                    {
                        if (destStock.PTime == null)
                            destStock.PTime = DateTime.Now;

                        Insert(destStock);
                    }
                    #endregion

                    //DbContext.Ado.CommitTran();

                    isMoveSuccess = true;
                }
                catch (Exception ex)
                {
                    error_message = ex.Message;
                    //DbContext.Ado.RollbackTran();
                    return false;
                }

            }
            else
            {
                isMoveSuccess = false;
            }


            return isMoveSuccess;


        }

        #endregion

        #region 出库

        /// <summary>
        /// 出库-根据销售单号、销售单行号出库
        /// </summary>
        /// <param name="ItemCode">物料编号</param>
        /// <param name="binLocationCode">库位</param>
        /// <param name="SaleNum">销售订单号</param>
        /// <param name="SaleLine">销售订单行号</param>
        /// <param name="outQty">数量</param>
        /// <param name="opUser">当前用户</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool StockOut(string ItemCode, string binLocationCode, string SaleNum, int SaleLine, decimal? outQty, string opUser, out string error_message)
        {
            //bool isMoveSuccess = false;
            error_message = "";
            try
            {
                //if (ValidateStockOut(ItemCode, binLocationCode, SaleNum, SaleLine, outQty, out error_message, null))
                //{  
                //库存校验通过后
                MD_Stock sourceStock = GetFirstEntity(x => x.ItemCode == ItemCode && x.BinLocationCode == binLocationCode
                && x.SaleNum == SaleNum && x.SaleLine == SaleLine);
                if (sourceStock != null)
                {
                    if (outQty >= sourceStock.Qty)
                    {
                        sourceStock.Qty = 0;
                        sourceStock.DUser = opUser;
                        sourceStock.DTime = DateTime.Now;

                        Delete(sourceStock);
                    }
                    else
                    {
                        sourceStock.Qty -= outQty;
                        sourceStock.MUser = opUser;
                        sourceStock.MTime = DateTime.Now;

                        Update(sourceStock);
                    }
                    return true;
                }

                else
                    return false;


            }
            catch (Exception ex)
            {
                //if (tranDbContext.Session.IsInTransaction)
                //    tranDbContext.Ado.RollbackTran();
                error_message = ex.Message;
                return false;
            }
        }

        /// <summary>
        /// 出库-无任何属性(无供应商库存、无销售单号库存)
        /// 仓库模块使用
        /// </summary>
        /// <param name="ItemCode">物料编号</param>
        /// <param name="binLocationCode">库位</param>
        /// <param name="outQty">数量</param>
        /// <param name="opUser">当前用户</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool StockOut(string ItemCode, string binLocationCode, decimal? outQty, string opUser, out string error_message)
        {
            //bool isMoveSuccess = false;
            error_message = "";
            try
            {
                //if (ValidateStockOut(ItemCode, binLocationCode, outQty, out error_message, null, null))
                //{  //库存校验通过后
                    MD_Stock sourceStock = GetFirstEntity(x => x.ItemCode == ItemCode && x.BinLocationCode == binLocationCode
                    && (x.SaleNum == null || x.SaleNum == ""));
                if (sourceStock != null)
                {
                    if (outQty >= sourceStock.Qty)
                    {
                        sourceStock.Qty = 0;
                        sourceStock.DUser = opUser;
                        sourceStock.DTime = DateTime.Now;

                        Delete(sourceStock);
                    }
                    else
                    {
                        sourceStock.Qty -= outQty;
                        sourceStock.MUser = opUser;
                        sourceStock.MTime = DateTime.Now;

                        Update(sourceStock);
                    }
                    //isMoveSuccess = true;
                    return true;
                }
                else
                    return false;

            }
            catch (Exception ex)
            {
                //if (tranDbContext.Session.IsInTransaction)
                //    tranDbContext.Ado.RollbackTran();
                error_message = ex.Message;
                return false;
            }
        }

        /// <summary>
        /// 出库-根据供应商出库
        /// </summary>
        /// <param name="ItemCode">库位</param>
        /// <param name="SupplierCode">供应商编号</param>
        /// <param name="SpecialStock">特殊库存(E,O,T)</param>
        /// <param name="outQty">数量</param>
        /// <param name="opUser">当前用户</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool StockOut(string ItemCode, string SupplierCode, string SpecialStock, decimal? outQty, string opUser, out string error_message)
        {
            //bool isMoveSuccess = false;
            error_message = "";
            try
            {
                //if (ValidateStockOut(ItemCode, SupplierCode, SpecialStock, outQty, out error_message, null))
                //{  //库存校验通过后
                    MD_Stock sourceStock = GetFirstEntity(x => x.ItemCode == ItemCode && x.SupplierCode == SupplierCode
                    && x.SpecialStock == SpecialStock);
                if (sourceStock != null)
                {
                    if (outQty >= sourceStock.Qty)
                    {
                        sourceStock.Qty = 0;
                        sourceStock.DUser = opUser;
                        sourceStock.DTime = DateTime.Now;

                        Delete(sourceStock);
                    }
                    else
                    {
                        sourceStock.Qty -= outQty;
                        sourceStock.MUser = opUser;
                        sourceStock.MTime = DateTime.Now;

                        Update(sourceStock);
                    }
                    //isMoveSuccess = true;
                    return true;
                }
                else
                    return false;
            }
            catch (Exception ex)
            {
                //if (tranDbContext.Session.IsInTransaction)
                //    tranDbContext.Ado.RollbackTran();
                error_message = ex.Message;
                return false;
            }
        }

        /// <summary>
        /// 出库-根据序列号进行出库
        /// </summary>
        /// <param name="barCode"></param>
        /// <param name="binLocationCode"></param>
        /// <param name="outQty"></param>
        /// <param name="opUser"></param>
        /// <param name="error_message"></param>
        /// <returns></returns>
        public bool StockOutByBarCode(string barCode, string binLocationCode, decimal? outQty, string opUser, out string error_message)
        {
            bool isMoveSuccess = false;
            error_message = "";

            //if (ValidateStockOut(barCode, binLocationCode, outQty, out error_message, tranDbContext))
            //{  //库存校验通过后
                MD_Stock sourceStock = GetFirstEntity(x => x.BarCode == barCode && x.BinLocationCode == binLocationCode);
            if (sourceStock != null)
            {
                if (outQty >= sourceStock.Qty)
                {
                    sourceStock.Qty = 0;
                    sourceStock.DUser = opUser;
                    sourceStock.DTime = DateTime.Now;
                    Delete(sourceStock);
                }
                else
                {
                    sourceStock.Qty -= outQty;
                    sourceStock.MUser = opUser;
                    sourceStock.MTime = DateTime.Now;
                    Update(sourceStock);
                }
                isMoveSuccess = true;

            }
            return isMoveSuccess;
        }


        /// <summary>
        /// 出库-根据销售发货进行出库
        /// </summary>
        /// <param name="barCode"></param>
        /// <param name="binLocationCode"></param>
        /// <param name="outQty"></param>
        /// <param name="opUser"></param>
        /// <param name="error_message"></param>
        /// <returns></returns>
        public bool StockOutBySale(string barCode, string ItemCode, string SaleNum, int SaleLine, string binLocationCode, decimal? outQty, string opUser, out string error_message)
        {
            bool isMoveSuccess = false;
            error_message = "";

            //序列号
            if (!string.IsNullOrEmpty(barCode))
            {
                //if (ValidateStockOut(barCode, binLocationCode, outQty, out error_message, tranDbContext))
                //{  //库存校验通过后
                MD_Stock sourceStock = GetFirstEntity(x => x.BarCode == barCode && x.BinLocationCode == binLocationCode);
                if (sourceStock != null)
                {
                    if (outQty >= sourceStock.Qty)
                    {
                        sourceStock.Qty = 0;
                        sourceStock.DUser = opUser;
                        sourceStock.DTime = DateTime.Now;
                        Delete(sourceStock);
                    }
                    else
                    {
                        sourceStock.Qty -= outQty;
                        sourceStock.MUser = opUser;
                        sourceStock.MTime = DateTime.Now;
                        Update(sourceStock);
                    }
                    isMoveSuccess = true;

                }
                else
                    isMoveSuccess = false;
            }
            else//物料号
            {
                //先查物料号+销售订单号
                //if (ValidateStockOut(ItemCode, binLocationCode, SaleNum, SaleLine, outQty, out error_message, null))
                //{  //库存校验通过后
                MD_Stock sourceStock = GetFirstEntity(x => x.ItemCode == ItemCode && x.BinLocationCode == binLocationCode
                && x.SaleNum == SaleNum && x.SaleLine == SaleLine);
                if (sourceStock != null)
                {
                    if (outQty >= sourceStock.Qty)
                    {
                        sourceStock.Qty = 0;
                        sourceStock.DUser = opUser;
                        sourceStock.DTime = DateTime.Now;

                        Delete(sourceStock);
                    }
                    else
                    {
                        sourceStock.Qty -= outQty;
                        sourceStock.MUser = opUser;
                        sourceStock.MTime = DateTime.Now;

                        Update(sourceStock);
                    }
                    isMoveSuccess = true;
                }
                else
                {
                    //物料号
                    //if (ValidateStockOut(ItemCode, binLocationCode, outQty, out error_message, null, null))
                    //{  //库存校验通过后
                    sourceStock = GetFirstEntity(x => x.ItemCode == ItemCode && x.BinLocationCode == binLocationCode);
                    if (sourceStock != null)
                    {
                        if (outQty >= sourceStock.Qty)
                        {
                            sourceStock.Qty = 0;
                            sourceStock.DUser = opUser;
                            sourceStock.DTime = DateTime.Now;

                            Delete(sourceStock);
                        }
                        else
                        {
                            sourceStock.Qty -= outQty;
                            sourceStock.MUser = opUser;
                            sourceStock.MTime = DateTime.Now;

                            Update(sourceStock);
                        }
                        isMoveSuccess = true;
                    }
                    else
                        isMoveSuccess = false;
                }
            }
            return isMoveSuccess;
        }

        /// <summary>
        /// 销售单号、销售单行号出库校验
        /// </summary>
        /// <param name="barCode"></param>
        /// <param name="fromBinLocation"></param>
        /// <param name="moveQty"></param>
        /// <param name="error_message"></param>
        /// <returns></returns>
        public bool ValidateStockOut(string ItemCode, string fromBinLocation, string SaleNum, int SaleLine, decimal? moveQty, out string error_message)
        {
            bool isValidate = true;
            error_message = "";

            MD_Stock nowStock = GetFirstEntity(x => x.ItemCode == ItemCode && x.BinLocationCode == fromBinLocation && x.SaleNum == SaleNum && x.SaleLine == SaleLine);

            if (nowStock == null)
            {
                error_message = "当前物料:[" + ItemCode + "]在库位：[" + fromBinLocation + "]下没有库存！";
                return false;
            }

            //string barCodeItemCode = nowStock.ItemCode;// GetList(x => x.BarCode == barCode).ToList().FirstOrDefault()?.ItemCode;
            //MD_Region region = new MD_RegionApp().GetRegionByBinLocation(fromBinLocation);

            //if (region != null)
            //{
            //    //校验是否在盘点计划中

            //    if (CheckStockLocked(barCodeItemCode, fromBinLocation, region.RegionCode, tranDbContext))
            //    {
            //        //throw new Exception("Common.error", new Exception("Common.StockIsLocked"));
            //        error_message = "Common.StockIsLocked";
            //        return false;
            //    }

            if (moveQty == null || moveQty <= 0)
            {
                error_message = "移库数量必须大于0！";
                return false;
            }

            if (nowStock.Qty < moveQty)
            {
                error_message = "当前物料:[" + ItemCode + "]在库位：[" + fromBinLocation + "]下库存数量:[" + nowStock.Qty + "]小于移动数量，无法进行出库操作！";
                return false;
            }
            //}
            //else
            //{
            //    error_message = "Common.NotExistBinLocation";
            //    return false;
            //}
            return isValidate;
        }

        /// <summary>
        /// 无任何属性(无供应商库存、无销售单号库存)出库校验-仓库模块使用
        /// </summary>
        /// <param name="ItemCode"></param>
        /// <param name="fromBinLocation"></param>
        /// <param name="moveQty"></param>
        /// <param name="error_message"></param>
        /// <returns></returns>
        public bool ValidateStockOut(string ItemCode, string fromBinLocation, decimal? moveQty, out string error_message, string param = null)
        {
            bool isValidate = true;
            error_message = "";

            MD_Stock nowStock = GetFirstEntity(x => x.ItemCode == ItemCode && x.BinLocationCode == fromBinLocation && (x.SaleNum == null || x.SaleNum == ""));

            if (nowStock == null)
            {
                error_message = "当前物料:[" + ItemCode + "]在库位：[" + fromBinLocation + "]下没有库存！";
                return false;
            }

            //string barCodeItemCode = nowStock.ItemCode;// GetList(x => x.BarCode == barCode).ToList().FirstOrDefault()?.ItemCode;
            //MD_Region region = new MD_RegionApp().GetRegionByBinLocation(fromBinLocation);

            //if (region != null)
            //{
            //    //校验是否在盘点计划中

            //    if (CheckStockLocked(barCodeItemCode, fromBinLocation, region.RegionCode, tranDbContext))
            //    {
            //        //throw new Exception("Common.error", new Exception("Common.StockIsLocked"));
            //        error_message = "Common.StockIsLocked";
            //        return false;
            //    }

            if (moveQty == null || moveQty <= 0)
            {
                error_message = "移库数量必须大于0！";
                return false;
            }

            if (nowStock.Qty < moveQty)
            {
                error_message = "当前物料:[" + ItemCode + "]在库位：[" + fromBinLocation + "]下库存数量:[" + nowStock.Qty + "]小于移动数量，无法进行出库操作！";
                return false;
            }
            //}
            //else
            //{
            //    error_message = "Common.NotExistBinLocation";
            //    return false;
            //}
            return isValidate;
        }

        /// <summary>
        /// 供应商库存(特殊库存)出库校验
        /// </summary>
        /// <param name="ItemCode">物料编号</param>
        /// <param name="SupplierCode">供应商编号</param>
        /// <param name="SpecialStock">特殊库存</param>
        /// <param name="moveQty">数量</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool ValidateStockOut(string ItemCode, string SupplierCode, string SpecialStock, decimal? moveQty, out string error_message)
        {
            bool isValidate = true;
            error_message = "";

            MD_Stock nowStock = GetFirstEntity(x => x.ItemCode == ItemCode && x.SupplierCode == SupplierCode && x.SpecialStock == SpecialStock);

            if (nowStock == null)
            {
                error_message = "当前物料:[" + ItemCode + "]在供应商：[" + SupplierCode + "]下没有库存！";
                return false;
            }

            //string barCodeItemCode = nowStock.ItemCode;// GetList(x => x.BarCode == barCode).ToList().FirstOrDefault()?.ItemCode;
            //MD_Region region = new MD_RegionApp().GetRegionByBinLocation(fromBinLocation);

            //if (region != null)
            //{
            //    //校验是否在盘点计划中

            //    if (CheckStockLocked(barCodeItemCode, fromBinLocation, region.RegionCode, tranDbContext))
            //    {
            //        //throw new Exception("Common.error", new Exception("Common.StockIsLocked"));
            //        error_message = "Common.StockIsLocked";
            //        return false;
            //    }

            if (moveQty == null || moveQty <= 0)
            {
                error_message = "移库数量必须大于0！";
                return false;
            }

            if (nowStock.Qty < moveQty)
            {
                error_message = "当前物料:[" + ItemCode + "]在供应商：[" + SupplierCode + "]下库存数量:[" + nowStock.Qty + "]小于移动数量，无法进行出库操作！";
                return false;
            }
            //}
            //else
            //{
            //    error_message = "Common.NotExistBinLocation";
            //    return false;
            //}
            return isValidate;
        }

        /// <summary>
        /// 供应商库存(特殊库存)出库校验SAP
        /// </summary>
        /// <param name="ItemCode">物料编号</param>
        /// <param name="SupplierCode">供应商编号</param>
        /// <param name="SpecialStock">特殊库存</param>
        /// <param name="moveQty">数量</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool ValidateStockOutSAP(string ItemCode, string SupplierCode, string SpecialStock, decimal? moveQty, out string error_message)
        {
            bool isValidate = true;
            error_message = "";

            List<MD_Stock> result = _SAPCompanyInfoApp.ZFGWMS024("001", "2002", ItemCode, "" ,
            SpecialStock == null ? "O" : SpecialStock, SupplierCode == null ? "" : SupplierCode, out error_message);
            if (result == null || result.Count == 0)
            {
                error_message = "当前物料:[" + ItemCode + "]在供应商：[" + SupplierCode + "]下没有库存！";
                return false;
            }
            else
            {
                if (moveQty == null || moveQty <= 0)
                {
                    error_message = "移库数量必须大于0！";
                    return false;
                }

                if (result[0].Qty < moveQty)
                {
                    error_message = "当前物料:[" + ItemCode + "]在供应商：[" + SupplierCode + "]下库存数量:[" + result[0].Qty + "]小于移动数量["+ moveQty + "]，无法进行出库操作！";
                    return false;
                }
            }
            //}
            //else
            //{
            //    error_message = "Common.NotExistBinLocation";
            //    return false;
            //}
            return isValidate;
        }

        /// <summary>
        /// 序列号出库校验
        /// </summary>
        /// <param name="barCode"></param>
        /// <param name="fromBinLocation"></param>
        /// <param name="moveQty"></param>
        /// <param name="error_message"></param>
        /// <returns></returns>
        public bool ValidateStockOut(string BarCode, string fromBinLocation, decimal? moveQty, out string error_message)
        {
            bool isValidate = true;
            error_message = "";

            MD_Stock nowStock = GetFirstEntity(x => x.BarCode == BarCode && x.BinLocationCode == fromBinLocation);

            if (nowStock == null)
            {
                error_message = "当前条码:[" + BarCode + "]在库位：[" + fromBinLocation + "]下没有库存！";
                return false;
            }

            //string barCodeItemCode = nowStock.ItemCode;// GetList(x => x.BarCode == barCode).ToList().FirstOrDefault()?.ItemCode;
            //MD_Region region = new MD_RegionApp().GetRegionByBinLocation(fromBinLocation);

            //if (region != null)
            //{
            //    //校验是否在盘点计划中

            //    if (CheckStockLocked(barCodeItemCode, fromBinLocation, region.RegionCode, tranDbContext))
            //    {
            //        //throw new Exception("Common.error", new Exception("Common.StockIsLocked"));
            //        error_message = "Common.StockIsLocked";
            //        return false;
            //    }

            if (moveQty == null || moveQty <= 0)
            {
                error_message = "移库数量必须大于0！";
                return false;
            }

            if (nowStock.Qty < moveQty)
            {
                error_message = "当前条码:[" + BarCode + "]在库位：[" + fromBinLocation + "]下库存数量:[" + nowStock.Qty + "]小于移动数量，无法进行出库操作！";
                return false;
            }
            //}
            //else
            //{
            //    error_message = "Common.NotExistBinLocation";
            //    return false;
            //}
            return isValidate;
        }


        #endregion

        #region 评估类型-出库

        /// <summary>
        /// 评估类型出库-根据销售单号、销售单行号出库
        /// </summary>
        /// <param name="ItemCode">物料编号</param>
        /// <param name="binLocationCode">库位</param>
        /// <param name="SaleNum">销售订单号</param>
        /// <param name="SaleLine">销售订单行号</param>
        /// <param name="outQty">数量</param>
        /// <param name="AssessmentType">评估类型</param>
        /// <param name="opUser">当前用户</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool StockOutForAssmentType(string ItemCode, string binLocationCode, string SaleNum, int SaleLine, decimal? outQty, string AssessmentType, string opUser, out string error_message)
        {
            //bool isMoveSuccess = false;
            error_message = "";
            try
            {
                if (ValidateStockOutForAssmentType(ItemCode, binLocationCode, SaleNum, SaleLine, outQty, AssessmentType, out error_message))
                {  //库存校验通过后
                    MD_Stock sourceStock = GetFirstEntity(x => x.ItemCode == ItemCode && x.BinLocationCode == binLocationCode
                    && x.SaleNum == SaleNum && x.SaleLine == SaleLine && x.AssessType == AssessmentType);

                    if (outQty == sourceStock.Qty)
                    {
                        sourceStock.Qty = 0;
                        sourceStock.DUser = opUser;
                        sourceStock.DTime = DateTime.Now;

                        Delete(sourceStock);
                    }
                    else
                    {
                        sourceStock.Qty -= outQty;
                        sourceStock.MUser = opUser;
                        sourceStock.MTime = DateTime.Now;

                        Update(sourceStock);
                    }
                    return true;
                }
                else
                    return false;


            }
            catch (Exception ex)
            {
                //if (tranDbContext.Session.IsInTransaction)
                //    tranDbContext.Ado.RollbackTran();
                error_message = ex.Message;
                return false;
            }
        }

        /// <summary>
        /// 评估类型-销售单号、销售单行号出库校验
        /// </summary>
        /// <param name="ItemCode">物料编号</param>
        /// <param name="fromBinLocation">库位</param>
        /// <param name="SaleNum">销售单号</param>
        /// <param name="SaleLine">销售单行号</param>
        /// <param name="AssessmentType">评估类型</param>
        /// <param name="moveQty">数量</param>
        /// <param name="error_message">错误消息</param>
        /// <param name="tranDbContext">事务</param>
        /// <returns></returns>
        public bool ValidateStockOutForAssmentType(string ItemCode, string fromBinLocation, string SaleNum, int SaleLine, decimal? moveQty, string AssessmentType, out string error_message)
        {
            bool isValidate = true;
            error_message = "";

            MD_Stock nowStock = GetFirstEntity(x => x.ItemCode == ItemCode && x.BinLocationCode == fromBinLocation && x.SaleNum == SaleNum && x.SaleLine == SaleLine && x.AssessType == AssessmentType);

            if (nowStock == null)
            {
                error_message = "当前物料:[" + ItemCode + "],评估类型[" + AssessmentType + "],在库位：[" + fromBinLocation + "]下没有库存！";
                return false;
            }

            if (moveQty == null || moveQty <= 0)
            {
                error_message = "移库数量必须大于0！";
                return false;
            }

            if (nowStock.Qty < moveQty)
            {
                error_message = "当前物料:[" + ItemCode + "],评估类型[" + AssessmentType + "]在库位：[" + fromBinLocation + "]下库存数量:[" + nowStock.Qty + "]小于移动数量，无法进行出库操作！";
                return false;
            }
            return isValidate;
        }

        /// <summary>
        /// 评估类型出库-无任何属性(无供应商库存、无销售单号库存)
        /// 仓库模块使用
        /// </summary>
        /// <param name="ItemCode">物料编号</param>
        /// <param name="binLocationCode">库位</param>
        /// <param name="outQty">数量</param>
        /// <param name="opUser">当前用户</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool StockOutForAssmentType(string ItemCode, string binLocationCode, decimal? outQty, string AssessmentType, string opUser, out string error_message)
        {
            //bool isMoveSuccess = false;
            error_message = "";
            try
            {
                if (ValidateStockOutForAssmentType(ItemCode, binLocationCode, outQty, AssessmentType, out error_message))
                {  //库存校验通过后
                    MD_Stock sourceStock = GetFirstEntity(x => x.ItemCode == ItemCode && x.BinLocationCode == binLocationCode
                    && (x.SaleNum == null || x.SaleNum == "") && x.AssessType == AssessmentType);

                    if (outQty == sourceStock.Qty)
                    {
                        sourceStock.Qty = 0;
                        sourceStock.DUser = opUser;
                        sourceStock.DTime = DateTime.Now;

                        Delete(sourceStock);
                    }
                    else
                    {
                        sourceStock.Qty -= outQty;
                        sourceStock.MUser = opUser;
                        sourceStock.MTime = DateTime.Now;

                        Update(sourceStock);
                    }
                    //isMoveSuccess = true;
                    return true;
                }
                else
                    return false;

            }
            catch (Exception ex)
            {
                //if (tranDbContext.Session.IsInTransaction)
                //    tranDbContext.Ado.RollbackTran();
                error_message = ex.Message;
                return false;
            }
        }

        /// <summary>
        /// 评估类型-无任何属性(无供应商库存、无销售单号库存)出库校验-仓库模块使用
        /// </summary>
        /// <param name="ItemCode">物料编号</param>
        /// <param name="fromBinLocation">库位</param>
        /// <param name="moveQty">数量</param>
        /// <param name="AssessmentType">评估类型</param>
        /// <param name="error_message">错误消息</param>
        /// <param name="tranDbContext">事务</param>
        /// <returns></returns>
        public bool ValidateStockOutForAssmentType(string ItemCode, string fromBinLocation, decimal? moveQty, string AssessmentType, out string error_message)
        {
            bool isValidate = true;
            error_message = "";

            MD_Stock nowStock = GetFirstEntity(x => x.ItemCode == ItemCode && x.BinLocationCode == fromBinLocation && (x.SaleNum == null || x.SaleNum == "") && x.AssessType == AssessmentType);

            if (nowStock == null)
            {
                error_message = "当前物料:[" + ItemCode + "],评估类型[" + AssessmentType + "]在库位：[" + fromBinLocation + "]下没有库存！";
                return false;
            }
            if (moveQty == null || moveQty <= 0)
            {
                error_message = "移库数量必须大于0！";
                return false;
            }

            if (nowStock.Qty < moveQty)
            {
                error_message = "当前物料:[" + ItemCode + "],评估类型[" + AssessmentType + "]在库位：[" + fromBinLocation + "]下库存数量:[" + nowStock.Qty + "]小于移动数量，无法进行出库操作！";
                return false;
            }
            return isValidate;
        }

        #endregion

        #region 入库

        /// <summary>
        /// 入库
        /// </summary>
        /// <param name="stockInfo"></param>
        /// <param name="opUser"></param>
        /// <param name="error_message"></param>
        /// <returns></returns>
        public bool StockIn(MD_Stock stockInfo, string opUser, out string error_message)
        {
            stockInfo.BarCode = stockInfo.BarCode ?? "";
            stockInfo.BatchNum = stockInfo.BatchNum ?? "";
            stockInfo.SaleNum = stockInfo.SaleNum ?? "";
            stockInfo.SaleLine = stockInfo.SaleLine ?? 0;

            error_message = "";
            // 如果存在库位和条码一直的记录，数量累加，否则新插入一条记录
            //MD_Stock existStock = GetFirstEntity(x => x.BarCode == stockInfo.BarCode && x.BinLocationCode == stockInfo.BinLocationCode);
            MD_Stock existStock = GetFirstEntity(
                x =>
                x.BarCode == stockInfo.BarCode
                && x.BatchNum == stockInfo.BatchNum
                && x.ItemCode == stockInfo.ItemCode
                && x.BinLocationCode == stockInfo.BinLocationCode
                && x.SaleNum == stockInfo.SaleNum
                && x.SaleLine == stockInfo.SaleLine);

            //校验是否在盘点计划中
            //if (CheckStockLocked(stockInfo.ItemCode, stockInfo.BinLocationCode, stockInfo.RegionCode, tranDbContext))
            //{
            //    //throw new Exception("Common.error", new Exception("Common.StockIsLocked"));
            //    error_message = "Common.StockIsLocked";
            //    return false;
            //}

            if (existStock != null)
            {
                existStock.Qty += stockInfo.Qty;
                existStock.MUser = opUser;
                existStock.MTime = DateTime.Now;

                Update(existStock);
            }
            else
            {
                if (stockInfo.PTime == null)
                    stockInfo.PTime = DateTime.Now;

                stockInfo.IsDelete = false;
                stockInfo.CUser = opUser;
                stockInfo.CTime = DateTime.Now;

                Insert(stockInfo);
            }

            return true;
        }

        /// <summary>
        /// 供应商库存入库
        /// </summary>
        /// <param name="stockInfo"></param>
        /// <param name="opUser"></param>
        /// <param name="error_message"></param>
        /// <returns></returns>
        public bool StockInSupplier(MD_Stock stockInfo, string opUser, out string error_message)
        {
            error_message = "";
            // 如果存在库位和条码一直的记录，数量累加，否则新插入一条记录
            //MD_Stock existStock = GetFirstEntity(x => x.BarCode == stockInfo.BarCode && x.BinLocationCode == stockInfo.BinLocationCode);
            MD_Stock existStock = GetFirstEntity(x => x.ItemCode == stockInfo.ItemCode && x.SupplierCode == stockInfo.SupplierCode
                                                   && x.SpecialStock == stockInfo.SpecialStock);

            //校验是否在盘点计划中
            //if (CheckStockLocked(stockInfo.ItemCode, stockInfo.BinLocationCode, stockInfo.RegionCode, tranDbContext))
            //{
            //    //throw new Exception("Common.error", new Exception("Common.StockIsLocked"));
            //    error_message = "Common.StockIsLocked";
            //    return false;
            //}

            if (existStock != null)
            {
                existStock.Qty += stockInfo.Qty;
                existStock.MUser = opUser;
                existStock.MTime = DateTime.Now;

                Update(existStock);
            }
            else
            {
                if (stockInfo.PTime == null)
                    stockInfo.PTime = DateTime.Now;

                stockInfo.IsDelete = false;
                stockInfo.CUser = opUser;
                stockInfo.CTime = DateTime.Now;

                Insert(stockInfo);
            }

            return true;
        }

        #endregion

        #region 暂时废弃

        #region 查询批次为最早的库存记录

        /// <summary>
        /// 查询批次为最早的库存记录
        /// </summary>
        /// <param name="itemCode"></param>
        /// <returns></returns>
        public MD_Stock GetEarliestEntity(string itemCode)
        {
            var query = DbContext.Queryable<MD_Stock>().Where(x => x.ItemCode == itemCode && x.Qty > 0 && !x.IsDelete)?.OrderBy("BatchNum asc");
            return query?.ToList().FirstOrDefault();
        }

        /// <summary>
        /// 查询批次为最早的库存记录
        /// </summary>
        /// <param name="itemCode"></param>
        /// <param name="regionCode"></param>
        /// <returns></returns>
        public MD_Stock GetFIFOEntity(string itemCode, string WhsCode)
        {
            var query = DbContext.Queryable<MD_Stock>().Where(x => x.ItemCode == itemCode && x.WhsCode == WhsCode && x.Qty > 0 && !x.IsDelete)?.OrderBy("BatchNum asc");
            return query?.ToList().FirstOrDefault();
        }

        #endregion

        #region 先进先出(FIFO)

        /// <summary>
        /// 查询指定物料、指定库位，按批次(生产时间)升序排列的库存列表
        /// </summary>
        /// <param name="itemCode"></param>
        /// <param name="binLocationCode"></param>
        /// <returns></returns>
        public List<MD_Stock> GetFIFOStockList(string itemCode, string binLocationCode)
        {
            return this.GetList(x => x.ItemCode == itemCode && x.BinLocationCode == binLocationCode)?.OrderBy(x => x.BatchNum)?.ToList();
        }

        /// <summary>
        /// 校验先进先出,扫描条码符合先进先出就返回null，不符合返回应该扫描条码信息
        /// </summary>
        /// <param name="itemCode"></param>
        /// <param name="RegionCode"></param>
        /// <param name="BatchNum"></param>
        /// <param name="scanBarCode"></param>
        /// <returns></returns>
        public MD_Stock IsFIFO(string itemCode, string RegionCode, string BatchNum, string[] scanBarCode)
        {
            //查询物料是否不需要校验先进先出
            if (new Sys_DictionaryApp().GetList(x => x.TypeCode == "MD002" && x.EnumValue == itemCode).Any())
                return null;

            MD_Stock info = this.GetList(x => x.ItemCode == itemCode && x.RegionCode == RegionCode && !scanBarCode.Contains(x.BarCode))?.OrderBy(x => x.BatchNum)?.ToList().FirstOrDefault();
            //批次精确到天即可
            if (info.BatchNum.Substring(0, 7) == BatchNum.Substring(0, 7))
                return null;
            else
                return info;

        }


        #endregion

        /// <summary>
        /// 从库存列表中取出符合数量的最少条码,FIFO规则（按批次升序顺序)
        /// </summary>
        /// <param name="qty"></param>
        /// <param name="itemCode"></param>
        /// <param name="binLocationCode"></param>
        /// <returns></returns>
        public List<MD_Stock> GetMinimumStockList(decimal qty, string itemCode, string binLocationCode)
        {
            List<MD_Stock> result = null;
            var srcStockList = this.GetFIFOStockList(itemCode, binLocationCode);

            if (qty > 0 && srcStockList != null)
            {
                result = new List<MD_Stock>();
                decimal? qtyTmp = 0;
                while (srcStockList.Count > 0)
                {
                    var stock = srcStockList.ToList().FirstOrDefault();
                    qtyTmp += stock.Qty ?? 0;
                    result.Add(stock);
                    srcStockList.Remove(stock);

                    if (qtyTmp >= stock.Qty)
                    {
                        break;
                    }
                }

            }
            return result;
        }


        /// <summary>
        /// 检查库存是否已锁(有进行中的盘点计划)
        /// </summary>
        /// <param name="itemCode"></param>
        /// <param name="binLocationCode"></param>
        /// <param name="regionCode"></param>
        /// <returns></returns>
        public bool CheckStockLocked(string itemCode = null, string binLocationCode = null, string regionCode = null, SqlSugarClient tranDbContext = null)
        {
            MM_TakeStockPlanApp MM_TakeStockPlanApp = new MM_TakeStockPlanApp();
            var list = MM_TakeStockPlanApp.GetStartedTakeStockPlanList(itemCode, binLocationCode, regionCode, tranDbContext);
            return list?.Count > 0;
        }

        /// <summary>
        /// 库存移动,如果目标位置有相同条码库存,不新增库存记录,只把数量加回已存在的条码
        /// </summary>
        /// <param name="barCode"></param>
        /// <param name="fromBinLocation"></param>
        /// <param name="toBinLocation"></param>
        /// <param name="moveQty"></param>
        /// <param name="opUser"></param>
        /// <param name="error_message"></param>
        public bool StockMoveWithoutTransaction(string barCode, string fromBinLocation, string toBinLocation, decimal? moveQty, string opUser, out string error_message)
        {
            bool isMoveSuccess = true;
            error_message = "";
            //MD_Stock sourceStock = GetFirstEntity(x => x.BarCode == barCode && x.BinLocationCode == fromBinLocation);
            //MD_Stock destStock = GetFirstEntity(x => x.BarCode == barCode && x.BinLocationCode == toBinLocation);

            //if (ValidateStockOut(barCode, fromBinLocation, moveQty, out error_message))
            //{
            //    MD_BinLocation destBinLocation = _binLocationApp.GetFirstEntity(x => x.BinLocationCode == toBinLocation);
            //    MD_Region destRegion = _regionApp.GetFirstEntity(x => x.RegionCode == destBinLocation.RegionCode);

            //    try
            //    {
            //        //DbContext.Ado.BeginTran();
            //        if (sourceStock.Qty == moveQty)
            //        {
            //            //全部移走
            //            sourceStock.DUser = opUser;
            //            sourceStock.DTime = DateTime.Now;
            //            Delete(sourceStock);
            //        }
            //        else
            //        {   //部分移走
            //            sourceStock.Qty -= moveQty;
            //            sourceStock.MUser = opUser;
            //            sourceStock.MTime = DateTime.Now;

            //            Update(sourceStock);
            //        }

            //        if (destStock != null)
            //        {
            //            //目标位置有库存
            //            destStock.Qty += moveQty;
            //            destStock.MUser = opUser;
            //            destStock.MTime = DateTime.Now;

            //            Update(destStock);
            //        }
            //        else
            //        {
            //            //目标位置无库存
            //            destStock = sourceStock.Clone() as MD_Stock;
            //            destStock.StockID = Guid.NewGuid().ToString();
            //            destStock.Qty = moveQty;
            //            destStock.RegionCode = destRegion.RegionCode;
            //            destStock.RegionName = destRegion.RegionName;
            //            destStock.BinLocationCode = destBinLocation.BinLocationCode;
            //            destStock.BinLocationName = destBinLocation.BinLocationName;
            //            destStock.CUser = opUser;
            //            destStock.MUser = opUser;
            //            destStock.IsDelete = false;

            //            Insert(destStock);
            //        }
            //        //DbContext.Ado.CommitTran();

            //        isMoveSuccess = true;
            //    }
            //    catch (Exception ex)
            //    {
            //        error_message = ex.Message;
            //        //DbContext.Ado.RollbackTran();
            //        return false;
            //    }

            //}
            //else
            //{
            //    isMoveSuccess = false;
            //}


            return isMoveSuccess;
        }

        #region 获取批次号

        /// <summary>
        /// 获取批次号
        /// </summary>
        /// <param name="Tname">表名</param>
        /// <param name="Ptime">生产日期</param>
        /// <returns></returns>
        public string GetBatchNum(string Tname, DateTime? Ptime)
        {
            string NewBatchNum = "B" + Ptime?.ToString("yyMMdd");
            string sql = "SELECT BatchNum FROM " + Tname + " WHERE BatchNum LIKE '" + NewBatchNum + "%' order by BatchNum desc ";

            MD_Stock info = DbContext.Ado.SqlQuery<MD_Stock>(sql)?.ToList().FirstOrDefault();
            if (info != null)
            {
                int num = (Convert.ToInt32(info.BatchNum.Substring(7, 3)) + 1);
                NewBatchNum += num.ToString().PadLeft(3, '0');
            }
            else
                NewBatchNum += "001";

            return NewBatchNum;
        }

        #endregion

        #endregion


        /// <summary>
        /// 查询SAP库存
        /// </summary>
        /// <returns></returns>
        public List<MD_Stock> GetStockList(string ItemCode, string WhsCode, string SpecialStock, string SupplierCode)
        {
            string error_message = "";
            Sys_SAPCompanyInfoApp _SAPCompanyInfoApp = new Sys_SAPCompanyInfoApp();
            List<MD_Stock> result = _SAPCompanyInfoApp.ZFGWMS024("001", "2002", ItemCode == null ? "" : ItemCode, WhsCode == null ? "" : WhsCode,
                SpecialStock == null ? "" : SpecialStock, SupplierCode == null ? "" : SupplierCode, out error_message);

            if (SpecialStock == "E" && result.Count > 0)
            {//销售库存


                var ItemCodeS = result.Select(x => x.ItemCode).Distinct().ToArray();
                string _ItemCodeS = string.Join(",", ItemCodeS);

                var SaleNumS = result.Select(x => x.SaleNum).Distinct().ToArray();
                string _SaleNumS = string.Join(",", SaleNumS);


                List<MD_Stock> query = GetList(t => _ItemCodeS.Contains(t.ItemCode)&& _SaleNumS.Contains(t.SaleNum)).ToList();

                if (query.Count > 0)
                {

                    result.ForEach(sapData =>
                    {
                        var wmsData = query.FirstOrDefault(f => f.SaleNum == sapData.SaleNum && f.SaleLine == sapData.SaleLine);

                        sapData.WhsName = sapData.WhsName == null ? "" : sapData.WhsName;
                        sapData.AssessType = sapData.AssessType == null ? "" : sapData.AssessType;
                        sapData.SupplierCode = sapData.SupplierCode == null ? "" : sapData.SupplierCode;
                        sapData.BatchNum = sapData.BatchNum == null ? "" : sapData.BatchNum;
                        sapData.BarCode = wmsData?.BarCode == null ? "" : wmsData?.BarCode;
                    });
                    return result;
                }
                else
                    return result;
            }
            else
                return result;

        }

        /// <summary>
        /// 查询SAP库存-车间
        /// </summary>
        /// <returns></returns>
        public List<PP_ReturnMaterial_View> GetStockList_PP(string ItemCode, string WhsCode, string SpecialStock, string SupplierCode)
        {
            string error_message = "";
       
            List<PP_ReturnMaterial_View> result = _SAPCompanyInfoApp.ZFGWMS024_PP("001", "2002", ItemCode == null ? "" : ItemCode, WhsCode == null ? "" : WhsCode,
                SpecialStock == null ? "" : SpecialStock, SupplierCode == null ? "" : SupplierCode, out error_message);

        
             return result;

        }

    }


}

