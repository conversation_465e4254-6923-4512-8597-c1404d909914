/// <XRTypeInfo>
///   <AssemblyFullName>DevExpress.XtraReports.v16.1, Version=16.1.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</AssemblyFullName>
///   <AssemblyLocation>C:\Windows\Microsoft.Net\assembly\GAC_MSIL\DevExpress.XtraReports.v16.1\v4.0_16.1.5.0__b88d1754d700e49a\DevExpress.XtraReports.v16.1.dll</AssemblyLocation>
///   <TypeName>DevExpress.XtraReports.UI.XtraReport</TypeName>
///   <Localization>zh-CN</Localization>
///   <Version>16.1</Version>
///   <Resources>
///     <Resource Name="XtraReportSerialization.XtraReport">
/// zsrvvgEAAACRAAAAbFN5c3RlbS5SZXNvdXJjZXMuUmVzb3VyY2VSZWFkZXIsIG1zY29ybGliLCBWZXJzaW9uPTQuMC4wLjAsIEN1bHR1cmU9bmV1dHJhbCwgUHVibGljS2V5VG9rZW49Yjc3YTVjNTYxOTM0ZTA4OSNTeXN0ZW0uUmVzb3VyY2VzLlJ1bnRpbWVSZXNvdXJjZVNldAIAAAABAAAAAAAAAFBBRFBBRFA4hdt+AAAAAA8BAABOcwBxAGwARABhAHQAYQBTAG8AdQByAGMAZQAxAC4AUgBlAHMAdQBsAHQAUwBjAGgAZQBtAGEAUwBlAHIAaQBhAGwAaQB6AGEAYgBsAGUAAAAAAAHQBFBFUmhkR0ZUWlhRZ1RtRnRaVDBpYzNGc1JHRjBZVk52ZFhKalpURWlQanhXYVdWM0lFNWhiV1U5SWsxTlgxUmhhMlZUZEc5amExTmpZVzRpUGp4R2FXVnNaQ0JPWVcxbFBTSkVi
/// Mk5PZFcwaUlGUjVjR1U5SWxOMGNtbHVaeUlnTHo0OFJtbGxiR1FnVG1GdFpUMGlRbUYwWTJoT2RXMGlJRlI1Y0dVOUlsTjBjbWx1WnlJZ0x6NDhSbWxsYkdRZ1RtRnRaVDBpU1hSbGJVTnZaR1VpSUZSNWNHVTlJbE4wY21sdVp5SWdMejQ4Um1sbGJHUWdUbUZ0WlQwaVNYUmxiVTVoYldVaUlGUjVjR1U5SWxOMGNtbHVaeUlnTHo0OFJtbGxiR1FnVG1GdFpUMGlVM1J2WTJ0UmRIa2lJRlI1Y0dVOUlrUmxZMmx0WVd3aUlDOCtQRVpwWld4a0lFNWhiV1U5SWxOallXNVJkSGtpSUZSNWNHVTlJa1JsWTJsdFlXd2lJQzgrUEVacFpXeGtJRTVoYldVOUlrUnBabVpSZEhraUlGUjVjR1U5SWtSbFkybHRZV3dpSUM4K1BFWnBaV3hrSUU1aGJXVTlJa0pwYmt4dlkyRjBhVzl1
/// UTI5a1pTSWdWSGx3WlQwaVUzUnlhVzVuSWlBdlBqeEdhV1ZzWkNCT1lXMWxQU0pDYVc1TWIyTmhkR2x2Yms1aGJXVWlJRlI1Y0dVOUlsTjBjbWx1WnlJZ0x6NDhMMVpwWlhjK1BDOUVZWFJoVTJWMFBnPT0=</Resource>
///   </Resources>
/// </XRTypeInfo>
namespace XtraReportSerialization {
    
    public class XtraReport : DevExpress.XtraReports.UI.XtraReport {
        private DevExpress.XtraReports.UI.XRLabel label2;
        private DevExpress.XtraReports.UI.ReportFooterBand ReportFooter;
        private DevExpress.XtraReports.UI.XRLabel label3;
        private DevExpress.XtraReports.UI.XRLabel label4;
        private DevExpress.XtraReports.UI.XRLabel label1;
        private DevExpress.XtraReports.UI.XRLabel label7;
        private DevExpress.XtraReports.UI.XRLabel label6;
        private DevExpress.XtraReports.UI.XRControlStyle Title;
        private DevExpress.XtraReports.UI.XRControlStyle FieldCaption;
        private DevExpress.XtraReports.UI.XRTableCell tableCell6;
        private DevExpress.XtraReports.UI.XRTableCell tableCell15;
        private DevExpress.XtraReports.UI.XRBarCode barCode2;
        private DevExpress.XtraReports.UI.XRLabel label24;
        private DevExpress.XtraReports.UI.BottomMarginBand bottomMarginBand1;
        private DevExpress.DataAccess.Sql.SqlDataSource sqlDataSource1;
        private DevExpress.XtraReports.UI.XRTableRow tableRow1;
        private DevExpress.XtraReports.UI.XRTableCell tableCell1;
        private DevExpress.XtraReports.UI.XRTableCell tableCell2;
        private DevExpress.XtraReports.UI.XRTableCell tableCell3;
        private DevExpress.XtraReports.UI.XRTableCell tableCell4;
        private DevExpress.XtraReports.UI.XRTableCell tableCell5;
        private DevExpress.XtraReports.UI.XRTableCell tableCell18;
        private DevExpress.XtraReports.UI.XRTableCell tableCell14;
        private DevExpress.XtraReports.UI.XRTableCell tableCell16;
        private DevExpress.XtraReports.UI.XRTableCell tableCell13;
        private DevExpress.XtraReports.UI.CalculatedField ActualQty;
        private DevExpress.XtraReports.UI.XRTable table1;
        private DevExpress.XtraReports.UI.DetailBand detailBand1;
        private DevExpress.XtraReports.UI.XRTable table2;
        private DevExpress.XtraReports.UI.XRTableRow tableRow2;
        private DevExpress.XtraReports.UI.XRTableCell tableCell12;
        private DevExpress.XtraReports.UI.XRLabel label9;
        private DevExpress.XtraReports.UI.TopMarginBand topMarginBand1;
        private DevExpress.XtraReports.UI.XRLabel label10;
        private DevExpress.XtraReports.UI.ReportHeaderBand reportHeaderBand1;
        private DevExpress.XtraReports.UI.XRControlStyle DataField;
        private DevExpress.XtraReports.UI.XRControlStyle PageInfo;
        private System.Resources.ResourceManager _resources;
        private string _resourceString;
        public XtraReport() {
            this._resourceString = DevExpress.XtraReports.Serialization.XRResourceManager.GetResourceFor("XtraReportSerialization.XtraReport");
            this.InitializeComponent();
        }
        private System.Resources.ResourceManager resources {
            get {
                if (_resources == null) {
                    this._resources = new DevExpress.XtraReports.Serialization.XRResourceManager(this._resourceString);
                }
                return this._resources;
            }
        }
        private void InitializeComponent() {
            DevExpress.XtraPrinting.BarCode.QRCodeGenerator qRCodeGenerator1 = new DevExpress.XtraPrinting.BarCode.QRCodeGenerator();
            DevExpress.DataAccess.ConnectionParameters.MsSqlConnectionParameters msSqlConnectionParameters1 = new DevExpress.DataAccess.ConnectionParameters.MsSqlConnectionParameters();
            DevExpress.DataAccess.Sql.SelectQuery selectQuery1 = new DevExpress.DataAccess.Sql.SelectQuery();
            DevExpress.DataAccess.Sql.Column column1 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression1 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Table table3 = new DevExpress.DataAccess.Sql.Table();
            DevExpress.DataAccess.Sql.Column column2 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression2 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column3 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression3 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column4 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression4 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column5 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.CustomExpression customExpression1 = new DevExpress.DataAccess.Sql.CustomExpression();
            DevExpress.DataAccess.Sql.Column column6 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.CustomExpression customExpression2 = new DevExpress.DataAccess.Sql.CustomExpression();
            DevExpress.DataAccess.Sql.Column column7 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.CustomExpression customExpression3 = new DevExpress.DataAccess.Sql.CustomExpression();
            DevExpress.DataAccess.Sql.Column column8 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression5 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column9 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression6 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Group group1 = new DevExpress.DataAccess.Sql.Group();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression7 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Group group2 = new DevExpress.DataAccess.Sql.Group();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression8 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Group group3 = new DevExpress.DataAccess.Sql.Group();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression9 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Group group4 = new DevExpress.DataAccess.Sql.Group();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression10 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Group group5 = new DevExpress.DataAccess.Sql.Group();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression11 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Group group6 = new DevExpress.DataAccess.Sql.Group();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression12 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.QueryParameter queryParameter1 = new DevExpress.DataAccess.Sql.QueryParameter();
            this.label2 = new DevExpress.XtraReports.UI.XRLabel();
            this.ReportFooter = new DevExpress.XtraReports.UI.ReportFooterBand();
            this.Title = new DevExpress.XtraReports.UI.XRControlStyle();
            this.FieldCaption = new DevExpress.XtraReports.UI.XRControlStyle();
            this.tableCell6 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell15 = new DevExpress.XtraReports.UI.XRTableCell();
            this.barCode2 = new DevExpress.XtraReports.UI.XRBarCode();
            this.label24 = new DevExpress.XtraReports.UI.XRLabel();
            this.bottomMarginBand1 = new DevExpress.XtraReports.UI.BottomMarginBand();
            this.label6 = new DevExpress.XtraReports.UI.XRLabel();
            this.label3 = new DevExpress.XtraReports.UI.XRLabel();
            this.sqlDataSource1 = new DevExpress.DataAccess.Sql.SqlDataSource();
            this.tableRow1 = new DevExpress.XtraReports.UI.XRTableRow();
            this.tableCell18 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell1 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell3 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell14 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell16 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell13 = new DevExpress.XtraReports.UI.XRTableCell();
            this.ActualQty = new DevExpress.XtraReports.UI.CalculatedField();
            this.label4 = new DevExpress.XtraReports.UI.XRLabel();
            this.table1 = new DevExpress.XtraReports.UI.XRTable();
            this.tableCell4 = new DevExpress.XtraReports.UI.XRTableCell();
            this.detailBand1 = new DevExpress.XtraReports.UI.DetailBand();
            this.label9 = new DevExpress.XtraReports.UI.XRLabel();
            this.tableCell12 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell2 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableRow2 = new DevExpress.XtraReports.UI.XRTableRow();
            this.topMarginBand1 = new DevExpress.XtraReports.UI.TopMarginBand();
            this.reportHeaderBand1 = new DevExpress.XtraReports.UI.ReportHeaderBand();
            this.DataField = new DevExpress.XtraReports.UI.XRControlStyle();
            this.PageInfo = new DevExpress.XtraReports.UI.XRControlStyle();
            this.label1 = new DevExpress.XtraReports.UI.XRLabel();
            this.label7 = new DevExpress.XtraReports.UI.XRLabel();
            this.tableCell5 = new DevExpress.XtraReports.UI.XRTableCell();
            this.label10 = new DevExpress.XtraReports.UI.XRLabel();
            this.table2 = new DevExpress.XtraReports.UI.XRTable();
            ((System.ComponentModel.ISupportInitialize)(this.table1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.table2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // label2
            // 
            this.label2.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.label2.Dpi = 96F;
            this.label2.Font = new System.Drawing.Font("宋体", 10F);
            this.label2.LocationFloat = new DevExpress.Utils.PointFloat(394.4999F, 31.2F);
            this.label2.Name = "label2";
            this.label2.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 96F);
            this.label2.SizeF = new System.Drawing.SizeF(211.991F, 36.4F);
            this.label2.StylePriority.UseBorders = false;
            this.label2.StylePriority.UseFont = false;
            this.label2.StylePriority.UseTextAlignment = false;
            this.label2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleJustify;
            // 
            // ReportFooter
            // 
            this.ReportFooter.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
                        this.label3,
                        this.label4,
                        this.label1,
                        this.label2,
                        this.label7,
                        this.label6});
            this.ReportFooter.Dpi = 96F;
            this.ReportFooter.Font = new System.Drawing.Font("宋体", 10F);
            this.ReportFooter.HeightF = 77.6F;
            this.ReportFooter.Name = "ReportFooter";
            this.ReportFooter.StylePriority.UseFont = false;
            // 
            // Title
            // 
            this.Title.BackColor = System.Drawing.Color.Transparent;
            this.Title.BorderColor = System.Drawing.Color.Black;
            this.Title.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.Title.BorderWidth = 1F;
            this.Title.Font = new System.Drawing.Font("Times New Roman", 20F, System.Drawing.FontStyle.Bold);
            this.Title.ForeColor = System.Drawing.Color.Maroon;
            this.Title.Name = "Title";
            // 
            // FieldCaption
            // 
            this.FieldCaption.BackColor = System.Drawing.Color.Transparent;
            this.FieldCaption.BorderColor = System.Drawing.Color.Black;
            this.FieldCaption.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.FieldCaption.BorderWidth = 1F;
            this.FieldCaption.Font = new System.Drawing.Font("Arial", 10F, System.Drawing.FontStyle.Bold);
            this.FieldCaption.ForeColor = System.Drawing.Color.Maroon;
            this.FieldCaption.Name = "FieldCaption";
            // 
            // tableCell6
            // 
            this.tableCell6.Dpi = 96F;
            this.tableCell6.Font = new System.Drawing.Font("宋体", 9F);
            this.tableCell6.Multiline = true;
            this.tableCell6.Name = "tableCell6";
            this.tableCell6.StylePriority.UseFont = false;
            this.tableCell6.Text = "实际产品数量";
            this.tableCell6.Weight = 0.94338336530774325D;
            // 
            // tableCell15
            // 
            this.tableCell15.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "MM_TakeStockScan.ItemName")});
            this.tableCell15.Dpi = 96F;
            this.tableCell15.Name = "tableCell15";
            this.tableCell15.Text = "tableCell15";
            this.tableCell15.Weight = 1.4383441646619735D;
            // 
            // barCode2
            // 
            this.barCode2.Alignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.barCode2.AutoModule = true;
            this.barCode2.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.barCode2.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "MM_TakeStockScan.DocNum")});
            this.barCode2.Dpi = 96F;
            this.barCode2.Font = new System.Drawing.Font("宋体", 10F);
            this.barCode2.LocationFloat = new DevExpress.Utils.PointFloat(816.4315F, 13.24379F);
            this.barCode2.Module = 1.92F;
            this.barCode2.Name = "barCode2";
            this.barCode2.Padding = new DevExpress.XtraPrinting.PaddingInfo(10, 10, 0, 0, 96F);
            this.barCode2.SizeF = new System.Drawing.SizeF(114.7686F, 101.2F);
            this.barCode2.StylePriority.UseBorders = false;
            this.barCode2.StylePriority.UseFont = false;
            this.barCode2.StylePriority.UseTextAlignment = false;
            qRCodeGenerator1.CompactionMode = DevExpress.XtraPrinting.BarCode.QRCodeCompactionMode.Byte;
            this.barCode2.Symbology = qRCodeGenerator1;
            this.barCode2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // label24
            // 
            this.label24.Dpi = 96F;
            this.label24.Font = new System.Drawing.Font("宋体", 16F, System.Drawing.FontStyle.Bold);
            this.label24.LocationFloat = new DevExpress.Utils.PointFloat(313.2F, 43.2F);
            this.label24.Name = "label24";
            this.label24.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 96F);
            this.label24.SizeF = new System.Drawing.SizeF(304.8F, 26.4F);
            this.label24.StylePriority.UseFont = false;
            this.label24.StylePriority.UseTextAlignment = false;
            this.label24.Text = "仓库盘点表";
            this.label24.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // bottomMarginBand1
            // 
            this.bottomMarginBand1.Dpi = 96F;
            this.bottomMarginBand1.HeightF = 24F;
            this.bottomMarginBand1.Name = "bottomMarginBand1";
            // 
            // label6
            // 
            this.label6.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.label6.Dpi = 96F;
            this.label6.Font = new System.Drawing.Font("宋体", 10F);
            this.label6.LocationFloat = new DevExpress.Utils.PointFloat(0.7999023F, 31.2F);
            this.label6.Multiline = true;
            this.label6.Name = "label6";
            this.label6.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 96F);
            this.label6.SizeF = new System.Drawing.SizeF(85.19997F, 36.40001F);
            this.label6.StylePriority.UseBorders = false;
            this.label6.StylePriority.UseFont = false;
            this.label6.StylePriority.UseTextAlignment = false;
            this.label6.Text = "盘点日期:";
            this.label6.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // label3
            // 
            this.label3.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.label3.Dpi = 96F;
            this.label3.Font = new System.Drawing.Font("宋体", 10F);
            this.label3.LocationFloat = new DevExpress.Utils.PointFloat(606.4909F, 31.2F);
            this.label3.Multiline = true;
            this.label3.Name = "label3";
            this.label3.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 96F);
            this.label3.SizeF = new System.Drawing.SizeF(103.1999F, 36.4F);
            this.label3.StylePriority.UseBorders = false;
            this.label3.StylePriority.UseFont = false;
            this.label3.StylePriority.UseTextAlignment = false;
            this.label3.Text = "复核:";
            this.label3.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // sqlDataSource1
            // 
            this.sqlDataSource1.ConnectionName = "192.168.1.147_FaradyneWMS_Connection";
            msSqlConnectionParameters1.AuthorizationType = DevExpress.DataAccess.ConnectionParameters.MsSqlAuthorizationType.SqlServer;
            msSqlConnectionParameters1.DatabaseName = "FaradyneWMS";
            msSqlConnectionParameters1.Password = "Zkhz2018";
            msSqlConnectionParameters1.ServerName = "192.168.1.147";
            msSqlConnectionParameters1.UserName = "sa";
            this.sqlDataSource1.ConnectionParameters = msSqlConnectionParameters1;
            this.sqlDataSource1.Name = "sqlDataSource1";
            columnExpression1.ColumnName = "DocNum";
            table3.MetaSerializable = "30|30|125|620";
            table3.Name = "MM_TakeStockScan";
            columnExpression1.Table = table3;
            column1.Expression = columnExpression1;
            columnExpression2.ColumnName = "BatchNum";
            columnExpression2.Table = table3;
            column2.Expression = columnExpression2;
            columnExpression3.ColumnName = "ItemCode";
            columnExpression3.Table = table3;
            column3.Expression = columnExpression3;
            columnExpression4.ColumnName = "ItemName";
            columnExpression4.Table = table3;
            column4.Expression = columnExpression4;
            column5.Alias = "StockQty";
            customExpression1.Expression = "SUM([MM_TakeStockScan.StockQty])";
            column5.Expression = customExpression1;
            column6.Alias = "ScanQty";
            customExpression2.Expression = "SUM([MM_TakeStockScan.ScanQty])";
            column6.Expression = customExpression2;
            column7.Alias = "DiffQty";
            customExpression3.Expression = "SUM([MM_TakeStockScan.DiffQty])";
            column7.Expression = customExpression3;
            columnExpression5.ColumnName = "BinLocationCode";
            columnExpression5.Table = table3;
            column8.Expression = columnExpression5;
            columnExpression6.ColumnName = "BinLocationName";
            columnExpression6.Table = table3;
            column9.Expression = columnExpression6;
            selectQuery1.Columns.Add(column1);
            selectQuery1.Columns.Add(column2);
            selectQuery1.Columns.Add(column3);
            selectQuery1.Columns.Add(column4);
            selectQuery1.Columns.Add(column5);
            selectQuery1.Columns.Add(column6);
            selectQuery1.Columns.Add(column7);
            selectQuery1.Columns.Add(column8);
            selectQuery1.Columns.Add(column9);
            selectQuery1.FilterString = "[MM_TakeStockScan.DocNum] = ?TakeStockDocNum";
            columnExpression7.ColumnName = "DocNum";
            columnExpression7.Table = table3;
            group1.Expression = columnExpression7;
            columnExpression8.ColumnName = "BatchNum";
            columnExpression8.Table = table3;
            group2.Expression = columnExpression8;
            columnExpression9.ColumnName = "ItemCode";
            columnExpression9.Table = table3;
            group3.Expression = columnExpression9;
            columnExpression10.ColumnName = "ItemName";
            columnExpression10.Table = table3;
            group4.Expression = columnExpression10;
            columnExpression11.ColumnName = "BinLocationCode";
            columnExpression11.Table = table3;
            group5.Expression = columnExpression11;
            columnExpression12.ColumnName = "BinLocationName";
            columnExpression12.Table = table3;
            group6.Expression = columnExpression12;
            selectQuery1.Groups.Add(group1);
            selectQuery1.Groups.Add(group2);
            selectQuery1.Groups.Add(group3);
            selectQuery1.Groups.Add(group4);
            selectQuery1.Groups.Add(group5);
            selectQuery1.Groups.Add(group6);
            selectQuery1.Name = "MM_TakeStockScan";
            queryParameter1.Name = "TakeStockDocNum";
            queryParameter1.Type = typeof(string);
            queryParameter1.ValueInfo = "TS20191104002";
            selectQuery1.Parameters.Add(queryParameter1);
            selectQuery1.Tables.Add(table3);
            this.sqlDataSource1.Queries.AddRange(new DevExpress.DataAccess.Sql.SqlQuery[] {
                        selectQuery1});
            this.sqlDataSource1.ResultSchemaSerializable = resources.GetString("sqlDataSource1.ResultSchemaSerializable");
            // 
            // tableRow1
            // 
            this.tableRow1.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
                        this.tableCell1,
                        this.tableCell2,
                        this.tableCell3,
                        this.tableCell4,
                        this.tableCell5,
                        this.tableCell6});
            this.tableRow1.Dpi = 96F;
            this.tableRow1.Name = "tableRow1";
            this.tableRow1.Weight = 1D;
            // 
            // tableCell18
            // 
            this.tableCell18.Dpi = 96F;
            this.tableCell18.Name = "tableCell18";
            this.tableCell18.Weight = 0.97045150995866969D;
            // 
            // tableCell1
            // 
            this.tableCell1.CanGrow = false;
            this.tableCell1.Dpi = 96F;
            this.tableCell1.Multiline = true;
            this.tableCell1.Name = "tableCell1";
            this.tableCell1.Text = "序号";
            this.tableCell1.Weight = 0.45266973671762889D;
            // 
            // tableCell3
            // 
            this.tableCell3.Dpi = 96F;
            this.tableCell3.Multiline = true;
            this.tableCell3.Name = "tableCell3";
            this.tableCell3.Text = "产品编号";
            this.tableCell3.Weight = 1.023896857841371D;
            // 
            // tableCell14
            // 
            this.tableCell14.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "MM_TakeStockScan.ItemCode")});
            this.tableCell14.Dpi = 96F;
            this.tableCell14.Name = "tableCell14";
            this.tableCell14.Text = "tableCell14";
            this.tableCell14.Weight = 1.0518032074668984D;
            // 
            // tableCell16
            // 
            this.tableCell16.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "MM_TakeStockScan.BatchNum")});
            this.tableCell16.Dpi = 96F;
            this.tableCell16.Name = "tableCell16";
            this.tableCell16.Text = "tableCell16";
            this.tableCell16.Weight = 1.2319565072003174D;
            // 
            // tableCell13
            // 
            this.tableCell13.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "MM_TakeStockScan.BinLocationCode")});
            this.tableCell13.Dpi = 96F;
            this.tableCell13.Name = "tableCell13";
            this.tableCell13.Text = "tableCell13";
            this.tableCell13.Weight = 1.1532552348424621D;
            // 
            // ActualQty
            // 
            this.ActualQty.DataMember = "MM_TakeStockScan";
            this.ActualQty.Expression = "[Qty] * [BoxQty]";
            this.ActualQty.FieldType = DevExpress.XtraReports.UI.FieldType.String;
            this.ActualQty.Name = "ActualQty";
            // 
            // label4
            // 
            this.label4.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.label4.Dpi = 96F;
            this.label4.Font = new System.Drawing.Font("宋体", 10F);
            this.label4.LocationFloat = new DevExpress.Utils.PointFloat(709.6907F, 31.2F);
            this.label4.Name = "label4";
            this.label4.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 96F);
            this.label4.SizeF = new System.Drawing.SizeF(222.3091F, 36.4F);
            this.label4.StylePriority.UseBorders = false;
            this.label4.StylePriority.UseFont = false;
            this.label4.StylePriority.UseTextAlignment = false;
            this.label4.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleJustify;
            // 
            // table1
            // 
            this.table1.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
                        | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.table1.Dpi = 96F;
            this.table1.Font = new System.Drawing.Font("宋体", 10F);
            this.table1.LocationFloat = new DevExpress.Utils.PointFloat(0.3999977F, 10F);
            this.table1.Name = "table1";
            this.table1.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
                        this.tableRow1});
            this.table1.SizeF = new System.Drawing.SizeF(930.8F, 32F);
            this.table1.StylePriority.UseBorders = false;
            this.table1.StylePriority.UseFont = false;
            this.table1.StylePriority.UseTextAlignment = false;
            this.table1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // tableCell4
            // 
            this.tableCell4.Dpi = 96F;
            this.tableCell4.Multiline = true;
            this.tableCell4.Name = "tableCell4";
            this.tableCell4.Text = "产品描述";
            this.tableCell4.Weight = 1.4001813128932106D;
            // 
            // detailBand1
            // 
            this.detailBand1.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
                        this.table2});
            this.detailBand1.Dpi = 96F;
            this.detailBand1.Font = new System.Drawing.Font("宋体", 10F);
            this.detailBand1.HeightF = 33.6F;
            this.detailBand1.Name = "detailBand1";
            this.detailBand1.StylePriority.UseFont = false;
            // 
            // label9
            // 
            this.label9.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "MM_TakeStockScan.DocNum")});
            this.label9.Dpi = 96F;
            this.label9.Font = new System.Drawing.Font("宋体", 10F);
            this.label9.LocationFloat = new DevExpress.Utils.PointFloat(109.8F, 102.3638F);
            this.label9.Name = "label9";
            this.label9.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 96F);
            this.label9.SizeF = new System.Drawing.SizeF(152.4F, 22.08001F);
            this.label9.StylePriority.UseFont = false;
            this.label9.StylePriority.UseTextAlignment = false;
            this.label9.Text = "label8";
            this.label9.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // tableCell12
            // 
            this.tableCell12.Dpi = 96F;
            this.tableCell12.Name = "tableCell12";
            this.tableCell12.Scripts.OnBeforePrint = "tableCell12_BeforePrint";
            this.tableCell12.Weight = 0.46094027355288381D;
            // 
            // tableCell2
            // 
            this.tableCell2.CanGrow = false;
            this.tableCell2.Dpi = 96F;
            this.tableCell2.Multiline = true;
            this.tableCell2.Name = "tableCell2";
            this.tableCell2.Text = "库位号";
            this.tableCell2.Weight = 1.1226570454828766D;
            // 
            // tableRow2
            // 
            this.tableRow2.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
                        this.tableCell12,
                        this.tableCell13,
                        this.tableCell14,
                        this.tableCell15,
                        this.tableCell16,
                        this.tableCell18});
            this.tableRow2.Dpi = 96F;
            this.tableRow2.Name = "tableRow2";
            this.tableRow2.Weight = 1D;
            // 
            // topMarginBand1
            // 
            this.topMarginBand1.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
                        this.barCode2,
                        this.label9,
                        this.label10,
                        this.label24});
            this.topMarginBand1.Dpi = 96F;
            this.topMarginBand1.Font = new System.Drawing.Font("宋体", 10F);
            this.topMarginBand1.HeightF = 124.4438F;
            this.topMarginBand1.Name = "topMarginBand1";
            this.topMarginBand1.StylePriority.UseFont = false;
            // 
            // reportHeaderBand1
            // 
            this.reportHeaderBand1.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
                        this.table1});
            this.reportHeaderBand1.Dpi = 96F;
            this.reportHeaderBand1.Font = new System.Drawing.Font("宋体", 10F);
            this.reportHeaderBand1.HeightF = 42F;
            this.reportHeaderBand1.Name = "reportHeaderBand1";
            this.reportHeaderBand1.StylePriority.UseFont = false;
            // 
            // DataField
            // 
            this.DataField.BackColor = System.Drawing.Color.Transparent;
            this.DataField.BorderColor = System.Drawing.Color.Black;
            this.DataField.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.DataField.BorderWidth = 1F;
            this.DataField.Font = new System.Drawing.Font("Times New Roman", 10F);
            this.DataField.ForeColor = System.Drawing.Color.Black;
            this.DataField.Name = "DataField";
            this.DataField.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.DataField.TextAlignment = DevExpress.XtraPrinting.TextAlignment.BottomCenter;
            // 
            // PageInfo
            // 
            this.PageInfo.BackColor = System.Drawing.Color.Transparent;
            this.PageInfo.BorderColor = System.Drawing.Color.Black;
            this.PageInfo.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.PageInfo.BorderWidth = 1F;
            this.PageInfo.Font = new System.Drawing.Font("Times New Roman", 10F, System.Drawing.FontStyle.Bold);
            this.PageInfo.ForeColor = System.Drawing.Color.Black;
            this.PageInfo.Name = "PageInfo";
            // 
            // label1
            // 
            this.label1.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.label1.Dpi = 96F;
            this.label1.Font = new System.Drawing.Font("宋体", 10F);
            this.label1.LocationFloat = new DevExpress.Utils.PointFloat(254.509F, 31.2F);
            this.label1.Multiline = true;
            this.label1.Name = "label1";
            this.label1.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 96F);
            this.label1.SizeF = new System.Drawing.SizeF(139.9909F, 36.4F);
            this.label1.StylePriority.UseBorders = false;
            this.label1.StylePriority.UseFont = false;
            this.label1.StylePriority.UseTextAlignment = false;
            this.label1.Text = "盘点人:";
            this.label1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // label7
            // 
            this.label7.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.label7.Dpi = 96F;
            this.label7.Font = new System.Drawing.Font("宋体", 10F);
            this.label7.LocationFloat = new DevExpress.Utils.PointFloat(86.19985F, 31.2F);
            this.label7.Name = "label7";
            this.label7.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 96F);
            this.label7.SizeF = new System.Drawing.SizeF(168.3091F, 36.40001F);
            this.label7.StylePriority.UseBorders = false;
            this.label7.StylePriority.UseFont = false;
            this.label7.StylePriority.UseTextAlignment = false;
            this.label7.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleJustify;
            // 
            // tableCell5
            // 
            this.tableCell5.Dpi = 96F;
            this.tableCell5.Multiline = true;
            this.tableCell5.Name = "tableCell5";
            this.tableCell5.Text = "库存批次编号";
            this.tableCell5.Weight = 1.199270942538301D;
            // 
            // label10
            // 
            this.label10.Dpi = 96F;
            this.label10.Font = new System.Drawing.Font("宋体", 10F);
            this.label10.LocationFloat = new DevExpress.Utils.PointFloat(0F, 102.3638F);
            this.label10.Name = "label10";
            this.label10.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 96F);
            this.label10.SizeF = new System.Drawing.SizeF(109.6F, 22.08F);
            this.label10.StylePriority.UseFont = false;
            this.label10.StylePriority.UseTextAlignment = false;
            this.label10.Text = "盘点计划单号：";
            this.label10.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // table2
            // 
            this.table2.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.table2.Dpi = 96F;
            this.table2.Font = new System.Drawing.Font("宋体", 10F);
            this.table2.LocationFloat = new DevExpress.Utils.PointFloat(0.8F, 0F);
            this.table2.Name = "table2";
            this.table2.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
                        this.tableRow2});
            this.table2.SizeF = new System.Drawing.SizeF(930.4F, 33.6F);
            this.table2.StylePriority.UseBorders = false;
            this.table2.StylePriority.UseFont = false;
            this.table2.StylePriority.UseTextAlignment = false;
            this.table2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // XtraReport
            // 
            this.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
                        this.topMarginBand1,
                        this.detailBand1,
                        this.bottomMarginBand1,
                        this.reportHeaderBand1,
                        this.ReportFooter});
            this.CalculatedFields.AddRange(new DevExpress.XtraReports.UI.CalculatedField[] {
                        this.ActualQty});
            this.ComponentStorage.AddRange(new System.ComponentModel.IComponent[] {
                        this.sqlDataSource1});
            this.DataMember = "MM_TakeStockScan";
            this.DataSource = this.sqlDataSource1;
            this.Dpi = 96F;
            this.Landscape = true;
            this.Margins = new System.Drawing.Printing.Margins(83, 96, 124, 24);
            this.Name = "XtraReport";
            this.PageHeight = 794;
            this.PageWidth = 1123;
            this.PaperKind = System.Drawing.Printing.PaperKind.A4;
            this.ReportUnit = DevExpress.XtraReports.UI.ReportUnit.Pixels;
            this.ScriptsSource = "\r\nprivate void tableCell12_BeforePrint(object sender, System.Drawing.Printing.Pri" +
                "ntEventArgs e) {\r\n    ((XRTableCell)sender).Text = string.Format(\"{0}\", this.Cur" +
                "rentRowIndex + 1);\r\n\r\n}\r\n";
            this.SnapGridSize = 12.5F;
            this.StyleSheet.AddRange(new DevExpress.XtraReports.UI.XRControlStyle[] {
                        this.Title,
                        this.FieldCaption,
                        this.PageInfo,
                        this.DataField});
            this.Version = "16.1";
            ((System.ComponentModel.ISupportInitialize)(this.table1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.table2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();
        }
    }
}
