using System;
using System.ComponentModel;
using HZ.WMS.Entity;
using SqlSugar;

namespace AOS.WMS.Entity.Sys
{
    /// <summary>
    /// 供应商库存
    /// </summary>
    [SugarTable("MM_StockTodoDetail")]
    public class MM_StockTodoDetail : BaseEntity
    {
        
        /// <summary>
        /// ID
        /// </summary>
        [Description("ID")]
        [SugarColumn(IsPrimaryKey = true)]
        public string ID { get; set; }
        
        /// <summary>
        /// PID
        /// </summary>
        [Description("PID")]
        public string PID { get; set; }

        /// <summary>
        /// 物料编码
        /// </summary>
        [Description("物料编码")]
        public string ItemCode { get; set; }
        
        /// <summary>
        /// 物料描述
        /// </summary>
        [Description("物料描述")]
        public string ItemName { get; set; }
        
        /// <summary>
        /// 物料类型
        /// </summary>
        [Description("物料类型")]
        public string ItemType { get; set; }

        /// <summary>
        /// 操作数量
        /// </summary>
        [Description("操作数量")]
        public decimal OperateNo { get; set; }

        /// <summary>
        /// 操作人
        /// </summary> 
        [Description("操作人")]
        public string Operator { get; set; }

        /// <summary>
        /// 操作类型
        /// </summary>
        [Description("操作类型")]
        public int OperateType { get; set; }

        /// <summary>
        /// 操作时间
        /// </summary>
        [Description("操作时间")]
        public DateTime OperateTime { get; set; }
        
    }
}