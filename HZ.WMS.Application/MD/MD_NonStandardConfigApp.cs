using System;
using System.Collections.Generic;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.MD.Import;
using AOS.Core.Utilities;

namespace HZ.WMS.Application.MD
{
    /// <summary>
    /// 非标参数配置
    /// </summary>
    public class MD_NonStandardConfigApp : BaseApp<MD_NonStandardConfig>
    {
        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public MD_NonStandardConfigApp() : base() { }

        #endregion

        #region 导入

        /// <summary>
        /// 导入
        /// </summary>
        /// <param name="excelList">接收的集合</param>
        /// <param name="opUser">当前用户</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool ImportExcelToBaseData(List<MD_NonStandardConfigImport> excelList, string opUser, out string error_message)
        {
            error_message = "";
            var flag = true;
            DateTime date = DateTime.Now;
            var entityList = new List<MD_NonStandardConfig>();

            try
            {
                foreach (var item in excelList)
                {
                    // 验证必填字段
                    if (string.IsNullOrEmpty(item.AcceptNo))
                    {
                        error_message = "接受编号不能为空";
                        return false;
                    }

                    if (string.IsNullOrEmpty(item.SeriesModel))
                    {
                        error_message = "系列型号不能为空";
                        return false;
                    }

                    // 检查是否已存在相同的接受编号
                    var existEntity = GetFirstEntity(x => x.AcceptNo == item.AcceptNo);
                    if (existEntity != null)
                    {
                        error_message = $"接受编号 {item.AcceptNo} 已存在";
                        return false;
                    }

                    // 使用BeanUtil.Copy进行对象映射
                    var entity = BeanUtil.Copy<MD_NonStandardConfig>(item);
                    entity.Id = Guid.NewGuid().ToString();
                    entity.CUser = opUser;
                    entity.CTime = date;
                    entity.IsDelete = false;

                    entityList.Add(entity);
                }

                if (entityList.Count > 0)
                {
                    Insert(entityList);
                }

                return flag;
            }
            catch (Exception ex)
            {
                error_message = ex.Message;
                return false;
            }
        }

        #endregion

        #region 业务方法

        /// <summary>
        /// 根据接受编号获取配置信息
        /// </summary>
        /// <param name="AcceptNo">接受编号</param>
        /// <returns></returns>
        public MD_NonStandardConfig GetByAcceptNo(string AcceptNo)
        {
            if (string.IsNullOrEmpty(AcceptNo))
                return null;

            return GetFirstEntity(x => x.AcceptNo == AcceptNo);
        }

        /// <summary>
        /// 根据系列型号获取配置信息列表
        /// </summary>
        /// <param name="seriesModel">系列型号</param>
        /// <returns></returns>
        public List<MD_NonStandardConfig> GetBySeriesModel(string seriesModel)
        {
            if (string.IsNullOrEmpty(seriesModel))
                return new List<MD_NonStandardConfig>();

            return GetList(x => x.SeriesModel.Contains(seriesModel)).ToList();
        }

        /// <summary>
        /// 检查接受编号是否已存在
        /// </summary>
        /// <param name="AcceptNo">接受编号</param>
        /// <param name="excludeId">排除的ID（用于编辑时检查）</param>
        /// <returns></returns>
        public bool IsAcceptNoExists(string AcceptNo, string excludeId = null)
        {
            if (string.IsNullOrEmpty(AcceptNo))
                return false;

            if (string.IsNullOrEmpty(excludeId))
            {
                return GetFirstEntity(x => x.AcceptNo == AcceptNo) != null;
            }
            else
            {
                return GetFirstEntity(x => x.AcceptNo == AcceptNo && x.Id != excludeId) != null;
            }
        }

        #endregion
    }
}
