using SqlSugar;
using System;
using System.Collections.Generic;

using HZ.Core.Security;
using HZ.WMS.Entity.PO;
using HZ.WMS.Entity.MD;
using HZ.Core.Http;
using HZ.WMS.Application.MD;
using HZ.WMS.Application.Sys;
using System.Linq;
using HZ.WMS.Entity.Sys;
using HZ.WMS.Application.QM;
using HZ.WMS.Entity.QM;
using HZ.WMS.Entity.SAP.View;
using System.Linq.Expressions;
using HZ.WMS.Entity.SAP;
using HZ.WMS.Entity.PO.Parameters;
using HZ.WMS.Application.SAP;
using HZ.WMS.Entity;

namespace HZ.WMS.Application.PO
{
    /// <summary>
    /// 采购退货
    /// </summary>
    public class PO_ReturnScanApp : BaseApp<PO_ReturnScan>
    {
        #region 默认构造函数

        /// <summary>
        /// 采购退货明细
        /// </summary>
        public class PO_ReturnScanDetailedApp : BaseApp<PO_ReturnScanDetailed> { }

        /// <summary>
        /// 采购退货明细
        /// </summary>
        public PO_ReturnScanDetailedApp _detaidApp = new PO_ReturnScanDetailedApp();

        BaseApp<BaseEntity> _baseApp = new BaseApp<BaseEntity>();

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public PO_ReturnScanApp() : base(){}

        #endregion

        #region 保存

        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="Parameters">参数类</param>
        /// <param name="user">用户</param>
        /// <param name="error_message">错误信息</param>
        /// <param name="type">类型 1:提交失败 2：过账失败</param>
        /// <returns></returns>
        public bool Save(PO_ReturnScanParameters Parameters, string user, out string error_message, out string type)
        {
            error_message = "";
            type = "";
            MD_StockApp _stockApp = new MD_StockApp();
            try
            {
                #region 库存校验

                //foreach (var x in Parameters.detailed)
                //{
                //    //如果信息不存在评估类型，走正常的库存逻辑
                //    if (string.IsNullOrEmpty(string.IsNullOrEmpty(x.EvaluationType) ? "" : x.EvaluationType.Trim()))
                //    {
                //        string stockMsg = "";
                //        //库存校验
                //        //bool result = _stockApp.ValidateStockOut(x.ItemCode, Parameters.BinLocationCode, x.SalesOrderNum, Convert.ToInt32(x.SalesOrderLine), x.ReturnScanQty, out message, null);
                //        bool result = _stockApp.ValidateStockOut(x.ItemCode, x.BinLocationCode, x.ReturnScanQty, out stockMsg, null, null);
                //        if (!result)
                //        {
                //            error_message = stockMsg;
                //            type = "1";
                //            return false;
                //        }
                //    }
                //    else//如果存在评估类型，走评估类型的库存逻辑
                //    {
                //        string stockMsg = "";
                //        //库存校验
                //        bool result = _stockApp.ValidateStockOutForAssmentType(x.ItemCode, x.BinLocationCode, x.ReturnScanQty,x.EvaluationType, out stockMsg, null);
                //        if (!result)
                //        {
                //            error_message = stockMsg;
                //            type = "1";
                //            return false;
                //        }
                //    }
                //}

                #endregion

                #region 属性初始化

                DateTime time = DateTime.Now;
                var poscanList = new List<PO_ReturnScan>();

                #endregion

                #region 逻辑处理

                ////多条数据后根据供应商编号、供应商名称进行分组
                var mainInfoList = Parameters.detailed.GroupBy(g => new
                {
                    g.SupplierCode,
                    g.SupplierName
                }).Select(q => new
                {
                    SupplierCode = q.Key.SupplierCode,
                    SupplierName = q.Key.SupplierName
                });

                var detail=new List<PO_ReturnScanDetailed>();
                //按供应商编号、供应商名称分组
                foreach (var mainInfo in mainInfoList)
                {
                    if (poscanList.Count>0)
                    {
                        Parameters.DocNum=_baseApp.GetNewDocNum(DocType.PO, DocFixedNumDef.PO_ReturnScan);
                    }
                    //主表信息
                    poscanList.Add(new PO_ReturnScan
                    {
                        ManualPostTime = Parameters.ManualPostTime,
                        DocNum = Parameters.DocNum,
                        IsPosted = false,
                        IsDelete = false,
                        CUser = user,
                        CTime = time,
                        Remark = Parameters.Remark,
                        SupplierCode = mainInfo.SupplierCode,
                        SupplierName = mainInfo.SupplierName
                    });

                    var y=Parameters.detailed.Where(x=>x.SupplierCode==mainInfo.SupplierCode).ToList();
                    //明细表信息
                    foreach (PO_ReturnScanDetailed x in y)
                    {
                        x.CompanyCode = "001";
                        // x.FactoryCode = "2002";
                        x.IsPosted = false;
                        x.DocNum = Parameters.DocNum;
                        x.IsDelete = false;
                        x.CUser = user;
                        x.CTime = time;

                        //x.WhsCode = Parameters.WhsCode;
                        //x.WhsName = Parameters.WhsName;
                        //x.RegionCode = Parameters.RegionCode;
                        //x.RegionName = Parameters.RegionName;
                        //x.BinLocationCode = Parameters.BinLocationCode;
                        //x.BinLocationName = Parameters.BinLocationName;
                    }
                    detail.AddRange(y);
                }

                #endregion

                #region 事务操作

                DbContext.Ado.BeginTran();
                _detaidApp.DbContext = this.DbContext;
                //_stockApp.DbContext = this.DbContext;

                //批量插入
                Insert(poscanList);

                _detaidApp.Insert(detail);

                DbContext.Ado.CommitTran();

                #endregion

                #region 是否自动过账

                Sys_SwithConfigApp _switchApp = new Sys_SwithConfigApp();
                //是否自动过账判定
                if (_switchApp.IsPoReturnScanAutoPost)
                {
                    string postMsg = "";
                    bool bpost=DoPost(poscanList, detail, user, out postMsg);
                    if (!bpost)
                    {
                        error_message = "提交成功"+postMsg;
                        type = "2";
                        return false;
                    }
                    else
                    {
                        error_message = "提交并" + postMsg;
                    }
                }

                #endregion

                if (string.IsNullOrEmpty(error_message))
                    error_message = "提交成功";
                return true;
            }
            catch (Exception ex)
            {
                if(DbContext.Ado.IsAnyTran())
                    DbContext.Ado.RollbackTran();

                error_message = ex.Message;
                type = "1";
                return false;
            }
        }

        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="Parameters">参数类</param>
        /// <param name="user">用户</param>
        /// <param name="error_message">错误信息</param>
        /// <param name="type">类型 1:提交失败 2：过账失败</param>
        /// <returns></returns>
        public bool Update(PO_ReturnScanParameters Parameters, string user, out string error_message, out string type)
        {
            error_message = "";
            type = "";
            MD_StockApp _stockApp = new MD_StockApp();
            try
            {
                #region 库存校验

                //foreach (var x in Parameters.detailed)
                //{
                //    //如果不存在评估类型，走正常的库存逻辑
                //    if (string.IsNullOrEmpty(string.IsNullOrEmpty(x.EvaluationType) ? "" : x.EvaluationType.Trim()))
                //    {
                //        string stockMsg = "";
                //        //库存校验
                //        //bool result = _stockApp.ValidateStockOut(x.ItemCode, Parameters.BinLocationCode, x.SalesOrderNum, Convert.ToInt32(x.SalesOrderLine), x.ReturnScanQty, out message, null);
                //        bool result = _stockApp.ValidateStockOut(x.ItemCode, x.BinLocationCode, x.ReturnScanQty, out stockMsg, null, null);
                //        if (!result)
                //        {
                //            error_message = stockMsg;
                //            type = "1";
                //            return false;
                //        }
                //    }
                //    else//如果存在评估类型，走评估类型的库存逻辑
                //    {
                //        string stockMsg = "";
                //        //库存校验
                //        bool result = _stockApp.ValidateStockOutForAssmentType(x.ItemCode, x.BinLocationCode, x.ReturnScanQty, x.EvaluationType, out stockMsg, null);
                //        if (!result)
                //        {
                //            error_message = stockMsg;
                //            type = "1";
                //            return false;
                //        }
                //    }
                //}

                #endregion

                #region 属性初始化

                DateTime time = DateTime.Now;
                var AddmsDetailList = new List<PO_ReturnScanDetailed>();
                var UpdatemsDetailList = new List<PO_ReturnScanDetailed>();

                #endregion

                #region 逻辑处理

                //根据主键ID查询采购退料明细信息
                var entities = _detaidApp.GetListByKeys(Parameters.deletedetail);

                //根据申请单号查询采购退料主信息
                var query = GetList(x => x.DocNum == Parameters.DocNum)?.ToList();

               
                //插入子表信息 podetailed
                foreach (var rsdetail in Parameters.detailed)
                {
                    if (string.IsNullOrEmpty(rsdetail.ReturnScanDetailedID))
                    {
                        rsdetail.CompanyCode = "001";
                        // rsdetail.FactoryCode = "2002";
                        rsdetail.IsPosted = false;
                        rsdetail.DocNum = Parameters.DocNum;
                        rsdetail.IsDelete = false;
                        rsdetail.CUser = user;
                        rsdetail.CTime = time;
                        //rsdetail.WhsCode = Parameters.WhsCode;
                        //rsdetail.WhsName = Parameters.WhsName;
                        //rsdetail.RegionCode = Parameters.RegionCode;
                        //rsdetail.RegionName = Parameters.RegionName;
                        //rsdetail.BinLocationCode = Parameters.BinLocationCode;
                        //rsdetail.BinLocationName = Parameters.BinLocationName;
                        AddmsDetailList.Add(rsdetail);
                    }
                    else
                    {
                        rsdetail.MUser = user;
                        rsdetail.MTime = time;
                        //rsdetail.WhsCode = Parameters.WhsCode;
                        //rsdetail.WhsName = Parameters.WhsName;
                        //rsdetail.RegionCode = Parameters.RegionCode;
                        //rsdetail.RegionName = Parameters.RegionName;
                        //rsdetail.BinLocationCode = Parameters.BinLocationCode;
                        //rsdetail.BinLocationName = Parameters.BinLocationName;
                        UpdatemsDetailList.Add(rsdetail);
                    }
                    //listmsdetail.Add(podetailed);
                }

                #endregion

                #region 事务操作

                DbContext.Ado.BeginTran();
                _detaidApp.DbContext = DbContext;

                //删除采购退料明细信息
                if (entities.Count > 0)
                {
                    _detaidApp.Delete(entities, user);
                }
                //更新采购退料信息
                if (query != null && query.Count>0)
                {
                    query[0].Remark = Parameters.Remark;
                    query[0].MUser = user;
                    query[0].MTime = time;
                    Update(query);
                }
                //插入采购退料明细信息
                //this.Update(listms);
                if (AddmsDetailList.Count > 0)
                {
                    _detaidApp.Insert(AddmsDetailList);
                }
                //更新采购退料明细信息
                if (UpdatemsDetailList.Count > 0)
                {
                    _detaidApp.Update(UpdatemsDetailList);
                }

                DbContext.Ado.CommitTran();

                #endregion

                #region 是否自动过账

                Sys_SwithConfigApp _switchApp = new Sys_SwithConfigApp();
                //是否自动过账判定
                if (_switchApp.IsPoReturnScanAutoPost)
                {
                    string postMsg = "";
                    bool bpost = DoPost(query, null, user, out postMsg);
                    if (!bpost)
                    {
                        error_message = "更新成功"+postMsg;
                        type = "2";
                        return false;
                    }
                    else
                    {
                        error_message = "更新并" + postMsg;
                    }
                }

                #endregion

                if (string.IsNullOrEmpty(error_message))
                    error_message = "更新成功";
                return true;
            }
            catch (Exception ex)
            {
                if(DbContext.Ado.IsAnyTran())
                    DbContext.Ado.RollbackTran();

                error_message = ex.Message;
                type = "1";
                return false;
            }
        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="DocNums">单号数组</param>
        /// <param name="opUser">当前用户</param>
        /// <param name="error_message">错误消息</param>
        public bool Delete(string[] DocNums, string opUser, out string error_message)
        {
            bool bDeleted = true;
            List<PO_ReturnScan> mslist = new List<PO_ReturnScan>();
            List<PO_ReturnScanDetailed> msDetailList = new List<PO_ReturnScanDetailed>();

            foreach (string key in DocNums)
            {
                var item = GetList(x => (string.IsNullOrEmpty(key) || x.DocNum.Contains(key))).ToList();
                if (item != null)
                {
                    mslist.AddRange(item);
                }
                var itemdetail = _detaidApp.GetList(x => (string.IsNullOrEmpty(key) || x.DocNum.Contains(key))).ToList();
                if (itemdetail != null)
                {
                    msDetailList.AddRange(itemdetail);
                }
            }
            //List<PO_ReturnScan> entities = GetListByKeys(ids);
            if (CheckDelete(mslist, out error_message))
            {
                try
                {
                    DbContext.Ado.BeginTran();
                    _detaidApp.DbContext = DbContext;

                    //删除主表信息
                    Delete(mslist,opUser);

                    //删除明细表信息
                    _detaidApp.Delete(msDetailList, opUser);

                    DbContext.Ado.CommitTran();
                }
                catch (Exception ex)
                {
                    bDeleted = false;
                    error_message = ex.Message;
                    if(DbContext.Ado.IsAnyTran())
                        DbContext.Ado.RollbackTran();
                }
            }
            else
            {
                bDeleted = false;
            }
            return bDeleted;

        }

        /// <summary>
        /// 删除校验
        /// </summary>
        /// <param name="entities">集合</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        private bool CheckDelete(List<PO_ReturnScan> entities, out string error_message)
        {
            bool isPass = true;
            error_message = "";
            foreach (var po in entities)
            {
                if (po.IsPosted == true)
                {
                    error_message = "ui.Message.PODeletePostingWarning"; // "已过账数据不允许删除!";
                    isPass = false;
                    break;
                }

                // 入库校验
                //if(_stockApp.CheckStockLocked(po.ItemCode, po.OutBinLocationCode, po.OutRegionCode))
                //{
                //    //throw new Exception("Common.error", new Exception("Common.StockIsLocked"));
                //    error_message = "Common.StockIsLocked"; //库存锁定，不允许出入库
                //    return false;
                //}

            }

            return isPass;
        }

        #endregion

        #region 过账

        /// <summary>
        /// 过账
        /// </summary>
        /// <param name="entities">主信息</param>
        /// <param name="entitieDetails">明细信息 如果明细信息不为空则是自动过账，为空则是PC界面过账</param>
        /// <param name="opUser">当前过账用户</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool DoPost(List<PO_ReturnScan> entities, List<PO_ReturnScanDetailed> entitieDetails, string opUser, out string error_message)
        {
            error_message = "";

            Sys_SAPCompanyInfoApp _SAPCompanyInfoApp = new Sys_SAPCompanyInfoApp();
            MD_StockApp _stockApp = new MD_StockApp();
            try
            {
                ////多条数据后根据单号进行分组
                var mainInfoList = entities.GroupBy(g => new
                {
                    g.DocNum
                }).Select(q => new
                {
                    DocNum = q.Key.DocNum
                });

                //按订单号分组
                foreach (var mainInfo in mainInfoList)
                {
                    var list = new List<ZFGWMS006>();
                    //int Line = 0;
                    var scandetaList = new List<PO_ReturnScanDetailed>();
                    if (entitieDetails != null && entitieDetails.Count > 0)//手持端添加信息后自动过账，不需要在查询一次
                    {
                        //查询明细信息
                        scandetaList = entitieDetails.Where(x => x.DocNum == mainInfo.DocNum && x.IsPosted == false)?.ToList();
                    }
                    else//PC端过账
                    {
                        scandetaList = _detaidApp.GetList(x => x.DocNum == mainInfo.DocNum && x.IsPosted == false)?.ToList();
                    }

                    foreach (var x in scandetaList)
                    {
                        //x.Line = Line += 1;
                        list.Add(new ZFGWMS006
                        {
                            EBELN = x.BaseNum,
                            EBELP = x.BaseLine,
                            MATNR = x.ItemCode,
                            WERKS = x.FactoryCode ?? "2002",
                            BWART = "101",
                            MENGE = x.ReturnScanQty,
                            MEINS = x.Unit,
                            LGORT = x.WhsCode,
                            KDAUF = x.SalesOrderNum,
                            KDPOS = x.SalesOrderLine,
                            SOBKZ = string.IsNullOrEmpty(x.SalesOrderNum) ? "" : "E",
                            BWTAR = string.IsNullOrEmpty(x.EvaluationType) ? "" : x.EvaluationType,
                            SGTXT = ""
                        });
                    }

                    bool ispost = false;
                    //DateTime postdate = DateTime.Now;//过账时间 后续需要改逻辑

                    DateTime date = DateTime.Now;
                    //查询Sap账期时间
                    DateTime ManualPostTime = new SAPApp().GetSAPpostTime(Convert.ToDateTime(entities[0].ManualPostTime));
                    var saplist = _SAPCompanyInfoApp.ZFGWMS006("001", mainInfo.DocNum, ManualPostTime, list, out ispost, out error_message);
                    if (saplist != null && saplist.Count > 0)
                    {
                        //待完善，完善内容 事务，库存缺少信息。
                        //List<PO_ReturnScanDetailed> querydetails = new List<PO_ReturnScanDetailed>();
                        foreach (var sap in saplist)
                        {
                            var querydetail = scandetaList.Where(x => x.DocNum == mainInfo.DocNum && x.BaseNum == sap.basenum && x.BaseLine == sap.baseline).ToList().FirstOrDefault();
                            if (querydetail != null)
                            {
                                querydetail.IsPosted = true;
                                querydetail.PostUser = opUser;
                                querydetail.PostTime = date;
                                querydetail.MUser = opUser;
                                querydetail.MTime = date;
                                querydetail.SapDocNum = sap.sapDocNum;
                                querydetail.SapLine = sap.sapline;

                                if (_detaidApp.Update(querydetail) > 0)//更新成功后，更新库存
                                {
                                    //评估类型为空的话，走正常库存逻辑
                                    if (string.IsNullOrEmpty(string.IsNullOrEmpty(querydetail.EvaluationType) ? "" : querydetail.EvaluationType.Trim()))
                                    {
                                        string errorMsg = string.Empty;
                                        if (!_stockApp.StockOut(querydetail.ItemCode, querydetail.BinLocationCode, querydetail.SalesOrderNum, Convert.ToInt32(querydetail.SalesOrderLine), querydetail.ReturnScanQty, opUser, out errorMsg))
                                        {
                                            error_message = errorMsg;
                                            //return false;
                                        }
                                    }
                                    else//评估类型不为空的话，走评估类型的库存逻辑
                                    {
                                        string errorMsg = string.Empty;
                                        if (!_stockApp.StockOutForAssmentType(querydetail.ItemCode, querydetail.BinLocationCode, querydetail.SalesOrderNum, Convert.ToInt32(querydetail.SalesOrderLine), querydetail.ReturnScanQty,querydetail.EvaluationType, opUser, out errorMsg))
                                        {
                                            error_message = errorMsg;
                                            //return false;
                                        }
                                    }
                                   
                                }

                                //querydetails.Add(querydetail);
                            }
                        }

                        var query = entities.Where(x => x.DocNum == mainInfo.DocNum).ToList().FirstOrDefault();
                        if (query != null)
                        {
                            query.IsPosted = true;
                            query.PostUser = opUser;
                            query.PostTime = date;
                            query.MUser = opUser;
                            query.MTime = date;
                            query.ManualPostTime = ManualPostTime;
                            query.SAPmark = "S";
                        }

                        Update(query);
                        //_detaidApp.Update(querydetails);
                    }
                    else
                    {
                        var query = entities.Where(x => x.DocNum == mainInfo.DocNum).ToList().FirstOrDefault();
                        if (query != null)
                        {
                            query.SAPmark = "E";
                            query.SAPmessage = error_message;
                        }
                        Update(query);

                        error_message = "单号：" + mainInfo.DocNum + "，过账失败，失败原因：" + error_message;
                        return false;
                    }
                }
                if (string.IsNullOrEmpty(error_message))
                    error_message = "过账成功";
                return true;
            }
            catch (Exception ex)
            {
                error_message = ex.Message;
                return false;
            }
        }


        #endregion

    }
}

