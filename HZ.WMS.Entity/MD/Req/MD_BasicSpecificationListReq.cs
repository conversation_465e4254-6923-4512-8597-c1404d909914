namespace HZ.WMS.Entity.MD
{
    /// <summary>
    /// 基础规格表查询请求
    /// </summary>
    public class MD_BasicSpecificationListReq
    {
        /// <summary>
        /// 系列型号
        /// </summary>
        public string SeriesModel { get; set; }

        /// <summary>
        /// 能效备案型号
        /// </summary>
        public string EnergyEfficiencyModel { get; set; }

        /// <summary>
        /// Forvorda件号
        /// </summary>
        public string ForvordaPartNo { get; set; }

        /// <summary>
        /// SAP件号
        /// </summary>
        public string SAPPartNo { get; set; }

        /// <summary>
        /// 型号
        /// </summary>
        public string Model { get; set; }

        /// <summary>
        /// SAP产品型号
        /// </summary>
        public string SAPProductModel { get; set; }

        /// <summary>
        /// 额定功率
        /// </summary>
        public string RatedPower { get; set; }

        /// <summary>
        /// 装箱尺寸
        /// </summary>
        public string PackingSize { get; set; }

        /// <summary>
        /// 装箱净重
        /// </summary>
        public string PackingNetWeight { get; set; }

        /// <summary>
        /// 装箱毛重
        /// </summary>
        public string PackingGrossWeight { get; set; }
    }
}
