using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;
using AOS.OMS.Application.Salesa;
using AOS.OMS.Entity.Sale;
using AOS.WMS.Entity.CableProduce;
using HZ.Core.Http;
using HZ.WMS.WebAPI.Controllers;

namespace HZ.WMS.WebAPI.Areas.PP.Controllers
{
    /// <summary>
    /// 生产序列号分配
    /// </summary>
    public class PartScanController : ApiBaseController
    {
        private SD_Cable_Sale_PartOrderInfoApp _salePartOrderInfoApp = new SD_Cable_Sale_PartOrderInfoApp();


        #region 分页列表

        [HttpGet]
        public IHttpActionResult GetPartInfo([FromUri] SD_Cable_Sale_OrderInfoListReq orderInfoListReq)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _salePartOrderInfoApp.DbContextForOMS.Queryable<SD_Cable_Sale_PartOrderInfo>()
                    .Where(t => t.IsDelete == false && orderInfoListReq.ContractNo == t.ContractNo).ToList();
                var ids = itemsData.Select(t => t.Id).ToList();
                var details = _salePartOrderInfoApp.DbContextForOMS.Queryable<SD_Cable_Sale_PartOrderDetails>()
                    .Where(t => t.IsDelete == false && ids.Contains(t.Pid))
                    .ToList();
                var detailMap = details.GroupBy(t => t.Pid).ToDictionary(t => t.Key, t => t.ToList());
                foreach (var salePartOrderInfo in itemsData)
                {
                    salePartOrderInfo.OrderDetailList = detailMap.TryGetValue(salePartOrderInfo.Id, out var items) ? items : new List<SD_Cable_Sale_PartOrderDetails>();
                }
                result.Data = itemsData;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
            return Json(result);
        }

        #endregion

        #region 创建计划订单

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult ScanConfirm([FromBody] string[] ids)
        {
            var result = new ResponseData();
            try
            {
                return Json(_salePartOrderInfoApp.ScanConfirm(ids, GetCurrentUser()));
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion
    }
}