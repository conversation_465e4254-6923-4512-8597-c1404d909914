using SqlSugar;
using System;
using System.Collections.Generic;

using HZ.Core.Security;
using HZ.WMS.Entity.MD;
using HZ.Core.Http;
using HZ.WMS.Entity.MD.ViewModel;
using System.Linq;
using System.Threading.Tasks;
using System.Linq.Expressions;
using SqlSugar;

namespace HZ.WMS.Application.MD
{
    /// <summary>
    /// 物料主数据
    /// 没有out_message消息的函数，意味着只在最前端做未处理异常捕捉
    /// </summary>
    public class MD_ItemApp : BaseApp<MD_Item>
    {
        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public MD_ItemApp() : base(){}

        #endregion

        #region 获取分页列表

        /// <summary>
        /// 获取分页列表
        /// </summary>
        /// <param name="page">分页</param>
        /// <param name="where">参数</param>
        /// <returns></returns>
        public List<MD_Item_View> GetPageList(Pagination page, Expression<Func<MD_Item_View, bool>> where)
        {
            page.Sort = "ItemCode";

            var query = DbContext.Queryable<MD_Item_View>()
                .Where(where)
                .OrderBy(page.Sort);

            int total = query.Count();
            page.Total = total;
            if (page.PageSize > total)
            {
                return query.ToPageList(page.PageNumber, total);
            }
            return query.ToPageList(page.PageNumber, page.PageSize);
        }

        #endregion

        /// <summary>
        /// 更新或新增
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public bool UpdateOrInsert(MD_Item entity, string user)
        {
            var materialEntity = this.GetFirstEntityByFieldValue("ItemCode", entity.ItemCode);
            if (materialEntity != null)
            {
                entity.ItemID = materialEntity.ItemID;
                entity.MTime = DateTime.Now;
                entity.MUser = user;
                return (base.Update(entity) > 0);
            }
            else
            {
                entity.ItemID = Guid.NewGuid().ToString();
                entity.CUser = user;
                entity.CTime = DateTime.Now;
                entity.IsDelete = false;
                return (base.Insert(entity) != null);
            }
        }



        /// <summary>
        /// 更新或新增
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public bool UpdateOrInsertStockManWay(List<MD_Item> list, string user)
        {
            foreach (MD_Item mod in list)
            {
                var materialEntity = this.GetFirstEntityByFieldValue("ItemCode", mod.ItemCode);
                if (materialEntity != null)
                {

                    materialEntity.StockManWay = 2;
                    materialEntity.MTime = DateTime.Now;
                    materialEntity.MUser = user;
                    Update(materialEntity);
                }
                else
                {
                    mod.ItemID = Guid.NewGuid().ToString();
                    mod.StockManWay = 2;
                    mod.CUser = user;
                    mod.CTime = DateTime.Now;
                    mod.IsDelete = false;
                    base.Insert(mod);
                }
            }

            return true;
        }
    }
}

