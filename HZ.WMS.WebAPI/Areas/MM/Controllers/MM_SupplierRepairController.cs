using HZ.Core.Http;
using HZ.Core.Office;
using HZ.WMS.Application;
using HZ.WMS.Application.MD;
using HZ.WMS.Application.MM;
using HZ.WMS.Application.Sys;
using HZ.WMS.Entity.MM;
using HZ.WMS.Entity.MM.Parameters;
using HZ.WMS.Entity.MM.ViewModel;
using HZ.WMS.Entity.Sys;
using HZ.WMS.WebAPI.Controllers;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Web;
using System.Web.Http;

namespace HZ.WMS.WebAPI.Areas.MM.Controllers
{
    /// <summary>
    /// 供应商返修
    /// </summary>
    public class MM_SupplierRepairController : ApiBaseController
    {
        private  MM_SupplierRepairApp _app = new MM_SupplierRepairApp();
        private MM_SupplierRepairViewApp _appView = new MM_SupplierRepairViewApp();
        private Sys_DictionaryApp _dictionaryapp = new Sys_DictionaryApp();
        private MD_StockApp _stockApp = new MD_StockApp();

        #region 获取系统生成的单号

        /// <summary>
        /// 获取系统生成的单号
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetDocNum()
        {
            var result = new ResponseData();
            try
            {
                result.Data = base.GenerateDocNum(DocType.MM, DocFixedNumDef.MM_SupplierRepair);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 查询分页列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="page">分页</param>
        /// <param name="keyword">参数</param>
        /// <param name="dateValue">日期区间</param>
        /// <param name="isPosted">是否过账 0：未过账 1：已过帐</param>
        /// <param name="LendingType">借出单类型 0：手动创建 1：自动创建</param>
        /// <param name="Status">状态 0:未审核 1：审核中 2：已审核 3：已取消</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]string keyword, [FromUri] DateTime[] dateValue)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormatTodayDate(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];

                var itemsData = _app.GetPageList(page, t => (string.IsNullOrEmpty(keyword)
                        || t.DocNum.Contains(keyword) || t.SupplierCode.Contains(keyword) || t.SupplierName.Contains(keyword)
                        || t.ItemCode.Contains(keyword) || t.ItemName.Contains(keyword)
                        || t.WhsCode.Contains(keyword) || t.WhsName.Contains(keyword) || t.Remark.Contains(keyword)
                        || t.CUser.Contains(keyword) || t.HandlenCode.Contains(keyword) || t.HandlenName.Contains(keyword))
                        && (t.CTime >= fromTime && t.CTime <= toTime)
                        ).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

       

        

        #region 保存

        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="Parameters">参数类</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Save([FromBody]List<MM_SupplierRepair> Parameters)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                Sys_User currentUser = GetCurrentUser();
                bool bSubmit = _app.Save(Parameters, currentUser.LoginAccount, out error_message);
                if (!bSubmit)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }

        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="entities">接收的集合</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Update([FromBody]List<MM_SupplierRepair> entities)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                Sys_User currentUser = GetCurrentUser();

                if (_app.Update(entities) > 0)
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2000;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                    result.MessageParam = 2010;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="DocNums">领料单号</param>
        /// <returns></returns>
        [HttpDelete]
        public IHttpActionResult Delete([FromBody]List<string> ids)
        {
            var result = new ResponseData();
            try
            {
                //获取当前登陆者
                Sys_User currLoginUser = GetCurrentUser();
                string error_message = "";
                bool delete = _app.Delete(ids, currLoginUser.LoginAccount, out error_message);
                if (!delete)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                }
                ////删除主表
                //var flag = _app.Delete(d => DocNums.Contains(d.DocNum), currLoginUser.LoginAccount);
                ////删除子表
                //var mark = _detailApp.Delete(d => DocNums.Contains(d.DocNum), currLoginUser.LoginAccount);
                //result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion


        #region 导出

        /// <summary>
        /// 导出
        /// </summary>
        /// <param name="keyword">参数</param>
        /// <param name="dateValue">日期区间</param>
        /// <param name="isPosted">是否过账 0：未过账 1：已过帐</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] string keyword, [FromUri] DateTime[] dateValue)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];
                var itemsData = _appView.GetList(t => (string.IsNullOrEmpty(keyword)
              || t.DocNum.Contains(keyword) || t.SupplierCode.Contains(keyword) || t.SupplierName.Contains(keyword)
              || t.ItemCode.Contains(keyword) || t.ItemName.Contains(keyword)
              || t.WhsCode.Contains(keyword) || t.WhsName.Contains(keyword) || t.Remark.Contains(keyword)
              || t.CUser.Contains(keyword) || t.HandlenCode.Contains(keyword) || t.HandlenName.Contains(keyword))
              && (t.CTime >= fromTime && t.CTime <= toTime)
              ).ToList();
                var columns = ExcelService.FetchDefaultColumnList<MM_SupplierRepair_View>();
                string[] ignoreField = new string[]
                {
                    "ID","BarCode","HandlenCode","IsDelete","MUser","MTime",
                    "DUser","DTime"
                };
                var ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (var userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                //columns.ForEach((column) =>
                //{
                //if (column.ColumnName == "IsPosted")
                //{
                //    column.Formattor = ExcelExportFormatter.IsOrNoFormatter;
                //}
                //});

                return ExportToExcelFile(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #region 查询销售、仓库库存信息

        /// <summary>
        /// 查询销售、仓库库存信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IHttpActionResult GetStockInfo([FromUri]Pagination page, [FromUri] string keyword, [FromUri] string Whs)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _stockApp.GetPageList(page, t => (string.IsNullOrEmpty(keyword)
                                                  || t.ItemCode.Contains(keyword) || t.ItemName.Contains(keyword)
                                                  || t.Unit.Contains(keyword) || t.WhsCode.Contains(keyword)
                                                  || t.WhsName.Contains(keyword) || t.RegionCode.Contains(keyword)
                                                  || t.RegionName.Contains(keyword) || t.BinLocationCode.Contains(keyword)
                                                  || t.BinLocationName.Contains(keyword) || t.SaleNum.Contains(keyword)
                                                  || t.SpecialStock.Contains(keyword))
                                                  && (string.IsNullOrEmpty(Whs) || t.WhsCode.Contains(Whs) || t.WhsName.Contains(Whs))
                                                  && (t.SpecialStock != "O" || t.SpecialStock == null && t.Qty > 0)
                                                  ).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 打印

        /// <summary>
        /// 打印
        /// </summary>
        /// <param name="docNums"></param>
        /// <param name="templateCode"></param>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IHttpActionResult Print([FromUri]string[] docNums)
        {
            var result = new ResponseData();
            string templateCode = "供应商返修单.repx";
            if (docNums == null || docNums.Length == 0 || string.IsNullOrEmpty(templateCode))
            {
                result.Code = (int)WMSStatusCode.Failed;
            }
            else
            {
                DevExpress.XtraReports.UI.XtraReport report =
                    DevExpress.XtraReports.UI.XtraReport.FromFile(HttpContext.Current.Server.MapPath("~/") + ConfigurationManager.AppSettings["LabelTemplateUploadPath"]?.ToString() + templateCode, true);
                DevExpress.DataAccess.Sql.SqlDataSource dataSource = report.DataSource as DevExpress.DataAccess.Sql.SqlDataSource;
                DevExpress.DataAccess.Sql.QueryParameter parameter = new DevExpress.DataAccess.Sql.QueryParameter()
                {
                    Name = "docNum",
                    Type = typeof(List<string>),
                    Value = docNums.ToList()
                };

                dataSource.Queries[0].Parameters[0] = parameter;
                return base.PrintToPDF(report);
            }
            return Json(result);
        }

        #endregion

    }
}