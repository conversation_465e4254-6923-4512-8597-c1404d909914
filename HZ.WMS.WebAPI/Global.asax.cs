using System.Web.Http;
using System.Web.Mvc;
using System.Web.Optimization;
using System.Web.Routing;
using System.Threading.Tasks;
using HZ.WMS.WebJob;
using System.Configuration;
using HZ.WMS.Application.Base;

namespace HZ.WMS.WebAPI
{
    /// <summary>
    /// 
    /// </summary>
    public class WebApiApplication : System.Web.HttpApplication
    {
        /// <summary>
        /// 应用程序启动
        /// </summary>
        protected void Application_Start()
        {
            try
            {
                AreaRegistration.RegisterAllAreas();
                GlobalConfiguration.Configure(WebApiConfig.Register);
                FilterConfig.RegisterGlobalFilters(GlobalFilters.Filters);
                RouteConfig.RegisterRoutes(RouteTable.Routes);
                BundleConfig.RegisterBundles(BundleTable.Bundles);

                // 初始化数据库连接池
                DatabaseConfig.Initialize();

                // 初始化连接池管理器
                ConnectionPoolManager.Initialize();

                // log4net
                // log4net.Config.XmlConfigurator.Configure();

                // 启动定时任务
                var isAutoRunJob = bool.Parse(ConfigurationManager.AppSettings["AutoRunJob"]);
                if (isAutoRunJob)
                {
                    //自动运行JOB
                    JobManager.RunJob();
                }
            }
            catch (System.Exception ex)
            {
                // 记录应用程序启动错误
                HZ.Core.Logging.LogHelper.Instance.LogError($"应用程序启动失败：{ex.Message}\n堆栈跟踪: {ex.StackTrace}");
                throw;
            }
        }
    }
}
