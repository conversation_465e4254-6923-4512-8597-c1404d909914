using System.ComponentModel;

namespace HZ.WMS.Entity.Produce.Req
{
    public class CreatePlanRes
    {
        
        /// <summary>
        /// 工厂
        /// </summary>  
        [Description("工厂")]
        public string WERKS { get; set; }
        
        /// <summary>
        /// 销售订单号
        /// </summary>  
        [Description("销售订单号")]
        public string VBELN { get; set; }
        
        /// <summary>
        /// 销售订单行
        /// </summary>  
        [Description("销售订单行")]
        public string POSNR { get; set; }
        
        /// <summary>
        /// 消息类型
        /// </summary>  
        [Description("消息类型")]
        public string ETYPE { get; set; }

        /// <summary>
        /// 消息文本
        /// </summary>  
        [Description("消息文本")]
        public string EMSG { get; set; }

        /// <summary>
        /// 采购申请号
        /// </summary>  
        [Description("采购申请号")]
        public string BANFN { get; set; }
        
    }
}