using HZ.Core.Http;
using HZ.Core.Office;
using HZ.WMS.Application;
using HZ.WMS.Application.QM;
using HZ.WMS.Application.Sys;
using HZ.WMS.Entity.QM;
using HZ.WMS.Entity.QM.Parameters;
using HZ.WMS.Entity.Sys;
using HZ.WMS.WebAPI.Controllers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Http;
using HZ.WMS.Entity.SAP;

namespace HZ.WMS.WebAPI.Areas.QM.Controllers
{
    /// <summary>
    /// 采购入库检验（业务控制层）
    /// </summary>
    public class QM_PurchaseInspectionController : ApiBaseController
    {
        private QM_PurchaseInspectionApp _app = new QM_PurchaseInspectionApp();
        private Sys_DictionaryApp _dictionaryapp = new Sys_DictionaryApp();

        #region PC

        #region 查询分页列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="page">分页</param>
        /// <param name="keyword"></param>
        /// <param name="dateValue">日期</param>
        /// <param name="isPosted">是否过账</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]string keyword, [FromUri] DateTime[] dateValue, [FromUri] bool? isPosted)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];

                var itemsData = _app.GetPageList(page, t => (string.IsNullOrEmpty(keyword)
                        || t.DocNum.Contains(keyword)
                        || t.ItemCode.Contains(keyword)
                        || t.ItemName.Contains(keyword)
                        || t.SupplierCode.Contains(keyword)
                        || t.SupplierName.Contains(keyword)
                        || t.InspectionNum.Contains(keyword)
                        || t.BaseNum.Contains(keyword)
                        || t.CUser.Contains(keyword))
                        && (t.CTime >= fromTime && t.CTime <= toTime)
                        && (isPosted == null || t.IsPosted == isPosted)).ToList();

                var baseNumList = itemsData.Select(x => x.BaseNum).ToList();
                var baseNumLineList = itemsData.Select(x => x.BaseNum + x.BaseLine).ToList();
                var ekkoDict = _app.DbContextForSAP.Queryable<XZ_SAP_EKKO>()
                    .Where(t => baseNumList.Contains(t.EBELN) && t.Status == false).ToList()
                    .ToDictionary(t => t.EBELN, t => t);
                var ekpoDict = _app.DbContextForSAP.Queryable<XZ_SAP_EKPO>()
                    .Where(t => baseNumLineList.Contains(t.OrderNoAndLineNo) && t.Status == false).ToList()
                    .ToDictionary(t => t.OrderNoAndLineNo, t => t);
                
                foreach (var purchaseInspection in itemsData)
                {
                    string remark = "工厂:";
                    if (ekkoDict.ContainsKey(purchaseInspection.BaseNum))
                    {
                        if (ekkoDict[purchaseInspection.BaseNum].BUKRS == "2002")
                        {
                            remark += "富沃德电机;";
                        }
                        if (ekkoDict[purchaseInspection.BaseNum].BUKRS == "2020")
                        {
                            remark += "富沃德电缆;";
                        }
                    }

                    if (ekpoDict.ContainsKey(purchaseInspection.BaseNum + purchaseInspection.BaseLine))
                    {
                        remark += ekpoDict[purchaseInspection.BaseNum + purchaseInspection.BaseLine].ZTEXT;
                    }

                    purchaseInspection.Remark = remark;
                }
                
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 导出

       /// <summary>
       /// 导出
       /// </summary>
       /// <param name="keyword">参数</param>
       /// <param name="dateValue">日期区间</param>
       /// <param name="isPosted">是否过账</param>
       /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] string keyword, [FromUri] DateTime[] dateValue, [FromUri] string isPosted)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];
                var itemsData = _app.GetList().Where(t => string.IsNullOrEmpty(keyword) || t.DocNum.Contains(keyword)
                        || t.InspectionNum.Contains(keyword) || t.ItemCode.Contains(keyword) || t.SupplierCode.Contains(keyword) || t.SupplierName.Contains(keyword)
                        || t.ItemName.Contains(keyword) || t.Batch.Contains(keyword)
                || t.PostUser.Contains(keyword)
                || t.CUser.Contains(keyword)).Where(x => x.CTime >= fromTime && x.CTime <= toTime && !x.IsDelete && (string.IsNullOrEmpty(isPosted) || x.IsPosted == bool.Parse(isPosted))).ToList();
                //var itemsData = _app.GetList().ToList();
                var columns = ExcelService.FetchDefaultColumnList<QM_PurchaseInspection>();
                string[] ignoreField = new string[] 
                {
                    "InspectionID", "InspectionLine", "BaseEntry", "BaseType", "Batch", "BarCode", "WhsCode",
                    "PurchaseQty", "IsPosted", "PostUser", "PostTime", "IsDelete", "DTime", "DUser","MUser","MTime",
                    "Line","InspectionTime","Unit"

                };

                var ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (var userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                columns.ForEach((column) =>
                {
                    if (column.ColumnName == "InspectionItem")
                    {
                        column.Formattor = ExcelExportFormatter.IStatusFormatterForItem;
                    }
                });
                return ExportToExcelFile(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #region 回写SRM检验单

        /// <summary>
        /// 回写SRM检验单
        /// </summary>
        /// <param name="entities">集合</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult DoPost([FromBody]List<QM_PurchaseInspection> entities)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                var currentUser = GetCurrentUser();// , currentUser.LoginAccount 
                bool bPost = _app.DoPost(entities, currentUser.LoginAccount, out error_message);
                if (!bPost)//过账失败==2000，前台处理过账失败后重新刷新界面信息
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2010;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2000;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="ids">根据ID进行删除</param>
        /// <returns></returns>
        [HttpDelete]
        public IHttpActionResult Delete([FromBody]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                bool bDelete = _app.Deletes(ids, GetCurrentUser().LoginAccount, out error_message);
                if (!bDelete || !string.IsNullOrEmpty(error_message))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }

            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #endregion

        #region Model

        #region 查询数据字典信息

        /// <summary>
        /// 查询数据字典信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetDictionary()
        {
            string keyword = "PO005";
            var result = new ResponseData();
            try
            {
                var itemsData = _dictionaryapp.GetEntity(keyword);
                result.Data = itemsData;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 查询SRM报检单信息

        /// <summary>
        /// 查询SRM报检单信息
        /// </summary>
        /// <param name="keyword">参数</param>
        /// <returns></returns>
        [HttpGet]
        //[AllowAnonymous]
        public IHttpActionResult GetSRMInspectionInfo([FromUri] string keyword)
        {
            string error_message;
            var result = new ResponseData();
            if (_app.CheckDate(keyword, out error_message))//校验
            {
                try
                {
                    var itemsData = _app.GetSRMInspectionInfo(keyword, out error_message);
                    if (itemsData != null && itemsData.Count > 0)
                    {
                        result.Data = itemsData;
                        result.Code = (int)WMSStatusCode.Success;
                    }
                    else
                    {
                        result.Message = error_message;
                        result.Code = (int)WMSStatusCode.Success;
                    }
                }
                catch (Exception ex)
                {
                    result.Code = (int)WMSStatusCode.UnHandledException;
                    result.Message = ex.InnerException?.Message ?? ex.Message;
                }
            }
            else
            {
                result.Code = (int)WMSStatusCode.Success;
                result.Message = error_message;
            }
            return Json(result);
        }

        #endregion

        #region 校验报检单号、物料号是否已经在采购检验中提交过了--废弃

        /// <summary>
        /// 校验报检单号、物料号是否已经在采购检验中提交过了
        /// </summary>
        /// <param name="InspectionNo">报检单号</param>
        /// <param name="ItemCode">物料编号</param>
        /// <returns></returns>
        //[HttpGet]
        ////[AllowAnonymous]
        //public IHttpActionResult CheckPurInsDate([FromUri] string InspectionNo, [FromUri] string ItemCode)
        //{
        //    string error_message;
        //    var result = new ResponseData();
        //    try
        //    {
        //        bool purins = _app.CheckPurInsDate(InspectionNo, ItemCode, out error_message);
        //        if (!purins)
        //        {
        //            result.Message = error_message;
        //            result.Code = (int)WMSStatusCode.Success;
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        result.Code = (int)WMSStatusCode.UnHandledException;
        //        result.Message = ex.InnerException?.Message ?? ex.Message;
        //    }
        //    return Json(result);
        //}

        #endregion

        #region 保存

        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="Parameters">参数类</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Save([FromBody]QM_PurchaseInspectionParameters Parameters)
        {

            var result = new ResponseData();
            try
            {
                string error_message = "";
                string type = ""; //类型 1:提交失败 2：过账失败
                Sys_User currentUser = GetCurrentUser();
                bool bSave = _app.Save(Parameters.purchaseReceipts, currentUser.LoginAccount, out error_message, out type);
                if (!bSave && type == "1")
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
                else if (!bSave && type == "2")
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }

        #endregion

        #endregion

    }
}