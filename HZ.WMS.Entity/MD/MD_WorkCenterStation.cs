using SqlSugar;
using System;
using System.ComponentModel;

namespace HZ.WMS.Entity.MD
{
    /// <summary>
    /// 工作中心站点
    /// </summary>
    [SugarTable("MD_WorkCenterStation")]
    public class MD_WorkCenterStation : BaseEntity
    {
        /// <summary>
        /// Id
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Description("Id")]
        public string Id { get; set; }

        /// <summary>
        /// 工作中心名称
        /// </summary>
        [Description("工作中心名称")]
        public string WorkCenterName { get; set; }

        /// <summary>
        /// 工作中心编码
        /// </summary>
        [Description("工作中心编码")]
        public string WorkCenterCode { get; set; }

        /// <summary>
        /// 站点代码
        /// </summary>
        [Description("站点代码")]
        public string StationCode { get; set; }

        /// <summary>
        /// 站点名称
        /// </summary>
        [Description("站点名称")]
        public string StationName { get; set; }
    }
} 