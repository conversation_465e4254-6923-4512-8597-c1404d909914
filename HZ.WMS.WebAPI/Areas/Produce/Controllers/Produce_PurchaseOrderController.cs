using System;
using System.Collections.Generic;
using System.Web.Http;
using HZ.Core.Http;
using HZ.WMS.Application.Produce;
using HZ.WMS.Entity.Produce;
using HZ.WMS.Entity.Produce.Req;
using HZ.WMS.WebAPI.Controllers;

namespace HZ.WMS.WebAPI.Areas.Produce.Controllers
{
    /// <summary>
    /// 采购订单控制器
    /// </summary>
    public class Produce_PurchaseOrderController : ApiBaseController
    {
        #region 变量及构造函数

        private readonly Produce_PurchaseOrderApp _app = new Produce_PurchaseOrderApp();

        #endregion

        #region 获取分页列表

        /// <summary>
        /// 获取分页列表
        /// </summary>
        /// <param name="page">分页参数</param>
        /// <param name="req">查询条件</param>
        /// <returns>分页数据</returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri] Pagination page, [FromUri] Produce_PurchaseOrderListReq req)
        {
            var result = new ResponseData();
            try
            {
                var data = _app.GetPageList(page, req);
                result.Data = new ResponsePageData { total = page.Total, items = data };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取详情

        /// <summary>
        /// 获取详情
        /// </summary>
        /// <param name="id">主键</param>
        /// <returns>实体详情</returns>
        [HttpGet]
        public IHttpActionResult GetEntity(string id)
        {
            var result = new ResponseData();
            try
            {
                var entity = _app.GetEntity(id);
                result.Data = entity;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 根据父ID获取明细列表

        /// <summary>
        /// 根据父ID获取明细列表
        /// </summary>
        /// <param name="pid">父ID</param>
        /// <returns>明细列表</returns>
        [HttpGet]
        public IHttpActionResult GetDetailsByPid(string pid)
        {
            var result = new ResponseData();
            try
            {
                var data = _app.GetDetailsByPid(pid);
                result.Data = data;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 根据采购订单号获取明细列表

        /// <summary>
        /// 根据采购订单号获取明细列表
        /// </summary>
        /// <param name="purchaseOrderNo">采购订单号</param>
        /// <returns>明细列表</returns>
        [HttpGet]
        public IHttpActionResult GetDetailsByOrderNo(string purchaseOrderNo)
        {
            var result = new ResponseData();
            try
            {
                var data = _app.GetDetailsByOrderNo(purchaseOrderNo);
                result.Data = data;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 保存

        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="entity">实体数据</param>
        /// <returns>保存结果</returns>
        [HttpPost]
        public IHttpActionResult SaveForm([FromBody] Produce_PurchaseOrder entity)
        {
            var result = new ResponseData();
            try
            {
                bool success = _app.SaveForm(entity, GetCurrentUser().LoginAccount);
                if (success)
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = "保存成功";
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "保存失败";
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 批量保存

        /// <summary>
        /// 批量保存
        /// </summary>
        /// <param name="entities">实体列表</param>
        /// <returns>保存结果</returns>
        [HttpPost]
        public IHttpActionResult SaveBatch([FromBody] List<Produce_PurchaseOrder> entities)
        {
            var result = new ResponseData();
            try
            {
                bool success = _app.SaveBatch(entities, GetCurrentUser().LoginAccount);
                if (success)
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = "保存成功";
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "保存失败";
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id">主键</param>
        /// <returns>删除结果</returns>
        [HttpPost]
        public IHttpActionResult DeleteForm(string id)
        {
            var result = new ResponseData();
            try
            {
                bool success = _app.DeleteForm(id, GetCurrentUser().LoginAccount);
                if (success)
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = "删除成功";
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "删除失败";
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 批量删除

        /// <summary>
        /// 批量删除
        /// </summary>
        /// <param name="ids">主键数组</param>
        /// <returns>删除结果</returns>
        [HttpPost]
        public IHttpActionResult DeleteBatch([FromBody] string[] ids)
        {
            var result = new ResponseData();
            try
            {
                bool success = _app.DeleteBatch(ids, GetCurrentUser().LoginAccount);
                if (success)
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = "删除成功";
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "删除失败";
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion
    }
} 