using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.SAP
{
    /// <summary>
    ///SAP中间库-销售订单明细
    /// </summary>
    [SugarTable("XZ_SAP_VBAP")]
    public class XZ_SAP_VBAP
    {
        /// <summary>
        /// 销售凭证
        /// </summary>
        [Description("销售凭证")]
        public string VBELN { get; set; }

        /// <summary>
        /// 项目
        /// </summary>
        [Description("项目")]
        public int? POSNR { get; set; }

        /// <summary>
        /// 物料
        /// </summary>
        [Description("物料")]
        public string MATNR { get; set; }

        /// <summary>
        /// 项目类别
        /// </summary>
        [Description("项目类别")]
        public string PSTYV { get; set; }

        /// <summary>
        /// 净值 
        /// </summary>
        [Description("净值 ")]
        public decimal? NETWR { get; set; }

        /// <summary>
        /// 凭证货币
        /// </summary>
        [Description("凭证货币")]
        public string WAERK { get; set; }

        /// <summary>
        /// 订单数量
        /// </summary>
        [Description("订单数量")]
        public decimal? KWMENG { get; set; }

        /// <summary>
        /// 参考凭证
        /// </summary>
        [Description("参考凭证")]
        public string VGBEL { get; set; }

        /// <summary>
        /// 参考项目
        /// </summary>
        [Description("参考项目")]
        public int? VGPOS { get; set; }

        /// <summary>
        /// 库存地点
        /// </summary>
        [Description("库存地点")]
        public string LGORT { get; set; }

        /// <summary>
        /// 承诺的交货日期
        /// </summary>
        [Description("承诺的交货日期")]
        public DateTime? CMTD_DELIV_DATE { get; set; }

        /// <summary>
        /// 销售交货批
        /// </summary>
        [Description("销售交货批")]
        public int? ZDELBA { get; set; }

        /// <summary>
        /// 合同编号
        /// </summary>
        [Description("合同编号")]
        public string ZORD_CONT { get; set; }

        /// <summary>
        /// 生产主机编号/客户出厂编号
        /// </summary>
        [Description("生产主机编号/客户出厂编号")]
        public string ZORD_OUTNO { get; set; }

        /// <summary>
        /// 装运点/收货点
        /// </summary>
        [Description("装运点/收货点")]
        public string VSTEL { get; set; }

        /// <summary>
        /// 利润中心
        /// </summary>
        [Description("利润中心")]
        public string PRCTR { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        [Description("状态")]
        public int? Status { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [Description("创建时间")]
        public DateTime? CDate { get; set; }


    }
}
