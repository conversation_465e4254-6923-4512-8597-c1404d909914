using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.MM.ViewModel
{
    /// <summary>
    /// 委外发料导出视图
    /// </summary>
    public class MM_WarehousingExport_View
    {
       
        /// <summary> 
        /// 入库单号
        /// </summary> 
        [Description("入库单号")]
        public string DocNum { get; set; }

        /// <summary> 
        /// 报检单号
        /// </summary> 
        [Description("报检单号")]
        public string InspectionNum { get; set; }

        /// <summary> 
        /// 报检单行号
        /// </summary> 
        [Description("报检单行号")]
        public int? InspectionLine { get; set; }

        /// <summary> 
        /// 采购单号
        /// </summary> 
        [Description("采购单号")]
        public string BaseNum { get; set; }

        /// <summary> 
        /// 采购单行号
        /// </summary> 
        [Description("采购单行号")]
        public int? BaseLine { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量")]
        public decimal? Qty { get; set; }

        /// <summary> 
        /// 物料编号
        /// </summary> 
        [Description("物料编号")]
        public string ItemCode { get; set; }

        /// <summary> 
        /// 物料名称
        /// </summary> 
        [Description("物料名称")]
        public string ItemName { get; set; }

        /// <summary> 
        /// 仓库编号
        /// </summary> 
        [Description("仓库编号")]
        public string WhsCode { get; set; }

        /// <summary> 
        /// 仓库名称
        /// </summary> 
        [Description("仓库")]
        public string WhsName { get; set; }

        /// <summary> 
        /// 区域编号
        /// </summary> 
        [Description("区域编号")]
        public string RegionCode { get; set; }

        /// <summary> 
        /// 区域名称
        /// </summary> 
        [Description("区域")]
        public string RegionName { get; set; }

        /// <summary> 
        /// 库位编号
        /// </summary> 
        [Description("库位编号")]
        public string BinLocationCode { get; set; }

        /// <summary> 
        /// 库位名称
        /// </summary> 
        [Description("库位")]
        public string BinLocationName { get; set; }

        /// <summary>
        /// 销售订单号
        /// </summary>
        [Description("销售订单号")]
        public string SaleNo { get; set; }

        /// <summary>
        /// 销售订单行号
        /// </summary>
        [Description("销售订单行号")]
        public decimal? SaleLineNo { get; set; }

        /// <summary>
        /// 过账日期
        /// </summary>
        [Description("过账日期")]
        public DateTime? ManualPostTime { get; set; }

        /// <summary>
        /// 是否过账
        /// </summary>
        [Description("是否过账")]
        public bool IsPosted { get; set; }

        /// <summary> 
        /// 过账人
        /// </summary> 
        [Description("过账人")]
        public string PostUser { get; set; }


        /// <summary>
        /// 凭证日期
        /// </summary>
        [Description("凭证日期")]
        public DateTime? PostTime { get; set; }

        /// <summary> 
        /// 组件编号
        /// </summary> 
        [Description("组件编号")]
        public string ComponentCode { get; set; }

        /// <summary> 
        /// 组件名称
        /// </summary> 
        [Description("组件名称")]
        public string ComponentName { get; set; }

        /// <summary> 
        /// 供应商编号
        /// </summary> 
        [Description("供应商编号")]
        public string SupplierCode { get; set; }

        /// <summary> 
        /// 供应商名称
        /// </summary> 
        [Description("供应商名称")]
        public string SupplierName { get; set; }

        /// <summary> 
        /// 组件数量
        /// </summary> 
        [Description("组件数量")]
        public decimal? ComponentQty { get; set; }

        /// <summary> 
        /// 单位
        /// </summary> 
        [Description("单位")]
        public string Unit { get; set; }

        /// <summary> 
        /// 备注
        /// </summary> 
        [Description("备注")]
        public string ZRemark { get; set; }

        /// <summary>
        /// 插入记录的用户Id
        /// </summary>
        [Description("创建用户")]
        public string CUser { get; set; }

        /// <summary>
        /// 插入记录的服务器时间
        /// </summary>
        [Description("创建时间")]
        public DateTime? CTime { get; set; } = DateTime.Now;

    }
}
