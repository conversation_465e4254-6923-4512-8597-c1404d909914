using SqlSugar;
using HZ.Core.Http;
using HZ.WMS.Entity.MM.ViewModel;
using HZ.WMS.Entity.PO.ViewModel;
using HZ.WMS.Entity.SAP;
using HZ.WMS.Entity.SAP.View;
using HZ.WMS.Entity.SD.ViewModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Application.SAP
{
    /// <summary>
    ///SAP中间库方法层
    /// </summary>
    public class SAPApp:ContentBase
    {
        #region 采购订单

        /// <summary>
        /// 查询采购订单(不区分类型)
        /// </summary>
        /// <param name="page">分页</param>
        /// <param name="keyword">参数</param>
        /// <returns></returns>
        public List<SAP_EKKO_EKPO_View> GetXZSAP_EKKO(Pagination page, string keyword)
        {
            Expression<Func<SAP_EKKO_EKPO_View, bool>> condition = x => (string.IsNullOrEmpty(keyword) || x.EBELN.Contains(keyword) );
            if (string.IsNullOrEmpty(page.Sort))
            {
                page.Sort = "EBELN";
            }
            var query = DbContext.Queryable<SAP_EKKO_EKPO_View>()
                .Where(condition)
                .OrderBy(page.Sort);

            int total = query.Count();
            page.Total = total;
            if (page.PageSize > total)
            {
                return query.ToPageList(page.PageNumber, total);
            }
            return query.ToPageList(page.PageNumber, page.PageSize);

            //有用
            //List<XZSAP_EKKO_EKPO_View>
            //page.Sort = "EBELN desc";
            //string sql = @"SELECT 
            //                 A.BSART,A.BATXT,A.EBELN,A.BUKRS,A.EKORG,A.EKGRP,A.AEDAT,A.ERNAM,A.LIFNR,A.NAME1,A.ZTERM,a.ZNUMBER,a.ZZSTACODE,
            //                 A.[Status],B.EBELP,B.PSTYP,B.KNTTP,B.MATNR,B.TXZ01,B.WERKS,B.LGORT,B.LGOBE,B.MENGE,B.MEINS,B.PEINH,B.RETPO,
            //                 B.EINDT,B.LOEKZ,B.NETPR,B.BRTWR,B.MWSKZ,B.TEXT1,B.MATKL,B.BWTTY,B.BWTAR,B.SAKTO,B.ANLN1,B.AUFNR,B.VBELN,B.VBELP,
            //                 B.EMLIF,B.[Status] EkPOStatus ,B.KOSTL
            //                FROM XZ_SAP.dbo.XZ_SAP_EKKO A
            //                LEFT JOIN(SELECT *FROM XZ_SAP.dbo.XZ_SAP_EKPO) B ON b.EBELN=a.EBELN";
            //List<XZSAP_EKKO_EKPO_View> query = view_app.GetDataTableByPage(page,sql).ToList();
            //return query;
        }

        /// <summary>
        /// 查询采购订单类型Z006(退货采购订单)
        /// </summary>
        /// <param name="page">分页</param>
        /// <param name="keyword">参数</param>
        /// <returns></returns>
        public List<PO_ReturnScanForEKKO_View> GetPO_ReturnScanForEKKO(Pagination page, string keyword)
        {
            Expression<Func<PO_ReturnScanForEKKO_View, bool>> condition = x => (string.IsNullOrEmpty(keyword) || x.EBELN.Contains(keyword) || x.MATNR.Contains(keyword) || x.LIFNR.Contains(keyword)) ;
            if (string.IsNullOrEmpty(page.Sort))
            {
                page.Sort = "EBELN";
            }
            var query = DbContext.Queryable<PO_ReturnScanForEKKO_View>()
                .Where(condition)
                .OrderBy(page.Sort);

            int total = query.Count();
            page.Total = total;
            if (page.PageSize > total)
            {
                return query.ToPageList(page.PageNumber, total);
            }
            return query.ToPageList(page.PageNumber, page.PageSize);

            //有用
            //List<XZSAP_EKKO_EKPO_View>
            //page.Sort = "EBELN desc";
            //string sql = @"SELECT 
            //                 A.BSART,A.BATXT,A.EBELN,A.BUKRS,A.EKORG,A.EKGRP,A.AEDAT,A.ERNAM,A.LIFNR,A.NAME1,A.ZTERM,a.ZNUMBER,a.ZZSTACODE,
            //                 A.[Status],B.EBELP,B.PSTYP,B.KNTTP,B.MATNR,B.TXZ01,B.WERKS,B.LGORT,B.LGOBE,B.MENGE,B.MEINS,B.PEINH,B.RETPO,
            //                 B.EINDT,B.LOEKZ,B.NETPR,B.BRTWR,B.MWSKZ,B.TEXT1,B.MATKL,B.BWTTY,B.BWTAR,B.SAKTO,B.ANLN1,B.AUFNR,B.VBELN,B.VBELP,
            //                 B.EMLIF,B.[Status] EkPOStatus ,B.KOSTL
            //                FROM XZ_SAP.dbo.XZ_SAP_EKKO A
            //                LEFT JOIN(SELECT *FROM XZ_SAP.dbo.XZ_SAP_EKPO) B ON b.EBELN=a.EBELN";
            //List<XZSAP_EKKO_EKPO_View> query = view_app.GetDataTableByPage(page,sql).ToList();
            //return query;
        }

        /// <summary>
        /// 分页条件查询采购订单关联委外领料申请单
        /// </summary>
        /// <param name="page">分页</param>
        /// <param name="condition">参数</param>
        /// <returns></returns>
        public List<MM_PickingApplyForEKKO_View> GetSAP_EKKOForSubcontractingApplication(Pagination page, Expression<Func<MM_PickingApplyForEKKO_View, bool>> condition)
        {
            if (string.IsNullOrEmpty(page.Sort))
            {
                page.Sort = "EBELN,EBELP";
            }
            var query = DbContext.Queryable<MM_PickingApplyForEKKO_View>()
                .Where(condition)
                .OrderBy(page.Sort);

            int total = query.Count();
            page.Total = total;
            if (page.PageSize > total)
            {
                return query.ToPageList(page.PageNumber, total);
            }
            return query.ToPageList(page.PageNumber, page.PageSize);
        }

        /// <summary>
        /// 查询采购订单组件-委外领料申请使用
        /// </summary>
        /// <param name="entities"></param>
        /// <returns></returns>
        public List<SAP_RESBM_View> GetXZ_SAP_RESBM_View(List<SAP_EKKO_EKPO_View> entities,out string error_message)
        {
            error_message = "";
            var queryList = new List<SAP_RESBM_View>();
            foreach (var x in entities)
            {
                var query = DbContext.Queryable<SAP_RESBM_View>().Where(i => i.BaseNum == x.EBELN && i.BaseLine == x.EBELP).ToList();
                if (query != null && query.Count > 0)
                {
                    queryList.AddRange(query);
                }
                else
                {
                    error_message = "采购订单号[" + x.EBELN + "],行号["+x.EBELP+"]未查询到组件信息，请重新选择";
                    break;
                }
            }
            return queryList;
        }

        #endregion

        #region 物料主数据

        /// <summary>
        /// 查询物料主数据
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <returns></returns>
        public List<XZ_SAP_MARC> GetXZSAP_MARC(Pagination page, string keyword)
        {
            Expression<Func<XZ_SAP_MARC, bool>> condition = x => (string.IsNullOrEmpty(keyword) 
                                                                  || x.MATNR.Contains(keyword) || x.MAKTX.Contains(keyword)
                                                                  || x.MEINS.Contains(keyword) || x.MATKL.Contains(keyword))
                                                                  && x.LVORA=="" && x.LVORM=="" && x.Status == false && (x.MSTAE == ""|| x.MSTAE==null);
            var query = DbContextForSAP.Queryable<XZ_SAP_MARC>()
                .Where(condition);
            int total = query.Count();
            page.Total = total;
            if (page.PageSize > total)
            {
                return query.ToPageList(page.PageNumber, total);
            }
            return query.ToPageList(page.PageNumber, page.PageSize);
        }

        /// <summary>
        /// 条件查询物料主数据
        /// </summary>
        /// <param name="keyword"></param>
        /// <returns></returns>
        public ISugarQueryable<XZ_SAP_MARC> GetXZSAP_MARC(string keyword)
        {
            Expression<Func<XZ_SAP_MARC, bool>> condition = x => x.MATNR==keyword && x.LVORA == "" && x.LVORM == "" && x.Status == false && (x.MSTAE == "" || x.MSTAE == null);
            var query = DbContextForSAP.Queryable<XZ_SAP_MARC>()
                .Where(condition);
            return query;
        }

        /// <summary>
        /// 根据物料编号查询物料主数据
        /// </summary>
        /// <param name="itemcode"></param>
        /// <returns></returns>
        public XZ_SAP_MARC GetSAP_MARCByCode(string itemcode)
        {
            return this.DbContextForSAP.Queryable<XZ_SAP_MARC>().Where(t => t.MATNR == itemcode && t.LVORA == "" && t.LVORM == "" && t.Status == false).ToList().FirstOrDefault();
        }

        /// <summary>
        /// 查询所有物料主数据
        /// </summary>
        /// <returns></returns>
        public ISugarQueryable<XZ_SAP_MARC> GetXZSAP_MARC()
        {
            var query = DbContextForSAP.Queryable<XZ_SAP_MARC>();
            return query;
        }

        /// <summary>
        /// 查询物料主数据关联委外领料申请单
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <returns></returns>
        public List<MM_PickingApplyForMARC_View> GetSAP_MARCForSubcontractingApplication(Pagination page, string keyword)
        {
            Expression<Func<MM_PickingApplyForMARC_View, bool>> condition = x => (string.IsNullOrEmpty(keyword) || x.MATNR.Contains(keyword)) && x.LVORA == "" && x.LVORM == "";
            var query = DbContext.Queryable<MM_PickingApplyForMARC_View>()
                .Where(condition);
            int total = query.Count();
            page.Total = total;
            if (page.PageSize > total)
            {
                return query.ToPageList(page.PageNumber, total);
            }
            return query.ToPageList(page.PageNumber, page.PageSize);
        }

        /// <summary>
        /// 校验所有物料信息是否存在
        /// </summary>
        /// <param name="itemCodes">物料数组</param>
        /// <returns></returns>
        public bool IsHaveItem(string[] itemCodes)
        {
            var isNotNullOrEmptyitemCodes = itemCodes.Where(x => !string.IsNullOrEmpty(x)).ToArray();
            var data = DbContextForSAP.Queryable<XZ_SAP_MARC>().Where(x => x.Status == false && x.LVORM == "" && x.LVORA == "" && isNotNullOrEmptyitemCodes.Contains(x.MATNR)).ToList();
            if (data.Count() == isNotNullOrEmptyitemCodes.Length)
            {
                return true;
            }
            else
            {
                string notCode = string.Empty;
                foreach (string str in isNotNullOrEmptyitemCodes)
                {
                    var notData = data.Where(s => s.MATNR.Equals(str))?.ToList();
                    if (notData != null && notData.Count > 0)
                    {
                        continue;
                    }
                    else
                    {
                        notCode = str;
                        break;
                    }
                }
                throw new Exception(string.Format("导入数据中[物料信息:{0}]在系统中不存在！", notCode));
            }
        }

        /// <summary>
        /// 校验所有物料名称是否存在
        /// </summary>
        /// <param name="itemNames">物料名称数组</param>
        /// <returns></returns>
        public bool IsHaveItemName(string[] itemNames)
        {
            var isNotNullOrEmptyitemNames = itemNames.Where(x => !string.IsNullOrEmpty(x)).ToArray();
            var data = DbContextForSAP.Queryable<XZ_SAP_MARC>().Where(x => x.Status == false && x.LVORM == "" && x.LVORA == "" && isNotNullOrEmptyitemNames.Contains(x.MAKTX)).ToList();
            if (data.Count() == isNotNullOrEmptyitemNames.Length)
            {
                return true;
            }
            else
            {
                string notCode = string.Empty;
                foreach (string str in isNotNullOrEmptyitemNames)
                {
                    var notData = data.Where(s => s.MAKTX.Equals(str))?.ToList();
                    if (notData != null && notData.Count > 0)
                    {
                        continue;
                    }
                    else
                    {
                        notCode = str;
                        break;
                    }
                }
                throw new Exception(string.Format("导入数据中[物料名称信息:{0}]在系统中不存在！", notCode));
            }
        }

        #endregion

        #region 仓库

        /// <summary>
        /// 分页条件查询仓库
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <returns></returns>
        public List<XZ_SAP_T001L> GetXZ_SAP_T001L(Pagination page, string keyword)
        {
            Expression<Func<XZ_SAP_T001L, bool>> condition = x => (string.IsNullOrEmpty(keyword) || x.LGORT.Contains(keyword) || x.LGOBE.Contains(keyword) );
            var query = DbContextForSAP.Queryable<XZ_SAP_T001L>()
                .Where(condition);
            int total = query.Count();
            page.Total = total;
            if (page.PageSize > total)
            {
                return query.ToPageList(page.PageNumber, total);
            }
            return query.ToPageList(page.PageNumber, page.PageSize);
        }

        /// <summary>
        /// 条件查询仓库
        /// </summary>
        /// <param name="keyword"></param>
        /// <returns></returns>
        public ISugarQueryable<XZ_SAP_T001L> GetXZ_SAP_T001L(string keyword)
        {
            Expression<Func<XZ_SAP_T001L, bool>> condition = x => (string.IsNullOrEmpty(keyword) || x.LGORT.Contains(keyword) || x.LGOBE.Contains(keyword));
            var query = DbContextForSAP.Queryable<XZ_SAP_T001L>()
                .Where(condition);
            return query;
        }

        /// <summary>
        /// 查询所有仓库
        /// </summary>
        /// <returns></returns>
        public ISugarQueryable<XZ_SAP_T001L> GetXZ_SAP_T001L()
        {
            var query = DbContextForSAP.Queryable<XZ_SAP_T001L>();
            return query;
        }

        #endregion

        #region 销售订单

        /// <summary>
        /// 查询销售发运计划关联SAP销售订单信息
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <returns></returns>
        public List<SD_ShippingPlanForVBAK_View> GetXZSAP_VBAK(Pagination page, string keyword, DateTime fromTime, DateTime toTime)
        {
            page.Sort = "VBELN,POSNR";
            Expression<Func<SD_ShippingPlanForVBAK_View, bool>> condition = x => (string.IsNullOrEmpty(keyword) 
                                                                || x.VBELN.Contains(keyword)
                                                                || x.KUNNR.Contains(keyword) || x.PSTYV.Contains(keyword)
                                                                || x.LGORT.Contains(keyword) || x.ZORD_CONT.Contains(keyword)
                                                                || x.ZORD_OUTNO.Contains(keyword) || x.VSTEL.Contains(keyword)
                                                                || x.KUNNRADDRESS.Contains(keyword) || x.MAKTX.Contains(keyword)
                                                                || x.MATNR.Contains(keyword) 
                                                                ) && x.AUART == "ZOR"&& (x.CMTD_DELIV_DATE >= fromTime && x.CMTD_DELIV_DATE <= toTime);
            var query = DbContext.Queryable<SD_ShippingPlanForVBAK_View>()
                .Where(condition)
                .OrderBy(page.Sort);

            int total = query.Count();
            page.Total = total;
            if (page.PageSize > total)
            {
                return query.ToPageList(page.PageNumber, total);
            }
            return query.ToPageList(page.PageNumber, page.PageSize);
        }

        /// <summary>
        /// 条件查询销售订单
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <returns></returns>
        public ISugarQueryable<SD_ShippingPlanForVBAK_View> GetXZSAP_VBAK(Expression<Func<SD_ShippingPlanForVBAK_View, bool>> condition)
        {
            //Expression<Func<XZ_SAP_VBAK_VBAP_View, bool>> condition = x => (string.IsNullOrEmpty(keyword)
                                                                //|| x.VBELN.Contains(keyword) || x.MATNR.Contains(keyword));
            var query = DbContext.Queryable<SD_ShippingPlanForVBAK_View>()
                .Where(condition);
            return query;
        }

        /// <summary>
        /// 查询所有销售订单
        /// </summary>
        /// <returns></returns>
        public ISugarQueryable<XZ_SAP_VBAK> GetXZ_SAP_VBAKAll()
        {
            var query = DbContextForSAP.Queryable<XZ_SAP_VBAK>().Where(x=>x.Status !=true);
            return query;
        }

        /// <summary>
        /// 查询销售退货单明细
        /// </summary>
        /// <returns></returns>
        public ISugarQueryable<SD_ReturnScanForVBAK_View> GetSDReturn(Expression<Func<SD_ReturnScanForVBAK_View, bool>> condition)
        {
            var query = DbContext.Queryable<SD_ReturnScanForVBAK_View>().Where(condition).OrderBy("ERDAT desc");
            return query;
        }

        #endregion

        #region 成本中心

        /// <summary>
        /// 查询所有成本中心
        /// </summary>
        /// <returns></returns>
        public ISugarQueryable<XZ_SAP_CSKS> GetXZ_SAP_CSKS()
        {
            var query = DbContextForSAP.Queryable<XZ_SAP_CSKS>().Where(x=>x.Status!=true);
            return query;
        }

        /// <summary>
        /// 条件查询成本中心
        /// </summary>
        /// <param name="keyword"></param>
        /// <returns></returns>
        public ISugarQueryable<XZ_SAP_CSKS> GetXZ_SAP_CSKS(string keyword)
        {
            Expression<Func<XZ_SAP_CSKS, bool>> condition = x => (string.IsNullOrEmpty(keyword) || x.KOSTL.Contains(keyword) || x.LTEXT.Contains(keyword)) && x.Status != true;
            var query = DbContextForSAP.Queryable<XZ_SAP_CSKS>()
                .Where(condition);
            return query;
        }

        #endregion

        #region 总账科目

        /// <summary>
        /// 查询所有总账科目(基于成本中心)
        /// </summary>
        /// <returns></returns>
        public ISugarQueryable<XZ_SAP_SKA1> GetXZ_SAP_SKA1()
        {
            var query = DbContextForSAP.Queryable<XZ_SAP_SKA1>().Where(x => x.Status != true
                                                                &&(x.SAKNR == "7001160100" || x.SAKNR == "7001170000" || x.SAKNR == "7001240000"
                                                                || x.SAKNR == "7001410000" || x.SAKNR == "7001230000" || x.SAKNR == "7001350100"
                                                                || x.SAKNR == "7001350200" || x.SAKNR == "7001250100" || x.SAKNR == "7001250400"
                                                                || x.SAKNR == "7001250500" || x.SAKNR == "7001110100" || x.SAKNR == "7001420100"
                                                                || x.SAKNR == "7001420200" || x.SAKNR == "7001190000" || x.SAKNR == "7001370200"
                                                                || x.SAKNR == "7001059900" || x.SAKNR == "7001130000" || x.SAKNR == "7001140000"
                                                                || x.SAKNR == "7001250100" || x.SAKNR == "7001250300" || x.SAKNR == "7001259900"
                                                                || x.SAKNR == "7001350300" || x.SAKNR == "7001359900" || x.SAKNR == "7001370200"
                                                                || x.SAKNR == "7001990000" || x.SAKNR == "7001299900"
                                                                || x.SAKNR == "6051030000" || x.SAKNR == "6301990000" || x.SAKNR == "6401080000"
                                                                || x.SAKNR == "6402030000" || x.SAKNR == "6711070000" || x.SAKNR == "6711990000"
                                                                || x.SAKNR == "1901010000"
                                                                || x.SAKNR == "7001370200" || x.SAKNR == "7001350100" || x.SAKNR == "7001350200"
                                                                || x.SAKNR == "7001410000" || x.SAKNR == "7001420100" || x.SAKNR == "7001420200"
                                                                || x.SAKNR == "7001190000" || x.SAKNR == "6401080000" || x.SAKNR == "6401070000"
                                                                || x.SAKNR == "6401010000" || x.SAKNR == "6401020000" || x.SAKNR == "6402010000"
                                                                || x.SAKNR == "6402020000"
                                                                )).OrderBy(x=>x.SAKNR);
            return query;
        }

        /// <summary>
        /// 查询所有总账科目(不基于成本中心)
        /// </summary>
        /// <returns></returns>
        public ISugarQueryable<XZ_SAP_SKA1> GetXZ_SAP_SKA12()
        {
            var query = DbContextForSAP.Queryable<XZ_SAP_SKA1>().Where(x => x.Status != true
                                                                && (x.SAKNR == "6402030000"|| x.SAKNR == "6301990000"
                                                                 || x.SAKNR == "6401080000"|| x.SAKNR == "6051030000"
                                                                 || x.SAKNR == "6711070000" || x.SAKNR == "6711990000"
                                                                  || x.SAKNR == "1901010000"
                                                                )).OrderBy(x => x.SAKNR);
            return query;
        }
        /// <summary>
        /// 条件查询总账科目
        /// </summary>
        /// <param name="keyword"></param>
        /// <returns></returns>
        public ISugarQueryable<XZ_SAP_SKA1> GetXZ_SAP_SKA1(string keyword)
        {
            Expression<Func<XZ_SAP_SKA1, bool>> condition = x => (string.IsNullOrEmpty(keyword) || x.SAKNR.Contains(keyword) || x.TXT50.Contains(keyword)) && x.Status != true;
            var query = DbContextForSAP.Queryable<XZ_SAP_SKA1>()
                .Where(condition);
            return query;
        }

        #endregion

        #region 客户主数据

        /// <summary>
        /// 查询所有客户主数据
        /// </summary>
        /// <returns></returns>
        public ISugarQueryable<SAP_KNA1_View> GetXZ_SAP_KNA1()
        {
            var query = DbContext.Queryable<SAP_KNA1_View>().Where(x=>x.KTOKD !="ZC04");//ZC04:员工客户
            return query;
        }

        /// <summary>
        /// 条件客户主数据
        /// </summary>
        /// <param name="keyword"></param>
        /// <returns></returns>
        public ISugarQueryable<SAP_KNA1_View> GetXZ_SAP_KNA1(string keyword)
        {
            Expression<Func<SAP_KNA1_View, bool>> condition = x => (string.IsNullOrEmpty(keyword) || x.CustomerCode.Contains(keyword) || x.CustomerName.Contains(keyword)) && x.KTOKD !="ZC04";
            var query = DbContextForSAP.Queryable<SAP_KNA1_View>()
                .Where(condition);
            return query;
        }

        #endregion

        #region 员工客户信息

        /// <summary>
        /// 员工客户信息
        /// </summary>
        /// <returns></returns>
        public ISugarQueryable<SAP_KNA1_View> GetXZ_SAP_KNA1ForEmp()
        {
            var query = DbContext.Queryable<SAP_KNA1_View>().Where(x => x.KTOKD == "ZC04");//ZC04:员工客户
            return query;
        }

        #endregion

        #region sap账期

        /// <summary>
        /// 查询SAP账期时间
        /// </summary>
        /// <param name="postTime">过账时间</param>
        /// <returns></returns>
        public DateTime GetSAPpostTime(DateTime postTime)
        {
            string TIME = postTime.ToString("yyyyMMdd");
            XZ_SAP_FICO002 query = DbContextForSAP.Queryable<XZ_SAP_FICO002>().Where(x => x.BWKEY == "2002" && x.ZSTATU == "X")?.ToList().FirstOrDefault();
            if (query != null)
            {
                if (query.LFMON == Convert.ToInt32(TIME.Substring(4, 2)))
                {
                    //如果在账期日 就返回原日期
                    return postTime;
                }
                else
                {
                    //如果不在账期日，就用新日期 当月一号
                    return Convert.ToDateTime(query.LFGJA.ToString() + "-" + query.LFMON.ToString() + "-" + "01");
                }
            }
            else
            {
                return postTime;
            }
        }

        #endregion

        #region 内部订单

        /// <summary>
        /// 查询所有内部订单
        /// </summary>
        /// <returns></returns>
        public ISugarQueryable<XZ_SAP_AUFK> GetSAPAUFK()
        {
            var query = DbContextForSAP.Queryable<XZ_SAP_AUFK>().Where(x => x.Status != true);
            return query;
        }

        #endregion

        #region 资产卡片

        /// <summary>
        /// 查询所有资产卡片
        /// </summary>
        /// <returns></returns>
        public List<XZ_SAP_ANLA> GetSAPANLA()
        {

            var query = this.DbContextForSAP.Ado.SqlQuery<XZ_SAP_ANLA>("select * from XZ_SAP_ANLA where Status=0 and ANLN1 like '000007%'").ToList();
            //var query = DbContextForSAP.Queryable<XZ_SAP_ANLA>().Where(x => x.Status != true&&x.ANLN1.Contains("000007"));
            return query;
        }

        #endregion


    }
}
