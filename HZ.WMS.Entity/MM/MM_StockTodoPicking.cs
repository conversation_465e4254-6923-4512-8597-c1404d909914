using System.ComponentModel;
using SqlSugar;

namespace HZ.WMS.Entity.MM
{
    /// <summary>
    /// 设备领料
    /// </summary>
    [SugarTable("MM_StockTodoPicking")]
    public class MM_StockTodoPicking : BaseEntity
    {
        /// <summary> 
        /// 主键ID
        /// </summary> 
        [SugarColumn(IsPrimaryKey = true)]
        [Description("主键ID")]
        public string ID { get; set; }

        /// <summary> 
        /// wms单号
        /// </summary> 
        [Description("领料单号")]
        public string DocNum { get; set; }

        /// <summary> 
        /// 物料编号
        /// </summary> 
        [Description("物料编号")]
        public string ItemCode { get; set; }

        /// <summary> 
        /// 物料名称
        /// </summary> 
        [Description("物料名称")]
        public string ItemName { get; set; }

        /// <summary> 
        /// 数量
        /// </summary> 
        [Description("数量")]
        public decimal Quantity { get; set; }

        /// <summary> 
        /// 单位
        /// </summary> 
        [Description("单位")]
        public string Unit { get; set; }

        /// <summary> 
        /// 是否审核 1：待审核，2：已审核，3：作废
        /// </summary> 
        [Description("是否审核")]
        public int AuditStatus { get; set; }

        /// <summary> 
        /// 办理人
        /// </summary> 
        [Description("办理人")]
        public string HandleName { get; set; }
    }
}