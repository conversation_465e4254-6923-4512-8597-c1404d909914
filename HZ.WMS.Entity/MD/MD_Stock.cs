using System;
using System.ComponentModel;
using SqlSugar;
//Chloe框架


namespace HZ.WMS.Entity.MD
{
    [SugarTable("MD_Stock")]
    public class MD_Stock : BaseEntity
    {
        /// <summary> 
        /// 库存ID
        /// </summary> 
        [Description("库存ID")]
        [SugarColumn(IsPrimaryKey = true)]
        public string StockID { get; set; }
        /// <summary> 
        /// 供应商编号
        /// </summary> 
        [Description("供应商编号")]
        public string SupplierCode { get; set; }
        /// <summary> 
        /// 供应商名称
        /// </summary> 
        [Description("供应商名称")]
        public string SupplierName { get; set; }

        /// <summary> 
        /// 包装箱码
        /// </summary> 
        [Description("包装箱码")]
        public string BoxBarCode { get; set; }

        /// <summary> 
        /// 条码
        /// </summary> 
        [Description("出厂编号")]
        public string BarCode { get; set; }
        /// <summary> 
        /// 批次
        /// </summary> 
        [Description("批次")]
        public string BatchNum { get; set; }
        /// <summary> 
        /// 供应商批次
        /// </summary> 
        [Description("供应商批次")]
        public string SupplierBatch { get; set; }
        /// <summary> 
        /// 生产日期
        /// </summary> 
        [Description("生产日期")]
        public DateTime? PTime { get; set; }
        /// <summary> 
        /// 物料编号
        /// </summary> 
        [Description("物料编号")]
        public string ItemCode { get; set; }
        /// <summary> 
        /// 物料名称
        /// </summary> 
        [Description("物料名称")]
        public string ItemName { get; set; }
        /// <summary> 
        /// 物料组编号
        /// </summary> 
        [Description("物料组编号")]
        public string ItmsGrpCode { get; set; }
        /// <summary> 
        /// 物料组名称
        /// </summary> 
        [Description("物料组名称")]
        public string ItmsGrpName { get; set; }
        /// <summary> 
        /// 库存数量
        /// </summary> 
        [Description("库存数量")]
        public decimal? Qty { get; set; }
        /// <summary> 
        /// 库存单位
        /// </summary> 
        [Description("库存单位")]
        public string Unit { get; set; }
        /// <summary> 
        /// 区域编号
        /// </summary> 
        [Description("区域编号")]
        public string RegionCode { get; set; }
        /// <summary> 
        /// 区域名称
        /// </summary> 
        [Description("区域名称")]
        public string RegionName { get; set; }
        /// <summary> 
        /// 库位编号
        /// </summary> 
        [Description("库位编号")]
        public string BinLocationCode { get; set; }
        /// <summary> 
        /// 库位名称
        /// </summary> 
        [Description("库位名称")]
        public string BinLocationName { get; set; }

        /// <summary> 
        /// 仓库编号
        /// </summary> 
        [Description("仓库编号")]
        public string WhsCode { get; set; }

        /// <summary> 
        /// 仓库名称
        /// </summary> 
        [Description("仓库名称")]
        public string WhsName { get; set; }

        /// <summary>
        /// 销售单号
        /// </summary>
        [Description("销售单号")]
        public string SaleNum { get; set; }

        /// <summary>
        /// 销售行号
        /// </summary>
        [Description("销售行号")]
        public int? SaleLine { get; set; }

        /// <summary>
        /// 评估类型
        /// </summary>
        [Description("评估类型")]
        public string AssessType { get; set; }


        /// <summary>
        /// 特殊库存 O:带供应商代码的,E:带销售订单的,T: 是在途库存
        /// </summary>
        [Description("特殊库存")]
        public string SpecialStock { get; set; }


    }
}


