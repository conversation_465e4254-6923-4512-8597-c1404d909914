using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.MM
{
    /// <summary>
    /// 委外入库表
    /// </summary>
    [SugarTable("MM_Warehousing")]
    public class MM_Warehousing : BaseEntity
    {
        /// <summary> 
        /// ID
        /// </summary> 
        [SugarColumn(IsPrimaryKey = true)]
        [Description("ID")]
        public string ID { get; set; }

        /// <summary> 
        /// 入库单号
        /// </summary> 
        [Description("入库单号")]
        public string DocNum { get; set; }

        /// <summary> 
        /// 报检单号
        /// </summary> 
        [Description("报检单号")]
        public string InspectionNum { get; set; }

        /// <summary> 
        /// 报检单行号
        /// </summary> 
        [Description("报检单行号")]
        public int? InspectionLine { get; set; }

        /// <summary> 
        /// 采购单号
        /// </summary> 
        [Description("采购单号")]
        public string BaseNum { get; set; }

        /// <summary> 
        /// 采购单行号
        /// </summary> 
        [Description("采购单行号")]
        public int? BaseLine { get; set; }

        /// <summary> 
        /// 供应商编号
        /// </summary> 
        [Description("供应商编号")]
        public string SupplierCode { get; set; }

        /// <summary> 
        /// 供应商名称
        /// </summary> 
        [Description("供应商名称")]
        public string SupplierName { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量")]
        public decimal? Qty { get; set; }

        /// <summary> 
        /// 物料编号
        /// </summary> 
        [Description("物料编号")]
        public string ItemCode { get; set; }

        /// <summary> 
        /// 物料名称
        /// </summary> 
        [Description("物料名称")]
        public string ItemName { get; set; }

        /// <summary> 
        /// 仓库编号
        /// </summary> 
        [Description("仓库编号")]
        public string WhsCode { get; set; }

        /// <summary> 
        /// 仓库名称
        /// </summary> 
        [Description("仓库名称")]
        public string WhsName { get; set; }

        /// <summary> 
        /// 区域编号
        /// </summary> 
        [Description("区域编号")]
        public string RegionCode { get; set; }

        /// <summary> 
        /// 区域名称
        /// </summary> 
        [Description("区域名称")]
        public string RegionName { get; set; }

        /// <summary> 
        /// 库位编号
        /// </summary> 
        [Description("库位编号")]
        public string BinLocationCode { get; set; }

        /// <summary> 
        /// 库位名称
        /// </summary> 
        [Description("库位名称")]
        public string BinLocationName { get; set; }

        /// <summary> 
        /// 是否过账 1：已过账，0：未过账
        /// </summary> 
        [Description("是否过账")]
        public bool IsPosted { get; set; }

        /// <summary> 
        /// 过账人
        /// </summary> 
        [Description("过账人")]
        public string PostUser { get; set; }

        /// <summary> 
        /// 过账时间
        /// </summary> 
        [Description("过账时间")]
        public DateTime? PostTime { get; set; }

        /// <summary> 
        /// 移动类型
        /// </summary> 
        [Description("移动类型")]
        public string MovementType { get; set; }

        /// <summary> 
        /// 工厂代码
        /// </summary> 
        [Description("工厂代码")]
        public string FactoryCode { get; set; }

        /// <summary> 
        /// 手动过账时间
        /// </summary> 
        [Description("手动过账时间")]
        public DateTime? ManualPostTime { get; set; }

        /// <summary> 
        /// 公司代码
        /// </summary> 
        [Description("公司代码")]
        public string CompanyCode { get; set; }

        /// <summary>
        /// 销售订单号
        /// </summary>
        [Description("销售订单号")]
        public string SaleNo { get; set; }

        /// <summary>
        /// 销售订单行号
        /// </summary>
        [Description("销售订单行号")]
        public decimal? SaleLineNo { get; set; }

        /// <summary> 
        /// SAP物料凭证单号
        /// </summary> 
        [Description("SAP物料凭证单号")]
        public string SapDocNum { get; set; }

        /// <summary> 
        /// SAP物料凭证行号
        /// </summary> 
        [Description("SAP物料凭证行号")]
        public int? SapLine { get; set; }

        /// <summary>
        /// SAP过账标示
        /// </summary>
        [Description("SAP过账标示")]
        public string SAPmark { get; set; }

        /// <summary>
        /// SAP过账消息
        /// </summary>
        [Description("SAP过账消息")]
        public string SAPmessage { get; set; }
    }
}
