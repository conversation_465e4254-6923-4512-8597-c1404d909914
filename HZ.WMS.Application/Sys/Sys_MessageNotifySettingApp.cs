using SqlSugar;
using System;
using System.Collections.Generic;

using HZ.Core.Security;
using HZ.WMS.Entity.Sys;
using HZ.Core.Http;

namespace HZ.WMS.Application.Sys
{
    /// <summary>
    /// 没有out_message消息的函数，意味着只在最前端做未处理异常捕捉
    /// </summary>

    public class Sys_MessageNotifySettingApp : BaseApp<Sys_MessageNotifySetting>
    {
        private Sys_MessageTypeApp messageTypeApp = new Sys_MessageTypeApp();

		#region 默认构造函数
        
        /// <summary>
        /// 默认构造函数
        /// </summary>
        public Sys_MessageNotifySettingApp() : base()
        {
        }

        
        

        #endregion

        #region 根据消息分类ID获取设置

        /// <summary>
        /// 根据消息分类，获取相关设置
        /// </summary>
        /// <param name="messageTypeID"></param>
        /// <returns></returns>
        public List<Sys_MessageNotifySetting> GetMessageNotifySetting(string messageTypeID)
        {
            return base.GetList(t => t.MessageTypeID == messageTypeID).ToList();
        }

        #endregion

        #region 修改保存消息分类通知设定

        /// <summary>
        /// 修改保存消息分类通知设定
        /// </summary>
        /// <param name="settings"></param>
        /// <param name="messageType"></param>
        /// <returns></returns>
        public bool SaveMessageNotifySetting(List<Sys_MessageNotifySetting> settings,Sys_MessageType messageType)
        {
            try
            {
                this.DbContext.Ado.BeginTran();
                base.HardDelete(t => t.MessageTypeID == messageType.MessageTypeID);
                base.Insert(settings);
                messageTypeApp.Update(messageType);
                this.DbContext.Ado.CommitTran();
                return true;
            }
            catch(Exception ex)
            {
                this.DbContext.Ado.RollbackTran();
                return false;
            }
            
        }

        #endregion

    }
}

