using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.Core.Extensions
{
    /// <summary>
    /// DateTime类型扩展
    /// </summary>
    public static class DateTimeExt
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="dt"></param>
        /// <returns></returns>
        public static int GetTimestamp(this DateTime dt)
        {
            var timeSpan = dt - new DateTime(1970, 1, 1, 0, 0, 0);
            return (int)timeSpan.TotalSeconds;
        }

        /// <summary>
        /// 位于指定时间区间内
        /// </summary>
        /// <param name="dtm"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        public static bool IsBetween(this DateTime dtm,DateTime startTime,DateTime endTime)
        {
            if (dtm == null) return false;
            return dtm >= startTime && dtm <= endTime;
        }

    }
}
