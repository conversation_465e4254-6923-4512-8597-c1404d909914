using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.MD
{
    /// <summary>
    /// 库存差异
    /// </summary>
    public class RPT_StockDiff
    {
        /// <summary> 
        /// 区域编号
        /// </summary> 
        public string RegionCode { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string RegionName { get; set; }
        /// <summary> 
        /// 物料编号
        /// </summary> 
        public string ItemCode { get; set; }
        /// <summary> 
        /// 物料名称
        /// </summary> 
        public string ItemName { get; set; }
        /// <summary> 
        /// 物料组编号
        /// </summary> 
        public string ItmsGrpCode { get; set; }
        /// <summary> 
        /// 物料组名称
        /// </summary> 
        public string ItmsGrpName { get; set; }
        /// <summary> 
        /// 库存数量
        /// </summary> 
        public decimal? WmsQty { get; set; }
        /// <summary> 
        /// 库存单位
        /// </summary> 
        public string WmsUnit { get; set; }

        /// <summary> 
        /// SAP库存数量
        /// </summary> 
        public decimal? SapQty { get; set; }
        /// <summary> 
        /// SAP库存单位
        /// </summary> 
        public string SapUnit { get; set; }

        /// <summary> 
        /// 未过账数量
        /// </summary> 
        public decimal? NotPostQty { get; set; }
        /// <summary> 
        /// 未过账单位
        /// </summary> 
        public string NotPostUnit { get; set; }

        /// <summary> 
        /// 寄售库存
        /// </summary> 
        public decimal? ConsignQty { get; set; }

        /// <summary> 
        /// 差异数量
        /// </summary> 
        public decimal? DiffQty { get; set; }
        /// <summary> 
        ///  差异单位
        /// </summary> 
        public string DiffUnit { get; set; }




    }

    /// <summary>
    /// 
    /// </summary>
    public class StockDiffComparer : IEqualityComparer<RPT_StockDiff> 
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="x"></param>
        /// <param name="y"></param>
        /// <returns></returns>
        public bool Equals(RPT_StockDiff x, RPT_StockDiff y)
        {
            if (x == null || y == null) return false;
            if (x.ItemCode==y.ItemCode && x.RegionCode == y.RegionCode)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public int GetHashCode(RPT_StockDiff obj)
        {
            return obj.ToString().GetHashCode();
            //return obj.GetPrimaryKeyValue().GetHashCode();


        }
    }
}
