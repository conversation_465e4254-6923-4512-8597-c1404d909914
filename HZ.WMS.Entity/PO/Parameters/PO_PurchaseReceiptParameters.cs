using HZ.WMS.Entity.QM;
using HZ.WMS.Entity.QM.Parameters;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.PO.Parameters
{
    /// <summary>
    /// 采购收货接收参数的类
    /// </summary>
    public class PO_PurchaseReceiptParameters
    {

        /// <summary>
        /// 采购检验
        /// </summary>
        public List<QM_InspectionDto> entities { get; set; }

        /// <summary>
        /// 手动过账时间
        /// </summary>
        public DateTime ManualPostTime { get; set; }

        ///// <summary>
        ///// 检验单号
        ///// </summary>
        //public string InspectionNum { get; set; }

        ///// <summary>
        ///// 库位编号
        ///// </summary>
        //public string BinLocationCode { get; set; }

        ///// <summary>
        ///// 库位名称
        ///// </summary>
        //public string BinLocationName { get; set; }

        ///// <summary>
        ///// 区域编号
        ///// </summary>
        //public string RegionCode { get; set; }

        ///// <summary>
        ///// 区域名称
        ///// </summary>
        //public string RegionName { get; set; }

        ///// <summary>
        ///// 仓库编号
        ///// </summary>
        //public string WhsCode { get; set; }

        ///// <summary>
        ///// 仓库名称
        ///// </summary>
        //public string WhsName { get; set; }
    }
}
