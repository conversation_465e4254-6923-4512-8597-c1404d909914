using System;
using System.Collections.Generic;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.MD.Import;

namespace HZ.WMS.Application.MD
{
    /// <summary>
    /// 生产线对照
    /// </summary>
    public class MD_ProduceLineApp : BaseApp<MD_ProduceLine>
    {
        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public MD_ProduceLineApp() : base(){}

        #endregion

        #region 导入

        /// <summary>
        /// 导入
        /// </summary>
        /// <param name="excelList">接收的集合</param>
        /// <param name="opUser">当前用户</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool ImportExcelToBaseData(List<MD_ProduceLineImport> excelList, string opUser, out string error_message)
        {
            error_message = "";
            var flag = true;
            DateTime date = DateTime.Now;
            var entityList = new List<MD_ProduceLine>();

            try
            {
                foreach (var item in excelList)
                {
                    if (string.IsNullOrEmpty(item.LineName))
                    {
                        error_message = "线体名称不能为空";
                        return false;
                    }

                    var entity = new MD_ProduceLine
                    {
                        Id = Guid.NewGuid().ToString(),
                        LineName = item.LineName,
                        SapCode = item.SapCode,
                        CUser = opUser,
                        CTime = date,
                        IsDelete = false
                    };

                    entityList.Add(entity);
                }

                if (entityList.Count > 0)
                {
                    Insert(entityList);
                }

                return flag;
            }
            catch (Exception ex)
            {
                error_message = ex.Message;
                return false;
            }
        }

        #endregion
    }
}

