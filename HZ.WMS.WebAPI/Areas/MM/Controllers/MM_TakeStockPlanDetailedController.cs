using SqlSugar;
using HZ.Core.Http;
using HZ.WMS.Application.MD;
//
using HZ.WMS.Application.MM;
using HZ.WMS.Entity.MM;
using HZ.WMS.Entity.MM.ViewModel;
using HZ.WMS.Entity.Sys;
using HZ.WMS.WebAPI.Controllers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;

namespace HZ.WMS.WebAPI.Areas.MM.Controllers
{
    public class MM_TakeStockPlanDetailedController : ApiBaseController
    {
        private MM_TakeStockPlanDetailedApp _app = new MM_TakeStockPlanDetailedApp();




        #region 查询分页列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="pagination"></param>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]string keyword)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetPageList(page, x => string.IsNullOrEmpty(keyword)
                                 || x.DocNum.Contains(keyword)
                                 || x.RegionCode.Contains(keyword)
                                 || x.RegionName.Contains(keyword)
                                 || x.BinLocationCode.Contains(keyword)
                                 || x.BinLocationName.Contains(keyword)
                                 || x.ItemCode.Contains(keyword)
                                 || x.ItemName.Contains(keyword)
                                 || x.Remark.Contains(keyword))?.ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 添加
        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Add([FromBody]MM_TakeStockPlanDetailed entity)
        {
            var result = new ResponseData();
            try
            {
                Sys_User currLoginUser = GetCurrentUser();
                entity.CUser = currLoginUser.LoginAccount;
                entity.MUser = currLoginUser.LoginAccount;
                entity.CTime = DateTime.Now;
                entity.MTime = DateTime.Now;
                result.Data = _app.Insert(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取详情
        /// <summary>
        /// 获取详情
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetEntity([FromUri]string ID)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.GetEntityByKey(ID);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Update([FromBody]MM_TakeStockPlanDetailed entity)
        {
            var result = new ResponseData();
            try
            {
                entity.MUser = GetCurrentUser().LoginAccount;
                entity.MTime = DateTime.Now;
                result.Data = _app.Update(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除（包括批量）

        /// <summary>
        /// 删除
        /// </summary>
        /// <returns></returns>
        [HttpDelete]
        public IHttpActionResult Delete([FromBody]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.DeleteByKeys(ids, GetCurrentUser().LoginAccount);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion




        ///// <summary>
        ///// 查询分页列表
        ///// </summary>
        ///// <param name="page"></param>
        ///// <param name="keyword"></param>
        ///// <param name="fromTime"></param>
        ///// <param name="toTime"></param>
        ///// <returns></returns>
        //[HttpGet]
        //public IHttpActionResult GetPage([FromUri]Pagination page, [FromUri]string keyword, [FromUri]string fromTime, [FromUri]string toTime)
        //{
        //    var result = new ResponseData();
        //    try
        //    {
        //        ISugarQueryable<MM_TakeStockPlanDetailed> query = null;
        //        if (!string.IsNullOrEmpty(fromTime))
        //        {
        //            query = _app.GetPageList(page, x => x.CTime >= Convert.ToDateTime(fromTime).Date
        //            && (string.IsNullOrEmpty(keyword)
        //                || x.DocNum.Contains(keyword)
        //                || x.RegionCode.Contains(keyword)
        //                || x.RegionName.Contains(keyword)
        //                || x.BinLocationCode.Contains(keyword)
        //                || x.BinLocationName.Contains(keyword)
        //                || x.ItemCode.Contains(keyword)
        //                || x.ItemName.Contains(keyword)
        //                || x.Remark.Contains(keyword)));
        //        }
        //        else
        //        {
        //            return GetPageList(page, keyword);
        //        }

        //        if (!string.IsNullOrEmpty(toTime))
        //        {
        //            query = _app.GetPageList(page, x => x.CTime >= Convert.ToDateTime(fromTime).Date && x.CTime < Convert.ToDateTime(toTime).Date.AddDays(1)
        //            && (string.IsNullOrEmpty(keyword)
        //                || x.DocNum.Contains(keyword)
        //                || x.RegionCode.Contains(keyword)
        //                || x.RegionName.Contains(keyword)
        //                || x.BinLocationCode.Contains(keyword)
        //                || x.BinLocationName.Contains(keyword)
        //                || x.ItemCode.Contains(keyword)
        //                || x.ItemName.Contains(keyword)
        //                || x.Remark.Contains(keyword)));
        //        }
        //        var itemsData = query?.ToList();
        //        result.Data = new ResponsePageData { total = page.Total, items = itemsData };
        //        result.Code = (int)WMSStatusCode.Success;
        //    }
        //    catch (Exception ex)
        //    {
        //        result.Code = (int)WMSStatusCode.UnHandledException;
        //        result.Message = ex.InnerException?.Message ?? ex.Message;
        //    }
        //    return Json(result);
        //}


        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="page"></param>
        /// <param name="docNum"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPage([FromUri]Pagination page, [FromUri]string docNum)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetPageList(page,
                    x => (!string.IsNullOrEmpty(docNum)) && x.DocNum == docNum).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }


        /// <summary>
        /// 查询列表
        /// </summary>
        /// <param name="docNum"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetListByDocNum([FromUri]string docNum)
        {
            var result = new ResponseData();
            try
            {
                //var itemsData = _app.GetAllList(x => (string.IsNullOrEmpty(docNum) || x.DocNum == docNum) && !x.IsDelete)?
                //    .Join<MM_TakeStockPlan>(JoinType.InnerJoin, (x, y) => x.DocNum == y.DocNum && !x.IsDelete && !y.IsDelete)
                //    .Select((x, y) => new MM_TakeStockPlanDetailedView()
                //    {
                //        PlanID = x.PlanID,
                //        DocNum = x.DocNum,
                //        RegionCode = x.RegionCode,
                //        RegionName = x.RegionName,
                //        BinLocationCode = x.BinLocationCode,
                //        BinLocationName = x.BinLocationName,
                //        ItemCode = x.ItemCode,
                //        ItemName = x.ItemName,
                //        Remark = x.Remark,
                //        IsDelete = x.IsDelete,
                //        CUser = x.CUser,
                //        CTime = x.CTime,
                //        MUser = x.MUser,
                //        MTime = x.MTime,
                //        PUser = y.PUser,
                //        Status = y.Status
                //    }).ToList();
                var itemsData = _app.GetList(t => t.DocNum == docNum).ToList();
                result.Data = itemsData;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 添加明细
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult AddList([FromBody]MM_TakeStockPlanDto data)
        {
            var result = new ResponseData();
            try
            {
                MM_TakeStockPlan primary = data.primary;
                List<MM_TakeStockPlanDetailed> list = data.list;
                //string error_message = "";
                ////库位限制校验
                //if (new MD_BinLimitApp().ValidateBinLocationLimit("400004", data.list.Select(x => x.BinLocationCode).ToArray(), out error_message) == false)
                //{
                //    throw new Exception("Common.error", new Exception(error_message));
                //}
                if (primary == null)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "MM.TakeStockPlan.PrimaryIsNull";
                }
                else if (list == null || list.Count == 0)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "ui.MM.TakeStockPlanDetailed.DetailsIsEmpty";
                }
                else
                {
                    result.Data = _app.AddList(data.primary, data.list, GetCurrentUser().LoginAccount);
                    result.Code = (int)WMSStatusCode.Success;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        /// <summary>
        /// 开始
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult UpdatePlanList([FromBody]MM_TakeStockPlanScanDto data)
        {
            var result = new ResponseData();
            try
            {
                MM_TakeStockPlanApp _TakeStockPlanApp = new MM_TakeStockPlanApp();
                var primary = _TakeStockPlanApp.GetAllList(x => data.primaryId.Contains(x.PlanID)).ToList();
                if (primary == null || primary.Count == 0)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "MM.TakeStockPlan.PrimaryIsNull";
                }
                else
                {
                    result.Data = _app.AddList(primary, data.status, GetCurrentUser().LoginAccount);
                    result.Code = (int)WMSStatusCode.Success;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        /// <summary>
        /// 修改主单的子明细
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult UpdateList([FromBody]MM_TakeStockPlanDto data)
        {
            var result = new ResponseData();
            try
            {
                MM_TakeStockPlan primary = data.primary;
                List<MM_TakeStockPlanDetailed> list = data.list;

                if (primary == null)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "MM.TakeStockPlan.PrimaryIsNull";
                }
                else if (list == null || list.Count == 0)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "ui.MM.TakeStockPlanDetailed.DetailsIsEmpty";
                }
                else
                {
                    result.Data = _app.UpdateList(primary, list, GetCurrentUser().LoginAccount);
                    result.Code = (int)WMSStatusCode.Success;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
    }

    public class MM_TakeStockPlanDto
    {
        public MM_TakeStockPlan primary { get; set; }
        public List<MM_TakeStockPlanDetailed> list { get; set; }
    }
    public class MM_TakeStockPlanScanDto
    {
        public string[] primaryId { get; set; }
        public int status { get; set; }
    }
}

