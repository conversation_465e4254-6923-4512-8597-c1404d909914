using System;
using System.Collections.Generic;
using System.Linq;
using HZ.Core.Http;
using HZ.WMS.Entity.Produce;
using HZ.WMS.Entity.Produce.Req;
using SqlSugar;

namespace HZ.WMS.Application.Produce
{
    /// <summary>
    /// 排产明细应用服务
    /// </summary>
    public class Produce_SchedulingDetailApp : BaseApp<Produce_SchedulingDetail>
    {
        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public Produce_SchedulingDetailApp() : base() { }

        #endregion

        #region 查询分页

        /// <summary>
        /// 获取分页列表
        /// </summary>
        /// <param name="page">分页参数</param>
        /// <param name="req">查询条件</param>
        /// <returns>分页数据列表</returns>
        public List<Produce_SchedulingDetail> GetPageList(Pagination page, Produce_SchedulingDetailListReq req)
        {
            if (string.IsNullOrEmpty(page.Sort))
            {
                page.Sort = "CTime desc";
            }

            // 使用直接的条件构建方式
            var query = DbContext.Queryable<Produce_SchedulingDetail>()
                .Where(x => !x.IsDelete);

            // 添加查询条件
            if (!string.IsNullOrEmpty(req.MaterialCode))
            {
                query = query.Where(t => t.MaterialCode.Contains(req.MaterialCode));
            }
            if (!string.IsNullOrEmpty(req.ProduceOrderNo))
            {
                query = query.Where(t => t.ProduceOrderNo == req.ProduceOrderNo);
            }
            if (!string.IsNullOrEmpty(req.Pid))
            {
                query = query.Where(t => t.Pid == req.Pid);
            }
            if (!string.IsNullOrEmpty(req.SapNo))
            {
                query = query.Where(t => t.SapNo.Contains(req.SapNo));
            }
            if (!string.IsNullOrEmpty(req.Status))
            {
                query = query.Where(t => t.Status == req.Status);
            }
            if (req.StartDate.HasValue)
            {
                query = query.Where(t => t.CTime >= req.StartDate);
            }
            if (req.EndDate.HasValue)
            {
                query = query.Where(t => t.CTime <= req.EndDate);
            }

            // 排序
            query = query.OrderBy(page.Sort);

            // 执行分页查询
            int total = query.Count();
            page.Total = total;
            if (page.PageSize > total)
            {
                return query.ToPageList(page.PageNumber, total);
            }
            return query.ToPageList(page.PageNumber, page.PageSize);
        }

        #endregion

        #region 获取实体

        /// <summary>
        /// 获取实体
        /// </summary>
        /// <param name="id">主键</param>
        /// <returns>实体</returns>
        public Produce_SchedulingDetail GetEntity(string id)
        {
            return GetEntityByKey(id);
        }

        #endregion

        #region 获取明细列表

        /// <summary>
        /// 根据父ID获取明细列表
        /// </summary>
        /// <param name="pid">父ID</param>
        /// <returns>明细列表</returns>
        public List<Produce_SchedulingDetail> GetDetailsByPid(string pid)
        {
            return DbContext.Queryable<Produce_SchedulingDetail>()
                .Where(t => t.Pid == pid && !t.IsDelete)
                .OrderBy(t => t.SapLine)
                .ToList();
        }

        /// <summary>
        /// 根据生产订单号获取明细列表
        /// </summary>
        /// <param name="produceOrderNo">生产订单号</param>
        /// <returns>明细列表</returns>
        public List<Produce_SchedulingDetail> GetDetailsByOrderNo(string produceOrderNo)
        {
            return DbContext.Queryable<Produce_SchedulingDetail>()
                .Where(t => t.ProduceOrderNo == produceOrderNo && !t.IsDelete)
                .OrderBy(t => t.SapLine)
                .ToList();
        }

        #endregion

        #region 保存

        /// <summary>
        /// 保存数据
        /// </summary>
        /// <param name="entity">实体</param>
        /// <param name="userName">用户名</param>
        /// <returns>保存结果</returns>
        public bool SaveForm(Produce_SchedulingDetail entity, string userName)
        {
            try
            {
                if (string.IsNullOrEmpty(entity.Id)) // 新增
                {
                    entity.Id = Guid.NewGuid().ToString();
                    entity.IsDelete = false;
                    entity.CUser = userName;
                    entity.CTime = DateTime.Now;

                    Insert(entity);
                }
                else // 修改
                {
                    var original = GetEntityByKey(entity.Id);
                    if (original == null)
                    {
                        return false;
                    }

                    // 更新主表
                    entity.MUser = userName;
                    entity.MTime = DateTime.Now;
                    entity.CUser = original.CUser;
                    entity.CTime = original.CTime;
                    entity.IsDelete = original.IsDelete;

                    Update(entity);
                }

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        #endregion

        #region 批量保存

        /// <summary>
        /// 批量保存数据
        /// </summary>
        /// <param name="entities">实体列表</param>
        /// <param name="userName">用户名</param>
        /// <returns>保存结果</returns>
        public bool SaveBatch(List<Produce_SchedulingDetail> entities, string userName)
        {
            try
            {
                DbContext.Ado.BeginTran();

                foreach (var entity in entities)
                {
                    if (string.IsNullOrEmpty(entity.Id))
                    {
                        entity.Id = Guid.NewGuid().ToString();
                        entity.IsDelete = false;
                        entity.CUser = userName;
                        entity.CTime = DateTime.Now;

                        DbContext.Insertable(entity).ExecuteCommand();
                    }
                    else
                    {
                        var original = GetEntityByKey(entity.Id);
                        if (original != null)
                        {
                            entity.MUser = userName;
                            entity.MTime = DateTime.Now;
                            entity.CUser = original.CUser;
                            entity.CTime = original.CTime;
                            entity.IsDelete = original.IsDelete;

                            DbContext.Updateable(entity).ExecuteCommand();
                        }
                    }
                }

                DbContext.Ado.CommitTran();
                return true;
            }
            catch (Exception)
            {
                DbContext.Ado.RollbackTran();
                return false;
            }
        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除数据
        /// </summary>
        /// <param name="keyValue">主键</param>
        /// <param name="userName">用户名</param>
        /// <returns>删除结果</returns>
        public bool DeleteForm(string keyValue, string userName)
        {
            try
            {
                var entity = GetEntityByKey(keyValue);
                if (entity != null)
                {
                    entity.IsDelete = true;
                    entity.DUser = userName;
                    entity.DTime = DateTime.Now;

                    Update(entity);
                }

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// 批量删除数据
        /// </summary>
        /// <param name="keyValues">主键列表</param>
        /// <param name="userName">用户名</param>
        /// <returns>删除结果</returns>
        public bool DeleteBatch(string[] keyValues, string userName)
        {
            try
            {
                DbContext.Ado.BeginTran();

                foreach (var keyValue in keyValues)
                {
                    var entity = GetEntityByKey(keyValue);
                    if (entity != null)
                    {
                        entity.IsDelete = true;
                        entity.DUser = userName;
                        entity.DTime = DateTime.Now;

                        DbContext.Updateable(entity).ExecuteCommand();
                    }
                }

                DbContext.Ado.CommitTran();
                return true;
            }
            catch (Exception)
            {
                DbContext.Ado.RollbackTran();
                return false;
            }
        }

        #endregion
    }
} 