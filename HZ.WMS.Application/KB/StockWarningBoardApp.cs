using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HZ.WMS.Entity;


namespace HZ.WMS.Application.KB
{
    /// <summary>
    /// 库存状态看板
    /// </summary>
    public class StockWarningBoardApp : BaseApp<BaseEntity>
    {


        /// <summary>
        /// 库存状态看板
        /// </summary>
        /// <returns></returns>
        public object GetStockWarningBoardInfo()
        {
            return base.SqlQuery("PROC_BRD_StockWarning", CommandType.StoredProcedure);
        }
    }
}
