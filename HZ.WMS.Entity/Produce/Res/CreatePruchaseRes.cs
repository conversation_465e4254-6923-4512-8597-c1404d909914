using System.ComponentModel;

namespace HZ.WMS.Entity.Produce.Req
{
    public class CreatePruchase
    {
        
        
        [Description("消息类型")]
        public string ETYPE { get; set; }

        [Description("消息文本")]
        public string EMSG { get; set; }

        [Description("采购申请号")]
        public string BANFN { get; set; }

        [Description("消息文本")]
        public Table001059[] DataIn { get; set; }

        [Description("消息文本")]
        public Table001059[] DataOut { get; set; }

        public class Table001059
        {
            
            [Description("工厂")]
            public string WERKS { get; set; }

            [Description("销售订单号")]
            public string VBELN { get; set; }

            [Description("销售订单行")]
            public string POSNR { get; set; }

            [Description("采购申请号")]
            public string BANFN { get; set; }
        }
        
    }
}