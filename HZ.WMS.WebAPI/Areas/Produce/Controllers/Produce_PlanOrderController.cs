using System;
using System.Collections.Generic;
using System.Web.Http;
using HZ.Core.Http;
using HZ.WMS.Application.Produce;
using HZ.WMS.Entity.Produce;
using HZ.WMS.Entity.Produce.Req;
using HZ.WMS.WebAPI.Controllers;

namespace HZ.WMS.WebAPI.Areas.Produce.Controllers
{
    /// <summary>
    /// 计划订单控制器
    /// 处理计划订单相关的HTTP请求
    /// </summary>
    [RoutePrefix("api/Produce/PlanOrder")]
    public class Produce_PlanOrderController : ApiBaseController
    {
        #region 变量及构造函数

        private readonly Produce_PlanOrderApp _app = new Produce_PlanOrderApp();

        #endregion

        #region 获取分页列表

        /// <summary>
        /// 获取分页列表
        /// </summary>
        /// <param name="page">分页参数</param>
        /// <param name="req">查询条件</param>
        /// <returns>分页数据</returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri] Pagination page, [FromUri] Produce_PlanOrderListReq req)
        {
            var result = new ResponseData();
            try
            {
                var data = _app.GetPageList(page, req);
                result.Data = new ResponsePageData { total = page.Total, items = data };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取详情

        /// <summary>
        /// 获取详情
        /// </summary>
        /// <param name="id">主键</param>
        /// <returns>详情数据</returns>
        [HttpGet]
        public IHttpActionResult GetForm(string id)
        {
            var result = new ResponseData();
            try
            {
                var data = _app.GetEntity(id);
                result.Data = data;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 根据父ID获取明细列表

        /// <summary>
        /// 根据父ID获取明细列表
        /// </summary>
        /// <param name="pid">父ID</param>
        /// <returns>明细列表</returns>
        [HttpGet]
        public IHttpActionResult GetDetailsByPid(string pid)
        {
            var result = new ResponseData();
            try
            {
                var data = _app.GetDetailsByPid(pid);
                result.Data = data;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 根据计划订单号获取明细列表

        /// <summary>
        /// 根据计划订单号获取明细列表
        /// </summary>
        /// <param name="planOrderNo">计划订单号</param>
        /// <returns>明细列表</returns>
        [HttpGet]
        public IHttpActionResult GetDetailsByOrderNo(string planOrderNo)
        {
            var result = new ResponseData();
            try
            {
                var data = _app.GetDetailsByOrderNo(planOrderNo);
                result.Data = data;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 保存

        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="entity">实体</param>
        /// <returns>保存结果</returns>
        [HttpPost]
        public IHttpActionResult SaveForm([FromBody] Produce_PlanOrder entity)
        {
            var result = new ResponseData();
            try
            {
                var success = _app.SaveForm(entity, GetCurrentUser().LoginAccount);
                if (success)
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = "保存成功";
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "保存失败";
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 批量保存

        /// <summary>
        /// 批量保存
        /// </summary>
        /// <param name="entities">实体列表</param>
        /// <returns>保存结果</returns>
        [HttpPost]
        public IHttpActionResult SaveBatch([FromBody] List<Produce_PlanOrder> entities)
        {
            var result = new ResponseData();
            try
            {
                var success = _app.SaveBatch(entities, GetCurrentUser().LoginAccount);
                if (success)
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = "批量保存成功";
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "批量保存失败";
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="keyValue">主键</param>
        /// <returns>删除结果</returns>
        [HttpPost]
        public IHttpActionResult DeleteForm(string keyValue)
        {
            var result = new ResponseData();
            try
            {
                var success = _app.DeleteForm(keyValue, GetCurrentUser().LoginAccount);
                if (success)
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = "删除成功";
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "删除失败";
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 批量删除

        /// <summary>
        /// 批量删除
        /// </summary>
        /// <param name="keyValues">主键数组</param>
        /// <returns>删除结果</returns>
        [HttpPost]
        public IHttpActionResult DeleteBatch([FromBody] string[] keyValues)
        {
            var result = new ResponseData();
            try
            {
                var success = _app.DeleteBatch(keyValues, GetCurrentUser().LoginAccount);
                if (success)
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = "批量删除成功";
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "批量删除失败";
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion
    }
} 