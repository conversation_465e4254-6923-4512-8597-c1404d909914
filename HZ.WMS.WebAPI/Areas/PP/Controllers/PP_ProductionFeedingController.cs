using HZ.Core.Http;
using HZ.Core.Office;
using HZ.WMS.Application;
using HZ.WMS.Application.PP;
using HZ.WMS.Application.Sys;
using HZ.WMS.Entity.PP;
using HZ.WMS.Entity.PP.Dto;
using HZ.WMS.Entity.PP.ViewModel;
using HZ.WMS.Entity.Sys;
using HZ.WMS.WebAPI.Controllers;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;

namespace HZ.WMS.WebAPI.Areas.PP.Controllers
{
    /// <summary>
    /// 生产投料
    /// </summary>
    public class PP_ProductionFeedingController : ApiBaseController
    {
        private PP_ProductionFeedingApp _app = new PP_ProductionFeedingApp();
        private PP_ProductionFeedingDetailApp detailApp = new PP_ProductionFeedingDetailApp();
        private PP_ProductionOrderApp orderApp = new PP_ProductionOrderApp();
        private PP_ProductionReportApp reportApp = new PP_ProductionReportApp();

        #region 查询

        /// <summary>
        /// 获取主表分页列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]string keyword, [FromUri]string ProductionLineDes, [FromUri]string ProductionScheduler, [FromUri] DateTime[] dateValue, [FromUri]bool? isPosted)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];

                var itemsData = _app.GetPageList(page,
                    t => (string.IsNullOrEmpty(keyword)
                        || t.SerialNo.Contains(keyword)
                        || t.ProductionFeedingNo.Contains(keyword)
                        || t.ProductionOrderNo.Contains(keyword)
                        || t.MaterialNo.Contains(keyword)
                        || t.MaterialName.Contains(keyword)
                        || t.ContractNo.Contains(keyword)
                        || t.SalesOrderNo.Contains(keyword)
                        || t.Shippers.Contains(keyword))
                        && (string.IsNullOrEmpty(ProductionLineDes) || t.ProductionLineCode.Contains(ProductionLineDes) || t.ProductionLineDes.Contains(ProductionLineDes))
                        && (string.IsNullOrEmpty(ProductionScheduler) || t.ProductionScheduler.Contains(ProductionScheduler))
                        && (t.StartTime >= fromTime && t.StartTime < toTime)
                        && (isPosted == null || t.IsPosted == isPosted)
                        && t.MovementType == "261")
                    .ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 查询子表分页列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetDetailPageList([FromUri]Pagination page, [FromUri]string productionFeedingNo)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = detailApp.GetPageList(page, w => productionFeedingNo == w.ProductionFeedingNo).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 查询子表列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetDetailList([FromUri]string productionFeedingNo)
        {
            var result = new ResponseData();
            try
            {
                result.Data = detailApp.GetList(w => productionFeedingNo == w.ProductionFeedingNo).ToList();
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取系统生成的单号

        /// <summary>
        /// 获取系统生成的单号
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetDocNum()
        {
            var result = new ResponseData();
            try
            {
                result.Data = base.GenerateDocNum(DocType.PP, DocFixedNumDef.PP_ProductionFeeding);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 根据序列号查询生产订单

        [HttpGet]
        public IHttpActionResult GetOrderBySerialNo([FromUri]string serialNo)
        {
            var result = new ResponseData();
            try
            {
                var order = orderApp.GetFirstEntity(w => w.SerialNo == serialNo);
                var material = new List<PP_FeedingDetail_View>();
                if (order != null)
                {
                    //主表订单数量
                    var parent = orderApp.GetSapOrderList(w => w.ProductionOrderNo == order.ProductionOrderNo).ToList().FirstOrDefault();
                    material = detailApp.GetSapOrderDetailList(
                        w => w.ProductionOrderNo == order.ProductionOrderNo
                        && w.IsBackflush.ToUpper() != "X").ToList();
                    if (order.OrderQty < parent?.OrderQty)
                    {
                        foreach (var item in material)
                        {
                            item.DemandQty = item.DemandQty / parent.OrderQty * order.OrderQty;//=>子表需求数量/主表订单数量*主表实际投料数量
                        }
                    }
                }
                result.Data = new { Order = order, Materials = material };
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 查询物料

        /// <summary>
        /// 投料时，组件扫序列号，原材料扫物料号 
        /// </summary>
        /// <param name="productionOrderNo"></param>
        /// <param name="componentCode">序列号/物料号</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetMaterial([FromUri]string productionOrderNo, [FromUri]string componentCode)
        {
            var result = new ResponseData();
            try
            {
                //根据序列号查组件
                var MaterialNo = orderApp.GetFirstEntity(w => w.SerialNo == componentCode)?.MaterialNo;
                if(string.IsNullOrEmpty(MaterialNo))
                {
                    MaterialNo = componentCode;//如果序列号查不到，则为物料号
                }
                var item = detailApp.GetSapOrderDetailList(
                        w => w.ProductionOrderNo == productionOrderNo
                        && w.ComponentCode == MaterialNo)
                        .ToList().FirstOrDefault();
                if(item?.IsBackflush.ToUpper() == "X")
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "倒冲料不允许投料！";
                }
                else
                {
                    result.Data = item;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取所有未启用序列号的生产订单

        /// <summary>
        /// 获取所有未启用序列号的生产订单
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetOrderNoSerialNo()
        {
            var result = new ResponseData();
            try
            {
                result.Data = reportApp.GetOrderNoSerialNo();
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取所有未启用序列号的生产订单

        /// <summary>
        /// 获取所有未启用序列号的生产订单
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetOrderNoSerialNoByNo([FromUri]string ProductionOrderNo)
        {
            var result = new ResponseData();
            try
            {
                result.Data = reportApp.GetOrderNoSerialNoByNo(ProductionOrderNo);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 根据订单查询物料

        /// <summary>
        /// 根据订单查询物料
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetMaterialByOrder([FromUri]string OrderNo)
        {
            var result = new ResponseData();
            try
            {
                result.Data = detailApp.GetSapOrderDetailList(
                    w => w.ProductionOrderNo == OrderNo
                    && w.IsBackflush.ToUpper() != "X").ToList(); 
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 增加

        private bool CheckQty(PP_ProductionFeeding_Dto productionFeeding)
        {
            var mark = true;
            foreach (var item in productionFeeding.DetailList)
            {
                //已投数量
                var feedSum = detailApp.GetListDistinct(w => w.ProductionOrderNo == item.ProductionOrderNo && w.ComponentCode == item.ComponentCode && w.ComponentLineNo == item.ComponentLineNo && w.MovementType == "261").Sum(s => s.DemandQty);
                if (productionFeeding.MovementType == "261")//投料
                {
                    //订单总需求量
                    var orderSum = orderApp.GetSapOrderDetailList(w => w.ProductionOrderNo == item.ProductionOrderNo && w.ComponentCode == item.ComponentCode && w.ComponentLineNo == item.ComponentLineNo && string.IsNullOrEmpty(w.IsDeleteSAP)).Sum(s => s.DemandQty);
                    mark = feedSum + item.DemandQty <= orderSum;
                    if (!mark) break;
                }
                else//退料
                {
                    //已退数量
                    var returnSum = detailApp.GetListDistinct(w => w.ProductionOrderNo == item.ProductionOrderNo && w.ComponentCode == item.ComponentCode && w.ComponentLineNo == item.ComponentLineNo && w.MovementType == "262").Sum(s => s.DemandQty);
                    mark = returnSum + item.DemandQty <= feedSum;
                    if (!mark) break;
                }
            }
            return mark;
        }

        [HttpPost]
        public IHttpActionResult Add([FromBody]PP_ProductionFeeding_Dto productionFeeding)
        {
            var result = new ResponseData();
            try
            {
                if(!CheckQty(productionFeeding))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "请核实本次投料/退料数量";
                    return Json(result);
                }

                Sys_User user = GetCurrentUser();
                var feeding = new PP_ProductionFeeding
                {
                    ScanningCode = productionFeeding.ScanningCode,
                    SerialNo = productionFeeding.SerialNo,
                    ProductionFeedingNo = productionFeeding.ProductionFeedingNo,
                    ProductionOrderNo = productionFeeding.ProductionOrderNo,
                    FactoryCode = productionFeeding.FactoryCode,
                    MaterialNo = productionFeeding.MaterialNo,
                    MaterialName = productionFeeding.MaterialName,
                    MovementType = productionFeeding.MovementType,
                    OrderType = productionFeeding.OrderType,
                    OrderQty = productionFeeding.OrderQty,
                    Unit = productionFeeding.Unit,
                    ContractNo = productionFeeding.ContractNo,
                    SalesOrderNo = productionFeeding.SalesOrderNo,
                    Shippers = productionFeeding.Shippers,
                    ProductionLineCode = productionFeeding.ProductionLineCode,
                    ProductionLineDes = productionFeeding.ProductionLineDes,
                    ProductionScheduler = productionFeeding.ProductionScheduler,
                    DeliveryTime = productionFeeding.DeliveryTime,
                    StartTime = productionFeeding.StartTime,
                    ManualPostTime = productionFeeding.ManualPostTime,
                    Remark = productionFeeding.Remark,
                    CUser = user.LoginAccount,
                };
                var detailList = productionFeeding.DetailList;
                detailList.ForEach(item =>
                {
                    item.ProductionFeedingNo = productionFeeding.ProductionFeedingNo;
                    item.MovementType = productionFeeding.MovementType;
                    item.ManualPostTime = productionFeeding.ManualPostTime;
                    item.CUser = user.LoginAccount;
                });

                if (!_app.Add(feeding, detailList))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                }
                else
                {
                    //是否自动过账判定
                    if (new Sys_SwithConfigApp().IsPPProductionFeedingAutoPost)
                    {
                        string error_message = "";
                        bool bSubmit = _app.DoPost(new List<PP_ProductionFeeding> { feeding }, user.LoginAccount, out error_message);

                        if (!bSubmit)
                        {
                            result.Message = "保存成功；过账失败，失败原因：" + error_message;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 更新

        [HttpPost]
        public IHttpActionResult Update([FromBody]PP_ProductionFeeding_Dto productionFeeding)
        {
            var result = new ResponseData();
            try
            {
                if (!CheckQty(productionFeeding))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "请核实本次投料/退料数量";
                    return Json(result);
                }

                Sys_User user = GetCurrentUser();
                if (!_app.Edit(productionFeeding, user.LoginAccount))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除

        [HttpDelete]
        public IHttpActionResult Delete([FromBody]string[] DocNums)
        {
            var result = new ResponseData();
            try
            {
                Sys_User user = GetCurrentUser();
                var list = _app.GetList(t => DocNums.Contains(t.ProductionFeedingNo)).ToList();
                if (list.Any(w => w.IsPosted))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "ui.Message.PODeletePostingWarning";
                }
                else
                {
                    var detailList = detailApp.GetList(w => DocNums.Contains(w.ProductionFeedingNo)).ToList();
                    _app.Delete(user.LoginAccount, list, detailList);
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 过账

        [HttpPost]
        public IHttpActionResult DoPost([FromBody]List<PP_ProductionFeeding> entities)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                Sys_User currentUser = GetCurrentUser();
                bool bSubmit = _app.DoPost(entities, currentUser.LoginAccount, out error_message);

                if (!bSubmit)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 导出

        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri]Pagination page, [FromUri]string keyword, [FromUri]string ProductionLineDes, [FromUri]string ProductionScheduler, [FromUri] DateTime[] dateValue, [FromUri]bool? isPosted)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];

                var list = _app.GetList(
                    t => (string.IsNullOrEmpty(keyword)
                        || t.SerialNo.Contains(keyword)
                        || t.ProductionFeedingNo.Contains(keyword)
                        || t.ProductionOrderNo.Contains(keyword)
                        || t.MaterialNo.Contains(keyword)
                        || t.MaterialName.Contains(keyword)
                        || t.ContractNo.Contains(keyword)
                        || t.SalesOrderNo.Contains(keyword)
                        || t.Shippers.Contains(keyword))
                        && (string.IsNullOrEmpty(ProductionLineDes) || t.ProductionLineCode.Contains(ProductionLineDes) || t.ProductionLineDes.Contains(ProductionLineDes))
                        && (string.IsNullOrEmpty(ProductionScheduler) || t.ProductionScheduler.Contains(ProductionScheduler))
                        && (t.StartTime >= fromTime && t.StartTime < toTime)
                        && (isPosted == null || t.IsPosted == isPosted)
                        && t.MovementType == "261")
                    .Select(s => s.ProductionFeedingNo)
                    .ToList();

                var itemsData = detailApp.GetList(t => list.Contains(t.ProductionFeedingNo)).ToList();

                List<ExcelColumn<PP_ProductionFeedingDetail>> columns = ExcelService.FetchDefaultColumnList<PP_ProductionFeedingDetail>();
                string[] ignoreField = new string[]
                {
                    "ID"
                    ,"FeedingLineNo"
                    ,"AssessmentCategory"
                    ,"AssessmentType"
                    ,"SpecialInventory"
                    ,"IsBackflush"
                    ,"MovementType"
                    ,"IsPosted"
                    ,"PostUser"
                    ,"PostTime"
                    ,"ManualPostTime"
                    ,"SapDocNum"
                    ,"SapLine"
                    ,"IsDelete"
                    ,"MUser"
                    ,"MTime"
                    ,"DUser"
                    ,"DTime"
                };
                List<ExcelColumn<PP_ProductionFeedingDetail>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<PP_ProductionFeedingDetail> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                //columns.ForEach((column) =>
                //{
                //    if (column.ColumnName == "IsPosted")
                //    {
                //        column.Formattor = ExcelExportFormatter.IsOrNoFormatter;
                //    }
                //});

                return ExportToExcelFile<PP_ProductionFeedingDetail>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #region 导入

        [HttpPost]
        public IHttpActionResult ImportExcelToData([FromBody]DataTable entitys)
        {
            var result = new ResponseData();
            try
            {
                if (entitys == null || entitys.Rows.Count < 1)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "数据不能为空！";
                    return Json(result);
                }

                Sys_User user = GetCurrentUser();
                List<PP_ProductionFeedingDetail> feedingDetails = new List<PP_ProductionFeedingDetail>();
                foreach (DataRow item in entitys.Rows)
                {
                    feedingDetails.Add(new PP_ProductionFeedingDetail
                    {
                        ProductionOrderNo = item[0].ToString(),
                        ReservedNo = item[1].ToString(),
                        ComponentLineNo = Convert.ToDecimal(item[2] ?? 0),
                        ComponentCode = item[3].ToString(),
                        MaterialName = item[4].ToString(),
                        DemandQty = Convert.ToDecimal(item[5] ?? 0),
                        ComponentUnit = item[6].ToString(),
                        FactoryCode = item[7].ToString(),
                        DeliverLocation = item[8].ToString(),
                        SalesOrderNo = item[9].ToString(),
                        SalesOrderLineNo = Convert.ToDecimal(item[10]?.ToString() == "" ? 0 : item[10]),
                        AssessmentCategory = item[11].ToString(),
                        AssessmentType = item[12].ToString(),
                        SpecialInventory = item[13].ToString(),
                        ManualPostTime = Convert.ToDateTime(item[14] ?? DateTime.Now),
                        Remark = item[15].ToString(),
                        MovementType = "261",
                        IsBackflush = "",
                        CUser = user.LoginAccount,
                    });
                }
                if (feedingDetails.Any(
                    a => string.IsNullOrEmpty(a.ProductionOrderNo)
                    || string.IsNullOrEmpty(a.ReservedNo)
                    || string.IsNullOrEmpty(a.ComponentCode)
                    || string.IsNullOrEmpty(a.DeliverLocation)
                    || a.DemandQty == 0
                    ))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = $"请检查数据完善性！";
                    return Json(result);
                }
                //分组生成主表
                var feedList = new List<PP_ProductionFeeding>();
                var group = feedingDetails.GroupBy(g => g.ProductionOrderNo);
                foreach (var item in group)
                {
                    var order = orderApp.GetFirstEntity(f => f.ProductionOrderNo == item.Key);
                    if (order == null)
                    {
                        order = reportApp.GetOrderNoSerialNoByNo(item.Key.ToString())?.ToList().FirstOrDefault();
                    }
                    if (order == null)
                    {
                        result.Code = (int)WMSStatusCode.Failed;
                        result.Message = $"{item.Key}工单无效！";
                        return Json(result);
                    }
                    var feeding = new PP_ProductionFeeding
                    {
                        //SerialNo = "",
                        ProductionFeedingNo = base.GenerateDocNum(DocType.PP, DocFixedNumDef.PP_ProductionFeeding),
                        ProductionOrderNo = item.Key,
                        FactoryCode = order?.FactoryCode,
                        MaterialNo = order?.MaterialNo,
                        MaterialName = order?.MaterialName,
                        MovementType = "261",
                        OrderType = order?.OrderType,
                        OrderQty = order?.OrderQty ?? 0,
                        Unit = order?.Unit,
                        ContractNo = order?.ContractNo,
                        SalesOrderNo = order?.SalesOrderNo,
                        Shippers = order?.Shippers,
                        ProductionLineCode = order?.ProductionLineCode,
                        ProductionLineDes = order?.ProductionLineDes,
                        ProductionScheduler = order?.ProductionScheduler,
                        DeliveryTime = order?.DeliveryTime,
                        StartTime = order?.StartTime,
                        ManualPostTime = item.ToList().FirstOrDefault().ManualPostTime ?? DateTime.Now,
                        Remark = order?.Remark,
                        CUser = user.LoginAccount,
                    };
                    feedList.Add(feeding);

                    foreach (var entity in item)
                    {
                        //判断物料是否有效，物料数量是否正确
                        var detail = orderApp.GetSapOrderDetailList(w => w.ProductionOrderNo == entity.ProductionOrderNo && w.ComponentCode == entity.ComponentCode && w.ReservedNo.ToString() == entity.ReservedNo).ToList().FirstOrDefault();
                        if (detail == null || detail.DemandQty < entity.DemandQty)
                        {
                            result.Code = (int)WMSStatusCode.Failed;
                            result.Message = $"生产订单{entity.ProductionOrderNo}的物料{entity.ComponentCode}不存在或者投料数量超过总需求数量！";
                            return Json(result);
                        }

                        //校验物料数量
                        //已投数量
                        var feedSum = detailApp.GetListDistinct(w => w.ProductionOrderNo == entity.ProductionOrderNo && w.ComponentCode == entity.ComponentCode && w.ComponentLineNo == entity.ComponentLineNo && w.MovementType == "261").Sum(s => s.DemandQty);
                        //订单总需求量
                        var orderSum = orderApp.GetSapOrderDetailList(w => w.ProductionOrderNo == entity.ProductionOrderNo && w.ComponentCode == entity.ComponentCode && w.ComponentLineNo == entity.ComponentLineNo && string.IsNullOrEmpty(w.IsDeleteSAP)).Sum(s => s.DemandQty);
                        if (feedSum + entity.DemandQty > orderSum)
                        {
                            result.Code = (int)WMSStatusCode.Failed;
                            result.Message = $"生产订单{entity.ProductionOrderNo}的物料{entity.ComponentCode},投料总数不能超过总需求数量";
                            return Json(result);
                        }
                        entity.ProductionFeedingNo = feeding?.ProductionFeedingNo;
                    }
                }
                
                if (string.IsNullOrEmpty(result.Message))
                {
                    _app.InsertWithTran(feedList);
                    detailApp.InsertWithTran(feedingDetails);
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 下载模板

        [HttpGet]
        public IHttpActionResult DownExcelModel()
        {
            var result = new ResponseData();
            try
            {
                string modelName = "生产投料-导入模板";
                var itemsData = new List<PP_ProductionFeedingDetail>();
                List<ExcelColumn<PP_ProductionFeedingDetail>> columns = ExcelService.FetchDefaultColumnList<PP_ProductionFeedingDetail>();
                string[] ignoreField = new string[]
                {
                    "ID"
                    ,"ScanningCode"
                    ,"ProductionFeedingNo"
                    ,"FeedingLineNo"
                    ,"MovementType"
                    ,"IsBackflush"
                    ,"IsPosted"
                    ,"PostUser"
                    ,"PostTime"
                    ,"SapDocNum"
                    ,"SapLine"
                    ,"IsDelete"
                    ,"CUser"
                    ,"CTime"
                    ,"MUser"
                    ,"MTime"
                    ,"DUser"
                    ,"DTime"
                };
                List<ExcelColumn<PP_ProductionFeedingDetail>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<PP_ProductionFeedingDetail> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                //columns.ForEach((column) =>
                //{
                //    if (column.ColumnName == "IsPosted")
                //    {
                //        column.Formattor = ExcelExportFormatter.IsOrNoFormatter;
                //    }
                //});

                return ExportToExcelFile<PP_ProductionFeedingDetail>(itemsData, columns, $"{modelName}_{GetCurrentUser().UserName}");
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion
    }
}
