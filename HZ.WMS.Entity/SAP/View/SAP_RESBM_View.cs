using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.SAP.View
{
    /// <summary>
    /// 采购订单组件视图
    /// </summary>
    public class SAP_RESBM_View
    {
        /// <summary>
        /// 采购订单号
        /// </summary>
        [Description("采购订单号")]
        public string BaseNum { get; set; }

        /// <summary>
        /// 采购订单行号
        /// </summary>
        [Description("采购订单行号")]
        public decimal? BaseLine { get; set; }

        /// <summary>
        /// 相关需求的编号
        /// </summary>
        [Description("相关需求的编号")]
        public decimal? RSNUM { get; set; }

        /// <summary>
        /// 相关需求的项目编号
        /// </summary>
        [Description("相关需求的项目编号")]
        public decimal? RSPOS { get; set; }

        /// <summary>
        /// 工厂
        /// </summary>
        [Description("工厂")]
        public string WERKS { get; set; }

        /// <summary>
        /// 物料编号
        /// </summary>
        [Description("物料编号")]
        public string ItemCode { get; set; }

        /// <summary>
        /// 物料名称
        /// </summary>
        [Description("物料名称")]
        public string ItemName { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量")]
        public decimal? Qty { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [Description("单位")]
        public string Unit { get; set; }

        /// <summary>
        /// 供应商编号
        /// </summary>
        [Description("供应商编号")]
        public string SupplierCode { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        [Description("供应商名称")]
        public string SupplierName { get; set; }

        /// <summary>
        /// 产品编号
        /// </summary>
        [Description("产品编号")]
        public string MatnrCode { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        [Description("产品名称")]
        public string MatnrName { get; set; }

        /// <summary>
        /// 产品数量
        /// </summary>
        [Description("产品数量")]
        public decimal? MatnrQty { get; set; }

    }
}
