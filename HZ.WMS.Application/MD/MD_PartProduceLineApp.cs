using System;
using System.Collections.Generic;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.MD.Import;

namespace HZ.WMS.Application.MD
{
    /// <summary>
    /// 安全部件生产线对照
    /// </summary>
    public class MD_PartProduceLineApp : BaseApp<MD_PartProduceLine>
    {
        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public MD_PartProduceLineApp() : base(){}

        #endregion

        #region 导入

        /// <summary>
        /// 导入
        /// </summary>
        /// <param name="excelList">接收的集合</param>
        /// <param name="opUser">当前用户</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool ImportExcelToBaseData(List<MD_PartProduceLineImport> excelList, string opUser, out string error_message)
        {
            error_message = "";
            var flag = true;
            DateTime date = DateTime.Now;
            var entityList = new List<MD_PartProduceLine>();

            try
            {
                foreach (var item in excelList)
                {
                    if (string.IsNullOrEmpty(item.PartName))
                    {
                        error_message = "部件名称不能为空";
                        return false;
                    }

                    if (string.IsNullOrEmpty(item.PartCode))
                    {
                        error_message = "部件编号不能为空";
                        return false;
                    }

                    if (string.IsNullOrEmpty(item.ProduceLineName))
                    {
                        error_message = "生产线名称不能为空";
                        return false;
                    }

                    var entity = new MD_PartProduceLine
                    {
                        Id = Guid.NewGuid().ToString(),
                        PartName = item.PartName,
                        PartCode = item.PartCode,
                        ProduceLineName = item.ProduceLineName,
                        UpOverSpeedCode = item.UpOverSpeedCode,
                        AccidentMoveCode = item.AccidentMoveCode,
                        CUser = opUser,
                        CTime = date,
                        IsDelete = false
                    };

                    entityList.Add(entity);
                }

                if (entityList.Count > 0)
                {
                    Insert(entityList);
                }

                return flag;
            }
            catch (Exception ex)
            {
                error_message = ex.Message;
                return false;
            }
        }

        #endregion
    }
}

