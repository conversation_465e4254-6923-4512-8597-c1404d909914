using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.SAP
{
    public class XZ_SAP_FICO002
    {
        /// <summary>
        /// 当前期间（过账期间）
        /// </summary>
        [Description("当前期间(过账期间)")]
        public int LFMON { get; set; }
        /// <summary>
        /// 当前期间的会计年度 
        /// </summary>
        [Description("当前期间的会计年度 ")]
        public int LFGJA { get; set; }
        /// <summary>
        /// 估价范围(工厂)
        /// </summary>
        [Description("估价范围(工厂)")]
        public string BWKEY { get; set; }

        /// <summary>
        /// 激活状态  X
        /// </summary>
        [Description("激活状态")]
        public string ZSTATU { get; set; }
    }
}
