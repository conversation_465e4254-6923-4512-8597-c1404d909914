using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.MM.ViewModel
{
    /// <summary>
    /// 借出单物料视图
    /// </summary>
    public class MM_LendingOrderItem_View 
    {
        /// <summary>
        /// 物料编码
        /// </summary>
        [Description("物料编码")]
        public string ItemCode { get; set; }

        /// <summary>
        /// 物料描述
        /// </summary>
        [Description("物料描述")]
        public string ItemName { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量")]
        public decimal? Qty { get; set; }

        /// <summary> 
        /// 单位
        /// </summary> 
        [Description("单位")]
        public string Unit { get; set; }


        /// <summary> 
        /// 借出仓库编号
        /// </summary> 
        [Description("借出仓库编号")]
        public string WhsCode { get; set; }

        /// <summary> 
        /// 借出仓库
        /// </summary> 
        [Description("借出仓库")]
        public string WhsName { get; set; }

        /// <summary> 
        /// 区域编号
        /// </summary> 
        [Description("区域编号")]
        public string RegionCode { get; set; }

        /// <summary> 
        /// 区域
        /// </summary> 
        [Description("区域")]
        public string RegionName { get; set; }

        /// <summary> 
        /// 库位编号
        /// </summary> 
        [Description("库位编号")]
        public string BinLocationCode { get; set; }

        /// <summary> 
        /// 库位
        /// </summary> 
        [Description("库位")]
        public string BinLocationName { get; set; }

        /// <summary>
        /// 评估类型
        /// </summary>
        [Description("评估类型")]
        public string AssessType { get; set; }

    }
    
}
