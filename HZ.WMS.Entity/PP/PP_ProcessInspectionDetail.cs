using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.PP
{
    /// <summary>
    /// 生产过程检验明细表
    /// </summary>
    [SugarTable("PP_ProcessInspectionDetail")]
    public class PP_ProcessInspectionDetail : BaseEntity
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Description("主键ID")]
        [SugarColumn(IsPrimaryKey = true)]
        public string ID { get; set; }
        /// <summary>
        /// 序列号
        /// </summary>
        [Description("序列号")]
        public string SerialNo { get; set; }
        /// <summary>
        /// 生产订单
        /// </summary>
        [Description("生产订单")]
        public string ProductionOrderNo { get; set; }
        /// <summary>
        /// 线体编码
        /// </summary>
        [Description("工作中心")]
        public string ProductionLineCode { get; set; }
        /// <summary>
        /// 线体描述
        /// </summary>
        [Description("装配线")]
        public string ProductionLineDes { get; set; }
        /// <summary>
        /// 工序编号
        /// </summary>
        [Description("工序编号")]
        public string WorkingProcedureCode { get; set; }
        /// <summary>
        /// 工序描述
        /// </summary>
        [Description("工序描述")]
        public string WorkingProcedureDes { get; set; }
        /// <summary>
        /// 检验项
        /// </summary>
        [Description("检验项")]
        public string InspectionItem { get; set; }
        /// <summary>
        /// 测量值
        /// </summary>
        [Description("测量值")]
        public decimal? Measurements { get; set; }
        /// <summary>
        /// 是否作废
        /// </summary>
        [Description("是否作废")]
        public bool? IsInvalid { get; set; }
    }
}
