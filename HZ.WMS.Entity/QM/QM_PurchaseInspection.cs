using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.QM
{
    /// <summary>
    /// 采购入库检验
    /// </summary>
    [SugarTable("QM_PurchaseInspection")]
    public class QM_PurchaseInspection : BaseEntity
    {
        /// <summary> 
        /// 采购入库检验主键ID
        /// </summary> 
        [SugarColumn(IsPrimaryKey = true)]
        [Description("采购入库检验主键ID")]
        public string InspectionID { get; set; }

        /// <summary> 
        /// 报检单号
        /// </summary> 
        [Description("报检单号")]
        public string InspectionNum { get; set; }

        /// <summary> 
        /// 检验单行号
        /// </summary> 
        [Description("报检单行号")]
        public int? InspectionLine { get; set; }

        /// <summary> 
        /// wms单号
        /// </summary> 
        [Description("检验单号")]
        public string DocNum { get; set; }

        /// <summary> 
        /// wms行号
        /// </summary> 
        [Description("检验行号")]
        public int? Line { get; set; }

        /// <summary> 
        /// 检验申请编号
        /// </summary> 
        [Description("检验申请编号")]
        public string BaseEntry { get; set; }

        /// <summary> 
        /// 单号
        /// </summary> 
        [Description("采购单号")]
        public string BaseNum { get; set; }

        /// <summary> 
        /// 行号
        /// </summary> 
        [Description("采购单行号")]
        public int? BaseLine { get; set; }

        /// <summary> 
        /// 单号
        /// </summary> 
        [Description("采购订单类型")]
        public string BaseType { get; set; }

        /// <summary> 
        /// 批次
        /// </summary> 
        [Description("批次")]
        public string Batch { get; set; }

        /// <summary> 
        /// 条码
        /// </summary> 
        [Description("条码")]
        public string BarCode { get; set; }

        /// <summary> 
        /// 检验项判定  合格1 不合格2 让步接收3
        /// </summary> 
        [Description("检验项判定")]
        public int? InspectionItem { get; set; }

        /// <summary>
        /// 报检数量
        /// </summary>
        [Description("报检数量")]
        public decimal? InspectionQty { get; set; }

        /// <summary> 
        /// 报检日期
        /// </summary> 
        [Description("报检日期")]
        public DateTime? InspectionTime { get; set; }

        /// <summary>
        /// 仓库编号
        /// </summary>
        [Description("仓库编号")]
        public string WhsCode { get; set; }

        /// <summary>
        /// 仓库名称
        /// </summary>
        [Description("仓库")]
        public string WhsName { get; set; }

        /// <summary> 
        /// 物料编号
        /// </summary> 
        [Description("物料编号")]
        public string ItemCode { get; set; }

        /// <summary> 
        /// 物料名称
        /// </summary> 
        [Description("物料名称")]
        public string ItemName { get; set; }

        /// <summary> 
        /// 采购订单交期
        /// </summary> 
        [Description("采购订单交期")]
        public DateTime? DeliveryDate { get; set; }

        /// <summary> 
        /// 单位
        /// </summary> 
        [Description("单位")]
        public string Unit { get; set; }

        /// <summary> 
        /// 采购数量
        /// </summary> 
        [Description("采购数量")]
        public decimal? PurchaseQty { get; set; }

        /// <summary> 
        /// 合格数量
        /// </summary> 
        [Description("合格数量")]
        public decimal? QualifiedQty { get; set; }

        /// <summary> 
        /// 不合格数量
        /// </summary> 
        [Description("不合格数量")]
        public decimal? UnqualifiedQty { get; set; }

        /// <summary> 
        /// 供应商编号
        /// </summary> 
        [Description("供应商编号")]
        public string SupplierCode { get; set; }

        /// <summary> 
        /// 供应商名称
        /// </summary> 
        [Description("供应商名称")]
        public string SupplierName { get; set; }

        /// <summary> 
        /// 是否过账
        /// </summary> 
        [Description("是否过账")]
        public bool? IsPosted { get; set; }

        /// <summary> 
        /// 过账人
        /// </summary> 
        [Description("过账人")]
        public string PostUser { get; set; }

        /// <summary> 
        /// 过账时间
        /// </summary> 
        [Description("过账时间")]
        public DateTime? PostTime { get; set; }

        /// <summary>
        /// 销售订单号
        /// </summary>
        [Description("销售订单号")]
        public string SalesOrderNum { get; set; }

        /// <summary>
        /// 销售订单行号
        /// </summary>
        [Description("销售订单行号")]
        public int? SalesOrderLine { get; set; }

        /// <summary>
        /// 公司代码
        /// </summary>
        [Description("公司代码")]
        public string CompanyCode { get; set; }

        /// <summary>
        /// 工厂代码
        /// </summary>
        [Description("工厂代码")]
        public string FactoryCode { get; set; }



    }
}
