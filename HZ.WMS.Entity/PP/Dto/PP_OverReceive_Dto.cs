using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.PP.Dto
{
    /// <summary>
    /// 超额领料
    /// </summary>
    public class PP_OverReceive_Dto : PP_OverReceive
    {
        /// <summary>
        /// 明细
        /// </summary>
        public List<PP_OverReceiveDetail> DetailList { get; set; }
        /// <summary>
        /// 删除明细id
        /// </summary>
        public string[] DelDetailIds { get; set; }
        /// <summary>
        /// 手动过账时间
        /// </summary>
        public DateTime? ManualPostTime { get; set; }
    }

    /// <summary>
    /// 超领料审核
    /// </summary>
    public class Examine
    {
        /// <summary>
        /// 审核实体
        /// </summary>
        public List<PP_OverReceive> entities { get; set; }
        /// <summary>
        /// 审核状态
        /// </summary>
        public int status { get; set; }
    }
}
