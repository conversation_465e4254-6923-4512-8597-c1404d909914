using System;
using System.Collections.Generic;
using System.ComponentModel;
using HZ.WMS.Entity;
using SqlSugar;

namespace AOS.OMS.Entity.Sale
{
    /// <summary>
    /// 订单类
    /// </summary>
    public class SD_Cable_Sale_OrderInfo : BaseEntity
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Description("主键ID")]
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 订单编号
        /// </summary>
        [Description("订单编号")]
        public string OrderNum { get; set; }

        /// <summary>
        /// 订单行
        /// </summary>
        [Description("订单行")]
        public int LineNum { get; set; }

        /// <summary>
        /// Sap行号
        /// </summary>
        [Description("Sap行号")]
        public int? SapLine { get; set; }

        /// <summary>
        /// 客户编号
        /// </summary>
        [Description("客户编号")]
        public string CustomerCode { get; set; }

        /// <summary>
        /// 客户名称
        /// </summary>
        [Description("客户名称")]
        public string CustomerName { get; set; }

        /// <summary>
        /// 客户地址
        /// </summary>
        [Description("客户地址")]
        public string CustomerAddress { get; set; }

        /// <summary>
        /// 客户结算地址
        /// </summary>
        [Description("客户结算地址")]
        public string SettlementAdd { get; set; }

        /// <summary>
        /// 订单状态
        /// </summary>
        [Description("订单状态")]
        public int Status { get; set; }

        /// <summary>
        /// 发运状态
        /// </summary>
        [Description("发运状态")]
        public int ShipmentStatus { get; set; }

        /// <summary>
        /// 订单类型(1:主机订单 2:部件订单 3:售后部件 4:售后主机)
        /// </summary>
        [Description("订单类型")]
        public int OrderType { get; set; }

        /// <summary>
        /// 订单日期
        /// </summary>
        [Description("订单日期")]
        public DateTime? OrderDate { get; set; }

        /// <summary>
        /// 发货日期
        /// </summary>
        [Description("发货日期")]
        public DateTime? DeliveryDate { get; set; }

        /// <summary>
        /// 过账用户
        /// </summary>
        [Description("过账用户")]
        public string PostUser { get; set; }

        /// <summary>
        /// 过账日期
        /// </summary>
        [Description("过账日期")]
        public DateTime? PostDate { get; set; }

        /// <summary>
        /// 过账状态
        /// </summary>
        [Description("过账状态")]
        public int? PostStatus { get; set; }

        /// <summary>
        /// 同步SAP销售单号
        /// </summary>
        [Description("同步SAP销售单号")]
        public string SapNo { get; set; }

        /// <summary>
        /// 发货时间
        /// </summary>
        [Description("发货时间")]
        public DateTime DeliveryTime { get; set; }

        /// <summary>
        /// 排产时间
        /// </summary>
        [Description("排产时间")]
        public DateTime? ProductionTime { get; set; }

        /// <summary>
        /// 合同号
        /// </summary>
        [Description("合同号")]
        public string ContractNo { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        [Description("项目名称")]
        public string ProjectDisc { get; set; }

        /// <summary>
        /// 履行备注
        /// </summary>
        [Description("履行备注")]
        public string PerformRemark { get; set; }

        /// <summary>
        /// 生产备注
        /// </summary>
        [Description("生产备注")]
        public string ProductionRemark { get; set; }

        /// <summary>
        /// 发运日期
        /// </summary>
        [Description("发运日期")]
        public DateTime? ShipmentDate { get; set; }

        /// <summary>
        /// 客户订单号
        /// </summary>
        [Description("客户订单号")]
        public string CustomerOrderNum { get; set; }

        /// <summary>
        /// 货币
        /// </summary>
        [Description("货币")]
        public string HuoBi { get; set; }

        /// <summary>
        /// 价格
        /// </summary>
        [Description("价格")]
        public decimal? Price { get; set; }

        /// <summary>
        /// 含税价格
        /// </summary>
        [Description("含税价格")]
        public decimal? TaxRatePrice { get; set; }

        /// <summary>
        /// 批次
        /// </summary>
        [Description("批次")]
        public string BatchNum { get; set; }

        /// <summary>
        /// 暂估运费
        /// </summary>
        [Description("暂估运费")]
        public decimal? Freight { get; set; }

        /// <summary>
        /// 催货日期
        /// </summary>
        [Description("催货日期")]
        public DateTime? ExpeditingDate { get; set; }

        /// <summary>
        /// 排产序号
        /// </summary>
        [Description("排产序号")]
        public string ProductionNo { get; set; }

        /// <summary>
        /// 订单行备注
        /// </summary>
        [Description("订单行备注")]
        public string LineRemark { get; set; }

        /// <summary>
        /// 件号
        /// </summary>
        [Description("件号")]
        public string PartCode { get; set; }

        /// <summary>
        /// 序号
        /// </summary>
        [Description("序号")]
        public string SerialNo { get; set; }

        /// <summary>
        /// 客户订单行
        /// </summary>
        [Description("客户订单行")]
        public string CustomerOrderLine { get; set; }

        /// <summary>
        /// 发运单号
        /// </summary>
        [Description("发运单号")]
        public string ShipmentNum { get; set; }

        /// <summary>
        /// 设置发运时间
        /// </summary>
        [Description("设置发运时间")]
        public DateTime? SetShipmentDate { get; set; }

        /// <summary>
        /// 发运下载标记
        /// </summary>
        [Description("发运下载标记")]
        public int ShipmentDownFlag { get; set; }

        /// <summary>
        /// 设置发运用户
        /// </summary>
        [Description("设置发运用户")]
        public string SetShipmentUser { get; set; }

        /// <summary>
        /// 工单打印状态
        /// </summary>
        [Description("工单打印状态")]
        public int WorkOrderPrintStatus { get; set; }

        /// <summary>
        /// 标签打印状态
        /// </summary>
        [Description("标签打印状态")]
        public int LabelPrintStatus { get; set; }
        
        /// <summary>
        /// 唛头打印状态
        /// </summary>
        [Description("唛头打印状态")]
        public int ShippingMarkPrintStatus { get; set; }
        
        /// <summary>
        /// 电梯类型
        /// </summary>
        [Description("电梯类型")]
        public string ElevatorType { get; set; }

        /// <summary>
        /// 订单明细
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<SD_Cable_Sale_OrderDetails> OrderDetailList { get; set; }

        /// <summary>
        /// 订单参数明细
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public SD_Cable_Sale_Parameter SaleParameter { get; set; }

        /// <summary>
        /// 合同号参数列表
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<SD_Cable_OrderSerialNum> OrderSerialNumList { get; set; }
        
    }
}
