using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;
using HZ.Core.Http;
using HZ.Core.Office;
using HZ.WMS.Application;
using HZ.WMS.Application.MM;
using HZ.WMS.Application.Sys;
using HZ.WMS.Entity.MM;
using HZ.WMS.Entity.Sys;
using HZ.WMS.WebAPI.Controllers;

namespace HZ.WMS.WebAPI.Areas.MM
{
    /// <summary>
    /// 设备领料
    /// </summary>
    public class MM_StockTodoPickingController : ApiBaseController
    {
        #region 初始化

        private MM_StockTodoPickingApp _app = new MM_StockTodoPickingApp();
        private Sys_DictionaryApp _dictionaryapp = new Sys_DictionaryApp();

        #endregion

        #region PC

        #region 获取系统生成的单号

        /// <summary>
        /// 获取系统生成的单号
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetDocNum()
        {
            var result = new ResponseData();
            try
            {
                result.Data = base.GenerateDocNum(DocType.MM, DocFixedNumDef.MM_EquipmentPicking);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 查询分页列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="page">分页</param>
        /// <param name="keyword">参数</param>
        /// <param name="dateValue">日期区间</param>
        /// <param name="isPosted">是否过账</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri] Pagination page, [FromUri] string keyword,
            [FromUri] DateTime[] dateValue, [FromUri] int? auditStatus)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];

                // 确保排序字段正确
                if (string.IsNullOrEmpty(page.Sort))
                {
                    page.Sort = "CTime desc";
                }

                var itemsData = _app.GetPageList(page, t => (string.IsNullOrEmpty(keyword)
                                                             || t.DocNum.Contains(keyword)
                                                             || t.ItemCode.Contains(keyword) ||
                                                             t.ItemName.Contains(keyword)
                    ) && (auditStatus == null || auditStatus.Value == t.AuditStatus)
                    && (t.CTime >= fromTime && t.CTime <= toTime)).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 保存

        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="Parameters">设备领料列表</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult SavePC([FromBody] List<MM_StockTodoPicking> parameters)
        {
            var result = new ResponseData();
            try
            {
                string message = "";
                string docNum = "";
                Sys_User currentUser = GetCurrentUser();
                bool bSubmit = _app.Save(parameters, currentUser.LoginAccount, out message, out docNum);
                if (!bSubmit)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = message;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Data = docNum;
                    result.Message = message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="entities">接收的集合</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Update([FromBody] List<MM_StockTodoPicking> entities)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                Sys_User currentUser = GetCurrentUser();

                if (_app.Update(entities) > 0)
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2000;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                    result.MessageParam = 2010;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="ids">根据ID进行删除</param>
        /// <returns></returns>
        [HttpDelete]
        //[AllowAnonymous]
        public IHttpActionResult Delete([FromBody] string[] ids)
        {
            //[FromBody]string[] ids
            var result = new ResponseData();
            try
            {
                string error_message = "";
                bool bDelete = _app.Deletes(ids, GetCurrentUser().LoginAccount, out error_message);
                if (!bDelete || !string.IsNullOrEmpty(error_message))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 导出

        /// <summary>
        /// 导出
        /// </summary>
        /// <param name="keyword">参数</param>
        /// <param name="dateValue">日期区间</param>
        /// <param name="isPosted">是否过账</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] string keyword, [FromUri] DateTime[] dateValue,
            [FromUri] bool? isPosted, [FromUri] bool? IsCancel)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];
                var itemsData = _app.GetList(t => (string.IsNullOrEmpty(keyword)
                                                   || t.DocNum.Contains(keyword)
                                                   || t.ItemCode.Contains(keyword) || t.ItemName.Contains(keyword)
                    ) && (t.CTime >= fromTime && t.CTime <= toTime)).ToList();
                //var itemsData = _app.GetList().ToList();
                var columns = ExcelService.FetchDefaultColumnList<MM_StockTodoPicking>();
                string[] ignoreField = new string[]
                {
                    "ID", "IsDelete", "MUser", "MTime", "DUser", "DTime",
                };
                var ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (var userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }

                columns.ForEach((column) =>
                {
                    if (column.ColumnName == "IsPosted")
                    {
                        column.Formattor = ExcelExportFormatter.IsOrNoFormatterForBoolean;
                    }
                    else if (column.ColumnName == "IsCancel")
                    {
                        column.Formattor = ExcelExportFormatter.IsOrNoFormatterForBoolean;
                    }
                });

                return ExportToExcelFile<MM_StockTodoPicking>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #region 审核

        /// <summary>
        /// 审核
        /// </summary>
        /// <param name="ids">根据ID进行审核</param>
        /// <returns></returns>
        [HttpGet]
        //[AllowAnonymous]
        public IHttpActionResult Audit([FromUri] string[] ids)
        {
            //[FromBody]string[] ids
            var result = new ResponseData();
            try
            {
                string error_message = "";
                string type = ""; //类型 1:提交失败 2：过账失败
                bool bAudits = _app.Audits(ids, GetCurrentUser().LoginAccount, out error_message, out type);
                if (!bAudits && type == "1")
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
                else if (!bAudits && type == "2")
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2010;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2000;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 取消

        /// <summary>
        /// 取消
        /// </summary>
        /// <param name="ids">根据ID进行取消</param>
        /// <returns></returns>
        [HttpGet]
        //[AllowAnonymous]
        public IHttpActionResult Reject([FromUri] string[] ids)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                bool bRejects = _app.Rejects(ids, GetCurrentUser().LoginAccount, out error_message);
                if (!bRejects)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 导入模板

        /// <summary>
        /// 导入模板
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelModel()
        {
            var result = new ResponseData();
            try
            {
                var columns = ExcelService.FetchDefaultColumnList<MM_StockTodoPicking>();
                string modelName = "设备领料-导入模板";
                //排除不需要的列名称 字符串
                string[] ignoreField = new string[]
                {
                    "ID", "HandleName", "IsDelete", "CUser", "CTime", "MUser", "MTime", "DUser", "DTime"
                };
                var itemsData = new List<MM_StockTodoPicking>() { };
                var ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<MM_StockTodoPicking> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }

                return ExportToExcelFile<MM_StockTodoPicking>(itemsData, columns,
                    GetCurrentUser().UserName + "_" + modelName);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #endregion

        
    }
}