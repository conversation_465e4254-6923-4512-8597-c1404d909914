using System;
using System.ComponentModel;
using SqlSugar;
namespace HZ.WMS.Entity.Sys
{
    /// <summary>
    /// sap链接配置
    /// </summary>
    [SugarTable("Sys_SAPCompanyInfo")]
    public class Sys_SAPCompanyInfo : BaseEntity
    {
        /// <summary>
        /// DictionaryID
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.String, IsPrimaryKey = true)]
        public string CompanyInfoId { get; set; }

        public string CompanyCode { get; set; }

        public string SAPName { get; set; }

        public string SAPAppServerHost { get; set; }

        public string SAPClient { get; set; }

        public string SAPUser { get; set; }

        public string SAPPassword { get; set; }

        public string  SAPSystemNumber { get; set; }
    }
}