using System;
using System.ComponentModel;
using HZ.WMS.Entity;
using SqlSugar;

namespace AOS.OMS.Entity.Sale
{
    /// <summary>
    /// 订单类
    /// </summary>
    public class SD_Cable_Sale_PartOrderDetails : BaseEntity
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Description("主键ID")]
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }
        
        /// <summary>
        /// 父主键ID
        /// </summary>
        [Description("父主键ID")]
        public string Pid { get; set; }
        
        /// <summary>
        /// 是否装箱
        /// </summary>
        [Description("是否装箱")]
        public int IsPacking { get; set; }

        /// <summary>
        /// 订单编号
        /// </summary>
        [Description("订单编号")]
        public string OrderNum { get; set; }

        /// <summary>
        /// 订单行号
        /// </summary>
        [Description("订单行号")]
        public int? LineNum { get; set; }

        /// <summary>
        /// OMS订单行号
        /// </summary>
        [Description("OMS订单行号")]
        public int? OmsLineNum { get; set; }

        /// <summary>
        /// 订单状态
        /// </summary>
        [Description("订单状态")]
        public string Status { get; set; }

        /// <summary>
        /// 订单类型(1:主机订单 2:部件订单 3:售后部件 4:售后主机)
        /// </summary>
        [Description("订单类型")]
        public int OrderType { get; set; }

        /// <summary>
        /// 过账用户
        /// </summary>
        [Description("过账用户")]
        public string PostUser { get; set; }

        /// <summary>
        /// 过账日期
        /// </summary>
        [Description("过账日期")]
        public DateTime? PostDate { get; set; }

        /// <summary>
        /// 同步SAP销售单号
        /// </summary>
        [Description("同步SAP销售单号")]
        public string SapNo { get; set; }
        
        /// <summary>
        /// 同步SAP销售单号
        /// </summary>
        [Description("同步SAP销售行号")]
        public int SapLine { get; set; }

        /// <summary>
        /// 物料编码
        /// </summary>
        [Description("物料编码")]
        public string MaterialCode { get; set; }

        /// <summary>
        /// 富沃德编码
        /// </summary>
        [Description("富沃德编码")]
        public string FwdCode { get; set; }

        /// <summary>
        /// 客户件号
        /// </summary>
        [Description("客户件号")]
        public string CustomerPartNo { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量")]
        public decimal Quantity { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [Description("单位")]
        public string Unit { get; set; }

        /// <summary>
        /// 物料描述
        /// </summary>
        [Description("物料描述")]
        public string MaterialDes { get; set; }

        /// <summary>
        /// 价格
        /// </summary>
        [Description("价格")]
        public decimal? ExcludingTaxPrice { get; set; }

        /// <summary>
        /// 客户单号
        /// </summary>
        [Description("客户单号")]
        public string CustomOrderNo { get; set; }

        /// <summary>
        /// 下单时间
        /// </summary>
        [Description("下单时间")]
        public DateTime? OrderDate { get; set; }
        
        /// <summary>
        /// 订单行备注
        /// </summary>
        [Description("订单行备注")]
        public string LineRemark { get; set; }
        
    }
}
