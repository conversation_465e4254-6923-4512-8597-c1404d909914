using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;
using HZ.Core.Http;
using HZ.Core.Office;
using HZ.WMS.Application;
using HZ.WMS.Application.MD;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.Sys;
using HZ.WMS.WebAPI.Controllers;

namespace HZ.WMS.WebAPI.Areas.MD.Controllers
{
    /// <summary>
    /// 销售运费里程
    /// </summary>
    public class MD_FreightMileageController : ApiBaseController
    {
        private MD_FreightMileageApp _app = new MD_FreightMileageApp();
        private MD_Province_City_DistrictApp _cityApp = new MD_Province_City_DistrictApp();

        #region 查询分页列表

        /// <summary>
        /// 查询分页列表 
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]string keyword)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetPageList(page, x => (string.IsNullOrEmpty(keyword)
                                                            || x.Region.Contains(keyword)
                                                            || x.Province.Contains(keyword)
                                                            || x.City.Contains(keyword)
                                                            || x.SettlementAdd.Contains(keyword)
                                                            || x.SupplierCode.Contains(keyword)
                                                            || x.SupplierName.Contains(keyword)
                                                            ))?.ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 查询所有信息

        /// <summary>
        /// 查询所有供应商信息 
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetGetSupplierList()
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetSupplierList()?.ToList();
                result.Data = itemsData;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <returns></returns>
        [HttpDelete]
        public IHttpActionResult Delete([FromBody]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.DeleteByKeys(ids, GetCurrentUser().LoginAccount);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 添加
        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Add([FromBody]MD_FreightMileage entity)
        {
            var result = new ResponseData();
            try
            {
                Sys_User currLoginUser = GetCurrentUser();
                entity.IsDelete = false;
                entity.CUser = currLoginUser.LoginAccount;
                entity.CTime = DateTime.Now;
                result.Data = _app.Insert(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Update([FromBody]MD_FreightMileage entity)
        {
            var result = new ResponseData();
            try
            {
                entity.MUser = GetCurrentUser().LoginAccount;
                entity.MTime = DateTime.Now;
                result.Data = _app.Update(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 导出

        /// <summary>
        /// 
        /// </summary>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] string keyword)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetList().Where(x => string.IsNullOrEmpty(keyword)
                                                           || x.Region.Contains(keyword)
                                                           || x.Province.Contains(keyword)
                                                           || x.City.Contains(keyword)
                                                           || x.SettlementAdd.Contains(keyword)
                                                           || x.SupplierCode.Contains(keyword)
                                                           || x.SupplierName.Contains(keyword)
                                                           || x.SettlementAdd.Contains(keyword)
                                                        ).ToList();
                //var itemsData = _app.GetList().ToList();
                List<ExcelColumn<MD_FreightMileage>> columns = ExcelService.FetchDefaultColumnList<MD_FreightMileage>();
                string[] ignoreField = new string[] 
                {
                    "RegionID","Province","City","Region","SupplierCode","","IsDelete", "DTime", "DUser","CUser","CTime","MUser","MTime"
                };

                List<ExcelColumn<MD_FreightMileage>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<MD_FreightMileage> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }

                columns.ForEach((column) =>
                {
                    //if (column.ColumnName == "CTime" || column.ColumnName == "MTime")
                    //{
                    //    column.Formattor = ExcelExportFormatter.DateTimeFormatter;
                    //}
                    if (column.ColumnName == "IsZF")
                    {
                        column.Formattor = ExcelExportFormatter.IsOrNoFormatterForBoolean;
                    }
                });

                return ExportToExcelFile(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }
        #endregion

        #region 查询省-市-区/县 三级联动

        /// <summary>
        /// 查询省-市-区/县 三级联动 
        /// </summary>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IHttpActionResult GetProvince_City_District( [FromUri]string ParentId)
        {
            var result = new ResponseData();
            try
            {
                var itemsData=new object();
                if (!string.IsNullOrEmpty(ParentId) && ParentId != "0" )
                {
                    itemsData = _cityApp.GetList(x => x.ParentId == Convert.ToInt32(ParentId))?.ToList();
                }
                else
                {
                    itemsData = _cityApp.GetList(x => x.ParentId == 0)?.ToList();
                }
                result.Data =  itemsData;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

    }
}
