using System;
using System.ComponentModel;
//Chloe框架

namespace HZ.WMS.Entity.SD
{
    /// <summary>
    /// 接收OMS发运计划的信息
    /// </summary>
    public class Sale_ShippingForOms
    {
        
        /// <summary> 
        /// Pid
        /// </summary> 
        [Description("Pid")]
        public string Pid { get; set; }

        /// <summary> 
        /// 客户订单号
        /// </summary> 
        [Description("客户订单号")]
        public string CustomerOrderNo { get; set; }

        /// <summary> 
        /// 订单类型
        /// </summary> 
        [Description("订单类型")]
        public string OrderType { get; set; }

        /// <summary> 
        /// 销售单号
        /// </summary> 
        [Description("销售单号")]
        public string SaleSapNo { get; set; }

        /// <summary> 
        /// 销售单行号
        /// </summary> 
        [Description("销售单行号")]
        public int? SaleSapLine { get; set; }

        /// <summary> 
        /// 销售类型
        /// </summary> 
        [Description("销售类型")]
        public string SaleType { get; set; }

        /// <summary> 
        /// 销售组织
        /// </summary> 
        [Description("销售组织")]
        public string SaleOrganization { get; set; }

        /// <summary> 
        /// 发货日期
        /// </summary> 
        [Description("发货日期")]
        public DateTime? DeliveryDate { get; set; }

        /// <summary> 
        /// 凭证日期
        /// </summary> 
        [Description("凭证日期")]
        public DateTime? VoucherDate { get; set; }

        /// <summary> 
        /// 客户编号
        /// </summary> 
        [Description("客户编号")]
        public string CustomerCode { get; set; }

        /// <summary> 
        /// 客户名称
        /// </summary> 
        [Description("客户名称")]
        public string CustomerName { get; set; }

        /// <summary> 
        /// 客户地址
        /// </summary> 
        [Description("客户地址")]
        public string CustomerAdd { get; set; }

        /// <summary> 
        /// 销售交货批
        /// </summary> 
        [Description("销售交货批")]
        public string BatchNum { get; set; }

        /// <summary> 
        /// 物料编号
        /// </summary> 
        [Description("物料编号")]
        public string ItemCode { get; set; }

        /// <summary> 
        /// 物料名称
        /// </summary> 
        [Description("物料名称")]
        public string ItemName { get; set; }

        /// <summary> 
        /// 数量
        /// </summary> 
        [Description("数量")]
        public decimal? Quantity { get; set; }

        /// <summary> 
        /// 合同单号
        /// </summary> 
        [Description("合同单号")]
        public string ContractNo { get; set; }

        /// <summary> 
        /// 结算地址
        /// </summary> 
        [Description("结算地址")]
        public string SettlementAdd { get; set; }
        

        /// <summary> 
        /// 承诺的交货日期
        /// </summary> 
        [Description("承诺的交货日期")]
        public DateTime? PromisedDeliveryDate{ get; set; }


        /// <summary> 
        /// 项目名称
        /// </summary> 
        [Description("项目名称")]
        public string ProjectName { get; set; }

        /// <summary> 
        /// 客户件号
        /// 211123-cc添加
        /// </summary> 
        [Description("客户件号")]
        public string CustomerPart { get; set; }

        /// <summary> 
        /// 明细备注
        /// 220412-cc添加
        /// </summary> 
        [Description("明细备注")]
        public string Remark { get; set; }

        /// <summary> 
        /// 生产备注
        /// 220412-cc添加
        /// </summary> 
        [Description("生产备注")]
        public string ProductionRemark { get; set; }

        /// <summary> 
        /// 发货方式
        /// 220412-cc添加
        /// </summary> 
        [Description("发货方式")]
        public string ShippingType { get; set; }
        
        /// <summary>
        /// 联系人
        /// </summary>
        [Description("联系人")]
        public string Contact { get; set; }

        /// <summary>
        /// 联系方式
        /// </summary>
        [Description("联系方式")]
        public string Telephone { get; set; }
        
        /// <summary>
        /// 创建人
        /// </summary>
        [Description("创建人")]
        public string CUser { get; set; }


    }
}
