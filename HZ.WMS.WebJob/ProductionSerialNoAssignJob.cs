using HZ.Core.Logging;
using HZ.WMS.Application.PP;
using Quartz;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.WebJob
{
    public class ProductionSerialNoAssignJob : IJob
    {
        PP_ProductionOrderApp _app = new PP_ProductionOrderApp();
        public Task Execute(IJobExecutionContext context)
        {
            LogHelper.Instance.LogDebug("生产订单序列号分配任务开始执行：" + DateTime.Now.ToString());
            return Task.Run(() => 
            {
                //_app.UpdateDataBySAP("WebJob");
                LogHelper.Instance.LogDebug("生产订单序列号分配任务执行结束：" + DateTime.Now.ToString());
            });
        }
    }

    #region 废弃

    //public class AutoPostGrabJob : IJob
    //{
    //    V_NotPostStockMoveApp _postApp = new V_NotPostStockMoveApp();
    //    public Task Execute(IJobExecutionContext context)
    //    {
    //        LogHelper.Instance.LogDebug("自动过账任务开始执行：" + DateTime.Now.ToString());
    //        return Task.Run(() =>
    //        {
    //            _postApp.AutoPostData();
    //            LogHelper.Instance.LogDebug("自动过账任务执行结束：" + DateTime.Now.ToString());
    //        });
    //    }
    //}

    //public class MaterialReqJob : IJob
    //{
    //    PP_MaterialReqKanbanApp PP_MaterialReqKanbanApp = new PP_MaterialReqKanbanApp();

    //    /// <summary>
    //    /// 备料看板数据采集任务（已废弃）
    //    /// </summary>
    //    /// <param name="context"></param>
    //    /// <returns></returns>
    //    public Task Execute(IJobExecutionContext context)
    //    {
    //        return PP_MaterialReqKanbanApp.GenerateData();
    //    }
    //}

    //public class ProductionOrderGrabJob : IJob
    //{
    //    PP_ProductionOrderApp PP_ProductionOrderApp = new PP_ProductionOrderApp();

    //    /// <summary>
    //    /// 生产订单数据采集任务
    //    /// </summary>
    //    /// <param name="context"></param>
    //    /// <returns></returns>
    //    public Task Execute(IJobExecutionContext context)
    //    {
    //        HZ.Core.Logging.LogHelper.Instance.LogDebug("生产订单数据采集任务执行开始:" + DateTime.Now.ToString());
    //        return PP_ProductionOrderApp.GrabData().ContinueWith((task)=>{
    //            HZ.Core.Logging.LogHelper.Instance.LogDebug("生产订单数据采集任务执行结束:" + DateTime.Now.ToString());
    //        });
    //    }
    //}

    //public class PurchaseOrderGrabJob : IJob
    //{

    //    SAP_PO_PurchaseOrderApp c_sap_purchaseorderapp = new SAP_PO_PurchaseOrderApp();

    //    /// <summary>
    //    /// 采购订单数据采集任务
    //    /// </summary>
    //    /// <param name="context"></param>
    //    /// <returns></returns>
    //    public Task Execute(IJobExecutionContext context)
    //    {
    //        HZ.Core.Logging.LogHelper.Instance.LogDebug("采购订单数据采集任务执行开始:" + DateTime.Now.ToString());
    //        return Task.Run(() =>
    //        {
    //            c_sap_purchaseorderapp.GrabPurchaseOrder();
    //            HZ.Core.Logging.LogHelper.Instance.LogDebug("采购订单数据采集任务执行结束:" + DateTime.Now.ToString());
    //        });
    //    }
    //}

    #endregion
}
