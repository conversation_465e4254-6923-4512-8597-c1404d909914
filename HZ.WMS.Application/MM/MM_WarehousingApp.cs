using SqlSugar;
using HZ.Core.Http;
using HZ.WMS.Application.MD;
using HZ.WMS.Application.QM;
using HZ.WMS.Application.SAP;
using HZ.WMS.Application.Sys;
using HZ.WMS.Entity;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.MM;
using HZ.WMS.Entity.MM.Parameters;
using HZ.WMS.Entity.MM.ViewModel;
using HZ.WMS.Entity.SAP;
using HZ.WMS.Entity.SAP.View;
using HZ.WMS.Entity.SRM;
using HZ.WMS.Entity.Sys;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Linq.Expressions;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace HZ.WMS.Application.MM
{
    /// <summary>
    /// 委外入库
    /// </summary>
    public class MM_WarehousingApp : BaseApp<MM_Warehousing>
    {
        #region 初始化

        public class MM_WarehousingDetailApp : BaseApp<MM_WarehousingDetail> { }

        private MM_WarehousingDetailApp _detailApp = new MM_WarehousingDetailApp();

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public MM_WarehousingApp() : base(){}

        BaseApp<BaseEntity> _baseApp = new BaseApp<BaseEntity>();
        MD_StockApp _stockApp = new MD_StockApp();

        #endregion

        #region PC

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="DocNums">单号数组</param>
        /// <param name="opUser">当前用户</param>
        /// <param name="error_message">错误消息</param>
        public bool Deletes(string[] IDs, string opUser, out string error_message)
        {
            bool bDeleted = true;
            var list = new List<MM_Warehousing>();
            var DetailList = new List<MM_WarehousingDetail>();

            foreach (string key in IDs)
            {
                var item = GetList(x => x.ID == key)?.ToList();
                  list.AddRange(item);

                var itemdetail = _detailApp.GetList(x => x.DocNum == item[0].DocNum&& x.BaseNum == item[0].BaseNum&&x.BaseLine == item[0].BaseLine)?.ToList();
                  DetailList.AddRange(itemdetail);
            }
            //List<PO_ReturnScan> entities = GetListByKeys(ids);
            if (CheckDelete(list, out error_message))
            {
                try
                {
                    DbContext.Ado.BeginTran();
                    _detailApp.DbContext = DbContext;

                    //删除主表信息
                    Delete(list, opUser);

                    //删除明细表信息
                    _detailApp.Delete(DetailList, opUser);

                    DbContext.Ado.CommitTran();
                }
                catch (Exception ex)
                {
                    bDeleted = false;
                    error_message = ex.Message;
                    if (DbContext.Ado.IsAnyTran())
                        DbContext.Ado.RollbackTran();
                }
            }
            else
            {
                bDeleted = false;
            }
            return bDeleted;
        }

        /// <summary>
        /// 删除校验
        /// </summary>
        /// <param name="entities">集合</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        private bool CheckDelete(List<MM_Warehousing> entities, out string error_message)
        {
            bool isPass = true;
            error_message = "";
            foreach (var ow in entities)
            {
                if (ow.IsPosted == true)
                {
                    error_message = ow.DocNum+ ":已过账数据不允许删除!"; // "已过账数据不允许删除!";
                    isPass = false;
                    break;
                }

                //    // 入库校验
                //    //if(_stockApp.CheckStockLocked(po.ItemCode, po.OutBinLocationCode, po.OutRegionCode))
                //    //{
                //    //    //throw new Exception("Common.error", new Exception("Common.StockIsLocked"));
                //    //    error_message = "Common.StockIsLocked"; //库存锁定，不允许出入库
                //    //    return false;
                //    //}
            }

            return isPass;
        }

        #endregion

        #region 过账

        /// <summary>
        /// 过账
        /// </summary>
        /// <param name="entities">主信息</param>
        /// <param name="entitieDetails">明细信息 如果明细信息不为空则是自动过账，为空则是PC界面过账</param>
        /// <param name="opUser">当前用户</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool DoPost(List<MM_Warehousing> entities,List<MM_WarehousingDetail> entitieDetails, string opUser, out string error_message)
        {
            error_message = "";
            try
            {
                MD_StockApp _stockApp = new MD_StockApp();
                Sys_SAPCompanyInfoApp _SAPCompanyInfoApp = new Sys_SAPCompanyInfoApp();
                MD_BinLocationApp _binLocationApp = new MD_BinLocationApp();
                QM_PurchaseInspectionApp _insApp = new QM_PurchaseInspectionApp();
                //多条数据后根据单号进行分组
                var mainInfoList = entities.GroupBy(g => new
                {
                    g.DocNum
                }).Select(q => new
                {
                    DocNum = q.Key.DocNum
                });

                //按订单号分组
                foreach (var mainInfo in mainInfoList)
                {
                    var list = new List<ZFGWMS002>();
                    var list1 = new List<ZFGWMS002_1>();
                    int Line = 0;
                    var wardetaList = new List<MM_WarehousingDetail>();
                    //查询主表信息
                    var warList = entities.Where(x => x.DocNum == mainInfo.DocNum)?.ToList();

                    if (entitieDetails != null && entitieDetails.Count > 0)//手持端添加信息后自动过账，不需要在查询一次
                    {
                        //查询明细信息
                        wardetaList = entitieDetails.Where(x => x.DocNum == mainInfo.DocNum && x.IsPosted == false)?.ToList();
                    }
                    else
                    {
                        wardetaList = _detailApp.GetList(x => x.DocNum == mainInfo.DocNum && x.IsPosted == false)?.ToList();
                    }

                    warList.ForEach(x =>
                    {
                        list.Add(new ZFGWMS002
                        {
                            ZNUM = Convert.ToInt32(x.InspectionLine),
                            EBELN = x.BaseNum,
                            EBELP = x.BaseLine,
                            MATNR = x.ItemCode,
                            WERKS = x.FactoryCode ?? "2002",//purchaseReceipt.FactoryCode
                            BWART = x.MovementType ?? "101", //purchasereceipt.MovementType;
                            MENGE = x.Qty,
                            MEINS = "",//condition.Unit,
                            LGORT = x.WhsCode
                        });
                    });

                    //组件信息
                    foreach (var x in wardetaList)
                    {
                        var itemBinlocation = _binLocationApp.GetSAPItemBinLocation(x.ItemCode);
                        if (itemBinlocation== null)
                        {
                            error_message = "物料编号["+x.ItemCode+"]没有在SAP设置默认地点";
                            return false;
                        }
                        x.Line = Line += 1;
                        var y = new ZFGWMS002_1();
                        y.EBELN = x.BaseNum;
                        y.EBELP = Convert.ToInt32(x.BaseLine);
                        y.RSNUM = Convert.ToInt32(x.RSNUM);
                        y.RSPOS = Convert.ToInt32(x.RSPOS);
                        y.MATNR = x.ItemCode;
                        y.WERKS = x.FactoryCode ?? "2002";
                        //BWART ="";
                        y.BDMNG = System.Math.Abs(Convert.ToDecimal(x.Qty));
                        y.MEINS = x.Unit;
                        y.LIFNR = x.SupplierCode;
                        y.BWTAR = "";
                        y.SGTXT = x.Remark;
                        if(x.SHKZG=="S")
                            y.LGORT = itemBinlocation.WhsCode;
                        list1.Add(y);

                        //list1.Add(new ZFGWMS002_1
                        //{
                        //    EBELN = x.BaseNum,
                        //    EBELP = Convert.ToInt32(x.BaseLine),
                        //    RSNUM =Convert.ToInt32(x.RSNUM),
                        //    RSPOS =Convert.ToInt32(x.RSPOS),
                        //    MATNR = x.ItemCode,
                        //    WERKS = x.FactoryCode ?? "2002",
                        //    //BWART ="",
                        //    BDMNG = System.Math.Abs(Convert.ToDecimal(x.Qty)),
                        //    MEINS = x.Unit,
                        //    LIFNR = x.SupplierCode,
                        //    BWTAR = "",
                        //    SGTXT = x.Remark,
                        //    LGORT= itemBinlocation.WhsCode

                        //});
                    };

                    bool ispost = false;
                    DateTime date = DateTime.Now;
                    //查询Sap账期时间
                    DateTime ManualPostTime = new SAPApp().GetSAPpostTime(Convert.ToDateTime(warList[0].ManualPostTime));
                    List<SAPRETURN> saplist = _SAPCompanyInfoApp.ZFGWMS002(warList[0].CompanyCode, mainInfo.DocNum, "", ManualPostTime, list, list1, out ispost, out error_message);
                    if (saplist != null && saplist.Count > 0)
                    {

                        //DbContext.Ado.BeginTran();
                        //_stockApp.DbContext = this.DbContext;
                        foreach (var sap in saplist)
                        {
                            var query1 = wardetaList.Where(x => x.DocNum == mainInfo.DocNum && x.BaseNum == sap.basenum && x.BaseLine == sap.baseline).ToList();
                            foreach (var querydetail in query1)
                            {
                                querydetail.IsPosted = true;
                                querydetail.PostUser = opUser;
                                querydetail.PostTime = date;
                                querydetail.MUser = opUser;
                                querydetail.MTime = date;
                                querydetail.SapDocNum = sap.sapDocNum;
                                querydetail.SapLine = sap.sapline;

                                //更新明细信息
                                if (_detailApp.Update(querydetail) > 0)
                                {
                                    if (querydetail.SHKZG == "H")//不是废料的出库
                                    {
                                        //库存出库
                                        string outStockMsg = string.Empty;
                                        if (!_stockApp.StockOut(querydetail.ItemCode, querydetail.SupplierCode, "O", querydetail.Qty, opUser, out outStockMsg))
                                        {
                                            error_message = outStockMsg;
                                            //return false;
                                        }
                                    }
                                    else//是废料的添加库存
                                    {
                                        //待完善，完善内容 事务，库存缺少销售订单信息。
                                        // 插入收货记录成功才能更新库存
                                        var itemBinlocation=_binLocationApp.GetSAPItemBinLocation(querydetail.ItemCode);
                                        string inStockMsg = string.Empty;
                                        MD_Stock stock = new MD_Stock();
                                        stock.BarCode = "";
                                        stock.BatchNum = "";
                                        stock.ItemCode = querydetail.ItemCode.Trim();
                                        stock.ItemName = querydetail.ItemName.Trim();
                                        //if (itemBinlocation ==null)
                                        //{
                                        //    itemBinlocation.WhsCode = "";
                                        //    itemBinlocation.WhsName = "";
                                        //}
                                        stock.WhsCode = itemBinlocation.WhsCode;
                                        stock.WhsName = itemBinlocation.WhsName;
                                        stock.RegionCode = itemBinlocation.RegionCode;
                                        stock.RegionName = itemBinlocation.RegionName;
                                        stock.BinLocationCode = itemBinlocation.BinLocationCode;
                                        stock.BinLocationName = itemBinlocation.BinLocationName;
                                        stock.Qty = System.Math.Abs(Convert.ToDecimal(querydetail.Qty));//绝对值
                                        //stock.Unit = query.Unit;
                                        stock.IsDelete = false;
                                        //stock.IsPurchase = 1;
                                        stock.SaleNum = "";
                                        stock.SaleLine = 0;
                                        if (!string.IsNullOrEmpty(stock.SaleNum.Trim()))
                                            stock.SpecialStock = "E";//销售库存

                                        if (!_stockApp.StockIn(stock, opUser, out inStockMsg))
                                        {
                                            error_message = inStockMsg;
                                            //return false;
                                        }
                                    }
                                   
                                }
                            }



                            //更新主信息
                            var query = entities.Where(x => x.DocNum == mainInfo.DocNum && x.BaseNum == sap.basenum && x.BaseLine == sap.baseline).ToList().FirstOrDefault();
                            if (query != null)
                            {
                                query.IsPosted = true;
                                query.PostUser = opUser;
                                query.PostTime = date;
                                query.MUser = opUser;
                                query.MTime = date;
                                query.ManualPostTime = ManualPostTime;
                                query.SapDocNum = sap.sapDocNum;
                                query.SapLine = sap.sapline;
                                query.SAPmark = "S";

                                //更新主信息
                                if (Update(query) > 0)
                                {
                                    //待完善，完善内容 事务，库存缺少销售订单信息。
                                    // 插入收货记录成功才能更新库存
                                    string inStockMsg = string.Empty;
                                    MD_Stock stock = new MD_Stock();
                                    stock.BarCode = "";
                                    stock.BatchNum = "";
                                    stock.ItemCode = query.ItemCode.Trim();
                                    stock.ItemName = query.ItemName.Trim();
                                    stock.WhsCode = query.WhsCode;
                                    stock.WhsName = query.WhsName;
                                    stock.RegionCode = query.RegionCode;
                                    stock.RegionName = query.RegionName;
                                    stock.BinLocationCode = query.BinLocationCode;
                                    stock.BinLocationName = query.BinLocationName;
                                    stock.Qty = query.Qty;
                                    //stock.Unit = query.Unit;
                                    stock.IsDelete = false;
                                    //stock.IsPurchase = 1;
                                    stock.SaleNum = query.SaleNo ?? "";
                                    stock.SaleLine = Convert.ToInt32(query.SaleLineNo);
                                    if (!string.IsNullOrEmpty(stock.SaleNum.Trim()))
                                        stock.SpecialStock = "E";//销售库存

                                    if (!_stockApp.StockIn(stock, opUser, out inStockMsg))
                                    {
                                        error_message = inStockMsg;
                                        //return false;
                                    }


                                    //过账成功后回写SRM报检单入库数量
                                    var insquery = DbContextForSRM.Queryable<P_InspectionDetail>().Where(t => t.InspectionNo == query.InspectionNum && t.OrderNo == query.BaseNum
                                                                                             && t.OrderLine == query.BaseLine && t.ItemCode == query.ItemCode
                                                                                             && t.IsDelete == false).ToList().FirstOrDefault();
                                    if (insquery != null)
                                    {
                                        if (insquery.StorageQty == null)
                                        {
                                            insquery.StorageQty = 0;
                                        }
                                        insquery.StorageQty += query.Qty;
                                        //insquery.Status = "3";
                                        _insApp.UpdateInspectionForSRM(insquery);
                                    }
                                }



                            }

                            Update(query);
                        }
                        //DbContext.Ado.CommitTran();

                    }
                    else
                    {
                        var query = entities.Where(x => x.DocNum == mainInfo.DocNum).ToList();
                        foreach (var x in query)
                        {
                            x.SAPmark = "E";
                            x.SAPmessage = error_message;
                        }
                        Update(query);

                        error_message = "单号：" + mainInfo.DocNum + "，过账失败，失败原因：" + error_message;
                        return false;
                    }
                }
                if (string.IsNullOrEmpty(error_message))
                {
                    error_message = "过账成功";
                }
                return true;
            }
            catch (Exception ex)
            {
                error_message = ex.Message;
                return false;
            }

        }

        #endregion

        #region 冲销过账

        /// <summary>
        /// 冲销过账
        /// </summary>
        /// <param name="entities"></param>
        /// <param name="opUser"></param>
        /// <param name="error_message"></param>
        /// <returns></returns>
        public bool PassPost(List<MM_Warehousing> entities, string opUser, out string error_message)
        {
            error_message = "";

            foreach (var x in entities)
            {
                if (x.IsPosted == false)
                {
                    error_message = "信息未过帐，不允许冲销过账";
                    return false;
                }

            }
            QM_PurchaseInspectionApp _insApp = new QM_PurchaseInspectionApp();
            Sys_SAPCompanyInfoApp _SAPCompanyInfoApp = new Sys_SAPCompanyInfoApp();
            MD_StockApp _stockApp = new MD_StockApp();
            MD_BinLocationApp _binLocationApp = new MD_BinLocationApp();
            try
            {

                var DocNums = entities.Select(x => x.DocNum).Distinct().ToArray();
                var conditionList1 = _detailApp.GetList(x => DocNums.Contains(x.DocNum) && x.IsPosted == true)?.ToList();

                //多条数据后根据单号进行分组
                var mainInfoList = conditionList1.GroupBy(g => new
                {
                    g.SapDocNum
                }).Select(q => new
                {
                    SapDocNum = q.Key.SapDocNum
                });

                //按订单号分组
                foreach (var mainInfo in mainInfoList)
                {
                    int Line = 0;

                    List<string> list = new List<string>();
                    var conditionList = conditionList1.Where(x => x.SapDocNum == mainInfo.SapDocNum)?.ToList();
                    conditionList.ForEach(x =>
                    {
                        list.Add(x.SapLine.ToString());

                    });

                    bool ispost = false;
                    DateTime time = DateTime.Now;
                    DateTime? ManualPostTime1 = entities.Where(x => x.DocNum == conditionList[0].DocNum)?.ToList()[0].ManualPostTime;
                    //查询Sap账期时间
                    DateTime ManualPostTime = new SAPApp().GetSAPpostTime(Convert.ToDateTime(ManualPostTime1));
                    //purchaseReceipt.CompanyCode 公司代码
                    var saplist = _SAPCompanyInfoApp.ZFGWMS021("001", mainInfo.SapDocNum, list, ManualPostTime, out ispost, out error_message);
                    if (string.IsNullOrEmpty(error_message))
                    {
                        foreach (var query1 in conditionList)
                        {

                            query1.IsDelete = true;
                            query1.DUser = opUser;
                            query1.DTime = time;
                            query1.Remark = query1.Remark + "已冲销";

                            if (_detailApp.Update(query1) > 0)//更新成功后更新库存
                            {
                                #region 更新库存
                                if (query1.SHKZG == "H")//不是废料的出库
                                {
                                    //待完善，完善内容 事务，库存缺少销售订单信息。
                                    // 插入收货记录成功才能更新库存
                                    var itemBinlocation = _binLocationApp.GetSAPItemBinLocation(query1.ItemCode);
                                    string inStockMsg = string.Empty;
                                    MD_Stock stock = new MD_Stock();
                                    stock.BarCode = "";
                                    stock.BatchNum = "";
                                    stock.ItemCode = query1.ItemCode.Trim();
                                    stock.ItemName = query1.ItemName.Trim();
                                    //if (itemBinlocation ==null)
                                    //{
                                    //    itemBinlocation.WhsCode = "";
                                    //    itemBinlocation.WhsName = "";
                                    //}
                                    stock.WhsCode = itemBinlocation.WhsCode;
                                    stock.WhsName = itemBinlocation.WhsName;
                                    stock.RegionCode = itemBinlocation.RegionCode;
                                    stock.RegionName = itemBinlocation.RegionName;
                                    stock.BinLocationCode = itemBinlocation.BinLocationCode;
                                    stock.BinLocationName = itemBinlocation.BinLocationName;
                                    stock.Qty = System.Math.Abs(Convert.ToDecimal(query1.Qty));//绝对值
                                                                                               //stock.Unit = query.Unit;
                                    stock.IsDelete = false;
                                    //stock.IsPurchase = 1;
                                    stock.SaleNum = "";
                                    stock.SaleLine = 0;
                                    if (!string.IsNullOrEmpty(stock.SaleNum.Trim()))
                                        stock.SpecialStock = "E";//销售库存

                                    if (!_stockApp.StockIn(stock, opUser, out inStockMsg))
                                    {
                                        error_message = inStockMsg;
                                        //return false;
                                    }
                                    
                                }
                                else//是废料的添加库存
                                {
                                    //库存出库
                                    string outStockMsg = string.Empty;
                                    if (!_stockApp.StockOut(query1.ItemCode, query1.SupplierCode, "O", query1.Qty, opUser, out outStockMsg))
                                    {
                                        error_message = outStockMsg;
                                        //return false;
                                    }
                                }
                                #endregion

                                //queryList.Add(query);
                            }
                        }

                        var query = entities.Where(x => x.DocNum == conditionList[0].DocNum).ToList().FirstOrDefault();
                        if (query != null)
                        {
                            query.IsDelete = true;
                            query.DUser = opUser;
                            query.DTime = time;
                            query.SAPmark = "已冲销";
                        }
                        //更新
                        if (Update(query) > 0)
                        {
                            string outStockMsg = string.Empty;
                            if (!_stockApp.StockOut(query.ItemCode, query.BinLocationCode,  query.Qty, opUser, out outStockMsg))
                            {
                                error_message = outStockMsg;
                                //return false;
                            }


                            //过账成功后回写SRM报检单入库数量
                            var insquery = DbContextForSRM.Queryable<P_InspectionDetail>().Where(t => t.InspectionNo == query.InspectionNum && t.OrderNo == query.BaseNum
                                                                                     && t.OrderLine == query.BaseLine && t.ItemCode == query.ItemCode
                                                                                     && t.IsDelete == false).ToList().FirstOrDefault();
                            if (insquery != null)
                            {
                                if (insquery.StorageQty == null)
                                {
                                    insquery.StorageQty = 0;
                                }
                                insquery.StorageQty -= query.Qty;
                                //insquery.Status = "3";
                                _insApp.UpdateInspectionForSRM(insquery);
                            }
                        }
                    }
                    else
                    {

                        error_message = "单号：" + conditionList[0].DocNum + "，冲销过账失败，失败原因：" + error_message;
                        return false;
                    }
                }
                if (string.IsNullOrEmpty(error_message))
                {
                    error_message = "冲销过账成功";
                }
                return true;
            }
            catch (Exception ex)
            {
                error_message = ex.Message;
                return false;
            }
        }

        #endregion

        #region 查询导出数据

        /// <summary>
        /// 查询导出数据
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        public ISugarQueryable<MM_WarehousingExport_View> GetWarehousingExport_View(Expression<Func<MM_WarehousingExport_View, bool>> condition)
        {
            var query = DbContext.Queryable<MM_WarehousingExport_View>().Where(condition);
            return query;

        }

        #endregion

        #endregion

        #region Mobile

        #region 查询SRM报检单信息

        /// <summary>
        ///  数据校验
        /// </summary>
        /// <param name="InspectionNum">报检单号</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool CheckDate(string InspectionNum, out string error_message)
        {
            error_message = "";
            var query = DbContextForSRM.Queryable<P_InspectionDetail>().Where(x => x.InspectionNo == InspectionNum).ToList().FirstOrDefault();
            if (query == null)
            {
                error_message = "报检单号[" + InspectionNum + "]无效，请检查数据！";
                return false;
            }
            return true;
        }

        /// <summary>
        /// 查询SRM报检单信息
        /// </summary>
        /// <param name="InspectionNum">报检单号</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public List<MM_WarehousingForSRMInspection_View> GetSRMInspection(string InspectionNum, out string error_message)
        {
            error_message = "";
            var query = DbContext.Queryable<MM_WarehousingForSRMInspection_View>()
                .Where(x => x.InspectionNo == InspectionNum)
                .OrderBy(t => t.ItemName)
             .OrderBy(r => r.ItemCode)
             .OrderBy(r => r.OrderNo)
             .OrderBy(r => r.Remark).ToList();
            if (query == null || query.Count <= 0)
            {
                error_message = "检验单号[" + InspectionNum + "]已收货完成！";
            }
            return query;
        }

        #endregion

        #region 委外入库查询采购订单组件信息

        /// <summary>
        /// 委外入库查询采购订单组件信息
        /// </summary>
        /// <param name="BaseNum">采购订单号</param>
        /// <param name="BaseLine">采购订单行号</param>
        /// <returns></returns>
        public List<MM_WarehousingForRESBM_View> GetWarehousingForRESBM(string BaseNum, int BaseLine, decimal InspectionQty, out string error_message)
        {
            error_message = "";
            var query = DbContext.Queryable<MM_WarehousingForRESBM_View>().Where(i => i.BaseNum == BaseNum && i.BaseLine == Convert.ToDecimal(BaseLine)).ToList();
            if (query == null || query.Count <= 0)
            {
                error_message = "采购订单号[" + BaseNum + "],行号[" + BaseLine + "]未查询到组件信息";
            }
            else
            {
                query.ForEach(x=> 
                {
                    if (InspectionQty!=x.EKPOQty)
                    {
                        if (x.SHKZG == "H")
                            x.Qty = x.Proportion * InspectionQty;
                        else if (x.SHKZG == "S")
                            x.Qty = x.Proportion * InspectionQty*-1;

                        x.Qty = Math.Round(Convert.ToDecimal(x.Qty),4);
                    }
                });
            }
            return query;
        }

        #endregion

        #region 保存

        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="entities">集合</param>
        /// <param name="ManualPostTime">手动过账时间</param>
        /// <param name="user">用户信息</param>
        /// <param name="error_message">错误消息返回</param>
        /// <param name="type">类型 1:提交失败 2：过账失败</param>
        public bool Save(List<MM_WarehousingDto> entities, DateTime ManualPostTime, string user, out string error_message, out string type)
        {
            error_message = "提交成功";
            type = "";
            MD_StockApp _stockApp = new MD_StockApp();
            try
            {

                foreach (var x in entities)
                {
                    //foreach (var y in x.Resbm)
                    //{
                    //    if (y.SHKZG == "H")
                    //    {
                    //        //校验出库库存
                    //        string errorMsg = string.Empty;
                    //        if (!_stockApp.ValidateStockOutSAP(y.ItemCode, y.SupplierCode, "O", y.Qty, out errorMsg, null))
                    //        {
                    //            error_message = errorMsg;
                    //            type = "1";
                    //            return false;
                    //        }
                    //    }
                    //}
                    //校验检验单是否有效
                    GetSRMInspection(x.InspectionNum, out error_message);
                    if (!string.IsNullOrEmpty(error_message))
                        return false;
                }

                DateTime date = DateTime.Now;
                string DocNum = _baseApp.GetNewDocNum(DocType.MM, DocFixedNumDef.MM_Warehousing);
                var list = new List<P_InspectionDetail>();
                var query = new List<MM_Warehousing>();
                var queryList = new List<MM_WarehousingDetail>();
                
                //调用仓库系统参数
                var jsonArray = new JArray();
                List<string> whsCodes = new List<string>();
                whsCodes.Add("X008");
                whsCodes.Add("X002");

                foreach (var x in entities)
                {
                    query.Add(new MM_Warehousing
                    {
                        DocNum = DocNum,
                        InspectionNum = x.InspectionNum,
                        InspectionLine=x.InspectionLine,
                        SupplierCode = x.Resbm[0].SupplierCode,
                        SupplierName = x.Resbm[0].SupplierName,
                        BaseNum = x.BaseNum,
                        BaseLine = x.BaseLine,
                        ItemCode = x.ItemCode,
                        ItemName=x.ItemName,
                        IsDelete = false,
                        CUser = user,
                        CTime = date,
                        IsPosted = false,
                        Qty=x.InspectionQty,
                        WhsCode = x.WhsCode,
                        WhsName = x.WhsName,
                        RegionCode = x.RegionCode,
                        RegionName = x.RegionName,
                        BinLocationCode = x.BinLocationCode,
                        BinLocationName = x.BinLocationName,
                        FactoryCode="2002",
                        MovementType="101",
                        CompanyCode = "001",
                        ManualPostTime=ManualPostTime,
                        Remark=x.Remark

                    });

                    foreach (var y in x.Resbm)
                    {
                        if (y.Qty != 0)
                        {
                            queryList.Add(new MM_WarehousingDetail
                            {
                                FactoryCode = "2002",
                                DocNum = DocNum,
                                BaseNum = y.BaseNum,
                                BaseLine = Convert.ToInt32(y.BaseLine),
                                ItemCode = y.ItemCode,
                                ItemName = y.ItemName,
                                SupplierCode = y.SupplierCode,
                                SupplierName = y.SupplierName,
                                Qty = y.Qty,
                                Unit = y.Unit,
                                IsDelete = false,
                                IsPosted = false,
                                CUser = user,
                                CTime = date,
                                RSNUM = y.RSNUM,
                                RSPOS = y.RSPOS,
                                SHKZG = y.SHKZG
                            });
                        }
                    }
                    
                    if (whsCodes.Contains(x.WhsCode))
                    {
                        var jsonObject = new JObject();
                        jsonObject.Add("inOutNumber",new JValue(x.InspectionQty));
                        jsonObject.Add("inspectionFormNumber",new JValue(x.InspectionNum));
                        jsonObject.Add("inOutType",new JValue(3));
                        jsonObject.Add("operationType",1);
                        var jsonObj = new JObject();
                        jsonObj.Add("partCode",new JValue(x.ItemCode));
                        jsonObject.Add("materialStorageInfo",jsonObj);
                        jsonArray.Add(jsonObject);
                    }
                }
                
                
                // 第三方入库
                try
                {
                    //调用生成发运的WMS接口
                    string postRst = HttpPost("http://se.hzforward.com/prod-api/material/storageInOut/materialReceipt", jsonArray.ToString(), "POST");
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                }

                DbContext.Ado.BeginTran();

                Insert(query);
                //批量插入
                _detailApp.Insert(queryList);

                DbContext.Ado.CommitTran();

                foreach (var x in entities)
                {
                    //查询是否已经完成入库
                    var z = DbContext.Queryable<MM_WarehousingForSRMInspection_View>().Where(z1 => z1.InspectionNo == x.InspectionNum && z1.OrderNo == x.BaseNum
                                                                                 && z1.OrderLine == x.BaseLine && z1.ItemCode == x.ItemCode).ToList().FirstOrDefault();
                    if (z == null)
                    {
                        var insquery = DbContextForSRM.Queryable<P_InspectionDetail>().Where(t => t.InspectionNo == x.InspectionNum && t.OrderNo == x.BaseNum
                                                                                       && t.OrderLine == x.BaseLine && t.ItemCode == x.ItemCode
                                                                                       && t.IsDelete == false).ToList().FirstOrDefault();
                        if (insquery != null)
                        {
                            insquery.QualityRasult = 1;
                            insquery.QualifiedQty += x.InspectionQty;
                            insquery.UnQualifiedQty = 0;
                            insquery.MUser = user;
                            insquery.MTime = date;
                            insquery.QualityUser = user;
                            insquery.QualityDate = date;
                            insquery.QualityRemark = "";
                            //insquery.Status = "3";//完成
                            list.Add(insquery);
                        }
                    }
                    else
                    {
                        var insquery = DbContextForSRM.Queryable<P_InspectionDetail>().Where(t => t.InspectionNo == x.InspectionNum && t.OrderNo == x.BaseNum
                                                                                   && t.OrderLine == x.BaseLine && t.ItemCode == x.ItemCode
                                                                                   && t.IsDelete == false).ToList().FirstOrDefault();
                        if (insquery != null)
                        {
                            insquery.QualityRasult = 1;
                            insquery.QualifiedQty += x.InspectionQty;
                            insquery.UnQualifiedQty = 0;
                            insquery.MUser = user;
                            insquery.MTime = date;
                            insquery.QualityUser = user;
                            insquery.QualityDate = date;
                            insquery.QualityRemark = "";
                            //insquery.Status = "2";// 正在收货
                            list.Add(insquery);
                        }
                    }
                }
                new QM_PurchaseInspectionApp().UpdateInspectionForSRM(list);

                //是否自动过账判定
                Sys_SwithConfigApp _switchApp = new Sys_SwithConfigApp();
                if (_switchApp.IsMMWarehousingAutoPost)
                {
                    string postMsg = "";
                    bool bpost= DoPost(query, queryList, user, out error_message);
                    if (!bpost)
                    {
                        error_message = postMsg;
                        type = "2";
                        return false;
                    }
                    else
                    {
                        error_message = "提交并" + postMsg;
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                if(DbContext.Ado.IsAnyTran())
                    DbContext.Ado.RollbackTran();

                error_message = ex.Message;
                type = "1";
                return false;
            }
        }

        #endregion

        #endregion
        
        
        /// <summary>
        /// 发运计划接口请求
        /// </summary>
        /// <param name="token"></param>
        /// <param name="url"></param>
        /// <param name="body"></param>
        /// <param name="postMethodType"></param>
        /// <returns></returns>
        private string HttpPost(string url, string body, string postMethodType)
        {
            Encoding encoding = Encoding.UTF8;
            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(url);
            request.Method = postMethodType;
            request.Accept = "text/html, application/xhtml+xml, */*";
            request.ContentType = "application/json";
            byte[] buffer = encoding.GetBytes(body);
            request.ContentLength = buffer.Length;
            request.GetRequestStream().Write(buffer, 0, buffer.Length);
            HttpWebResponse response = (HttpWebResponse)request.GetResponse();
            using (StreamReader reader = new StreamReader(response.GetResponseStream(), Encoding.UTF8))
            {
                return reader.ReadToEnd();
            }
        }

    }
}
