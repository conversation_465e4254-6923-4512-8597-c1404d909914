using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Web;
using System.Web.Http;
using HZ.Core.Http;
using HZ.WMS.Application.MD;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.Sys;
using HZ.WMS.WebAPI.Controllers;
using HZ.Core.Office;
using HZ.WMS.Application;

namespace HZ.WMS.WebAPI.Areas.MD.Controllers
{
    /// <summary>
    /// 销售供应商重量费率-A
    /// </summary>
    public class MD_CustomerWeightRateAController : ApiBaseController
    {
        private MD_CustomerWeightRateA_App _app = new MD_CustomerWeightRateA_App();

        #region 查询分页列表

        /// <summary>
        /// 查询分页列表 
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]string keyword)
        {
            var result = new ResponseData();
            try
            {
                //page.Sort = "CustomerCode asc";
                var itemsData = _app.GetPageList(page, x => (string.IsNullOrEmpty(keyword)
                                                            || x.SupplierCode.Contains(keyword)
                                                            || x.SupplierName.Contains(keyword)
                                                            ))?.ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <returns></returns>
        [HttpDelete]
        public IHttpActionResult Delete([FromBody]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.DeleteByKeys(ids, GetCurrentUser().LoginAccount);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 添加
        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Add([FromBody]MD_CustomerWeightRateA entity)
        {
            var result = new ResponseData();
            try
            {
                Sys_User currLoginUser = GetCurrentUser();
                entity.IsDelete = false;
                entity.CUser = currLoginUser.LoginAccount;
                entity.CTime = DateTime.Now;
                result.Data = _app.Insert(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Update([FromBody]MD_CustomerWeightRateA entity)
        {
            var result = new ResponseData();
            try
            {
                entity.MUser = GetCurrentUser().LoginAccount;
                entity.MTime = DateTime.Now;
                result.Data = _app.Update(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 导出

        /// <summary>
        /// 导出
        /// </summary>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] string keyword)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetList().Where(x => string.IsNullOrEmpty(keyword)
                                                           || x.SupplierCode.Contains(keyword)
                                                           || x.SupplierName.Contains(keyword)
                                                        ).ToList();
                //var itemsData = _app.GetList().ToList();
                List<ExcelColumn<MD_CustomerWeightRateA>> columns = ExcelService.FetchDefaultColumnList<MD_CustomerWeightRateA>();
                string[] ignoreField = new string[] 
                {
                    "IsDelete", "DTime", "DUser","RateID", "CTime", "CUser", "MTime", "MUser","SupplierCode"
                };

                List<ExcelColumn<MD_CustomerWeightRateA>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<MD_CustomerWeightRateA> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                columns.ForEach((column) =>
                {
                    //if (column.ColumnName == "CTime")
                    //{
                    //    column.Formattor = ExcelExportFormatter.DateTimeFormatter;
                    //}
                    //if (column.ColumnName == "MTime")
                    //{
                    //    column.Formattor = ExcelExportFormatter.DateTimeFormatter;
                    //}
                });

                return ExportToExcelFile(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }
        #endregion

    }
}
