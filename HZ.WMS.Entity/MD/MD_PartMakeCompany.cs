using SqlSugar;
using System.ComponentModel;

namespace HZ.WMS.Entity.MD
{
    /// <summary>
    /// 安全部件制造单位编号
    /// </summary>
    [SugarTable("MD_PartMakeCompany")]
    public class MD_PartMakeCompany : BaseEntity
    {
        /// <summary>
        /// ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Description("ID")]
        public string Id { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        [Description("公司名称")]
        public string CompanyName { get; set; }

        /// <summary>
        /// 制造单位编号
        /// </summary>
        [Description("制造单位编号")]
        public string CompanyCode { get; set; }

        /// <summary>
        /// 上级公司名称
        /// </summary>
        [Description("上级公司名称")]
        public string SuperCompanyName { get; set; }

    }
}