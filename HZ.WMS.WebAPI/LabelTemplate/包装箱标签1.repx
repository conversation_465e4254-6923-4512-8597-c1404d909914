/// <XRTypeInfo>
///   <AssemblyFullName>DevExpress.XtraReports.v16.1, Version=16.1.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</AssemblyFullName>
///   <AssemblyLocation>C:\Windows\Microsoft.Net\assembly\GAC_MSIL\DevExpress.XtraReports.v16.1\v4.0_16.1.5.0__b88d1754d700e49a\DevExpress.XtraReports.v16.1.dll</AssemblyLocation>
///   <TypeName>DevExpress.XtraReports.UI.XtraReport</TypeName>
///   <Localization>zh-CN</Localization>
///   <Version>18.1</Version>
///   <Resources>
///     <Resource Name="XtraReportSerialization.WmsBarCodesP">
/// zsrvvgEAAACRAAAAbFN5c3RlbS5SZXNvdXJjZXMuUmVzb3VyY2VSZWFkZXIsIG1zY29ybGliLCBWZXJzaW9uPTQuMC4wLjAsIEN1bHR1cmU9bmV1dHJhbCwgUHVibGljS2V5VG9rZW49Yjc3YTVjNTYxOTM0ZTA4OSNTeXN0ZW0uUmVzb3VyY2VzLlJ1bnRpbWVSZXNvdXJjZVNldAIAAAACAAAAAAAAAFBBRFBBRFAg898HOIXbfgAAAABRAAAAaAEAAExwAGkAYwB0AHUAcgBlAEIAbwB4ADEALgBJAG0AYQBnAGUAJgBTAHkAcwB0AGUAbQAuAEQAcgBhAHcAaQBuAGcALgBJAG0AYQBnAGUAAAAAAE5zAHEAbABEAGEAdABhAFMAbwB1AHIAYwBlADEALgBSAGUAcwB1AGwAdABTAGMAaABlAG0AYQBTAGUAcgBpAGEAbABpAHoAYQBiAGwAZQAvCQAAICoJAACJUE5HDQoaCgAA
/// AA1JSERSAAAAlgAAABYIBgAAACKxZToAAAAEZ0FNQQAAsY8L/GEFAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAACLxJREFUaEPt2Xl0VOUZx/GHsAmUELVYFQ0qLqUaay0qRigCFvcF21NobRUXTj1txVpbFzRiyUZCEiAhAcISFsVaRXEBEjOTVcASgSKRFJKQBG2tPVY5Vesu/f7uZMJN5kZJkJCx+ePDkJlk7tz3fe6z3DFLKw1PM4rtm1nrLfmFWkt6ocYSC7p0BvEFNaNmFtYO9t60cNAVWJ1Nn/j86ukZvtqJD6+rNu9NCwddgdWZnBafX+PLKqq744/51TZgzotdgdXloI2n/P2LoJocR6Y6SkGVXBTpvWnh4NAFVl8Mwpm4AKNxOcbjOhf9fCXG4EKcjZMQhe7weu+vE53no5S/vdlF9T94cC2ZKqPMLKnoZPbnWu9NCwftD6x+OAdX4U7MxhN4CTV4
/// H/va6DN8hPfwT1SiFKuQjTjcCgVhDAYiAl6fLxzcgn2Uv4p5xfVHTiNT9XeCyv8tSy9LZ39O9d60cEBgDSSwZrQeWAqgIVBmmYsSbMdO7IU7MD7Hv/EGXkc93sQ7eBcf4FO4/6a9FIRvoQp+zMP9mIDzoaDrCa9zOpx6YTL+C4KqevL84np7gEwVme5kqghLL32evfmlsz/NNiuczChyAmsuJ5dZVKcxtwcnfCJUliZBG/Y4V9WydN/uxTnF9Tn83tQkNjChoGYErw3F8RgABaH+XllEZUyPfRCJYzAY38FwXIaf4TdIxArk4xUoYLyCqS3exhY8hTTcBpVjXST6jF6bfqh0w7nIQWNA1axLLayNzi1tsKlrd1mfQFAZQfUc+7KwaX+a/hNuZiKl2EYt22Kz/Ltt5frXopaUNcQuL98zZnFpw1nzS+pPWVBS32dp2R6btm6X3byq0u54pspyiutsIYvCa19oYWm9zeF9kwtqLYWs6IUg
/// dW+CrmhlG5U6Zcm7oQ1RVtoDZ2MOgspsLQqg8noXdBwd71j0hvvztIcuqmjofdUivIbg8StwwWzfbstmDc9d8rL1SC1Ro66gKmZPyprtT7Mfws1MTizJbwNmldvoFVvtoqWbbSRiMTzvZcfoFVvsuMz1ZvE+ZyGGLa5wfi/4emtiFm6yG5+stAwWkqs0xHQwCRkBTLAG6P8phbXO62yCmzb9VFyC25GFNVBZ/gTuAGoPlXaVVpX71VC2ngb1kMp4yrAKliuggUPDyPW4CQrQDDyDV6G2wP3ea6bnVw/TxUs/5fS057A2Fl9ollrcl6DaxV5sC9mbkCcOj344FkMQg2GIxRhcjp/gp5jgop/Hc2KXWWrJWEvwx1qi/zzE4HScgKMtwddLmY2mMnCsRP+BIRB7kxWjKLeRjNBu/dFvdrld+9g2u23VDvvFE5WOCY9vd3q+JWREbYJ6kLkEXxrBqQDVIxvlFXAqr1OgDVbAqflXSXRvcEeq
/// oF2IIyMP0mfW+SRysYzl4j2CJr0b/S1reTbeQQV6OmvrFvLEV687jsFwKBh+j1n4EzZgOxrwFj7Avq/AJ9iLN1CDrViPVZiP6fgtJmI0hiIKzRdIqV5B2ZpkApCM2STBZ9HZGyxm0Sb7du5fLHreRhu5bLPpTvQfnvsbje4uJ6up1OZSapXxPLKbqLdRWdX0qlsb90FlNRh0Gio+hldQtNWHUH+4CJMIqJNV4oOZWBPfxWT9fumsB+fnnHda6TRondWs718vN88n268HzsSPkAAFz2YosltufmfyGf4BBaAWS02ogm8SLsFZOApe57yfFp+hwsl4CrREH1d3kUVk8DxlW1nu6pXbnEz3Q65+lVr1fCozQXP8dZaJrMZHbTKb7Q46DRbqqdRUj8ONULZ7COnIw2N4EipvKo26nbISC5CKe3EzxhHY0TTjTmbV8TQIpftqbeKft9uY5VutZ+A2QuC80suUHGqhNctoOm8vnk8euL7Qwe7D
/// U9iDlpv2dfAx3kQlNP1k4W7oAvouBkIXldcaBXpBUQZ0l9qUErLaSzYoe6NjYNYGu4GSes/zO+3Xq6tsCsOGGuVFLYaN3MZHBYR6HpXftlCZDr5HXlkD71Fjtz+9w+58tsqG5m6yo+lJu2vS0+fU504r/T7yobX4HD9H6Hm6eT75xXRnVWXED13pLTdBVIJUewuhrLUIs5GEONwF3e/4lYt+noKpeBgzkYnFWAltaDn+CpXO/8Dr2IeDMvIWrIHOU+dzFdQv9kfoOiq7OcMHG6jJKihYZpXpUovtfKavi5l8RzBwBMXmBYaUOEqrgovMc0AUQMqA4x7Z6ryv3kuZc0jOxkBWcsp74+cItDDqZbXPwfPU9Ke2JvR8WvJ8MtR5SAYdaNNBFLk7sBT3Q/3TCAzGAIQ2dAcvAkdA7388vgeVKh37d5iBZdBivAoF+EcIfuaOpmPrM+iCWI6HoPKqdToF34DXee7nlFRvkXPK7cisFy0qs20i
/// FDjql/Q+elQzHhhuVIFGQT2wLt7geShb34rQz9cazycDLkQetEGfQgfKxWRoItAk5/V3nYka8tOhjVSj/gBysBa6KN6FOxA6ksqrWgdl9mehr0I02Ogi0eaegUi0XmKD2a2tAn+vjHQiroRaGWVbBZD7M74Nvdb2JNHiiUG4BTrIJjwNlSfdCmj5u+FOC3sCLoI2Uwuopl3pvgrvwb3IHU0X89+hgUIthdoB9XbBifYGXINLMRY6D92iCT6KJl69fj3Uamiay8ZqaBp/H17HVhJ5EKoMXmv35Rr/o3FbB1a50zSn+0e90PyX/3/0hi4ytQAKOi2y+sQiqB1Qf6dWwGtTwpWytzLnj6GLzmtdDhz/6KbkFVC50IKG/lIXN5WFkzASKq/3YgHW4RV09lsrbq9jBW7CcfA63/bhHy1Ut2ZPdmkvZXlNTRoqroPaiBQ8Cn2XthNq5j+E10YfSgp4ffWiz3IPVDK/fHhoL88nuxwquoB1o1UD
/// hYajcdCUqKZdbYj6H02P6oF80DcT6rE0QOkbhDroBqVu5ipAVZZFPaFuRGv61A1eTeoaBjQp616bviLrwD651P4H/vWTtmh4MWMAAAAASUVORK5CYIIBvARQRVJoZEdGVFpYUWdUbUZ0WlQwaWMzRnNSR0YwWVZOdmRYSmpaVEVpUGp4V2FXVjNJRTVoYldVOUlsQlBYMEpoY2tOdlpHVWlQanhHYVdWc1pDQk9ZVzFsUFNKQ1lYSkRiMlJsSWlCVWVYQmxQU0pUZEhKcGJtY2lJQzgrUEVacFpXeGtJRTVoYldVOUlrSmhjMlZPZFcwaUlGUjVjR1U5SWxOMGNtbHVaeUlnTHo0OFJtbGxiR1FnVG1GdFpUMGlVM1Z3Y0d4cFpYSk9ZVzFsSWlCVWVYQmxQU0pUZEhKcGJtY2lJQzgrUEVacFpXeGtJRTVoYldVOUlsTjFjSEJzYVdWeVEyOWtaU0lnVkhsd1pUMGlVM1J5YVc1bklp
/// QXZQanhHYVdWc1pDQk9ZVzFsUFNKUVZHbHRaU0lnVkhsd1pUMGlSR0YwWlZScGJXVWlJQzgrUEVacFpXeGtJRTVoYldVOUlrbDBaVzFEYjJSbElpQlVlWEJsUFNKVGRISnBibWNpSUM4K1BFWnBaV3hrSUU1aGJXVTlJa2wwWlcxT1lXMWxJaUJVZVhCbFBTSlRkSEpwYm1jaUlDOCtQRVpwWld4a0lFNWhiV1U5SWxGMGVTSWdWSGx3WlQwaVJHVmphVzFoYkNJZ0x6NDhSbWxsYkdRZ1RtRnRaVDBpVTNWd2NHeHBaWEpDWVhSamFDSWdWSGx3WlQwaVUzUnlhVzVuSWlBdlBqd3ZWbWxsZHo0OEwwUmhkR0ZUWlhRKw==</Resource>
///   </Resources>
/// </XRTypeInfo>
namespace XtraReportSerialization {
    
    public class WmsBarCodesP : DevExpress.XtraReports.UI.XtraReport {
        private DevExpress.XtraReports.UI.XRTableCell tableCell17;
        private DevExpress.XtraReports.UI.XRTableCell tableCell12;
        private DevExpress.XtraReports.UI.XRTableCell tableCell11;
        private DevExpress.XtraReports.UI.XRLabel label2;
        private DevExpress.XtraReports.UI.XRTableRow tableRow5;
        private DevExpress.XtraReports.UI.XRBarCode barCode1;
        private DevExpress.XtraReports.Parameters.Parameter paramBarCode;
        private DevExpress.XtraReports.UI.XRTableCell tableCell13;
        private DevExpress.XtraReports.UI.TopMarginBand TopMargin;
        private DevExpress.XtraReports.UI.XRLabel label6;
        private DevExpress.XtraReports.UI.XRTableRow tableRow8;
        private DevExpress.XtraReports.UI.XRTableCell tableCell10;
        private DevExpress.XtraReports.UI.XRTableCell tableCell14;
        private DevExpress.XtraReports.UI.XRLabel label4;
        private DevExpress.XtraReports.UI.XRLabel label1;
        private DevExpress.XtraReports.UI.XRLabel label3;
        private DevExpress.XtraReports.UI.XRTableRow tableRow6;
        private DevExpress.XtraReports.UI.XRTable table2;
        private DevExpress.XtraReports.UI.XRLabel label7;
        private DevExpress.DataAccess.Sql.SqlDataSource sqlDataSource1;
        private DevExpress.XtraReports.UI.BottomMarginBand BottomMargin;
        private DevExpress.XtraReports.UI.XRLabel label5;
        private DevExpress.XtraReports.UI.DetailBand Detail;
        private DevExpress.XtraReports.UI.XRPictureBox pictureBox1;
        private System.Resources.ResourceManager _resources;
        private string _resourceString;
        public WmsBarCodesP() {
            this._resourceString = DevExpress.XtraReports.Serialization.XRResourceManager.GetResourceFor("XtraReportSerialization.WmsBarCodesP");
            this.InitializeComponent();
        }
        private System.Resources.ResourceManager resources {
            get {
                if (_resources == null) {
                    this._resources = new DevExpress.XtraReports.Serialization.XRResourceManager(this._resourceString);
                }
                return this._resources;
            }
        }
        private void InitializeComponent() {
            DevExpress.XtraPrinting.BarCode.QRCodeGenerator qRCodeGenerator1 = new DevExpress.XtraPrinting.BarCode.QRCodeGenerator();
            DevExpress.DataAccess.ConnectionParameters.MsSqlConnectionParameters msSqlConnectionParameters1 = new DevExpress.DataAccess.ConnectionParameters.MsSqlConnectionParameters();
            DevExpress.DataAccess.Sql.SelectQuery selectQuery1 = new DevExpress.DataAccess.Sql.SelectQuery();
            DevExpress.DataAccess.Sql.Column column1 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression1 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Table table1 = new DevExpress.DataAccess.Sql.Table();
            DevExpress.DataAccess.Sql.Column column2 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression2 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column3 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression3 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column4 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression4 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column5 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression5 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column6 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression6 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column7 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression7 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column8 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression8 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column9 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression9 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.QueryParameter queryParameter1 = new DevExpress.DataAccess.Sql.QueryParameter();
            this.tableCell17 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell12 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell11 = new DevExpress.XtraReports.UI.XRTableCell();
            this.label2 = new DevExpress.XtraReports.UI.XRLabel();
            this.tableRow5 = new DevExpress.XtraReports.UI.XRTableRow();
            this.barCode1 = new DevExpress.XtraReports.UI.XRBarCode();
            this.tableCell13 = new DevExpress.XtraReports.UI.XRTableCell();
            this.TopMargin = new DevExpress.XtraReports.UI.TopMarginBand();
            this.label6 = new DevExpress.XtraReports.UI.XRLabel();
            this.tableRow8 = new DevExpress.XtraReports.UI.XRTableRow();
            this.label4 = new DevExpress.XtraReports.UI.XRLabel();
            this.label1 = new DevExpress.XtraReports.UI.XRLabel();
            this.label3 = new DevExpress.XtraReports.UI.XRLabel();
            this.tableRow6 = new DevExpress.XtraReports.UI.XRTableRow();
            this.table2 = new DevExpress.XtraReports.UI.XRTable();
            this.label7 = new DevExpress.XtraReports.UI.XRLabel();
            this.sqlDataSource1 = new DevExpress.DataAccess.Sql.SqlDataSource();
            this.BottomMargin = new DevExpress.XtraReports.UI.BottomMarginBand();
            this.label5 = new DevExpress.XtraReports.UI.XRLabel();
            this.paramBarCode = new DevExpress.XtraReports.Parameters.Parameter();
            this.Detail = new DevExpress.XtraReports.UI.DetailBand();
            this.pictureBox1 = new DevExpress.XtraReports.UI.XRPictureBox();
            this.tableCell14 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell10 = new DevExpress.XtraReports.UI.XRTableCell();
            ((System.ComponentModel.ISupportInitialize)(this.table2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // tableCell17
            // 
            this.tableCell17.Dpi = 100F;
            this.tableCell17.Font = new System.Drawing.Font("宋体", 10F, System.Drawing.FontStyle.Bold);
            this.tableCell17.Name = "tableCell17";
            this.tableCell17.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 0, 0, 0, 100F);
            this.tableCell17.StylePriority.UseFont = false;
            this.tableCell17.StylePriority.UsePadding = false;
            this.tableCell17.StylePriority.UseTextAlignment = false;
            this.tableCell17.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.tableCell17.Weight = 2.3161079115347469D;
            // 
            // tableCell12
            // 
            this.tableCell12.Dpi = 100F;
            this.tableCell12.Font = new System.Drawing.Font("宋体", 10F, System.Drawing.FontStyle.Bold);
            this.tableCell12.Name = "tableCell12";
            this.tableCell12.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 0, 0, 0, 100F);
            this.tableCell12.StylePriority.UseFont = false;
            this.tableCell12.StylePriority.UsePadding = false;
            this.tableCell12.StylePriority.UseTextAlignment = false;
            this.tableCell12.Text = "HP";
            this.tableCell12.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.tableCell12.Weight = 1.0815853569816045D;
            // 
            // tableCell11
            // 
            this.tableCell11.Dpi = 100F;
            this.tableCell11.Font = new System.Drawing.Font("宋体", 10F, System.Drawing.FontStyle.Bold);
            this.tableCell11.Name = "tableCell11";
            this.tableCell11.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 0, 0, 0, 100F);
            this.tableCell11.StylePriority.UseFont = false;
            this.tableCell11.StylePriority.UsePadding = false;
            this.tableCell11.StylePriority.UseTextAlignment = false;
            this.tableCell11.Text = "VOLTS";
            this.tableCell11.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.tableCell11.Weight = 0.89935806261627693D;
            // 
            // label2
            // 
            this.label2.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.label2.Dpi = 100F;
            this.label2.Font = new System.Drawing.Font("宋体", 16F, System.Drawing.FontStyle.Bold);
            this.label2.LocationFloat = new DevExpress.Utils.PointFloat(87.49995F, 71.68116F);
            this.label2.Name = "label2";
            this.label2.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.label2.SizeF = new System.Drawing.SizeF(205F, 19.49274F);
            this.label2.StylePriority.UseBorders = false;
            this.label2.StylePriority.UseFont = false;
            this.label2.StylePriority.UseTextAlignment = false;
            this.label2.Text = "MOTORS";
            this.label2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopCenter;
            // 
            // tableRow5
            // 
            this.tableRow5.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
                        this.tableCell11,
                        this.tableCell17});
            this.tableRow5.Dpi = 100F;
            this.tableRow5.Font = new System.Drawing.Font("宋体", 10F);
            this.tableRow5.Name = "tableRow5";
            this.tableRow5.StylePriority.UseFont = false;
            this.tableRow5.Weight = 0.63382050530747946D;
            // 
            // barCode1
            // 
            this.barCode1.Alignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.barCode1.AutoModule = true;
            this.barCode1.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.barCode1.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding(this.paramBarCode, "Text", "")});
            this.barCode1.Dpi = 100F;
            this.barCode1.Font = new System.Drawing.Font("宋体", 10F);
            this.barCode1.LocationFloat = new DevExpress.Utils.PointFloat(225F, 103.6739F);
            this.barCode1.Name = "barCode1";
            this.barCode1.Padding = new DevExpress.XtraPrinting.PaddingInfo(10, 10, 0, 0, 100F);
            this.barCode1.SizeF = new System.Drawing.SizeF(108F, 103.4192F);
            this.barCode1.StylePriority.UseBorders = false;
            this.barCode1.StylePriority.UseFont = false;
            this.barCode1.StylePriority.UseTextAlignment = false;
            qRCodeGenerator1.CompactionMode = DevExpress.XtraPrinting.BarCode.QRCodeCompactionMode.Byte;
            this.barCode1.Symbology = qRCodeGenerator1;
            this.barCode1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // tableCell13
            // 
            this.tableCell13.Dpi = 100F;
            this.tableCell13.Font = new System.Drawing.Font("宋体", 10F, System.Drawing.FontStyle.Bold);
            this.tableCell13.Name = "tableCell13";
            this.tableCell13.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 0, 0, 0, 100F);
            this.tableCell13.StylePriority.UseFont = false;
            this.tableCell13.StylePriority.UsePadding = false;
            this.tableCell13.StylePriority.UseTextAlignment = false;
            this.tableCell13.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.tableCell13.Weight = 2.7853956530876842D;
            // 
            // TopMargin
            // 
            this.TopMargin.Dpi = 100F;
            this.TopMargin.HeightF = 8.000005F;
            this.TopMargin.Name = "TopMargin";
            this.TopMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.TopMargin.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // label6
            // 
            this.label6.Dpi = 100F;
            this.label6.Font = new System.Drawing.Font("宋体", 14F, System.Drawing.FontStyle.Bold);
            this.label6.LocationFloat = new DevExpress.Utils.PointFloat(10F, 279F);
            this.label6.Name = "label6";
            this.label6.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.label6.SizeF = new System.Drawing.SizeF(90.13497F, 23F);
            this.label6.StylePriority.UseFont = false;
            this.label6.StylePriority.UseTextAlignment = false;
            this.label6.Text = "Qty:";
            this.label6.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // tableRow8
            // 
            this.tableRow8.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
                        this.tableCell10,
                        this.tableCell14});
            this.tableRow8.Dpi = 100F;
            this.tableRow8.Name = "tableRow8";
            this.tableRow8.Weight = 0.63382050530747969D;
            // 
            // label4
            // 
            this.label4.Dpi = 100F;
            this.label4.Font = new System.Drawing.Font("宋体", 14F, System.Drawing.FontStyle.Bold);
            this.label4.LocationFloat = new DevExpress.Utils.PointFloat(103.3333F, 218.5F);
            this.label4.Name = "label4";
            this.label4.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.label4.SizeF = new System.Drawing.SizeF(229.6667F, 23F);
            this.label4.StylePriority.UseFont = false;
            this.label4.StylePriority.UseTextAlignment = false;
            this.label4.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // label1
            // 
            this.label1.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.label1.Dpi = 100F;
            this.label1.Font = new System.Drawing.Font("宋体", 22.2F, ((System.Drawing.FontStyle)((System.Drawing.FontStyle.Bold | System.Drawing.FontStyle.Italic))));
            this.label1.LocationFloat = new DevExpress.Utils.PointFloat(29.99995F, 32.16667F);
            this.label1.Name = "label1";
            this.label1.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.label1.SizeF = new System.Drawing.SizeF(303.0001F, 27.8261F);
            this.label1.StylePriority.UseBorders = false;
            this.label1.StylePriority.UseFont = false;
            this.label1.StylePriority.UseTextAlignment = false;
            this.label1.Text = "FARADYNE";
            this.label1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopCenter;
            // 
            // label3
            // 
            this.label3.Dpi = 100F;
            this.label3.Font = new System.Drawing.Font("宋体", 14F, System.Drawing.FontStyle.Bold);
            this.label3.LocationFloat = new DevExpress.Utils.PointFloat(10F, 218.5F);
            this.label3.Name = "label3";
            this.label3.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.label3.SizeF = new System.Drawing.SizeF(90.13497F, 23F);
            this.label3.StylePriority.UseFont = false;
            this.label3.StylePriority.UseTextAlignment = false;
            this.label3.Text = "MODEL:";
            this.label3.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // tableRow6
            // 
            this.tableRow6.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
                        this.tableCell12,
                        this.tableCell13});
            this.tableRow6.Dpi = 100F;
            this.tableRow6.Font = new System.Drawing.Font("宋体", 10F);
            this.tableRow6.Name = "tableRow6";
            this.tableRow6.StylePriority.UseFont = false;
            this.tableRow6.Weight = 0.633820496091452D;
            // 
            // table2
            // 
            this.table2.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
                        | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.table2.Dpi = 100F;
            this.table2.Font = new System.Drawing.Font("宋体", 9.75F);
            this.table2.LocationFloat = new DevExpress.Utils.PointFloat(9.999974F, 103.6739F);
            this.table2.Name = "table2";
            this.table2.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
                        this.tableRow6,
                        this.tableRow5,
                        this.tableRow8});
            this.table2.SizeF = new System.Drawing.SizeF(215F, 103.4192F);
            this.table2.StylePriority.UseBorders = false;
            this.table2.StylePriority.UseFont = false;
            this.table2.StylePriority.UseTextAlignment = false;
            this.table2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // label7
            // 
            this.label7.Dpi = 100F;
            this.label7.Font = new System.Drawing.Font("宋体", 14F, System.Drawing.FontStyle.Bold);
            this.label7.LocationFloat = new DevExpress.Utils.PointFloat(103.3333F, 279F);
            this.label7.Name = "label7";
            this.label7.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.label7.SizeF = new System.Drawing.SizeF(229.6667F, 23F);
            this.label7.StylePriority.UseFont = false;
            this.label7.StylePriority.UseTextAlignment = false;
            this.label7.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // sqlDataSource1
            // 
            this.sqlDataSource1.ConnectionName = "192.168.1.147_FaradyneWMS_Connection";
            msSqlConnectionParameters1.AuthorizationType = DevExpress.DataAccess.ConnectionParameters.MsSqlAuthorizationType.SqlServer;
            msSqlConnectionParameters1.DatabaseName = "FaradyneWMS";
            msSqlConnectionParameters1.Password = "Zkhz2018";
            msSqlConnectionParameters1.ServerName = "192.168.1.147";
            msSqlConnectionParameters1.UserName = "sa";
            this.sqlDataSource1.ConnectionParameters = msSqlConnectionParameters1;
            this.sqlDataSource1.Name = "sqlDataSource1";
            columnExpression1.ColumnName = "BarCode";
            table1.Name = "PO_BarCode";
            columnExpression1.Table = table1;
            column1.Expression = columnExpression1;
            columnExpression2.ColumnName = "BaseNum";
            columnExpression2.Table = table1;
            column2.Expression = columnExpression2;
            columnExpression3.ColumnName = "SupplierName";
            columnExpression3.Table = table1;
            column3.Expression = columnExpression3;
            columnExpression4.ColumnName = "SupplierCode";
            columnExpression4.Table = table1;
            column4.Expression = columnExpression4;
            columnExpression5.ColumnName = "PTime";
            columnExpression5.Table = table1;
            column5.Expression = columnExpression5;
            columnExpression6.ColumnName = "ItemCode";
            columnExpression6.Table = table1;
            column6.Expression = columnExpression6;
            columnExpression7.ColumnName = "ItemName";
            columnExpression7.Table = table1;
            column7.Expression = columnExpression7;
            columnExpression8.ColumnName = "Qty";
            columnExpression8.Table = table1;
            column8.Expression = columnExpression8;
            columnExpression9.ColumnName = "SupplierBatch";
            columnExpression9.Table = table1;
            column9.Expression = columnExpression9;
            selectQuery1.Columns.Add(column1);
            selectQuery1.Columns.Add(column2);
            selectQuery1.Columns.Add(column3);
            selectQuery1.Columns.Add(column4);
            selectQuery1.Columns.Add(column5);
            selectQuery1.Columns.Add(column6);
            selectQuery1.Columns.Add(column7);
            selectQuery1.Columns.Add(column8);
            selectQuery1.Columns.Add(column9);
            selectQuery1.FilterString = "[PO_BarCode.BarCode] In (?paramBarCode)";
            selectQuery1.Name = "PO_BarCode";
            queryParameter1.Name = "paramBarCode";
            queryParameter1.Type = typeof(DevExpress.DataAccess.Expression);
            queryParameter1.Value = new DevExpress.DataAccess.Expression("[Parameters.paramBarCode]", typeof(string));
            selectQuery1.Parameters.Add(queryParameter1);
            selectQuery1.Tables.Add(table1);
            this.sqlDataSource1.Queries.AddRange(new DevExpress.DataAccess.Sql.SqlQuery[] {
                        selectQuery1});
            this.sqlDataSource1.ResultSchemaSerializable = resources.GetString("sqlDataSource1.ResultSchemaSerializable");
            // 
            // BottomMargin
            // 
            this.BottomMargin.Dpi = 100F;
            this.BottomMargin.HeightF = 2F;
            this.BottomMargin.Name = "BottomMargin";
            this.BottomMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.BottomMargin.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // label5
            // 
            this.label5.Dpi = 100F;
            this.label5.Font = new System.Drawing.Font("宋体", 14F, System.Drawing.FontStyle.Bold);
            this.label5.LocationFloat = new DevExpress.Utils.PointFloat(9.999974F, 241.5F);
            this.label5.Name = "label5";
            this.label5.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.label5.SizeF = new System.Drawing.SizeF(323F, 37.49995F);
            this.label5.StylePriority.UseFont = false;
            this.label5.StylePriority.UseTextAlignment = false;
            this.label5.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // paramBarCode
            // 
            this.paramBarCode.Description = "条码号";
            this.paramBarCode.MultiValue = true;
            this.paramBarCode.Name = "paramBarCode";
            this.paramBarCode.ValueInfo = "4|50544";
            // 
            // Detail
            // 
            this.Detail.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
                        this.label6,
                        this.label7,
                        this.label5,
                        this.label4,
                        this.label3,
                        this.pictureBox1,
                        this.label2,
                        this.table2,
                        this.barCode1,
                        this.label1});
            this.Detail.Dpi = 100F;
            this.Detail.HeightF = 355.786F;
            this.Detail.Name = "Detail";
            this.Detail.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.Detail.PageBreak = DevExpress.XtraReports.UI.PageBreak.BeforeBand;
            this.Detail.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // pictureBox1
            // 
            this.pictureBox1.Dpi = 100F;
            this.pictureBox1.Image = ((System.Drawing.Image)(resources.GetObject("pictureBox1.Image")));
            this.pictureBox1.LocationFloat = new DevExpress.Utils.PointFloat(115.8333F, 9.166667F);
            this.pictureBox1.Name = "pictureBox1";
            this.pictureBox1.SizeF = new System.Drawing.SizeF(155.8333F, 23F);
            // 
            // tableCell14
            // 
            this.tableCell14.Dpi = 100F;
            this.tableCell14.Font = new System.Drawing.Font("宋体", 10F, System.Drawing.FontStyle.Bold);
            this.tableCell14.Name = "tableCell14";
            this.tableCell14.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 0, 0, 0, 100F);
            this.tableCell14.StylePriority.UseFont = false;
            this.tableCell14.StylePriority.UsePadding = false;
            this.tableCell14.StylePriority.UseTextAlignment = false;
            this.tableCell14.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.tableCell14.Weight = 2.3161079115347469D;
            // 
            // tableCell10
            // 
            this.tableCell10.Dpi = 100F;
            this.tableCell10.Font = new System.Drawing.Font("宋体", 10F, System.Drawing.FontStyle.Bold);
            this.tableCell10.Name = "tableCell10";
            this.tableCell10.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 0, 0, 0, 100F);
            this.tableCell10.StylePriority.UseFont = false;
            this.tableCell10.StylePriority.UsePadding = false;
            this.tableCell10.StylePriority.UseTextAlignment = false;
            this.tableCell10.Text = "KW";
            this.tableCell10.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.tableCell10.Weight = 0.89935806261627693D;
            // 
            // WmsBarCodesP
            // 
            this.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
                        this.Detail,
                        this.TopMargin,
                        this.BottomMargin});
            this.ComponentStorage.AddRange(new System.ComponentModel.IComponent[] {
                        this.sqlDataSource1});
            this.DataMember = "PO_BarCode";
            this.DataSource = this.sqlDataSource1;
            this.Dpi = 100F;
            this.Font = new System.Drawing.Font("宋体", 10F);
            this.Margins = new System.Drawing.Printing.Margins(0, 40, 8, 2);
            this.Name = "WmsBarCodesP";
            this.PageHeight = 350;
            this.PageWidth = 383;
            this.PaperKind = System.Drawing.Printing.PaperKind.Custom;
            this.Parameters.AddRange(new DevExpress.XtraReports.Parameters.Parameter[] {
                        this.paramBarCode});
            this.Version = "18.1";
            ((System.ComponentModel.ISupportInitialize)(this.table2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();
        }
    }
}
