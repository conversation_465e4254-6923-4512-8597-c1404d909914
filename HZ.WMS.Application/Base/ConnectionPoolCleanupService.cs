using System;
using System.Threading;
using System.Threading.Tasks;
using System.Configuration;
using HZ.Core.Logging;

namespace HZ.WMS.Application
{
    /// <summary>
    /// 连接池清理服务
    /// </summary>
    public class ConnectionPoolCleanupService
    {
        private static Timer _cleanupTimer;
        private static readonly object _lock = new object();
        private static bool _isRunning = false;

        /// <summary>
        /// 启动连接池清理服务
        /// </summary>
        public static void Start()
        {
            lock (_lock)
            {
                if (_isRunning)
                    return;

                try
                {
                    // 从配置文件读取清理间隔，默认5分钟
                    var intervalMinutes = int.Parse(ConfigurationManager.AppSettings["ConnectionPool.CleanupInterval"] ?? "5");
                    var interval = TimeSpan.FromMinutes(intervalMinutes);

                    _cleanupTimer = new Timer(CleanupCallback, null, interval, interval);
                    _isRunning = true;

                    LogHelper.Instance.LogInfo($"连接池清理服务已启动，清理间隔: {intervalMinutes} 分钟");
                }
                catch (Exception ex)
                {
                    LogHelper.Instance.LogError($"启动连接池清理服务失败: {ex.Message}", ex);
                }
            }
        }

        /// <summary>
        /// 停止连接池清理服务
        /// </summary>
        public static void Stop()
        {
            lock (_lock)
            {
                if (!_isRunning)
                    return;

                try
                {
                    _cleanupTimer?.Dispose();
                    _cleanupTimer = null;
                    _isRunning = false;

                    LogHelper.Instance.LogInfo("连接池清理服务已停止");
                }
                catch (Exception ex)
                {
                    LogHelper.Instance.LogError($"停止连接池清理服务失败: {ex.Message}", ex);
                }
            }
        }

        /// <summary>
        /// 清理回调方法
        /// </summary>
        /// <param name="state"></param>
        private static void CleanupCallback(object state)
        {
            try
            {
                LogHelper.Instance.LogInfo("开始执行连接池清理...");

                // 执行连接池状态检查
                Task.Run(async () =>
                {
                    try
                    {
                        var connectionStatus = await SimpleConnectionMonitor.CheckAllConnectionsAsync();

                        foreach (var status in connectionStatus)
                        {
                            if (!status.IsHealthy)
                            {
                                LogHelper.Instance.LogError($"连接池 {status.ConnectionKey} 不健康: {status.ErrorMessage}");
                            }
                            else if (status.IsNearCapacity)
                            {
                                LogHelper.Instance.LogWarn($"连接池 {status.ConnectionKey} 使用率过高: {status.UsagePercentage:F2}% (响应时间: {status.ResponseTimeMs:F0}ms)");
                            }
                            else
                            {
                                LogHelper.Instance.LogInfo($"连接池 {status.ConnectionKey} 状态: {status.Status} - {status.UsagePercentage:F2}% (响应时间: {status.ResponseTimeMs:F0}ms)");
                            }
                        }

                        // 强制垃圾回收，释放未使用的连接
                        GC.Collect();
                        GC.WaitForPendingFinalizers();
                        GC.Collect();

                        LogHelper.Instance.LogInfo("连接池清理完成");
                    }
                    catch (Exception ex)
                    {
                        LogHelper.Instance.LogError($"连接池状态检查失败: {ex.Message}", ex);
                    }
                });
            }
            catch (Exception ex)
            {
                LogHelper.Instance.LogError($"连接池清理回调执行失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 手动执行清理
        /// </summary>
        public static void ManualCleanup()
        {
            try
            {
                LogHelper.Instance.LogInfo("手动执行连接池清理");
                CleanupCallback(null);
            }
            catch (Exception ex)
            {
                LogHelper.Instance.LogError($"手动清理失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取服务状态
        /// </summary>
        /// <returns></returns>
        public static bool IsRunning()
        {
            return _isRunning;
        }
    }
}
