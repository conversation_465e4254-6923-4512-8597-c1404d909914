using SqlSugar;
using HZ.Core.Http;
using HZ.WMS.Entity.MM;
using HZ.WMS.Entity.VW;
using HZ.WMS.Application.Sys;
using System;
using System.Collections.Generic;
using System.Linq;
using HZ.WMS.Application.PO;
using HZ.WMS.Application.PP;
using HZ.WMS.Entity.PO;
using HZ.WMS.Application.MD;
using HZ.WMS.Application.SAP;
using HZ.WMS.Entity.SAP;
using SqlSugar;

namespace HZ.WMS.Application.MM
{
    /// <summary>
    /// 没有out_message消息的函数，意味着只在最前端做未处理异常捕捉
    /// </summary>

    public class MM_TakeStockScanApp : BaseApp<MM_TakeStockScan>
    {
        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public MM_TakeStockScanApp() : base()
        {
        }

        #endregion

        #region 获取标签表的物料信息
        /// <summary>
        /// 获取标签表的物料信息
        /// </summary>
        /// <param name="BarCode"></param>
        /// <returns></returns>
        public MM_TakeStockScan GetV_BarCode(string BarCode)
        {
            MM_TakeStockScan entity = this.DbContext.Ado.SqlQuery<MM_TakeStockScan>("select '' as ScanID,0.00 as ScanQty,0.00 as StockQty,'' as BinLocationCode,'' as RegionCode,BarCode,BatchNum,ItemCode,ItemName,PTime,ItmsGrpCode,ItmsGrpName,Unit from V_BarCode where IsDelete=0 and BarCode=@value", new SugarParameter("@value", BarCode)).FirstOrDefault();
            return entity;
        }
        #endregion

        #region 获取列表

        /// <summary>
        /// 获取列表
        /// </summary>
        /// <returns></returns>
        public List<MM_TakeStockScan> GetList()
        {
            return base.GetList().ToList();
        }
        /// <summary>
        /// 获取列表差异
        /// </summary>
        /// <returns></returns>
        public List<MM_TakeStockScan> GetListCY(string keyword, DateTime[] dateTimes, string isConfirm)
        {
            var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateTimes);
            DateTime fromTime = querDateTimes[0];
            DateTime toTime = querDateTimes[1];
            ISugarQueryable<MM_TakeStockScan> query = this.DbContext.Queryable<MM_TakeStockScan>()
                        .InnerJoin<MM_TakeStockPlan>((detail, master) => detail.DocNum == master.DocNum && master.Status == 3)
                        .Select((detail, master) => detail)
                        .Where(x => !x.IsDelete && (string.IsNullOrEmpty(keyword)
                                                    || x.DocNum.Contains(keyword)
                                                    || x.BarCode.Contains(keyword)
                                                    || x.BatchNum.Contains(keyword)
                                                    || x.ItemCode.Contains(keyword)
                                                    || x.ItemName.Contains(keyword)
                                                    || x.WhsCode.Contains(keyword)
                                                    || x.BinLocationCode.Contains(keyword)
                                                    || x.BinLocationName.Contains(keyword)
                            ))
                        .Where(x => !x.IsDelete && (isConfirm == null || x.IsConfirm == bool.Parse(isConfirm)) && x.CTime >= fromTime && x.CTime <= toTime)
                        .Distinct();
            return base.GetList().ToList();
        }

        #endregion

        #region 重写查询
        /// <summary>
        /// 
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <param name="dateStart"></param>
        /// <param name="dateEnd"></param>
        /// <param name="deliveryPlanStatus"></param>
        /// <param name="supplierCode"></param>
        /// <returns></returns>
        public List<MM_TakeStockScan> GetPageList(Pagination page, string keyword, DateTime dateStart, DateTime dateEnd, string isConfirm)
        {
            ISugarQueryable<MM_TakeStockScan> query = this.DbContext.Queryable<MM_TakeStockScan>()
                .InnerJoin<MM_TakeStockPlan>((detail, master) => detail.DocNum == master.DocNum && master.Status == 3)
                .Select((detail, master) => detail)
                .Where(x => !x.IsDelete && (string.IsNullOrEmpty(keyword)
                                            || x.DocNum.Contains(keyword)
                                            || x.BarCode.Contains(keyword)
                                            || x.BatchNum.Contains(keyword)
                                            || x.ItemCode.Contains(keyword)
                                            || x.ItemName.Contains(keyword)
                                            || x.WhsCode.Contains(keyword)
                                            || x.BinLocationCode.Contains(keyword)
                                            || x.BinLocationName.Contains(keyword)
                    ))
                .Where(x => !x.IsDelete && x.DiffQty != 0 && (isConfirm == null || x.IsConfirm == bool.Parse(isConfirm)) && x.CTime >= dateStart && x.CTime <= dateEnd)
                .Distinct();

            if (query != null)
            {
                page.Total = query.Count();
                if (string.IsNullOrEmpty(page.Sort))
                {
                    page.Sort = "DocNum asc";
                }
                page.Sort = page.Sort.Replace("ascending", "asc").Replace("descending", "desc");
                return query.OrderBy(page.Sort).ToPageList(page.PageNumber, page.PageSize);
            }
            else
            {
                return null;
            }
        }



        #endregion

        #region 添加

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public MM_TakeStockScan Insert(MM_TakeStockScan entity)
        {
            return base.Insert(entity);
        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public int Delete(MM_TakeStockScan entity)
        {
            return base.Delete(entity);
        }

        #endregion

        #region 批量删除

        /// <summary>
        /// 批量删除
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public int DeleteByIDS(object[] ids, string deleteUser)
        {

            var idsList = GetAllList(x => ids.Contains(x.ScanID) && !x.IsDelete).Select(x => x.DocNum).ToList().ToArray();

            MM_TakeStockPlanApp _planApp = new MM_TakeStockPlanApp();
            var planList = _planApp.GetAllList(x => idsList.Contains(x.DocNum) && x.Status == 3 && !x.IsDelete).ToList();
            if (planList != null && planList.Count > 0)
            {
                throw new Exception("Common.error", new Exception("盘点计划已完成不允许删除"));
            }
            return base.DeleteByKeys(ids, deleteUser);
        }

        #endregion

        #region 更新

        /// <summary>
        /// 更新，只能修改备注，是否已读，已读时间，修改时间，修改人Id这几个字段
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public int Update(MM_TakeStockScan entity)
        {
            return base.Update(entity);
        }

        #endregion

        #region 校验盘点数据是否在盘点计划内
        /// <summary>
        /// 
        /// </summary>
        /// <param name="docNum"></param>
        /// <param name="barcode"></param>
        /// <param name="binLocationCode"></param>
        /// <returns></returns>
        public string CheckTakeStockScanInPlan(string docNum, string ItemCode, string WhsCode)
        {

            MM_TakeStockPlanApp _planApp = new MM_TakeStockPlanApp();
            var plan = _planApp.GetAllList(x => x.DocNum == docNum && !x.IsDelete && x.Status == 2)?.ToList();

            if (plan.Count > 0)
            {

                MM_TakeStockPlanDetailedApp _planDetailedApp = new MM_TakeStockPlanDetailedApp();
                var planDetail = _planDetailedApp.GetAllList(x => x.DocNum == docNum && !x.IsDelete).ToList();


                foreach (var entity in planDetail)
                {

                    ////盘点供应商
                    //if (entity.SupplierCode != null && entity.SupplierCode == scanEntity.SupplierCode)
                    //{
                    //    return scanEntity;
                    //}
                    //盘点物料
                    if (entity.WhsCode == null && entity.ItemCode == ItemCode)
                    {
                        return "";
                    }
                    //盘点仓库
                    if (entity.ItemCode == null && entity.WhsCode == WhsCode)
                    {
                        return "";
                    }
                    ////盘点区域，库位
                    //if (entity.ItemCode == null && entity.RegionCode == scanEntity.RegionCode && entity.BinLocationCode == scanEntity.BinLocationCode)
                    //{
                    //    return scanEntity;
                    //}
                    ////盘点库位
                    //if (entity.ItemCode == null && entity.RegionCode == null && entity.BinLocationCode == scanEntity.BinLocationCode)
                    //{
                    //    return scanEntity;
                    //}
                    //盘点仓库，物料
                    if (entity.ItemCode == ItemCode && entity.WhsCode == WhsCode)
                    {
                        return "";
                    }
                    ////盘点库位，物料
                    //if (entity.RegionCode == null && entity.ItemCode == scanEntity.ItemCode && entity.BinLocationCode == scanEntity.BinLocationCode)
                    //{
                    //    return scanEntity;
                    //}


                }

                return "物料号【" + ItemCode + "】仓库【" + WhsCode + "】不在盘点计划号【" + docNum + "】内，不允许导入";
            }
            else
                return "盘点计划号【" + docNum + "】无效不允许导入，请检查盘点计划状态";
        }

        #endregion

        /// <summary>
        /// 批量添加，实际上是更新和添加
        /// </summary>
        /// <param name="list"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        public int AddList(List<MM_TakeStockScan> list, string user)
        {
            int result = 0;
            List<MM_TakeStockScan> addlist = new List<MM_TakeStockScan>();
            List<MM_TakeStockScan> updatelist = new List<MM_TakeStockScan>();
            MM_TakeStockScanApp _TakeStockScanApp = new MM_TakeStockScanApp();
            try
            {
                this.DbContext.Ado.BeginTran();

                list.ForEach(x =>
                {
                    x.MUser = user;
                    x.MTime = DateTime.Now;

                });
                foreach (MM_TakeStockScan entity in list)
                {

                    MM_TakeStockScan getEntity = _TakeStockScanApp.GetFirstEntity(x => x.DocNum == entity.DocNum && x.BarCode == entity.BarCode && x.BinLocationCode == entity.BinLocationCode);

                    if (getEntity == null)//entity.ScanID==null
                    {
                        if (entity.ScanID == null)
                        {
                            entity.StockQty = 0;
                            entity.CUser = entity.MUser;
                            entity.CTime = entity.MTime;
                            addlist.Add(entity);
                        }
                    }
                    else
                    {
                        getEntity.ScanQty = entity.ScanQty;
                        getEntity.MUser = entity.MUser;
                        getEntity.MTime = entity.MTime;
                        updatelist.Add(getEntity);
                    }
                    result++;
                }
                base.Update(updatelist);
                base.Insert(addlist);
                this.DbContext.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                result = 0;
                this.DbContext.Ado.RollbackTran();
                throw new Exception(ex.Message);
            }
            return result;

        }

        #region 导入

        /// <summary>
        /// 导入
        /// </summary>
        /// <param name="excelList">接收的集合</param>
        /// <param name="opUser">当前用户</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool ImprotExcelToBaseData(List<MM_TakeStockScanImport> excelList, string opUser, out string error_message)
        {
            error_message = "";

            if (!ValidateCheck(excelList))
            {
                return false;
            }

            var flag = true;
            DateTime date = DateTime.Now;
            List<MM_TakeStockScan> Addlist = new List<MM_TakeStockScan>();
            List<MM_TakeStockScan> Updatelist = new List<MM_TakeStockScan>();
            //物料
            var ItemCodes = excelList.Select(x => x.物料编号).Distinct().ToArray();
            var itemcodes = DbContextForSAP.Queryable<XZ_SAP_MARC>().Where(x => ItemCodes.Contains(x.MATNR))?.ToList();

            //仓库编码
            var WhsCodes = excelList.Select(x => x.仓库编号).Distinct().ToArray();
            var whscodes = new MD_BinLocationApp().GetList(x => WhsCodes.Contains(x.WhsCode))?.ToList();


            int i = 0;
            try
            {
                //暂时只进行插入操作，有需要在做更新操作
                foreach (var x in excelList)
                {
                  string masg= CheckTakeStockScanInPlan(x.盘点计划单号, x.物料编号, x.仓库编号);
                    if (string.IsNullOrEmpty(masg))
                    {
                        MM_TakeStockScan info1 = GetList(j => j.DocNum == x.盘点计划单号 && j.ItemCode == x.物料编号 && j.WhsCode == x.仓库编号 &&
                        (string.IsNullOrEmpty(x.出厂编号) || j.BarCode == x.出厂编号))?.ToList().FirstOrDefault();
                        if (info1 == null)
                        {
                            MM_TakeStockScan info = new MM_TakeStockScan();
                            info.DocNum = x.盘点计划单号;
                            info.IsDelete = false;
                            info.IsPosted = false;
                            info.CUser = opUser;
                            info.CTime = date;
                            info.ManualPostTime = date;

                            info.BarCode = x.出厂编号;

                            info.ItemCode = x.物料编号;
                            var item = itemcodes.Where(v => v.MATNR == x.物料编号)?.FirstOrDefault();
                            if (item == null)
                            {
                                error_message = "物料编号【" + x.物料编号 + "】无效!";
                                return false;
                            }
                            info.ItemName = item == null ? "" : item.MAKTX;
                            info.Unit = item == null ? "" : item.MEINS;
                            info.ScanQty = x.盘点数量;
                            info.WhsCode = x.仓库编号;
                            var outwhs = whscodes.Where(v => v.WhsCode == x.仓库编号)?.FirstOrDefault();
                            info.WhsName = outwhs == null ? "" : outwhs.WhsName;
                            info.RegionCode = outwhs == null ? "" : outwhs.RegionCode;
                            info.RegionName = outwhs == null ? "" : outwhs.RegionName;
                            info.BinLocationCode = outwhs == null ? "" : outwhs.BinLocationCode;
                            info.BinLocationName = outwhs == null ? "" : outwhs.BinLocationName;

                            //info.SupplierCode = x.供应商编号;
                            info.SaleNum = x.销售单号;
                            info.SaleLine = x.销售行号;
                            info.AssessType = x.评估类型;
                            info.SpecialStock = x.特殊库存;
                            info.StockQty = x.库存数量;

                            info.DiffQty = info.StockQty - info.ScanQty;

                            Addlist.Add(info);
                        }
                        else
                        {
                            if (info1.IsPosted == false)
                            {

                                info1.MUser = opUser;
                                info1.MTime = date;


                                info1.BarCode = x.出厂编号;

                                info1.ItemCode = x.物料编号;
                                var item = itemcodes.Where(v => v.MATNR == x.物料编号)?.FirstOrDefault();
                                if (item == null)
                                {
                                    error_message = "物料编号【" + x.物料编号 + "】无效!";
                                    return false;
                                }
                                info1.ItemName = item == null ? "" : item.MAKTX;
                                info1.Unit = item == null ? "" : item.MEINS;
                                info1.ScanQty = x.盘点数量;
                                info1.WhsCode = x.仓库编号;
                                var outwhs = whscodes.Where(v => v.WhsCode == x.仓库编号)?.ToList().FirstOrDefault();
                                info1.WhsName = outwhs == null ? "" : outwhs.WhsName;
                                info1.RegionCode = outwhs == null ? "" : outwhs.RegionCode;
                                info1.RegionName = outwhs == null ? "" : outwhs.RegionName;
                                info1.BinLocationCode = outwhs == null ? "" : outwhs.BinLocationCode;
                                info1.BinLocationName = outwhs == null ? "" : outwhs.BinLocationName;

                                info1.SupplierCode = x.供应商编号;
                                info1.SaleNum = x.销售单号;
                                info1.SaleLine = x.销售行号;
                                info1.AssessType = x.评估类型;
                                info1.SpecialStock = x.特殊库存;
                                info1.StockQty = x.库存数量;

                                info1.DiffQty = info1.StockQty - info1.ScanQty;
                                Updatelist.Add(info1);
                            }
                        }
                    }
                    else
                    {
                        error_message = masg;
                        return false;
                    }
                };
                this.DbContext.Ado.BeginTran();
                if (Addlist.Count > 0)
                    this.Insert(Addlist);
                if (Updatelist.Count > 0)
                    this.Update(Updatelist);

                this.DbContext.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                if (this.DbContext.Ado.IsAnyTran())
                    this.DbContext.Ado.RollbackTran();
                throw ex;
            }
            return flag;
        }

        /// <summary>
        /// 导入校验规则
        /// </summary>
        /// <param name="excelList">导入的集合</param>
        /// <returns></returns>
        public bool ValidateCheck(List<MM_TakeStockScanImport> excelList)
        {
            if (excelList == null || excelList.Count <= 0)
            {
                throw new Exception("没有可以导入的数据");
            }

            SAPApp _SAPApp = new SAPApp();
            MD_BinLocationApp _binApp = new MD_BinLocationApp();


            var flag = true;

            //校验空值
            //校验导入数据 是否符合导入条件 用数据校验 空值/是否存在
            _SAPApp.IsHaveItem(excelList.Select(x => x.物料编号).Distinct().ToArray());//物料信息
            //_SAPApp.IsHaveItemName(excelList.Select(x => x.物料名称).Distinct().ToArray());//物料名称信息
            _binApp.IsHaveWhsCode(excelList.Select(x => x.仓库编号).Distinct().ToArray());//仓库

            return flag;
        }

        #endregion


        #region 过账

        /// <summary>
        /// 过账
        /// </summary>
        /// <param name="entities">采购收货集合</param>
        /// <param name="opUser">当前过账用户</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool DoPost(List<MM_TakeStockScan> entities, string opUser, out string error_message)
        {
            error_message = "";
            try
            {
                MD_StockApp _stockApp = new MD_StockApp();
                Sys_SAPCompanyInfoApp _SAPCompanyInfoApp = new Sys_SAPCompanyInfoApp();
                MM_TakeStockPlanApp _planApp = new MM_TakeStockPlanApp();

                //多条数据后根据单号进行分组
                var mainInfoList = entities.GroupBy(g => new
                {
                    g.DocNum
                }).Select(q => new
                {
                    DocNum = q.Key.DocNum
                });

                //按订单号分组
                foreach (var mainInfo in mainInfoList)
                {
                    var planList = _planApp.GetAllList(x => x.DocNum== mainInfo.DocNum && x.Status != 3 && !x.IsDelete).ToList();
                    if (planList != null && planList.Count > 0)
                    {
                        error_message = "盘点单号[" + mainInfo.DocNum + "]状态未完成不允许过账" ;
                        return false;
                    }

                    var list = new List<ZFGWMS013>();
                    int Line = 0;
                    var conditionList = entities.Where(x => x.DocNum == mainInfo.DocNum)?.ToList();
                    conditionList.ForEach(x =>
                    {
                        Line = Line + 1;
                        x.Line = Line;
                        ZFGWMS013 y = new ZFGWMS013();
                        y.ZNUM = Line;
                        y.MATNR = x.ItemCode;
                        y.WERKS = "2002";
                        y.MENGE = Convert.ToDecimal(x.ScanQty);
                        y.MEINS = x.Unit;
                        y.LGORT = x.WhsCode;
                        if (!string.IsNullOrEmpty(string.IsNullOrEmpty(x.SaleNum) ? "" : x.SaleNum.Trim()))
                        {
                            y.KDAUF = x.SaleNum;//如果特殊库存等于E，则必填
                            y.KDPOS = Convert.ToInt32(x.SaleLine);//如果特殊库存等于E，则必填
                            y.SOBKZ = x.SpecialStock ?? "E";
                        }
                        else
                        {
                            y.KDAUF = "";
                            y.KDPOS = 0;
                            y.SOBKZ = x.SpecialStock;
                        }
                        y.BWTAR = x.AssessType;//如果物料启用分割评估，此字段必填(待定)
                        list.Add(y);
                    });

                    bool ispost = false;
                    DateTime time = DateTime.Now;

                    //查询Sap账期时间
                    DateTime ManualPostTime = new SAPApp().GetSAPpostTime(Convert.ToDateTime(conditionList[0].ManualPostTime));
                    //purchaseReceipt.CompanyCode 公司代码
                    var saplist = _SAPCompanyInfoApp.ZFGWMS013("001", mainInfo.DocNum, "", ManualPostTime, list, out ispost, out error_message);
                    if (saplist != null && saplist.Count > 0)
                    {
                        //List<PO_PurchaseReceipt> queryList = new List<PO_PurchaseReceipt>();

                        //DbContext.Ado.BeginTran();
                        //_stockApp.DbContext = this.DbContext;
                        foreach (var sap in saplist)
                        {
                            var query = conditionList.Where(x => x.DocNum == mainInfo.DocNum && x.Line == sap.line ).ToList().FirstOrDefault();
                            if (query != null)
                            {
                                query.IsPosted = true;
                                query.PostUser = opUser;
                                query.PostTime = time;
                                query.MUser = opUser;
                                query.MTime = time;
                                query.SapDocNum = sap.sapDocNum;
                                query.SapLine = sap.sapline;
                                query.ManualPostTime = ManualPostTime;
                                query.SAPmark = "S";

                                //更新
                                Update(query);
                            }

                        }
               
           

                    }
                    else
                    {


                        error_message = "单号[" + mainInfo.DocNum + "],过账失败，失败原因：" + error_message;
                        return false;
                    }
                }
                if (string.IsNullOrEmpty(error_message))
                {
                    error_message = "过账成功";
                }
                return true;
            }
            catch (Exception ex)
            {
                error_message = ex.Message;
                return false;
            }




        }

        #endregion
    }
}

