using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Web;
using System.Web.Http;
using HZ.Core.Http;
using HZ.WMS.Application.Produce;
using HZ.WMS.Entity.Produce;
using HZ.WMS.Entity.Produce.Req;
using HZ.WMS.Entity.SD;
using HZ.WMS.Entity.Sys;
using HZ.WMS.WebAPI.Controllers;
using Newtonsoft.Json;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;

namespace HZ.WMS.WebAPI.Areas.Produce.Controllers
{
    /// <summary>
    /// 生产报工控制器
    /// </summary>
    public class Produce_ReportController : ApiBaseController
    {
        
        #region 变量及构造函数

        private readonly Produce_ReportApp _app = new Produce_ReportApp();

        #endregion

        #region 获取分页列表

        /// <summary>
        /// 获取分页列表
        /// </summary>
        /// <param name="page">分页参数</param>
        /// <param name="req">查询条件</param>
        /// <returns>分页数据</returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri] Pagination page, [FromUri] Produce_ReportListReq req)
        {
            var result = new ResponseData();
            try
            {
                var data = _app.GetPageList(page, req);
                result.Data = new ResponsePageData { total = page.Total, items = data };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion
        
        #region 获取报工明细

        /// <summary>
        /// 获取报工明细
        /// </summary>
        /// <param name="id">分页参数</param>
        [HttpGet]
        public IHttpActionResult GetDetailList(string id)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.GetDetailList(id);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取详情

        /// <summary>
        /// 获取详情
        /// </summary>
        /// <param name="id">主键</param>
        /// <returns>实体详情</returns>
        [HttpGet]
        public IHttpActionResult GetEntity(string id)
        {
            var result = new ResponseData();
            try
            {
                var entity = _app.GetEntity(id);
                var details = _app.GetDetails(id);
                result.Data = new { entity, details };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 根据生产订单号 获取详情

        /// <summary>
        /// 获取详情
        /// </summary>
        /// <param name="id">主键</param>
        /// <returns>实体详情</returns>
        [HttpGet]
        public IHttpActionResult GetEntityByProduceOrderNo(string produceOrderNo)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.GetEntityByProduceOrderNo(produceOrderNo);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取当前账户站点列表

        /// <summary>
        /// 获取当前账户站点列表
        /// </summary>
        /// <returns>当前用户关联的站点列表</returns>
        /// <remarks>
        /// 返回当前登录用户关联的所有报工站点信息，包括工作中心和站点详情。
        /// 返回信息包含以下内容：
        /// - StationId: 站点ID
        /// - WorkCenterCode: 工作中心编码
        /// - WorkCenterName: 工作中心名称
        /// - StationCode: 站点代码
        /// - StationName: 站点名称
        /// - StationSort: 站点序号
        /// - ProcessNo: 工序号
        /// - ProcessShortText: 工序短文本
        /// - Enable: 启用状态(0:未启用, 1:启用)
        /// </remarks>
        [HttpGet]
        public IHttpActionResult GetSelfStationList()
        {
            var result = new ResponseData();
            try
            {
                var currentUser = GetCurrentUser();
                if (currentUser == null)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "获取当前用户信息失败";
                    return Json(result);
                }
                
                var stationList = _app.GetSelfStationList(currentUser);
                result.Data = stationList;
                result.Code = (int)WMSStatusCode.Success;
                
                // 如果返回的数据为空列表，添加提示信息
                if (stationList.Count == 0)
                {
                    result.Message = "当前用户未关联任何站点";
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 保存

        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="data">保存数据</param>
        /// <returns>保存结果</returns>
        [HttpPost]
        public IHttpActionResult SaveForm([FromBody] dynamic data)
        {
            var result = new ResponseData();
            try
            {
                Produce_Report entity = JsonConvert.DeserializeObject<Produce_Report>(data.entity.ToString());
                List<Produce_ReportDetail> details = JsonConvert.DeserializeObject<List<Produce_ReportDetail>>(data.details.ToString());

                bool success = _app.SaveForm(entity, details, GetCurrentUser().LoginAccount);
                if (success)
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = "保存成功";
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "保存失败";
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id">主键</param>
        /// <returns>删除结果</returns>
        [HttpPost]
        public IHttpActionResult DeleteForm(string id)
        {
            var result = new ResponseData();
            try
            {
                bool success = _app.DeleteForm(id, GetCurrentUser().LoginAccount);
                if (success)
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = "删除成功";
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "删除失败";
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 报工

        /// <summary>
        /// 报工
        /// </summary>
        /// <param name="req">报工请求</param>
        /// <returns>报工结果</returns>
        [HttpPost]
        public IHttpActionResult DoReport([FromBody] Produce_ReportScanReq req)
        {
            var result = new ResponseData();
            try
            {
                string message;
                bool success = _app.DoReport(req, GetCurrentUser().LoginAccount, out message);
                if (success)
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = message;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 导出

        /// <summary>
        /// 导出数据
        /// </summary>
        /// <param name="req">查询条件</param>
        /// <returns>Excel文件</returns>
        [HttpGet]
        public HttpResponseMessage Export([FromUri] Produce_ReportListReq req)
        {
            try
            {
                byte[] buffer = _app.ExportData(req);
                HttpResponseMessage response = new HttpResponseMessage(HttpStatusCode.OK);
                response.Content = new ByteArrayContent(buffer);
                response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                response.Content.Headers.ContentDisposition = new ContentDispositionHeaderValue("attachment")
                {
                    FileName = WebUtility.UrlEncode("生产报工数据_" + DateTime.Now.ToString("yyyyMMddHHmmss") + ".xlsx")
                };
                return response;
            }
            catch (Exception ex)
            {
                return new HttpResponseMessage(HttpStatusCode.InternalServerError)
                {
                    Content = new StringContent(ex.Message)
                };
            }
        }

        #endregion

        #region 导入模板

        /// <summary>
        /// 获取导入模板
        /// </summary>
        /// <returns>模板文件</returns>
        [HttpGet]
        public HttpResponseMessage GetImportTemplate()
        {
            try
            {
                byte[] buffer = _app.GetImportTemplate();
                HttpResponseMessage response = new HttpResponseMessage(HttpStatusCode.OK);
                response.Content = new ByteArrayContent(buffer);
                response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                response.Content.Headers.ContentDisposition = new ContentDispositionHeaderValue("attachment")
                {
                    FileName = WebUtility.UrlEncode("生产报工导入模板.xlsx")
                };
                return response;
            }
            catch (Exception ex)
            {
                return new HttpResponseMessage(HttpStatusCode.InternalServerError)
                {
                    Content = new StringContent(ex.Message)
                };
            }
        }

        #endregion

        #region 导入

        /// <summary>
        /// 导入数据
        /// </summary>
        /// <returns>导入结果</returns>
        [HttpPost]
        public IHttpActionResult ImportData()
        {
            var result = new ResponseData();
            try
            {
                if (!Request.Content.IsMimeMultipartContent())
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "请上传Excel文件";
                    return Json(result);
                }

                string root = HttpContext.Current.Server.MapPath("~/Temp/");
                if (!Directory.Exists(root))
                {
                    Directory.CreateDirectory(root);
                }

                var provider = new MultipartFormDataStreamProvider(root);
                var task = Request.Content.ReadAsMultipartAsync(provider).ContinueWith<ResponseData>(t =>
                {
                    if (t.IsFaulted || t.IsCanceled)
                    {
                        return new ResponseData
                        {
                            Code = (int)WMSStatusCode.Failed,
                            Message = "文件上传失败"
                        };
                    }

                    var fileInfo = provider.FileData[0];
                    string filePath = fileInfo.LocalFileName;
                    string fileName = fileInfo.Headers.ContentDisposition.FileName.Replace("\"", "");

                    if (!fileName.EndsWith(".xlsx", StringComparison.OrdinalIgnoreCase))
                    {
                        return new ResponseData
                        {
                            Code = (int)WMSStatusCode.Failed,
                            Message = "请上传Excel文件(.xlsx)"
                        };
                    }

                    IWorkbook workbook;
                    using (FileStream fs = new FileStream(filePath, FileMode.Open, FileAccess.Read))
                    {
                        workbook = new XSSFWorkbook(fs);
                    }

                    ISheet sheet = workbook.GetSheetAt(0);
                    int rowCount = sheet.LastRowNum;
                    
                    if (rowCount == 0)
                    {
                        return new ResponseData
                        {
                            Code = (int)WMSStatusCode.Failed,
                            Message = "导入数据为空"
                        };
                    }

                    List<Produce_Report> importList = new List<Produce_Report>();
                    List<string> errorList = new List<string>();
                    
                    // 从第二行开始读取数据
                    for (int i = 1; i <= rowCount; i++)
                    {
                        IRow row = sheet.GetRow(i);
                        if (row == null) continue;

                        try
                        {
                            string produceOrderNo = row.GetCell(0)?.ToString();
                            string serialNo = row.GetCell(1)?.ToString();
                            string materialNo = row.GetCell(2)?.ToString();
                            string materialName = row.GetCell(3)?.ToString();
                            
                            decimal reportTotal = 0;
                            decimal.TryParse(row.GetCell(4)?.ToString(), out reportTotal);
                            
                            decimal qualifiedQty = 0;
                            decimal.TryParse(row.GetCell(5)?.ToString(), out qualifiedQty);
                            
                            decimal unqualifiedQty = 0;
                            decimal.TryParse(row.GetCell(6)?.ToString(), out unqualifiedQty);
                            
                            string unqualifiedRemarks = row.GetCell(7)?.ToString();
                            string workCenterCode = row.GetCell(8)?.ToString();
                            string workCenterName = row.GetCell(9)?.ToString();
                            string remark = row.GetCell(10)?.ToString();

                            // 验证必填项
                            if (string.IsNullOrEmpty(produceOrderNo))
                            {
                                errorList.Add($"第{i+1}行：生产订单号不能为空");
                                continue;
                            }

                            if (reportTotal <= 0)
                            {
                                errorList.Add($"第{i+1}行：报工数量必须大于0");
                                continue;
                            }

                            if (qualifiedQty < 0)
                            {
                                errorList.Add($"第{i+1}行：合格数量不能小于0");
                                continue;
                            }

                            if (string.IsNullOrEmpty(workCenterCode))
                            {
                                errorList.Add($"第{i+1}行：工作中心编码不能为空");
                                continue;
                            }

                            Sys_User currentUser = GetCurrentUser();
                            
                            Produce_Report entity = new Produce_Report
                            {
                                Id = Guid.NewGuid().ToString(),
                                ProduceReportNo = "RPT" + DateTime.Now.ToString("yyyyMMddHHmmss") + "_" + i,
                                SerialNo = serialNo,
                                ProduceOrderNo = produceOrderNo,
                                MaterialCode = materialNo,
                                MaterialName = materialName,
                                ReportTotal = reportTotal,
                                QualifiedQty = qualifiedQty,
                                UnqualifiedQty = unqualifiedQty,
                                UnqualifiedRemarks = unqualifiedRemarks,
                                WorkCenterCode = workCenterCode,
                                WorkCenterName = workCenterName,
                                AssemblDate = DateTime.Now,
                                IsCompleted = true,
                                IsPosted = false,
                                IsDelete = false,
                                Remark = remark,
                                CUser = currentUser?.UserName,
                                CTime = DateTime.Now
                            };

                            importList.Add(entity);
                        }
                        catch (Exception ex)
                        {
                            errorList.Add($"第{i+1}行：处理数据异常，{ex.Message}");
                        }
                    }

                    // 删除临时文件
                    File.Delete(filePath);

                    if (importList.Count == 0)
                    {
                        return new ResponseData
                        {
                            Code = (int)WMSStatusCode.Failed,
                            Message = "没有有效数据可以导入，错误：" + string.Join("; ", errorList)
                        };
                    }

                    // 保存数据
                    int successCount = 0;
                    foreach (var item in importList)
                    {
                        try
                        {
                            // 查询生产订单信息
                            var orderInfo = _app.DbContext.Queryable<Produce_Scheduling>()
                                .Where(x => x.ProduceOrderNo == item.ProduceOrderNo && !x.IsDelete)
                                .ToList().FirstOrDefault();
                            
                            if (orderInfo != null)
                            {
                                // item.HostProduceOrderNo = orderInfo.HostProduceOrderNo;
                                item.OrderType = orderInfo.OrderType;
                                item.OrderQty = orderInfo.Quantity;
                                item.Unit = orderInfo.Unit;
                                item.CustomerName = orderInfo.CustomerName;
                                // item.MaterialGroupCode = orderInfo.MaterialGroupCode;
                                // item.MaterialGroupDes = orderInfo.MaterialGroupDes;
                            }
                            
                            _app.Insert(item);
                            successCount++;
                        }
                        catch (Exception ex)
                        {
                            errorList.Add($"导入数据 {item.ProduceOrderNo} 保存失败：{ex.Message}");
                        }
                    }

                    return new ResponseData
                    {
                        Code = (int)WMSStatusCode.Success,
                        Message = $"导入完成。成功导入{successCount}条数据" + (errorList.Count > 0 ? $"，失败{errorList.Count}条：{string.Join("; ", errorList)}" : "")
                    };
                });

                result = task.Result;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 创建初始报工信息

        /// <summary>
        /// 创建初始报工信息
        /// </summary>
        /// <param name="schedulingIds">排产ID列表</param>
        /// <returns>创建结果</returns>
        [HttpPost]
        public IHttpActionResult CreateInitialReportInfo([FromBody] string[] schedulingIds)
        {
            var result = new ResponseData();
            try
            {
                bool success = _app.CreateInitialReportInfo(schedulingIds, GetCurrentUser().LoginAccount);
                if (success)
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = "创建初始报工信息成功";
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "创建初始报工信息失败";
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion
        
        #region 站点扫描

        /// <summary>
        /// 站点扫描
        /// </summary>
        /// <param name="req">扫描请求</param>
        /// <returns>扫描结果</returns>
        /// <remarks>
        /// 用于扫描生产过程中的站点，传入站点代码和生产工单号进行扫描确认。
        /// 该接口包含以下业务规则：
        /// 1. 校验站点和生产工单是否存在
        /// 2. 校验站点是否已扫描过（同一站点同一生产订单不允许重复扫描）
        /// 3. 确保按照站点顺序进行扫描（不能跳过前面的站点）
        /// 4. 如果扫描成功，会自动判断是否为最后一个站点，如果是则更新报工状态为已完成
        /// </remarks>
        [HttpPost]
        public IHttpActionResult ScanStation([FromBody] Produce_StationScanReq req)
        {
            var result = new ResponseData();
            try
            {
                if (req == null)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "请求参数不能为空";
                    return Json(result);
                }

                if (string.IsNullOrEmpty(req.StationCode))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "站点代码不能为空";
                    return Json(result);
                }

                if (string.IsNullOrEmpty(req.ProduceOrderNo))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "生产工单号不能为空";
                    return Json(result);
                }

                string message;
                bool success = _app.ScanStation(req.StationCode, req.ProduceOrderNo, GetCurrentUser().LoginAccount, out message);
                if (success)
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = message;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion
        
    }
} 