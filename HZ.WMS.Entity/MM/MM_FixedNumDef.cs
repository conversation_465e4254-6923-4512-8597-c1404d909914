namespace HZ.WMS.Entity.MM
{
    /// <summary>
    /// 仓库管理各模块单号固定编码定义
    /// </summary>
    public class MM_FixedNumDef
    {
        #region 文档中有定义的
        /// <summary>
        /// 单号类型(模块名称缩写)
        /// </summary>
        public const string DocType = "MM";

        /// <summary>
        /// 其它收货
        /// </summary>
        public const string MM_InScan = "MI";

        /// <summary>
        /// 其它发货
        /// </summary>
        public const string MM_OutScan = "MO";

        /// <summary>
        /// 物料移动
        /// </summary>
        public const string MM_TransferScan = "MT";

        /// <summary>
        /// 盘点计划
        /// </summary>
        public const string MM_TakeStockPlan = "TS";

        /// <summary>
        /// 包装箱码
        /// </summary>
        public const string MM_BoxInfo = "M";

        #endregion

        /// <summary>
        /// 生产备货波次单
        /// </summary>
        public const string MM_StockingWave = "SW";

        /// <summary>
        /// 生产退料扫描记录单
        /// </summary>
        public const string MM_ReturnScan = "RS";

        /// <summary>
        /// 生产自制品质检扫描记录单
        /// </summary>
        public const string MM_InspectionScan = "INS";

        /// <summary>
        /// 生产不良报废记录单
        /// </summary>
        public const string MM_Scrapped = "SC";

        /// <summary>
        /// 生产不良登记单
        /// </summary>
        public const string MM_FTTP = "FP";

        /// <summary>
        /// 生产不良返修收货记录单
        /// </summary>
        public const string MM_Repair = "RP";

        /// <summary>
        /// 生产原料不良登记单
        /// </summary>
        public const string MM_FTTM = "FM";

        /// <summary>
        /// 生产自制品标签批次
        /// </summary>
        public const string MM_BarCode_BatchNum = "B";
    }
}
