using HZ.Core.Http;
using HZ.Core.Office;
using HZ.WMS.Application;
using HZ.WMS.Application.MD;
using HZ.WMS.Application.PP;
using HZ.WMS.Application.SAP;
using HZ.WMS.Entity.PP;
using HZ.WMS.Entity.Sys;
using HZ.WMS.WebAPI.Controllers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;

namespace HZ.WMS.WebAPI.Areas.PP.Controllers
{
    /// <summary>
    /// 物料报工
    /// </summary>
    public class PP_MaterialReportController : ApiBaseController
    {
        private PP_ProductionReportApp _app = new PP_ProductionReportApp();
        private MD_ProductionDistributionSettingApp disApp = new MD_ProductionDistributionSettingApp();

        #region 查询

        /// <summary>
        /// 获取主表分页列表
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <param name="dateValue"></param>
        /// <param name="isPosted"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]string keyword, [FromUri] DateTime[] dateValue, [FromUri]bool? isPosted)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];

                var itemsData = _app.GetPageList(page,
                    t => (string.IsNullOrEmpty(keyword)
                        || t.SerialNo.Contains(keyword)
                        || t.ProductionReportNo.Contains(keyword)
                        || t.ProductionOrderNo.Contains(keyword)
                        || t.MaterialNo.Contains(keyword)
                        || t.EmployeeNumber.Contains(keyword)
                        || t.EmployeeName.Contains(keyword)
                        || t.CUser.Contains(keyword))
                        && (t.CTime >= fromTime && t.CTime < toTime)
                        && t.ReportType == 1
                        && (isPosted == null || t.IsPosted == isPosted))
                    .ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 根据物料号查物料信息

        /// <summary>
        /// 根据物料号查物料信息
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetMaterialByCode(string code)
        {
            var result = new ResponseData();
            try
            {
                var material = new SAPApp().GetSAP_MARCByCode(code);
                result.Data = new PP_ProductionReport
                {
                    MaterialNo = material?.MATNR,
                    MaterialName = material?.MAKTX,
                    Unit = material?.MEINS,
                    MaterialGroupCode = material?.MATKL,
                    MaterialGroupDes = material?.WGBEZ,
                };
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取线体

        /// <summary>
        /// 获取线体
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetProductionLine()
        {
            var result = new ResponseData();
            try
            {
                result.Data = disApp.GetAllLine();
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 根据工作中心获取工序

        /// <summary>
        /// 根据工作中心获取工序
        /// </summary>
        /// <param name="LineNo"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetWorkingProcedure(string LineNo)
        {
            var result = new ResponseData();
            try
            {
                result.Code = (int)WMSStatusCode.Success;
                result.Data = _app.GetWorkingProcedureByLine(LineNo);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 增加

        [HttpPost]
        public IHttpActionResult Add([FromBody]List<PP_ProductionReport> productionReports)
        {
            var result = new ResponseData();
            try
            {
                Sys_User user = GetCurrentUser();
                productionReports.ForEach(entity =>
                {
                    entity.ReportType = 1;
                    entity.CUser = user.LoginAccount;
                    entity.EmployeeName = user.UserName;
                    entity.EmployeeNumber = user.LoginAccount;
                });
                _app.InsertWithTran(productionReports);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 更新

        [HttpPost]
        public IHttpActionResult Update([FromBody]PP_ProductionReport productionReport)
        {
            var result = new ResponseData();
            try
            {
                Sys_User user = GetCurrentUser();
                productionReport.MUser = user.LoginAccount;
                productionReport.ReportType = 1;
                _app.Update(productionReport);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除

        [HttpDelete]
        public IHttpActionResult Delete([FromBody]string[] Ids)
        {
            var result = new ResponseData();
            try
            {
                var list = _app.GetList(w => Ids.Contains(w.ID)).ToList();
                if (list.Any(w => w.IsPosted == true))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "ui.Message.PODeletePostingWarning";
                }
                else
                {
                    Sys_User user = GetCurrentUser();
                    _app.DeleteByKeys(Ids, user.LoginAccount);
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 导出

        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri]string keyword, [FromUri] DateTime[] dateValue, [FromUri]bool? isPosted)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];

                var itemsData = _app.GetList(
                    t => (string.IsNullOrEmpty(keyword)
                        || t.SerialNo.Contains(keyword)
                        || t.ProductionReportNo.Contains(keyword)
                        || t.ProductionOrderNo.Contains(keyword)
                        || t.MaterialNo.Contains(keyword)
                        || t.EmployeeNumber.Contains(keyword)
                        || t.EmployeeName.Contains(keyword)
                        || t.CUser.Contains(keyword))
                        && (t.CTime >= fromTime && t.CTime < toTime)
                        && t.ReportType == 1
                        && (isPosted == null || t.IsPosted == isPosted)
                    ).ToList();

                List<ExcelColumn<PP_ProductionReport>> columns = ExcelService.FetchDefaultColumnList<PP_ProductionReport>();
                string[] ignoreField = new string[]
                {
                    "ID"
                    ,"SerialNo"
                    ,"ProductionOrderNo"
                    ,"HostProductionOrderNo"
                    ,"ProductionScheduler"
                    ,"OrderType"
                    ,"OrderQty"
                    ,"AssessmentType"
                    ,"StartTime"
                    ,"ReportType"
                    ,"IsCompleted"
                    ,"IsPosted"
                    ,"PostUser"
                    ,"PostTime"
                    ,"ManualPostTime"
                    ,"SapDocNum"
                    ,"IsDelete"
                    ,"MUser"
                    ,"MTime"
                    ,"DUser"
                    ,"DTime"
                };
                List<ExcelColumn<PP_ProductionReport>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<PP_ProductionReport> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                //columns.ForEach((column) =>
                //{
                //    if (column.ColumnName == "IsPosted")
                //    {
                //        column.Formattor = ExcelExportFormatter.IsOrNoFormatter;
                //    }
                //});

                return ExportToExcelFile<PP_ProductionReport>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion
    }
}
