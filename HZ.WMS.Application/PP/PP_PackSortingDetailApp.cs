using HZ.WMS.Entity.PP;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Application.PP
{
    /// <summary>
    /// 装箱材料分拣明细表
    /// </summary>
    public class PP_PackSortingDetailApp : BaseApp<PP_PackSortingDetail>
    {
        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public PP_PackSortingDetailApp() : base()
        {
        }

        #endregion

        #region 获取SAP中间库数据

        /// <summary>
        /// 获取SAP中间库数据
        /// </summary>
        /// <param name="ProductionOrderNo"></param>
        /// <returns></returns>
        public IEnumerable<PP_PackSortingDetail> GetSapOrderDetailList(string ProductionOrderNo)
        {
            var sql = @"SELECT DISTINCT 
                        a.AUFNR AS ProductionOrderNo,					--生产订单
                        CAST(a.RSNUM as char) AS ReservedNo,			--预留编号
                        a.RSPOS AS ComponentLineNo,						--预留行号
                        a.MATNR AS ComponentCode,						--组件编码
                        c.MAKTX AS MaterialName,						--物料描述
                        a.BDMNG AS DemandQty,							--需求数量
                        a.MEINS AS ComponentUnit,						--组件单位
                        b.WERKS AS FactoryCode,							--工厂
                        a.LGORT AS DeliverLocation,						--发料库存地
                        a.KDAUF AS SalesOrderNo,						--销售订单
                        a.KDPOS AS SalesOrderLineNo,					--销售订单行项目
                        c.BWTTY AS AssessmentCategory,					--评估类别
                        a.SOBKZ AS SpecialInventory,					--特殊库存标识
                        a.RGEKZ AS IsBackflush							--是否是倒冲方式
                        --'' ID,'' ScanningCode,'' PackSortingNo,'' AssessmentType,null IsConfirm
                        FROM XZ_SAP.dbo.XZ_SAP_RESBP AS a				--SAP生产组件明细表
                        --关联SAP生产订单主表，查询工厂
                        LEFT JOIN XZ_SAP.dbo.XZ_SAP_AFKO AS b ON b.Status = 0 AND b.AUFNR = a.AUFNR
                        --关联SAP物料主数据
                        LEFT JOIN XZ_SAP.dbo.XZ_SAP_MARC AS c ON c.MATNR = a.MATNR AND c.Status = 0 AND c.WERKS = b.WERKS and c.LVORM= '' and c.LVORA = ''
                        inner join XZ_WMS.dbo.MD_Item  d on d.IsDelete = 0 and d.ItemCode = a.MATNR and d.IsPackaging = 1
                        WHERE a.Status = 0 and a.XLOEK = '' and a.DUMPS = '' and b.XLOEK <> 'X'
                        and a.AUFNR = '" + ProductionOrderNo + "' ";
            return DbContext.Ado.SqlQuery<PP_PackSortingDetail>(sql);
        }

        #endregion
    }
}
