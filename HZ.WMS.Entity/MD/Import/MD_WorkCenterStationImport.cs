using System.ComponentModel;

namespace HZ.WMS.Entity.MD.Import
{
    /// <summary>
    /// 工作中心站点导入
    /// </summary>
    public class MD_WorkCenterStationImport
    {
        /// <summary>
        /// 工作中心名称
        /// </summary>
        [Description("工作中心名称")]
        public string WorkCenterName { get; set; }

        /// <summary>
        /// 工作中心编码
        /// </summary>
        [Description("工作中心编码")]
        public string WorkCenterCode { get; set; }

        /// <summary>
        /// 站点代码
        /// </summary>
        [Description("站点代码")]
        public string StationCode { get; set; }

        /// <summary>
        /// 站点名称
        /// </summary>
        [Description("站点名称")]
        public string StationName { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Description("备注")]
        public string Remark { get; set; }
    }
} 