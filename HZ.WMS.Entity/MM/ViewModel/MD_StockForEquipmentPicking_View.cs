using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.MM.ViewModel
{
    /// <summary>
    /// 库存信息关联设备领料信息
    /// </summary>
    public class MD_StockForEquipmentPicking_View
    {
        /// <summary> 
        /// 物料编号
        /// </summary> 
        public string ItemCode { get; set; }

        /// <summary> 
        /// 物料名称
        /// </summary> 
        public string ItemName { get; set; }

        /// <summary>
        /// 所属仓库编号
        /// </summary>
        public string WhsCode { get; set; }

        /// <summary>
        /// 所属仓库名称
        /// </summary>
        public string WhsName { get; set; }

        /// <summary>
        /// 所属区域编号
        /// </summary>
        public string RegionCode { get; set; }

        /// <summary>
        /// 所属区域名称
        /// </summary>
        public string RegionName { get; set; }

        /// <summary>
        /// 库位编号
        /// </summary>
        [UniqueCode]
        public string BinLocationCode { get; set; }

        /// <summary>
        /// 库位名称
        /// </summary>
        public string BinLocationName { get; set; }

        /// <summary> 
        /// 库存数量
        /// </summary> 
        public decimal? Qty { get; set; }
       
    }
}
