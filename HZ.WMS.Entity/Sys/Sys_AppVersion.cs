using System.ComponentModel;
//Chloe框架
using SqlSugar;
using System;

namespace HZ.WMS.Entity.Sys
{
    /// <summary>
    /// 
    /// </summary>
    public class Sys_AppVersion:BaseEntity
    {
        /// <summary>
        /// APPID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string AppID { get; set; }


        /// <summary>
        /// 上一个版本号
        /// </summary>
        public string PreVersion { get; set; }

        /// <summary>
        /// 当前版本号
        /// </summary>
        public string CurrentVersion { get; set; }

        /// <summary>
        /// 升级URL
        /// </summary>
        public string UpdateUrl { get; set; }

        /// <summary>
        /// 发布时间
        /// </summary>
        public DateTime? PublishTime { get; set; }


        /// <summary>
        /// 是否强制更新
        /// </summary>
        public bool? ForceUpdate { get; set; }
    }
}
