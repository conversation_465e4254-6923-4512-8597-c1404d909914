using HZ.Core.Http;
using HZ.Core.Office;
using HZ.WMS.Application;
using HZ.WMS.Application.MM;
using HZ.WMS.Entity.MM;
using HZ.WMS.Entity.MM.Parameters;
using HZ.WMS.Entity.Sys;
using HZ.WMS.WebAPI.Controllers;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Web;
using System.Web.Http;

namespace HZ.WMS.WebAPI.Areas.MM.Controllers
{
    /// <summary>
    /// 委外领料申请
    /// </summary>
    public class MM_PickingApplyController : ApiBaseController
    {
        #region 初始化

        private MM_PickingApplyApp _app = new MM_PickingApplyApp();
        private MM_PickingApplyDetailApp _detailapp = new MM_PickingApplyDetailApp();

        #endregion

        #region 查询分页列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="page">分页</param>
        /// <param name="keyword">参数</param>
        /// <param name="dateValue">日期区间</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]string keyword, [FromUri] DateTime[] dateValue)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];
                var itemsData = _app.GetPageList(page, t => (string.IsNullOrEmpty(keyword)
                        || t.DocNum.Contains(keyword)
                        || t.CUser.Contains(keyword)
                ) && (t.CTime >= fromTime && t.CTime <= toTime)).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 查询明细分页列表

        /// <summary>
        /// 查询明细分页列表
        /// </summary>
        /// <param name="page">分页</param>
        /// <param name="keyword">参数</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageDetailList([FromUri]Pagination page, [FromUri]string keyword)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetPageDetailList(page, t => t.DocNum == keyword).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 编辑根据单号查询明细信息

        /// <summary>
        /// 编辑根据单号查询明细信息
        /// </summary>
        /// <param name="keyword">参数</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetList([FromUri]string keyword)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _detailapp.GetList(t => t.DocNum == keyword).ToList();
                    result.Data = itemsData;//new ResponsePageData { total = page.Total, items = itemsData };
                    result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 修改、删除校验

        /// <summary>
        /// 修改、删除校验
        /// </summary>
        /// <param name="Type">类型 update修改 delete删除</param>
        /// <param name="DocNums">根据单号进行删除</param>
        /// <returns></returns>
        //[HttpDelete]
        [HttpGet]
        //[AllowAnonymous]
        public IHttpActionResult CheckDeleteAndUpdate([FromUri] string Type, [FromUri]string[] DocNums)
        {
            //[FromBody]string[] ids
            var result = new ResponseData();
            try
            {
                string error_message = "";
                string[] strArray;
                //GetCurrentUser().LoginAccount
                bool bDelete = _app.CheckDeleteAndUpdate(Type, DocNums, out error_message, out strArray);
                if (!bDelete || !string.IsNullOrEmpty(error_message))
                {
                    if (Type == "update")
                    {
                        result.Code = (int)WMSStatusCode.Success;
                        result.Data = null;
                        result.Message = error_message;
                    }
                    else
                    {
                        result.Code = (int)WMSStatusCode.Success;
                        result.Data = strArray;
                        result.Message = error_message;
                    }
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 删除校验
        /// </summary>
        /// <param name="DocNums">根据单号进行删除</param>
        /// <returns></returns>
        //[HttpDelete]
        [HttpDelete]
        //[AllowAnonymous]
        public IHttpActionResult CheckDelete([FromBody]string[] DocNums)
        {
            //[FromBody]string[] ids
            var result = new ResponseData();
            try
            {
                string error_message = "";
                string[] strArray;
                //GetCurrentUser().LoginAccount
                bool bDelete = _app.CheckDelete(DocNums, out error_message, out strArray);
                if (!bDelete || !string.IsNullOrEmpty(error_message))
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Data = strArray;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="DocNums">根据单号进行删除</param>
        /// <returns></returns>
        //[HttpDelete]
        [HttpDelete]
        //[AllowAnonymous]
        public IHttpActionResult Delete([FromBody]string[] DocNums)
        {
            //[FromBody]string[] ids
            var result = new ResponseData();
            try
            {
                string error_message = "";
                //GetCurrentUser().LoginAccount
                bool bDelete = _app.Deletes(DocNums, GetCurrentUser().LoginAccount, out error_message);
                if (!bDelete || !string.IsNullOrEmpty(error_message))
                {
                    result.Code = (int)WMSStatusCode.UnHandledException;
                    result.Message = error_message;
                }

            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 导出

        /// <summary>
        /// 导出
        /// </summary>
        /// <param name="keyword"></param>
        /// <param name="dateValue"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] string keyword, [FromUri] DateTime[] dateValue)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];
                var itemsData = _detailapp.GetList().Where(t => string.IsNullOrEmpty(keyword) 
                        || t.DocNum.Contains(keyword) 
                        || t.ItemCode.Contains(keyword) || t.SupplierCode.Contains(keyword)
                        || t.SupplierName.Contains(keyword) || t.ItemName.Contains(keyword) 
                        || t.CUser.Contains(keyword) 
                        ).Where(x => x.CTime >= fromTime && x.CTime <= toTime && !x.IsDelete).ToList();
                //var itemsData = _app.GetList().ToList();
                List<ExcelColumn<MM_PickingApplyDetail>> columns = ExcelService.FetchDefaultColumnList<MM_PickingApplyDetail>();
                string[] ignoreField = new string[] 
                {
                    "DetailID",
                    "SubcontractingApplicationNum", "IsDelete","MUser","MTime", "DUser", "DTime","Status"

                };
                List<ExcelColumn<MM_PickingApplyDetail>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<MM_PickingApplyDetail> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                //columns.ForEach((column) =>
                //{
                //    if (column.ColumnName == "IsPosted")
                //    {
                //        column.Formattor = ExcelExportFormatter.IsOrNoFormatter;
                //    }
                //});

                return ExportToExcelFile(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #region 打印

        /// <summary>
        /// 打印
        /// </summary>
        /// <param name="docNums"></param>
        /// <param name="templateCode"></param>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IHttpActionResult Print([FromUri]string[] docNums)
        {
            var result = new ResponseData();
            string templateCode = "委外领料申请单.repx";
            if (docNums == null || docNums.Length == 0 || string.IsNullOrEmpty(templateCode))
            {
                result.Code = (int)WMSStatusCode.Failed;
            }
            else
            {
                //MD_LabelTempleteApp _labelTemplateApp = new MD_LabelTempleteApp();
                //MD_LabelTemplete template = _labelTemplateApp.GetFirstEntity(x => x.Remark == templateCode);
                //if (template == null)
                //{
                //    result.Code = (int)WMSStatusCode.Failed;
                //}
                //else
                //{
                DevExpress.XtraReports.UI.XtraReport report =
                    DevExpress.XtraReports.UI.XtraReport.FromFile(HttpContext.Current.Server.MapPath("~/") + ConfigurationManager.AppSettings["LabelTemplateUploadPath"]?.ToString() + templateCode, true);
                DevExpress.DataAccess.Sql.SqlDataSource dataSource = report.DataSource as DevExpress.DataAccess.Sql.SqlDataSource;
                //switch (templateCode)
                //{
                //    case "PP_BarCodeReturn":
                //    case "PP_BarCode":
                //    case "MM_BarCode":
                DevExpress.DataAccess.Sql.QueryParameter parameter = new DevExpress.DataAccess.Sql.QueryParameter()
                {
                    Name = "docNums",
                    Type = typeof(string),
                    Value = docNums[0]
                };

                dataSource.Queries[0].Parameters[0] = parameter;
                //dataSource.Fill();
                return base.PrintToPDF(report);

                //default:
                //    break;


            }
            return Json(result);
        }

        #endregion

        #region 保存

        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="Parameters">参数汇总</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Save([FromBody]MM_PickingApplyParameters Parameters)
        {

            var result = new ResponseData();
            try
            {
                string error_message = "";
                Sys_User currentUser = GetCurrentUser();
                bool bSubmit = _app.Save(Parameters.DocNum, Parameters.Remark, Parameters.detailed, currentUser.LoginAccount, out error_message);

                if (!bSubmit || !string.IsNullOrEmpty(error_message))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }

        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="Parameters">参数汇总</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Update([FromBody]MM_PickingApplyParameters Parameters)
        {

            var result = new ResponseData();
            try
            {
                string error_message = "";
                Sys_User currentUser = GetCurrentUser();
                bool bSubmit = _app.Update(Parameters.DocNum, Parameters.Remark, Parameters.deletedetail, Parameters.detailed, currentUser.LoginAccount, out error_message);

                if (!bSubmit || !string.IsNullOrEmpty(error_message))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }

        #endregion

        #region 获取系统生成的单号

        /// <summary>
        /// 获取系统生成的单号
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IHttpActionResult GetDocNum()
        {
            var result = new ResponseData();
            try
            {
                result.Data = base.GenerateDocNum(DocType.MM, DocFixedNumDef.MM_PickingApply);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 库存校验

        /// <summary>
        /// 库存校验
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ValidateStockOut([FromUri] string[] ItemCode)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                bool bStock=_app.ValidateStockOut(ItemCode, out error_message);
                if (!bStock)
                {
                    result.Message = error_message ;
                    result.Code = (int)WMSStatusCode.Failed;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

    }
}