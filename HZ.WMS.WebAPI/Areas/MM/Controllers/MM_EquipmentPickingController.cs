using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;
using HZ.Core.Http;
using HZ.Core.Office;
using HZ.WMS.Application;
using HZ.WMS.Application.MM;
using HZ.WMS.Application.Sys;
using HZ.WMS.Entity.MM;
using HZ.WMS.Entity.MM.Parameters;
using HZ.WMS.Entity.Sys;
using HZ.WMS.WebAPI.Controllers;

namespace HZ.WMS.WebAPI.Areas.MM
{
    /// <summary>
    /// 设备领料
    /// </summary>
    public class MM_EquipmentPickingController : ApiBaseController
    {
        #region 初始化

        private MM_EquipmentPickingApp _app = new MM_EquipmentPickingApp();
        private Sys_DictionaryApp _dictionaryapp = new Sys_DictionaryApp();

        #endregion

        #region PC

        #region 获取系统生成的单号

        /// <summary>
        /// 获取系统生成的单号
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetDocNum()
        {
            var result = new ResponseData();
            try
            {
                result.Data = base.GenerateDocNum(DocType.MM, DocFixedNumDef.MM_EquipmentPicking);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 查询分页列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="page">分页</param>
        /// <param name="keyword">参数</param>
        /// <param name="dateValue">日期区间</param>
        /// <param name="isPosted">是否过账</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]string keyword, [FromUri] DateTime[] dateValue, [FromUri] bool? isPosted ,[FromUri] bool? IsCancel)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];
                //&&t.EquipmentPickingStatus != "3"
                var itemsData = _app.GetPageList(page, t => (string.IsNullOrEmpty(keyword)
                        || t.DocNum.Contains(keyword) 
                        || t.CUser.Contains(keyword) || t.EquipmentNum.Contains(keyword)
                        || t.ItemCode.Contains(keyword) || t.ItemName.Contains(keyword)
                        || t.SupplierCode.Contains(keyword) || t.SupplierName.Contains(keyword)
                        || t.WhsCode.Contains(keyword) || t.WhsName.Contains(keyword)
                        || t.AssetCard.Contains(keyword) 
                        || t.LedgerAccount.Contains(keyword)|| t.LedgerAccountName.Contains(keyword)
                        || t.CostCenter.Contains(keyword) || t.CostCenterName.Contains(keyword)
                         || t.MovementType.Contains(keyword) || t.MovementTypeName.Contains(keyword)
                        || t.PostUser.Contains(keyword)
                ) && (t.CTime >= fromTime && t.CTime <= toTime)
                && (isPosted == null || t.IsPosted == isPosted)
                && (IsCancel == null || t.IsCancel == IsCancel)).ToList();
                //.LeftJoin<Sys_Dictionary>((bin, dic) => bin.EquipmentPickingStatus == dic.EnumKey.ToString())
                //        .Select(x => )
                //        .ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 保存

        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="Parameters">设备领料列表</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult SavePC([FromBody]List<MM_EquipmentPicking> Parameters)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                //string type = ""; //类型 1:提交失败 2：过账失败
                Sys_User currentUser = GetCurrentUser();
                bool bSubmit = _app.Save(Parameters,Convert.ToDateTime(Parameters[0].ManualPostTime), currentUser.LoginAccount, out error_message/*, out type*/);
                if (!bSubmit/*&& type == "1"*/)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
                //else if (!bSubmit && type == "2")
                //{
                //    result.Code = (int)WMSStatusCode.Success;
                //    result.Message = error_message;
                //}
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }

        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="entities">接收的集合</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Update([FromBody]List<MM_EquipmentPicking> entities)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                Sys_User currentUser = GetCurrentUser();

                if (_app.Update(entities) > 0)
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2000;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                    result.MessageParam = 2010;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="ids">根据ID进行删除</param>
        /// <returns></returns>
        [HttpDelete]
        //[AllowAnonymous]
        public IHttpActionResult Delete([FromBody]string[] ids)
        {
            //[FromBody]string[] ids
            var result = new ResponseData();
            try
            {
                string error_message = "";
                bool bDelete = _app.Deletes(ids, GetCurrentUser().LoginAccount, out error_message);
                if (!bDelete || !string.IsNullOrEmpty(error_message))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }

            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 导出

        /// <summary>
        /// 导出
        /// </summary>
        /// <param name="keyword">参数</param>
        /// <param name="dateValue">日期区间</param>
        /// <param name="isPosted">是否过账</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] string keyword, [FromUri] DateTime[] dateValue, [FromUri] bool? isPosted, [FromUri] bool? IsCancel)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];
                var itemsData = _app.GetList(t => (string.IsNullOrEmpty(keyword)
                       || t.DocNum.Contains(keyword)
                       || t.CUser.Contains(keyword) || t.EquipmentNum.Contains(keyword)
                       || t.ItemCode.Contains(keyword) || t.ItemName.Contains(keyword)
                       || t.SupplierCode.Contains(keyword) || t.SupplierName.Contains(keyword)
                       || t.WhsCode.Contains(keyword) || t.WhsName.Contains(keyword)
                       || t.AssetCard.Contains(keyword)
                       || t.LedgerAccount.Contains(keyword) || t.LedgerAccountName.Contains(keyword)
                       || t.CostCenter.Contains(keyword) || t.CostCenterName.Contains(keyword)
                        || t.MovementType.Contains(keyword) || t.MovementTypeName.Contains(keyword)
                       || t.PostUser.Contains(keyword)
               ) && (t.CTime >= fromTime && t.CTime <= toTime)
               && (isPosted == null || t.IsPosted == isPosted)
               && (IsCancel == null || t.IsCancel == IsCancel)).ToList();
                //var itemsData = _app.GetList().ToList();
                var columns = ExcelService.FetchDefaultColumnList<MM_EquipmentPicking>();
                string[] ignoreField = new string[]
                {
                    "EquipmentPickingID",
                    "Line",
                    "BatchNum",
                    "BaseEntry",
                    "BaseNum",
                    "BaseLine",
                    "RegionCode",
                    "BinLocationCode",
                    "CompanyCode",
                    "FactoryCode",
                    "MovementType",
                    "MovementTypeName",
                    "CostCenter",
                    "CostCenterName",
                    "LedgerAccount",
                    "LedgerAccountName",

                    "SpecialInventory",
                    "EvaluationType",
                    "Unit","RegionName","BinLocationName",
                    "SupplierCode","SupplierName","SupplierBatch","Order","SalesOrderNum","SalesOrderLine","EvaluationType",
                     "IsDelete","MUser","MTime", "DUser", "DTime","SAPmark","SAPmessage"
                };
                var ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (var userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                columns.ForEach((column) =>
                {
                    if (column.ColumnName == "IsPosted")
                    {
                        column.Formattor = ExcelExportFormatter.IsOrNoFormatterForBoolean;
                    }
                    else if (column.ColumnName == "IsCancel")
                    {
                        column.Formattor = ExcelExportFormatter.IsOrNoFormatterForBoolean;
                    }
                });

                return ExportToExcelFile<MM_EquipmentPicking>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #region 打印委外领料申请单

        /// <summary>
        /// 打印委外领料申请单
        /// </summary>
        /// <param name="docNums"></param>
        /// <param name="templateCode"></param>
        /// <returns></returns>
        //[HttpGet]
        //[AllowAnonymous]
        //public IHttpActionResult Print([FromUri]string[] docNums, [FromUri]string templateCode)
        //{
        //    var result = new ResponseData();

        //    if (docNums == null || docNums.Length == 0 || string.IsNullOrEmpty(templateCode))
        //    {
        //        result.Code = (int)WMSStatusCode.Failed;
        //    }
        //    else
        //    {
        //        //MD_LabelTempleteApp _labelTemplateApp = new MD_LabelTempleteApp();
        //        //MD_LabelTemplete template = _labelTemplateApp.GetFirstEntity(x => x.Remark == templateCode);
        //        //if (template == null)
        //        //{
        //        //    result.Code = (int)WMSStatusCode.Failed;
        //        //}
        //        //else
        //        //{
        //        DevExpress.XtraReports.UI.XtraReport report =
        //            DevExpress.XtraReports.UI.XtraReport.FromFile(HttpContext.Current.Server.MapPath("~/") + ConfigurationManager.AppSettings["LabelTemplateUploadPath"]?.ToString() + templateCode, true);
        //        DevExpress.DataAccess.Sql.SqlDataSource dataSource = report.DataSource as DevExpress.DataAccess.Sql.SqlDataSource;
        //        //switch (templateCode)
        //        //{
        //        //    case "PP_BarCodeReturn":
        //        //    case "PP_BarCode":
        //        //    case "MM_BarCode":
        //        DevExpress.DataAccess.Sql.QueryParameter parameter = new DevExpress.DataAccess.Sql.QueryParameter()
        //        {
        //            Name = "docNums",
        //            Type = typeof(string[]),
        //            Value = docNums
        //        };

        //        dataSource.Queries[0].Parameters[0] = parameter;
        //        //dataSource.Fill();
        //        return base.PrintToPDF(report);

        //        //default:
        //        //    break;


        //    }
        //    return Json(result);
        //}

        #endregion

        #region 过账

        /// <summary>
        ///过账
        /// </summary>
        /// <param name="entities">集合</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult DoPost([FromBody]List<MM_EquipmentPicking> entities)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                Sys_User currentUser = GetCurrentUser();
                bool bPost = _app.DoPost(entities, currentUser.LoginAccount, out error_message);
                if (!bPost)//过账失败==2000，前台处理过账失败后重新刷新界面信息
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2010;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2000;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }

        #endregion

        #region 审核

        /// <summary>
        /// 审核
        /// </summary>
        /// <param name="ids">根据ID进行审核</param>
        /// <returns></returns>
        [HttpGet]
        //[AllowAnonymous]
        public IHttpActionResult Audit([FromUri]string[] ids)
        {
            //[FromBody]string[] ids
            var result = new ResponseData();
            try
            {
                string error_message = "";
                string type = ""; //类型 1:提交失败 2：过账失败
                bool bAudits = _app.Audits(ids, GetCurrentUser().LoginAccount, out error_message, out type);
                if (!bAudits && type == "1")
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
                else if (!bAudits && type == "2")
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2010;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2000;
                }

            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 取消

        /// <summary>
        /// 取消
        /// </summary>
        /// <param name="ids">根据ID进行取消</param>
        /// <returns></returns>
        [HttpGet]
        //[AllowAnonymous]
        public IHttpActionResult Reject([FromUri]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                bool bRejects = _app.Rejects(ids, GetCurrentUser().LoginAccount, out error_message);
                if (!bRejects)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                }

            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 作废

        /// <summary>
        /// 作废
        /// </summary>
        /// <param name="DocNums">根据单号进行审核</param>
        /// <returns></returns>
        [HttpGet]
        //[AllowAnonymous]
        public IHttpActionResult Cancel([FromUri]string[] ids)
        {
            //[FromBody]string[] ids
            var result = new ResponseData();
            try
            {
                string error_message = "";
                bool bAudits = _app.Cancel(ids, GetCurrentUser().LoginAccount, out error_message);
                if (!bAudits)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                    result.MessageParam = 2010;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2000;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 导入模板

        /// <summary>
        /// 导入模板
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelModel()
        {
            var result = new ResponseData();
            try
            {
                var columns = ExcelService.FetchDefaultColumnList<MM_EquipmentPicking>();
                string modelName = "设备领料-导入模板";
                //排除不需要的列名称 字符串
                string[] ignoreField = new string[]
                {
                    "EquipmentPickingID","HandlenCode","HandlenName", "DocNum","BarCode" ,"BaseEntry","BaseNum","Line", "BaseLine", "BatchNum","ItemName",
                    "EquipmentPickingStatus","SupplierCode","SupplierName","SupplierBatch","Unit","WhsName", "RegionCode",
                    "RegionName","BinLocationCode", "BinLocationName", "IsCancel", "ManualPostTime",
                    "MovementType","MovementTypeName", "CostCenter", "CostCenterName", "LedgerAccount","LedgerAccountName",
                    "Order", "EvaluationType", "AssetCard","AssetCardName","SpecialInventory","SalesOrderNum",
                    "SalesOrderLine","EvaluationType","EquipmentNum","SAPmark","SAPmessage",
                    "IsPosted","CompanyCode","FactoryCode",
                    "PostUser","PostTime","SapDocNum","SapLine","IsDelete", "CUser", "CTime",
                    "MUser", "MTime", "DUser", "DTime"
                };
                var itemsData = new List<MM_EquipmentPicking>() { };
                var ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<MM_EquipmentPicking> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                //需要格式转换的字段
                //columns.ForEach((column) =>
                //{
                //    if (column.ColumnName == "CTime")
                //    {
                //        column.Formattor = ExcelExportFormatter.DateTimeFormatter;
                //    }
                //});
                return ExportToExcelFile<MM_EquipmentPicking>(itemsData, columns, GetCurrentUser().UserName + "_" + modelName);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion
        
        #endregion

        #region Mobile

        #region 查询数据字典信息

        /// <summary>
        /// 查询数据字典信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IHttpActionResult GetDictionaryForEquipment()
        {
            string keyword = "MD001";
            var result = new ResponseData();
            try
            {
                string[] EnumValues = { "Z19", "Z23", };//Z19:非生产性领料 Z23:其他领料
                var itemsData = _dictionaryapp.GetEntity(keyword).Where(x => EnumValues.Contains(x.EnumValue));
                result.Data = itemsData;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 查询库存信息

        /// <summary>
        /// 查询库存信息
        /// </summary>
        /// <param name="ItemCode">物料编号</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetStockInfo([FromUri] string ItemCode)
        {
            string error_message;
            var result = new ResponseData();
            if (_app.CheckDate(ItemCode, out error_message))//校验
            {
                try
                {
                    var itemsData = _app.GetStockInfo(ItemCode,out error_message);
                    if (itemsData == null)
                    {
                        result.Code = (int)WMSStatusCode.Success;
                        result.Message = error_message;
                    }
                    else
                    {
                        result.Data = itemsData;
                        result.Code = (int)WMSStatusCode.Success;
                    }
                }
                catch (Exception ex)
                {
                    result.Code = (int)WMSStatusCode.UnHandledException;
                    result.Message = ex.InnerException?.Message ?? ex.Message;
                }
            }
            else
            {
                result.Code = (int)WMSStatusCode.Success;
                result.Message = error_message;
            }
            return Json(result);
        }

        #endregion

        #region 保存

        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="Parameters">设备领料列表</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Save([FromBody]MM_EquipmentPickingParameters Parameters)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                //string type = ""; //类型 1:提交失败 2：过账失败
                Sys_User currentUser = GetCurrentUser();
                bool bSubmit = _app.Save(Parameters.MqList, Parameters.ManualPostTime, currentUser.LoginAccount, out error_message/*, out type*/);
                if (!bSubmit/*&& type == "1"*/)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
                //else if (!bSubmit && type == "2")
                //{
                //    result.Code = (int)WMSStatusCode.Success;
                //    result.Message = error_message;
                //}
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }

        #endregion

        #endregion
        
        
        // #region 开放API
        //
        // #region 保存
        //
        // /// <summary>
        // /// 保存
        // /// </summary>
        // /// <param name="Parameters">设备领料列表</param>
        // /// <returns></returns>
        // [HttpPost]
        // [AllowAnonymousAttribute]
        // public IHttpActionResult SaveOfOpenApi([FromBody]MM_EquipmentPickingParameters Parameters)
        // {
        //     var result = new ResponseData();
        //     try
        //     {
        //         string error_message = "";
        //         bool bSubmit = _app.Save(Parameters.MqList, Parameters.ManualPostTime, Parameters.MqList[0].HandlenName, out error_message);
        //         if (!bSubmit)
        //         {
        //             result.Code = (int)WMSStatusCode.Failed;
        //             result.Message = error_message;
        //         }
        //         else
        //         {
        //             result.Code = (int)WMSStatusCode.Success;
        //             result.Message = error_message;
        //         }
        //     }
        //     catch (Exception ex)
        //     {
        //         result.Code = (int)WMSStatusCode.UnHandledException;
        //         result.Message = ex.InnerException?.Message ?? ex.Message;
        //     }
        //     return Json(result);
        //
        // }
        //
        // #endregion
        //
        // #endregion

    }
}