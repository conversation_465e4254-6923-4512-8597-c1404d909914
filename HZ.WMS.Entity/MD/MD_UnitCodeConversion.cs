using SqlSugar;
//Chloe框架

namespace HZ.WMS.Entity.MD
{
    /// <summary>
    /// ERP通用单位转换率
    /// </summary>
    public class MD_UnitCodeConversion : BaseEntity
    {
        /// <summary> 
        /// 仓库ID
        /// </summary> 
        [SugarColumn(IsPrimaryKey = true)]
        public string ConversionID { get; set; }
        /// <summary> 
        /// 转换前单位
        /// </summary> 
        public string FromUnitCode { get; set; }
        /// <summary> 
        /// 转换前简称
        /// </summary> 
        public string FromUnitCodeShortDesc { get; set; }

        /// <summary> 
        /// 转换前简称
        /// </summary> 
        public string FromUnitCodeDesc { get; set; }

        /// <summary>
        /// 转换后单位
        /// </summary>
        public string ToUnitCode { get; set; }

        /// <summary>
        /// 转换后单位描述
        /// </summary>
        public string ToUnitCodeDesc { get; set; }


        /// <summary>
        /// 转换率 用乘法运算
        /// </summary>
        public decimal ConvertRate { get; set; }



    }
}
