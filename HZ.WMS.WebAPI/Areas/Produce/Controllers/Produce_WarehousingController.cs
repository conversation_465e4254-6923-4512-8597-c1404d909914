using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;
using HZ.Core.Http;
using HZ.Core.Office;
using HZ.WMS.Application;
using HZ.WMS.Application.PP;
using HZ.WMS.Entity.PP;
using HZ.WMS.Entity.SD;
using HZ.WMS.Entity.Sys;
using HZ.WMS.WebAPI.Controllers;
using SqlSugar;

namespace HZ.WMS.WebAPI.Areas.PP.Controllers
{
    /// <summary>
    /// 完工入库
    /// </summary>
    public class Produce_WarehousingController : ApiBaseController
    {
        private PP_ProductionReportApp _app = new PP_ProductionReportApp();
        private PP_ProductionReportExportViewApp _appView = new PP_ProductionReportExportViewApp();

        #region 查询

        /// <summary>
        /// 获取主表分页列表
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <param name="ProductionLineDes">线体</param>
        /// <param name="dateValue"></param>
        /// <param name="isPosted"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri] Pagination page, [FromUri] string keyword,
            [FromUri] string ProductionLineDes, [FromUri] string ProductionScheduler,
            [FromUri] string SalesOrderNo,
            [FromUri] string SalesOrderLineNo, [FromUri] string ContractNo, [FromUri] string OrderNo,
            [FromUri] DateTime[] dateValue, [FromUri] bool? isPosted)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];

                page.Sort = "ProductionOrderNo desc";

                string tableName = "PP_ProductionReportExport_View";

                if (ProductionScheduler == "101")
                {
                    tableName = "PP_ProductionReportExport_View_sale";
                }
                else if (ProductionScheduler == "201")
                {
                    tableName = "PP_ProductionReportExport_View_part";
                }

                string sql = "select * from " + tableName +
                             " where isDelete = 0 and (ReportType is null or ReportType = 0) and IsCompleted = 1 ";

                if (!string.IsNullOrEmpty(keyword))
                {
                    sql +=
                        "and (SerialNo like @keyword or ProductionReportNo like @keyword or ProductionOrderNo like @keyword " +
                        "or MaterialNo like @keyword or MaterialName like @keyword or ReceivingLocation like @keyword or Shippers like @keyword " +
                        "or EmployeeNumber like @keyword or EmployeeName like @keyword or SalesOrderNo like @keyword or ZORD_CONT like @keyword " +
                        "or CustomerOrderNum like @keyword or CUser like @keyword or Remark like @keyword) ";
                }

                if (!string.IsNullOrEmpty(ProductionLineDes))
                {
                    sql +=
                        "and (ProductionLineCode like @ProductionLineDes or ProductionLineDes like @ProductionLineDes) ";
                }

                if (!string.IsNullOrEmpty(ProductionScheduler))
                {
                    sql += "and (ProductionScheduler = @ProductionScheduler) ";
                }

                if (!string.IsNullOrEmpty(SalesOrderNo))
                {
                    sql += "and (SalesOrderNo like @SalesOrderNo) ";
                }

                if (!string.IsNullOrEmpty(SalesOrderLineNo))
                {
                    sql += "and (SalesOrderLineNo like @SalesOrderLineNo) ";
                }

                if (!string.IsNullOrEmpty(ContractNo))
                {
                    sql += "and (ZORD_CONT like @ContractNo) ";
                }

                if (!string.IsNullOrEmpty(OrderNo))
                {
                    sql += "and (CustomerOrderNum like @OrderNo) ";
                }

                if (isPosted != null)
                {
                    sql += "and (IsPosted = @isPosted) ";
                }

                sql +=
                    "and (StartTime >= @fromTime and StartTime < @toTime) order by ProductionOrderNo desc,CTime desc";


                int offset = (page.PageNumber - 1) * page.PageSize;

                List<SugarParameter> dbParams = new List<SugarParameter>();
                dbParams.Add(new SugarParameter("@keyword", "%" + keyword + "%"));
                dbParams.Add(new SugarParameter("@ProductionLineDes", "%" + ProductionLineDes + "%"));
                dbParams.Add(new SugarParameter("@ProductionScheduler", ProductionScheduler));
                dbParams.Add(new SugarParameter("@SalesOrderNo", "%" + SalesOrderNo + "%"));
                dbParams.Add(new SugarParameter("@SalesOrderLineNo", "%" + SalesOrderLineNo + "%"));
                dbParams.Add(new SugarParameter("@ContractNo", "%" + ContractNo + "%"));
                dbParams.Add(new SugarParameter("@OrderNo", "%" + OrderNo + "%"));
                dbParams.Add(new SugarParameter("@isPosted", isPosted));
                dbParams.Add(new SugarParameter("@fromTime", fromTime));
                dbParams.Add(new SugarParameter("@toTime", toTime));

                page.Total = _appView.DbContext.Ado.SqlQuery<PP_ProductionReportExport_View>(sql, dbParams).Count();

                List<PP_ProductionReportExport_View> itemsData = _appView.DbContext
                    .Ado.SqlQuery<PP_ProductionReportExport_View>(sql, dbParams).Take(offset + page.PageSize).Skip(offset)
                    .ToList();

                itemsData = _appView.HandleEapSerialNo(itemsData);

                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 导出

        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] string keyword, [FromUri] string ProductionLineDes,
            [FromUri] string ProductionScheduler,
            [FromUri] string SalesOrderNo,
            [FromUri] string SalesOrderLineNo, [FromUri] string ContractNo, [FromUri] string OrderNo,
            [FromUri] DateTime[] dateValue, [FromUri] bool? isPosted)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];

                var itemsData = _appView.GetList(
                        t => (string.IsNullOrEmpty(keyword)
                              || t.SerialNo.Contains(keyword)
                              || t.ProductionReportNo.Contains(keyword)
                              || t.ProductionOrderNo.Contains(keyword)
                              || t.MaterialNo.Contains(keyword)
                              || t.MaterialName.Contains(keyword)
                              || t.ReceivingLocation.Contains(keyword)
                              || t.Shippers.Contains(keyword)
                              || t.EmployeeNumber.Contains(keyword)
                              || t.EmployeeName.Contains(keyword)
                              || t.SalesOrderNo.Contains(keyword)
                              || t.ZORD_CONT.Contains(keyword)
                              || t.Shippers.Contains(keyword)
                              || t.CustomerOrderNum.Contains(keyword)
                              || t.CUser.Contains(keyword)
                              || t.Remark.Contains(keyword))
                             && (string.IsNullOrEmpty(ProductionLineDes) ||
                                 t.ProductionLineCode.Contains(ProductionLineDes) ||
                                 t.ProductionLineDes.Contains(ProductionLineDes))
                             && (string.IsNullOrEmpty(ProductionScheduler) ||
                                 t.ProductionScheduler.Contains(ProductionScheduler))
                             && (string.IsNullOrEmpty(SalesOrderNo) || t.SalesOrderNo.Contains(SalesOrderNo))
                             && (string.IsNullOrEmpty(SalesOrderLineNo) || t.SalesOrderLineNo == SalesOrderLineNo)
                             && (string.IsNullOrEmpty(ContractNo) || t.ZORD_CONT == ContractNo)
                             && (string.IsNullOrEmpty(OrderNo) || t.CustomerOrderNum == OrderNo)
                             && (t.ReportType == null || t.ReportType == 0)
                             && t.IsCompleted == true //最后一道工序
                             && (isPosted == null || t.IsPosted == isPosted)
                             && (t.StartTime >= fromTime && t.StartTime < toTime))
                    .ToList();

                itemsData = _appView.HandleEapSerialNo(itemsData);
                //var IDs = itemsData.Select(x => x.ID).Distinct().ToArray();
                //var itemsData2 = _app.GetProductionReportExport(string.Join(",", IDs));


                List<ExcelColumn<PP_ProductionReportExport_View>> columns =
                    ExcelService.FetchDefaultColumnList<PP_ProductionReportExport_View>();
                string[] ignoreField = new string[]
                {
                    "ID", "ProductionLineCode", "ProductionLineDes", "MaterialGroupCode", "MaterialGroupDes",
                    "ReportType", "IsCompleted", "IsPosted", "PostUser", "PostTime", "ManualPostTime", "SapDocNum",
                    "IsDelete", "MUser", "MTime", "DUser", "DTime"
                };
                List<ExcelColumn<PP_ProductionReportExport_View>> ignoreFieldList =
                    columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<PP_ProductionReportExport_View> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }

                var colum = columns[4];
                columns.RemoveAt(4);
                columns.Add(colum);
                //columns.ForEach((column) =>
                //{
                //    if (column.ColumnName == "IsPosted")
                //    {
                //        column.Formattor = ExcelExportFormatter.IsOrNoFormatter;
                //    }
                //});

                return ExportToExcelFile<PP_ProductionReportExport_View>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #region 过账

        [HttpPost]
        public IHttpActionResult DoPost([FromBody] List<PP_ProductionReport> entities)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                Sys_User currentUser = GetCurrentUser();
                bool bSubmit = _app.DoPost(entities, currentUser.LoginAccount, out error_message);
                result.Message = error_message;

                if (!bSubmit)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                    result.MessageParam = 2010;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = "过账成功";
                    result.MessageParam = 2000;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 取消过账

        /// <summary>
        /// 取消过账
        /// </summary>
        /// <param name="DocNums">取消过账</param>
        /// <returns></returns>
        [HttpGet]
        //[AllowAnonymous]
        public IHttpActionResult Audit([FromUri] string[] Ids)
        {
            //[FromBody]string[] ids
            var result = new ResponseData();
            try
            {
                string error_message = "";
                bool bAudits = _app.Audits(Ids, GetCurrentUser().LoginAccount);
                if (!bAudits)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2000;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 导入

        /// <summary>
        /// 导入
        /// </summary>
        /// <param name="entitys">接收的导入集合</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult ImportExcelToData([FromBody] List<SD_DeliveryScanImport> entitys)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                if (_app.ImprotExcelToBaseData(entitys, GetCurrentUser().LoginAccount, out error_message))
                {
                    result.Data = true;
                    result.Code = (int)WMSStatusCode.Success;
                }
                else
                {
                    result.Data = false;
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }

            return Json(result);
        }

        #endregion

        #region 完工自动跳转发运

        /// <summary>
        /// 获取主表分页列表
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <param name="ProductionLineDes">线体</param>
        /// <param name="dateValue"></param>
        /// <param name="isPosted"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult SetShippingToDelivery([FromUri] DateTime[] dateValue)
        {
            var result = new ResponseData();
            try
            {
                _app.SetShippingToDelivery(dateValue);
                result.Message = "同步成功";
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion
        
    }
}