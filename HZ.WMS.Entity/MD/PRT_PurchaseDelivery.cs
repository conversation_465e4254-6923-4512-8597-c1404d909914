using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.MD
{
    public class PRT_PurchaseDelivery
    {
        public Int64 RowID { get; set; }
        public string SupplierCode { get; set; }
        public string SupplierName { get; set; }
        public string ItemCode { get; set; }
        public string ItemName { get; set; }
        public string ItmsGrpCode { get; set; }
        public string ItmsGrpName { get; set; }
        public Int32 TotalDeliveryItem { get; set; }
        public Int32 OnTimeDeliveryItem { get; set; }
        public Int32 UnOnTimeDeliveryItem { get; set; }
        public decimal? TotalQty { get; set; }
        public decimal? TotalDeliveryedQty { get; set; }
        public decimal? TotalUnOnTimeDeliveryQty { get; set; }
        public Int32 OTDRate { get; set; }
    }
}
