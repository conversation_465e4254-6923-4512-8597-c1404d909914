using HZ.Core.Http;
using HZ.Core.Office;
using HZ.WMS.Application;
using HZ.WMS.Application.PP;
using HZ.WMS.Application.Sys;
using HZ.WMS.Entity.PP;
using HZ.WMS.Entity.PP.Dto;
using HZ.WMS.Entity.Sys;
using HZ.WMS.WebAPI.Controllers;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web;
using System.Web.Http;

namespace HZ.WMS.WebAPI.Areas.PP.Controllers
{
    /// <summary>
    /// 超领料申请
    /// </summary>
    public class PP_OverReceiveController : ApiBaseController
    {
        private PP_OverReceiveApp _app = new PP_OverReceiveApp();
        private PP_OverReceiveDetailApp detailApp = new PP_OverReceiveDetailApp();
        private Sys_SwithConfigApp _switchApp = new Sys_SwithConfigApp();

        #region 查询

        /// <summary>
        /// 获取主表分页列表
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <param name="ExamineStatus">审核状态</param>
        /// <param name="dateValue"></param>
        /// <param name="isPosted"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]string keyword, [FromUri]int? ExamineStatus, [FromUri] DateTime[] dateValue, [FromUri]bool? isPosted)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];

                var itemsData = _app.GetPageList(page,
                    t => (string.IsNullOrEmpty(keyword)
                        || t.OverReceiveNo.Contains(keyword)
                        || t.PostUser.Contains(keyword)
                        || t.CUser.Contains(keyword))
                        && (ExamineStatus == null || t.ExamineStatus == ExamineStatus)
                        && (t.CTime >= fromTime && t.CTime < toTime)
                        && (isPosted == null || t.IsPosted == isPosted))
                    .ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 查询子表分页列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetDetailPageList([FromUri]Pagination page, [FromUri]string overReceiveNo)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = detailApp.GetPageList(page, w => overReceiveNo == w.OverReceiveNo).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 查询子表列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetDetailList([FromUri]string overReceiveNo)
        {
            var result = new ResponseData();
            try
            {
                result.Data = detailApp.GetList(w => overReceiveNo == w.OverReceiveNo).ToList();
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取系统生成的单号

        /// <summary>
        /// 获取系统生成的单号
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetDocNum()
        {
            var result = new ResponseData();
            try
            {
                result.Data = base.GenerateDocNum(DocType.PP, DocFixedNumDef.PP_OverReceive);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取SAP生产订单

        /// <summary>
        /// 获取SAP生产订单
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <param name="ProductionOrderNo">生产订单号</param>
        /// <param name="ComponentCode">物料编号/物料名称</param>
        /// <param name="dateValue">装配日期</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetSapOrderList([FromUri]Pagination page, [FromUri]string keyword, [FromUri]string ProductionOrderNo, [FromUri]string ComponentCode, [FromUri] DateTime[] dateValue, [FromUri]string SerialNo)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];

                var itemsData = detailApp.GetSapPageList(page,
                    w => (string.IsNullOrEmpty(keyword)
                        || w.ProductionOrderNo.Contains(keyword)
                        || w.ComponentCode.Contains(keyword)
                        || w.SalesOrderNo.Contains(keyword))
                        && (string.IsNullOrEmpty(ProductionOrderNo) || w.ProductionOrderNo.Contains(ProductionOrderNo))
                        && (string.IsNullOrEmpty(SerialNo) || w.SerialNo.Contains(SerialNo))
                        && (string.IsNullOrEmpty(ComponentCode) || w.ComponentCode.Contains(ComponentCode) || w.MaterialName.Contains(ComponentCode))
                        && (w.StartTime >= fromTime && w.StartTime < toTime)
                        ).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 增加

        [HttpPost]
        public IHttpActionResult Add([FromBody]PP_OverReceive_Dto overReceive)
        {
            var result = new ResponseData();
            try
            {
                Sys_User user = GetCurrentUser();
                var entity = new PP_OverReceive
                {
                    OverReceiveNo = overReceive.OverReceiveNo,
                    MovementType = overReceive.MovementType,
                    FileName = overReceive.FileName,
                    FilePath = overReceive.FilePath,
                    Remark = overReceive.Remark,
                    CUser = user.LoginAccount,
                    ManualPostTime = overReceive.ManualPostTime,
                };
                overReceive.DetailList.ForEach(item => 
                {
                    item.OverReceiveNo = overReceive.OverReceiveNo;
                    item.MovementType = overReceive.MovementType;
                    item.CUser = user.LoginAccount;
                    item.ManualPostTime = overReceive.ManualPostTime;
                });

                if (!_app.Add(entity, overReceive.DetailList))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                }
                //else
                //{
                //    //是否自动过账判定
                //    if (new Sys_SwithConfigApp().IsPPOverPickingAutoPost)
                //    {
                //        string error_message = "";
                //        bool bSubmit = _app.DoPost(new List<PP_OverReceive> { entity }, user.LoginAccount, out error_message);

                //        if (!bSubmit)
                //        {
                //            result.Message = "保存成功；过账失败，失败原因：" + error_message;
                //        }
                //    }
                //}
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 更新

        [HttpPost]
        public IHttpActionResult Update([FromBody]PP_OverReceive_Dto overReceive)
        {
            var result = new ResponseData();
            try
            {
                Sys_User user = GetCurrentUser();
                if (!_app.Edit(overReceive,user.LoginAccount))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除

        [HttpDelete]
        public IHttpActionResult Delete([FromBody]string[] DocNums)
        {
            var result = new ResponseData();
            try
            {
                Sys_User user = GetCurrentUser();
                var list = _app.GetList(t => DocNums.Contains(t.OverReceiveNo)).ToList();
                string error_message = "";
                var mark = _app.ScanDataDeleteValidation(list, out error_message);
                if(mark)
                {
                    var detailList = detailApp.GetList(w => DocNums.Contains(w.OverReceiveNo)).ToList();
                    _app.Delete(user.LoginAccount, list, detailList);
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 上传

        [HttpPost]
        public IHttpActionResult Upload()
        {
            var result = new ResponseData();
            try
            {
                //获取参数信息
                HttpContextBase context = (HttpContextBase)Request.Properties["MS_HttpContext"];
                HttpRequestBase request = context.Request;
                string fileName = request.Params["TempleteFile"]?.ToString();
                fileName = fileName.Replace(Path.GetFileNameWithoutExtension(fileName), Guid.NewGuid().ToString());
                string resPath = ConfigurationManager.AppSettings["PP_OverReceiveUploadPath"]?.ToString() + fileName;
                string filePath = HttpContext.Current.Server.MapPath("~/") + resPath;
                request.Files[0].SaveAs(filePath);
                result.Data = new { FilePath = "/" + resPath.Replace("\\", "/") };
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 审核

        [HttpPost]
        public IHttpActionResult Examine(Examine examine)
        {
            var result = new ResponseData();
            try
            {
                Sys_User loginUser = GetCurrentUser();
                if (examine.entities.Any(w => w.ExamineStatus + 1 != examine.status))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "请检查审核状态";
                }
                else
                {
                    examine.entities.ForEach(entity =>
                    {
                        entity.MUser = loginUser.LoginAccount;
                        entity.ExamineStatus = examine.status;
                        if(examine.status == 2)
                        {
                            entity.WarehouseReviewer = loginUser.LoginAccount;
                            entity.WarehouseTime = DateTime.Now;
                        }
                        else if(examine.status == 1)
                        {
                            entity.PlanReviewer = loginUser.LoginAccount;
                            entity.PlannedTime = DateTime.Now;
                        }
                    });
                    _app.UpdateWithTran(examine.entities);

                    //仓库审核后直接扣料
                    if (examine.status == 2 && _switchApp.IsPPOverPickingAutoPost)
                    {
                        string error_message = "";
                        bool bSubmit = _app.DoPost(examine.entities, loginUser.LoginAccount, out error_message);

                        if (!bSubmit)
                        {
                            result.Code = (int)WMSStatusCode.Failed;
                            result.Message = error_message;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 取消审核

        [HttpPost]
        public IHttpActionResult CancelExamine(Examine examine)
        {
            var result = new ResponseData();
            try
            {
                Sys_User loginUser = GetCurrentUser();
                examine.entities.ForEach(entity =>
                {
                    entity.MUser = loginUser.LoginAccount;
                    entity.ExamineStatus = 0;
                });
                _app.UpdateWithTran(examine.entities);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 导出

        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri]Pagination page, [FromUri]string keyword, [FromUri] DateTime[] dateValue, [FromUri]bool? isPosted)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];

                var itemsData = detailApp.GetList(
                    t => (string.IsNullOrEmpty(keyword)
                        || t.OverReceiveNo.Contains(keyword)
                        || t.ProductionOrderNo.Contains(keyword)
                        || t.ComponentCode.Contains(keyword)
                        || t.SalesOrderNo.Contains(keyword))
                        && t.CTime >= fromTime && t.CTime < toTime
                        && (isPosted == null || t.IsPosted == isPosted)
                    ).ToList();

                List<ExcelColumn<PP_OverReceiveDetail>> columns = ExcelService.FetchDefaultColumnList<PP_OverReceiveDetail>();
                string[] ignoreField = new string[]
                {
                    "ID"
                    ,"ProductionLineCode"
                    ,"MaterialGroupCode"
                    ,"IsPosted"
                    ,"PostUser"
                    ,"PostTime"
                    ,"ManualPostTime"
                    ,"SapDocNum"
                    ,"SapLine"
                    ,"IsDelete"
                    ,"MUser"
                    ,"MTime"
                    ,"DUser"
                    ,"DTime"
                };
                List<ExcelColumn<PP_OverReceiveDetail>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<PP_OverReceiveDetail> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                //columns.ForEach((column) =>
                //{
                //    if (column.ColumnName == "IsPosted")
                //    {
                //        column.Formattor = ExcelExportFormatter.IsOrNoFormatter;
                //    }
                //});

                return ExportToExcelFile<PP_OverReceiveDetail>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #region 过账

        [HttpPost]
        public IHttpActionResult DoPost([FromBody]List<PP_OverReceive> entities)
        {
            var result = new ResponseData();
            try
            {
                if(entities.Any(w => w.ExamineStatus != 2))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "存在未经过仓库审核的数据，不允许过账";
                }
                else
                {
                    string error_message = "";
                    Sys_User currentUser = GetCurrentUser();
                    bool bSubmit = _app.DoPost(entities, currentUser.LoginAccount, out error_message);

                    if (!bSubmit)
                    {
                        result.Code = (int)WMSStatusCode.Failed;
                        result.Message = error_message;
                    }
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 打印超领申请单

        /// <summary>
        /// 
        /// </summary>
        /// <param name="docNums">超领单号</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult PrintApply([FromUri]string[] docNums)
        {
            var result = new ResponseData();
            try
            {
                DevExpress.XtraReports.UI.XtraReport report = DevExpress.XtraReports.UI.XtraReport.FromFile(HttpContext.Current.Server.MapPath("~/") + ConfigurationManager.AppSettings["LabelTemplateUploadPath"]?.ToString() + "超额领料单.repx", true);
                DevExpress.DataAccess.Sql.SqlDataSource dataSource = report.DataSource as DevExpress.DataAccess.Sql.SqlDataSource;
                DevExpress.DataAccess.Sql.QueryParameter parameter = new DevExpress.DataAccess.Sql.QueryParameter()
                {
                    Name = "docNums",
                    Type = typeof(string[]),
                    Value = docNums
                };
                dataSource.Queries[0].Parameters[0] = parameter;
                return base.PrintToPDF(report);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion
    }
}
