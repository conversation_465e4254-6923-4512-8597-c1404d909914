using HZ.Core.Http;
using HZ.WMS.Application.MD;
using HZ.WMS.Application.PO;
using HZ.WMS.Application.Sys;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.MM;
using HZ.WMS.Entity.PO;
using System;
using System.Collections.Generic;
using System.Linq;

namespace HZ.WMS.Application.MM
{
    /// <summary>
    /// 没有out_message消息的函数，意味着只在最前端做未处理异常捕捉
    /// </summary>

    public class MM_TakeStockPlanDetailedApp : BaseApp<MM_TakeStockPlanDetailed>
    {
        private MM_TakeStockScanApp MM_TakeStockScanApp = new MM_TakeStockScanApp();
        private MD_StockApp MD_StockApp = new MD_StockApp();
        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public MM_TakeStockPlanDetailedApp() : base()
        {
        }




        #endregion

        #region 获取列表

        /// <summary>
        /// 获取列表
        /// </summary>
        /// <returns></returns>
        public List<MM_TakeStockPlanDetailed> GetList()
        {
            return base.GetList().ToList();
        }

        #endregion

        #region 获取分页列表

        /// <summary>
        /// 分页查询
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyWard"></param>
        /// <returns></returns>
        public List<MM_TakeStockPlanDetailed> GetPageList(Pagination page)
        {
            // 无关键字查询所有数据
            return base.GetPageList(page);
        }

        #endregion

        #region 添加

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public MM_TakeStockPlanDetailed Insert(MM_TakeStockPlanDetailed entity)
        {
            return base.Insert(entity);
        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public int Delete(MM_TakeStockPlanDetailed entity)
        {
            return base.Delete(entity);
        }

        #endregion

        #region 批量删除

        /// <summary>
        /// 批量删除
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public int DeleteByIDS(object[] ids, string deleteUser)
        {
            return base.DeleteByKeys(ids, deleteUser);
        }

        #endregion

        #region 更新

        /// <summary>
        /// 更新，只能修改备注，是否已读，已读时间，修改时间，修改人Id这几个字段
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public int Update(MM_TakeStockPlanDetailed entity)
        {
            return base.Update(entity);
        }

        #endregion

        /// <summary>
        /// 开始
        /// </summary>
        /// <param name="primarys"></param>
        /// <param name="status"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        public bool AddList(List<MM_TakeStockPlan> primarys, int status, string user)
        {
            var result = false;
            try
            {
                MM_TakeStockPlanApp _TakeStockPlanApp = new MM_TakeStockPlanApp();
                //PO_BarCodeApp PO_BarCodeApp = new PO_BarCodeApp();
                Sys_SAPCompanyInfoApp _SAPCompanyInfoApp = new Sys_SAPCompanyInfoApp();

                //List<MM_TakeStockScan> scanList = new List<MM_TakeStockScan>();
                List<MM_TakeStockPlan> List = new List<MM_TakeStockPlan>();
                List<MM_TakeStockPlanDetailed> ListDetailed = new List<MM_TakeStockPlanDetailed>();
                foreach (MM_TakeStockPlan primary in primarys)
                {

                    //更新主单
                    var tmp = _TakeStockPlanApp.GetList(x => x.DocNum == primary.DocNum).ToList().FirstOrDefault();
                    tmp.MTime = DateTime.Now;
                    tmp.MUser = user;
                    tmp.Remark = primary.Remark;
                    tmp.Status = status;
                    tmp.PUser = primary.PUser;
                    List.Add(tmp);
                    if (status == 2)//更新主表状态，判断是否是状态2 已开始
                    {
                        //获取sap账面量
                        var list =GetList(x => x.DocNum == primary.DocNum)?.ToList();
                        foreach (MM_TakeStockPlanDetailed info in list)
                        {
                            string error_message = "";
                            List<MD_Stock> resul = _SAPCompanyInfoApp.ZFGWMS024("001", "2002", info.ItemCode, info.WhsCode, 
                                info.SpecialStock == null ? "" : info.SpecialStock, info.SupplierCode == null ? "" : info.SupplierCode, out error_message);
                            foreach (MD_Stock mod in resul)
                            {
                                if (info.SpecialStock == "E")
                                {
                                    if (info.SaleNum == mod.SaleNum && info.SaleLine == mod.SaleLine)
                                    {
                                        info.Qty = mod.Qty;
                                    }
                                }
                                else
                                {
                                    info.Qty = mod.Qty;
                                }

                            }

                            ListDetailed.Add(info);

                        }
                    }
                }
                DbContext.Ado.BeginTran();
                _TakeStockPlanApp.DbContext = this.DbContext;
                MM_TakeStockScanApp.DbContext = this.DbContext;
                MD_StockApp.DbContext = this.DbContext;

                if (_TakeStockPlanApp.Update(List) > 0)
                {
                    if (ListDetailed?.Count > 0)
                        Update(ListDetailed);
                }
                result = true;
                DbContext.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                if (DbContext.Ado.IsAnyTran())
                {
                    DbContext.Ado.RollbackTran();
                }
                throw ex;
            }
            return result;
        }

        /// <summary>
        /// 添加盘点主从表记录，并生成账面数据
        /// </summary>
        /// <param name="primary"></param>
        /// <param name="list"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        public bool AddList(MM_TakeStockPlan primary, List<MM_TakeStockPlanDetailed> list, string user)
        {
            bool result = false;
            try
            {
                MM_TakeStockPlanApp _TakeStockPlanApp = new MM_TakeStockPlanApp();

                primary.PlanID = Guid.NewGuid().ToString();
                primary.CUser = user;
                primary.CTime = DateTime.Now;
                primary.IsDelete = false;
                list.ForEach(x =>
                {
                    x.PlanID = Guid.NewGuid().ToString();
                    x.CUser = user;
                    x.CTime = DateTime.Now;
                    x.DocNum = primary.DocNum;
                    x.IsDelete = false;
                });

                DbContext.Ado.BeginTran();
                _TakeStockPlanApp.DbContext = this.DbContext;
                MM_TakeStockScanApp.DbContext = this.DbContext;
                MD_StockApp.DbContext = this.DbContext;

                var p = _TakeStockPlanApp.Insert(primary);
                if (p != null)
                {
                    if (this.Insert(list) > 0)
                    {
                        #region 注释
                        //    if (primary.Status == 2)//&& pApp.LockStock(user))
                        //    {//已开始
                        //     //生成账面数据
                        //        list.ForEach(x =>
                        //        {
                        //            List<MM_TakeStockScan> scanList = new List<MM_TakeStockScan>();
                        //            //查找所有库存
                        //            var stockList = MD_StockApp.GetList(y => (string.IsNullOrEmpty(x.ItemCode) || y.ItemCode == x.ItemCode) &&
                        //    (string.IsNullOrEmpty(x.BinLocationCode) || y.BinLocationCode == x.BinLocationCode) &&
                        //    (string.IsNullOrEmpty(x.RegionCode) || y.RegionCode == x.RegionCode))?.ToList();

                        //            //遍历库存，生成账面记录
                        //            stockList.ForEach(y =>
                        //{
                        //    var MM_TakeStockScan = new MM_TakeStockScan();
                        //    MM_TakeStockScan.ScanID = Guid.NewGuid().ToString();
                        //    MM_TakeStockScan.DocNum = primary.DocNum;                 //此时不生成单号，在PDA扫描的时候再更新
                        //    MM_TakeStockScan.BoxBarCode = y.BoxBarCode;
                        //    MM_TakeStockScan.BarCode = y.BarCode;
                        //    MM_TakeStockScan.BatchNum = y.BatchNum;
                        //    MM_TakeStockScan.PTime = y.PTime;
                        //    MM_TakeStockScan.ItemCode = y.ItemCode;
                        //    MM_TakeStockScan.ItemName = y.ItemName;
                        //    MM_TakeStockScan.ItmsGrpCode = y.ItmsGrpCode;
                        //    MM_TakeStockScan.ItmsGrpName = y.ItmsGrpName;
                        //    MM_TakeStockScan.StockQty = y.Qty.Value;
                        //    MM_TakeStockScan.ScanQty = 0;
                        //    MM_TakeStockScan.DiffQty = 0;
                        //    MM_TakeStockScan.Unit = y.Unit;
                        //    MM_TakeStockScan.WhsCode = "SZ1";
                        //    MM_TakeStockScan.WhsName = "SZ1";
                        //    MM_TakeStockScan.RegionCode = y.RegionCode;
                        //    MM_TakeStockScan.RegionName = y.RegionName;
                        //    MM_TakeStockScan.BinLocationCode = y.BinLocationCode;
                        //    MM_TakeStockScan.BinLocationName = y.BinLocationName;
                        //    MM_TakeStockScan.IsConfirm = false;
                        //    MM_TakeStockScan.CTime = DateTime.Now;
                        //    MM_TakeStockScan.CUser = user;
                        //    MM_TakeStockScan.Remark = "系统自动生成";
                        //    MM_TakeStockScan.IsDelete = false;
                        //    scanList.Add(MM_TakeStockScan);
                        //});

                        //            //不在foreach外insert是为了防止一次性insert过多记录
                        //            if (scanList?.Count > 0)
                        //                MM_TakeStockScanApp.Insert(scanList);
                        //        });
                        //    }
                        #endregion
                        result = true;
                    }
                    else
                    {
                        if (DbContext.Ado.IsAnyTran())
                        {
                            DbContext.Ado.RollbackTran();
                        }
                    }
                }
                DbContext.Ado.CommitTran();
            }
            catch (System.Exception ex)
            {
                if (DbContext.Ado.IsAnyTran())
                {
                    DbContext.Ado.RollbackTran();
                }
                throw ex;
            }
            return result;
        }

        /// <summary>
        /// 更新盘点主从表记录，并生成账面数据
        /// </summary>
        /// <param name="primary"></param>
        /// <param name="list"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        public bool UpdateList(MM_TakeStockPlan primary, List<MM_TakeStockPlanDetailed> list, string user)
        {
            bool result = false;
            try
            {
                MM_TakeStockPlanApp _TakeStockPlanApp = new MM_TakeStockPlanApp();
            
                //更新主单
                var tmp = _TakeStockPlanApp.GetList(x => x.DocNum == primary.DocNum).ToList().FirstOrDefault();
                tmp.MTime = DateTime.Now;
                tmp.MUser = user;
                tmp.Remark = primary.Remark;
                tmp.Status = primary.Status;
                tmp.PUser = primary.PUser;
                list.ForEach(x =>
                {
                    x.PlanID = Guid.NewGuid().ToString();
                    x.CUser = user;
                    x.CTime = DateTime.Now;
                    x.DocNum = primary.DocNum;
                    x.IsDelete = false;
                });

                DbContext.Ado.BeginTran();
                _TakeStockPlanApp.DbContext = this.DbContext;
                MM_TakeStockScanApp.DbContext = this.DbContext;
                MD_StockApp.DbContext = this.DbContext;

                if (_TakeStockPlanApp.Update(tmp) > 0)
                {
                    //删除原有，新增现有
                    this.Delete(x => x.DocNum == primary.DocNum, user);
         

                    if (this.Insert(list) > 0)
                    {
                        #region 注释
                        //if (primary.Status == 2)//&& app.LockStock(user))
                        //{//已开始，生成账面数据
                        //    list.ForEach(x =>
                        //    {
                        //        List<MM_TakeStockScan> scanList = new List<MM_TakeStockScan>();
                        //        //查找所有库存
                        //        var stockList = MD_StockApp.GetList(y => (string.IsNullOrEmpty(x.ItemCode) || y.ItemCode == x.ItemCode) &&
                        //(string.IsNullOrEmpty(x.BinLocationCode) || y.BinLocationCode == x.BinLocationCode) &&
                        //(string.IsNullOrEmpty(x.RegionCode) || y.RegionCode == x.RegionCode))?.ToList();

                        //        //遍历库存，生成账面记录
                        //        stockList.ForEach(y =>
                        //{
                        //    var MM_TakeStockScan = new MM_TakeStockScan();
                        //    MM_TakeStockScan.ScanID = Guid.NewGuid().ToString();
                        //    MM_TakeStockScan.DocNum = primary.DocNum;                 //此时不生成单号，在PDA扫描的时候再更新
                        //    MM_TakeStockScan.BoxBarCode = y.BoxBarCode;
                        //    MM_TakeStockScan.BarCode = y.BarCode;
                        //    MM_TakeStockScan.BatchNum = y.BatchNum;
                        //    MM_TakeStockScan.PTime = y.PTime;
                        //    MM_TakeStockScan.ItemCode = y.ItemCode;
                        //    MM_TakeStockScan.ItemName = y.ItemName;
                        //    MM_TakeStockScan.ItmsGrpCode = y.ItmsGrpCode;
                        //    MM_TakeStockScan.ItmsGrpName = y.ItmsGrpName;
                        //    MM_TakeStockScan.StockQty = y.Qty.Value;
                        //    MM_TakeStockScan.ScanQty = 0;
                        //    MM_TakeStockScan.DiffQty = 0;
                        //    MM_TakeStockScan.Unit = y.Unit;
                        //    MM_TakeStockScan.WhsCode = "SZ1";
                        //    MM_TakeStockScan.WhsName = "SZ1";
                        //    MM_TakeStockScan.RegionCode = y.RegionCode;
                        //    MM_TakeStockScan.RegionName = y.RegionName;
                        //    MM_TakeStockScan.BinLocationCode = y.BinLocationCode;
                        //    MM_TakeStockScan.BinLocationName = y.BinLocationName;
                        //    MM_TakeStockScan.IsConfirm = false;
                        //    MM_TakeStockScan.CTime = DateTime.Now;
                        //    MM_TakeStockScan.CUser = "SYS";
                        //    MM_TakeStockScan.Remark = "系统自动生成";
                        //    MM_TakeStockScan.IsDelete = false;
                        //    scanList.Add(MM_TakeStockScan);
                        //});

                        //        //不在foreach外insert是为了防止一次性insert过多记录
                        //        if (scanList?.Count > 0)
                        //            MM_TakeStockScanApp.Insert(scanList);
                        //    });
                        //}
                        #endregion
                        result = true;
                    }
                    else
                    {
                        if (DbContext.Ado.IsAnyTran())
                        {
                            DbContext.Ado.RollbackTran();
                        }
                    }
                }
                DbContext.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                if (DbContext.Ado.IsAnyTran())
                {
                    DbContext.Ado.RollbackTran();
                }
                throw ex;
            }
            return result;
        }
    }
}

