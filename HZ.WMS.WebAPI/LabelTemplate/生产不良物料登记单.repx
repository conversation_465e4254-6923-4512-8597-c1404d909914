/// <XRTypeInfo>
///   <AssemblyFullName>DevExpress.XtraReports.v16.1, Version=16.1.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</AssemblyFullName>
///   <AssemblyLocation>C:\Windows\Microsoft.Net\assembly\GAC_MSIL\DevExpress.XtraReports.v16.1\v4.0_16.1.5.0__b88d1754d700e49a\DevExpress.XtraReports.v16.1.dll</AssemblyLocation>
///   <TypeName>DevExpress.XtraReports.UI.XtraReport</TypeName>
///   <Localization>zh-CN</Localization>
///   <Version>16.1</Version>
///   <Resources>
///     <Resource Name="XtraReportSerialization.PPFTTM">
/// zsrvvgEAAACRAAAAbFN5c3RlbS5SZXNvdXJjZXMuUmVzb3VyY2VSZWFkZXIsIG1zY29ybGliLCBWZXJzaW9uPTQuMC4wLjAsIEN1bHR1cmU9bmV1dHJhbCwgUHVibGljS2V5VG9rZW49Yjc3YTVjNTYxOTM0ZTA4OSNTeXN0ZW0uUmVzb3VyY2VzLlJ1bnRpbWVSZXNvdXJjZVNldAIAAAABAAAAAAAAAFBBRFBBRFA4hdt+AAAAAA8BAABOcwBxAGwARABhAHQAYQBTAG8AdQByAGMAZQAxAC4AUgBlAHMAdQBsAHQAUwBjAGgAZQBtAGEAUwBlAHIAaQBhAGwAaQB6AGEAYgBsAGUAAAAAAAGUBlBFUmhkR0ZUWlhRZ1RtRnRaVDBpYzNGc1JHRjBZVk52ZFhKalpURWlQanhXYVdWM0lFNWhiV1U5SWxCUVgwWlVWRTBpUGp4R2FXVnNaQ0JPWVcxbFBTSkViMk5PZFcwaUlGUjVj
/// R1U5SWxOMGNtbHVaeUlnTHo0OFJtbGxiR1FnVG1GdFpUMGlRbUZ6WlU1MWJTSWdWSGx3WlQwaVUzUnlhVzVuSWlBdlBqeEdhV1ZzWkNCT1lXMWxQU0pRVEdsdVpTSWdWSGx3WlQwaVUzUnlhVzVuSWlBdlBqeEdhV1ZzWkNCT1lXMWxQU0pEVkdsdFpTSWdWSGx3WlQwaVJHRjBaVlJwYldVaUlDOCtQRVpwWld4a0lFNWhiV1U5SWxCUVgwWlVWRTFFWlhSaGFXeGxaRjlFYjJOT2RXMGlJRlI1Y0dVOUlsTjBjbWx1WnlJZ0x6NDhSbWxsYkdRZ1RtRnRaVDBpVW1WaGMyOXVjME52WkdVaUlGUjVjR1U5SWxOMGNtbHVaeUlnTHo0OFJtbGxiR1FnVG1GdFpUMGlVbVZoYzI5dWMwUmxjMk1pSUZSNWNHVTlJbE4wY21sdVp5SWdMejQ4Um1sbGJHUWdUbUZ0WlQwaVVYUjVJaUJV
/// ZVhCbFBTSkVaV05wYldGc0lpQXZQanhHYVdWc1pDQk9ZVzFsUFNKWFEyOXVabWx5YlNJZ1ZIbHdaVDBpUW05dmJHVmhiaUlnTHo0OFJtbGxiR1FnVG1GdFpUMGlTVU52Ym1acGNtMGlJRlI1Y0dVOUlrSnZiMnhsWVc0aUlDOCtQRVpwWld4a0lFNWhiV1U5SWtsMFpXMURiMlJsSWlCVWVYQmxQU0pUZEhKcGJtY2lJQzgrUEVacFpXeGtJRTVoYldVOUlrbDBaVzFPWVcxbElpQlVlWEJsUFNKVGRISnBibWNpSUM4K1BFWnBaV3hrSUU1aGJXVTlJa1JsZEdGcGJHVmtTVVFpSUZSNWNHVTlJbE4wY21sdVp5SWdMejQ4TDFacFpYYytQQzlFWVhSaFUyVjBQZz09</Resource>
///   </Resources>
/// </XRTypeInfo>
namespace XtraReportSerialization {
    
    public class PPFTTM : DevExpress.XtraReports.UI.XtraReport {
        private DevExpress.XtraReports.UI.XRTable table2;
        private DevExpress.XtraReports.UI.XRTableRow tableRow2;
        private DevExpress.XtraReports.UI.XRTableCell tableCell9;
        private DevExpress.XtraReports.UI.XRTableCell tableCell13;
        private DevExpress.XtraReports.UI.XRTableRow tableRow3;
        private DevExpress.XtraReports.UI.XRTableCell tableCell12;
        private DevExpress.XtraReports.UI.XRTableCell tableCell14;
        private DevExpress.XtraReports.UI.XRTableCell tableCell28;
        private DevExpress.XtraReports.UI.XRTableRow tableRow1;
        private DevExpress.XtraReports.UI.XRTableCell tableCell1;
        private DevExpress.XtraReports.UI.XRTableCell tableCell2;
        private DevExpress.XtraReports.UI.XRTableCell tableCell3;
        private DevExpress.XtraReports.UI.XRTableCell tableCell6;
        private DevExpress.XtraReports.UI.XRTableCell tableCell4;
        private DevExpress.XtraReports.UI.XRTableCell tableCell7;
        private DevExpress.XtraReports.UI.XRTableCell tableCell10;
        private DevExpress.XtraReports.UI.XRTableCell tableCell11;
        private DevExpress.XtraReports.UI.CalculatedField calculatedField1;
        private DevExpress.XtraReports.UI.XRTableCell tableCell22;
        private DevExpress.XtraReports.UI.XRTableCell tableCell24;
        private DevExpress.XtraReports.UI.BottomMarginBand BottomMargin;
        private DevExpress.DataAccess.Sql.SqlDataSource sqlDataSource1;
        private DevExpress.XtraReports.UI.XRTable table3;
        private DevExpress.XtraReports.UI.XRTableRow tableRow4;
        private DevExpress.XtraReports.UI.XRTableCell tableCell18;
        private DevExpress.XtraReports.UI.XRTableCell tableCell15;
        private DevExpress.XtraReports.UI.XRTableCell tableCell16;
        private DevExpress.XtraReports.UI.XRTableCell tableCell19;
        private DevExpress.XtraReports.UI.XRTableCell tableCell20;
        private DevExpress.XtraReports.UI.XRTableCell tableCell17;
        private DevExpress.XtraReports.UI.XRTableCell tableCell21;
        private DevExpress.XtraReports.UI.XRTableCell tableCell23;
        private DevExpress.XtraReports.UI.TopMarginBand TopMargin;
        private DevExpress.XtraReports.UI.XRTableCell tableCell27;
        private DevExpress.XtraReports.UI.GroupHeaderBand GroupHeader1;
        private DevExpress.XtraReports.UI.XRTable table4;
        private DevExpress.XtraReports.UI.XRTableRow tableRow5;
        private DevExpress.XtraReports.UI.XRTableCell tableCell25;
        private DevExpress.XtraReports.UI.XRTableCell tableCell26;
        private DevExpress.XtraReports.UI.XRTableCell tableCell29;
        private DevExpress.XtraReports.UI.XRTableCell tableCell30;
        private DevExpress.XtraReports.UI.XRTableCell tableCell31;
        private DevExpress.XtraReports.UI.XRTableCell tableCell32;
        private DevExpress.XtraReports.UI.XRTable table1;
        private DevExpress.XtraReports.UI.ReportHeaderBand ReportHeader;
        private DevExpress.XtraReports.UI.XRLabel label1;
        private DevExpress.XtraReports.UI.DetailBand Detail;
        private DevExpress.XtraReports.Parameters.Parameter docNum;
        private System.Resources.ResourceManager _resources;
        private string _resourceString;
        public PPFTTM() {
            this._resourceString = DevExpress.XtraReports.Serialization.XRResourceManager.GetResourceFor("XtraReportSerialization.PPFTTM");
            this.InitializeComponent();
        }
        private System.Resources.ResourceManager resources {
            get {
                if (_resources == null) {
                    this._resources = new DevExpress.XtraReports.Serialization.XRResourceManager(this._resourceString);
                }
                return this._resources;
            }
        }
        private void InitializeComponent() {
            DevExpress.DataAccess.ConnectionParameters.MsSqlConnectionParameters msSqlConnectionParameters1 = new DevExpress.DataAccess.ConnectionParameters.MsSqlConnectionParameters();
            DevExpress.DataAccess.Sql.SelectQuery selectQuery1 = new DevExpress.DataAccess.Sql.SelectQuery();
            DevExpress.DataAccess.Sql.Column column1 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression1 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Table table5 = new DevExpress.DataAccess.Sql.Table();
            DevExpress.DataAccess.Sql.Column column2 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression2 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column3 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression3 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column4 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression4 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column5 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression5 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Table table6 = new DevExpress.DataAccess.Sql.Table();
            DevExpress.DataAccess.Sql.Column column6 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression6 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column7 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression7 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column8 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression8 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column9 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression9 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column10 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression10 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column11 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression11 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column12 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression12 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column13 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression13 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.QueryParameter queryParameter1 = new DevExpress.DataAccess.Sql.QueryParameter();
            DevExpress.DataAccess.Sql.Join join1 = new DevExpress.DataAccess.Sql.Join();
            DevExpress.DataAccess.Sql.RelationColumnInfo relationColumnInfo1 = new DevExpress.DataAccess.Sql.RelationColumnInfo();
            this.table2 = new DevExpress.XtraReports.UI.XRTable();
            this.tableCell28 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableRow1 = new DevExpress.XtraReports.UI.XRTableRow();
            this.calculatedField1 = new DevExpress.XtraReports.UI.CalculatedField();
            this.tableCell22 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell24 = new DevExpress.XtraReports.UI.XRTableCell();
            this.BottomMargin = new DevExpress.XtraReports.UI.BottomMarginBand();
            this.tableCell6 = new DevExpress.XtraReports.UI.XRTableCell();
            this.sqlDataSource1 = new DevExpress.DataAccess.Sql.SqlDataSource();
            this.table3 = new DevExpress.XtraReports.UI.XRTable();
            this.TopMargin = new DevExpress.XtraReports.UI.TopMarginBand();
            this.tableCell27 = new DevExpress.XtraReports.UI.XRTableCell();
            this.GroupHeader1 = new DevExpress.XtraReports.UI.GroupHeaderBand();
            this.tableRow5 = new DevExpress.XtraReports.UI.XRTableRow();
            this.tableRow4 = new DevExpress.XtraReports.UI.XRTableRow();
            this.ReportHeader = new DevExpress.XtraReports.UI.ReportHeaderBand();
            this.tableCell16 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell7 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell13 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell12 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell11 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell19 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell18 = new DevExpress.XtraReports.UI.XRTableCell();
            this.Detail = new DevExpress.XtraReports.UI.DetailBand();
            this.tableCell32 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell30 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell1 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell29 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell9 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell10 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell26 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell3 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell21 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell2 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell20 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell14 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell25 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableRow2 = new DevExpress.XtraReports.UI.XRTableRow();
            this.tableCell23 = new DevExpress.XtraReports.UI.XRTableCell();
            this.label1 = new DevExpress.XtraReports.UI.XRLabel();
            this.tableCell15 = new DevExpress.XtraReports.UI.XRTableCell();
            this.table4 = new DevExpress.XtraReports.UI.XRTable();
            this.table1 = new DevExpress.XtraReports.UI.XRTable();
            this.tableCell17 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableRow3 = new DevExpress.XtraReports.UI.XRTableRow();
            this.docNum = new DevExpress.XtraReports.Parameters.Parameter();
            this.tableCell31 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell4 = new DevExpress.XtraReports.UI.XRTableCell();
            ((System.ComponentModel.ISupportInitialize)(this.table2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.table3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.table4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.table1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // table2
            // 
            this.table2.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Top | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.table2.Dpi = 100F;
            this.table2.LocationFloat = new DevExpress.Utils.PointFloat(633.1674F, 0F);
            this.table2.Name = "table2";
            this.table2.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
                        this.tableRow2,
                        this.tableRow3});
            this.table2.SizeF = new System.Drawing.SizeF(158.8326F, 75F);
            this.table2.StylePriority.UseBorders = false;
            // 
            // tableCell28
            // 
            this.tableCell28.Dpi = 100F;
            this.tableCell28.Name = "tableCell28";
            this.tableCell28.Text = "站点";
            this.tableCell28.Weight = 0.31674364706062574D;
            // 
            // tableRow1
            // 
            this.tableRow1.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
                        this.tableCell1,
                        this.tableCell2,
                        this.tableCell3,
                        this.tableCell6,
                        this.tableCell4,
                        this.tableCell7,
                        this.tableCell10,
                        this.tableCell11});
            this.tableRow1.Dpi = 100F;
            this.tableRow1.Name = "tableRow1";
            this.tableRow1.Weight = 1.6363636817616878D;
            // 
            // calculatedField1
            // 
            this.calculatedField1.DataMember = "PP_FTTM";
            this.calculatedField1.DisplayName = "ScrappedCount";
            this.calculatedField1.Expression = "[DetailedID].Count()";
            this.calculatedField1.Name = "calculatedField1";
            // 
            // tableCell22
            // 
            this.tableCell22.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "PP_FTTM.RejectQty")});
            this.tableCell22.Dpi = 100F;
            this.tableCell22.Name = "tableCell22";
            this.tableCell22.Text = "数量";
            this.tableCell22.Weight = 0.50356853984663563D;
            // 
            // tableCell24
            // 
            this.tableCell24.Dpi = 100F;
            this.tableCell24.Name = "tableCell24";
            this.tableCell24.Text = "序号";
            this.tableCell24.Weight = 0.43746740888586327D;
            // 
            // BottomMargin
            // 
            this.BottomMargin.Dpi = 100F;
            this.BottomMargin.HeightF = 48.33333F;
            this.BottomMargin.Name = "BottomMargin";
            // 
            // tableCell6
            // 
            this.tableCell6.Dpi = 100F;
            this.tableCell6.Name = "tableCell6";
            this.tableCell6.Weight = 0.50896798907879714D;
            // 
            // sqlDataSource1
            // 
            this.sqlDataSource1.ConnectionName = "192.168.1.147_FaradyneWMS_Connection";
            msSqlConnectionParameters1.AuthorizationType = DevExpress.DataAccess.ConnectionParameters.MsSqlAuthorizationType.SqlServer;
            msSqlConnectionParameters1.DatabaseName = "FaradyneWMS";
            msSqlConnectionParameters1.Password = "qwe!123";
            msSqlConnectionParameters1.ServerName = "192.168.1.220";
            msSqlConnectionParameters1.UserName = "sa";
            this.sqlDataSource1.ConnectionParameters = msSqlConnectionParameters1;
            this.sqlDataSource1.Name = "sqlDataSource1";
            columnExpression1.ColumnName = "DocNum";
            table5.MetaSerializable = "30|30|125|300";
            table5.Name = "PP_FTTM";
            columnExpression1.Table = table5;
            column1.Expression = columnExpression1;
            columnExpression2.ColumnName = "BaseNum";
            columnExpression2.Table = table5;
            column2.Expression = columnExpression2;
            columnExpression3.ColumnName = "PLine";
            columnExpression3.Table = table5;
            column3.Expression = columnExpression3;
            columnExpression4.ColumnName = "CTime";
            columnExpression4.Table = table5;
            column4.Expression = columnExpression4;
            column5.Alias = "PP_FTTMDetailed_DocNum";
            columnExpression5.ColumnName = "DocNum";
            table6.MetaSerializable = "185|30|125|440";
            table6.Name = "PP_FTTMDetailed";
            columnExpression5.Table = table6;
            column5.Expression = columnExpression5;
            columnExpression6.ColumnName = "ReasonsCode";
            columnExpression6.Table = table6;
            column6.Expression = columnExpression6;
            columnExpression7.ColumnName = "ReasonsDesc";
            columnExpression7.Table = table6;
            column7.Expression = columnExpression7;
            columnExpression8.ColumnName = "Qty";
            columnExpression8.Table = table6;
            column8.Expression = columnExpression8;
            columnExpression9.ColumnName = "WConfirm";
            columnExpression9.Table = table6;
            column9.Expression = columnExpression9;
            columnExpression10.ColumnName = "IConfirm";
            columnExpression10.Table = table6;
            column10.Expression = columnExpression10;
            columnExpression11.ColumnName = "ItemCode";
            columnExpression11.Table = table6;
            column11.Expression = columnExpression11;
            columnExpression12.ColumnName = "ItemName";
            columnExpression12.Table = table6;
            column12.Expression = columnExpression12;
            columnExpression13.ColumnName = "DetailedID";
            columnExpression13.Table = table6;
            column13.Expression = columnExpression13;
            selectQuery1.Columns.Add(column1);
            selectQuery1.Columns.Add(column2);
            selectQuery1.Columns.Add(column3);
            selectQuery1.Columns.Add(column4);
            selectQuery1.Columns.Add(column5);
            selectQuery1.Columns.Add(column6);
            selectQuery1.Columns.Add(column7);
            selectQuery1.Columns.Add(column8);
            selectQuery1.Columns.Add(column9);
            selectQuery1.Columns.Add(column10);
            selectQuery1.Columns.Add(column11);
            selectQuery1.Columns.Add(column12);
            selectQuery1.Columns.Add(column13);
            selectQuery1.FilterString = "[PP_FTTM.DocNum] = ?docNum";
            selectQuery1.Name = "PP_FTTM";
            queryParameter1.Name = "docNum";
            queryParameter1.Type = typeof(string);
            selectQuery1.Parameters.Add(queryParameter1);
            relationColumnInfo1.NestedKeyColumn = "DocNum";
            relationColumnInfo1.ParentKeyColumn = "DocNum";
            join1.KeyColumns.Add(relationColumnInfo1);
            join1.Nested = table6;
            join1.Parent = table5;
            selectQuery1.Relations.Add(join1);
            selectQuery1.Tables.Add(table5);
            selectQuery1.Tables.Add(table6);
            this.sqlDataSource1.Queries.AddRange(new DevExpress.DataAccess.Sql.SqlQuery[] {
                        selectQuery1});
            this.sqlDataSource1.ResultSchemaSerializable = resources.GetString("sqlDataSource1.ResultSchemaSerializable");
            // 
            // table3
            // 
            this.table3.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
                        | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.table3.Dpi = 100F;
            this.table3.LocationFloat = new DevExpress.Utils.PointFloat(0F, 0F);
            this.table3.Name = "table3";
            this.table3.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
                        this.tableRow4});
            this.table3.SizeF = new System.Drawing.SizeF(792F, 25F);
            this.table3.StylePriority.UseBorders = false;
            // 
            // TopMargin
            // 
            this.TopMargin.Dpi = 100F;
            this.TopMargin.HeightF = 41.66667F;
            this.TopMargin.Name = "TopMargin";
            // 
            // tableCell27
            // 
            this.tableCell27.Dpi = 100F;
            this.tableCell27.Name = "tableCell27";
            this.tableCell27.Text = "物料编码";
            this.tableCell27.Weight = 0.4625654414322401D;
            // 
            // GroupHeader1
            // 
            this.GroupHeader1.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
                        this.table4,
                        this.table2,
                        this.table1});
            this.GroupHeader1.Dpi = 100F;
            this.GroupHeader1.HeightF = 100F;
            this.GroupHeader1.Name = "GroupHeader1";
            // 
            // tableRow5
            // 
            this.tableRow5.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
                        this.tableCell24,
                        this.tableCell25,
                        this.tableCell26,
                        this.tableCell27,
                        this.tableCell28,
                        this.tableCell29,
                        this.tableCell30,
                        this.tableCell31,
                        this.tableCell32});
            this.tableRow5.Dpi = 100F;
            this.tableRow5.Name = "tableRow5";
            this.tableRow5.Weight = 1D;
            // 
            // tableRow4
            // 
            this.tableRow4.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
                        this.tableCell18,
                        this.tableCell15,
                        this.tableCell16,
                        this.tableCell19,
                        this.tableCell20,
                        this.tableCell17,
                        this.tableCell21,
                        this.tableCell22,
                        this.tableCell23});
            this.tableRow4.Dpi = 100F;
            this.tableRow4.Name = "tableRow4";
            this.tableRow4.Weight = 1D;
            // 
            // ReportHeader
            // 
            this.ReportHeader.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
                        this.label1});
            this.ReportHeader.Dpi = 100F;
            this.ReportHeader.HeightF = 41.66667F;
            this.ReportHeader.Name = "ReportHeader";
            // 
            // tableCell16
            // 
            this.tableCell16.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "PP_FTTM.BaseNum")});
            this.tableCell16.Dpi = 100F;
            this.tableCell16.Name = "tableCell16";
            this.tableCell16.Text = "MO ID";
            this.tableCell16.Weight = 0.49635714948527809D;
            // 
            // tableCell7
            // 
            this.tableCell7.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "PP_FTTM.PLine")});
            this.tableCell7.Dpi = 100F;
            this.tableCell7.Name = "tableCell7";
            this.tableCell7.Text = "tableCell7";
            this.tableCell7.Weight = 0.73965859954718971D;
            // 
            // tableCell13
            // 
            this.tableCell13.Dpi = 100F;
            this.tableCell13.Name = "tableCell13";
            this.tableCell13.Weight = 1.0145382809094845D;
            // 
            // tableCell12
            // 
            this.tableCell12.Dpi = 100F;
            this.tableCell12.Name = "tableCell12";
            this.tableCell12.Text = "仓库签收";
            this.tableCell12.Weight = 1.1577937733688692D;
            // 
            // tableCell11
            // 
            this.tableCell11.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "PP_FTTM.calculatedField3")});
            this.tableCell11.Dpi = 100F;
            this.tableCell11.Name = "tableCell11";
            this.tableCell11.Text = "tableCell11";
            this.tableCell11.Weight = 0.41737773443275894D;
            // 
            // tableCell19
            // 
            this.tableCell19.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "PP_FTTM.ItemCode")});
            this.tableCell19.Dpi = 100F;
            this.tableCell19.Name = "tableCell19";
            this.tableCell19.Text = "产品型号";
            this.tableCell19.Weight = 0.4625654414322401D;
            // 
            // tableCell18
            // 
            this.tableCell18.Dpi = 100F;
            this.tableCell18.Name = "tableCell18";
            this.tableCell18.Scripts.OnBeforePrint = "tableCell18_BeforePrint";
            this.tableCell18.Text = "序号";
            this.tableCell18.Weight = 0.43746740888586327D;
            // 
            // Detail
            // 
            this.Detail.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
                        | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.Detail.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
                        this.table3});
            this.Detail.Dpi = 100F;
            this.Detail.HeightF = 25F;
            this.Detail.Name = "Detail";
            this.Detail.StylePriority.UseBorders = false;
            // 
            // tableCell32
            // 
            this.tableCell32.Dpi = 100F;
            this.tableCell32.Name = "tableCell32";
            this.tableCell32.Text = "质量确认";
            this.tableCell32.Weight = 0.565417767836144D;
            // 
            // tableCell30
            // 
            this.tableCell30.Dpi = 100F;
            this.tableCell30.Name = "tableCell30";
            this.tableCell30.Text = "处理方案";
            this.tableCell30.Weight = 1.1602749313704384D;
            // 
            // tableCell1
            // 
            this.tableCell1.Dpi = 100F;
            this.tableCell1.Multiline = true;
            this.tableCell1.Name = "tableCell1";
            this.tableCell1.Text = "日期\r\nDate";
            this.tableCell1.Weight = 0.47680469744953341D;
            // 
            // tableCell29
            // 
            this.tableCell29.Dpi = 100F;
            this.tableCell29.Name = "tableCell29";
            this.tableCell29.Text = "不合格描述";
            this.tableCell29.Weight = 0.94490142647203557D;
            // 
            // tableCell9
            // 
            this.tableCell9.Dpi = 100F;
            this.tableCell9.Name = "tableCell9";
            this.tableCell9.Text = "返修签收";
            this.tableCell9.Weight = 2.9854617190905155D;
            // 
            // tableCell10
            // 
            this.tableCell10.Dpi = 100F;
            this.tableCell10.Name = "tableCell10";
            this.tableCell10.Text = "001-Scrap报废 汇总";
            this.tableCell10.Weight = 1.1374383351327906D;
            // 
            // tableCell26
            // 
            this.tableCell26.Dpi = 100F;
            this.tableCell26.Name = "tableCell26";
            this.tableCell26.Text = "MO ID";
            this.tableCell26.Weight = 0.49635714948527809D;
            // 
            // tableCell3
            // 
            this.tableCell3.Dpi = 100F;
            this.tableCell3.Multiline = true;
            this.tableCell3.Name = "tableCell3";
            this.tableCell3.Text = "班次\r\nShift";
            this.tableCell3.Weight = 0.53618150976149825D;
            // 
            // tableCell21
            // 
            this.tableCell21.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "PP_FTTM.EnumValue1")});
            this.tableCell21.Dpi = 100F;
            this.tableCell21.Name = "tableCell21";
            this.tableCell21.Text = "处理方案";
            this.tableCell21.Weight = 1.1602749313704384D;
            // 
            // tableCell2
            // 
            this.tableCell2.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "PP_FTTM.CTime")});
            this.tableCell2.Dpi = 100F;
            this.tableCell2.Name = "tableCell2";
            this.tableCell2.Text = "tableCell2";
            this.tableCell2.Weight = 0.48291740812524125D;
            // 
            // tableCell20
            // 
            this.tableCell20.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "PP_FTTM.ActualStation")});
            this.tableCell20.Dpi = 100F;
            this.tableCell20.Name = "tableCell20";
            this.tableCell20.Text = "站点";
            this.tableCell20.Weight = 0.31674364706062574D;
            // 
            // tableCell14
            // 
            this.tableCell14.Dpi = 100F;
            this.tableCell14.Name = "tableCell14";
            this.tableCell14.Weight = 0.39344832013446929D;
            // 
            // tableCell25
            // 
            this.tableCell25.Dpi = 100F;
            this.tableCell25.Name = "tableCell25";
            this.tableCell25.Text = "FTT ID";
            this.tableCell25.Weight = 0.44307593130767153D;
            // 
            // tableRow2
            // 
            this.tableRow2.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
                        this.tableCell9,
                        this.tableCell13});
            this.tableRow2.Dpi = 100F;
            this.tableRow2.Name = "tableRow2";
            this.tableRow2.Weight = 1D;
            // 
            // tableCell23
            // 
            this.tableCell23.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "PP_FTTM.calculatedField1")});
            this.tableCell23.Dpi = 100F;
            this.tableCell23.Name = "tableCell23";
            this.tableCell23.Text = "质量确认";
            this.tableCell23.Weight = 0.565417767836144D;
            // 
            // label1
            // 
            this.label1.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.label1.Dpi = 100F;
            this.label1.Font = new System.Drawing.Font("宋体", 28.2F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label1.LocationFloat = new DevExpress.Utils.PointFloat(0F, 0F);
            this.label1.Name = "label1";
            this.label1.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.label1.SizeF = new System.Drawing.SizeF(792F, 41.66667F);
            this.label1.StylePriority.UseBorders = false;
            this.label1.StylePriority.UseFont = false;
            this.label1.Text = "[PLine] FTT 报告";
            // 
            // tableCell15
            // 
            this.tableCell15.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "PP_FTTM.DocNum")});
            this.tableCell15.Dpi = 100F;
            this.tableCell15.Name = "tableCell15";
            this.tableCell15.Text = "FTT ID";
            this.tableCell15.Weight = 0.44307593130767153D;
            // 
            // table4
            // 
            this.table4.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right)));
            this.table4.Dpi = 100F;
            this.table4.LocationFloat = new DevExpress.Utils.PointFloat(0F, 75F);
            this.table4.Name = "table4";
            this.table4.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
                        this.tableRow5});
            this.table4.SizeF = new System.Drawing.SizeF(792F, 25F);
            this.table4.StylePriority.UseBorders = false;
            // 
            // table1
            // 
            this.table1.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
                        | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.table1.Dpi = 100F;
            this.table1.LocationFloat = new DevExpress.Utils.PointFloat(0F, 0F);
            this.table1.Name = "table1";
            this.table1.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
                        this.tableRow1});
            this.table1.SizeF = new System.Drawing.SizeF(633.1674F, 75F);
            this.table1.StylePriority.UseBorders = false;
            // 
            // tableCell17
            // 
            this.tableCell17.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "PP_FTTM.ReasonsDesc")});
            this.tableCell17.Dpi = 100F;
            this.tableCell17.Name = "tableCell17";
            this.tableCell17.Text = "不合格描述";
            this.tableCell17.Weight = 0.94490142647203557D;
            // 
            // tableRow3
            // 
            this.tableRow3.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
                        this.tableCell12,
                        this.tableCell14});
            this.tableRow3.Dpi = 100F;
            this.tableRow3.Name = "tableRow3";
            this.tableRow3.Weight = 1D;
            // 
            // docNum
            // 
            this.docNum.Description = "docNum";
            this.docNum.Name = "docNum";
            this.docNum.ValueInfo = "FP20191126040";
            // 
            // tableCell31
            // 
            this.tableCell31.Dpi = 100F;
            this.tableCell31.Name = "tableCell31";
            this.tableCell31.Text = "数量";
            this.tableCell31.Weight = 0.50356853984663563D;
            // 
            // tableCell4
            // 
            this.tableCell4.Dpi = 100F;
            this.tableCell4.Name = "tableCell4";
            this.tableCell4.Text = "生产线";
            this.tableCell4.Weight = 0.34522539444396944D;
            // 
            // PPFTTM
            // 
            this.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
                        this.TopMargin,
                        this.Detail,
                        this.BottomMargin,
                        this.GroupHeader1,
                        this.ReportHeader});
            this.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Top | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.CalculatedFields.AddRange(new DevExpress.XtraReports.UI.CalculatedField[] {
                        this.calculatedField1});
            this.ComponentStorage.AddRange(new System.ComponentModel.IComponent[] {
                        this.sqlDataSource1});
            this.DataMember = "PP_FTTM";
            this.DataSource = this.sqlDataSource1;
            this.Dpi = 100F;
            this.Font = new System.Drawing.Font("宋体", 10.2F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Margins = new System.Drawing.Printing.Margins(15, 20, 42, 48);
            this.Name = "PPFTTM";
            this.PageHeight = 1169;
            this.PageWidth = 827;
            this.PaperKind = System.Drawing.Printing.PaperKind.A4;
            this.Parameters.AddRange(new DevExpress.XtraReports.Parameters.Parameter[] {
                        this.docNum});
            this.Scripts.OnBeforePrint = "PPFTTM_BeforePrint";
            this.ScriptsSource = "private void tableCell18_BeforePrint(object sender, System.Drawing.Printing.Print" +
                "EventArgs e) {\r\n((XRTableCell)sender).Text = string.Format(\"{0}\", this.CurrentRo" +
                "wIndex + 1);\r\n}\r\n";
            this.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.Version = "16.1";
            ((System.ComponentModel.ISupportInitialize)(this.table2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.table3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.table4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.table1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();
        }
    }
}
