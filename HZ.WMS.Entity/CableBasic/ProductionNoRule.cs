using System.ComponentModel;
using HZ.WMS.Entity;
using SqlSugar;

namespace AOS.OMS.Entity.Basic
{
    /// <summary>
    /// Bom清单
    /// </summary>
    [SugarTable("Cable_Basic_ProductionNo_Rule")]
    public class ProductionNoRule : BaseEntity
    {
        /// <summary>
        /// ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Description("ID")]
        public string Id { get; set; }

        /// <summary>
        /// 序号
        /// </summary>
        [Description("序号")]
        public decimal SortNo  { get; set; }

        /// <summary>
        /// 排序组合
        /// </summary>
        [Description("排序组合")]
        public string SortGroup  { get; set; }

        /// <summary>
        /// 客户
        /// </summary>
        [Description("客户")]
        public string CustomName  { get; set; }


    }
}
