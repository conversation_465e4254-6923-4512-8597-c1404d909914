using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;
using AOS.WMS.Entity.CableProduce;
using HZ.Core.Http;
using HZ.Core.Office;
using HZ.WMS.Application;
using HZ.WMS.Application.PP;
using HZ.WMS.Entity;
using HZ.WMS.Entity.Sys;
using HZ.WMS.WebAPI.Controllers;
using LinqKit;

namespace HZ.WMS.WebAPI.Areas.PP.Controllers
{
    /// <summary>
    /// 发运计划
    /// </summary>
    public class DeliveryController : ApiBaseController
    {
        
        private Cable_DeliveryApp _app = new Cable_DeliveryApp();
        BaseApp<BaseEntity> _baseApp = new BaseApp<BaseEntity>();

        #region 获取发运列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri] Pagination page,
            [FromUri] Cable_ShippingPlanListReq shippingPlanListReq)
        {
            var result = new ResponseData();
            try
            {
                page.Sort = "CTime desc";
                var searchCondition = PredicateBuilder.New<Delivery>(t =>
                    (string.IsNullOrEmpty(shippingPlanListReq.ContractNo) ||
                     t.ContractNo.Equals(shippingPlanListReq.ContractNo))
                    && (string.IsNullOrEmpty(shippingPlanListReq.SapNo) || t.SapNo.Equals(shippingPlanListReq.SapNo))
                    && (shippingPlanListReq.SapLine == null || t.SapLine.Equals(shippingPlanListReq.SapLine)));
                if (shippingPlanListReq.CreateDate != null && shippingPlanListReq.CreateDate.Length == 2)
                {
                    searchCondition.And(t => t.CTime >= shippingPlanListReq.CreateDate[0] && t.CTime < shippingPlanListReq.CreateDate[1]);
                }
                var itemsData = _app.GetPageList(page, searchCondition);
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 导出

        /// <summary>
        /// 查询分页列表，按客户名称分sheet导出
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult Export([FromUri] Pagination page,
            [FromUri] Cable_ShippingPlanListReq shippingPlanListReq)
        {
            var result = new ResponseData();
            try
            {
                page.Sort = "CTime desc";
                var searchCondition = PredicateBuilder.New<Delivery>(t =>
                    (string.IsNullOrEmpty(shippingPlanListReq.ContractNo) ||
                     t.ContractNo.Equals(shippingPlanListReq.ContractNo))
                    && (string.IsNullOrEmpty(shippingPlanListReq.SapNo) || t.SapNo.Equals(shippingPlanListReq.SapNo))
                    && (shippingPlanListReq.SapLine == null || t.SapLine.Equals(shippingPlanListReq.SapLine)));
                if (shippingPlanListReq.CreateDate != null && shippingPlanListReq.CreateDate.Length == 2)
                {
                    searchCondition.And(t => t.CTime >= shippingPlanListReq.CreateDate[0] && t.CTime < shippingPlanListReq.CreateDate[1]);
                }
                var itemsData = _app.GetList(searchCondition).ToList();
                List<ExcelColumn<Delivery>> columns = ExcelService.FetchDefaultColumnList<Delivery>();
                string[] ignoreField = new string[]
                {
                    "IsDelete", "DTime", "DUser", "CustomerId", "CustomerCode", "CTime", "CUser", "MTime", "MUser"
                };

                List<ExcelColumn<Delivery>> ignoreFieldList =
                    columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<Delivery> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }

                // 按客户名称分组
                var customerGroups = itemsData.GroupBy(t => t.CustomerName ?? "未知客户");
                var customerGroupDict = customerGroups.ToDictionary(g => g.Key, g => g.ToList());

                // 如果没有数据，返回空表
                if (customerGroupDict.Count == 0)
                {
                    return ExportToExcelFile(new List<Delivery>(), columns);
                }

                // 使用按组导出的方法，每个客户一个sheet
                return ExportToExcelFileByGroup(customerGroupDict, columns, GetCurrentUser().UserName + "_销售交货导出");
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #region 导入模板

        /// <summary>
        /// 导入模板
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelModel()
        {
            var result = new ResponseData();
            try
            {
                var columns = ExcelService.FetchDefaultColumnList<Delivery>();
                string modelName = "销售交货-导入模板";
                //排除不需要的列名称 字符串
                string[] ignoreField = new string[]
                {
                    "Id", "PostTime", "PostUser", "PostMsg", "IsDelete", "CUser", "CTime", "Pid", "Status", "DownFlag",
                    "HandleStatus",
                    "MUser", "MTime", "DUser", "DTime"
                };
                var itemsData = new List<Delivery>() { };
                var ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<Delivery> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }

                //需要格式转换的字段
                //columns.ForEach((column) =>
                //{
                //    if (column.ColumnName == "CTime")
                //    {
                //        column.Formattor = ExcelExportFormatter.DateTimeFormatter;
                //    }
                //});
                return ExportToExcelFile<Delivery>(itemsData, columns, GetCurrentUser().UserName + "_" + modelName);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #region 批量创建

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult CreateBatch([FromBody] List<Delivery> entityList)
        {
            var result = new ResponseData();
            try
            {
                Sys_User currLoginUser = GetCurrentUser();
                var docs = _baseApp.GetNewDocNums(DocType.CS, DocFixedNumDef.CableDelivery, new DateTime(),
                    entityList.Count, 5);
                foreach (var bom in entityList)
                {
                    bom.DocNum = docs[0];
                    bom.IsDelete = false;
                    bom.CUser = currLoginUser.LoginAccount;
                    bom.CTime = DateTime.Now;
                    docs.RemoveAt(0);
                }

                result.Data = _app.Insert(entityList);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 过账

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Post([FromBody] string[] ids)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.Post(ids.ToList(),GetCurrentUser());
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 取消过账

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult CancelPost([FromBody] string[] ids)
        {
            return Json(_app.CancelPost(ids.ToList(),GetCurrentUser()));
        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="ids">根据单号进行删除</param>
        /// <returns></returns>
        //[HttpDelete]
        [HttpDelete]
        //[AllowAnonymous]
        public IHttpActionResult Delete([FromBody] string[] ids)
        {
            //[FromBody]string[] ids
            var result = new ResponseData();
            try
            {
                bool bDelete = _app.Deletes(ids, GetCurrentUser().LoginAccount);
                if (!bDelete)
                {
                    result.Code = (int)WMSStatusCode.UnHandledException;
                    result.Message = "删除失败";
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion
        
    }
}