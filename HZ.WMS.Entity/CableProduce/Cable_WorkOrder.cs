using System;
using System.Collections.Generic;
using System.ComponentModel;
using AOS.OMS.Entity.Sale;
using HZ.WMS.Entity;
using SqlSugar;

namespace AOS.WMS.Entity.CableProduce
{
    [SugarTable("SD_Cable_Sale_OrderInfo")]
    public class Cable_WorkOrder : BaseEntity
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Description("主键ID")]
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 订单编号
        /// </summary>
        [Description("订单编号")]
        public string OrderNum { get; set; }
        
        /// <summary>
        /// 排产序号
        /// </summary>
        [Description("排产序号")]
        public string ProductionNo { get; set; }

        /// <summary>
        /// 客户编号
        /// </summary>
        [Description("客户编号")]
        public string CustomerCode { get; set; }

        /// <summary>
        /// 客户名称
        /// </summary>
        [Description("客户名称")]
        public string CustomerName { get; set; }

        /// <summary>
        /// 客户地址
        /// </summary>
        [Description("客户地址")]
        public string CustomerAddress { get; set; }

        /// <summary>
        /// 客户结算地址
        /// </summary>
        [Description("客户结算地址")]
        public string SettlementAdd { get; set; }

        /// <summary>
        /// 订单状态
        /// </summary>
        [Description("订单状态")]
        public int Status { get; set; }
        
        /// <summary>
        /// 过账状态
        /// </summary>
        [Description("过账状态")]
        public int? PostStatus { get; set; }

        /// <summary>
        /// 订单类型(1:主机订单 2:部件订单 3:售后部件 4:售后主机)
        /// </summary>
        [Description("订单类型")]
        public int OrderType { get; set; }

        /// <summary>
        /// 订单日期
        /// </summary>
        [Description("订单日期")]
        public DateTime? OrderDate { get; set; }

        /// <summary>
        /// 发货日期
        /// </summary>
        [Description("发货日期")]
        public DateTime? DeliveryDate { get; set; }

        /// <summary>
        /// 同步SAP销售单号
        /// </summary>
        [Description("同步SAP销售单号")]
        public string SapNo { get; set; }

        /// <summary>
        /// 发货时间
        /// </summary>
        [Description("发货时间")]
        public DateTime DeliveryTime { get; set; }

        /// <summary>
        /// 排产时间
        /// </summary>
        [Description("排产时间")]
        public string ProductionTime { get; set; }

        /// <summary>
        /// 合同号
        /// </summary>
        [Description("合同号")]
        public string ContractNo { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        [Description("项目名称")]
        public string ProjectDisc { get; set; }

        /// <summary>
        /// 履行备注
        /// </summary>
        [Description("履行备注")]
        public string PerformRemark { get; set; }

        /// <summary>
        /// 生产备注
        /// </summary>
        [Description("生产备注")]
        public string ProductionRemark { get; set; }

        /// <summary>
        /// 客户订单号
        /// </summary>
        [Description("客户订单号")]
        public string CustomerOrderNum { get; set; }

        /// <summary>
        /// 货币
        /// </summary>
        [Description("货币")]
        public string HuoBi { get; set; }

        /// <summary>
        /// 批次
        /// </summary>
        [Description("批次")]
        public string BatchNum { get; set; }
        
        /// <summary>
        /// 催货日期
        /// </summary>
        [Description("催货日期")]
        public DateTime? ExpeditingDate { get; set; }
        
        /// <summary>
        /// 订单行备注
        /// </summary>
        [Description("订单行备注")]
        public string LineRemark { get; set; }
        
        /// <summary>
        /// 客户订单行
        /// </summary>
        [Description("客户订单行")]
        public string CustomerOrderLine { get; set; }
        
        /// <summary>
        /// 顺序号
        /// </summary>
        [Description("顺序号")]
        public string SerialNo { get; set; }
        
        /// <summary>
        /// 打印状态 每个类型占一位 1 工单 2电缆箱排产清单 3分支线排产 4附加线排产 5 随行电缆裁线 6标签 7清单A4 8清单A5 9唛头
        /// </summary>
        [Description("打印状态")]
        public string PrintStatus { get; set; }
        
        /// <summary>
        /// 工单打印状态
        /// </summary>
        [Description("工单打印状态")]
        public int WorkOrderPrintStatus { get; set; }

        /// <summary>
        /// 标签打印状态
        /// </summary>
        [Description("标签打印状态")]
        public int LabelPrintStatus { get; set; }
        
        /// <summary>
        /// 设置发运时间
        /// </summary>
        [Description("设置发运时间")]
        public DateTime? SetShipmentDate { get; set; }
        
        /// <summary>
        /// 完工日期
        /// </summary>
        [Description("完工日期")]
        public DateTime? CompletionDate { get; set; }
        
        /// <summary>
        /// 计划发货时间
        /// </summary>
        [Description("计划发货时间")]
        public DateTime? ShipmentDate { get; set; }
        
        /// <summary>
        /// 订单类型名称
        /// </summary>
        [Description("订单类型名称")]
        public string OrderTypeName { get; set; }
        
        /// <summary>
        /// 唛头打印状态
        /// </summary>
        [Description("唛头打印状态")]
        public int ShippingMarkPrintStatus { get; set; }
        
        /// <summary>
        /// 件号
        /// </summary>
        [Description("件号")]
        public string PartCode { get; set; }
        
        /// <summary>
        /// 电梯类型
        /// </summary>
        [Description("电梯类型")]
        public string ElevatorType { get; set; }
        
        /// <summary>
        /// 扫描状态
        /// </summary>
        [Description("扫描状态")]
        public int? ScanStatus { get; set; }
        
        /// <summary>
        /// 井道扫描状态
        /// </summary>
        [Description("井道扫描状态")]
        public int? HoistwayStatus { get; set; }
        
        /// <summary>
        /// 订单明细
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<SD_Cable_Sale_OrderDetails> OrderDetailList { get; set; }

        /// <summary>
        /// 订单参数明细
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public SD_Cable_Sale_Parameter SaleParameter { get; set; }

        /// <summary>
        /// 合同号参数列表
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<SD_Cable_OrderSerialNum> OrderSerialNumList { get; set; }

    }
}