using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.PP
{
    /// <summary>
    /// 超额领料主表
    /// </summary>
    public class PP_OverReceive : BaseEntity
    {
        /// <summary>
        /// 主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string ID { get; set; }
        /// <summary>
        /// 超额领料单号
        /// </summary>
        [Description("超额领料单号")]
        public string OverReceiveNo { get; set; }
        /// <summary>
        /// 移动类型，工废：261，如果是料废311
        /// </summary>
        [Description("移动类型")]
        public string MovementType { get; set; }
        /// <summary>
        /// 附件名称
        /// </summary>
        [Description("附件名称")]
        public string FileName { get; set; }
        /// <summary>
        /// 附件路径
        /// </summary>
        [Description("附件路径")]
        public string FilePath { get; set; }
        /// <summary>
        /// 审核状态：0未审核，1计划审核，2仓库审核
        /// </summary>
        [Description("审核状态")]
        public int ExamineStatus { get; set; } = 0;
        /// <summary>
        /// 计划审核人
        /// </summary>
        [Description("计划审核人")]
        public string PlanReviewer { get; set; }
        /// <summary>
        /// 计划审核时间
        /// </summary>
        [Description("计划审核时间")]
        public DateTime? PlannedTime { get; set; }
        /// <summary>
        /// 仓库审核人
        /// </summary>
        [Description("仓库审核人")]
        public string WarehouseReviewer { get; set; }
        /// <summary>
        /// 仓库审核时间
        /// </summary>
        [Description("仓库审核时间")]
        public DateTime? WarehouseTime { get; set; }
        /// <summary>
        /// 是否过账
        /// </summary>
        [Description("是否过账")]
        public bool IsPosted { get; set; } = false;
        /// <summary>
        /// 过账人
        /// </summary>
        [Description("过账人")]
        public string PostUser { get; set; }
        /// <summary>
        /// 过账时间
        /// </summary>
        [Description("过账时间")]
        public DateTime? PostTime { get; set; }
        /// <summary>
        /// 手动过账时间
        /// </summary>
        [Description("手动过账时间")]
        public DateTime? ManualPostTime { get; set; }
    }
}
