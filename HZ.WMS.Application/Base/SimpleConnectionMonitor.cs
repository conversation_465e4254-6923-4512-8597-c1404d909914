using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Threading.Tasks;
using HZ.Core.Logging;

namespace HZ.WMS.Application
{
    /// <summary>
    /// 简化的连接池监控器
    /// </summary>
    public static class SimpleConnectionMonitor
    {
        /// <summary>
        /// 检查数据库连接是否可用
        /// </summary>
        /// <param name="dbKey">数据库连接键</param>
        /// <returns></returns>
        public static async Task<SimpleConnectionInfo> CheckConnectionAsync(string dbKey)
        {
            var info = new SimpleConnectionInfo
            {
                ConnectionKey = dbKey,
                CheckTime = DateTime.Now
            };

            try
            {
                var connectionString = ConnectionStringBuilder.BuildOptimizedConnectionString(dbKey);
                var poolConfig = ConnectionStringBuilder.ParseConnectionPoolConfig(connectionString);
                
                info.MaxConnections = poolConfig.MaxPoolSize;

                var startTime = DateTime.Now;
                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    
                    // 执行简单查询测试连接
                    using (var command = new SqlCommand("SELECT 1", connection))
                    {
                        await command.ExecuteScalarAsync();
                    }
                }
                var endTime = DateTime.Now;

                var responseTime = (endTime - startTime).TotalMilliseconds;
                info.ResponseTimeMs = responseTime;
                info.IsHealthy = true;

                // 根据响应时间估算连接池使用情况
                if (responseTime > 2000) // 超过2秒
                {
                    info.EstimatedUsage = 90; // 估算90%使用率
                    info.Status = "高负载";
                }
                else if (responseTime > 1000) // 超过1秒
                {
                    info.EstimatedUsage = 70; // 估算70%使用率
                    info.Status = "中等负载";
                }
                else if (responseTime > 500) // 超过0.5秒
                {
                    info.EstimatedUsage = 40; // 估算40%使用率
                    info.Status = "轻度负载";
                }
                else
                {
                    info.EstimatedUsage = 20; // 估算20%使用率
                    info.Status = "正常";
                }
            }
            catch (Exception ex)
            {
                info.IsHealthy = false;
                info.ErrorMessage = ex.Message;
                info.Status = "连接失败";
                info.EstimatedUsage = 0;
                info.MaxConnections = 50; // 默认值
                
                LogHelper.Instance.LogError($"数据库连接检查失败 - {dbKey}: {ex.Message}");
            }

            return info;
        }

        /// <summary>
        /// 检查所有数据库连接状态
        /// </summary>
        /// <returns></returns>
        public static async Task<List<SimpleConnectionInfo>> CheckAllConnectionsAsync()
        {
            var results = new List<SimpleConnectionInfo>();
            var connectionKeys = new[] 
            { 
                "DbConnection", 
                "DbConnectionForSAP", 
                "DbConnectionForSRM", 
                "DbConnectionForEAP", 
                "DbConnectionForOMS" 
            };

            foreach (var key in connectionKeys)
            {
                var info = await CheckConnectionAsync(key);
                results.Add(info);
            }

            return results;
        }
    }

    /// <summary>
    /// 简化的连接信息
    /// </summary>
    public class SimpleConnectionInfo
    {
        public string ConnectionKey { get; set; }
        public bool IsHealthy { get; set; }
        public string Status { get; set; }
        public double ResponseTimeMs { get; set; }
        public int EstimatedUsage { get; set; }
        public int MaxConnections { get; set; }
        public string ErrorMessage { get; set; }
        public DateTime CheckTime { get; set; }

        /// <summary>
        /// 是否接近满载
        /// </summary>
        public bool IsNearCapacity => EstimatedUsage > 80;

        /// <summary>
        /// 使用率百分比
        /// </summary>
        public double UsagePercentage => EstimatedUsage;
    }
}
