using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using HZ.Core.Security;
using HZ.WMS.Entity.Sys;
using HZ.Core.Http;

namespace HZ.WMS.Application.Sys
{
    /// <summary>
    /// 没有out_message消息的函数，意味着只在最前端做未处理异常捕捉
    /// </summary>

    public class Sys_MailServerConfigApp : BaseApp<Sys_MailServerConfig>
    {
		#region 默认构造函数
        
        /// <summary>
        /// 默认构造函数
        /// </summary>
        public Sys_MailServerConfigApp() : base()
        {
        }




        #endregion

        #region 获取邮件配置(唯一性，不存在多行数据)

        /// <summary>
        /// 获取邮件服务器配置
        /// </summary>
        /// <returns></returns>
        public Sys_MailServerConfig GetMailServerConfig()
        {
            return GetList().ToList().FirstOrDefault();
        }

        #endregion

    }
}

