using System.Collections.Generic;
using System.ComponentModel;
using SqlSugar;

namespace HZ.WMS.Entity.SAP
{
    public class ZFITFG001018
    {
        /// <summary>
        /// 物料编码
        /// </summary>
        public string MATERIAL { get; set; }
        /// <summary>
        /// 工厂代码
        /// </summary>
        public string PROD_PLANT { get; set; }
        /// <summary>
        /// 计划工厂代码
        /// </summary>
        public string PLAN_PLANT { get; set; }
        /// <summary>
        /// 订单数量
        /// </summary>
        public string TOTAL_PLORD_QTY { get; set; }
        /// <summary>
        /// 订单开始时间
        /// </summary>
        public string ORDER_START_DATE { get; set; }
        /// <summary>
        /// 订单结束时间
        /// </summary>
        public string ORDER_FIN_DATE { get; set; }
        /// <summary>
        /// 生产版本
        /// </summary>
        public string VERSION { get; set; }
        /// <summary>
        /// 单位
        /// </summary>
        public string BASE_UOM { get; set; }
        /// <summary>
        /// 销售订单号
        /// </summary>
        public string SALES_ORD { get; set; }
        /// <summary>
        /// 销售订单行
        /// </summary>
        public string S_ORD_ITEM { get; set; }
        /// <summary>
        /// 单位
        /// </summary>
        public string MANUAL_COMPONENT { get; set; }
        /// <summary>
        /// 单位
        /// </summary>
        public string FIRMING_IND { get; set; }
        /// <summary>
        /// 单位
        /// </summary>
        public string PLDORD_PROFILE { get; set; }
        /// <summary>
        /// 单位
        /// </summary>
        public string ACCTASSCAT { get; set; }
        /// <summary>
        /// 单位
        /// </summary>
        public List<ZFITFG001018_ITEM> itmes { get; set; }
        
    }
}