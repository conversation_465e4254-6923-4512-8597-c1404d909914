using System.ComponentModel;
using SqlSugar;

namespace HZ.WMS.Entity.Produce
{
    /// <summary>
    /// 排产明细
    /// </summary>
    [SugarTable("Produce_SchedulingDetail")]
    public class Produce_SchedulingDetail : BaseEntity
    {
        /// <summary>
        /// 主键编号
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 物料编码
        /// </summary>
        [Description("物料编码")]
        public string MaterialCode { get; set; }

        /// <summary>
        /// 生产订单号
        /// </summary>
        [Description("生产订单号")]
        public string ProduceOrderNo { get; set; }

        /// <summary>
        /// 父ID
        /// </summary>
        [Description("父ID")]
        public string Pid { get; set; }

        /// <summary>
        /// Sap单号
        /// </summary>
        [Description("Sap单号")]
        public string SapNo { get; set; }

        /// <summary>
        /// Sap行号
        /// </summary>
        [Description("Sap行号")]
        public int? SapLine { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        [Description("状态")]
        public string Status { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        [Description("消息")]
        public string Massage { get; set; }
    }
}