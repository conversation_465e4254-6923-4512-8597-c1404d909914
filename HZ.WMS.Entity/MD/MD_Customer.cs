using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.MD
{
    /// <summary>
    /// 客户信息
    /// </summary>
    public class MD_Customer : BaseEntity
    {
        /// <summary>
        /// 主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string CustomerObjectID { get; set; }
        /// <summary>
        /// 客户类别 1 个人客户 2 公司客户
        /// </summary>
        public string CategoryCode { get; set; }
        /// <summary>
        /// 客户编号
        /// </summary>
        public string InternalID { get; set; }
        /// <summary>
        /// 个人客户名称
        /// </summary>
        public string FamilyName { get; set; }
        /// <summary>
        /// 公司客户名称
        /// </summary>
        public string FirstLineName { get; set; }
        /// <summary>
        /// 状态 2 有效
        /// </summary>
        public string LifeCycleStatusCode { get; set; }
        /// <summary>
        /// 邮箱
        /// </summary>
        public string EMailURI { get; set; }
        /// <summary>
        /// 城市
        /// </summary>
        public string CityName { get; set; }
        /// <summary>
        /// 电话
        /// </summary>
        public string Phone { get; set; }
        /// <summary>
        /// 传真
        /// </summary>
        public string Fax { get; set; }
        /// <summary>
        /// 联系人姓名
        /// </summary>
        public string ContactPerson { get; set; }
        /// <summary>
        /// 常用地址
        /// </summary>
        public string Address { get; set; }
    }
    /// <summary>
    /// 
    /// </summary>
    public class MD_CustomerEquality : IEqualityComparer<MD_Customer>
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="x"></param>
        /// <param name="y"></param>
        /// <returns></returns>
        public bool Equals(MD_Customer x, MD_Customer y)
        {
            return x.InternalID == y.InternalID;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public int GetHashCode(MD_Customer obj)
        {
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return obj.ToString().GetHashCode();
            }
        }
    }
}
