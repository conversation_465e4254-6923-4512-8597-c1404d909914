using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.SAP
{
    /// <summary>
    ///客户主数据
    /// </summary>
    [SugarTable("XZ_SAP_CSKS")]
    public class XZ_SAP_KNA1
    {
        /// <summary>
        /// 客户编码
        /// </summary>
        [Description("客户编码")]
        public string KUNNR { get; set; }

        /// <summary>
        /// 客户名称
        /// </summary>
        [Description("客户名称")]
        public string NAME1 { get; set; }

        /// <summary>
        /// 货币代码
        /// </summary>
        [Description("货币代码")]
        public string WAERS { get; set; }

        /// <summary>
        /// 税分类
        /// </summary>
        [Description("税分类")]
        public string TAXKD { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        [Description("状态")]
        public bool? Status { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [Description("创建时间")]
        public DateTime? CDate { get; set; }

    }
}
