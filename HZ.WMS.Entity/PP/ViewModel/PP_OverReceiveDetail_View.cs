using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.PP.ViewModel
{
    /// <summary>
    /// 超额领料
    /// </summary>
    public class PP_OverReceiveDetail_View
    {
        /// <summary>
        /// 生产订单
        /// </summary>
        public string ProductionOrderNo { get; set; }
        /// <summary>
        /// 出厂编号 = 开始日期 + 4位流水号
        /// </summary>
        public string SerialNo { get; set; }
        /// <summary>
        /// 组件行项目号
        /// </summary>
        public decimal ComponentLineNo { get; set; }
        /// <summary>
        /// 组件编码
        /// </summary>
        public string ComponentCode { get; set; }
        /// <summary>
        /// 物料描述
        /// </summary>
        public string MaterialName { get; set; }
        /// <summary>
        /// 需求数量
        /// </summary>
        public decimal DemandQty { get; set; }
        /// <summary>
        /// 组件单位
        /// </summary>
        public string ComponentUnit { get; set; }
        /// <summary>
        /// 工厂
        /// </summary>
        public string FactoryCode { get; set; }
        /// <summary>
        /// 线体编码
        /// </summary>
        public string ProductionLineCode { get; set; }
        /// <summary>
        /// 线体描述
        /// </summary>
        public string ProductionLineDes { get; set; }
        /// <summary>
        /// 转出仓库
        /// </summary>
        public string OutWarehouse { get; set; }
        /// <summary>
        /// 发料库存地
        /// </summary>
        public string DeliverLocation { get; set; }
        /// <summary>
        /// 合同号
        /// </summary>
        public string ContractNo { get; set; }
        /// <summary>
        /// 物料组
        /// </summary>
        public string MaterialGroupCode { get; set; }
        /// <summary>
        /// 物料组描述
        /// </summary>
        public string MaterialGroupDes { get; set; }
        /// <summary>
        /// 销售订单
        /// </summary>
        public string SalesOrderNo { get; set; }
        /// <summary>
        /// 销售订单行项目
        /// </summary>
        public int? SalesOrderLineNo { get; set; }
        /// <summary>
        /// 特殊库存标识
        /// </summary>
        public string SpecialInventory { get; set; }
        /// <summary>
        /// 评估类别，为B时评估类型为01自制02外购
        /// </summary>
        public string AssessmentCategory { get; set; }
        /// <summary>
        /// 开始日期(装配日期)
        /// </summary>
        public DateTime? StartTime { get; set; }
    }
}
