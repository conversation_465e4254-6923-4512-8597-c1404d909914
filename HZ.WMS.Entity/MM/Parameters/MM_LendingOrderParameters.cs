using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.MM.Parameters
{
    /// <summary>
    /// 接收参数的类
    /// </summary>
    public class MM_LendingOrderParameters
    {
        /// <summary>
        /// 借出单明细
        /// </summary>
        public List<MM_LendingOrderDetail> DetailedList { get; set; }

        /// <summary>
        /// 删除明细数组
        /// </summary>
        public string[] deldetailArray { get; set; }

        /// <summary>
        /// 手动过账时间
        /// </summary>
        public DateTime? ManualPostTime { get; set; }

        /// <summary>
        /// 借出单号
        /// </summary>
        public string DocNum { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }

        /// <summary>
        /// 类型
        /// </summary>
        public string LendingType { get; set; }
        

        /// <summary>
        /// 交易对象
        /// </summary>
        public int TradingPartners { get; set; }

        /// <summary>
        /// 交易对象名称
        /// </summary>
        public string TradingPartnersName { get; set; }

        /// <summary>
        /// 对象编号
        /// </summary>
        public string PartnersNum { get; set; }

        /// <summary>
        /// 对象全称
        /// </summary>
        public string PartnersName { get; set; }


        /// <summary>
        /// 办理人员代号
        /// </summary>
        public string HandlenCode { get; set; }

        /// <summary>
        /// 办理人员
        /// </summary>
        public string HandlenName { get; set; }


    }
}
