using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;

//
using HZ.Core.Security;
using HZ.WMS.Application.Sys;
using HZ.WMS.Entity.Sys;
using HZ.Core.Http;
using HZ.WMS.WebAPI.Controllers;
using Newtonsoft.Json;
using HZ.Core.Vue;

namespace HZ.WMS.WebAPI.Areas.Sys.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    public class Sys_DbBackupConfigController : ApiBaseController
    {
        private Sys_DbBackupConfigApp _app = new Sys_DbBackupConfigApp();

        #region 获取数据库备份配置


        /// <summary>
        /// 获取数据库备份配置
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetDbBackupConfig()
        {
            var result = new ResponseData();
            try
            {
                
                result.Data = _app.GetDbBackupConfig();
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Update([FromBody]Sys_DbBackupConfig entity)
        {
            var result = new ResponseData();
            try
            {
                entity.MUser = GetCurrentUser().LoginAccount;
                entity.MTime = DateTime.Now;
                result.Data = _app.SaveDbBackupConfig(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion
    }
}
