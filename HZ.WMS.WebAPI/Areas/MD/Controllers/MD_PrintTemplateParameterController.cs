using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;
using HZ.Core.Http;
using HZ.Core.Office;
using HZ.WMS.Application.MD;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.MD.Import;
using HZ.WMS.WebAPI.Controllers;

namespace HZ.WMS.WebAPI.Areas.MD.Controllers
{
    /// <summary>
    /// 打印参数模板管理
    /// </summary>
    public class MD_PrintTemplateParameterController : ApiBaseController
    {
        private MD_PrintTemplateParameterApp _app = new MD_PrintTemplateParameterApp();

        #region 查询分页列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="page">分页参数</param>
        /// <param name="req">查询参数</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri] Pagination page, [FromUri] MD_PrintTemplateParameterListReq req)
        {
            var result = new ResponseData();
            try
            {
                var data = _app.GetPageList(page, x =>
                    (string.IsNullOrEmpty(req.Keyword) || x.TemplateName.Contains(req.Keyword))
                    && (req.Enable == null || x.Enable == req.Enable)
                    && (string.IsNullOrEmpty(req.TemplateKey) || x.TemplateKey == req.TemplateKey));

                result.Data = new { rows = data, total = page.Total };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 根据ID获取实体

        /// <summary>
        /// 根据ID获取实体
        /// </summary>
        /// <param name="id">主键ID</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetById([FromUri] string id)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.GetEntityByKey(id);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 根据模板Key获取打印参数模板列表

        /// <summary>
        /// 根据模板Key获取打印参数模板列表
        /// </summary>
        /// <param name="templateKey">模板Key</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetByTemplateKey([FromUri] string templateKey)
        {
            var result = new ResponseData();
            try
            {
                var data = _app.GetList(x =>
                    !string.IsNullOrEmpty(templateKey) && x.TemplateKey == templateKey
                    && x.Enable == true).ToList();

                result.Data = data;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 新增

        /// <summary>
        /// 新增
        /// </summary>
        /// <param name="entity">实体</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Add([FromBody] MD_PrintTemplateParameter entity)
        {
            var result = new ResponseData();
            try
            {
                // 检查模板名称是否重复
                var existTemplate = _app.GetFirstEntityByFieldValue("TemplateName", entity.TemplateName);
                if (existTemplate != null)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = $"模板名称 '{entity.TemplateName}' 已存在";
                    return Json(result);
                }

                entity.Id = Guid.NewGuid().ToString();
                entity.CUser = GetCurrentUser().LoginAccount;
                entity.CTime = DateTime.Now;
                entity.IsDelete = false;

                _app.Insert(entity);
                result.Data = entity;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 修改

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="entity">实体</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Update([FromBody] MD_PrintTemplateParameter entity)
        {
            var result = new ResponseData();
            try
            {
                // 检查模板名称是否重复（排除自己）
                var existTemplate = _app.GetFirstEntity(x => x.TemplateName == entity.TemplateName && x.Id != entity.Id);
                if (existTemplate != null)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = $"模板名称 '{entity.TemplateName}' 已存在";
                    return Json(result);
                }

                entity.MUser = GetCurrentUser().LoginAccount;
                entity.MTime = DateTime.Now;

                _app.Update(entity);
                result.Data = entity;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="ids">主键ID集合</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Delete([FromBody] List<string> ids)
        {
            var result = new ResponseData();
            try
            {
                var entities = _app.GetList(x => ids.Contains(x.Id)).ToList();
                result.Data = _app.Delete(entities, GetCurrentUser().LoginAccount);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 导出模板

        /// <summary>
        /// 导出模板
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelModel()
        {
            var result = new ResponseData();
            try
            {
                var columns = ExcelService.FetchDefaultColumnList<MD_PrintTemplateParameter>();
                string modelName = "打印参数模板-导入模板";
                //排除不需要的列名称 字符串
                string[] ignoreField = new string[]
                {
                    "Id", "Remark", "IsDelete", "CUser", "CTime",
                    "MUser", "MTime", "DUser", "DTime"
                };
                var itemsData = new List<MD_PrintTemplateParameter>() { };
                var ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<MD_PrintTemplateParameter> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }

                return ExportToExcelFile<MD_PrintTemplateParameter>(itemsData, columns,
                    GetCurrentUser().UserName + "_" + modelName);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        /// <summary>
        /// 导出数据
        /// </summary>
        /// <param name="req">查询参数</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] MD_PrintTemplateParameterListReq req)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetList(x =>
                    (string.IsNullOrEmpty(req.Keyword) || x.TemplateName.Contains(req.Keyword))
                    && (req.Enable == null || x.Enable == req.Enable)
                    && (string.IsNullOrEmpty(req.TemplateKey) || x.TemplateKey == req.TemplateKey)).ToList();

                var columns = ExcelService.FetchDefaultColumnList<MD_PrintTemplateParameter>();
                string modelName = "打印参数模板-导出数据";
                //排除不需要的列名称 字符串
                string[] ignoreField = new string[]
                {
                    "Id", "IsDelete", "CUser", "CTime",
                    "MUser", "MTime", "DUser", "DTime"
                };
                var ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<MD_PrintTemplateParameter> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }

                return ExportToExcelFile<MD_PrintTemplateParameter>(itemsData, columns,
                    GetCurrentUser().UserName + "_" + modelName);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #region 导入

        /// <summary>
        /// 导入
        /// </summary>
        /// <param name="entitys">接收的导入集合</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult ImportExcelToData([FromBody] List<MD_PrintTemplateParameterImport> entitys)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                if (_app.ImportExcelToBaseData(entitys, GetCurrentUser().LoginAccount, out error_message))
                {
                    result.Data = true;
                    result.Code = (int)WMSStatusCode.Success;
                }
                else
                {
                    result.Data = false;
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
            return Json(result);
        }

        #endregion
    }
}
