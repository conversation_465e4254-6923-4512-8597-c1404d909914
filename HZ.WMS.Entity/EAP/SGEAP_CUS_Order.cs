using System;
using System.ComponentModel;
using SqlSugar;

namespace HZ.WMS.Entity.EAP
{
    public class SGEAP_CUS_Order
    {
        [SugarColumn(IsPrimaryKey = true)]
        [Description("主键id")]
        public long? ID { get; set; }

        [Description("出场编号")]
        public String SerialNo { get; set; }

        [Description("合同号")]
        public String ContractNo { get; set; }

        [Description("订单号")]
        public String OrderNo { get; set; }

    }
}