using System;
using System.Collections.Generic;
using System.ComponentModel;

namespace HZ.WMS.Entity.SD
{
    /// <summary>
    /// 完成预排产请求
    /// </summary>
    public class CompletePreProduceReq
    {
        /// <summary> 
        /// 计划时间
        /// </summary> 
        [Description("计划时间")]
        public DateTime PlanTime { get; set; }

        /// <summary> 
        /// Ids
        /// </summary> 
        [Description("Ids")]
        public List<string> Ids { get; set; }

        /// <summary> 
        /// 校验合同号数量（包含第一次扫描）
        /// 预设值：3 或 5
        /// </summary> 
        [Description("校验合同号数量")]
        public int ContractValidationCount { get; set; } = 3;
    }
}