using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.Sys.ViewModel
{
    /// <summary>
    /// 用户所属的角色信息视图
    /// </summary>
    public class SysUserForRole_View:BaseEntity
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Description("用户ID")]
        [SugarColumn(IsPrimaryKey = true)]
        public string UserID { get; set; }
        /// <summary>
        /// 所属机构
        /// </summary>
        [Description("所属机构")]
        public string OrganizationID { get; set; }
        /// <summary>
        /// 机构描述
        /// </summary>
        [Description("机构描述")]
        public string OrganizationDesc { get; set; }
        /// <summary>
        /// 用户姓名
        /// </summary>
        [Description("用户姓名")]
        public string UserName { get; set; }
        /// <summary>
        /// 英文名字
        /// </summary>
        [Description("英文名字")]
        public string FrgnName { get; set; }

        /// <summary>
        /// 角色
        /// </summary>
        [Description("角色")]
        public string RoleDescs { get; set; }

        /// <summary>
        /// 登录账号
        /// </summary>
        [UniqueCode]
        [Description("登录账号")]
        public string LoginAccount { get; set; }
        /// <summary>
        /// 登录密码
        /// </summary>
        [Description("登录密码")]
        public string LoginPassword { get; set; }
        /// <summary>
        /// 头像
        /// </summary>
        [Description("头像")]
        public string Avatar { get; set; }
        /// <summary>
        /// 性别
        /// </summary>
        [Description("性别")]
        public int? Gender { get; set; }
        /// <summary>
        /// 出生日期
        /// </summary>
        [Description("出生日期")]
        public DateTime? Birthday { get; set; }
        /// <summary>
        /// 电子邮箱
        /// </summary>
        [Description("电子邮箱")]
        public string Email { get; set; }
        /// <summary>
        /// 移动手机
        /// </summary>
        [Description("移动手机")]
        public string Mobile { get; set; }
        /// <summary>
        /// 联系电话
        /// </summary>
        [Description("联系电话")]
        public string Telphone { get; set; }
        /// <summary>
        /// 冻结标识
        /// </summary>
        [Description("冻结标识")]
        public bool? IsEnable { get; set; }
        /// <summary>
        /// 
        /// </summary>
        [Description("用户角色")]
        public int? UserRole { get; set; }

        /// <summary>
        /// 是否供应商
        /// </summary>
        public bool? IsSupplier { get; set; } = false;

        /// <summary>
        /// 是否需要更新密码
        /// </summary>
        public bool? NeedUpdatePassword { get; set; } = false;

        /// <summary>
        /// 重置登录次数
        /// </summary>
        public int? ResetLoginCount { get; set; } = 0;
    }
}
