using System;
using System.ComponentModel;
using SqlSugar;
//Chloe框架


namespace HZ.WMS.Entity.PO
{
    /// <summary>
    /// 采购退货扫描
    /// </summary>
    [SugarTable("PO_ReturnScan")]
    public class PO_ReturnScan:BaseEntity
    {
        /// <summary> 
        /// ID
        /// </summary> 
        [SugarColumn(IsPrimaryKey = true)]
        [Description("ID")]
        public string ReturnScanID { get;set;}

        /// <summary> 
        /// wms单号
        /// </summary> 
        [Description("单号")]
        public string DocNum {get;set;}

        /// <summary> 
        /// 手动过账时间
        /// </summary> 
        [Description("手动过账时间")]
        public DateTime? ManualPostTime { get; set; }

        /// <summary> 
        /// 是否已过账
        /// </summary> 
        [Description("是否已过账")]
        public bool? IsPosted {get;set;}

        /// <summary> 
        /// 过账人
        /// </summary> 
        [Description("过账人")]
        public string PostUser {get;set;}

        /// <summary> 
        /// 过账时间
        /// </summary> 
        [Description("过账时间")]
        public DateTime? PostTime {get;set;}

        /// <summary> 
        /// 供应商编号
        /// </summary> 
        [Description("供应商编号")]
        public string SupplierCode { get; set; }
        /// <summary> 
        /// 供应商名称
        /// </summary> 
        [Description("供应商")]
        public string SupplierName { get; set; }

        /// <summary>
        /// SAP过账标示
        /// </summary>
        [Description("SAP过账标示")]
        public string SAPmark { get; set; }

        /// <summary>
        /// SAP过账信息
        /// </summary>
        [Description("SAP过账信息")]
        public string SAPmessage { get; set; }

    }
}


