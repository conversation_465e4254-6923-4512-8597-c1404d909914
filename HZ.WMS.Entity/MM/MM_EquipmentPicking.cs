using System;
using System.ComponentModel;
using SqlSugar;

namespace HZ.WMS.Entity.MM
{
    /// <summary>
    /// 设备领料
    /// </summary>
    [SugarTable("MM_EquipmentPicking")]
    public class MM_EquipmentPicking : BaseEntity
    {
        /// <summary> 
        /// 主键ID
        /// </summary> 
        [SugarColumn(IsPrimaryKey = true)]
        [Description("主键ID")]
        public string EquipmentPickingID { get; set; }

        /// <summary> 
        /// wms单号
        /// </summary> 
        [Description("领料单号")]
        public string DocNum { get; set; }

        /// <summary> 
        /// wms行号
        /// </summary> 
        [Description("领料行号")]
        public int? Line { get; set; }

        /// <summary>
        /// 办理人员编号
        /// </summary>
        [Description("办理人员编号")]
        public string HandlenCode { get; set; }

        /// <summary>
        /// 办理人员
        /// </summary>
        [Description("办理人员")]
        public string HandlenName { get; set; }

        /// <summary>
        /// 序列号
        /// </summary>
        [Description("序列号")]
        public string BarCode { get; set; }

        /// <summary>
        /// 设备领料编号
        /// </summary>
        [Description("设备领料编号")]
        public string BaseEntry { get; set; }

        /// <summary>
        /// 设备领料单号
        /// </summary>
        [Description("设备领料单号")]
        public string BaseNum { get; set; }

        /// <summary>
        /// 设备领料单行号
        /// </summary>
        [Description("设备领料单行号")]
        public int? BaseLine { get; set; }

        /// <summary> 
        /// 批次
        /// </summary> 
        [Description("批次")]
        public string BatchNum { get; set; }

        /// <summary> 
        /// 设备领料状态
        /// </summary> 
        [Description("设备领料状态")]
        public string EquipmentPickingStatus { get; set; }

        /// <summary> 
        /// 物料编号
        /// </summary> 
        [Description("物料编号")]
        public string ItemCode { get; set; }

        /// <summary> 
        /// 物料名称
        /// </summary> 
        [Description("物料名称")]
        public string ItemName { get; set; }

        /// <summary> 
        /// 供应商编号
        /// </summary> 
        [Description("供应商编号")]
        public string SupplierCode { get; set; }

        /// <summary> 
        /// 供应商名称
        /// </summary> 
        [Description("供应商名称")]
        public string SupplierName { get; set; }

        /// <summary> 
        /// 供应商批次
        /// </summary> 
        [Description("供应商批次")]
        public string SupplierBatch { get; set; }

        /// <summary> 
        /// 数量
        /// </summary> 
        [Description("数量")]
        public decimal? EquipmentPickingQty { get; set; }

        /// <summary> 
        /// 单位
        /// </summary> 
        [Description("单位")]
        public string Unit { get; set; }

        /// <summary> 
        /// 仓库编号
        /// </summary> 
        [Description("仓库编号")]
        public string WhsCode { get; set; }

        /// <summary> 
        /// 仓库名称
        /// </summary> 
        [Description("仓库")]
        public string WhsName { get; set; }

        /// <summary> 
        /// 区域编号
        /// </summary> 
        [Description("区域编号")]
        public string RegionCode { get; set; }

        /// <summary> 
        /// 区域名称
        /// </summary> 
        [Description("区域")]
        public string RegionName { get; set; }

        /// <summary> 
        /// 库位编号
        /// </summary> 
        [Description("库位编号")]
        public string BinLocationCode { get; set; }

        /// <summary> 
        /// 库位名称
        /// </summary> 
        [Description("库位")]
        public string BinLocationName { get; set; }

        /// <summary> 
        /// 是否作废
        /// </summary> 
        [Description("是否作废")]
        public bool? IsCancel { get; set; }

        /// <summary> 
        /// 手动过账时间
        /// </summary> 
        [Description("手动过账时间")]
        public DateTime? ManualPostTime { get; set; }

        /// <summary> 
        /// 是否过账 1:已过帐 0：未过账
        /// </summary> 
        [Description("是否过账")]
        public bool IsPosted { get; set; }

        /// <summary> 
        /// 过账人
        /// </summary> 
        [Description("过账人")]
        public string PostUser { get; set; }

        /// <summary> 
        /// 过账时间
        /// </summary> 
        [Description("过账时间")]
        public DateTime? PostTime { get; set; }

        /// <summary> 
        /// Sap生成单号
        /// </summary> 
        [Description("Sap生成单号")]
        public string SapDocNum { get; set; }

        /// <summary> 
        /// Sap生成行号
        /// </summary> 
        [Description("Sap生成行号")]
        public int? SapLine { get; set; }

        /// <summary> 
        /// 公司代码
        /// </summary> 
        [Description("公司代码")]
        public string CompanyCode { get; set; }

        /// <summary> 
        /// 工厂代码
        /// </summary> 
        [Description("工厂代码")]
        public string FactoryCode { get; set; }

        /// <summary> 
        /// 移动类型
        /// </summary> 
        [Description("移动类型")]
        public string MovementType { get; set; }

        /// <summary> 
        /// 移动类型名称
        /// </summary> 
        [Description("移动类型名称")]
        public string MovementTypeName { get; set; }

        /// <summary> 
        /// 成本中心
        /// </summary> 
        [Description("成本中心")]
        public string CostCenter { get; set; }

        /// <summary> 
        /// 成本中心名称
        /// </summary> 
        [Description("成本中心名称")]
        public string CostCenterName { get; set; }

        /// <summary> 
        /// 总账科目
        /// </summary> 
        [Description("总账科目")]
        public string LedgerAccount { get; set; }

        /// <summary> 
        /// 总账科目名称
        /// </summary> 
        [Description("总账科目名称")]
        public string LedgerAccountName { get; set; }

        /// <summary> 
        /// 订单
        /// </summary> 
        [Description("订单")]
        public string Order { get; set; }

        /// <summary> 
        /// 资产卡片
        /// </summary> 
        [Description("资产卡片")]
        public string AssetCard { get; set; }

        /// <summary> 
        /// 资产卡片名称
        /// </summary> 
        [Description("资产卡片名称")]
        public string AssetCardName { get; set; }

        /// <summary> 
        /// 特殊库存 E：销售库存 O:供应商库存 T:在途库存 null or "": 正常仓库库存
        /// </summary> 
        [Description("特殊库存")]
        public string SpecialInventory { get; set; }

        /// <summary> 
        /// 销售订单号
        /// </summary> 
        [Description("销售订单号")]
        public string SalesOrderNum { get; set; }

        /// <summary> 
        /// 销售订单行号
        /// </summary> 
        [Description("销售订单行号")]
        public int? SalesOrderLine { get; set; }

        /// <summary> 
        /// 评估类型
        /// </summary> 
        [Description("评估类型")]
        public string EvaluationType { get; set; }

        /// <summary> 
        /// 设备编号
        /// </summary> 
        [Description("设备编号")]
        public string EquipmentNum { get; set; }

        /// <summary>
        /// SAP过账标示
        /// </summary>
        [Description("SAP过账标示")]
        public string SAPmark { get; set; }

        /// <summary>
        /// SAP过账消息
        /// </summary>
        [Description("SAP过账消息")]
        public string SAPmessage { get; set; }

    }
}
