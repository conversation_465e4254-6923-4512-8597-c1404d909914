using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.PP.Dto
{
    /// <summary>
    /// 车间退料
    /// </summary>
    public class PP_ReturnMaterial_Dto : PP_ReturnMaterial
    {
        /// <summary>
        /// 明细
        /// </summary>
        public List<PP_ReturnMaterialDetail> detailed { get; set; }
        /// <summary>
        /// 删除明细id
        /// </summary>
        public string[] deletedetail { get; set; }
        /// <summary>
        /// 移动类型,工废：261，料废:311 跟SAP无关做报表用
        /// </summary>
        public string MovementType { get; set; }
    }

    /// <summary>
    /// 退料审核
    /// </summary>
    public class ReturnExamine
    {
        /// <summary>
        /// 审核实体
        /// </summary>
        public List<PP_ReturnMaterial> entities { get; set; }
        /// <summary>
        /// 明细
        /// </summary>
        public List<PP_ReturnMaterialDetail> DetailList { get; set; }
        /// <summary>
        /// 审核状态，0未审核，1已审核
        /// </summary>
        public int status { get; set; }
        /// <summary>
        /// 手动过账时间
        /// </summary>
        public DateTime? ManualPostTime { get; set; }
    }
}
