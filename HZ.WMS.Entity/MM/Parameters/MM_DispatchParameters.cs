using HZ.WMS.Entity.MM.ViewModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.MM.Parameters
{
    /// <summary>
    /// 接收参数的类
    /// </summary>
    public class MM_DispatchParameters
    {
        /// <summary>
        /// 库位编号
        /// </summary>
        public string BinLocationCode { get; set; }

        /// <summary>
        /// 库位
        /// </summary>
        public string BinLocationName { get; set; }

        /// <summary>
        /// 区域编号
        /// </summary>
        public string RegionCode { get; set; }

        /// <summary>
        /// 区域
        /// </summary>
        public string RegionName { get; set; }

        /// <summary>
        /// 仓库编号
        /// </summary>
        public string WhsCode { get; set; }

        /// <summary>
        /// 仓库
        /// </summary>
        public string WhsName { get; set; }

        /// <summary>
        /// 委外领料申请信息
        /// </summary>
        public List<MM_DispatchForPickingApply_View> entities { get; set; }

        /// <summary>
        /// 手动过账时间
        /// </summary>
        public DateTime ManualPostTime { get; set; }
    }
}
