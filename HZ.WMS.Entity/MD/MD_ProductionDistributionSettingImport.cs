using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.MD
{
    /// <summary>
    /// 生产配送配置表
    /// </summary>
    public class MD_ProductionDistributionSettingImport
    {

        /// <summary>
        /// 员工号
        /// </summary>
        public string 员工号 { get; set; }
        /// <summary>
        /// 员工姓名
        /// </summary>
        public string 员工姓名 { get; set; }
        /// <summary>
        /// 线体编码
        /// </summary>
        public string 线体编码 { get; set; }
        /// <summary>
        /// 线体描述
        /// </summary>
        public string 线体描述 { get; set; }
        /// <summary>
        /// 物料编号 模糊查询
        /// </summary>
        public string 物料描述 { get; set; }
        /// <summary>
        /// 物料组编码
        /// </summary>
        public string 物料组编码 { get; set; }
        /// <summary>
        /// 物料组描述
        /// </summary>
        public string 物料组描述 { get; set; }
    }
}
