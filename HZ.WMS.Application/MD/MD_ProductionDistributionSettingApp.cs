using SqlSugar;
using HZ.WMS.Entity.MD;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Application.MD
{
    /// <summary>
    /// 生产配送配置
    /// </summary>
    public class MD_ProductionDistributionSettingApp : BaseApp<MD_ProductionDistributionSetting>
    {
        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public MD_ProductionDistributionSettingApp() : base()
        {
        }

        #endregion

        #region 获取外部物料组

        public DataTable GetMaterialGroup()
        {
            return DbContext.Ado.GetDataTable($"select EXTWG MaterialGroupCode,EWBEZ MaterialGroupDes from XZ_SAP.dbo.XZ_SAP_TWEWT");
        }

        #endregion

        #region 获取线体

        /// <summary>
        /// 获取SAP中间库所有线体
        /// </summary>
        /// <returns></returns>
        public DataTable GetAllLine()
        {
            return DbContext.Ado.GetDataTable($"select distinct ARBPL ProductionLineCode,KTEXT ProductionLineDes from XZ_SAP.dbo.XZ_SAP_CRHD");
        }

        /// <summary>
        /// 获取主机车间线体
        /// </summary>
        /// <returns></returns>
        public DataTable GetHostLine()
        {
            return DbContext.Ado.GetDataTable($"select distinct a.ARBPL ProductionLineCode,a.KTEXT ProductionLineDes from XZ_SAP.dbo.XZ_SAP_CRHD a LEFT JOIN XZ_SAP.dbo.XZ_SAP_CRHD b ON b.ARBPL = a.ARBPL where b.KTEXT like '主机车间%' ");
        }

        #endregion



        #region 导入

        /// <summary>
        /// 导入
        /// </summary>
        /// <param name="excelList">接收的集合</param>
        /// <param name="opUser">当前用户</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool ImprotExcelToBaseData(List<MD_ProductionDistributionSettingImport> excelList, string opUser, out string error_message)
        {
            error_message = "";


            var flag = true;
            DateTime date = DateTime.Now;
            List<MD_ProductionDistributionSetting> list = new List<MD_ProductionDistributionSetting>();
   
            try
            {
                //暂时只进行插入操作，有需要在做更新操作
                foreach (var x in excelList)
                {
                   
                    var z = new MD_ProductionDistributionSetting();
                   z.EmployeeNumber= x.员工号;
                    z.EmployeeName = x.员工姓名;
                    z.ProductionLineCode = x.线体编码;
                    z.ProductionLineDes = x.线体描述;
                    z.ItemName = x.物料描述;
                    z.MaterialGroupCode = x.物料组编码;
                    z.MaterialGroupDes = x.物料组描述;
                    z.IsDelete = false;
                    z.CUser = opUser;
                    z.CTime = date;
                    list.Add(z);
                };


                this.DbContext.Ado.BeginTran();
 

                this.Insert(list);
            

                this.DbContext.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                 flag = false;
                if (this.DbContext.Ado.IsAnyTran())
                    this.DbContext.Ado.RollbackTran();
                throw ex;
            }
            return flag;
        }
        #endregion
    }
}
