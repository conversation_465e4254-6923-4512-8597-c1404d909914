using System;
using System.Collections.Generic;
using AOS.WMS.Entity.CableProduce;
using HZ.Core.Http;
using HZ.WMS.Entity;
using HZ.WMS.Entity.PP;
using SqlSugar;

namespace HZ.WMS.Application.PP
{
    /// <summary>
    /// 生产订单主表
    /// </summary>
    public class Cable_ShippingPlanApp : BaseApp<ShippingPlan>
    {
        private BaseApp<BaseEntity> _baseApp = new BaseApp<BaseEntity>();

        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public Cable_ShippingPlanApp() : base()
        {
        }

        #endregion

        /**
         * 获取 计划订单列表
         */
        public List<ShippingPlan> GetPlanOrderPageList(Pagination page, Cable_ProductionOrderListReq productionOrderListReq)
        {
            return GetPageList(page, t =>
                (string.IsNullOrEmpty(productionOrderListReq.ContractNo) || t.ContractNo.Equals(productionOrderListReq.ContractNo))
                                                                       && (string.IsNullOrEmpty(productionOrderListReq.SapNo) || t.SapNo.Equals(productionOrderListReq.SapNo))
                                                                       && (productionOrderListReq.SapLine == null || t.SapLine.Equals(productionOrderListReq.SapLine)) &&
                                                                       t.CTime >= productionOrderListReq.GetStartCreateDate() &&
                                                                       t.CTime < productionOrderListReq.GetEndCreateDate() &&
                                                                       t.IsDelete == false);
        }

        /**
         * 创建计划订单
         */
        public ResponseData CreatePlanOrder(List<string> ids, DateTime assembleDate)
        {
            var list = GetList(t => ids.Contains(t.Id)).ToList();
            
            foreach (var cableProductionOrder in list)
            {
                cableProductionOrder.Status = 2;
            }

            Update(list);
            return new ResponseData((int)WMSStatusCode.Success,"创建成功");
        }

        /**
         * 计划转生产订单
         */
        public ResponseData PlanConvertProduceOrder(List<string> ids)
        {
            var list = GetList(t => ids.Contains(t.Id)).ToList();
            foreach (var cableProductionOrder in list)
            {
                cableProductionOrder.Status = 3;
            }
            Update(list);
            return new ResponseData((int)WMSStatusCode.Success,"转换成功");
        }

        /**
         * 序列号分配
         */
        public ResponseData DispenseSerialNo(List<string> ids)
        {
            var list = GetList(t => ids.Contains(t.Id)).ToList();
            foreach (var cableProductionOrder in list)
            {
                cableProductionOrder.Status = 4;
            }
            Update(list);
            return new ResponseData((int)WMSStatusCode.Success,"转换成功");
        }

        /**
         * 生产报工
         */
        public object ProduceReportWork(List<string> ids)
        {
            var list = GetList(t => ids.Contains(t.Id)).ToList();
            foreach (var cableProductionOrder in list)
            {
                cableProductionOrder.Status = 5;
            }
            Update(list);
            return new ResponseData((int)WMSStatusCode.Success,"转换成功");
        }

        public bool Deletes(string[] ids, string loginAccount)
        {
            DeleteByKeys(ids,loginAccount);
            return true;
        }
    }
}