using System;
using System.Collections.Generic;
using System.Linq;
using AOS.OMS.Entity.Sale;
using AOS.WMS.Entity.CableProduce;
using HZ.Core.Http;
using HZ.WMS.Application;
using HZ.WMS.Entity.Sys;

namespace AOS.OMS.Application.Salesa
{
    /// <summary>
    ///  订单 app
    /// </summary>
    public class SD_Cable_Sale_PartOrderInfoApp : BaseApp<SD_Cable_Sale_PartOrderInfo>
    {
        #region 初始化

        #endregion

        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public SD_Cable_Sale_PartOrderInfoApp() : base()
        {
        }

        #endregion


        /**
         * 扫描确认
         */
        public ResponseData ScanConfirm(string[] ids, Sys_User getCurrentUser)
        {
            var list = DbContextForOMS.Queryable<SD_Cable_Sale_PartOrderInfo>().Where(t => ids.Contains(t.Id)).ToList();
            foreach (var salePartOrderInfo in list)
            {
                salePartOrderInfo.PackingFlag = 1;
                salePartOrderInfo.MUser = getCurrentUser.LoginAccount;
                salePartOrderInfo.MTime = DateTime.Now;
            }
            DbContextForOMS.Updateable<SD_Cable_Sale_PartOrderInfo>(list).ExecuteCommand();
            return new ResponseData((int)WMSStatusCode.Success, "扫描确认成功");
        }
    }
}