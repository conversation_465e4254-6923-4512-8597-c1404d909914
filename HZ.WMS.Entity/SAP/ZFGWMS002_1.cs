using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.SAP
{
    /// <summary>
    /// 采购收-组件
    /// </summary>
   public class ZFGWMS002_1
    {
        /// <summary>
        /// 采购订单号
        /// </summary>
        public string EBELN { get; set; }

        /// <summary>
        /// 采购订单行号
        /// </summary>
        public int EBELP { get; set; }

        /// <summary>
        /// 预留
        /// </summary>
        public int RSNUM { get; set; }

        /// <summary>
        /// 预留的项目编号
        /// </summary>
        public int RSPOS { get; set; }

        /// <summary>
        /// 物料编码
        /// </summary>
        public string MATNR { get; set; }

        /// <summary>
        /// 工厂
        /// </summary>
        public string WERKS { get; set; }

        /// <summary>
        /// 移动类型
        /// </summary>
        public string BWART { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public decimal BDMNG { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string MEINS { get; set; }

        /// <summary>
        /// 供应商
        /// </summary>
        public string LIFNR { get; set; }

        /// <summary>
        /// 评估类型
        /// </summary>
        public string BWTAR { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string SGTXT { get; set; }

        /// <summary>
        /// 库存地点
        /// </summary>
        public string LGORT { get; set; }
        
    }
}
