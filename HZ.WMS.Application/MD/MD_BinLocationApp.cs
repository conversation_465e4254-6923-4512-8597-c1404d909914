using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using HZ.Core.Security;
using HZ.WMS.Entity.MD;
using HZ.Core.Http;
using HZ.WMS.Entity;
using HZ.WMS.Entity.MD.ViewModel;
using System.Linq.Expressions;
using System.Reflection.Emit;

namespace HZ.WMS.Application.MD
{
    /// <summary>
    /// 仓库(warehouse)->区域(region)->库位管理(binlocation*)
    /// </summary>

    public class MD_BinLocationApp : BaseApp<MD_BinLocation>
    {
        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public MD_BinLocationApp() : base()
        {
        }




        #endregion

        /// <summary>
        /// 获取退供区库位
        /// </summary>
        public static string RETURN_SUPPLIER_BINLOCATIONCODE = "IN002";

        #region 获取推荐库位

        /// <summary>
        /// 获取推荐库位
        /// </summary>
        /// <param name="opType">推荐类型</param>
        /// <param name="top">推荐的数量</param>
        /// <returns></returns>
        //public List<MD_BinLocation> GetRecommend(string opType, int top = 1, string itemCode = null)
        //{
        //    List<MD_BinLocation> result = new List<MD_BinLocation>();
        //    switch (opType.ToUpper())
        //    {
        //        case "PP_RETURNSCAN":
        //            //todo:

        //            //模拟
        //            string[] haveStockBinLocation1 = this.DbContext.Queryable<MD_Stock>(x => !x.IsDelete && x.RegionCode == "RW1" && x.Qty > 0).ToList().Select(x => x.BinLocationCode).Distinct().ToArray();
        //            result = this.DbContext.Queryable<MD_BinLocation>(x => !x.IsDelete && x.RegionCode == "RW1" && !haveStockBinLocation1.Contains(x.BinLocationCode)).ToList().OrderBy(x => x.BinLocationCode).Take(top).ToList();
        //            break;
        //        case "PP_INSCAN":
        //            //todo:

        //            //模拟
        //            string[] haveStockBinLocation = this.DbContext.Queryable<MD_Stock>(x => !x.IsDelete && x.RegionCode == "RW1" && x.Qty > 0).ToList().Select(x => x.BinLocationCode).Distinct().ToArray();
        //            result = this.DbContext.Queryable<MD_BinLocation>(x => !x.IsDelete && x.RegionCode == "RW1" && !haveStockBinLocation.Contains(x.BinLocationCode)).ToList().OrderBy(x => x.BinLocationCode).Take(top).ToList();
        //            break;
        //        case "PO_SHELFSCAN":
        //            bool? isFreeTax = false;
        //            if (itemCode != null)
        //            {
        //                var item = new MD_ItemApp().GetFirstEntity(x => x.ItemCode == itemCode);
        //                isFreeTax = item == null ? false : item?.IsFreeTax;
        //            }
        //            string[] query = this.DbContext.Queryable<MD_BinLocation>(x => x.RegionCode == "RW1" && !x.IsDelete && (itemCode == null))
        //                 .LeftJoin<MD_Stock>((bin, stock) => bin.BinLocationCode == stock.BinLocationCode)
        //                 .Select(((bin, stock) => new { BinLocationCode = bin.BinLocationCode, StockQty = stock.Qty }))
        //                 .ToList()
        //                 .GroupBy(t => t.BinLocationCode)
        //                 .Select(x => new { BinLocationCode = x.Key, StockQty = x.Sum(t => t.StockQty) })
        //                 .OrderBy(x => x.StockQty)
        //                 .ThenBy(x => x.BinLocationCode)
        //                 .ToList()
        //                 .Take(top)
        //                 .Select(x => x.BinLocationCode)
        //                 .ToArray();

        //            result = base.GetList(x => query.Contains(x.BinLocationCode)).ToList();

        //            break;
        //        default:
        //            result = null;
        //            break;
        //    }

        //    return result;
        //}

        #endregion

        #region 获取退供区库位

        /// <summary>
        /// 获取退供区库位
        /// </summary>
        /// <returns></returns>
        public MD_BinLocation GetPurchaseReturnBinLocation()
        {
            return GetFirstEntity(x => x.BinLocationCode == "IN002");
        }


        #endregion

        /// <summary>
        /// 获取生产线对应的库位
        /// </summary>
        /// <param name="pLine"></param>
        /// <returns></returns>
        public MD_BinLocation GetPLineBinLocation(string pLine)
        {
            var binLocation = this.GetFirstEntity(x => x.RegionCode == "BK1" );
            return binLocation;
        }

        /// <summary>
        /// 获取生产线对应的库位
        /// </summary>
        /// <param name="pLines"></param>
        /// <returns></returns>
        public List<MD_BinLocation> GetPLinesBinLocation(params string[] pLines)
        {
            var binLocations = this.GetList(x => x.RegionCode == "BK1" ).ToList();
            return binLocations;
        }

        #region 获取待检区

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public List<MD_BinLocation> GetPoInspectionBinLocations()
        {
            string[] inspBinLocation = new string[] { "IN002", "IN003" };
            return GetList(x => !inspBinLocation.Contains(x.BinLocationCode) && x.RegionCode == MD_RegionApp.INSPECTION_REGIONCODE).ToList();
        }

        #endregion

        #region 获取SAP物料库位信息

        /// <summary>
        /// 获取SAP物料库位信息
        /// </summary>
        /// <param name="ItemCode">物料编号</param>
        /// <returns></returns>
        public SAP_ItemBinLocation_View GetSAPItemBinLocation(string ItemCode)
        {
            var query = DbContext.Queryable<SAP_ItemBinLocation_View>()
                .Where(x => x.ItemCode == ItemCode).ToList().FirstOrDefault();
            return query;
        }

        #endregion

        #region 

        /// <summary>
        /// 校验所有仓库是否存在
        /// </summary>
        /// <param name="whsCodes">仓库数组</param>
        /// <param name="atnam"></param>
        /// <returns></returns>
        public bool IsHaveWhsCode(string[] whsCodes,string atnam = null)
        {
            var isNotNullOrEmptywhsCodes = whsCodes.Where(x => !string.IsNullOrEmpty(x)).ToArray();
            var data = this.DbContext.Queryable<MD_BinLocation>().Where(x => isNotNullOrEmptywhsCodes.Contains(x.WhsCode)).ToList();
            if (data.Count() == isNotNullOrEmptywhsCodes.Length)
            {
                return true;
            }
            else
            {
                string notCode = string.Empty;
                foreach (string str in isNotNullOrEmptywhsCodes)
                {
                    var notData = data.Where(s => s.WhsCode.Equals(str))?.ToList();
                    if (notData != null && notData.Count > 0)
                    {
                        continue;
                    }
                    else
                    {
                        notCode = str;
                        break;
                    }
                }
                if (data.Count() > 1 && notCode == "")
                {
                    throw new Exception(string.Format("导入数据中[{0}]在系统中存在数量不匹配请检查！", SwitchWhsCodeType(atnam)));
                }
                else
                {
                    throw new Exception(string.Format("导入数据中[{0}:{1}]在系统中不存在！", SwitchWhsCodeType(atnam), notCode));
                }
            }
        }

        /// <summary>
        /// 转入/转出仓库编码
        /// </summary>
        /// <param name="atnam"></param>
        /// <returns></returns>
        public string SwitchWhsCodeType(string atnam)
        {
            string atnamStr = "仓库编号";
            switch (atnam)
            {
                case "inWhsCode":
                    atnamStr = "转入仓库编码";
                    break;
                case "outWhsCode":
                    atnamStr = "转出仓库编码";
                    break;
            }
            return atnamStr;
        }

        #endregion

    }
}

