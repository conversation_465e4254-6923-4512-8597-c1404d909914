using System;
using System.Web;
using HZ.WMS.Application;

namespace HZ.WMS.WebAPI
{
    /// <summary>
    /// 数据库配置类
    /// </summary>
    public static class DatabaseConfig
    {
        /// <summary>
        /// 初始化数据库连接池
        /// </summary>
        public static void Initialize()
        {
            // 预热连接池
            try
            {
                HZ.Core.Logging.LogHelper.Instance.LogInfo("开始初始化数据库连接池...");
                
                var connectionKeys = new[] 
                { 
                    "DbConnection", 
                    "DbConnectionForSAP", 
                    "DbConnectionForSRM", 
                    "DbConnectionForEAP", 
                    "DbConnectionForOMS" 
                };

                foreach (var key in connectionKeys)
                {
                    try
                    {
                        var db = SqlSugarClientFactory.GetClient(key);
                        // 执行简单查询来预热连接
                        db.Ado.GetString("SELECT 1");
                        HZ.Core.Logging.LogHelper.Instance.LogInfo($"数据库连接池 {key} 初始化成功");
                    }
                    catch (Exception ex)
                    {
                        HZ.Core.Logging.LogHelper.Instance.LogError($"数据库连接池 {key} 初始化失败: {ex.Message}\n堆栈跟踪: {ex.StackTrace}");
                    }
                }
                
                // 注册应用程序结束时的清理事件
                HttpRuntime.Cache.Insert("DatabaseCleanup", 
                    new object(), 
                    null, 
                    DateTime.MaxValue, 
                    TimeSpan.Zero, 
                    System.Web.Caching.CacheItemPriority.NotRemovable, 
                    (key, value, reason) => 
                    {
                        HZ.Core.Logging.LogHelper.Instance.LogInfo("应用程序关闭，清理数据库连接池");
                        SqlSugarClientFactory.DisposeAll();
                    });

                // 启动连接池清理服务
                HZ.WMS.Application.ConnectionPoolCleanupService.Start();

                HZ.Core.Logging.LogHelper.Instance.LogInfo("数据库连接池初始化完成");
            }
            catch (Exception ex)
            {
                // 记录初始化错误
                HZ.Core.Logging.LogHelper.Instance.LogError($"数据库连接池初始化失败：{ex.Message}\n堆栈跟踪: {ex.StackTrace}");
                throw; // 重新抛出异常，确保应用程序知道初始化失败
            }
        }

        /// <summary>
        /// 获取连接池状态
        /// </summary>
        /// <returns></returns>
        public static string GetConnectionPoolStatus()
        {
            return SqlSugarClientFactory.GetPoolStatus();
        }

        /// <summary>
        /// 清理空闲连接
        /// </summary>
        public static void ClearIdleConnections()
        {
            // SqlSugar 的连接池会自动管理，这里记录日志
            HZ.Core.Logging.LogHelper.Instance.LogInfo("执行连接池清理操作");
        }
    }
}
