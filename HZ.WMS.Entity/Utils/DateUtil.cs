using System;

namespace HZ.Core.Utilities
{
    public static class DateUtil
    {

        /// <summary>
        /// 查询/导出 时间条件格式化
        /// </summary>
        /// <param name="dateTimes"></param>
        /// <param name="isDateTime"></param>
        /// <returns></returns>
        public static DateTime[] QueryDateTimesFormat(DateTime[] dateTimes, bool isDateTime = false)
        {
            DateTime fromTime = new DateTime(2020, 1, 1).Date;
            DateTime toTime = DateTime.Now.AddDays(1).Date;
            if (dateTimes != null && dateTimes.Length >= 2)
            {
                fromTime = dateTimes[0].Date;
                toTime = dateTimes[1].AddDays(1).Date;
                if (isDateTime)
                {
                    fromTime = dateTimes[0];
                    toTime = dateTimes[1].AddDays(1);
                }
            }

            return new DateTime[] { fromTime, toTime };
        }
    }
}