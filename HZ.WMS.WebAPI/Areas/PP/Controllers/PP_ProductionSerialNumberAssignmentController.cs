using HZ.Core.Http;
using HZ.Core.Office;
using HZ.WMS.Application;
using HZ.WMS.Application.PP;
using HZ.WMS.Application.Sys;
using HZ.WMS.Entity.PP;
using HZ.WMS.Entity.PP.ViewModel;
using HZ.WMS.Entity.Sys;
using HZ.WMS.WebAPI.Controllers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;

namespace HZ.WMS.WebAPI.Areas.PP.Controllers
{
    /// <summary>
    /// 生产序列号分配
    /// </summary>
    public class PP_ProductionSerialNumberAssignmentController : ApiBaseController
    {
        private PP_ProductionOrderApp _app = new PP_ProductionOrderApp();
        private Sys_MessageApp messageApp = new Sys_MessageApp();
        private PP_ProductionFeedingApp feedingApp = new PP_ProductionFeedingApp();
        private PP_ProductionReportApp reportApp = new PP_ProductionReportApp();
        private PP_AssignSerialNoViewApp _appView = new PP_AssignSerialNoViewApp();

        #region 获取数据

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]string keyword, [FromUri]string SalesOrderNo, [FromUri]string ContractNo, [FromUri]string OrderNo, [FromUri]string SalesOrderLineNo,
            [FromUri]string ProductionLineDes, [FromUri]string ProductionScheduler, [FromUri] DateTime[] dateValue, [FromUri] DateTime[] createDateValue)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];


                DateTime? fromCreateTime = null;
                DateTime? toCreateTime = null;
                if (createDateValue != null && createDateValue.Length >= 2)
                {
                    fromCreateTime = createDateValue[0].Date;
                    toCreateTime = createDateValue[1].AddDays(1).Date;
                }
                var querCreateDateTimes = FormatProcessor.QueryDateTimesFormat(createDateValue);
                

                //var itemsData = _app.GetOrderListWithSerialNo(page, keyword, ProductionLineDes, ProductionScheduler, fromTime,toTime).ToList();
                page.Sort = "ProductionOrderNo desc";
                //var itemsData = _appView.GetPageList(page,
                //    t => (string.IsNullOrEmpty(keyword)
                //        || t.SerialNo.Contains(keyword)
                //        || t.ProductionOrderNo.Contains(keyword)
                //        || t.MaterialNo.Contains(keyword)
                //        || t.MaterialName.Contains(keyword)
                //        || t.Shippers.Contains(keyword) || t.CUser.Contains(keyword))
                //        && (string.IsNullOrEmpty(SalesOrderNo) || t.SalesOrderNo.Contains(SalesOrderNo))
                //        && (string.IsNullOrEmpty(SalesOrderLineNo) || t.SalesOrderLineNo == Convert.ToDecimal(SalesOrderLineNo))
                //        && (string.IsNullOrEmpty(ContractNo) || t.ContractNo.Contains(ContractNo))
                //        && (string.IsNullOrEmpty(OrderNo) || t.OrderNo.Contains(OrderNo))
                //        && (string.IsNullOrEmpty(ProductionLineDes) || t.ProductionLineDes.Contains(ProductionLineDes))
                //        && (string.IsNullOrEmpty(ProductionScheduler) || t.ProductionScheduler.Contains(ProductionScheduler))
                //        && (t.StartTime >= fromTime && t.StartTime < toTime))
                //    .ToList();
                var itemsData = _appView.GetSerialNoList(page, fromTime, toTime, fromCreateTime, toCreateTime, keyword, SalesOrderNo, ContractNo, OrderNo, SalesOrderLineNo, ProductionLineDes, ProductionScheduler);
                itemsData = _appView.HandleEapSerialNo(itemsData);
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除

        [HttpDelete]
        public IHttpActionResult Delete([FromBody]string[] SerialNos)
        {
            var result = new ResponseData();
            try
            {
                if (SerialNos == null || SerialNos.Count() < 1)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "请选择需要删除的数据";
                    return Json(result);
                }

                if (feedingApp.Any(w => SerialNos.Contains(w.SerialNo)) || reportApp.Any(w => SerialNos.Contains(w.SerialNo)))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "订单有生产投料记录不允许删除的数据，请重新选择";
                    return Json(result);
                }
                if (reportApp.Any(w => SerialNos.Contains(w.SerialNo)) || reportApp.Any(w => SerialNos.Contains(w.SerialNo)))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "订单有报工记录不允许删除的数据，请重新选择";
                    return Json(result);
                }
                _app.HardDelete(h => SerialNos.Contains(h.SerialNo));
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 同步

        /// <summary>
        /// 同步SAP数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult SyncProductionOrder([FromUri]DateTime[] dateValue)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];

                Sys_User sysUser = GetCurrentUser();
                if (!_app.UpdateDataBySAP(sysUser.LoginAccount, fromTime, toTime))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                }
            }
            catch (Exception ex)
            {
                //result.Message = ex.InnerException?.Message ?? ex.Message;
                result.Message = ex.ToString();
                result.Code = (int)WMSStatusCode.UnHandledException;
            }
            return Json(result);
        }

        #endregion

        #region 通知

        /// <summary>
        /// 邮件通知
        /// </summary>
        /// <param name="ids">工单号</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult IssueNotice(List<string> ids)
        {
            var result = new ResponseData();
            try
            {
                if (ids == null || ids.Count < 1)
                {
                    result.Message = "请选择要通知的订单";
                    return Json(result);
                }

                Sys_User sysUser = GetCurrentUser();
                var list = _app.GetList(w => ids.Contains(w.ProductionOrderNo)).ToList();
                if (list.Count != ids.Count)
                {
                    ids.RemoveAll(r => list.Select(s => s.ProductionOrderNo).Contains(r));
                    result.Message = $"订单：{string.Join(",", ids)}未分配出厂编号";
                }
                else
                {
                    //通知相关人员
                    messageApp.ProductionSend(list, sysUser.LoginAccount);
                    //更新数据库状态
                    list.ForEach(entity =>
                    {
                        entity.IsNoticed = 1;
                        entity.MUser = sysUser.LoginAccount;
                    });
                    _app.UpdateWithTran(list);
                    result.Message = "通知成功";
                }
            }
            catch (Exception ex)
            {
                result.Message = ex.InnerException?.Message ?? ex.Message;
                result.Code = (int)WMSStatusCode.UnHandledException;
            }
            return Json(result);
        }

        #endregion

        #region 导出

        /// <summary>
        /// 导出
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri]string keyword, [FromUri]string SalesOrderNo, [FromUri]string ContractNo, [FromUri]string OrderNo, [FromUri]string SalesOrderLineNo,
            [FromUri]  string ProductionLineDes, [FromUri]string ProductionScheduler, [FromUri] DateTime[] dateValue)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];

                //var itemsData = _app.GetExectData(fromTime, toTime, productionOrderID, productionOrderStatus, productionLine); PP_ProductionOrder_View
                var itemsData = _app.ExportAssignSerialNo(null,
                    w => (string.IsNullOrEmpty(keyword)
                    || w.SerialNo.Contains(keyword)
                        || w.ProductionOrderNo.Contains(keyword)
                        || w.MaterialNo.Contains(keyword)
                        || w.MaterialName.Contains(keyword)
                        || w.Shipper.Contains(keyword) || w.CUser.Contains(keyword))
                    && (string.IsNullOrEmpty(SalesOrderNo) || w.SalesOrderNo.Contains(SalesOrderNo))
                    && (string.IsNullOrEmpty(SalesOrderLineNo) || w.SalesOrderLineNo == Convert.ToDecimal(SalesOrderLineNo))
                    && (string.IsNullOrEmpty(ContractNo) || w.ContractNo.Contains(ContractNo))
                    && (string.IsNullOrEmpty(OrderNo) || w.OrderNo.Contains(OrderNo))
                    && (string.IsNullOrEmpty(ProductionLineDes) || w.AssemblyLineNo.Contains(ProductionLineDes))
                    && (string.IsNullOrEmpty(ProductionScheduler) || w.ProductionScheduler == ProductionScheduler)
                    && (w.StartTime >= fromTime && w.StartTime < toTime)
                    ).ToList();
                List<ExcelColumn<PP_ExportAssignSerialNo_View>> columns = ExcelService.FetchDefaultColumnList<PP_ExportAssignSerialNo_View>();
                string[] ignoreField = new string[]
                {
                    "ProductionScheduler"
                    ,"StartTime"
                };
                List<ExcelColumn<PP_ExportAssignSerialNo_View>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<PP_ExportAssignSerialNo_View> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                //columns.ForEach((column) =>
                //{
                //    if (column.ColumnName == "IsPosted")
                //    {
                //        column.Formattor = ExcelExportFormatter.IsOrNoFormatter;
                //    }
                //});

                //return ExportToExcelFile<PP_ExportAssignSerialNo_View>(itemsData, columns);
                var dic = new Dictionary<string, List<PP_ExportAssignSerialNo_View>>();
                //混装线和标准线合并为常规线
                foreach (var item in itemsData)
                {
                    
                    if (item.AssemblyLineNo.Contains("标准装配线") || item.AssemblyLineNo.Contains("混装装配线"))
                    {
                        var key = "常规装配线";
                        if (dic.Keys.Contains(key))
                        {
                            dic[key].Add(item);
                        }
                        else
                        {
                            dic.Add(key, new List<PP_ExportAssignSerialNo_View> { item });
                        }
                    }
                    else
                    {
                        var key = item.AssemblyLineNo;
                        if (dic.Keys.Contains(key))
                        {
                            dic[key].Add(item);
                        }
                        else
                        {
                            dic.Add(key, new List<PP_ExportAssignSerialNo_View> { item });
                        }
                    }
                }

                //排序
                var _key = $"常规装配线";
                if (dic.Keys.Contains(_key))
                {
                    dic[_key] = dic[_key].OrderByDescending(o => o.AssemblyLineNo).ToList();
                }
                dic = dic.OrderBy(o => o.Key).ToDictionary(p => p.Key, o => o.Value);

                //序号
                foreach (var item in dic.Keys)
                {
                    int line = 1;
                    foreach (var val in dic[item])
                    {
                        val.SequenceNo = line;
                        line++;
                    }
                }
                //文件名称
                string title = $"新正式排产：{DateTime.Now.GetDateTimeFormats('M')[0].ToString()}主机装配指令单（车间{dateValue[0].GetDateTimeFormats('M')[0].ToString()}-{dateValue[1].GetDateTimeFormats('M')[0].ToString()}装配)";
                if (dateValue[0] == dateValue[1])
                {
                    title = $"新正式排产：{DateTime.Now.GetDateTimeFormats('M')[0].ToString()}主机装配指令单（车间{dateValue[0].GetDateTimeFormats('M')[0].ToString()}装配)";
                }
                return ExportToExcelFileByGroup<string, PP_ExportAssignSerialNo_View>(dic, columns, title);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #region 导出

        /// <summary>
        /// 导出
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFileSerialNumber([FromUri]string keyword, [FromUri]string SalesOrderNo, [FromUri]string ContractNo, [FromUri]string OrderNo, [FromUri]string SalesOrderLineNo,
            [FromUri]  string ProductionLineDes, [FromUri]string ProductionScheduler, [FromUri] DateTime[] dateValue, [FromUri] DateTime[] createDateValue)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];

                DateTime? fromCreateTime = null;
                DateTime? toCreateTime = null;
                if (createDateValue != null && createDateValue.Length >= 2)
                {
                    fromCreateTime = createDateValue[0].Date;
                    toCreateTime = createDateValue[1].AddDays(1).Date;
                }

                //var itemsData = _app.GetExectData(fromTime, toTime, productionOrderID, productionOrderStatus, productionLine); PP_ProductionOrder_View
                //var itemsData = _appView.GetList(
                //      t => (string.IsNullOrEmpty(keyword)
                //          || t.SerialNo.Contains(keyword)
                //          || t.ProductionOrderNo.Contains(keyword)
                //          || t.MaterialNo.Contains(keyword)
                //          || t.MaterialName.Contains(keyword)
                //          || t.Shippers.Contains(keyword) || t.CUser.Contains(keyword))
                //          && (string.IsNullOrEmpty(SalesOrderNo) || t.SalesOrderNo.Contains(SalesOrderNo))
                //          && (string.IsNullOrEmpty(SalesOrderLineNo) || t.SalesOrderLineNo == Convert.ToDecimal(SalesOrderLineNo))
                //          && (string.IsNullOrEmpty(ContractNo) || t.ContractNo.Contains(ContractNo))
                //          && (string.IsNullOrEmpty(OrderNo) || t.OrderNo.Contains(OrderNo))
                //          && (string.IsNullOrEmpty(ProductionLineDes) || t.ProductionLineDes.Contains(ProductionLineDes))
                //          && (string.IsNullOrEmpty(ProductionScheduler) || t.ProductionScheduler.Contains(ProductionScheduler))
                //          && (t.StartTime >= fromTime && t.StartTime < toTime))
                //      .ToList();

                var itemsData = _appView.GetSerialNoList( fromTime, toTime, fromCreateTime, toCreateTime, keyword, SalesOrderNo, ContractNo, OrderNo, SalesOrderLineNo, ProductionLineDes, ProductionScheduler);
                itemsData = _appView.HandleEapSerialNo(itemsData);
                List<ExcelColumn<PP_AssignSerialNo_View>> columns = ExcelService.FetchDefaultColumnList<PP_AssignSerialNo_View>();
                string[] ignoreField = new string[]
                {
                    "ProductionScheduler"
                    ,"StartTime",
                    "ReceivingLocation",
                    "IsNoticed","FactoryCode","OrderStatus","OrderType","ReceiptNo","ProductionBatch",
                    "IsComponent","Remark", "IsDelete", "CTime", "CUser","DTime", "DUser", "MUser","MTime"
                };
                List<ExcelColumn<PP_AssignSerialNo_View>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<PP_AssignSerialNo_View> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                //columns.ForEach((column) =>
                //{
                //    if (column.ColumnName == "IsPosted")
                //    {
                //        column.Formattor = ExcelExportFormatter.IsOrNoFormatter;
                //    }
                //});

                //return ExportToExcelFile<PP_ExportAssignSerialNo_View>(itemsData, columns);
                var dic = new Dictionary<string, List<PP_AssignSerialNo_View>>();
                //混装线和标准线合并为常规线
                foreach (var item in itemsData)
                {
                    if (string.IsNullOrEmpty(item.ProductionLineDes) || item.ProductionLineDes.Contains("标准装配线") || item.ProductionLineDes.Contains("混装装配线"))
                    {
                        var key = "常规装配线";
                        if (dic.Keys.Contains(key))
                        {
                            dic[key].Add(item);
                        }
                        else
                        {
                            dic.Add(key, new List<PP_AssignSerialNo_View> { item });
                        }
                    }
                    else
                    {
                        var key = item.ProductionLineDes;
                        if (dic.Keys.Contains(key))
                        {
                            dic[key].Add(item);
                        }
                        else
                        {
                            dic.Add(key, new List<PP_AssignSerialNo_View> { item });
                        }
                    }
                }

                //排序
                var _key = $"常规装配线";
                if (dic.Keys.Contains(_key))
                {
                    dic[_key] = dic[_key].OrderByDescending(o => o.ProductionLineDes).ToList();
                }
                dic = dic.OrderBy(o => o.Key).ToDictionary(p => p.Key, o => o.Value);

                //序号
                foreach (var item in dic.Keys)
                {
                    int line = 1;
                    foreach (var val in dic[item])
                    {
                        val.SequenceNo = line;
                        line++;
                    }
                }
                //文件名称
                string title = $"新正式排产：{DateTime.Now.GetDateTimeFormats('M')[0].ToString()}主机装配序列号（车间{dateValue[0].GetDateTimeFormats('M')[0].ToString()}-{dateValue[1].GetDateTimeFormats('M')[0].ToString()})";
                if (dateValue[0] == dateValue[1])
                {
                    title = $"新正式排产：{DateTime.Now.GetDateTimeFormats('M')[0].ToString()}主机装序列号（车间{dateValue[0].GetDateTimeFormats('M')[0].ToString()})";
                }
                return ExportToExcelFileByGroup<string, PP_AssignSerialNo_View>(dic, columns, title);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion
    }
}
