using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.MM
{
    /// <summary>
    /// 报废申请单明细
    /// </summary>
    public class MM_ScrapApplicationDetail : BaseEntity
    {
        /// <summary>
        /// 主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Description("ID")]
        public string DetailedID { get; set; }

        /// <summary>
        /// 调拨申请单号
        /// </summary>
        [Description("调拨申请单号")]
        public string DocNum { get; set; }

        /// <summary>
        /// 调拨申请行号
        /// </summary>
        [Description("行号")]
        public int? Line { get; set; }

        /// <summary>
        /// 出厂编号
        /// </summary>
        [Description("出厂编号")]
        public string BarCode { get; set; }

        /// <summary>
        /// 物料编码
        /// </summary>
        [Description("物料编码")]
        public string ItemCode { get; set; }

        /// <summary>
        /// 物料描述
        /// </summary>
        [Description("物料名称")]
        public string ItemName { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        [Description("状态")]
        public int? Status { get; set; }

        /// <summary>
        /// 公司代码
        /// </summary>
        [Description("公司代码")]
        public string CompanyCode { get; set; }

        /// <summary>
        /// 工厂代码
        /// </summary>
        [Description("工厂代码")]
        public string FactoryCode { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量")]
        public decimal? Qty { get; set; }

        /// <summary> 
        /// 单位
        /// </summary> 
        [Description("单位")]
        public string Unit { get; set; }

        /// <summary> 
        /// "转出仓库编号
        /// </summary> 
        [Description("转出仓库编码")]
        public string OutWhsCode { get; set; }

        /// <summary> 
        /// "转出仓库
        /// </summary> 
        [Description("转出仓库")]
        public string OutWhsName { get; set; }

        /// <summary> 
        /// 转出区域编号
        /// </summary> 
        [Description("转出区域编号")]
        public string OutRegionCode { get; set; }

        /// <summary> 
        /// 转出区域
        /// </summary> 
        [Description("转出区域")]
        public string OutRegionName { get; set; }

        /// <summary> 
        /// 转出库位编号
        /// </summary> 
        [Description("转出库位编号")]
        public string OutBinLocationCode { get; set; }

        /// <summary> 
        /// 转出库位
        /// </summary> 
        [Description("转出库位")]
        public string OutBinLocationName { get; set; }

        /// <summary> 
        /// 转入仓库编号
        /// </summary> 
        [Description("转入仓库编码")]
        public string InWhsCode { get; set; }

        /// <summary> 
        /// 转入仓库
        /// </summary> 
        [Description("转入仓库")]
        public string InWhsName { get; set; }

        /// <summary> 
        /// 转入区域编号
        /// </summary> 
        [Description("转入区域编号")]
        public string InRegionCode { get; set; }

        /// <summary> 
        /// 转入区域
        /// </summary> 
        [Description("转入区域")]
        public string InRegionName { get; set; }

        /// <summary> 
        /// 转入库位编号
        /// </summary> 
        [Description("转入库位编号")]
        public string InBinLocationCode { get; set; }

        /// <summary> 
        /// 转入库位
        /// </summary> 
        [Description("转入库位")]
        public string InBinLocationName { get; set; }

        /// <summary>
        /// 移动类型
        /// </summary>
        [Description("移动类型")]
        public string MovementType { get; set; }

        /// <summary>
        /// 移动类型名称
        /// </summary>
        [Description("移动类型名称")]
        public string MovementTypeName { get; set; }

        /// <summary> 
        /// 成本中心
        /// </summary> 
        [Description("成本中心")]
        public string CostCenter { get; set; }

        /// <summary> 
        /// 成本中心名称
        /// </summary> 
        [Description("成本中心名称")]
        public string CostCenterName { get; set; }
        /// <summary> 
        /// 特殊库存
        /// </summary> 
        [Description("特殊库存")]
        public string SpecialStock { get; set; }

        /// <summary> 
        /// 销售订单
        /// </summary> 
        [Description("销售订单")]
        public string SaleNum { get; set; }

        /// <summary> 
        /// 销售订单项目
        /// </summary> 
        [Description("销售订单项目")]
        public int? SaleLine { get; set; }

        /// <summary> 
        /// 评估类型
        /// </summary> 
        [Description("评估类型")]
        public string AssessType { get; set; }

        /// <summary> 
        /// 是否过账 1:已过帐 0：未过账
        /// </summary> 
        [Description("是否过账")]
        public bool? IsPosted { get; set; }

        /// <summary> 
        /// 过账人
        /// </summary> 
        [Description("过账人")]
        public string PostUser { get; set; }

        /// <summary> 
        /// 过账时间
        /// </summary> 
        [Description("过账时间")]
        public DateTime? PostTime { get; set; }

        /// <summary> 
        /// sap生成单号
        /// </summary> 
        [Description("sap生成单号")]
        public string SapDocNum { get; set; }

        /// <summary> 
        /// sap生成行号
        /// </summary> 
        [Description("sap生成行号")]
        public int? SapLine { get; set; }



        /// <summary> 
        /// 是否过账 1:已过帐 0：未过账
        /// </summary> 
        [Description("是否过账")]
        public bool? IsPosted2 { get; set; }

        /// <summary> 
        /// 过账人
        /// </summary> 
        [Description("过账人")]
        public string PostUser2 { get; set; }

        /// <summary> 
        /// 过账时间
        /// </summary> 
        [Description("过账时间")]
        public DateTime? PostTime2 { get; set; }

        /// <summary> 
        /// sap生成单号
        /// </summary> 
        [Description("sap生成单号")]
        public string SapDocNum2 { get; set; }

        /// <summary> 
        /// sap生成行号
        /// </summary> 
        [Description("sap生成行号")]
        public int? SapLine2 { get; set; }
    }
}
