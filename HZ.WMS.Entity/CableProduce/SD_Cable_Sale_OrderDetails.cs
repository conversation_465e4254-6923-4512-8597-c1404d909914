using System;
using System.ComponentModel;
using HZ.WMS.Entity;
using SqlSugar;

namespace AOS.OMS.Entity.Sale
{
    /// <summary>
    /// 订单类
    /// </summary>
    public class SD_Cable_Sale_OrderDetails : BaseEntity
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Description("主键ID")]
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }
        
        /// <summary>
        /// 父主键ID
        /// </summary>
        [Description("父主键ID")]
        public string Pid { get; set; }

        /// <summary>
        /// 订单编号
        /// </summary>
        [Description("订单编号")]
        public string OrderNum { get; set; }

        /// <summary>
        /// 订单行号
        /// </summary>
        [Description("订单行号")]
        public int? LineNum { get; set; }

        /// <summary>
        /// 订单状态
        /// </summary>
        [Description("订单状态")]
        public string Status { get; set; }

        /// <summary>
        /// 订单类型(1:主机订单 2:部件订单 3:售后部件 4:售后主机)
        /// </summary>
        [Description("订单类型")]
        public int OrderType { get; set; }

        /// <summary>
        /// 过账用户
        /// </summary>
        [Description("过账用户")]
        public string PostUser { get; set; }

        /// <summary>
        /// 过账日期
        /// </summary>
        [Description("过账日期")]
        public DateTime? PostDate { get; set; }

        /// <summary>
        /// 同步SAP销售单号
        /// </summary>
        [Description("同步SAP销售单号")]
        public string SapNo { get; set; }
        /// <summary>
        /// 同步SAP销售单号
        /// </summary>
        [Description("同步SAP销售行号")]
        public int SapLine { get; set; }

        /// <summary>
        /// 物料编码
        /// </summary>
        [Description("物料编码")]
        public string MaterialCode { get; set; }

        /// <summary>
        /// 客户件号
        /// </summary>
        [Description("客户件号")]
        public string CustomerPartNo { get; set; }

        /// <summary>
        /// 规格型号
        /// </summary>
        [Description("规格型号")]
        public string SpecificationModel { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量")]
        public decimal Quantity { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [Description("单位")]
        public string Unit { get; set; }

        /// <summary>
        /// 单根数量
        /// </summary>
        [Description("单根数量")]
        public string SingleQuantity { get; set; }

        /// <summary>
        /// 四线组
        /// </summary>
        [Description("四线组")]
        public string FourLineGroup { get; set; }

        /// <summary>
        /// 分类
        /// </summary>
        [Description("分类")]
        public string Classify { get; set; }

        /// <summary>
        /// 物料描述
        /// </summary>
        [Description("物料描述")]
        public string MaterialDes { get; set; }

        /// <summary>
        /// 价格
        /// </summary>
        [Description("价格")]
        public Decimal Price { get; set; }

        /// <summary>
        /// 序号
        /// </summary>
        [Description("序号")]
        public string SerialNo { get; set; }
        
        
    }
}
