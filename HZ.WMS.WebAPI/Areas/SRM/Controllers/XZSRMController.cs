using HZ.Core.Http;
using HZ.WMS.Application.SRM;
using HZ.WMS.WebAPI.Controllers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Http;

namespace HZ.WMS.WebAPI.Areas.SRM.Controllers
{
    /// <summary>
    /// SRM
    /// </summary>
    public class XZSRMController : ApiBaseController
    {
        #region 初始化

        private SRMApp _app = new SRMApp();

        #endregion

        #region 供应商主数据

        #region 查询所有供应商主数据

        /// <summary>
        /// 查询所有供应商主数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous] //添加此属性，PostMan可以直接通过接口调试，而不通过前端界面
        public IHttpActionResult GetSRM_SupplierInfo()
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetSRM_SupplierInfo().ToList();
                result.Data = itemsData;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 查询物流供应商主数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous] //添加此属性，PostMan可以直接通过接口调试，而不通过前端界面
        public IHttpActionResult GetSRM_WLSupplierInfo()
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetSRM_SupplierInfo().ToList().Where(X=>X.AccountGroupCode== "ZV04");
                result.Data = itemsData;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #endregion
    }
}