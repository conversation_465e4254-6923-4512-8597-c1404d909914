using System;
using System.Collections.Generic;

namespace HZ.Core.Utilities
{
    public class ThirtyThreeRadixUtil
    {
        
        private static readonly Dictionary<char, int> CharToValue = new Dictionary<char, int>
        {
            { '0', 0 }, { '1', 1 }, { '2', 2 }, { '3', 3 }, { '4', 4 }, { '5', 5 }, { '6', 6 }, { '7', 7 }, { '8', 8 }, { '9', 9 },
            { 'A', 10 }, { 'B', 11 }, { 'C', 12 }, { 'D', 13 }, { 'E', 14 }, { 'F', 15 }, { 'G', 16 }, { 'H', 17 },
            { 'J', 18 }, { 'K', 19 }, { 'L', 20 }, { 'M', 21 }, { 'N', 22 }, { 'P', 23 }, { 'R', 24 }, { 'S', 25 },
            { 'T', 26 }, { 'U', 27 }, { 'V', 28 }, { 'W', 29 }, { 'X', 30 }, { 'Y', 31 }, { 'Z', 32 }
        };

        private static readonly char[] ValueToChar = new char[33];

        static ThirtyThreeRadixUtil()
        {
            foreach (var kvp in CharToValue)
            {
                ValueToChar[kvp.Value] = kvp.Key;
            }
        }

        public static string IncrementCustomBase(string input)
        {
            // 校验输入有效性
            if (input?.Length != 5)
                throw new ArgumentException("Input must be 5 characters");

            var chars = input.ToUpper().ToCharArray();

            // 验证字符合法性
            foreach (char c in chars)
            {
                if (!CharToValue.ContainsKey(c))
                    throw new ArgumentException($"Invalid character: {c}");
            }

            int carry = 1; // 初始加1操作
            for (int i = chars.Length - 1; i >= 0 && carry > 0; i--)
            {
                int currentValue = CharToValue[chars[i]] + carry;

                if (currentValue >= 33) // 33进制进位处理
                {
                    carry = 1;
                    currentValue = 0;
                }
                else
                {
                    carry = 0;
                }

                chars[i] = ValueToChar[currentValue];
            }

            // 处理最高位溢出（如ZZZZZ+1=00000）
            if (carry > 0)
            {
                return new string('0', 5);
            }

            return new string(chars);
        }
        
    }
}