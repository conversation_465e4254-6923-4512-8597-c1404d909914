using System.ComponentModel;

namespace HZ.WMS.Entity.MD.Import
{
    /// <summary>
    /// 打印参数模板导入
    /// </summary>
    public class MD_PrintTemplateParameterImport
    {
        /// <summary>
        /// 模板名称
        /// </summary>
        [Description("模板名称")]
        public string TemplateName { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        [Description("是否启用")]
        public bool Enable { get; set; }

        /// <summary>
        /// 参数json
        /// </summary>
        [Description("参数json")]
        public string ParameterJson { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Description("备注")]
        public string Remark { get; set; }

        /// <summary>
        /// 模板key
        /// </summary>
        [Description("模板key")]
        public string TemplateKey { get; set; }
    }
}
