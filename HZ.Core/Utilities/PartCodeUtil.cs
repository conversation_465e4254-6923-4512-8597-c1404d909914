using System;
using System.Collections.Generic;

namespace HZ.Core.Utilities
{
    public class PartCodeUtil
    {
        
        private static readonly Dictionary<char, int> NumDict = new Dictionary<char, int>
        {
            {'0', 0}, {'1', 1}, {'2', 2}, {'3', 3}, {'4', 4}, {'5', 5},
            {'6', 6}, {'7', 7}, {'8', 8}, {'9', 9}, {'A', 10}, {'B', 11},
            {'C', 12}, {'D', 13}, {'E', 14}, {'F', 15}, {'G', 16}, {'H', 17},
            {'J', 18}, {'K', 19}, {'L', 20}, {'M', 21}, {'N', 22}, {'P', 23},
            {'R', 24}, {'S', 25}, {'T', 26}, {'U', 27}, {'V', 28}, {'W', 29},
            {'X', 30}, {'Y', 31}, {'Z', 32}
        };
        
        public static string GetCode(string impCode, string prefix)
        {
            if (impCode?.Length == 19)
            {
                string impCodeTail = impCode.Substring(4);
                if (CheckCodeValid(impCodeTail))
                {
                    return GenerateResults(impCodeTail, prefix);
                }
            }
            else
            {
                Console.WriteLine("请检查编号长度!");
            }
            return "";
        }

        private static bool CheckCodeValid(string code)
        {
            foreach (char c in code)
            {
                if (!NumDict.ContainsKey(c))
                {
                    Console.WriteLine($"无效代码[{c}],请检查!");
                    return false;
                }
            }
            return true;
        }

        private static string GenerateResults(string tail, string prefix)
        {
            string fullCode = prefix + tail;
            int checkNum = CalculateCheckNum(fullCode);
            return fullCode + checkNum;
        }

        private static int CalculateCheckNum(string code)
        {
            int sumCode = 0;
            for (int i = 0; i < code.Length; i++)
            {
                sumCode += (i + 1) * NumDict[code[i]];
            }
            int num = sumCode % 103;
            return num % 10; // 取最后一位数字
        }
    }
}