using SqlSugar;
using HZ.Core.Http;
using HZ.WMS.Entity.PP;
using HZ.WMS.Entity.PP.Dto;
using HZ.WMS.Entity.PP.ViewModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Application.PP
{
    /// <summary>
    /// 生产过程检验
    /// </summary>
    public class PP_ProcessInspectionApp : BaseApp<PP_ProcessInspection>
    {
        private PP_ProcessInspectionDetailApp detailApp = new PP_ProcessInspectionDetailApp();

        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public PP_ProcessInspectionApp() : base()
        {
        }

        #endregion

        #region 查询分页列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <returns></returns>
        public IEnumerable<PP_ProcessInspection_View> GetPageViewList(Pagination page, string keyword, DateTime fromTime, DateTime toTime, string ProductionScheduler)
        {
            StringBuilder strSql = new StringBuilder();
            strSql.AppendLine(@"select a.*,'' Undone from PP_ProcessInspection a
                                where a.IsDelete = 0 and a.StartTime >= '" + fromTime + "' and a.StartTime < '" + toTime + "' ");
            if (!string.IsNullOrEmpty(keyword))
            {
                strSql.AppendLine($"and a.SerialNo like '%{keyword}%' and a.ProductionOrderNo like '%{keyword}%' and a.MaterialNo like '%{keyword}%' and a.MaterialName like '%{keyword}%' and a.ContractNo like '%{keyword}%' and a.SalesOrderNo like '%{keyword}%' and a.Shippers like '%{keyword}%' ");
            }
            if (!string.IsNullOrEmpty(ProductionScheduler))
            {
                strSql.AppendLine($"and a.ProductionScheduler like '%{ProductionScheduler}%' ");
            }

            var list = DbContext.Ado.SqlQuery<PP_ProcessInspection_View>(strSql.ToString()).ToList();
            if (page == null)
            {
                return list;
            }
            else
            {
                page.Total = list.Count();
                //return list.Skip(page.PageSize * (page.PageNumber - 1)).Take(page.PageSize);
                if (page.PageSize > page.Total)
                {
                    return list.Skip(page.PageSize * (page.PageNumber - 1)).Take(page.Total);
                }
                return list.Skip(page.PageSize * (page.PageNumber - 1)).Take(page.PageSize);
            }
        }

        #endregion

        #region 增加

        public bool Add(PP_ProcessInspection_Dto entity, string user)
        {
            var mark = false;
            try
            {
                var pack = new PP_ProcessInspection
                {
                    ScanningCode = entity.ScanningCode,
                    SerialNo = entity.SerialNo,
                    ProductionOrderNo = entity.ProductionOrderNo,
                    FactoryCode = entity.FactoryCode,
                    MaterialNo = entity.MaterialNo,
                    MaterialName = entity.MaterialName,
                    OrderType = entity.OrderType,
                    OrderQty = entity.OrderQty,
                    Unit = entity.Unit,
                    StartTime = entity.StartTime,
                    ContractNo = entity.ContractNo,
                    SalesOrderNo = entity.SalesOrderNo,
                    Shippers = entity.Shippers,
                    ProductionScheduler = entity.ProductionScheduler,
                    DeliveryTime = entity.DeliveryTime,
                    TestType = entity.TestType,
                    RelevantOrderNo = entity.RelevantOrderNo,
                    Remark = entity.Remark,
                    CUser = user,
                    CTime = DateTime.Now
                };
                var detailList = entity.DetailList;
                detailList.ForEach(item =>
                {
                    item.SerialNo = entity.SerialNo;
                    item.ProductionOrderNo = entity.ProductionOrderNo;
                    item.CUser = user;
                });

                //this.DbContext.Ado.BeginTran();
                this.DbContext.Ado.BeginTran();
                base.Insert(pack);
                detailApp.Insert(detailList);
                this.DbContext.Ado.CommitTran();
                mark = true;
            }
            catch (Exception ex)
            {
                mark = false;
                this.DbContext.Ado.RollbackTran();
                throw ex;
            }
            return mark;
        }

        #endregion

        #region 编辑

        public bool Edit(PP_ProcessInspection_Dto entity, string user)
        {
            bool result = false;
            try
            {
                var pack = new PP_ProcessInspection
                {
                    ID = entity.ID,
                    ScanningCode = entity.ScanningCode,
                    SerialNo = entity.SerialNo,
                    ProductionOrderNo = entity.ProductionOrderNo,
                    FactoryCode = entity.FactoryCode,
                    MaterialNo = entity.MaterialNo,
                    MaterialName = entity.MaterialName,
                    OrderType = entity.OrderType,
                    OrderQty = entity.OrderQty,
                    Unit = entity.Unit,
                    StartTime = entity.StartTime,
                    ContractNo = entity.ContractNo,
                    SalesOrderNo = entity.SalesOrderNo,
                    Shippers = entity.Shippers,
                    ProductionScheduler = entity.ProductionScheduler,
                    DeliveryTime = entity.DeliveryTime,
                    TestType = entity.TestType,
                    RelevantOrderNo = entity.RelevantOrderNo,
                    Remark = entity.Remark,
                    CUser = entity.CUser,
                    CTime = entity.CTime,
                    MUser = user,
                };

                var addList = new List<PP_ProcessInspectionDetail>();
                var updateList = new List<PP_ProcessInspectionDetail>();
                foreach (var item in entity.DetailList)
                {
                    item.SerialNo = entity.SerialNo;
                    item.ProductionOrderNo = entity.ProductionOrderNo;
                    if (string.IsNullOrEmpty(item.ID))
                    {
                        item.CUser = user;
                        addList.Add(item);
                    }
                    else
                    {
                        item.MUser = user;
                        updateList.Add(item);
                    }
                }
                var deleteList = detailApp.GetListByKeys(entity.DelDetailIds);
                if (deleteList != null && deleteList.Count > 0)
                {
                    deleteList.ForEach(item =>
                    {
                        item.DUser = user;
                        item.DTime = DateTime.Now;
                        item.IsDelete = true;
                    });
                    updateList.AddRange(deleteList);
                }

                //this.DbContext.Ado.BeginTran();
                this.DbContext.Ado.BeginTran();
                base.Update(entity);
                detailApp.Update(updateList);
                detailApp.Insert(addList);
                this.DbContext.Ado.CommitTran();
                result = true;
            }
            catch (Exception ex)
            {
                result = false;
                this.DbContext.Ado.RollbackTran();
                throw ex;
            }
            return result;
        }

        #endregion
    }
}
