using System.ComponentModel;
using HZ.WMS.Entity;
using SqlSugar;

namespace AOS.WMS.Entity.Sys
{
    /// <summary>
    /// 供应商库存
    /// </summary>
    [SugarTable("MM_StockTodo")]
    public class MM_StockTodo : BaseEntity
    {
        
        /// <summary>
        /// ID
        /// </summary>
        [Description("ID")]
        [SugarColumn(IsPrimaryKey = true)]
        public string ID { get; set; }

        /// <summary>
        /// 物料编码
        /// </summary>
        [Description("物料编码")]
        public string ItemCode { get; set; }

        /// <summary>
        /// 物料描述
        /// </summary>
        [Description("物料描述")]
        public string ItemName { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [Description("单位")]
        public string Unit { get; set; }

        /// <summary>
        /// 在库数量
        /// </summary>
        [Description("在库数量")]
        public decimal StoreNo { get; set; }

        /// <summary>
        /// 物料类型
        /// </summary>
        [Description("物料类型")]
        public int? ItemType { get; set; }
        
    }
}