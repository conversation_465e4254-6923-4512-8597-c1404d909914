using System.ComponentModel;

namespace HZ.WMS.Entity.MD.Import
{
    /// <summary>
    /// 报工站点物料导入实体类
    /// </summary>
    public class MD_ReportStationMaterialImport
    {
        /// <summary>
        /// 工作中心名称
        /// </summary>
        [Description("工作中心名称")]
        public string WorkCenterName { get; set; }

        /// <summary>
        /// 工作中心编码
        /// </summary>
        [Description("工作中心编码")]
        public string WorkCenterCode { get; set; }

        /// <summary>
        /// 工序号
        /// </summary>
        [Description("工序号")]
        public int? ProcessNo { get; set; }

        /// <summary>
        /// 工序短文本
        /// </summary>
        [Description("工序短文本")]
        public string ProcessShortText { get; set; }

        /// <summary>
        /// 站点代码
        /// </summary>
        [Description("站点代码")]
        public string StationCode { get; set; }

        /// <summary>
        /// 站点名称
        /// </summary>
        [Description("站点名称")]
        public string StationName { get; set; }

        /// <summary>
        /// 物料编码
        /// </summary>
        [Description("物料编码")]
        public string MaterialCode { get; set; }

        /// <summary>
        /// 物料描述
        /// </summary>
        [Description("物料描述")]
        public string MaterialDesc { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量")]
        public decimal? Quantity { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [Description("单位")]
        public string Unit { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Description("备注")]
        public string Remark { get; set; }
    }
} 