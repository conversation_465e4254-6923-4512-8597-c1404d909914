using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.SAP.View
{
    /// <summary>
    /// 客户主数据
    /// </summary>
    public class SAP_KNA1_View
    {
        /// <summary>
        /// 客户编码
        /// </summary>
        [Description("客户编码")]
        public string CustomerCode { get; set; }

        /// <summary>
        /// 客户名称
        /// </summary>
        [Description("客户名称")]
        public string CustomerName { get; set; }

        /// <summary>
        /// 客户账户组 ZC04:员工客户
        /// </summary>
        [Description("客户账户组")]
        public string KTOKD { get; set; }

        
    }
}
