using SqlSugar;
using HZ.Core.Http;
using HZ.WMS.Entity.PP;
using HZ.WMS.Entity.PP.ViewModel;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Application.PP
{
    /// <summary>
    /// 生产投料明细表
    /// </summary>
    public class PP_ProductionFeedingDetailApp : BaseApp<PP_ProductionFeedingDetail>
    {
        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public PP_ProductionFeedingDetailApp() : base()
        {
        }

        #endregion

        #region 获取SAP中间库数据

        public ISugarQueryable<PP_FeedingDetail_View> GetSapOrderDetailList(Expression<Func<PP_FeedingDetail_View, bool>> condition = null)
        {
            return DbContext.Queryable<PP_FeedingDetail_View>().Where(condition);
        }

        #endregion

        #region 生产退料数据源

        public IEnumerable<PP_ProductionFeedingDetail> GetDetailSource(Pagination page,string keyword, string ComponentCode, DateTime? fromTime, DateTime? toTime)
        {
            var sqlBuilder = new StringBuilder();
            sqlBuilder.AppendLine(@"select z.* from (select a.ProductionOrderNo,a.ReservedNo,a.ComponentLineNo,a.ComponentCode,a.MaterialName,
                        DemandQty = sum(a.DemandQty) -  IsNull(sum(b.DemandQty),0),a.ComponentUnit,a.FactoryCode,a.DeliverLocation,a.SalesOrderNo,a.SalesOrderLineNo,a.AssessmentCategory,
                        a.SpecialInventory,a.IsBackflush
                        from XZ_WMS.dbo.PP_ProductionFeedingDetail a 
                        left join (select a.ProductionOrderNo,a.ReservedNo,a.ComponentLineNo,a.ComponentCode,a.MaterialName,
                                                case sum(a.DemandQty) when null then 0 else sum(a.DemandQty) end DemandQty,a.ComponentUnit,a.FactoryCode,a.DeliverLocation,a.SalesOrderNo,a.SalesOrderLineNo,a.AssessmentCategory,
                                                a.SpecialInventory,a.IsBackflush
                        from XZ_WMS.dbo.PP_ProductionFeedingDetail a 
                        where a.IsDelete = 0 and a.MovementType = '262'
                        group by a.ProductionOrderNo,a.ReservedNo,a.ComponentLineNo,a.ComponentCode,a.MaterialName,a.ComponentUnit,a.FactoryCode,a.DeliverLocation,a.SalesOrderNo,a.SalesOrderLineNo,
                        a.AssessmentCategory,a.SpecialInventory,a.IsBackflush) b on b.ProductionOrderNo = a.ProductionOrderNo and b.ComponentCode = a.ComponentCode and a.ComponentLineNo = b.ComponentLineNo
                        where a.IsDelete = 0 and a.MovementType = '261'
                        group by a.ProductionOrderNo,a.ReservedNo,a.ComponentLineNo,a.ComponentCode,a.MaterialName,a.ComponentUnit,a.FactoryCode,a.DeliverLocation,a.SalesOrderNo,a.SalesOrderLineNo,
                        a.AssessmentCategory,a.SpecialInventory,a.IsBackflush) z
						left join XZ_SAP.dbo.XZ_SAP_AFKO y on y.Status = 0 and y.XLOEK <> 'X' and z.ProductionOrderNo = y.AUFNR
                        where z.DemandQty > 0 and z.DemandQty is not null ");
            if (!string.IsNullOrEmpty(keyword))
            {
                sqlBuilder.AppendLine($"and (z.ComponentCode like '%{keyword}%' or z.ProductionOrderNo like '%{keyword}%' or z.DeliverLocation like '%{keyword}%') ");
            }
            if (!string.IsNullOrEmpty(ComponentCode))
            {
                sqlBuilder.AppendLine($"and (z.ComponentCode like '%{keyword}%' or z.MaterialName like '%{keyword}%') ");
            }
            if (fromTime != null)
            {
                sqlBuilder.AppendLine($"and y.GSTRP >= '{fromTime}' and y.GSTRP < '{toTime}' ");
            }
            var list = DbContext.Ado.SqlQuery<PP_ProductionFeedingDetail>(sqlBuilder.ToString());
            if (page == null)
            {
                return list;
            }
            else
            {
                page.Total = list.Count();
                return list.Skip(page.PageSize * (page.PageNumber - 1)).Take(page.PageSize);
            }
        }

        #endregion
    }
}
