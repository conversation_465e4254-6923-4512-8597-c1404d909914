using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using HZ.Core.Http;
using HZ.WMS.Application.KB;


namespace HZ.WMS.WebAPI.Areas.KB.Controllers
{
    /// <summary>
    /// 库存预警
    /// </summary>
    public class StockWarningController : ApiController
    {
        private StockWarningBoardApp _app = new StockWarningBoardApp();

        /// <summary>
        /// 库存预警看板
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IHttpActionResult GetStockWarningBoardInfo()
        {
            var result = new ResponseData();
            try
            {
                //验证用户名和密码
                result.Data = _app.GetStockWarningBoardInfo();
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
    }
}
