//Chloe框架
using SqlSugar;
using System;
using System.ComponentModel;

namespace HZ.WMS.Entity.PP
{
    /// <summary>
    /// 生产订单主表
    /// </summary>
    [SugarTable("PP_ProductionOrder")]
    public class PP_ProductionOrder : BaseEntity
    {
        /// <summary>
        /// 给Chloe用
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string ID { get; set; } = Guid.NewGuid().ToString();
        /// <summary>
        /// 出厂编号 = 开始日期 + 4位流水号
        /// </summary>
        [Description("出厂编号")]
        public string SerialNo { get; set; }
        /// <summary>
        /// 生产订单
        /// </summary>
        [Description("生产订单")]
        public string ProductionOrderNo { get; set; }
        /// <summary>
        /// 主机生产订单
        /// </summary>
        [Description("主机生产订单")]
        public string HostProductionOrderNo { get; set; }
        /// <summary>
        /// 工厂
        /// </summary>
        [Description("工厂")]
        public string FactoryCode { get; set; }
        /// <summary>
        /// 工作中心
        /// </summary>
        [Description("工作中心")]
        public string ProductionLineCode { get; set; }
        /// <summary>
        /// 线体描述
        /// </summary>
        [Description("装配线")]
        public string ProductionLineDes { get; set; }
        /// <summary>
        /// 生产调度员,101是主机，组件
        /// </summary>
        [Description("生产调度员")]
        public string ProductionScheduler { get; set; }
        /// <summary>
        /// 物料编码
        /// </summary>
        [Description("物料编码")]
        public string MaterialNo { get; set; }
        /// <summary>
        /// 物料描述
        /// </summary>
        [Description("物料描述")]
        public string MaterialName { get; set; }

        /// <summary>
        /// 产品型号
        /// </summary>
        [Description("产品型号")]
        public string ProductModel { get; set; }
        /// <summary>
        /// 订单类型
        /// </summary>
        [Description("订单类型")]
        public string OrderType { get; set; }
        /// <summary>
        /// 订单数量
        /// </summary>
        [Description("订单数量")]
        public decimal OrderQty { get; set; }
        /// <summary>
        /// 单位
        /// </summary>
        [Description("单位")]
        public string Unit { get; set; }
        /// <summary>
        /// 生产批次
        /// </summary>
        [Description("生产批次")]
        public string ProductionBatch { get; set; }
        /// <summary>
        ///  收货库存地点
        /// </summary>
        [Description("收货库存地点")]
        public string ReceivingLocation { get; set; }
        /// <summary>
        ///  订单状态
        /// </summary>
        [Description("订单状态")]
        public string OrderStatus { get; set; }
        /// <summary>
        /// 评估类型，01自制02外购
        /// </summary>
        [Description("评估类型")]
        public string AssessmentType { get; set; }
        /// <summary>
        /// 合同号
        /// </summary>
        [Description("合同号")]
        public string ContractNo { get; set; }
        /// <summary>
        /// 销售订单
        /// </summary>
        [Description("销售订单")]
        public string SalesOrderNo { get; set; }
        /// <summary>
        /// 销售订单行项目
        /// </summary>
        [Description("销售订单行项目")]
        public decimal? SalesOrderLineNo { get; set; }
        /// <summary>
        /// 交货时间
        /// </summary>
        [Description("交货时间")]
        public DateTime? DeliveryTime { get; set; }
        /// <summary>
        /// 通知状态,1:已通知,0:未通知
        /// </summary>
        [Description("通知状态")]
        public int? IsNoticed { get; set; } = 0;
        /// <summary>
        /// 开始日期(装配日期)
        /// </summary>
        [Description("开始日期")]
        public DateTime? StartTime { get; set; }
        /// <summary>
        /// 完成日期
        /// </summary>
        [Description("完成日期")]
        public DateTime? EndTime { get; set; }
        /// <summary>
        /// 流水号
        /// </summary>
        [Description("流水号")]
        public int? Serial { get; set; }
        /// <summary>
        /// 部件代码
        /// </summary>
        [Description("部件代码")]
        public string PartCode { get; set; }
        /// <summary>
        /// 上行超速保护代码
        /// </summary>
        [Description("上行超速保护代码")]
        public string UplinkCode { get; set; }
        /// <summary>
        /// 意外移动保护代码
        /// </summary>
        [Description("意外移动保护代码")]
        public string DownCode { get; set; }
        /// <summary>
        /// 客户代码
        /// </summary>
        [Description("客户代码")]
        public string Customer { get; set; }
        /// <summary>
        /// 发货单位
        /// </summary>
        [Description("发货单位")]
        public string Shippers { get; set; }



        ///// <summary>
        ///// 客户订单号
        ///// </summary>
        //[Description("客户订单号")]
        //public string OrderNo { get; set; }

        ///// <summary>
        ///// 非标接收编号
        ///// </summary>
        //[Description("非标接收编号")]
        //public string ReceiptNo { get; set; }
    }
}

