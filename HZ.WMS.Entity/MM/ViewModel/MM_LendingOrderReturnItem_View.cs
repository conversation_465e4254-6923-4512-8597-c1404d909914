using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.MM.ViewModel
{
    /// <summary>
    /// 归还单物料视图
    /// </summary>
    public class MM_LendingOrderReturnItem_View 
    {

        /// <summary>
        /// 借出单id
        /// </summary>
        [Description("借出单id")]
        public string LendingOrderId { get; set; }

        /// <summary>
        /// 借出单号
        /// </summary>
        [Description("借出单号")]
        public string LendingOrderNum { get; set; }

        /// <summary>
        /// 物料编码
        /// </summary>
        [Description("物料编码")]
        public string ItemCode { get; set; }

        /// <summary>
        /// 物料描述
        /// </summary>
        [Description("物料描述")]
        public string ItemName { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量")]
        public decimal? Qty { get; set; }

        /// <summary> 
        /// 单位
        /// </summary> 
        [Description("单位")]
        public string Unit { get; set; }

        /// <summary> 
        /// 备注
        /// </summary> 
        [Description("备注")]
        public string Remark { get; set; }
    }
    
}
