using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.SAP
{
    /// <summary>
    ///SAP中间库-销售订单
    /// </summary>
    [SugarTable("XZ_SAP_VBAK")]
    public class XZ_SAP_VBAK
    {
        /// <summary>
        /// 销售凭证
        /// </summary>
        [Description("销售凭证")]
        public string VBELN { get; set; }

        /// <summary>
        /// 创建日期
        /// </summary>
        [Description("创建日期")]
        public DateTime? ERDAT { get; set; }

        /// <summary>
        /// 时间
        /// </summary>
        [Description("时间")]
        public string ERZET { get; set; }

        /// <summary>
        /// 凭证日期
        /// </summary>
        [Description("凭证日期")]
        public DateTime? AUDAT { get; set; }

        /// <summary>
        /// 销售凭证类型
        /// </summary>
        [Description("销售凭证类型")]
        public string AUART { get; set; }

        /// <summary>
        /// 销售组织
        /// </summary>
        [Description("销售组织")]
        public string VKORG { get; set; }

        /// <summary>
        /// 分销渠道
        /// </summary>
        [Description("分销渠道")]
        public string VTWEG { get; set; }

        /// <summary>
        /// 产品组
        /// </summary>
        [Description("产品组")]
        public string SPART { get; set; }

        /// <summary>
        /// 客户参考
        /// </summary>
        [Description("客户参考")]
        public string BSTNK { get; set; }

        /// <summary>
        /// 售达方
        /// </summary>
        [Description("售达方")]
        public string KUNNR { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        [Description("状态")]
        public bool? Status { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [Description("创建时间")]
        public DateTime CDate { get; set; }

    }
}
