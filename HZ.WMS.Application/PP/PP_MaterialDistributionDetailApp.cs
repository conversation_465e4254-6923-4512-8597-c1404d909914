using SqlSugar;
using HZ.Core.Http;
using HZ.WMS.Entity.PP;
using HZ.WMS.Entity.PP.ViewModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Application.PP
{
    /// <summary>
    /// 生产物料配送单明细
    /// </summary>
    public class PP_MaterialDistributionDetailApp : BaseApp<PP_MaterialDistributionDetail>
    {
        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public PP_MaterialDistributionDetailApp() : base()
        {
        }

        #endregion

        #region 获取SAP配送单

        public IEnumerable<PP_DistributionDetail_View> GetSapMaterialDistributionPageList(Pagination page,Expression<Func<PP_DistributionDetail_View, bool>> condition = null)
        {
            //var query = new List<PP_DistributionDetail_View>();
            //查询SAP所有数据
            var query = DbContext.Queryable<PP_DistributionDetail_View>().Where(condition).ToList();
            ////减掉本地已配送数据
            //sapList.ForEach(item =>
            //{
            //    var sum = this.GetList(w => w.ComponentCode == item.ComponentCode && w.ProductionLineCode == item.ProductionLineCode && w.StartTime == item.StartTime).Sum(s => s.DemandQty);
            //    if(sum < item.DemandQty)
            //    {
            //        item.DemandQty = item.DemandQty - sum;
            //        query.Add(item);
            //    }
            //});

            if(page == null)
            {
                return query;
            }
            else
            {
                int total = query.Count();
                page.Total = total;
                if (page.PageSize > page.Total)
                {
                    return query.Skip(page.PageSize * (page.PageNumber - 1)).Take(page.Total);
                }
                return query.Skip(page.PageSize * (page.PageNumber - 1)).Take(page.PageSize);
            }
        }

        #endregion
    }
}
