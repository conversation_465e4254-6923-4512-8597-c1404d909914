using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.SD.ViewModel
{
    /// <summary>
    /// 托运单视图
    /// </summary>
    public class SD_ConsignmentNote_View
    {
        /// <summary> 
        /// wms单号
        /// </summary> 
        [Description("wms单号")]
        public string DocNum { get; set; }

        /// <summary> 
        /// 发货日期
        /// </summary> 
        [Description("发货日期")]
        public DateTime? DeliveryDate { get; set; }

        /// <summary> 
        /// 发运计划单号
        /// </summary> 
        [Description("发运计划单号")]
        public string ShippingPlanNum { get; set; }

        /// <summary> 
        /// 状态
        /// </summary> 
        [Description("状态")]
        public int? Status { get; set; }

        /// <summary> 
        /// 客户ID
        /// </summary> 
        [Description("客户ID")]
        public string CustomerID { get; set; }

        /// <summary> 
        /// 客户编号
        /// </summary> 
        [Description("客户编号")]
        public string CustomerCode { get; set; }

        /// <summary> 
        /// 客户名称
        /// </summary> 
        [Description("客户")]
        public string CustomerName { get; set; }

        /// <summary> 
        /// 客户地址
        /// </summary> 
        [Description("客户地址")]
        public string CustomerAdd { get; set; }

        /// <summary> 
        /// 结算地址
        /// </summary> 
        [Description("结算地址")]
        public string CustomerRegion { get; set; }

        /// <summary> 
        /// 物流供应商编号
        /// </summary> 
        [Description("物流供应商编号")]
        public string LogisticsSupplierCode { get; set; }

        /// <summary> 
        /// 物流供应商名称
        /// </summary> 
        [Description("物流供应商")]
        public string LogisticsSupplierName { get; set; }

        /// <summary>
        /// 托运部门
        /// </summary>
        [Description("托运部门")]
        public string ShippingDepar { get; set; }

        /// <summary> 
        /// 运输方式
        /// </summary> 
        [Description("运输方式")]
        public string ShippingType { get; set; }

        /// <summary> 
        /// 托运人
        /// </summary> 
        [Description("托运人")]
        public string Shipper { get; set; }

        /// <summary> 
        /// 车牌
        /// </summary> 
        [Description("车牌")]
        public string CarNum { get; set; }

        /// <summary> 
        /// 特殊费用
        /// </summary> 
        [Description("特殊费用")]
        public decimal? SpecialExpenses { get; set; }

        /// <summary> 
        /// 总理论费用
        /// </summary> 
        [Description("总金额")]
        public decimal? TotalTheoreticalAmount { get; set; }

        /// <summary> 
        /// 总重量
        /// </summary> 
        [Description("总重量")]
        public decimal? TotalWeight { get; set; }

        /// <summary> 
        /// 类型, 1:自动生成，2.手动生成
        /// </summary> 
        [Description("类型")]
        public int? Type { get; set; }

        /// <summary> 
        /// wms行号
        /// </summary> 
        [Description("wms行号")]
        public int? Line { get; set; }

        /// <summary> 
        /// 销售订单编号
        /// </summary> 
        [Description("销售订单编号")]
        public string BaseEntry { get; set; }

        /// <summary> 
        /// 销售订单号
        /// </summary> 
        [Description("销售订单号")]
        public string BaseNum { get; set; }

        /// <summary> 
        /// 销售单行号
        /// </summary> 
        [Description("销售单行号")]
        public int? BaseLine { get; set; }

        /// <summary> 
        /// 销售订单类型
        /// </summary> 
        [Description("销售订单类型")]
        public string BaseType { get; set; }

        /// <summary> 
        /// 批次
        /// </summary> 
        [Description("批次")]
        public string BatchNum { get; set; }

        /// <summary> 
        /// 合同号
        /// </summary> 
        [Description("合同号")]
        public string ContractNo { get; set; }

        /// <summary> 
        /// 物料编号
        /// </summary> 
        [Description("物料编号")]
        public string ItemCode { get; set; }

        /// <summary> 
        /// 物料名称
        /// </summary> 
        [Description("物料名称")]
        public string ItemName { get; set; }

        /// <summary> 
        /// 物料组编号
        /// </summary> 
        [Description("物料组")]
        public string ItmsGrpCode { get; set; }

        /// <summary> 
        /// 物料组名称
        /// </summary> 
        [Description("物料组名称")]
        public string ItmsGrpName { get; set; }

        /// <summary> 
        /// 托运单数量
        /// </summary> 
        [Description("数量")]
        public decimal? Qty { get; set; }

        /// <summary> 
        /// 库存单位
        /// </summary> 
        [Description("库存单位")]
        public string Unit { get; set; }

        /// <summary> 
        /// 发货时间
        /// </summary> 
        [Description("发货时间")]
        public DateTime? DeliverDate { get; set; }

        /// <summary> 
        /// 联系人
        /// </summary> 
        [Description("联系人")]
        public string Contact { get; set; }

        /// <summary> 
        /// 联系方式
        /// </summary> 
        [Description("联系方式")]
        public string Telephone { get; set; }

        /// <summary> 
        /// 里程
        /// </summary> 
        [Description("里程")]
        public int? Mileage { get; set; }

        /// <summary> 
        /// 里程费率
        /// </summary> 
        [Description("费率")]
        public decimal? MileageRate { get; set; }

        /// <summary> 
        /// 重量
        /// </summary> 
        [Description("重量")]
        public decimal? Weight { get; set; }

        /// <summary> 
        /// 重量费率
        /// </summary> 
        [Description("重量费率")]
        public decimal? WeightRate { get; set; }

        /// <summary> 
        /// 重量单位
        /// </summary> 
        [Description("重量单位")]
        public string WeightUnit { get; set; }

        /// <summary> 
        /// 行理论费用
        /// </summary> 
        [Description("行金额")]
        public decimal? RowTheoreticalAmount { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Description("备注")]
        public string Remark { get; set; }

        /// <summary>
        /// 插入记录的用户Id
        /// </summary>
        [Description("创建用户")]
        public string CUser { get; set; }
        /// <summary>
        /// 插入记录的服务器时间
        /// </summary>
        [Description("创建时间")]
        public DateTime? CTime { get; set; } = DateTime.Now;

    }
}
