using SqlSugar;
using HZ.WMS.Entity.MM;
using HZ.WMS.Entity.PP;
using HZ.WMS.Entity.PP.ViewModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Application.PP
{
    /// <summary>
    /// 生产物料配送单
    /// </summary>
    public class PP_DistributionDetailViewApp : BaseApp<PP_DistributionDetail_View>
    {
        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public PP_DistributionDetailViewApp() : base(){ }


        #endregion


    }
}
