using HZ.Core.Http;
using HZ.Core.Office;
using HZ.WMS.Application;
using HZ.WMS.Application.MD;
using HZ.WMS.Application.MM;
using HZ.WMS.Application.Sys;
using HZ.WMS.Entity.MM;
using HZ.WMS.Entity.MM.Parameters;
using HZ.WMS.Entity.MM.ViewModel;
using HZ.WMS.Entity.Sys;
using HZ.WMS.WebAPI.Controllers;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Web;
using System.Web.Http;

namespace HZ.WMS.WebAPI.Areas.MM.Controllers
{
    /// <summary>
    /// 借出单
    /// </summary>
    public class MM_LendingOrderController : ApiBaseController
    {
        private MM_LendingOrderApp _app = new MM_LendingOrderApp();
        private MM_LendingOrderDetailApp _detailApp = new MM_LendingOrderDetailApp();
        private MD_StockApp _stockApp = new MD_StockApp();
        private Sys_DictionaryApp _dictionaryapp = new Sys_DictionaryApp();

        #region 获取系统生成的单号

        /// <summary>
        /// 获取系统生成的单号
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetDocNum()
        {
            var result = new ResponseData();
            try
            {
                result.Data = base.GenerateDocNum(DocType.MM, DocFixedNumDef.MM_LendingOrder);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 查询分页列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="page">分页</param>
        /// <param name="keyword">参数</param>
        /// <param name="dateValue">日期区间</param>
        /// <param name="isPosted">是否过账 0：未过账 1：已过帐</param>
        /// <param name="LendingType">借出单类型 0：手动创建 1：自动创建</param>
        /// <param name="Status">状态 0:未审核 1：审核中 2：已审核 3：已取消</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]string keyword, [FromUri] DateTime[] dateValue, 
            [FromUri] bool? isPosted,[FromUri] string LendingType,[FromUri] string Status, [FromUri] string ItemCode, 
            [FromUri] string Rqty)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormatTodayDate(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];

                if (string.IsNullOrEmpty(ItemCode)&& string.IsNullOrEmpty(Rqty))
                {

                    var itemsData = _app.GetPageList(page, t => (string.IsNullOrEmpty(keyword)
                            || t.DocNum.Contains(keyword) || t.LendingType.Contains(keyword)
                            || t.HandlenCode.Contains(keyword) || t.HandlenName.Contains(keyword)
                            || t.PartnersNum.Contains(keyword) || t.PartnersName.Contains(keyword)
                            || t.AuditUser.Contains(keyword) || t.RejectUser.Contains(keyword)
                            || t.CUser.Contains(keyword) || t.PostUser.Contains(keyword))
                            && (t.CTime >= fromTime && t.CTime <= toTime)
                            && (isPosted == null || t.IsPosted == isPosted)
                            && (LendingType == null || t.LendingType == LendingType)
                            && (Status == null || t.Status == Status)
                            ).ToList();
                    result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                    result.Code = (int)WMSStatusCode.Success;
                }
                else
                {
                    List<MM_LendingOrderDetail> itemsData = new List<MM_LendingOrderDetail>();
                    if (string.IsNullOrEmpty(Rqty))
                    {
                        itemsData = _detailApp.GetList(t => (string.IsNullOrEmpty(ItemCode) || t.ItemCode.Contains(ItemCode))
                               && (t.CTime >= fromTime && t.CTime <= toTime)
                               && (isPosted == null || t.IsPosted == isPosted)).ToList();
                    }
                    else if (Rqty == "未归还")
                    {
                        itemsData = _detailApp.GetList(t => (string.IsNullOrEmpty(ItemCode) || t.ItemCode.Contains(ItemCode))
                               && (t.CTime >= fromTime && t.CTime <= toTime)
                               && (t.ReturnQty == null ||t.ReturnQty==0)
                               && (isPosted == null || t.IsPosted == isPosted)).ToList();
                    }
                    else if (Rqty == "已归还")
                    {
                        itemsData = _detailApp.GetList(t => (string.IsNullOrEmpty(ItemCode) || t.ItemCode.Contains(ItemCode))
                              && (t.CTime >= fromTime && t.CTime <= toTime)
                              && t.ReturnQty >0
                              && (isPosted == null || t.IsPosted == isPosted)).ToList();
                    }


                    var DocNums = itemsData.Select(x => x.DocNum).Distinct().ToArray();
                    //string strCodes = string.Join(",", DocNums);

                    var itemsData1 = _app.GetPageList(page, t => DocNums.Contains(t.DocNum)
                            && (string.IsNullOrEmpty(keyword)
                            || t.DocNum.Contains(keyword) || t.LendingType.Contains(keyword)
                            || t.HandlenCode.Contains(keyword) || t.HandlenName.Contains(keyword)
                            || t.PartnersNum.Contains(keyword) || t.PartnersName.Contains(keyword)
                            || t.AuditUser.Contains(keyword) || t.RejectUser.Contains(keyword)
                            || t.CUser.Contains(keyword) || t.PostUser.Contains(keyword))
                            && (t.CTime >= fromTime && t.CTime <= toTime)
                            && (isPosted == null || t.IsPosted == isPosted)
                            && (LendingType == null || t.LendingType == LendingType)
                            && (Status == null || t.Status == Status)).ToList();

                    result.Data = new ResponsePageData { total = page.Total, items = itemsData1 };
                    result.Code = (int)WMSStatusCode.Success;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 查询明细分页列表

        /// <summary>
        /// 查询明细分页列表
        /// </summary>
        /// <param name="page">分页信息</param>
        /// <param name="DocNum">领料单号</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetDetailPageList([FromUri]Pagination page, [FromUri]string DocNum)
        {
            var result = new ResponseData();
            try
            {
                //var itemsData = _app.GetDepReqDetailedView(page, t =>t.DocNum==DocNum).ToList();
                var itemsData = _detailApp.GetPageList(page, t => t.DocNum == DocNum).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 编辑根据单号查询明细信息

        /// <summary>
        /// 编辑根据单号查询明细信息
        /// </summary>
        /// <param name="DocNum">领料单号</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetList([FromUri]string DocNum)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _detailApp.GetList(t => t.DocNum == DocNum).ToList();
                result.Data = itemsData;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 保存

        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="Parameters">参数类</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Save([FromBody]MM_LendingOrderParameters Parameters)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                Sys_User currentUser = GetCurrentUser();
                bool bSubmit = _app.Save(Parameters, currentUser.LoginAccount, out error_message);
                if (!bSubmit)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }

        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="Parameters">接收参数的类</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Update([FromBody]MM_LendingOrderParameters Parameters)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                Sys_User currentUser = GetCurrentUser();
                bool bSubmit = _app.Update(Parameters, currentUser.LoginAccount, out error_message);
                if (!bSubmit)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="DocNums">领料单号</param>
        /// <returns></returns>
        [HttpDelete]
        public IHttpActionResult Delete([FromBody]List<string> DocNums)
        {
            var result = new ResponseData();
            try
            {
                //获取当前登陆者
                Sys_User currLoginUser = GetCurrentUser();
                string error_message = "";
                bool delete = _app.Delete(DocNums, currLoginUser.LoginAccount, out error_message);
                if (!delete)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                }
                ////删除主表
                //var flag = _app.Delete(d => DocNums.Contains(d.DocNum), currLoginUser.LoginAccount);
                ////删除子表
                //var mark = _detailApp.Delete(d => DocNums.Contains(d.DocNum), currLoginUser.LoginAccount);
                //result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 过账

        /// <summary>
        /// 过账SAP
        /// </summary>
        /// <param name="entities">主信息</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult DoPost([FromBody] List<MM_LendingOrder> entities)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                Sys_User currentUser = GetCurrentUser();
                bool bPost = _app.DoPost(entities, null, currentUser.LoginAccount, out error_message);
                if (!bPost)//过账失败==2000，前台处理过账失败后重新刷新界面信息
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2010;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2000;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }

        #endregion

        #region 导出

        /// <summary>
        /// 导出
        /// </summary>
        /// <param name="keyword">参数</param>
        /// <param name="dateValue">日期区间</param>
        /// <param name="isPosted">是否过账 0：未过账 1：已过帐</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] string keyword, [FromUri] DateTime[] dateValue, [FromUri] bool? isPosted, 
            [FromUri] string LendingType, [FromUri] string Status, [FromUri] string ItemCode,[FromUri] string Rqty)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];

                var itemsData =new  List<MM_LendingOrderExport_View>();
                if (string.IsNullOrEmpty(ItemCode)&& string.IsNullOrEmpty(Rqty))
                {
                    itemsData = _app.GetLendingOrderExportView(t => (string.IsNullOrEmpty(keyword)
                           || t.DocNum.Contains(keyword) || t.LendingType.Contains(keyword)
                            || t.HandlenCode.Contains(keyword) || t.HandlenName.Contains(keyword)
                            || t.PartnersNum.Contains(keyword) || t.PartnersName.Contains(keyword)
                            || t.AuditUser.Contains(keyword) || t.RejectUser.Contains(keyword)
                            || t.CUser.Contains(keyword) || t.PostUser.Contains(keyword))
                            && (t.CTime >= fromTime && t.CTime <= toTime)
                            && (isPosted == null || t.IsPosted == isPosted)
                            && (LendingType == null || t.LendingType == LendingType)
                            && (Status == null || t.Status == Status)
                            ).ToList();
                }
                else
                {
                    List<MM_LendingOrderDetail> itemsData1 = new List<MM_LendingOrderDetail>();

                    if (string.IsNullOrEmpty(Rqty))
                    {
                        itemsData1 = _detailApp.GetList(t => (string.IsNullOrEmpty(ItemCode) || t.ItemCode.Contains(ItemCode))
                               && (t.CTime >= fromTime && t.CTime <= toTime)
                               && (isPosted == null || t.IsPosted == isPosted)).ToList();
                    }
                    else  if (Rqty == "未归还")
                        {
                        itemsData1 = _detailApp.GetList(t => (string.IsNullOrEmpty(ItemCode) || t.ItemCode.Contains(ItemCode))
                               && (t.CTime >= fromTime && t.CTime <= toTime)
                               && (t.ReturnQty == null || t.ReturnQty == 0)
                               && (isPosted == null || t.IsPosted == isPosted)).ToList();
                    }
                    else if (Rqty == "已归还")
                    {
                        itemsData1 = _detailApp.GetList(t => (string.IsNullOrEmpty(ItemCode) || t.ItemCode.Contains(ItemCode))
                              && (t.CTime >= fromTime && t.CTime <= toTime)
                              && (t.ReturnQty > 0)
                              && (isPosted == null || t.IsPosted == isPosted)).ToList();
                    }

                    var DocNums = itemsData1.Select(x => x.DocNum).Distinct().ToArray();
                    //string strCodes = string.Join(",", DocNums);

                    itemsData = _app.GetLendingOrderExportView(t => (string.IsNullOrEmpty(keyword)
                            || t.DocNum.Contains(keyword) || t.LendingType.Contains(keyword)
                            || t.HandlenCode.Contains(keyword) || t.HandlenName.Contains(keyword)
                            || t.PartnersNum.Contains(keyword) || t.PartnersName.Contains(keyword)
                            || t.AuditUser.Contains(keyword) || t.RejectUser.Contains(keyword)
                            || t.CUser.Contains(keyword) || t.PostUser.Contains(keyword))
                           && DocNums.Contains(t.DocNum)
                           && (t.CTime >= fromTime && t.CTime <= toTime)
                           && (isPosted == null || t.IsPosted == isPosted)
                           && (LendingType == null || t.LendingType == LendingType)
                           && (Status == null || t.Status == Status)
                           ).ToList();
                }
                var columns = ExcelService.FetchDefaultColumnList<MM_LendingOrderExport_View>();
                string[] ignoreField = new string[]
                {
                    "LendingType","Status","ManualPostTime","AuditUser","RejectUser",
                    "DetailID","Line","CompanyCode","FactoryCode","MovementType","MovementTypeName",
                    "CostCenter","LedgerType","RegionCode","BinLocationCode","SapDocNum","SapLine","IsDelete","MUser","MTime",
                    "DUser","DTime"
                };
                var ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (var userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                columns.ForEach((column) =>
                {
                    if (column.ColumnName == "IsPosted")
                    {
                        column.Formattor = ExcelExportFormatter.IsOrNoFormatterForBoolean;
                    }
                });

                return ExportToExcelFile(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #region 打印

        /// <summary>
        /// 打印
        /// </summary>
        /// <param name="docNums">单号数组</param>
        /// <param name="TradingPartners">交易对象 1:人员 2：客户 3：供应商</param>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IHttpActionResult Print([FromUri]string[] docNums)
        {
            var result = new ResponseData();
            string templateCode = "借出单.repx";
            if (docNums == null || docNums.Length == 0 || string.IsNullOrEmpty(templateCode))
            {
                result.Code = (int)WMSStatusCode.Failed;
            }
            else
            {
                DevExpress.XtraReports.UI.XtraReport report =
                    DevExpress.XtraReports.UI.XtraReport.FromFile(HttpContext.Current.Server.MapPath("~/") + ConfigurationManager.AppSettings["LabelTemplateUploadPath"]?.ToString() + templateCode, true);
                DevExpress.DataAccess.Sql.SqlDataSource dataSource = report.DataSource as DevExpress.DataAccess.Sql.SqlDataSource;
                DevExpress.DataAccess.Sql.QueryParameter parameter = new DevExpress.DataAccess.Sql.QueryParameter()
                {
                    Name = "docNums",
                    Type = typeof(List<string>),
                    Value = docNums.ToList()
                };

                dataSource.Queries[0].Parameters[0] = parameter;
                return base.PrintToPDF(report);
            }
            return Json(result);
        }

        #endregion

        #region 审核

        /// <summary>
        /// 审核
        /// </summary>
        /// <param name="DocNums">根据单号进行审核</param>
        /// <returns></returns>
        [HttpGet]
        //[AllowAnonymous]
        public IHttpActionResult Audit([FromUri]string[] DocNums)
        {
            //[FromBody]string[] ids
            var result = new ResponseData();
            try
            {
                string error_message = "";
                string type = ""; //类型 1:提交失败 2：过账失败
                bool bAudits = _app.Audits(DocNums, GetCurrentUser().LoginAccount, out error_message, out type);
                if (!bAudits && type == "1")
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
                else if (!bAudits && type == "2")
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2010;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2000;
                }

            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 取消

        /// <summary>
        /// 取消
        /// </summary>
        /// <param name="DocNums">根据单号进行取消</param>
        /// <returns></returns>
        [HttpGet]
        //[AllowAnonymous]
        public IHttpActionResult Reject([FromUri]string[] DocNums)
        {
            //[FromBody]string[] ids
            var result = new ResponseData();
            try
            {
                string error_message = "";
                bool bRejects = _app.Rejects(DocNums, GetCurrentUser().LoginAccount, out error_message);
                if (!bRejects)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                }

            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 查询仓库库存信息

        /// <summary>
        /// 查询仓库库存信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IHttpActionResult GetStockInfo([FromUri]Pagination page, [FromUri] string keyword, [FromUri] string Whs)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _stockApp.GetPageList(page, t => (string.IsNullOrEmpty(keyword)
                                                  || t.ItemCode.Contains(keyword) || t.ItemName.Contains(keyword)
                                                  || t.Unit.Contains(keyword) || t.WhsCode.Contains(keyword)
                                                  || t.WhsName.Contains(keyword) || t.RegionCode.Contains(keyword)
                                                  || t.RegionName.Contains(keyword) || t.BinLocationCode.Contains(keyword)
                                                  || t.BinLocationName.Contains(keyword) || t.SaleNum.Contains(keyword)
                                                  || t.SpecialStock.Contains(keyword))
                                                  && (string.IsNullOrEmpty(Whs) || t.WhsCode.Contains(Whs) || t.WhsName.Contains(Whs))
                                                  && (t.SpecialStock == null || t.SpecialStock == "")
                                                  && (t.SupplierCode == null || t.SupplierCode == "")
                                                  && (t.SaleNum == null || t.SaleNum == "")
                                                  && t.Qty > 0 ).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 查询借出单-交易对象信息

        /// <summary>
        /// 查询借出单-交易对象信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IHttpActionResult GetDictionaryForLenOrder()
        {
            string keyword = "MM003";
            var result = new ResponseData();
            try
            {
                var itemsData = _dictionaryapp.GetEntity(keyword).OrderBy(x=>x.SortNum);
                result.Data = itemsData;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region OA同步WMS借出单保存

        /// <summary>
        /// OA同步WMS借出单保存
        /// </summary>
        /// <param name="lendList">参数汇总</param>
        /// <returns></returns>
        [HttpPost]
        //[AllowAnonymous]
        public IHttpActionResult LendingSaveForOA([FromBody] List<MM_LendingOrderDetail> lendList)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                Sys_User currentUser = GetCurrentUser();
                bool bSubmit = _app.SaveForOA(lendList, currentUser.LoginAccount, out error_message);
                if (!bSubmit)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion



        /// <summary>
        /// 分页条件查询物料数据
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IHttpActionResult GetItem([FromUri]Pagination page, [FromUri]string keyword, [FromUri]string Whs)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetItem(page, keyword, Whs).ToList();
                //return query.ToPageList(page.PageNumber, page.PageSize);
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }



        
    }
}