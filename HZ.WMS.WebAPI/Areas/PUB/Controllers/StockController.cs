using HZ.Core.Http;
using HZ.WMS.Application.MD;
using HZ.WMS.Application.MM;
using HZ.WMS.Application.Sys;
using HZ.WMS.Entity.MM;
using HZ.WMS.WebAPI.Controllers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;

namespace HZ.WMS.WebAPI.Areas.MM.Controllers
{
    public class StockController : ApiBaseController
    {
        //private MM_InScanApp _InScanApp = new MM_InScanApp();
        //private MM_OutScanApp _OutScanApp = new MM_OutScanApp();
        private MD_StockApp _StockApp = new MD_StockApp();
        private Sys_SwithConfigApp _switchApp = new Sys_SwithConfigApp();

        /// <summary>
        /// 查询库存
        /// </summary>
        /// <param name="materialIDs">物料编号数组,同WMS系统的ItemCode字段,可选</param>
        /// <param name="regionIDs">区域编号数组,同WMS系统的RegionCode字段,可选</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult Query([FromUri]string[] materialIDs = null, [FromUri]string[] regionIDs = null)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _StockApp.GetList(x =>
                     (materialIDs == null || materialIDs.Contains(x.ItemCode))
                     && (regionIDs == null || regionIDs.Contains(x.RegionCode))
                 );
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (System.Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 入库
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult StockIn([FromBody]StockDto data)
        {
            var result = new ResponseData();
            try
            {
                if (data == null || data.Data == null)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "输入参数不能为空";
                }
                else
                {
                    //List<MM_InScan> inScanList = new List<MM_InScan>();
                    string docNum = this.GenerateDocNum(MM_FixedNumDef.DocType, MM_FixedNumDef.MM_InScan);
                    foreach (var stock in data.Data)
                    {
                        //验证条码是否有效
                        var stockTmp = _StockApp.GetFirstEntity(x => x.BarCode == stock.BarCode && x.BinLocationCode == stock.BinLocationCode);
                        if (stockTmp == null)
                        {
                            result.Code = (int)WMSStatusCode.Failed;
                            result.Message = "未能在库位[" + stock.BinLocationCode + "]找到条码[" + stock.BarCode + "]的库存记录";
                            break;
                        }

                        //MM_InScan inScan = new MM_InScan();
                        //inScan.BarCode = stock.BarCode;
                        //inScan.BatchNum = stock.BatchNum;
                        //inScan.CostCenter = stock.BarCode;
                        //inScan.DocNum = docNum;
                        //inScan.InBinLocationCode = stock.BinLocationCode;
                        //inScan.InBinLocationName = stock.BinLocationName;
                        //inScan.InRegionCode = stock.RegionCode;
                        //inScan.InRegionName = stock.RegionName;
                        //inScan.InWhsCode = "SZ1";
                        //inScan.InWhsName = "SZ1";
                        //inScan.IsCheck = 1;
                        //inScan.IsPosted = false;
                        //inScan.ItemCode = stock.ItemCode;
                        //inScan.ItemName = stock.ItemName;
                        //inScan.ItmsGrpCode = stock.ItmsGrpCode;
                        //inScan.ItmsGrpName = stock.ItmsGrpName;
                        //inScan.PostTime = DateTime.Now;
                        //inScan.PostUser = null;
                        //if (stock.PTime.HasValue)
                        //{
                        //    inScan.PTime = stock.PTime.Value;
                        //}
                        //inScan.Qty = stock.Qty;
                        //inScan.RTypeCode = null;
                        //inScan.RTypeName = null;
                        //inScan.Subject = null;
                        //inScan.Unit = stock.Unit;
                        //inScan.CUser = "admin";
                        //inScan.CTime = DateTime.Now;
                        //inScan.Remark = "WMS库存收货接口";
                        //inScanList.Add(inScan);
                    }

                    //int count = _InScanApp.AddList(inScanList, "admin");


                    ////过账
                    //if (_switchApp.IsMMInScanAutoPost)
                    //{
                    //    if (_InScanApp.PostToSAP(inScanList, "admin"))
                    //    {
                    //        result.Code = (int)WMSStatusCode.Success;
                    //        result.Message = "入库成功,已自动过帐到SAP";
                    //    }
                    //    else
                    //    {
                    //        result.Message = "入库成功,未成功过帐到SAP";
                    //    }
                    //}
                    //else
                    //{
                    //    result.Code = (int)WMSStatusCode.Success;
                    //    result.Message = "入库成功,未自动过帐到SAP";
                    //}
                    //if (count > 0)
                    //{
                    //    result.Code = (int)WMSStatusCode.Success;
                    //    result.Message = "入库成功,过帐结果请到SAP或者WMS系统其他收货界面中查看";
                    //}
                    //else
                    //{
                    //    result.Code = (int)WMSStatusCode.Failed;
                    //    result.Message = "入库失败";
                    //}
                }
            }
            catch (System.Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 出库
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult StockOut([FromBody]StockDto data)
        {
            var result = new ResponseData();
            try
            {
                if (data == null || data.Data == null)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "输入参数不能为空";
                }
                else
                {
                    //List<MM_OutScan> outScanList = new List<MM_OutScan>();
                    string docNum = this.GenerateDocNum(MM_FixedNumDef.DocType, MM_FixedNumDef.MM_OutScan);
                    foreach (var stock in data.Data)
                    {
                        //验证条码是否有效
                        var stockTmp = _StockApp.GetFirstEntity(x => x.BarCode == stock.BarCode && x.BinLocationCode == stock.BinLocationCode);
                        if (stockTmp == null)
                        {
                            result.Code = (int)WMSStatusCode.Failed;
                            result.Message = "未能在库位[" + stock.BinLocationCode + "]找到条码[" + stock.BarCode + "]的库存记录";
                            break;
                        }

                        //MM_OutScan outScan = new MM_OutScan();
                        //outScan.BarCode = stock.BarCode;
                        //outScan.BatchNum = stock.BatchNum;
                        //outScan.CostCenter = stock.BarCode;
                        //outScan.DocNum = docNum;
                        //outScan.OutBinLocationCode = stock.BinLocationCode;
                        //outScan.OutBinLocationName = stock.BinLocationName;
                        //outScan.OutRegionCode = stock.RegionCode;
                        //outScan.OutRegionName = stock.RegionName;
                        //outScan.OutWhsCode = "SZ1";
                        //outScan.OutWhsName = "SZ1";
                        //outScan.IsCheck = 1;
                        //outScan.IsPosted = false;
                        //outScan.ItemCode = stock.ItemCode;
                        //outScan.ItemName = stock.ItemName;
                        //outScan.ItmsGrpCode = stock.ItmsGrpCode;
                        //outScan.ItmsGrpName = stock.ItmsGrpName;
                        //outScan.PostTime = DateTime.Now;
                        //outScan.PostUser = null;
                        //if (stock.PTime.HasValue)
                        //{
                        //    outScan.PTime = stock.PTime.Value;
                        //}
                        //outScan.Qty = stock.Qty;
                        //outScan.RTypeCode = "1";
                        //outScan.RTypeName = null;
                        //outScan.Subject = null;
                        //outScan.Unit = stock.Unit;
                        //outScan.CUser = "admin";
                        //outScan.CTime = DateTime.Now;
                        //outScan.Remark = "WMS库存收发货接口";
                        //outScanList.Add(outScan);
                    }

                    //int count = _OutScanApp.AddList(outScanList, "admin");

                    ////过账
                    //if (_switchApp.IsMMOutScanAutoPost)
                    //{
                    //    if (_OutScanApp.PostToSAP(outScanList, "admin"))
                    //    {
                    //        result.Code = (int)WMSStatusCode.Success;
                    //        result.Message = "出库成功,已自动过帐到SAP";
                    //    }
                    //    else
                    //    {
                    //        result.Message = "出库成功,未成功过帐到SAP";
                    //    }
                    //}
                    //else
                    //{
                    //    result.Code = (int)WMSStatusCode.Success;
                    //    result.Message = "出库成功,未自动过帐到SAP";
                    //}

                    //if (count > 0)
                    //{
                    //    result.Code = (int)WMSStatusCode.Success;
                    //    result.Message = "出库成功,过帐结果请到SAP或者WMS系统其他发货界面中查看";
                    //}
                    //else
                    //{
                    //    result.Code = (int)WMSStatusCode.Failed;
                    //    result.Message = "出库失败";
                    //}

                }
            }
            catch (System.Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        public class StockDto
        {
            public List<Stock> Data { get; set; }
        }

        public class Stock
        {
            /// <summary>
            /// 条码
            /// </summary>
            public string BarCode { get; set; }
            /// <summary>
            /// 批次
            /// </summary>
            public string BatchNum { get; set; }
            /// <summary>
            /// 生产日期
            /// </summary>
            public DateTime? PTime { get; set; }
            /// <summary>
            /// 物料编号
            /// </summary>
            public string ItemCode { get; set; }
            /// <summary>
            /// 物料名称
            /// </summary>
            public string ItemName { get; set; }
            /// <summary>
            /// 物料组编号
            /// </summary>
            public string ItmsGrpCode { get; set; }
            /// <summary>
            /// 物料组名称
            /// </summary>
            public string ItmsGrpName { get; set; }
            /// <summary>
            /// 扫描数量
            /// </summary>
            public decimal Qty { get; set; }
            /// <summary>
            /// 库存单位
            /// </summary>
            public string Unit { get; set; }
            /// <summary>
            /// 转入/转出区域编号
            /// </summary>
            public string RegionCode { get; set; }
            /// <summary>
            /// 转入/转出区域名称
            /// </summary>
            public string RegionName { get; set; }
            /// <summary>
            /// 转入/转出库位编号
            /// </summary>
            public string BinLocationCode { get; set; }
            /// <summary>
            /// 转入/转出库位名称
            /// </summary>
            public string BinLocationName { get; set; }
        }
    }
}

