using SqlSugar;
namespace HZ.WMS.Entity.MD
{
    /// <summary>
    /// 产品类别(ItemGroup)
    /// </summary>
    [SugarTable("MD_ProductCategory")]
    public class MD_ProductCategory : BaseEntity
    {
        /// <summary>
        /// ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string ID { get; set; }
        /// <summary>
        /// 产品类别编号
        /// </summary>
        public string ProductCategoryID { get; set; }
        /// <summary>
        /// 产品类别说明
        /// </summary>
        public string ProductCategoryDesc { get; set; }

    }
}