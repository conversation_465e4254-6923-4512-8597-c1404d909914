using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.PP.ViewModel
{
    /// <summary>
    /// 延时报工
    /// </summary>
    public class PP_DelayReport_View
    {
        /// <summary>
        /// 序列号
        /// </summary>
        public string SerialNo { get; set; }
        /// <summary>
        /// 生产订单
        /// </summary>
        public string ProductionOrderNo { get; set; }
        /// <summary>
        /// 主机生产订单
        /// </summary>
        public string HostProductionOrderNo { get; set; }
        /// <summary>
        /// 生产调度员,101是主机，组件
        /// </summary>
        public string ProductionScheduler { get; set; }
        /// <summary>
        /// 订单类型
        /// </summary>
        public string OrderType { get; set; }
        /// <summary>
        /// 物料编码
        /// </summary>
        public string MaterialNo { get; set; }
        /// <summary>
        /// 物料描述
        /// </summary>
        public string MaterialName { get; set; }
        /// <summary>
        /// 工序编号，最后一道工序
        /// </summary>
        public decimal WorkingProcedureCode { get; set; }
        /// <summary>
        /// 工序描述
        /// </summary>
        public string WorkingProcedureDes { get; set; }
        /// <summary>
        /// 订单数量
        /// </summary>
        public decimal OrderQty { get; set; }
        /// <summary>
        /// 单位
        /// </summary>
        public string Unit { get; set; }
        /// <summary>
        /// 评估类型，01自制02外购
        /// </summary>
        public string AssessmentType { get; set; }
        /// <summary>
        /// 发货单位
        /// </summary>
        public string Shippers { get; set; }
        /// <summary>
        ///  收货库存地点
        /// </summary>
        public string ReceivingLocation { get; set; }
        /// <summary>
        /// 开始日期(装配日期)
        /// </summary>
        public DateTime? StartTime { get; set; }
        /// <summary>
        /// 线体编码
        /// </summary>
        public string ProductionLineCode { get; set; }
        /// <summary>
        /// 线体描述
        /// </summary>
        public string ProductionLineDes { get; set; }
        /// <summary>
        /// 物料组
        /// </summary>
        public string MaterialGroupCode { get; set; }
        /// <summary>
        /// 物料组描述
        /// </summary>
        public string MaterialGroupDes { get; set; }
    }

    /// <summary>
    /// 报工标识
    /// </summary>
    public class PP_ReportMark_View
    {
        /// <summary>
        /// 生产订单
        /// </summary>
        public string ProductionOrderNo { get; set; }
        /// <summary>
        /// 生产调度员,101是主机，组件
        /// </summary>
        public string ProductionScheduler { get; set; }
        /// <summary>
        /// 订单类型
        /// </summary>
        public string OrderType { get; set; }
        /// <summary>
        /// 主机生产订单
        /// </summary>
        public string HostProductionOrderNo { get; set; }
        /// <summary>
        /// 工序编号，第一道
        /// </summary>
        public decimal WorkingProcedureCode { get; set; }
        /// <summary>
        /// 线体编码
        /// </summary>
        public string ProductionLineCode { get; set; }
        /// <summary>
        /// 同步/延迟标识
        /// </summary>
        public string Mark { get; set; }
    }
}
