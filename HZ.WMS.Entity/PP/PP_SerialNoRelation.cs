using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.PP
{
    /// <summary>
    /// 生产序列号关联主表
    /// </summary>
    public class PP_SerialNoRelation : BaseEntity
    {
        /// <summary>
        /// 主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string ID { get; set; }

        /// <summary>
        /// 生产投料单号
        /// </summary>
        [Description("生产投料单号")]
        public string DocNum { get; set; }

        /// <summary>
        /// 扫描码
        /// </summary>
        [Description("扫描码")]
        public string ScanningCode { get; set; }
        /// <summary>
        /// 序列号
        /// </summary>
        [Description("序列号")]
        public string SerialNo { get; set; }

        /// <summary>
        /// 生产订单
        /// </summary>
        [Description("生产订单")]
        public string ProductionOrderNo { get; set; }
        /// <summary>
        /// 工厂
        /// </summary>
        [Description("工厂")]
        public string FactoryCode { get; set; }
        /// <summary>
        /// 物料编码
        /// </summary>
        [Description("物料编码")]
        public string MaterialNo { get; set; }
        /// <summary>
        /// 物料描述
        /// </summary>
        [Description("物料描述")]
        public string MaterialName { get; set; }

        /// <summary>
        /// 订单类型
        /// </summary>
        [Description("订单类型")]
        public string OrderType { get; set; }
        /// <summary>
        /// 订单数量
        /// </summary>
        [Description("订单数量")]
        public decimal OrderQty { get; set; }
        /// <summary>
        /// 单位
        /// </summary>
        [Description("单位")]
        public string Unit { get; set; }
        /// <summary>
        /// 开始日期(装配日期)
        /// </summary>
        [Description("装配日期")]
        public DateTime? StartTime { get; set; }
        /// <summary>
        /// 合同号
        /// </summary>
        [Description("合同号")]
        public string ContractNo { get; set; }
        /// <summary>
        /// 销售订单
        /// </summary>
        [Description("客户订单号")]
        public string SalesOrderNo { get; set; }
        /// <summary>
        /// 发货单位
        /// </summary>
        [Description("发货单位")]
        public string Shippers { get; set; }
        /// <summary>
        /// 生产调度员,101是主机，组件
        /// </summary>
        [Description("生产调度员")]
        public string ProductionScheduler { get; set; }
        /// <summary>
        /// 线体编码
        /// </summary>
        [Description("工作中心")]
        public string ProductionLineCode { get; set; }
        /// <summary>
        /// 线体描述
        /// </summary>
        [Description("装配线")]
        public string ProductionLineDes { get; set; }
        /// <summary>
        /// 交货时间
        /// </summary>
        [Description("交货时间")]
        public DateTime? DeliveryTime { get; set; }
      
    }
}
