using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using HZ.Core.Security;
using HZ.WMS.Entity.MD;
using HZ.Core.Http;
using SqlSugar;

namespace HZ.WMS.Application.MD
{
    /// <summary>
    /// 仓库管理
    /// </summary>

    public class MD_WarehouseApp : BaseApp<MD_Warehouse>
    {
        private MD_RegionApp _regionApp = new MD_RegionApp();

		#region 默认构造函数
        
        /// <summary>
        /// 默认构造函数
        /// </summary>
        public MD_WarehouseApp() : base()
        {
        }




        #endregion

        #region 获取默认仓库

        /// <summary>
        /// 默认只有一个仓库
        /// </summary>
        /// <returns></returns>
        public MD_Warehouse GetDefaultWarehouse()
        {
            return GetList().ToList().FirstOrDefault();
        }

        #endregion

        #region 获取仓库下的第一个默认区域

        /// <summary>
        /// 获取仓库下的第一个默认区域
        /// </summary>
        /// <param name="whsCode">仓库编号</param>
        /// <returns></returns>
        public ISugarQueryable<MD_Region> GetDefaultWarehouseRegion(string whsCode)
        {
            return _regionApp.GetList(x => x.WhsCode == whsCode);
        }


        #endregion

        #region 查询仓库下的所有区域

        /// <summary>
        /// 查询仓库下的所有区域
        /// </summary>
        /// <param name="whsCode">仓库编号</param>
        /// <returns></returns>
        public List<MD_Region> GetWarehouseRegion(string whsCode)
        {
            return _regionApp.GetList(x => x.WhsCode == whsCode).ToList();
        }


        #endregion

        #region 查询区域所属仓库

        /// <summary>
        /// 查询区域所属仓库
        /// </summary>
        /// <param name="regionCode"></param>
        /// <returns></returns>
        public MD_Warehouse GetWarehouseByRegionCode(string regionCode)
        {
            MD_Region region = _regionApp.GetFirstEntity(x => x.RegionCode == regionCode);
            return GetFirstEntity(x => x.WhsCode == region.WhsCode);
             
        }


        #endregion

        #region 查询库位所属仓库
        /// <summary>
        /// 
        /// </summary>
        /// <param name="binLocationCode"></param>
        /// <returns></returns>
        public MD_Warehouse GetWarehouseByBinLocationCode(string binLocationCode)
        {
            MD_Region region = _regionApp.GetRegionByBinLocation(binLocationCode);
            return GetWarehouseByRegionCode(region.RegionCode);

        }

        #endregion
    }
}

