using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.MM.ViewModel
{
    /// <summary>
    /// 委外领料申请明细关联委外发料，显示已发料数量
    /// </summary>
    public class MM_PickingApplyDetailForDispatch_View:MM_PickingApplyDetail
    {
        /// <summary> 
        /// 发料数量
        /// </summary> 
        [Description("发料数量")]
        public decimal? DispatchQty { get; set; }
        
    }
}
