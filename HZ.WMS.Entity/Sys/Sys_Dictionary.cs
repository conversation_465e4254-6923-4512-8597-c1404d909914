using System;
using System.ComponentModel;
using SqlSugar;
namespace HZ.WMS.Entity.Sys
{
    /// <summary>
    /// 数据字典
    /// </summary>
    [SugarTable("Sys_Dictionary")]
    public class Sys_Dictionary : BaseEntity
    {
        /// <summary>
        /// DictionaryID
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.String, IsPrimaryKey = true)]

        [Description("DictionaryID")]
        public string DictionaryID { get; set; }
        /// <summary>
        /// 类型编号
        /// </summary>

        [Description("类型编号")]
        public string TypeCode { get; set; }
        /// <summary>
        /// 类型名称
        /// </summary>

        [Description("类型名称")]
        public string TypeDisc { get; set; }
        /// <summary>
        /// 枚举值
        /// </summary>

        [Description("枚举值")]
        public int EnumKey { get; set; }
        /// <summary>
        /// 枚举值描述
        /// </summary>

        [Description("枚举值描述")]
        public string EnumValue { get; set; }
        /// <summary>
        /// 枚举值描述1
        /// </summary>

        [Description("枚举值描述1")]
        public string EnumValue1 { get; set; }
        /// <summary>
        /// 枚举值描述2
        /// </summary>

        [Description("枚举值描述2")]
        public string EnumValue2 { get; set; }
        /// <summary>
        /// 排序
        /// </summary>

        [Description("排序")]
        public int? SortNum { get; set; }
    }
}