using HZ.Core.Http;
using HZ.Core.Office;
using HZ.WMS.Application.MD;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.Sys;
using HZ.WMS.WebAPI.Controllers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;

namespace HZ.WMS.WebAPI.Areas.MD.Controllers
{
    /// <summary>
    /// 生产配送配置
    /// </summary>
    public class MD_ProductionDistributionSettingController : ApiBaseController
    {
        MD_ProductionDistributionSettingApp _app = new MD_ProductionDistributionSettingApp();

        #region 获取数据

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]string keyword, [FromUri]string EmployeeName, [FromUri]string ProductionLineCode, [FromUri]string ItemName)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetPageList(page, t => (string.IsNullOrEmpty(keyword)
                        || t.EmployeeNumber.Contains(keyword)
                        || t.ProductionLineDes.Contains(keyword)
                        || t.MaterialGroupCode.Contains(keyword)
                        || t.MaterialGroupDes.Contains(keyword))
                        &&(string.IsNullOrEmpty(EmployeeName) || t.EmployeeName.Contains(EmployeeName))
                        && (string.IsNullOrEmpty(ProductionLineCode) || t.ProductionLineCode.Contains(ProductionLineCode))
                        && (string.IsNullOrEmpty(ItemName) || t.ItemName.Contains(ItemName))
                        ).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 获取物料组
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetMaterialGroup()
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.GetMaterialGroup();
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 获取线体
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetProductionLine()
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.GetAllLine();
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 保存数据

        /// <summary>
        /// 
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult SaveEntity(MD_ProductionDistributionSetting entity)
        {
            var result = new ResponseData();
            try
            {
                Sys_User user = GetCurrentUser();
                if(string.IsNullOrEmpty(entity.ID))
                {
                    entity.CUser = user.LoginAccount;
                    _app.Insert(entity);
                }
                else
                {
                    entity.MUser = user.LoginAccount;
                    _app.Update(entity);
                }
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除数据

        /// <summary>
        /// 删除数据
        /// </summary>
        /// <param name="keys"></param>
        /// <returns></returns>
        [HttpDelete]
        public IHttpActionResult DeleteEntity(string[] keys)
        {
            var result = new ResponseData();
            try
            {
                Sys_User user = GetCurrentUser();
                _app.DeleteByKeysWithTran(keys, user.LoginAccount);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion


        #region 导入

        /// <summary>
        /// 导入
        /// </summary>
        /// <param name="entitys">接收的导入集合</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult ImportExcelToData([FromBody]List<MD_ProductionDistributionSettingImport> entitys)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                if (_app.ImprotExcelToBaseData(entitys, GetCurrentUser().LoginAccount, out error_message))
                {
                    result.Data = true;
                    result.Code = (int)WMSStatusCode.Success;
                }
                else
                {
                    result.Data = false;
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
            return Json(result);
        }

        #endregion

        #region 导入模板

        /// <summary>
        /// 导入模板
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelModel()
        {
            var result = new ResponseData();
            try
            {
                var columns = ExcelService.FetchDefaultColumnList<MD_ProductionDistributionSetting>();
                string modelName = "配送任务-导入模板";
                //排除不需要的列名称 字符串
                string[] ignoreField = new string[]
                {
                    "ID", "DocNum","Remark","IsDelete", "CUser", "CTime",
                    "MUser", "MTime", "DUser", "DTime"
                };
                var itemsData = new List<MD_ProductionDistributionSetting>() { };
                var ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<MD_ProductionDistributionSetting> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                //需要格式转换的字段
                //columns.ForEach((column) =>
                //{
                //    if (column.ColumnName == "CTime")
                //    {
                //        column.Formattor = ExcelExportFormatter.DateTimeFormatter;
                //    }
                //});
                return ExportToExcelFile<MD_ProductionDistributionSetting>(itemsData, columns, GetCurrentUser().UserName + "_" + modelName);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion
    }
}
