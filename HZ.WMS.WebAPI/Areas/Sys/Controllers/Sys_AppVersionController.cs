using HZ.Core.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using HZ.WMS.Entity.Sys;
using HZ.WMS.Application.Sys;

namespace HZ.WMS.WebAPI.Areas.Sys.Controllers
{
    /// <summary>
    /// 版本管理
    /// </summary>
    public class Sys_AppVersionController : ApiController
    {

        private Sys_AppVersionApp _app = new Sys_AppVersionApp();

        /// <summary>
        /// 获取最新版本 
        /// </summary>
        /// <param name="appid"></param>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IHttpActionResult GetAppLastVersion([FromUri] string appid)
        {
            var result = new ResponseData();
            try
            {
                Sys_AppVersion appVersion = _app.GetEntityByKey(appid);
                result.Data = new { LastVersion=appVersion.CurrentVersion ,UpdateUrl= appVersion.UpdateUrl,ForceUpdate=appVersion.ForceUpdate };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        
    }
}
