using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;
using HZ.Core.Http;
using HZ.Core.Office;
using HZ.WMS.Application.MD;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.MD.Import;
using HZ.WMS.Entity.Sys;
using HZ.WMS.WebAPI.Controllers;

namespace HZ.WMS.WebAPI.Areas.MD.Controllers
{
    /// <summary>
    /// 报工站点
    /// </summary>
    public class MD_ReportStationController : ApiBaseController
    {
        private MD_ReportStationApp _app = new MD_ReportStationApp();

        #region 查询分页列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="page">分页参数</param>
        /// <param name="keyword">关键字</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]string keyword)
        {
            var result = new ResponseData();
            try
            {
                var query = _app.GetList();
                if (!string.IsNullOrEmpty(keyword))
                {
                    query = query.Where(t => t.WorkCenterName.Contains(keyword) || 
                                            t.WorkCenterCode.Contains(keyword) || 
                                            t.ProcessShortText.Contains(keyword) || 
                                            t.StationCode.Contains(keyword) || 
                                            t.StationName.Contains(keyword));
                }
                
                var count = query.Count();
                page.Total = count;
                var data = query.OrderBy(t => t.WorkCenterCode).Skip((page.PageNumber - 1) * page.PageSize).Take(page.PageSize).ToList();
                
                result.Data = new ResponsePageData { total = count, items = data };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 添加

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="entity">实体</param>
        /// <param name="userIds">关联的用户ID列表</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Add( [FromBody] MD_ReportStationAddReq req)
        {
            var result = new ResponseData();
            try
            {
                var entity = req.entity;
                // 验证工作中心站点是否存在
                string error;
                if (!_app.ValidateWorkCenterStationInfo(entity.WorkCenterCode, entity.StationCode, out error))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error;
                    return Json(result);
                }
                
                Sys_User currLoginUser = GetCurrentUser();
                entity.Id = Guid.NewGuid().ToString();
                entity.IsDelete = false;
                entity.CUser = currLoginUser.LoginAccount;
                entity.MUser = currLoginUser.LoginAccount;
                entity.CTime = DateTime.Now;
                entity.MTime = DateTime.Now;
                
                // 保存站点并创建用户关联关系
                result.Data = _app.InsertWithUserRelations(entity, req.userIds, currLoginUser.LoginAccount);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="entity">实体</param>
        /// <param name="userIds">关联的用户ID列表</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Update([FromBody]MD_ReportStationUpdateReq req)
        {
            var result = new ResponseData();
            try
            {
                var entity = req.entity;
                // 验证工作中心站点是否存在
                string error;
                if (!_app.ValidateWorkCenterStationInfo(entity.WorkCenterCode, entity.StationCode, out error))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error;
                    return Json(result);
                }
                
                var currentUser = GetCurrentUser().LoginAccount;
                entity.MUser = currentUser;
                entity.MTime = DateTime.Now;
                
                // 更新站点和用户关联关系
                result.Data = _app.UpdateWithUserRelations(entity, req.userIds, currentUser);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除（包括批量）

        /// <summary>
        /// 删除
        /// </summary>
        /// <returns></returns>
        [HttpDelete]
        public IHttpActionResult Delete([FromBody]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.DeleteByKeys(ids, GetCurrentUser().LoginAccount);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 导出

        /// <summary>
        /// 导出
        /// </summary>
        /// <param name="keyword">关键字</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] string keyword)
        {
            var result = new ResponseData();
            try
            {
                var query = _app.GetList();
                if (!string.IsNullOrEmpty(keyword))
                {
                    query = query.Where(t => t.WorkCenterName.Contains(keyword) || 
                                            t.WorkCenterCode.Contains(keyword) || 
                                            t.ProcessShortText.Contains(keyword) || 
                                            t.StationCode.Contains(keyword) || 
                                            t.StationName.Contains(keyword));
                }
                
                var itemsData = query.OrderBy(t => t.WorkCenterCode).ToList();
                List<ExcelColumn<MD_ReportStation>> columns = ExcelService.FetchDefaultColumnList<MD_ReportStation>();
                string[] ignoreField = new string[] { "IsDelete", "DTime", "DUser" };
                var ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<MD_ReportStation> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }

                return ExportToExcelFile<MD_ReportStation>(itemsData, columns,
                    GetCurrentUser().UserName + "_报工站点");
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #region 导入

        /// <summary>
        /// 导入
        /// </summary>
        /// <param name="entitys">接收的导入集合</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult ImportExcelToData([FromBody]List<MD_ReportStationImport> entitys)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                if (_app.ImportExcelToBaseData(entitys, GetCurrentUser().LoginAccount, out error_message))
                {
                    result.Data = true;
                    result.Code = (int)WMSStatusCode.Success;
                }
                else
                {
                    result.Data = false;
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
            return Json(result);
        }

        #endregion

        #region 导出模板

        /// <summary>
        /// 导出模板
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelModel()
        {
            var result = new ResponseData();
            try
            {
                var columns = ExcelService.FetchDefaultColumnList<MD_ReportStationImport>();
                string modelName = "报工站点-导入模板";
                var itemsData = new List<MD_ReportStationImport>() { };
                return ExportToExcelFile<MD_ReportStationImport>(itemsData, columns,
                    GetCurrentUser().UserName + "_" + modelName);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #region 获取站点关联的用户列表

        /// <summary>
        /// 获取站点关联的用户列表
        /// </summary>
        /// <param name="stationId">站点ID</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetStationUsers([FromUri]string stationId)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.GetStationUserRelations(stationId);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion
    }
}
