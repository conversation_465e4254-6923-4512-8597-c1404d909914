using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.MM
{
    /// <summary>
    /// 委外领料申请明细表
    /// </summary>
    [SugarTable("MM_PickingApplyDetail")]
    public class MM_PickingApplyDetail : BaseEntity
    {
        /// <summary> 
        /// 明细ID
        /// </summary> 
        [SugarColumn(IsPrimaryKey = true)]
        [Description("明细ID")]
        public string DetailID { get; set; }

        /// <summary> 
        /// 单号
        /// </summary> 
        [Description("单号")]
        public string DocNum { get; set; }

        /// <summary> 
        /// 领料申请单单号
        /// </summary> 
        [Description("领料申请单单号")]
        public string SubcontractingApplicationNum { get; set; }

        /// <summary> 
        /// 行号
        /// </summary> 
        [Description("行号")]
        public int? BaseLine { get; set; }

        /// <summary> 
        /// 采购订单
        /// </summary> 
        [Description("采购订单")]
        public string BaseNum { get; set; }
        
        /// <summary> 
        /// 物料编号
        /// </summary> 
        [Description("物料编号")]
        public string ItemCode { get; set; }

        /// <summary> 
        /// 物料名称
        /// </summary> 
        [Description("物料名称")]
        public string ItemName { get; set; }

        /// <summary> 
        /// 供应商编号
        /// </summary> 
        [Description("供应商编号")]
        public string SupplierCode { get; set; }

        /// <summary> 
        /// 供应商名称
        /// </summary> 
        [Description("供应商名称")]
        public string SupplierName { get; set; }

        /// <summary> 
        /// 数量
        /// </summary> 
        [Description("数量")]
        public decimal? Qty { get; set; }

        /// <summary> 
        /// 单位
        /// </summary> 
        [Description("单位")]
        public string Unit { get; set; }

        /// <summary> 
        /// 状态 0:未开始 1:进行中 2：已完成
        /// </summary> 
        [Description("状态")]
        public int? Status { get; set; }

        /// <summary>
        /// 产品编号
        /// </summary>
        [Description("产品编号")]
        public string MatnrCode { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        [Description("产品名称")]
        public string MatnrName { get; set; }

        /// <summary>
        /// 产品数量
        /// </summary>
        [Description("产品数量")]
        public decimal? MatnrQty { get; set; }

    }
}
