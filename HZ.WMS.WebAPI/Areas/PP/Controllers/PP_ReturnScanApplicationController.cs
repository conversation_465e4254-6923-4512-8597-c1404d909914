using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using HZ.WMS.WebAPI.Controllers;
using System.Web;
using HZ.WMS.Application.PP;
using HZ.Core.Http;
using HZ.WMS.Application;
using HZ.WMS.Entity.Sys;
using HZ.WMS.Entity.PP.Dto;
using System.Configuration;
using HZ.WMS.Entity.PP;
using HZ.WMS.Application.Sys;
using HZ.Core.Office;
using System.IO;
using HZ.WMS.Application.MD;
using HZ.WMS.Entity.PP.ViewModel;

namespace HZ.WMS.WebAPI.Areas.PP.Controllers
{
    /// <summary>
    /// 车间退料申请
    /// </summary>
    public class PP_ReturnScanApplicationController : ApiBaseController
    {
        private PP_ReturnMaterialApp _app = new PP_ReturnMaterialApp();
        private PP_ReturnMaterialDetailApp detailApp = new PP_ReturnMaterialDetailApp();
        private Sys_SwithConfigApp _switchApp = new Sys_SwithConfigApp();
        private MD_StockApp _stockApp = new MD_StockApp();

        #region 查询

        /// <summary>
        /// 查询主表分页列表
        /// </summary>
        /// <param name="page">分页</param>
        /// <param name="keyword"></param>
        /// <param name="dateValue">日期</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]string keyword, [FromUri] DateTime[] dateValue, [FromUri] bool? isPosted,[FromUri]int? ExamineStatus)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];

                var itemsData = _app.GetPageList(page, t => (string.IsNullOrEmpty(keyword)
                        || t.DocNum.Contains(keyword)
                        || t.CUser.Contains(keyword))
                        && (t.CTime >= fromTime && t.CTime < toTime)
                        && (ExamineStatus == null || t.ExamineStatus == ExamineStatus)
                        && (isPosted == null || t.IsPosted == isPosted)).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 查询子表的分页列表
        /// </summary>
        /// <param name="page">分页</param>
        /// <param name="DocNum"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetDetailedPageList([FromUri]Pagination page, [FromUri]string DocNum)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = detailApp.GetPageList(page, x => x.DocNum == DocNum).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 编辑根据单号查询明细信息,编辑和审核走一个接口
        /// </summary>
        /// <param name="DocNums"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetDetailList([FromUri]string[] DocNums)
        {
            var result = new ResponseData();
            try
            {
                result.Data = detailApp.GetList(x => DocNums.Contains(x.DocNum)).ToList();
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取SAP生产订单子表

        /// <summary>
        /// 查询仓库库存信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetStockInfo([FromUri]Pagination page, [FromUri] string keyword, [FromUri] string Whs)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _stockApp.GetPageList(page, t => (string.IsNullOrEmpty(keyword)
                                                  || t.ItemCode.Contains(keyword) || t.ItemName.Contains(keyword)
                                                  || t.WhsCode.Contains(keyword) || t.WhsName.Contains(keyword) 
                                                  || t.RegionCode.Contains(keyword) || t.RegionName.Contains(keyword) 
                                                  || t.BinLocationCode.Contains(keyword) || t.BinLocationName.Contains(keyword) 
                                                  || t.SaleNum.Contains(keyword) || t.SpecialStock.Contains(keyword))
                                                  && (string.IsNullOrEmpty(Whs) || t.WhsCode.Contains(Whs) || t.WhsName.Contains(Whs))
                                                  && (t.SpecialStock == null || t.SpecialStock == "")
                                                  && (t.SupplierCode == null || t.SupplierCode == "")
                                                  && (t.SaleNum == null || t.SaleNum == "")
                                                  && t.Qty > 0)
                                                  .Select(s => new PP_ReturnMaterial_View
                                                  {
                                                      //ProductionOrderNo = "",
                                                      //ComponentLineNo= 0,
                                                      ComponentCode = s.ItemCode,
                                                      MaterialName = s.ItemName,
                                                      DemandQty = s.Qty,
                                                      ComponentUnit = s.Unit,
                                                      //FactoryCode = "",
                                                      OutWhsCode = s.WhsCode,
                                                      OutWhsName = s.WhsName,
                                                      OutRegionCode = s.RegionCode,
                                                      OutRegionName = s.RegionName,
                                                      OutBinCode = s.BinLocationCode,
                                                      OutBinName = s.BinLocationName,
                                                      InWhsCode = "",
                                                      InWhsName = "",
                                                      InRegionCode = "",
                                                      InRegionName = "",
                                                      InBinCode = "",
                                                      InBinName = "",
                                                      SalesOrderNo = s.SaleNum,
                                                      SalesOrderLineNo = s.SaleLine,
                                                      SpecialInventory = s.SpecialStock,
                                                      AssessmentCategory = string.IsNullOrEmpty(s.AssessType) ? "" : "B",
                                                  }).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 获取SAP生产订单子表
        /// </summary>
        /// <param name="page"></param>
        /// <param name="ProductionOrderNo">生产订单</param>
        /// <param name="MaterialNo">主表物料号</param>
        /// <param name="ComponentCode">组件物料编号</param>
        /// <param name="dateValue">装配日期区间</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageSapOrderDetailList([FromUri]Pagination page, [FromUri]string ProductionOrderNo, [FromUri]string MaterialNo, [FromUri]string ComponentCode, [FromUri] DateTime[] dateValue)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];

                var itemsData = detailApp.GetSapPageList(page,
                    t => (string.IsNullOrEmpty(ProductionOrderNo) || t.ProductionOrderNo.Contains(ProductionOrderNo))
                    && (string.IsNullOrEmpty(MaterialNo) || t.MaterialNo.Contains(MaterialNo))
                    && (string.IsNullOrEmpty(ComponentCode) || t.ComponentCode.Contains(ComponentCode))
                    && (t.StartTime >= fromTime && t.StartTime < toTime)).ToList();

                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取系统生成的单号

        /// <summary>
        /// 获取系统生成的单号
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetDocNum()
        {
            var result = new ResponseData();
            try
            {
                result.Data = base.GenerateDocNum(DocType.PP, DocFixedNumDef.PP_ReturnScanApplication);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 上传

        /// <summary>
        /// 上传
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Upload()
        {
            var result = new ResponseData();
            try
            {
                //获取参数信息
                HttpContextBase context = (HttpContextBase)Request.Properties["MS_HttpContext"];
                HttpRequestBase request = context.Request;
                string fileName = request.Params["TempleteFile"]?.ToString();
                fileName = fileName.Replace(Path.GetFileNameWithoutExtension(fileName), Guid.NewGuid().ToString());
                string resPath = ConfigurationManager.AppSettings["PP_ReturnScanApplicationUploadPath"]?.ToString() + fileName;
                string filePath = HttpContext.Current.Server.MapPath("~/") + resPath;
                request.Files[0].SaveAs(filePath);
                result.Data = new { FilePath = "/" + resPath.Replace("\\", "/") };
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 保存

        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="material_Dto"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Save([FromBody]PP_ReturnMaterial_Dto material_Dto)
        {
            var result = new ResponseData();
            try
            {
                Sys_User currentUser = GetCurrentUser();
                var entity = new PP_ReturnMaterial
                {
                    DocNum = material_Dto.DocNum,
                    FileName = material_Dto.FileName,
                    FilePath = material_Dto.FilePath,
                    ExamineStatus = 0,
                    Remark = material_Dto.Remark,
                    CUser = currentUser.LoginAccount,
                };

                material_Dto.detailed.ForEach(item =>
                {
                    item.DocNum = material_Dto.DocNum;
                    item.MovementType = material_Dto.MovementType;
                    item.CUser = currentUser.LoginAccount;
                });
                if (!_app.Add(entity, material_Dto.detailed))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                }
                //else
                //{
                //    //是否自动过账判定
                //    if (_switchApp.IsPPReturnScanAutoPost)
                //    {
                //        string error_message = "";
                //        bool bSubmit = _app.DoPost(new List<PP_ReturnMaterial> { entity }, currentUser.LoginAccount, out error_message);

                //        if (!bSubmit)
                //        {
                //            result.Message = "审核成功；过账失败，失败原因：" + error_message;
                //        }
                //    }
                //}
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="material_Dto"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Update([FromBody]PP_ReturnMaterial_Dto material_Dto)
        {
            var result = new ResponseData();
            try
            {
                Sys_User user = GetCurrentUser();
                if (!_app.Edit(material_Dto, user.LoginAccount))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="DocNums">根据单号进行删除</param>
        /// <returns></returns>
        [HttpDelete]
        public IHttpActionResult Delete([FromBody]string[] DocNums)
        {
            var result = new ResponseData();
            try
            {
                Sys_User user = GetCurrentUser();
                var details = detailApp.GetList(t => DocNums.Contains(t.DocNum)).ToList();
                if (details.Any(w => w.IsPosted == true))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "ui.Message.PODeletePostingWarning"; // "已过账数据不允许删除!";
                }
                else
                {
                    var list = _app.GetList(w => DocNums.Contains(w.DocNum)).ToList();
                    _app.Delete(user.LoginAccount, list, details);
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 审核

        /// <summary>
        /// 审核
        /// </summary>
        /// <param name="examine"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Audit([FromBody]ReturnExamine examine)
        {
            var result = new ResponseData();
            try
            {
                Sys_User loginUser = GetCurrentUser();
                if (examine.entities.Any(w => w.ExamineStatus == 1))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "请检查审核状态";
                }
                else
                {
                    examine.entities.ForEach(entity =>
                    {
                        entity.MUser = loginUser.LoginAccount;
                        entity.ExamineStatus = examine.status;
                        entity.WarehouseReviewer = loginUser.LoginAccount;
                        entity.WarehouseTime = DateTime.Now;
                        entity.ManualPostTime = examine.ManualPostTime;
                    });
                    _app.UpdateWithTran(examine.entities);
                    detailApp.UpdateWithTran(examine.DetailList);

                    //是否自动过账判定
                    if (_switchApp.IsPPReturnScanAutoPost)
                    {
                        string error_message = "";
                        var docNums = examine.entities.Select(s => s.DocNum);
                        var details = detailApp.GetList(w => docNums.Contains(w.DocNum)).ToList();
                        bool bSubmit = _app.DoPost(examine.entities, loginUser.LoginAccount, out error_message);

                        if (!bSubmit)
                        {
                            result.Message = "审核成功；过账失败，失败原因：" + error_message;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 取消审核

        [HttpPost]
        public IHttpActionResult CancelExamine([FromBody]ReturnExamine examine)
        {
            var result = new ResponseData();
            try
            {
                Sys_User loginUser = GetCurrentUser();
                examine.entities.ForEach(entity =>
                {
                    entity.MUser = loginUser.LoginAccount;
                    entity.ExamineStatus = 0;
                    entity.WarehouseReviewer = "";
                    entity.WarehouseTime = null;
                });
                _app.UpdateWithTran(examine.entities);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 打印退料申请单

        /// <summary>
        /// 
        /// </summary>
        /// <param name="docNums"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult PrintReturnApply([FromUri]string[] docNums)
        {
            var result = new ResponseData();
            try
            {
                DevExpress.XtraReports.UI.XtraReport report = DevExpress.XtraReports.UI.XtraReport.FromFile(HttpContext.Current.Server.MapPath("~/") + ConfigurationManager.AppSettings["LabelTemplateUploadPath"]?.ToString() + "车间退料单.repx", true);
                DevExpress.DataAccess.Sql.SqlDataSource dataSource = report.DataSource as DevExpress.DataAccess.Sql.SqlDataSource;
                DevExpress.DataAccess.Sql.QueryParameter parameter = new DevExpress.DataAccess.Sql.QueryParameter()
                {
                    Name = "docNums",
                    Type = typeof(string[]),
                    Value = docNums
                };
                dataSource.Queries[0].Parameters[0] = parameter;
                return base.PrintToPDF(report);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 导出

        /// <summary>
        /// 导出
        /// </summary>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] string keyword, [FromUri] DateTime[] dateValue)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];

                var itemsData = detailApp.GetList(
                    t => (string.IsNullOrEmpty(keyword) 
                    || t.DocNum.Contains(keyword)
                    || t.CUser.Contains(keyword))
                    && (t.CTime >= fromTime && t.CTime < toTime)
                    && !t.IsDelete
                ).ToList();
                List<ExcelColumn<PP_ReturnMaterialDetail>> columns = ExcelService.FetchDefaultColumnList<PP_ReturnMaterialDetail>();
                string[] ignoreField = new string[]
                {
                    "ID"
                    ,"IsPosted"
                    ,"PostUser"
                    ,"PostTime"
                    ,"ManualPostTime"
                    ,"SapDocNum"
                    ,"SapLine"
                    ,"IsDelete"
                    ,"MUser"
                    ,"MTime"
                    ,"DUser"
                    ,"DTime"
                };

                List<ExcelColumn<PP_ReturnMaterialDetail>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<PP_ReturnMaterialDetail> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                columns.ForEach((column) =>
                {
                    //if (column.ColumnName == "IsPosted")
                    //{
                    //    column.Formattor = ExcelExportFormatter.IsOrNoFormatterForBoolean;
                    //}
                });

                return ExportToExcelFile(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion
    }
}
