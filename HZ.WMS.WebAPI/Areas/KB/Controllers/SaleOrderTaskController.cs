using HZ.Core.Http;
using HZ.WMS.Application.KB;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Http;

namespace HZ.WMS.WebAPI.Areas.KB.Controllers
{
    public class SaleOrderTaskController : ApiController
    {
        private SaleOrderBoardApp _poApp = new SaleOrderBoardApp();

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IHttpActionResult GetUserInfo()
        {
            var result = new ResponseData();
            try
            {
                //验证用户名和密码
                result.Data = _poApp.GetSaleOrderTaskData();
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
    }
}