using SqlSugar;
using HZ.Core.Http;
//
using HZ.WMS.Application.MD;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.Sys;
using HZ.WMS.WebAPI.Controllers;
using System;
using System.Web.Http;
using HZ.WMS.Application.RPT;
using System.Collections.Generic;
using HZ.Core.Office;
using HZ.WMS.Application;
using System.Linq;
using HZ.WMS.Entity.MD.ViewModel;
using HZ.WMS.Application.PO;
using HZ.WMS.Entity.RPT;

namespace HZ.WMS.WebAPI.Areas.MD.Controllers
{
    /// <summary>
    /// 物料移动记录
    /// </summary>
    public class RPT_ItemMoveController : ApiBaseController
    {
        private RPT_ItemMoveViewApp _app = new RPT_ItemMoveViewApp();


        #region 查询分页列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <param name="fromTime"></param>
        /// <param name="toTime"></param>
        /// <param name="regionCode"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPage([FromUri]Pagination page, [FromUri]string keyword, [FromUri] DateTime[] dateValue,[FromUri] string[] DocTypes)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];
                page.Sort = "CTime,ItemCode";
                var itemsData = new List<RPT_ItemMoveView>();
                if (DocTypes.Length == 0)
                {
                     itemsData = _app.GetPageList(page, t => (string.IsNullOrEmpty(keyword)
                                                  || t.DocNum.Contains(keyword) || t.DocType.Contains(keyword)
                                                  || t.WhsCode.Contains(keyword) || t.WhsName.Contains(keyword)
                                                  || t.ItemCode.Contains(keyword) || t.ItemName.Contains(keyword)
                                                  || t.SupplierCode.Contains(keyword) || t.SupplierName.Contains(keyword)
                                                  || t.SapDocNum.Contains(keyword) || t.BaseNum.Contains(keyword)
                                                  || t.PostUser.Contains(keyword) || t.CUser.Contains(keyword))
                                                  && (t.CTime >= fromTime && t.CTime <= toTime)
                                                   ).ToList();
                }
                else
                {
                     itemsData = _app.GetPageList(page, t => (string.IsNullOrEmpty(keyword)
                                            || t.DocNum.Contains(keyword) || t.DocType.Contains(keyword)
                                            || t.WhsCode.Contains(keyword) || t.WhsName.Contains(keyword)
                                            || t.ItemCode.Contains(keyword) || t.ItemName.Contains(keyword)
                                            || t.SupplierCode.Contains(keyword) || t.SupplierName.Contains(keyword)
                                            || t.SapDocNum.Contains(keyword) || t.BaseNum.Contains(keyword)
                                            || t.PostUser.Contains(keyword) || t.CUser.Contains(keyword))
                                            && DocTypes.Contains(t.DocType)
                                            && (t.CTime >= fromTime && t.CTime <= toTime)
                                             ).ToList();

         
                }
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }


        #endregion



        #region 导出

        /// <summary>
        /// 
        /// </summary>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] string keyword,  [FromUri] DateTime[] dateValue, [FromUri] string[] DocTypes)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];
                var itemsData = new List<RPT_ItemMoveView>();
                if (DocTypes.Length == 0)
                {
                     itemsData = _app.GetList().Where(t => (string.IsNullOrEmpty(keyword)
                                                || t.DocNum.Contains(keyword) || t.DocType.Contains(keyword)
                                                || t.WhsCode.Contains(keyword) || t.WhsName.Contains(keyword)
                                                || t.ItemCode.Contains(keyword) || t.ItemName.Contains(keyword)
                                                || t.SupplierCode.Contains(keyword) || t.SupplierName.Contains(keyword)
                                                || t.SapDocNum.Contains(keyword) || t.BaseNum.Contains(keyword)
                                                || t.PostUser.Contains(keyword) || t.CUser.Contains(keyword))
                                                && (t.CTime >= fromTime && t.CTime <= toTime)).OrderBy(x => x.CTime).OrderBy(x => x.ItemCode).ToList();
                }
                else
                {
                    itemsData = _app.GetList().Where(t => (string.IsNullOrEmpty(keyword)
                                               || t.DocNum.Contains(keyword) || t.DocType.Contains(keyword)
                                               || t.WhsCode.Contains(keyword) || t.WhsName.Contains(keyword)
                                               || t.ItemCode.Contains(keyword) || t.ItemName.Contains(keyword)
                                               || t.SupplierCode.Contains(keyword) || t.SupplierName.Contains(keyword)
                                               || t.SapDocNum.Contains(keyword) || t.BaseNum.Contains(keyword)
                                               || t.PostUser.Contains(keyword) || t.CUser.Contains(keyword))
                                                && DocTypes.Contains(t.DocType)
                                               && (t.CTime >= fromTime && t.CTime <= toTime)).OrderBy(x => x.CTime).OrderBy(x => x.ItemCode).ToList();
                }
                //PO_BarCodeApp _barcodeApp = new PO_BarCodeApp();
                //var isConsignData = _barcodeApp.GetList(x => x.IsConsign == true)?.ToList();
                //itemsData.ForEach(item =>
                //{
                //    var flag = isConsignData.Where(x => x.BarCode == item.BarCode)?.ToList().FirstOrDefault();
                //    item.Remark = "0";
                //    if (flag != null)
                //    {
                //        item.Remark = "1";
                //    }
                //});
                //var itemsData = _app.GetList().ToList();
                List<ExcelColumn<RPT_ItemMoveView>> columns = ExcelService.FetchDefaultColumnList<RPT_ItemMoveView>();
                string[] ignoreField = new string[] {"MUser","MTime","IsDelete", "DTime", "DUser" };

                List<ExcelColumn<RPT_ItemMoveView>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<RPT_ItemMoveView> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }


                return ExportToExcelFile<RPT_ItemMoveView>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }
        #endregion

    }
}

