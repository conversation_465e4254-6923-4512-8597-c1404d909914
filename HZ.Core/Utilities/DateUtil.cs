using System;

namespace AOS.Core.Utilities
{
    public class DateUtil
    {
        public static DateTime? GetStartTime(DateTime? dateTime)
        {
            return dateTime?.Date;
        }

        public static DateTime? GetEndTime(DateTime? dateTime)
        {
            return dateTime?.Date.AddHours(23).AddMinutes(59).AddSeconds(59);
        }

        public static string ParseStr(DateTime? dateTime, string format = "yyyy-MM-dd")
        {
            return dateTime.Value.ToString(format);
        }
    }
}