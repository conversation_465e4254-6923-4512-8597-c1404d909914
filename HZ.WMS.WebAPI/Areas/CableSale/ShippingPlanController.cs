using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;
using AOS.Core.Utilities;
using AOS.OMS.Entity.Sale;
using AOS.WMS.Entity.CableProduce;
using HZ.Core.Http;
using HZ.Core.Office;
using HZ.WMS.Application.PP;
using HZ.WMS.WebAPI.Controllers;
using LinqKit;

namespace HZ.WMS.WebAPI.Areas.PP.Controllers
{
    /// <summary>
    /// 发运计划
    /// </summary>
    public class ShippingPlanController : ApiBaseController
    {
        private Cable_ShippingPlanApp _app = new Cable_ShippingPlanApp();

        #region 获取发运列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri] Pagination page,
            [FromUri] Cable_ShippingPlanListReq shippingPlanListReq)
        {
            var result = new ResponseData();
            try
            {
                page.Sort = "CTime desc";
                var searchCondition = PredicateBuilder.New<ShippingPlan>(t =>
                    (string.IsNullOrEmpty(shippingPlanListReq.ContractNo) ||
                     t.ContractNo.Equals(shippingPlanListReq.ContractNo)) &&
                     (string.IsNullOrEmpty(shippingPlanListReq.SapNo) || t.SapNo.Equals(shippingPlanListReq.SapNo)) &&
                    (string.IsNullOrEmpty(shippingPlanListReq.CustomerName) || t.CustomerName.Equals(shippingPlanListReq.CustomerName)) &&
                    (string.IsNullOrEmpty(shippingPlanListReq.CustomerOrderNum) || t.CustomerOrderNum.Equals(shippingPlanListReq.CustomerOrderNum)) &&
                    (string.IsNullOrEmpty(shippingPlanListReq.ContractNo) || t.ContractNo.Equals(shippingPlanListReq.ContractNo)) &&
                    (string.IsNullOrEmpty(shippingPlanListReq.OrderTypeName) || t.OrderTypeName.Equals(shippingPlanListReq.OrderTypeName)) &&
                    (string.IsNullOrEmpty(shippingPlanListReq.ElevatorType) || t.ElevatorType.Equals(shippingPlanListReq.ElevatorType)) &&
                    (shippingPlanListReq.SapLine == null || t.SapLine.Equals(shippingPlanListReq.SapLine)) &&
                    (shippingPlanListReq.ShipmentDownFlag == null || t.ShipmentDownFlag.Equals(shippingPlanListReq.ShipmentDownFlag)) &&
                    t.ShipmentStatus > 0);
                if (shippingPlanListReq.CreateDate != null && shippingPlanListReq.CreateDate.Length == 2)
                {
                    searchCondition.And(t =>
                        t.SetShipmentDate >= DateUtil.GetStartTime(shippingPlanListReq.CreateDate[0])  &&
                        t.SetShipmentDate < DateUtil.GetEndTime(shippingPlanListReq.CreateDate[1]));
                }
                if (shippingPlanListReq.ShipmentDate != null && shippingPlanListReq.ShipmentDate.Length == 2)
                {
                    searchCondition.And(t =>
                        t.ShipmentDate >= DateUtil.GetStartTime(shippingPlanListReq.ShipmentDate[0]) &&
                        t.ShipmentDate < DateUtil.GetEndTime(shippingPlanListReq.ShipmentDate[1]));
                }
                var itemsData = _app.GetPageList(page, searchCondition, _app.DbContextForOMS);
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 导出

        /// <summary>
        /// 查询分页列表，按客户名称分sheet导出
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult Export([FromUri] Pagination page,
            [FromUri] Cable_ShippingPlanListReq shippingPlanListReq)
        {
            var result = new ResponseData();
            try
            {
                List<ShippingPlan> itemsData = new List<ShippingPlan>();
                if (shippingPlanListReq.Ids != null && shippingPlanListReq.Ids.Length > 0)
                {
                    itemsData = _app.DbContextForOMS.Queryable<ShippingPlan>().Where(t => shippingPlanListReq.Ids.Contains(t.Id)).ToList();
                }
                else
                {
                    var searchCondition = PredicateBuilder.New<ShippingPlan>(t =>
                        (string.IsNullOrEmpty(shippingPlanListReq.ContractNo) ||
                         t.ContractNo.Equals(shippingPlanListReq.ContractNo)) &&
                        (string.IsNullOrEmpty(shippingPlanListReq.SapNo) || t.SapNo.Equals(shippingPlanListReq.SapNo)) &&
                        (string.IsNullOrEmpty(shippingPlanListReq.CustomerName) || t.CustomerName.Equals(shippingPlanListReq.CustomerName)) &&
                        (shippingPlanListReq.SapLine == null || t.SapLine.Equals(shippingPlanListReq.SapLine)) &&
                        (shippingPlanListReq.ShipmentDownFlag == null || t.ShipmentDownFlag.Equals(shippingPlanListReq.ShipmentDownFlag)) &&
                        t.ShipmentStatus > 0);
                    if (shippingPlanListReq.CreateDate != null && shippingPlanListReq.CreateDate.Length == 2)
                    {
                        searchCondition.And(t =>
                            t.SetShipmentDate >= DateUtil.GetStartTime(shippingPlanListReq.CreateDate[0])  &&
                            t.SetShipmentDate < DateUtil.GetEndTime(shippingPlanListReq.CreateDate[1]));
                    }
                    if (shippingPlanListReq.ShipmentDate != null && shippingPlanListReq.ShipmentDate.Length == 2)
                    {
                        searchCondition.And(t =>
                            t.ShipmentDate >= DateUtil.GetStartTime(shippingPlanListReq.ShipmentDate[0]) &&
                            t.ShipmentDate < DateUtil.GetEndTime(shippingPlanListReq.ShipmentDate[1]));
                    }
                    page.PageSize = int.MaxValue;
                    itemsData = _app.GetPageList(page, searchCondition, _app.DbContextForOMS);
                }
                var cableIds = itemsData.Select(detail => detail.Id).ToList();
                var details = _app.DbContextForOMS.Queryable<SD_Cable_Sale_OrderDetails>()
                    .Where(t => cableIds.Contains(t.Pid)).ToList();
                var detailMap = details.GroupBy(t => t.Pid).ToDictionary(t => t.Key, t => t.ToList());
                var parameters = _app.DbContextForOMS.Queryable<SD_Cable_Sale_Parameter>()
                    .Where(t => cableIds.Contains(t.Pid)).ToList();
                var parameterMap = parameters.GroupBy(t => t.Pid).ToDictionary(t => t.Key, t => t.ToList().FirstOrDefault());

                foreach (var detail in itemsData)
                {
                    // 计算木箱 木盘
                    if (detailMap.ContainsKey(detail.Id))
                    {
                        var detailList = detailMap[detail.Id];
                        foreach (var orderDetail in detailList)
                        {
                            if (orderDetail.Classify == "包装箱1")
                            {
                                detail.WoodenBoxSize = orderDetail.SpecificationModel;
                            }

                            if (orderDetail.Classify == "包装箱2")
                            {
                                detail.WoodenTraySize = orderDetail.SpecificationModel;
                            }
                        }
                    }

                    if (parameterMap.ContainsKey(detail.Id))
                    {
                        detail.BoxPrint = parameterMap[detail.Id].Print + parameterMap[detail.Id].PackageBoxStatus;
                    }
                }

                // 更新导出标记
                foreach (var shippingPlan in itemsData)
                {
                    shippingPlan.ShipmentDownFlag = 1;
                }
                _app.DbContextForOMS.Updateable<ShippingPlan>(itemsData).ExecuteCommand();

                List<ExcelColumn<ShippingPlan>> columns = ExcelService.FetchDefaultColumnList<ShippingPlan>();
                string[] ignoreField = new string[]
                {
                    "IsDelete", "DTime", "DUser", "CustomerId", "CTime", "CUser", "MTime", "MUser", "Id",
                    "OrderNum", "LineNum", "Status", "OrderType", "DownFlag", "SapNo", "SapLine", "Quantity",
                    "ActualDate", "CustomerCode", "SettlementAdd", "ShipmentDownFlag",
                    "ShipmentStatus", "DeliveryDate", "DeliveryDate", "SetShipmentDate"
                };

                List<ExcelColumn<ShippingPlan>> ignoreFieldList =
                    columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<ShippingPlan> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }

                // 按客户名称分组
                var customerGroups = itemsData.GroupBy(t => t.CustomerName ?? "未知客户");
                var customerGroupDict = customerGroups.ToDictionary(g => g.Key, g => g.ToList());

                // 如果没有数据，返回空表
                if (customerGroupDict.Count == 0)
                {
                    return ExportToExcelFile(new List<ShippingPlan>(), columns);
                }

                // 使用按组导出的方法，每个客户一个sheet
                return ExportToExcelFileByGroup(customerGroupDict, columns, GetCurrentUser().UserName + "_发运计划导出");
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="DocNums">根据单号进行删除</param>
        /// <returns></returns>
        //[HttpDelete]
        [HttpDelete]
        //[AllowAnonymous]
        public IHttpActionResult Delete([FromBody] string[] Ids)
        {
            //[FromBody]string[] ids
            var result = new ResponseData();
            try
            {
                bool bDelete = _app.Deletes(Ids, GetCurrentUser().LoginAccount);
                if (!bDelete)
                {
                    result.Code = (int)WMSStatusCode.UnHandledException;
                    result.Message = "删除失败";
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion
    }
}