using System;
using System.Collections.Generic;
using System.Linq;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.MD.Import;

namespace HZ.WMS.Application.MD
{
    /// <summary>
    /// 报工站点物料
    /// </summary>
    public class MD_ReportStationMaterialApp : BaseApp<MD_ReportStationMaterial>
    {
        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public MD_ReportStationMaterialApp() : base() { }

        #endregion

        #region 导入

        /// <summary>
        /// 导入
        /// </summary>
        /// <param name="excelList">接收的集合</param>
        /// <param name="opUser">当前用户</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool ImportExcelToBaseData(List<MD_ReportStationMaterialImport> excelList, string opUser, out string error_message)
        {
            error_message = "";
            var flag = true;
            DateTime date = DateTime.Now;
            var entityList = new List<MD_ReportStationMaterial>();

            try
            {
                // 验证工作中心站点是否存在
                var workCenterStationApp = new MD_WorkCenterStationApp();
                
                foreach (var item in excelList)
                {
                    // 验证工作中心站点是否存在
                    if (!workCenterStationApp.ValidateWorkCenterStationExists(item.WorkCenterCode, item.StationCode))
                    {
                        error_message = $"工作中心编码'{item.WorkCenterCode}'和站点代码'{item.StationCode}'的组合不存在，请先在工作中心站点模块中添加";
                        return false;
                    }
                    
                    var entity = new MD_ReportStationMaterial
                    {
                        Id = Guid.NewGuid().ToString(),
                        WorkCenterName = item.WorkCenterName,
                        WorkCenterCode = item.WorkCenterCode,
                        ProcessNo = item.ProcessNo,
                        ProcessShortText = item.ProcessShortText,
                        StationCode = item.StationCode,
                        StationName = item.StationName,
                        MaterialCode = item.MaterialCode,
                        MaterialDesc = item.MaterialDesc,
                        Quantity = item.Quantity,
                        Unit = item.Unit,
                        Remark = item.Remark,
                        IsDelete = false,
                        CUser = opUser,
                        CTime = date,
                        MUser = opUser,
                        MTime = date
                    };

                    entityList.Add(entity);
                }

                if (entityList.Count > 0)
                {
                    DbContext.Insertable(entityList).ExecuteCommand();
                }
            }
            catch (Exception ex)
            {
                flag = false;
                error_message = ex.Message;
            }

            return flag;
        }

        #endregion
        
        #region 验证方法
        
        /// <summary>
        /// 验证工作中心站点信息是否存在
        /// </summary>
        /// <param name="workCenterCode">工作中心编码</param>
        /// <param name="stationCode">站点代码</param>
        /// <param name="error">错误信息</param>
        /// <returns>验证结果</returns>
        public bool ValidateWorkCenterStationInfo(string workCenterCode, string stationCode, out string error)
        {
            error = string.Empty;
            var workCenterStationApp = new MD_WorkCenterStationApp();
            
            if (!workCenterStationApp.ValidateWorkCenterStationExists(workCenterCode, stationCode))
            {
                error = $"工作中心编码'{workCenterCode}'和站点代码'{stationCode}'的组合不存在，请先在工作中心站点模块中添加";
                return false;
            }
            
            return true;
        }
        
        #endregion
    }
} 