using System;
using System.Collections.Generic;
using System.Linq;
using AOS.Core.Utilities;
using AOS.OMS.Entity.Sale;
using AOS.WMS.Entity.CableProduce;
using HZ.Core.Http;
using HZ.WMS.Application.Sys;
using HZ.WMS.Entity;
using HZ.WMS.Entity.SAP;
using HZ.WMS.Entity.Sys;
using LinqKit;

namespace HZ.WMS.Application.PP
{
    /// <summary>
    /// 生产订单主表
    /// </summary>
    public class Cable_ProductionOrderApp : BaseApp<Cable_ProductionOrder>
    {
        private BaseApp<BaseEntity> _baseApp = new BaseApp<BaseEntity>();
        private Sys_SAPCompanyInfoApp _sap = new Sys_SAPCompanyInfoApp();

        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public Cable_ProductionOrderApp() : base()
        {
        }

        #endregion

        /**
         * 获取 计划订单列表
         */
        public List<Cable_ProductionOrderListRes> GetPageList(Pagination page,
            Cable_ProductionOrderListReq productionOrderListReq)
        {
            var searchCondition = PredicateBuilder.New<Cable_ProductionOrder>(t => (string.IsNullOrEmpty(productionOrderListReq.ContractNo) 
                || t.ContractNo.Equals(productionOrderListReq.ContractNo)) 
                && (string.IsNullOrEmpty(productionOrderListReq.SapNo) 
                    || t.SapNo.Equals(productionOrderListReq.SapNo)) 
                && (productionOrderListReq.SapLine == null 
                    || t.SapLine.Equals(productionOrderListReq.SapLine)) 
                && t.Status >= productionOrderListReq.Status 
                && t.CTime >= productionOrderListReq.GetStartCreateDate() 
                && t.CTime < productionOrderListReq.GetEndCreateDate());
            if (productionOrderListReq.AssembleDate != null && productionOrderListReq.AssembleDate.Length == 2)
            {
                var additionalCondition = PredicateBuilder.New<Cable_ProductionOrder>(t =>
                    t.AssembleDate >= productionOrderListReq.GetStartAssembleDate() 
                    && t.AssembleDate < productionOrderListReq.GetEndAssembleDate());
                searchCondition.And(additionalCondition);
            }
            var list = GetPageList(page, searchCondition);
            var orderIds = list.Select(t => t.OrderId).Distinct().ToList();
            var orderInfos = DbContextForOMS.Queryable<SD_Cable_Sale_OrderInfo>().Where(t => orderIds.Contains(t.Id))
                .ToList();
            var orderMap = orderInfos.ToDictionary(t => t.Id, t => t);
            List<Cable_ProductionOrderListRes> resList = new List<Cable_ProductionOrderListRes>();
            foreach (var cableProductionOrder in list)
            {
                var cableProductionOrderRes = BeanUtil.Copy<Cable_ProductionOrderListRes>(cableProductionOrder);
                if (orderMap.ContainsKey(cableProductionOrder.OrderId))
                {
                    var orderInfo = orderMap[cableProductionOrder.OrderId];
                    cableProductionOrderRes.ContractNo = orderInfo.ContractNo;
                    cableProductionOrderRes.CustomerName = orderInfo.CustomerName;
                    cableProductionOrderRes.BatchNum = orderInfo.BatchNum;
                    cableProductionOrderRes.CustomerOrderNum = orderInfo.CustomerOrderNum;
                    cableProductionOrderRes.ProductionNo = orderInfo.ProductionNo;
                }
                resList.Add(cableProductionOrderRes);
            }
            return resList;
        }

        /**
         * 创建计划订单
         */
        public ResponseData CreatePlanOrder(List<string> ids, DateTime assembleDate, Sys_User currentUser)
        {
            var list = GetList(t => ids.Contains(t.Id)).ToList();

            var count = list.Count(t => t.Status != 1);
            if (count > 0)
            {
                return new ResponseData((int)WMSStatusCode.Failed, "请检查提交数据 无法重复创建计划订单");
            }

            var orderMap = list.GroupBy(t => t.ContractNo + t.CustomerOrderNum)
                .ToDictionary(t => t.Key, t => t.ToList());
            foreach (var orderMapKey in orderMap.Keys)
            {
                var cableProduction = orderMap[orderMapKey][0];
                var cableProductionList = orderMap[orderMapKey];

                foreach (var cableProductionOrder in cableProductionList)
                {
                    //SAP调用参数
                    ZFITFG001018 param = new ZFITFG001018();
                    param.MATERIAL = cableProduction.PartCode;
                    param.PROD_PLANT = "2020";
                    param.PLAN_PLANT = "2020";
                    param.TOTAL_PLORD_QTY = cableProduction.Quantity.ToString();
                    param.ORDER_START_DATE = assembleDate.ToString("yyyyMMdd");
                    param.ORDER_FIN_DATE = assembleDate.ToString("yyyyMMdd");
                    param.VERSION = "0001";
                    // param.BASE_UOM = cableProduction.Unit;
                    // if (param.BASE_UOM == "PC")
                    // {
                    //     param.BASE_UOM = "ST";
                    // }

                    param.BASE_UOM = "";

                    param.SALES_ORD = cableProduction.SapNo;
                    param.S_ORD_ITEM = cableProduction.SapLine.ToString();
                    param.MANUAL_COMPONENT = "1";
                    param.FIRMING_IND = "X";
                    param.PLDORD_PROFILE = "KD";
                    param.ACCTASSCAT = "M";
                    List<ZFITFG001018_ITEM> items = new List<ZFITFG001018_ITEM>();

                    //更改参数
                    cableProductionOrder.Status = 2;
                    cableProductionOrder.PlanCTime = DateTime.Now;
                    cableProductionOrder.MTime = DateTime.Now;
                    cableProductionOrder.MUser = currentUser.LoginAccount;

                    ZFITFG001018_ITEM item = new ZFITFG001018_ITEM();
                    item.PLANT = "2020";
                    item.MATERIAL = cableProductionOrder.ComponentCode;
                    item.ENTRY_QTY = cableProductionOrder.Quantity.ToString();
                    item.ENTRY_UOM = cableProductionOrder.ComponentUnit;
                    if (item.ENTRY_UOM == "PC")
                    {
                        item.ENTRY_UOM = "ST";
                    }

                    items.Add(item);

                    param.itmes = items;
                    var res = _sap.ZFITFG001018("001", param, out bool isPosted, out string rstMessage);
                    if (isPosted)
                    {
                        cableProductionOrder.PlanNo = res.PLNUM;
                        cableProductionOrder.PlanStatus = res.PTYPE;
                        cableProductionOrder.PlanMessage = res.PMSG;
                        cableProductionOrder.PlanCTime = DateTime.Now;
                        cableProductionOrder.PlanCUser = currentUser.LoginAccount;
                        cableProductionOrder.AssembleDate = assembleDate;
                    }
                    else
                    {
                        return new ResponseData((int)WMSStatusCode.Failed, rstMessage);
                    }

                    Update(cableProductionList);
                }
            }

            return new ResponseData((int)WMSStatusCode.Success, "创建成功");
        }

        /**
         * 计划转生产订单
         */
        public ResponseData PlanConvertProduceOrder(List<string> ids, Sys_User currentUser)
        {
            var list = GetList(t => ids.Contains(t.Id)).ToList();
            var count = list.Count(t => t.Status != 2);
            if (count > 0)
            {
                return new ResponseData((int)WMSStatusCode.Failed, "请检查提交数据 无法重复转换生产订单");
            }

            List<ZFITFG001019> reqs = new List<ZFITFG001019>();
            foreach (var cableProductionOrder in list)
            {
                //整理过账数据
                ZFITFG001019 req = new ZFITFG001019();
                req.PLNUM = cableProductionOrder.PlanNo;
                req.AUART = "ZP01";
                req.MATERIAL = cableProductionOrder.MaterialCode;
                req.SALES_ORD = cableProductionOrder.SapNo;
                req.S_ORD_ITEM = cableProductionOrder.SapLine.ToString();
                req.PLANSTATUS = cableProductionOrder.PlanStatus;
                req.PLAMMSG = cableProductionOrder.PlanMessage;
                reqs.Add(req);
            }

            var res = _sap.ZFITFG001019("001", reqs, out bool isPosted, out string rstMessage);
            if (isPosted)
            {
                // 设置生产订单参数
                var resMap = res.GroupBy(t => t.PLNUM).ToDictionary(t => t.Key, t => t.FirstOrDefault());
                foreach (var cableProductionOrder in list)
                {
                    if (resMap.ContainsKey(cableProductionOrder.PlanNo))
                    {
                        var resParam = resMap[cableProductionOrder.PlanNo];
                        //设置返回参数
                        cableProductionOrder.ProduceNo = resParam.AUFNR;
                        cableProductionOrder.ProduceStatus = resParam.PTYPE;
                        cableProductionOrder.ProduceMessage = resParam.PMSG;

                        //设置过账后数据
                        cableProductionOrder.Status = 3;
                        cableProductionOrder.ProduceCTime = DateTime.Now;
                        cableProductionOrder.ProduceCUser = currentUser.LoginAccount;
                        cableProductionOrder.MTime = DateTime.Now;
                        cableProductionOrder.MUser = currentUser.LoginAccount;
                    }
                }

                Update(list);
                return new ResponseData((int)WMSStatusCode.Success, "转换成功");
            }
            return new ResponseData((int)WMSStatusCode.Failed, rstMessage);
        }
        
        /**
         * 计划订单确认
         */
        public ResponseData ProduceOrderConfirm(List<string> ids, Sys_User currentUser)
        {
            var list = GetList(t => ids.Contains(t.Id)).ToList();
            var count = list.Count(t => t.Status != 3);
            if (count > 0)
            {
                return new ResponseData((int)WMSStatusCode.Failed, "请检查提交数据 无法重复确认生产订单");
            }
            List<string> produceNos = new List<string>();
            foreach (var cableProductionOrder in list)
            {
                produceNos.Add(cableProductionOrder.ProduceNo);
            }
            
            var res = _sap.ZFITFG001011("001", produceNos, out bool isPosted, out string rstMessage);

            if (isPosted)
            {
                foreach (var cableProductionOrder in list)
                {
                    cableProductionOrder.Status = 4;
                    cableProductionOrder.ProduceReleaseMessage = res.EMSG;
                    cableProductionOrder.ProduceReleaseStatus = res.ETYPE;
                    cableProductionOrder.ProduceReleaseCTime = DateTime.Now;
                    cableProductionOrder.ProduceReleaseCUser = currentUser.LoginAccount;
                }
                Update(list);
                return new ResponseData((int)WMSStatusCode.Success, "确认成功");
            }
            else
            {
                return new ResponseData((int)WMSStatusCode.Failed, rstMessage);
            }
            
        }

        /**
         * 序列号分配
         */
        public ResponseData DispenseSerialNo(List<string> ids)
        {
            var list = GetList(t => ids.Contains(t.Id)).ToList();
            var count = list.Count(t => t.Status != 3);
            if (count > 0)
            {
                return new ResponseData((int)WMSStatusCode.Failed, "请检查提交数据 无法重复分配序列号");
            }

            var nums = _baseApp.GetNewDocNums(DocType.CS, DocFixedNumDef.SerialNumber, new DateTime(), ids.Count, 5);
            foreach (var cableProductionOrder in list)
            {
                //设置序列号参数
                cableProductionOrder.SerialNo = nums[0];
                cableProductionOrder.SerialNoCtime = DateTime.Now;
                cableProductionOrder.Status = 4;
                nums.RemoveAt(0);
            }

            Update(list);
            return new ResponseData((int)WMSStatusCode.Success, "分配完成");
        }

        /**
         * 取消序列号分配
         */
        public ResponseData UnDispenseSerialNo(List<string> ids, Sys_User currentUser)
        {
            var list = GetList(t => ids.Contains(t.Id)).ToList();
            var count = list.Select(t => t.Status == 3).ToList().Count;
            if (count != ids.Count)
            {
                return new ResponseData((int)WMSStatusCode.Failed, "请检查提交数据 无法重复取消分配");
            }

            foreach (var cableProductionOrder in list)
            {
                cableProductionOrder.Status = 3;
                cableProductionOrder.SerialNo = null;
                cableProductionOrder.SerialNoCtime = null;
                cableProductionOrder.MTime = DateTime.Now;
                cableProductionOrder.MUser = currentUser.LoginAccount;
            }

            Update(list);
            return new ResponseData((int)WMSStatusCode.Success, "转换成功");
        }

        /**
         * 生产报工
         */
        public ResponseData ProduceReportWork(List<string> ids, DateTime assembleDate, Sys_User currentUser)
        {
            var list = GetList(t => ids.Contains(t.Id)).ToList();
            var count = list.Count(t => t.Status != 4);
            if (count > 0)
            {
                return new ResponseData((int)WMSStatusCode.Failed, "请检查提交数据 无法重复报工");
            }

            foreach (var cableProductionOrder in list)
            {
                ZFGWMS017 req = new ZFGWMS017();
                req.ACTID = "I";
                req.AUFNR = cableProductionOrder.ProduceNo;
                req.BUDAT = assembleDate.ToString("yyyy-MM-dd");
                req.LTXA1 = (req.AUFNR + req.LTXA1).Replace("-", "");
                req.GMNGA = "1";
                req.XMNGA = "0";
                var res = _sap.ZFGWMS017("001", req, out bool isPosted, out string rstMessage);
                if (isPosted)
                {
                    //设置返回参数
                    cableProductionOrder.ReportingWorkDate = assembleDate;
                    cableProductionOrder.ReportingWorkMessage = res.ZMESSAGE;
                    cableProductionOrder.ReportingWorkStatus = res.ZTYPE;

                    //更改 报工信息
                    cableProductionOrder.Status = 5;
                    cableProductionOrder.ReportingCTime = DateTime.Now;
                    cableProductionOrder.ReportingCUser = currentUser.LoginAccount;
                    cableProductionOrder.MTime = DateTime.Now;
                    cableProductionOrder.MUser = currentUser.LoginAccount;
                    Update(cableProductionOrder);
                }
                else
                {
                    return new ResponseData((int)WMSStatusCode.Failed, rstMessage);
                }
            }
            return new ResponseData((int)WMSStatusCode.Success, "转换成功");
        }

        public ResponseData UnProduceReportWork(List<string> ids, Sys_User getCurrentUser)
        {
            var list = GetList(t => ids.Contains(t.Id)).ToList();
            var count = list.Select(t => t.Status == 5).ToList().Count;
            if (count != list.Count)
            {
                return new ResponseData((int)WMSStatusCode.Failed, "请检查提交数据 无法重复取消");
            }

            foreach (var cableProductionOrder in list)
            {
                _sap.ZFITFG001021("001", cableProductionOrder.ProduceNo, out bool isPosted, out string rstMessage);
                if (isPosted)
                {
                    cableProductionOrder.Status = 4;
                    cableProductionOrder.ReportingWorkMessage = "已取消";
                    cableProductionOrder.ReportingCTime = null;
                    cableProductionOrder.ReportingWorkDate = null;
                    cableProductionOrder.ReportingWorkStatus = null;
                    cableProductionOrder.MTime = DateTime.Now;
                    cableProductionOrder.MUser = getCurrentUser.LoginAccount;
                    Update(cableProductionOrder);
                }
                else
                {
                    return new ResponseData((int)WMSStatusCode.Failed, "过账失败");
                }
            }
            return new ResponseData((int)WMSStatusCode.Success, "取消成功");
        }
    }
}