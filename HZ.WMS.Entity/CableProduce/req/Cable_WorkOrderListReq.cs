using System;
using System.ComponentModel;
using HZ.Core.Utilities;
using HZ.WMS.Entity;

namespace AOS.WMS.Entity.CableProduce
{
    public class Cable_WorkOrderListReq : BaseEntity
    {

        /// <summary>
        /// 订单编号
        /// </summary>
        [Description("订单编号")]
        public string OrderNum { get; set; }

        /// <summary>
        /// 客户编号
        /// </summary>
        [Description("客户编号")]
        public string CustomerCode { get; set; }

        /// <summary>
        /// 客户名称
        /// </summary>
        [Description("客户名称")]
        public string CustomerName { get; set; }

        /// <summary>
        /// 顺序号
        /// </summary>
        [Description("顺序号")]
        public string SerialNo { get; set; }
        
        /// <summary>
        /// 扫描状态
        /// </summary>
        [Description("扫描状态")]
        public int? ScanStatus { get; set; }
        
        /// <summary>
        /// 井道扫描状态
        /// </summary>
        [Description("井道扫描状态")]
        public int? HoistwayStatus { get; set; }
        
        /// <summary>
        /// 订单状态
        /// </summary>
        [Description("订单状态")]
        public int Status { get; set; }

        /// <summary>
        /// 订单类型(1:主机订单 2:部件订单 3:售后部件 4:售后主机)
        /// </summary>
        [Description("订单类型")]
        public int? OrderType { get; set; }

        /// <summary>
        /// 发货日期
        /// </summary>
        [Description("发货日期")]
        public DateTime[] DeliveryDate { get; set; }

        /// <summary>
        /// 发运日期
        /// </summary>
        [Description("发运日期")]
        public DateTime[] ShipmentDate { get; set; }

        /// <summary>
        /// 同步SAP销售单号
        /// </summary>
        [Description("同步SAP销售单号")]
        public string SapNo { get; set; }
        
        /// <summary>
        /// 合同号
        /// </summary>
        [Description("合同号")]
        public string ContractNo { get; set; }

        /// <summary>
        /// 客户订单号
        /// </summary>
        [Description("客户订单号")]
        public string CustomerOrderNum { get; set; }

        /// <summary>
        /// 货币
        /// </summary>
        [Description("货币")]
        public string HuoBi { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [Description("创建时间")]
        public DateTime[] CreateDate { get; set; }

        /// <summary>
        /// 工单打印时间
        /// </summary>
        [Description("工单打印时间")]
        public int? WorkOrderPrintStatus { get; set; }

        /// <summary>
        /// 标签打印状态
        /// </summary>
        [Description("标签打印状态")]
        public int? LabelPrintStatus { get; set; }

        /// <summary>
        /// 标签打印状态
        /// </summary>
        [Description("标签打印状态")]
        public int? ShippingMarkPrintStatus { get; set; }
        
        /// <summary>
        /// 订单类型名称
        /// </summary>
        [Description("订单类型名称")]
        public string OrderTypeName { get; set; }
        
        /// <summary>
        /// 电梯类型
        /// </summary>
        [Description("电梯类型")]
        public string ElevatorType { get; set; }
        

    }
}