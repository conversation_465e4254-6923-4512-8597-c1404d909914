using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.SD.Parameters
{
    /// <summary>
    /// 托运单接收参数的类
    /// </summary>
    public class SD_ConsignmentNoteParameters
    {
        /// <summary>
        /// 单据号
        /// </summary>
        public string DocNum { get; set; }

        /// <summary>
        /// 发货时间
        /// </summary>
        public DateTime DeliveryDate { get; set; }

        /// <summary>
        /// 托运部门
        /// </summary>
        public string ShippingDepar { get; set; }

        /// <summary>
        /// 成本中心
        /// </summary>
        public string CostCenter { get; set; }

        /// <summary> 
        /// 物流供应商编号
        /// </summary> 
        public string LogisticsSupplierCode { get; set; }

        /// <summary> 
        /// 物流供应商名称
        /// </summary> 
        public string LogisticsSupplierName { get; set; }

        /// <summary> 
        /// 运输方式
        /// </summary> 
        public string ShippingType { get; set; }

        /// <summary> 
        /// 托运人
        /// </summary> 
        public string Shipper { get; set; }

        /// <summary> 
        /// 车牌
        /// </summary> 
        public string CarNum { get; set; }

        /// <summary> 
        /// 特殊费用
        /// </summary> 
        public decimal SpecialExpenses { get; set; }

        /// <summary> 
        /// 总理论费用
        /// </summary> 
        public decimal? TotalTheoreticalAmount { get; set; }

        /// <summary> 
        /// 总重量
        /// </summary> 
        public decimal? TotalWeight { get; set; }

        /// <summary> 
        /// 客户ID
        /// </summary> 
        public string CustomerID { get; set; }

        /// <summary> 
        /// 客户编号
        /// </summary> 
        public string CustomerCode { get; set; }

        /// <summary> 
        /// 客户名称
        /// </summary> 
        public string CustomerName { get; set; }

        /// <summary> 
        /// 客户地址
        /// </summary> 
        public string CustomerAdd { get; set; }

        /// <summary> 
        /// 结算地址
        /// </summary> 
        public string CustomerRegion { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }

        /// <summary>
        /// 明细数组
        /// </summary>
        public string[] deletedetail { get; set; }

        /// <summary>
        /// 主信息
        /// </summary>
        public List<SD_ConsignmentNote> entities { get; set; }

        /// <summary>
        /// 明细信息
        /// </summary>
        public List<SD_ConsignmentNoteDetail> detailed { get; set; }
    }
}
