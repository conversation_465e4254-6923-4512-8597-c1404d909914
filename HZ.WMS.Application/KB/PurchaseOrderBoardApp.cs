using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HZ.WMS.Entity;

namespace HZ.WMS.Application.KB
{
    /// <summary>
    /// 采购看板
    /// </summary>
    public class PurchaseOrderBoardApp : BaseApp<BaseEntity>
    {
        
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public object GetPurchaseDeliveryData()
        {
            return base.SqlQuery("PROC_BRD_PurchaseDelivery", CommandType.StoredProcedure);

        }
    }
}
