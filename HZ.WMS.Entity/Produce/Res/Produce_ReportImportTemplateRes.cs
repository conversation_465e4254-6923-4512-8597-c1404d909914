using System;
using System.ComponentModel;

namespace HZ.WMS.Entity.Produce.Res
{
    /// <summary>
    /// 生产报工导入模板响应
    /// </summary>
    public class Produce_ReportImportTemplateRes
    {
        /// <summary>
        /// 生产报工单号
        /// </summary>
        [Description("生产报工单号")]
        public string ProduceReportNo { get; set; }

        /// <summary>
        /// 出厂编号
        /// </summary>
        [Description("出厂编号")]
        public string SerialNo { get; set; }

        /// <summary>
        /// 生产订单号
        /// </summary>
        [Description("生产订单号")]
        public string ProduceOrderNo { get; set; }

        /// <summary>
        /// 主机生产订单号
        /// </summary>
        [Description("主机生产订单号")]
        public string HostProduceOrderNo { get; set; }

        /// <summary>
        /// 物料编码
        /// </summary>
        [Description("物料编码")]
        public string MaterialNo { get; set; }

        /// <summary>
        /// 物料描述
        /// </summary>
        [Description("物料描述")]
        public string MaterialName { get; set; }

        /// <summary>
        /// 订单数量
        /// </summary>
        [Description("订单数量")]
        public decimal? OrderQty { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [Description("单位")]
        public string Unit { get; set; }

        /// <summary>
        /// 报工数量
        /// </summary>
        [Description("报工数量")]
        public decimal? ReportTotal { get; set; }

        /// <summary>
        /// 合格数量
        /// </summary>
        [Description("合格数量")]
        public decimal? QualifiedQty { get; set; }

        /// <summary>
        /// 不合格数量
        /// </summary>
        [Description("不合格数量")]
        public decimal? UnqualifiedQty { get; set; }

        /// <summary>
        /// 不合格备注
        /// </summary>
        [Description("不合格备注")]
        public string UnqualifiedRemarks { get; set; }

        /// <summary>
        /// 装配日期
        /// </summary>
        [Description("装配日期")]
        public DateTime? AssemblDate { get; set; }

        /// <summary>
        /// 工作中心编码
        /// </summary>
        [Description("工作中心编码")]
        public string WorkCenterCode { get; set; }

        /// <summary>
        /// 工作中心名称
        /// </summary>
        [Description("工作中心名称")]
        public string WorkCenterName { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Description("备注")]
        public string Remark { get; set; }
    }
} 