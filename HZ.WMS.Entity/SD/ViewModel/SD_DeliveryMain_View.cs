using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.SD.ViewModel

{
    /// <summary>
    /// 销售发货主信息
    /// </summary>
    public class SD_DeliveryMain_View
    {

        /// <summary> 
        /// wms单号
        /// </summary> 
        [Description("wms单号")]
        public string DocNum { get; set; }

        /// <summary> 
        /// 客户编号
        /// </summary> 
        [Description("客户编号")]
        public string CustomerCode { get; set; }

        /// <summary> 
        /// 客户名称
        /// </summary> 
        [Description("客户名称")]
        public string CustomerName { get; set; }

        /// <summary> 
        /// 发货仓库
        /// </summary> 
        [Description("发货仓库")]
        public string WhsCode { get; set; }
        
        /// <summary> 
        /// 客户地址
        /// </summary> 
        [Description("客户地址")]
        public string CustomerAdd { get; set; }

        /// <summary> 
        /// 销售订单类型
        /// </summary> 
        [Description("销售订单类型")]
        public string BaseType { get; set; }

        /// <summary> 
        /// 销售订单
        /// </summary> 
        [Description("销售订单")]
        public string BaseNum { get; set; }

        /// <summary> 
        /// 销售订单行号
        /// </summary> 
        [Description("销售订单行号")]
        public int BaseLine { get; set; }

        /// <summary> 
        /// 数量
        /// </summary> 
        [Description("数量")]
        public decimal? DeliveryScanQty { get; set; }

        /// <summary> 
        /// 结算地址
        /// </summary> 
        [Description("结算地址")]
        public string SettlementAdd { get; set; }

        /// <summary> 
        /// 是否已过账
        /// </summary> 
        [Description("是否已过账")]
        public bool? IsPosted { get; set; }

        /// <summary>
        /// SAP过账标示
        /// </summary>
        [Description("SAP过账标示")]
        public string SAPmark { get; set; }

        /// <summary>
        /// 插入记录的用户Id
        /// </summary>
        [Description("创建用户")]
        public string CUser { get; set; }

        /// <summary>
        /// 插入记录的服务器时间
        /// </summary>
        [Description("创建时间")]
        public DateTime? CTime { get; set; }

        /// <summary>
        /// 交货时间
        /// </summary>
        [Description("交货时间")]
        public DateTime? DeliveryDate { get; set; }

    }
}
