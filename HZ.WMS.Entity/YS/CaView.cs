using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations.Schema;
using SqlSugar;

namespace HZ.WMS.Entity.Produce
{
    /// <summary>
    /// Ca视图
    /// </summary>
    [SugarTable("CLOUDPIVOT.CaView")]
    public class CaView
    {
        /// <summary>
        /// 主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, ColumnName = "ID")]
        public string Id { get; set; }
        
        /// <summary>
        /// 更改类型
        /// </summary>
        [SugarColumn(ColumnName = "CHANGE_TYPE")]
        public string ChangeType { get; set; }
        
        /// <summary>
        /// 会评完成时间
        /// </summary>
        [SugarColumn(ColumnName = "MEETING_COMPLETE_DATE")]
        public DateTime? MeetingCompleteDate { get; set; }

        /// <summary>
        /// 完成日期
        /// </summary>
        [Description("完成日期")]
        [SugarColumn(ColumnName = "COMPLETE_DATE")]
        public DateTime? CompleteDate { get; set; }

        /// <summary>
        /// CA流程编号
        /// </summary>
        [Description("CA流程编号")]
        [SugarColumn(ColumnName = "SEQUENCE_NO")]
        public string SequenceNo { get; set; }

        /// <summary>
        /// 物料代码
        /// </summary>
        [Description("物料代码")]
        [SugarColumn(ColumnName = "MATERIAL_CODE")]
        public string MaterialCode { get; set; }

        /// <summary>
        /// CA状态 
        /// </summary>
        [Description("CA状态")]
        [SugarColumn(ColumnName = "CA_STATUS")]
        public int? CaStatus { get; set; }
    }
}