using System.Collections.Generic;
using System.ComponentModel;
using SqlSugar;
//Chloe框架

namespace HZ.WMS.Entity.Sys
{
    /// <summary>
    /// 资源表
    /// </summary>
    [SugarTable("Sys_Resource")]
    public class Sys_Resource:BaseEntity
    {

        /// <summary>
        /// 资源ID
        /// </summary>
        [Description("资源ID")]
        [SugarColumn(IsPrimaryKey = true)]
        public string ResourceID { get; set; }
        /// <summary>
        /// 上级资源ID
        /// </summary>
        [Description("上级资源ID")]
        public string FathResourceID { get; set; }
        /// <summary>
        /// 
        /// </summary>
        [Description("")]
        public string ResourceTitle { get; set; }
        /// <summary>
        /// 资源名称
        /// </summary>
        [Description("资源名称")]
        public string ResourceName { get; set; }
        /// <summary>
        /// 资源类型
        /// </summary>
        [Description("资源类型")]
        public int? ResourceType { get; set; }
        /// <summary>
        /// 跳转
        /// </summary>
        [Description("跳转")]
        public string RedirectUrl { get; set; }
        /// <summary>
        /// 资源路径
        /// </summary>
        [Description("资源路径")]
        public string ResourcePath { get; set; }
        /// <summary>
        /// 资源层级
        /// </summary>
        [Description("资源层级")]
        public int? ResourceLevel { get; set; }
        /// <summary>
        /// 图标
        /// </summary>
        [Description("图标")]
        public string ResourceIcon { get; set; }
        /// <summary>
        /// 是否缓存
        /// </summary>
        [Description("是否缓存")]
        public bool? IsCache { get; set; }

        /// <summary>
        /// 是否隐藏
        /// </summary>
        public bool? Hidden { get; set; }

        /// <summary>
        /// 引用组件
        /// </summary>
        [Description("引用组件")]
        public string Component { get; set; }
        /// <summary>
        /// 布局
        /// </summary>
        [Description("布局")]
        public string Layout { get; set; }
        /// <summary>
        /// IsDeveloperResource
        /// </summary>
        [Description("IsDeveloperResource")]
        public string Role { get; set; }
        /// <summary>
        /// 
        /// </summary>
        [Description("")]
        public bool? IsDeveloperResource { get; set; }
        /// <summary>
        /// 所属客户端
        /// </summary>
        [Description("所属客户端")]
        public string AppID { get; set; }

        /// <summary>
        /// 排序字段
        /// </summary>
        public int SortNo { get; set; }

    }

    /// <summary>
    /// 资源类型
    /// </summary>
    public enum ResourceType
    {
        /// <summary>
        /// 模块
        /// </summary>
        Module = 1,

        /// <summary>
        /// 菜单
        /// </summary>
        Menu = 2,

        /// <summary>
        /// 按钮
        /// </summary>
        Button =3

    }

    /// <summary>
    /// 
    /// </summary>
    public class Sys_RecourceComparer: IEqualityComparer<Sys_Resource>
    {
        public bool Equals(Sys_Resource x, Sys_Resource y)
        {
            if (x == null || y == null)
                return false;
            if (x.ResourceID == y.ResourceID)
                return true;
            else
                return false;
        }

        public int GetHashCode(Sys_Resource obj)
        {
            if (obj == null)
                return 0;
            else
                return obj.ResourceID.GetHashCode();
        }
}

}

