using System.Collections.Generic;
using System.ComponentModel;
using HZ.WMS.Entity;
using SqlSugar;

namespace AOS.OMS.Entity.Basic
{
    /// <summary>
    /// 客户件号属性
    /// </summary>
    [SugarTable("Cable_Basic_Customer_Part")]
    public class CustomerPart : BaseEntity
    {
        
        /// <summary>
        /// ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Description("Id")]
        public string Id { get; set; }
        
        /// <summary>
        /// 物料编码
        /// </summary>
        [Description("物料编码")]
        public string MaterialCode { get; set; }
        
        /// <summary>
        /// 客户件号
        /// </summary>
        [Description("客户件号")]
        public string PartNo { get; set; }
        
        /// <summary>
        /// 物料描述
        /// </summary>
        [Description("物料描述")]
        public string MaterialDesc { get; set; }
        
        /// <summary>
        /// 规格型号
        /// </summary>
        [Description("规格型号")]
        public string SpecificationModel { get; set; }
        
        /// <summary>
        /// 单根数量
        /// </summary>
        [Description("单根数量")]
        public string SingleNo { get; set; }
        
        /// <summary>
        /// 分类
        /// </summary>
        [Description("分类")]
        public string Classify { get; set; }
        
        /// <summary>
        /// 是否装箱件
        /// </summary>
        [Description("是否装箱件")]
        public string IsBoxedItem { get; set; }
        
        /// <summary>
        /// 排序
        /// </summary>
        [Description("排序")]
        public string Sort { get; set; }
        
        /// <summary>
        /// 重量
        /// </summary>
        [Description("重量")]
        public string Weight { get; set; }
        
        /// <summary>
        /// 主件号
        /// </summary>
        [Description("主件号")]
        public string MainPart { get; set; }
        
        /// <summary>
        /// 客户件号属性列表
        /// </summary>
        [Description("客户件号属性列表")]
        [SugarColumn(IsIgnore = true)]
        public List<CustomerPartParams> CustomerPartParamsList { get; set; }
        
    }
}