using System;
using System.ComponentModel;
using SqlSugar;

namespace HZ.WMS.Entity.SD
{
    /// <summary>
    /// 发运计划
    /// </summary>
    [SugarTable("Sale_ShippingPlan")]
    public class Sale_ShippingPlan : BaseEntity
    {
        
        /// <summary> 
        /// ID
        /// </summary> 
        [SugarColumn(IsPrimaryKey = true)]
        [Description("ID")]
        public string Id { get; set; }
        
        /// <summary> 
        /// Pid
        /// </summary> 
        [Description("Pid")]
        public string Pid { get; set; }
        
        /// <summary> 
        /// wms单号
        /// </summary> 
        [Description("发运单号")]
        public string DocNum { get; set; }
        
        /// <summary> 
        /// wms行号
        /// </summary> 
        [Description("发运行号")]
        public int? DocLine { get; set; }
        
        /// <summary> 
        /// 订单类型
        /// </summary> 
        [Description("订单类型")]
        public string OrderType { get; set; }
        
        /// <summary> 
        /// Sap订单号
        /// </summary> 
        [Description("Sap订单号")]
        public string SaleSapNo { get; set; }
        
        /// <summary> 
        /// Sap行号
        /// </summary> 
        [Description("Sap行号")]
        public int? SaleSapLine { get; set; }
        
        /// <summary> 
        /// 客户订单号
        /// </summary> 
        [Description("客户订单号")]
        public string CustomerOrderNo { get; set; }
        
        /// <summary> 
        /// 合同号
        /// </summary> 
        [Description("合同号")]
        public string ContractNo { get; set; }
        
        /// <summary> 
        /// 出厂编号
        /// </summary> 
        [Description("出厂编号")]
        public string FactoryNo { get; set; }
        
        /// <summary> 
        /// 物料编号
        /// </summary> 
        [Description("物料编号")]
        public string ItemCode { get; set; }

        /// <summary> 
        /// 物料名称
        /// </summary> 
        [Description("物料名称")]
        public string ItemName { get; set; }

        /// <summary> 
        /// 客户件号
        /// </summary> 
        [Description("客户件号")]
        public string CustomerItemCode { get; set; }
        
        /// <summary> 
        /// 客户编号
        /// </summary> 
        [Description("客户编号")]
        public string CustomerCode { get; set; }

        /// <summary> 
        /// 客户名称
        /// </summary> 
        [Description("客户名称")]
        public string CustomerName { get; set; }
        
        /// <summary> 
        /// 客户地址
        /// </summary> 
        [Description("客户地址")]
        public string CustomerAdd { get; set; }

        /// <summary>
        /// 结算地址
        /// </summary>
        [Description("结算地址")]
        public string SettlementAdd { get; set; }
        
        /// <summary> 
        /// 数量
        /// </summary> 
        [Description("数量")]
        public decimal? Quantity { get; set; }
        
        /// <summary> 
        /// 单位
        /// </summary> 
        [Description("单位")]
        public string Unit { get; set; }
        
        /// <summary> 
        /// 重量
        /// </summary> 
        [Description("重量")]
        public decimal? Weight { get; set; }
        
        /// <summary> 
        /// 项目名称
        /// </summary> 
        [Description("项目名称")]
        public string ProjectName { get; set; }
        
        /// <summary> 
        /// 销售交货批
        /// </summary> 
        [Description("销售交货批")]
        public string BatchNum { get; set; }
        
        /// <summary>
        /// 发货方式
        /// </summary>
        [Description("发货方式")]
        public string ShippingType { get; set; }
        
        /// <summary> 
        /// 客户参考
        /// </summary> 
        [Description("客户参考")]
        public string ShippingReference { get; set; }
        
        /// <summary>
        /// 发货人编号
        /// </summary>
        [Description("发货人编号")]
        public string DeliveryAccount { get; set; }
        
        /// <summary>
        /// 交货设置时间
        /// </summary>
        [Description("交货设置时间")]
        public DateTime? DeliverySetTime { get; set; }

        /// <summary>
        /// 发货人名称
        /// </summary>
        [Description("发货人名称")]
        public string DeliveryUserName { get; set; }
        
        /// <summary>
        /// 物流供应商编号
        /// </summary>
        [Description("物流供应商编号")]
        public string SupplierCode { get; set; }

        /// <summary>
        /// 物流供应商名称
        /// </summary>
        [Description("物流供应商名称")]
        public string SupplierName { get; set; }

        /// <summary>
        /// 物流联系人
        /// </summary>
        [Description("物流联系人")]
        public string Contact { get; set; }

        /// <summary>
        /// 物流联系电话
        /// </summary>
        [Description("物流联系电话")]
        public string Telephone { get; set; }
        
        /// <summary> 
        /// 销售组织
        /// </summary> 
        [Description("销售组织")]
        public string SaleOrganization { get; set; }
        
        /// <summary> 
        /// 利润中心
        /// </summary> 
        [Description("利润中心")]
        public string ProfitCenter { get; set; }
        
        /// <summary> 
        /// 销售类型
        /// </summary> 
        [Description("销售类型")]
        public string SaleType { get; set; }
        
        /// <summary> 
        /// 产品组
        /// </summary> 
        [Description("产品组")]
        public string ProductGroup { get; set; }
        
        /// <summary> 
        /// 分销渠道
        /// </summary> 
        [Description("分销渠道")]
        public string DistributionChannels { get; set; }
        
        /// <summary> 
        /// 项目类别
        /// </summary> 
        [Description("项目类别")]
        public string ProjectCategory { get; set; }
        
        /// <summary>
        /// 装运点/收货点
        /// </summary>
        [Description("装运点/收货点")]
        public string ShipmentPoint { get; set; }
        
        /// <summary> 
        /// 凭证日期
        /// </summary> 
        [Description("凭证日期")]
        public DateTime? VoucherDate { get; set; }
        
        /// <summary> 
        /// 要求发运日期
        /// </summary> 
        [Description("要求发运日期")]
        public DateTime? DeliveryDate { get; set; }

        /// <summary>
        /// 计划入库时间
        /// </summary>
        [Description("计划入库时间")]
        public DateTime? PlanStockTime { get; set; }

        /// <summary>
        /// 实际入库时间
        /// </summary>
        [Description("实际入库时间")]
        public DateTime? ActualStockTime { get; set; }
        
        /// <summary>
        /// 生产备注
        /// </summary>
        [Description("生产备注")]
        public string ProductionRemark { get; set; }

        /// <summary> 
        /// 仓库编号
        /// </summary> 
        [Description("仓库编号")]
        public string WhsCode { get; set; }

        /// <summary> 
        /// 仓库名称
        /// </summary> 
        [Description("仓库名称")]
        public string WhsName { get; set; }
        
        /// <summary>
        /// 参考单据的单据编号
        /// </summary>
        [Description("参考单据的单据编号")]
        public string ReferenceDocNo { get; set; }

        /// <summary>
        /// 参考项目的项目号
        /// </summary>
        [Description("参考项目的项目号")]
        public int? ReferenceDocLine { get; set; }

        /// <summary>
        /// 是否交货导入
        /// </summary>
        [Description("是否交货导入")]
        public int? IsDeliveryImport { get; set; }

        /// <summary>
        /// 是否下载 0否 1是
        /// </summary>
        [Description("是否下载")]
        public int? IsDownload { get; set; }

        /// <summary>
        /// 状态 0-未发运 1-已同步 2-发运中 3-已完成
        /// </summary>
        [Description("状态")]
        public int? Status { get; set; }

        /// <summary>
        /// 状态 0-未同步 1-已同步
        /// </summary>
        [Description("SRM同步状态")]
        public int? SrmStatus { get; set; }
        
        /// <summary> 
        /// 物料组编号
        /// </summary> 
        [Description("物料组编号")]
        public string ItemGroupCode { get; set; }

        /// <summary> 
        /// 物料组名称
        /// </summary> 
        [Description("物料组名称")]
        public string ItemGroupName { get; set; }
        
        /// <summary> 
        /// 区域编号
        /// </summary> 
        [Description("区域编号")]
        public string RegionCode { get; set; }

        /// <summary> 
        /// 区域名称
        /// </summary> 
        [Description("区域名称")]
        public string RegionName { get; set; }

        /// <summary> 
        /// 库位编号
        /// </summary> 
        [Description("库位编号")]
        public string BinLocationCode { get; set; }

        /// <summary> 
        /// 库位名称
        /// </summary> 
        [Description("库位名称")]
        public string BinLocationName { get; set; }
        
        /// <summary> 
        /// 是否已装箱单
        /// </summary> 
        [Description("是否已装箱单")]
        public bool? IsDelivery { get; set; }
        
        /// <summary> 
        /// 手动过账时间
        /// </summary> 
        [Description("手动过账时间")]
        public DateTime? ManualPostTime { get; set; }
        
        /// <summary> 
        /// 是否已过账
        /// </summary> 
        [Description("是否已过账")]
        public bool? IsPosted { get; set; }
        
        /// <summary> 
        /// 过账人
        /// </summary> 
        [Description("过账人")]
        public string PostUser { get; set; }

        /// <summary> 
        /// 过账时间
        /// </summary> 
        [Description("过账时间")]
        public DateTime? PostTime { get; set; }
        
        /// <summary>
        /// SapNo
        /// </summary>
        [Description("SapNo")]
        public string SapNo { get; set; }

        /// <summary>
        /// Sap行号
        /// </summary>
        [Description("Sap行号")]
        public int? SapLine { get; set; }
        
        /// <summary>
        /// 公司代码
        /// </summary>
        [Description("公司代码")]
        public string CompanyCode { get; set; }

        /// <summary>
        /// 工厂代码
        /// </summary>
        [Description("工厂代码")]
        public string FactoryCode { get; set; }
        
        /// <summary>
        /// 项目状态
        /// </summary>
        [Description("项目状态")]
        public string ProjectStatus { get; set; }
        
        /// <summary>
        /// 序列号
        /// </summary>
        [Description("序列号")]
        public string SerialNo { get; set; }
        
        /// <summary> 
        /// 发运计划单号
        /// </summary> 
        [Description("发运计划单号")]
        public string ShippingPlanDocNo { get; set; }
        
        /// <summary>
        /// SAP标记
        /// </summary>
        [Description("SAP标记")]
        public string SapMark { get; set; }

        /// <summary>
        /// SAP消息
        /// </summary>
        [Description("SAP消息")]
        public string SapMessage { get; set; }

        /// <summary>
        /// 价格
        /// </summary>
        [Description("价格")]
        [SugarColumn(IsIgnore = true)]
        public decimal? Price { get; set; }

    }
}
