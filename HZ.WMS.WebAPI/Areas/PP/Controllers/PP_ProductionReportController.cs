using HZ.Core.Http;
using HZ.Core.Office;
using HZ.WMS.Application;
using HZ.WMS.Application.PP;
using HZ.WMS.Application.Sys;
using HZ.WMS.Entity.PP;
using HZ.WMS.Entity.Sys;
using HZ.WMS.WebAPI.Controllers;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web.Http;

namespace HZ.WMS.WebAPI.Areas.PP.Controllers
{
    /// <summary>
    /// 生产报工
    /// </summary>
    public class PP_ProductionReportController : ApiBaseController
    {
        private PP_ProductionReportApp _app = new PP_ProductionReportApp();
        private PP_ProductionOrderApp orderApp = new PP_ProductionOrderApp();
        private PP_ProductionReportExportViewApp _appView = new PP_ProductionReportExportViewApp();
        #region 查询

        /// <summary>
        /// 获取主表分页列表
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <param name="dateValue"></param>
        /// <param name="isPosted"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]string keyword, [FromUri]string ProductionLineDes, [FromUri]string ProductionScheduler, [FromUri] DateTime[] dateValue, [FromUri]bool? isPosted
             ,[FromUri]string SalesOrderNo,
            [FromUri]string SalesOrderLineNo, [FromUri]string ContractNo, [FromUri]string OrderNo)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];

                page.Sort = "ProductionOrderNo desc";
                var itemsData = _appView.GetPageList(page,
                    t => (string.IsNullOrEmpty(keyword)
                        || t.SerialNo.Contains(keyword)
                        || t.ProductionReportNo.Contains(keyword)
                        || t.ProductionOrderNo.Contains(keyword)
                        || t.MaterialNo.Contains(keyword)
                        || t.MaterialName.Contains(keyword)
                        || t.Shippers.Contains(keyword)
                        || t.EmployeeNumber.Contains(keyword)
                        || t.EmployeeName.Contains(keyword)
                        || t.CUser.Contains(keyword))
                        && (string.IsNullOrEmpty(ProductionLineDes) || t.ProductionLineCode.Contains(ProductionLineDes) || t.ProductionLineDes.Contains(ProductionLineDes))
                        && (string.IsNullOrEmpty(ProductionScheduler) || t.ProductionScheduler.Contains(ProductionScheduler))
                               && (string.IsNullOrEmpty(SalesOrderNo) || t.SalesOrderNo.Contains(SalesOrderNo))
                        && (string.IsNullOrEmpty(SalesOrderLineNo) || t.SalesOrderLineNo == SalesOrderLineNo)
                        && (string.IsNullOrEmpty(ContractNo) || t.ZORD_CONT == ContractNo)
                        && (string.IsNullOrEmpty(OrderNo) || t.CustomerOrderNum == OrderNo)
                        && (t.StartTime >= fromTime && t.StartTime < toTime)
                        && (t.ReportType == null || t.ReportType == 0)
                        && (isPosted == null || t.IsPosted == isPosted))
                    .ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        [HttpGet]
        public IHttpActionResult GetEntity(string key)
        {
            var result = new ResponseData();
            try
            {
                result.Code = (int)WMSStatusCode.Success;
                result.Data = _app.GetEntityByKey(key);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 根据序列号查询生产订单

        [HttpGet]
        public IHttpActionResult GetOrderBySerialNo([FromUri]string serialNo)
        {
            var result = new ResponseData();
            try
            {
                //result.Data = orderApp.GetFirstEntity(w => w.SerialNo == serialNo);

                result.Data = orderApp.GetSerialNoEntity(serialNo).ToList().FirstOrDefault();
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 根据生产订单获取工序

        /// <summary>
        /// 根据生产订单获取工序
        /// </summary>
        /// <param name="OrderNo"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetWorkingProcedure(string OrderNo)
        {
            var result = new ResponseData();
            try
            {
                result.Code = (int)WMSStatusCode.Success;
                result.Data = _app.GetWorkingProcedure(OrderNo);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取所有未启用序列号的生产订单

        /// <summary>
        /// 获取所有未启用序列号的生产订单
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetOrderNoSerialNo()
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.GetOrderNoSerialNo();
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取所有未启用序列号的生产订单根据单号

        /// <summary>
        /// 获取所有未启用序列号的生产订单
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetOrderNoSerialNoByNo([FromUri]string ProductionOrderNo)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.GetOrderNoSerialNoByNo(ProductionOrderNo);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取系统生成的单号

        /// <summary>
        /// 获取系统生成的单号
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetDocNum()
        {
            var result = new ResponseData();
            try
            {
                result.Data = base.GenerateDocNum(DocType.PP, DocFixedNumDef.PP_ProductionReport);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 根据订单和工序查询剩余数量

        /// <summary>
        /// 
        /// </summary>
        /// <param name="SerialNo">序列号</param>
        /// <param name="ProductionOrderNo">工单号</param>
        /// <param name="WorkingProcedureCode">工序号</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetQty(string SerialNo, string ProductionOrderNo, string WorkingProcedureCode)
        {
            var result = new ResponseData();
            try
            {
                if (string.IsNullOrEmpty(SerialNo))
                {
                    var qty = orderApp.GetSapOrderList(w => w.ProductionOrderNo == ProductionOrderNo)?.Max(m => m.OrderQty);
                    var sum = _app.GetListDistinct(w => w.ProductionOrderNo == ProductionOrderNo && w.WorkingProcedureCode == WorkingProcedureCode)?.Sum(s => s.QualifiedQty);
                    result.Data = new { QualifiedQty = qty - sum };
                }
                else
                {
                    var qty = orderApp.GetFirstEntity(w => w.SerialNo == SerialNo)?.OrderQty;
                    var sum = _app.GetListDistinct(w => w.SerialNo == SerialNo && w.WorkingProcedureCode == WorkingProcedureCode)?.Sum(s => s.QualifiedQty);
                    result.Data = new { QualifiedQty = qty - sum };
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 增加

        [HttpPost]
        public IHttpActionResult Add([FromBody]List<PP_ProductionReport> productionReports)
        {
            var result = new ResponseData();
            try
            {
                Sys_User user = GetCurrentUser();
                if (productionReports.Any(
                    a => //a.SerialNo?.Length > 24 ||
                       string.IsNullOrEmpty(a.ProductionOrderNo)
                    || string.IsNullOrEmpty(a.ProductionScheduler)
                    || string.IsNullOrEmpty(a.OrderType)
                    || string.IsNullOrEmpty(a.WorkingProcedureCode)
                    || string.IsNullOrEmpty(a.ReceivingLocation)
                    || string.IsNullOrEmpty(a.ProductionLineCode)
                    || a.OrderQty == 0
                    || a.QualifiedQty == 0
                    ))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = $"请检查数据完善性！";
                    return Json(result);
                }

                var mainInfoList = productionReports.GroupBy(g => new
                {
                    g.SerialNo,
                    g.ProductionOrderNo,
                    g.WorkingProcedureCode
                }).Select(q => new
                {
                    SerialNo = q.Key.SerialNo,
                    ProductionOrderNo = q.Key.ProductionOrderNo,
                    WorkingProcedureCode = q.Key.WorkingProcedureCode
                });
                foreach (var mainInfo in mainInfoList)
                {
                    List<PP_ProductionReport> dList = productionReports.Where(x => x.SerialNo == mainInfo.SerialNo 
                    && x.ProductionOrderNo == mainInfo.ProductionOrderNo 
                    && x.WorkingProcedureCode == mainInfo.WorkingProcedureCode)?.ToList();
                    if (dList.Count > 1)
                    {
                        result.Code = (int)WMSStatusCode.Failed;
                        result.Message =  $"重复报工,生产订单号"+ mainInfo.ProductionOrderNo;
                        return Json(result);
                    }
                }


                productionReports.ForEach(entity =>
                {
                    //延时报工
                    if((entity.OrderType == "ZP01" || entity.OrderType == "ZP02") && (entity.ProductionScheduler == "102" || entity.ProductionScheduler == "103"))
                    {
                        var view = _app.GetReportView(w => w.ProductionOrderNo == entity.HostProductionOrderNo && w.Mark.ToUpper() == "X").ToList().FirstOrDefault();
                        if (view != null)
                        {
                            result.Code = (int)WMSStatusCode.Failed;
                            result.Message = $"工单{entity.ProductionOrderNo}是延时报工，不能单独报工";
                            return;
                        }
                    }

                    //校验报工数量
                    decimal num = 0;
                    if (string.IsNullOrEmpty(entity.SerialNo))
                    {
                        // 当entity.SerialNo为空时，查询同一生产订单和工序下所有SerialNo为空的记录
                        num = _app.GetListForAggregation(
                            w => string.IsNullOrEmpty(w.SerialNo)
                            && w.ProductionOrderNo == entity.ProductionOrderNo
                            && w.WorkingProcedureCode == entity.WorkingProcedureCode).Sum(s => s.QualifiedQty);
                    }
                    else
                    {
                        // 当entity.SerialNo不为空时，查询相同SerialNo的记录
                        num = _app.GetListForAggregation(
                            w => w.SerialNo == entity.SerialNo
                            && w.ProductionOrderNo == entity.ProductionOrderNo
                            && w.WorkingProcedureCode == entity.WorkingProcedureCode).Sum(s => s.QualifiedQty);
                    }
                    if(entity.QualifiedQty + num > entity.OrderQty)
                    {
                        result.Code = (int)WMSStatusCode.Failed;
                        result.Message = $"生产订单:{entity.ProductionOrderNo},报工数不能超过生产订单数量";
                        return;
                    }

                    entity.ReportType = 0;
                    entity.CUser = user.LoginAccount;
                    entity.EmployeeName = user.UserName;
                    entity.EmployeeNumber = user.LoginAccount;
                });
                if(string.IsNullOrEmpty(result.Message))
                {
                    int res = _app.InsertWithTran(productionReports);
                    if(res > 0)
                    {
                        //是否自动过账判定
                        if (new Sys_SwithConfigApp().IsPPProductionReportAutoPost)
                        {
                            string error_message = "";
                            bool bSubmit = _app.DoPost(productionReports.Where(w => w.IsCompleted == true).ToList(), user.LoginAccount, out error_message);

                            if (!bSubmit)
                            {
                                result.Code = (int)WMSStatusCode.Failed;
                                result.Message = "保存成功；过账失败，失败原因：" + error_message;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 更新

        [HttpPost]
        public IHttpActionResult Update([FromBody]PP_ProductionReport productionReport)
        {
            var result = new ResponseData();
            try
            {
                if(productionReport.IsPosted == true)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = $"生产订单:{productionReport.ProductionOrderNo},已经过账不允许更改";
                    return Json(result);
                }
                //校验报工数量
                var num = _app.GetListDistinct(w => w.ProductionOrderNo == productionReport.ProductionOrderNo && w.WorkingProcedureCode == productionReport.WorkingProcedureCode && w.ProductionReportNo != productionReport.ProductionReportNo && w.IsDelete == false).Sum(s => s.QualifiedQty);
                if (productionReport.QualifiedQty + num > productionReport.OrderQty)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = $"生产订单:{productionReport.ProductionOrderNo},报工数不能超过生产订单数量";
                    return Json(result);
                }

                Sys_User user = GetCurrentUser();
                productionReport.MUser = user.LoginAccount;
                productionReport.ReportType = 0;
                _app.Update(productionReport);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除

        [HttpDelete]
        public IHttpActionResult Delete([FromBody]string[] Ids)
        {
            var result = new ResponseData();
            try
            {
                var list = _app.GetList(w => Ids.Contains(w.ID)).ToList();
                if (list.Any(w => w.IsPosted == true))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "ui.Message.PODeletePostingWarning";
                }
                else
                {
                    Sys_User user = GetCurrentUser();
                    _app.DeleteByKeys(Ids, user.LoginAccount);
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 导出

        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri]string keyword, [FromUri]string ProductionLineDes, [FromUri]string ProductionScheduler, [FromUri] DateTime[] dateValue, [FromUri]bool? isPosted
             ,[FromUri]string SalesOrderNo,
            [FromUri]string SalesOrderLineNo, [FromUri]string ContractNo, [FromUri]string OrderNo)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];

                var itemsData = _appView.GetList(
                    t => (string.IsNullOrEmpty(keyword)
                        || t.SerialNo.Contains(keyword)
                        || t.ProductionReportNo.Contains(keyword)
                        || t.ProductionOrderNo.Contains(keyword)
                        || t.MaterialNo.Contains(keyword)
                        || t.MaterialName.Contains(keyword)
                        || t.Shippers.Contains(keyword)
                        || t.EmployeeNumber.Contains(keyword)
                        || t.EmployeeName.Contains(keyword)
                        || t.CUser.Contains(keyword))
                        && (string.IsNullOrEmpty(ProductionLineDes) || t.ProductionLineCode.Contains(ProductionLineDes) || t.ProductionLineDes.Contains(ProductionLineDes))
                        && (string.IsNullOrEmpty(ProductionScheduler) || t.ProductionScheduler.Contains(ProductionScheduler))
                        && (string.IsNullOrEmpty(SalesOrderNo) || t.SalesOrderNo.Contains(SalesOrderNo))
                        && (string.IsNullOrEmpty(SalesOrderLineNo) || t.SalesOrderLineNo == SalesOrderLineNo)
                        && (string.IsNullOrEmpty(ContractNo) || t.ZORD_CONT == ContractNo)
                        && (string.IsNullOrEmpty(OrderNo) || t.CustomerOrderNum == OrderNo)
                        && (t.StartTime >= fromTime && t.StartTime < toTime)
                        && (t.ReportType == null || t.ReportType == 0)
                        && (isPosted == null || t.IsPosted == isPosted)
                    ).ToList();

                List<ExcelColumn<PP_ProductionReportExport_View>> columns = ExcelService.FetchDefaultColumnList<PP_ProductionReportExport_View>();
                string[] ignoreField = new string[]
                {
                    "ID"
                    ,"ProductionLineCode"
                    ,"ProductionLineDes"
                    ,"MaterialGroupCode"
                    ,"MaterialGroupDes"
                    ,"ReportType"
                    ,"IsCompleted"
                    ,"IsPosted"
                    ,"PostUser"
                    ,"PostTime"
                    ,"ManualPostTime"
                    ,"SapDocNum"
                    ,"IsDelete"
                    ,"MUser"
                    ,"MTime"
                    ,"DUser"
                    ,"DTime"
                };
                List<ExcelColumn<PP_ProductionReportExport_View>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<PP_ProductionReportExport_View> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                //columns.ForEach((column) =>
                //{
                //    if (column.ColumnName == "IsPosted")
                //    {
                //        column.Formattor = ExcelExportFormatter.IsOrNoFormatter;
                //    }
                //});

                return ExportToExcelFile<PP_ProductionReportExport_View>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #region 导入

        [HttpPost]
        public IHttpActionResult ImportExcelToData([FromBody]DataTable entitys)
        {
            var result = new ResponseData();
            try
            {
                if (entitys == null || entitys.Rows.Count < 1)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = $"数据不能为空！";
                    return Json(result);
                }

                Sys_User user = GetCurrentUser();
                List<PP_ProductionReport> productionReports = new List<PP_ProductionReport>();
                foreach (DataRow item in entitys.Rows)
                {
                    productionReports.Add(new PP_ProductionReport
                    {
                        ProductionReportNo = base.GenerateDocNum(DocType.PP, DocFixedNumDef.PP_ProductionReport),
                        SerialNo = item[0].ToString(),
                        ProductionOrderNo = item[1].ToString(),
                        HostProductionOrderNo = item[2].ToString(),
                        ProductionScheduler = item[3].ToString(),
                        OrderType = item[4].ToString(),
                        MaterialNo = item[5].ToString(),
                        MaterialName = item[6].ToString(),
                        WorkingProcedureCode = item[7].ToString(),
                        WorkingProcedureDes = item[8].ToString(),
                        Shippers = item[9].ToString(),
                        OrderQty = Convert.ToDecimal(item[10] ?? 0),
                        Unit = item[11].ToString(),
                        ReceivingLocation = item[12].ToString(),
                        ReportTotal = Convert.ToDecimal(item[13] ?? 0),
                        QualifiedQty = Convert.ToDecimal(item[14] ?? 0),
                        UnqualifiedQty = Convert.ToDecimal(item[15] ?? 0),
                        UnqualifiedRemarks = "",
                        AssessmentType = item[16].ToString(),
                        StartTime = Convert.ToDateTime(item[17] ?? DateTime.Now),
                        ProductionLineCode = item[18].ToString(),
                        ProductionLineDes = item[19].ToString(),
                        ManualPostTime = Convert.ToDateTime(item[20] ?? DateTime.Now),
                        IsCompleted = true,
                        Remark = item[21].ToString(),
                        ReportType = 0,
                    CUser = user.LoginAccount,
                    EmployeeName = user.UserName,
                    EmployeeNumber = user.LoginAccount,
                });
                }
                if (productionReports.Any(
                    a => string.IsNullOrEmpty(a.ProductionOrderNo)
                    || string.IsNullOrEmpty(a.ProductionScheduler)
                    || string .IsNullOrEmpty(a.OrderType)
                    || string .IsNullOrEmpty(a.WorkingProcedureCode)
                    || string .IsNullOrEmpty(a.ReceivingLocation)
                    || string .IsNullOrEmpty(a.ProductionLineCode)
                    || a.OrderQty == 0
                    || a.QualifiedQty == 0
                    ))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = $"请检查数据完善性！";
                    return Json(result);
                }
                //productionReports.ForEach(entity =>
                //{
                    ////判断工单是否有效，工单数量是否正确
                    //if (!string.IsNullOrEmpty(entity.SerialNo))
                    //{
                    //    var order = orderApp.GetFirstEntity(w => w.SerialNo == entity.SerialNo);
                    //    if (order == null || order.OrderQty < entity.OrderQty)
                    //    {
                    //        result.Code = (int)WMSStatusCode.Failed;
                    //        result.Message = $"{entity.ProductionOrderNo}工单不存在或者工单数量错误！";
                    //        return;
                    //    }
                    //}
                    //else
                    //{
                    //    var order = orderApp.GetSapOrderList(w => w.ProductionOrderNo == entity.ProductionOrderNo).ToList().FirstOrDefault();
                    //    if (order == null || order.OrderQty < entity.OrderQty)
                    //    {
                    //        result.Code = (int)WMSStatusCode.Failed;
                    //        result.Message = $"{entity.ProductionOrderNo}工单不存在或者工单数量错误！";
                    //        return;
                    //    }
                    //}
                    ////延时报工
                    //if ((entity.OrderType == "ZP01" || entity.OrderType == "ZP02") && (entity.ProductionScheduler == "102" || entity.ProductionScheduler == "103"))
                    //{
                    //    var view = _app.GetReportView(w => w.ProductionOrderNo == entity.HostProductionOrderNo && w.Mark.ToUpper() == "X").ToList().FirstOrDefault();
                    //    if (view != null)
                    //    {
                    //        result.Code = (int)WMSStatusCode.Failed;
                    //        result.Message = $"工单{entity.ProductionOrderNo}是延时报工，不能单独报工";
                    //        return;
                    //    }
                    //}

                    ////校验报工数量
                    //var num = _app.GetList(
                    //    w => (w.SerialNo == entity.SerialNo || string.IsNullOrEmpty(w.SerialNo) == string.IsNullOrEmpty(entity.SerialNo)) 
                    //    && w.ProductionOrderNo == entity.ProductionOrderNo 
                    //    && w.WorkingProcedureCode == entity.WorkingProcedureCode 
                    //    && w.IsDelete == false).Sum(s => s.QualifiedQty);
                    //if (entity.QualifiedQty + num > entity.OrderQty)
                    //{
                    //    result.Code = (int)WMSStatusCode.Failed;
                    //    result.Message = $"生产订单:{entity.ProductionOrderNo},报工数不能超过生产订单数量";
                    //    return;
                    //}

           
               // });
                if (string.IsNullOrEmpty(result.Message))
                {
                    _app.Insert(productionReports);

                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = "";
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 下载模板

        [HttpGet]
        public IHttpActionResult DownExcelModel()
        {
            var result = new ResponseData();
            try
            {
                string modelName = "生产报工-导入模板";
                var itemsData = new List<PP_ProductionReport>();
                List<ExcelColumn<PP_ProductionReport>> columns = ExcelService.FetchDefaultColumnList<PP_ProductionReport>();
                string[] ignoreField = new string[]
                {
                    "ID"
                    ,"ProductionReportNo"
                    ,"ScanningCode"
                    ,"EmployeeNumber"
                    ,"EmployeeName"
                    ,"UnqualifiedRemarks"
                    ,"MaterialGroupCode"
                    ,"MaterialGroupDes"
                    ,"ReportType"
                    ,"IsCompleted"
                    ,"IsPosted"
                    ,"PostUser"
                    ,"PostTime"
                    ,"SapDocNum"
                    ,"IsDelete"
                    ,"CUser"
                    ,"CTime"
                    ,"MUser"
                    ,"MTime"
                    ,"DUser"
                    ,"DTime"
                };
                List<ExcelColumn<PP_ProductionReport>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<PP_ProductionReport> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                //columns.ForEach((column) =>
                //{
                //    if (column.ColumnName == "IsPosted")
                //    {
                //        column.Formattor = ExcelExportFormatter.IsOrNoFormatter;
                //    }
                //});

                return ExportToExcelFile<PP_ProductionReport>(itemsData, columns,  $"{modelName}_{GetCurrentUser().UserName}");
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        
    }
}
