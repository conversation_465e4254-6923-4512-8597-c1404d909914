//Chloe框架
using SqlSugar;
using System.ComponentModel;

namespace HZ.WMS.Entity.MM
{
    [SugarTable("MM_TakeStockPlanDetailed")]
    public class MM_TakeStockPlanDetailed:BaseEntity
    {
        /// <summary> 
        /// 计划ID
        /// </summary> 
        [SugarColumn(IsPrimaryKey = true)]
        public string PlanID { get; set; }
        /// <summary>
        /// 计划单号
        /// </summary>
        public string DocNum { get; set; }

        /// <summary>
        /// 物料编号
        /// </summary>
        public string ItemCode { get; set; }
        /// <summary>
        /// 物料名称
        /// </summary>
        public string ItemName { get; set; }

        /// <summary> 
        /// 仓库编号
        /// </summary> 
        public string WhsCode { get; set; }

        /// <summary> 
        /// 仓库名称
        /// </summary> 
        public string WhsName { get; set; }

        /// <summary>
        /// 区域编号
        /// </summary>
        public string RegionCode { get; set; }
        /// <summary>
        /// 区域名称
        /// </summary>
        public string RegionName { get; set; }
        /// <summary>
        /// 库位编号
        /// </summary>
        public string BinLocationCode { get; set; }
        /// <summary>
        /// 库位名称
        /// </summary>
        public string BinLocationName { get; set; }
        /// <summary> 
        /// 出厂编号
        /// </summary> 
        [Description("出厂编号")]
        public string BarCode { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量")]
        public decimal? Qty { get; set; }

        /// <summary> 
        /// 特殊库存
        /// </summary> 
        [Description("特殊库存")]
        public string SpecialStock { get; set; }

        /// <summary> 
        /// 销售订单
        /// </summary> 
        [Description("销售订单")]
        public string SaleNum { get; set; }

        /// <summary> 
        /// 销售订单项目
        /// </summary> 
        [Description("销售订单项目")]
        public int? SaleLine { get; set; }

        /// <summary> 
        /// 评估类型
        /// </summary> 
        [Description("评估类型")]
        public string AssessType { get; set; }


        /// <summary>
        /// 供应商编号
        /// </summary>
        public string SupplierCode { get; set; }
        /// <summary>
        /// 供应商名称
        /// </summary>
        public string SupplierName { get; set; }
    }
}


