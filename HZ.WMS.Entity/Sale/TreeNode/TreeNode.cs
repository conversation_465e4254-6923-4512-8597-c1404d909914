using System.Collections.Generic;

namespace AOS.OMS.Entity.Sale.OrderTree
{
    public class TreeNode
    {
        /// <summary>
        /// 描述
        /// </summary>
        public string Label { get; set; }

        /// <summary>
        /// 值
        /// </summary>
        public string Value { get; set; }

        /// <summary>
        /// 等级
        /// </summary>
        public int Level { get; set; }

        /// <summary>
        /// 分支
        /// </summary>
        public string Branch { get; set; }

        /// <summary>
        /// 是否有下级
        /// </summary>
        public bool Leaf { get; set; }

        /// <summary>
        /// 总个数
        /// </summary>
        public int Count { get; set; }

        /// <summary>
        /// 下级节点
        /// </summary>
        public List<TreeNode> Children { get; set; }
    }
}