//Chloe框架


namespace HZ.WMS.Entity.MD.ViewModel
{
    /// <summary>
    /// 物料主数据视图
    /// </summary>
    public class MD_Item_View
    {
        /// <summary>
        /// 物料编号
        /// </summary>
        public string ItemCode { get; set; }

        /// <summary>
        /// 物料名称
        /// </summary>
        public string ItemName { get; set; }


        /// <summary>
        /// 物料组
        /// </summary>
        public string MATKL { get; set; }


        /// <summary>
        /// 物料小组
        /// </summary>
        public string EXTWG { get; set; }

        /// <summary>
        /// 库存管理方式 1：启用批次 2：启用序列号 0：不启用
        /// </summary>
        public int? StockManWay { get; set; }

        /// <summary>
        /// 是否包装物料 0：否 1：是
        /// </summary>
        public bool? IsPackaging { get; set; }

        /// <summary>
        /// 是否确认 0：否 1：是
        /// </summary>
        public bool? IsConfirm { get; set; }

        /// <summary>
        /// 库存管理方式名称
        /// </summary>
        public string StockManWayName { get; set; }
        
    }
}


