using SqlSugar;
using HZ.WMS.Entity.Sys;
using System.Data;

namespace HZ.WMS.Application.Sys
{
    /// <summary>
    /// 没有out_message消息的函数，意味着只在最前端做未处理异常捕捉
    /// </summary>

    public class Sys_SwithConfigApp : BaseApp<Sys_SwithConfig>
    {
        private const string PO_PURCHASERECEIPT_AUTOPOST = "PO_AP_001";    //采购收货自动过账
        private const string PO_RETURNSCAN_AUTOPOST = "PO_AP_002";       //采购退货扫描自动过账

        private const string PP_DELIVERY_AUTOPOST = "PP_AP_001";         //生产配送自动过账开关
        private const string PP_REGULARPICKING_AUTOPOST = "PP_AP_002";   //生产常规领料自动过账开关
        private const string PP_OVERPICKING_AUTOPOST = "PP_AP_003";      //生产超额领料自动过账开关
        private const string PP_PRODUCTIONRETURN_AUTOPOST = "PP_AP_004"; //生产工单退料自动过账开关
        private const string PP_RETURNSCAN_AUTOPOST = "PP_AP_005";       //生产车间退料自动过账开关
        private const string PP_PRODUCTIONFEEDING_AUTOPOST = "PP_AP_006";//生产投料自动过账开关
        private const string PP_PRODUCTIONREPORT_AUTOPOST = "PP_AP_007"; //生产报工自动过账开关
        private const string MM_DEPREQUISITION_AUTOPOST = "PP_AP_008";     //部门领料自动过账

        private const string MM_EQUIPMENTPICKING_AUTOPOST = "MM_AP_001";     //设备领料自动过账开关
        private const string MM_DISPATCH_AUTOPOST = "MM_AP_002";     //委外发料自动过账开关
        private const string MM_WAREHOUSING_AUTOPOST = "MM_AP_003";     //委外入库自动过账开关
        private const string MM_MAGNETSWAREHOUSING_AUTOPOST = "MM_AP_004";     //磁材委外入库自动过账开关
        private const string MM_RETURN_AUTOPOST = "MM_AP_005";     //委外退料自动过账开关
        private const string MM_REDEPLOYAPPLY_AUTOPOST = "MM_AP_006";     //调拨申请自动过账开关
        private const string MM_LENDINGORDER_AUTOPOST = "MM_AP_007";     //借出单自动过账

        private const string SD_DeliverySCAN_AUTOPOST = "SD_AP_001";     //销售发货自动过账开关
        private const string SD_ReturnSCAN_AUTOPOST = "SD_AP_002";     //销售退货自动过账开关

        private const string QM_PURCHASEINSPECTION_AUTOPOST = "QM_AP_001";       //采购入库检验自动过账开关

        private const string SYS_AP_001 = "SYS_AP_001";       //是否开启12月账期

        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public Sys_SwithConfigApp() : base()
        {
        }




        #endregion

        #region 采购模块

        #region 采购退货扫描自动过账

        /// <summary>
        /// 采购退货扫描自动过账
        /// </summary>
        public bool IsPoReturnScanAutoPost
        {
            get
            {
                //return GetFirstEntity(x => x.ConfigCode == PO_SELFSCAN_AUTOPOST)?.SwitchValue==true;
                var config = GetFirstEntity(x => x.ConfigCode == PO_RETURNSCAN_AUTOPOST);
                return config == null ? false : config.SwitchValue == true;
            }
        }

        #endregion

        #region 采购收货自动过账

        /// <summary>
        /// 采购收货自动过账
        /// </summary>
        public bool IsPOPurchaseReceiptAutoPost
        {
            get
            {
                //return GetFirstEntity(x => x.ConfigCode == PO_SELFSCAN_AUTOPOST)?.SwitchValue==true;
                var config = GetFirstEntity(x => x.ConfigCode == PO_PURCHASERECEIPT_AUTOPOST);
                return config == null ? false : config.SwitchValue == true;
            }
        }

        #endregion

        #region 采购入库检验自动过账SRM

        /// <summary>
        /// 采购入库检验自动过账SRM
        /// </summary>
        public bool IsQMPurchaseInspectionAutoPost
        {
            get
            {
                //return GetFirstEntity(x => x.ConfigCode == PO_SELFSCAN_AUTOPOST)?.SwitchValue==true;
                var config = GetFirstEntity(x => x.ConfigCode == QM_PURCHASEINSPECTION_AUTOPOST);
                return config == null ? false : config.SwitchValue == true;
            }
        }

        #endregion

        #endregion

        #region 生产模块

        #region 生产配送自动过账

        /// <summary>
        /// 生产配送自动过账
        /// </summary>
        public bool IsPPDeliveryAutoPost
        {
            get
            {
                var config = GetFirstEntity(x => x.ConfigCode == PP_DELIVERY_AUTOPOST);
                return config == null ? false : config.SwitchValue == true;
            }
        }

        #endregion

        #region 生产超额领料自动过账

        /// <summary>
        /// 生产超额领料自动过账
        /// </summary>
        public bool IsPPOverPickingAutoPost
        {
            get
            {
                var config = GetFirstEntity(x => x.ConfigCode == PP_OVERPICKING_AUTOPOST);
                return config == null ? false : config.SwitchValue == true;
            }
        }

        #endregion

        #region 生产投料自动过账

        /// <summary>
        /// 生产投料自动过账
        /// </summary>
        public bool IsPPProductionFeedingAutoPost
        {
            get
            {
                var config = GetFirstEntity(x => x.ConfigCode == PP_PRODUCTIONFEEDING_AUTOPOST);
                return config == null ? false : config.SwitchValue == true;
            }
        }

        #endregion

        #region 生产报工自动过账

        /// <summary>
        /// 生产报工自动过账
        /// </summary>
        public bool IsPPProductionReportAutoPost
        {
            get
            {
                var config = GetFirstEntity(x => x.ConfigCode == PP_PRODUCTIONREPORT_AUTOPOST);
                return config == null ? false : config.SwitchValue == true;
            }
        }

        #endregion

        #region 生产车间退料自动过账

        /// <summary>
        /// 生产车间退料自动过账
        /// </summary>
        public bool IsPPReturnScanAutoPost
        {
            get
            {
                var config = GetFirstEntity(x => x.ConfigCode == PP_RETURNSCAN_AUTOPOST);
                return config == null ? false : config.SwitchValue == true;
            }
        }

        #endregion

        #region 生产工单退料自动过账

        /// <summary>
        /// 生产工单退料自动过账
        /// </summary>
        public bool IsPPProductionReturnAutoPost
        {
            get
            {
                var config = GetFirstEntity(x => x.ConfigCode == PP_PRODUCTIONRETURN_AUTOPOST);
                return config == null ? false : config.SwitchValue == true;
            }
        }

        #endregion

        #endregion

        #region 仓库模块

        #region 仓库-委外退料自动过账

        /// <summary>
        /// 仓库-委外退料自动过账
        /// </summary>
        public bool IsMMReturnAutoPost
        {
            get
            {
                //return GetFirstEntity(x => x.ConfigCode == PO_SELFSCAN_AUTOPOST)?.SwitchValue==true;
                var config = GetFirstEntity(x => x.ConfigCode == MM_RETURN_AUTOPOST);
                return config == null ? false : config.SwitchValue == true;
            }
        }

        #endregion

        #region 仓库-委外发料自动过账

        /// <summary>
        /// 仓库-委外发料自动过账
        /// </summary>
        public bool IsMMDispatchAutoPost
        {
            get
            {
                //return GetFirstEntity(x => x.ConfigCode == PO_SELFSCAN_AUTOPOST)?.SwitchValue==true;
                var config = GetFirstEntity(x => x.ConfigCode == MM_DISPATCH_AUTOPOST);
                return config == null ? false : config.SwitchValue == true;
            }
        }

        #endregion

        #region 仓库-委外入库自动过账

        /// <summary>
        /// 仓库-委外入库自动过账
        /// </summary>
        public bool IsMMWarehousingAutoPost
        {
            get
            {
                //return GetFirstEntity(x => x.ConfigCode == PO_SELFSCAN_AUTOPOST)?.SwitchValue==true;
                var config = GetFirstEntity(x => x.ConfigCode == MM_WAREHOUSING_AUTOPOST);
                return config == null ? false : config.SwitchValue == true;
            }
        }

        #endregion

        #region 仓库-磁材委外入库自动过账

        /// <summary>
        /// 仓库-磁材委外入库自动过账
        /// </summary>
        public bool IsMM_MagnetsWarehousingAutoPost
        {
            get
            {
                //return GetFirstEntity(x => x.ConfigCode == PO_SELFSCAN_AUTOPOST)?.SwitchValue==true;
                var config = GetFirstEntity(x => x.ConfigCode == MM_MAGNETSWAREHOUSING_AUTOPOST);
                return config == null ? false : config.SwitchValue == true;
            }
        }

        #endregion

        #region 仓库-设备领料自动过账

        /// <summary>
        /// 仓库-设备领料自动过账
        /// </summary>
        public bool IsMMEquipmentPickingAutoPost
        {
            get
            {
                //return GetFirstEntity(x => x.ConfigCode == PO_SELFSCAN_AUTOPOST)?.SwitchValue==true;
                var config = GetFirstEntity(x => x.ConfigCode == MM_EQUIPMENTPICKING_AUTOPOST);
                return config == null ? false : config.SwitchValue == true;
            }
        }

        #endregion

        #region 仓库-部门领料自动过账

        /// <summary>
        /// 仓库-部门领料自动过账
        /// </summary>
        public bool IsDeqRequisitionAutoPost
        {
            get
            {
                //return GetFirstEntity(x => x.ConfigCode == PO_SELFSCAN_AUTOPOST)?.SwitchValue==true;
                var config = GetFirstEntity(x => x.ConfigCode == MM_DEPREQUISITION_AUTOPOST);
                return config == null ? false : config.SwitchValue == true;
            }
        }

        #endregion

        #region 仓库-调拨申请自动过账

        /// <summary>
        /// 仓库-调拨申请自动过账
        /// </summary>
        public bool IsRedeployApplyAutoPost
        {
            get
            {
                //return GetFirstEntity(x => x.ConfigCode == PO_SELFSCAN_AUTOPOST)?.SwitchValue==true;
                var config = GetFirstEntity(x => x.ConfigCode == MM_REDEPLOYAPPLY_AUTOPOST);
                return config == null ? false : config.SwitchValue == true;
            }
        }

        #endregion

        #region 仓库-借出单自动过账

        /// <summary>
        /// 仓库-借出单自动过账
        /// </summary>
        public bool IsLendingOrderAutoPost
        {
            get
            {
                //return GetFirstEntity(x => x.ConfigCode == PO_SELFSCAN_AUTOPOST)?.SwitchValue==true;
                var config = GetFirstEntity(x => x.ConfigCode == MM_LENDINGORDER_AUTOPOST);
                return config == null ? false : config.SwitchValue == true;
            }
        }

        #endregion

        #endregion

        #region 销售模块

        #region 销售发货自动过账开关

        /// <summary>
        /// 销售发货自动过账开关
        /// </summary>
        public bool IsSDDeliveryScanAutoPost
        {
            get
            {
                var config = GetFirstEntity(x => x.ConfigCode == SD_DeliverySCAN_AUTOPOST);
                return config == null ? false : config.SwitchValue == true;
            }
        }

        #endregion

        #region 销售退货自动过账开关

        /// <summary>
        /// 销售退货自动过账开关
        /// </summary>
        public bool IsSDReturnScanAutoPost
        {
            get
            {
                var config = GetFirstEntity(x => x.ConfigCode == SD_ReturnSCAN_AUTOPOST);
                return config == null ? false : config.SwitchValue == true;
            }
        }

        #endregion

        #endregion


        //更新账期
        public bool UpdateZQ(string IsOn)
        {
            var pm1 = new SugarParameter("@IsOn", IsOn);
            DbContext.Ado.UseStoredProcedure().ExecuteCommand("PROC_UpdateSAPZQ", pm1);
            return true;

        }

    }
}
