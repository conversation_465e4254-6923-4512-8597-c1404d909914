using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.MM
{
    /// <summary>
    /// 委外领料申请
    /// </summary>
    [SugarTable("MM_PickingApply")]
    public class MM_PickingApply : BaseEntity
    {
        /// <summary> 
        /// ID
        /// </summary> 
        [SugarColumn(IsPrimaryKey = true)]
        [Description("ID")]
        public string SubcontractingApplicationID { get; set; }

        /// <summary> 
        /// 单号
        /// </summary> 
        [Description("单号")]
        public string DocNum { get; set; }

        /// <summary> 
        /// 状态 0:未开始 1:进行中 2：已完成
        /// </summary> 
        [Description("状态")]
        public int? Status { get; set; }



    }
}
