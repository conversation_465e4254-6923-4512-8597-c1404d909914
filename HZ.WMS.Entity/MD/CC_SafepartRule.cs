using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.MD
{
    /// <summary>
    /// 安全部件代码-编码规则
    /// </summary>
    public class CC_SafepartRule : BaseEntity
    {
        /// <summary>
        /// 主键-编号
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string id { get; set; }
        /// <summary>
        /// 前三位编码
        /// </summary>
        public string code1 { get; set; }
        /// <summary>
        /// 后三位编码
        /// </summary>
        public string code2 { get; set; }
        /// <summary>
        /// 年份码
        /// </summary>
        public string codey { get; set; }
        /// <summary>
        /// 当前最大编号
        /// </summary>
        public string maxnum { get; set; }
        
    }
}
