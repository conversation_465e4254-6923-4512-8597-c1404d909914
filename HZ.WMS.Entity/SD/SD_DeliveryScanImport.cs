using System;
using System.ComponentModel;
//Chloe框架

namespace HZ.WMS.Entity.SD
{
    /// <summary>
    /// 销售交货导入
    /// </summary>
    public class SD_DeliveryScanImport 
    {

        /// <summary> 
        /// 客户订单号
        /// </summary> 
        [Description("客户订单号")]
        public string 客户订单号 { get; set; }
        /// <summary> 
        /// 销售订单号
        /// </summary> 
        [Description("销售订单号")]
        public string 销售订单号 { get; set; }

        /// <summary> 
        /// 销售单行号
        /// </summary> 
        [Description("销售单行号")]
        public int? 销售单行号 { get; set; }

        /// <summary> 
        /// 销售订单类型
        /// </summary> 
        [Description("销售订单类型")]
        public string 销售订单类型 { get; set; }

        /// <summary> 
        /// 销售组织
        /// </summary> 
        [Description("销售组织")]
        public string 销售组织 { get; set; }

        /// <summary> 
        /// 交货日期
        /// </summary> 
        [Description("交货日期")]
        public DateTime? 交货日期 { get; set; }

        /// <summary> 
        /// 批次
        /// </summary> 
        [Description("批次")]
        public string 批次 { get; set; }

        /// <summary> 
        /// 客户编号
        /// </summary> 
        [Description("客户编号")]
        public string 客户编号 { get; set; }

        /// <summary> 
        /// 客户名称
        /// </summary> 
        [Description("客户名称")]
        public string 客户名称 { get; set; }

        /// <summary>
        /// 客户地址
        /// </summary>
        [Description("客户地址")]
        public string 客户地址 { get; set; }

        /// <summary>
        /// 序列号
        /// </summary>
        [Description("序列号")]
        public string 序列号 { get; set; }

        /// <summary> 
        /// 物料编号
        /// </summary> 
        [Description("物料编号")]
        public string 物料编号 { get; set; }

        /// <summary> 
        /// 交货数量
        /// </summary> 
        [Description("交货数量")]
        public decimal? 交货数量 { get; set; }

        /// <summary> 
        /// 库存单位
        /// </summary> 
        [Description("库存单位")]
        public string 库存单位 { get; set; }

        /// <summary> 
        /// 项目
        /// </summary> 
        [Description("项目名称")]
        public string 项目名称 { get; set; }

        /// <summary> 
        /// 项目类别
        /// </summary> 
        [Description("项目类别")]
        public string 项目类别 { get; set; }

        /// <summary> 
        /// 仓库编号
        /// </summary> 
        [Description("仓库编号")]
        public string 仓库编号 { get; set; }

        /// <summary> 
        /// 过账时间
        /// </summary> 
        [Description("过账时间")]
        public DateTime? 过账时间 { get; set; }

        /// <summary> 
        /// 合同单号
        /// </summary> 
        [Description("合同单号")]
        public string 合同单号 { get; set; }

        /// <summary>
        /// 结算地址
        /// </summary>
        [Description("结算地址")]
        public string 结算地址 { get; set; }

        /// <summary>
        /// 物流供应商编号
        /// </summary>
        [Description("物流供应商编号")]
        public string 物流供应商编号 { get; set; }

        /// <summary>
        /// 物流供应商名称
        /// </summary>
        [Description("物流供应商")]
        public string 物流供应商 { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Description("备注")]
        public string 备注 { get; set; }

    }
}


