using System;
using System.Collections.Generic;
using System.Linq;
using HZ.Core.Http;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.MD.Import;
using HZ.WMS.Entity.MD.DTO;
using SqlSugar;

namespace HZ.WMS.Application.MD
{
    /// <summary>
    /// 打印模板管理
    /// </summary>
    public class MD_PrintTemplateApp : BaseApp<MD_PrintTemplate>
    {
        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public MD_PrintTemplateApp() : base() { }

        #endregion

        #region 获取包含参数模板名称的分页列表

        /// <summary>
        /// 获取包含参数模板名称的分页列表
        /// </summary>
        /// <param name="page">分页参数</param>
        /// <param name="req">查询条件</param>
        /// <returns></returns>
        public List<MD_PrintTemplateWithParameterName> GetPageListWithParameterName(Pagination page, MD_PrintTemplateListReq req)
        {
            if (string.IsNullOrEmpty(page.Sort))
            {
                page.Sort = "t.CTime desc";
            }
            else
            {
                page.Sort = page.Sort.Replace("ascending", "asc").Replace("descending", "desc");
                // 确保排序字段有表别名
                if (!page.Sort.Contains("t.") && !page.Sort.Contains("p."))
                {
                    page.Sort = "t." + page.Sort;
                }
            }

            var query = DbContext.Queryable<MD_PrintTemplate, MD_PrintTemplateParameter>((t, p) => new object[] {
                SqlSugar.JoinType.Left, t.TemplateParameterId == p.Id
            })
            .Where((t, p) => !t.IsDelete)
            .WhereIF(!string.IsNullOrEmpty(req.Keyword), (t, p) => t.TemplateName.Contains(req.Keyword))
            .WhereIF(!string.IsNullOrEmpty(req.TemplateName), (t, p) => t.TemplateName == req.TemplateName)
            .WhereIF(req.Enable != null, (t, p) => t.Enable == req.Enable)
            .Select((t, p) => new MD_PrintTemplateWithParameterName
            {
                Id = t.Id,
                TemplateName = t.TemplateName,
                TemplateJson = t.TemplateJson,
                Enable = t.Enable,
                TemplateParameterId = t.TemplateParameterId,
                TemplateParameterName = p.TemplateName,
                Remark = t.Remark,
                IsDelete = t.IsDelete,
                CUser = t.CUser,
                CTime = t.CTime,
                MUser = t.MUser,
                MTime = t.MTime,
                DUser = t.DUser,
                DTime = t.DTime
            })
            .OrderBy(page.Sort);

            int total = query.Count();
            page.Total = total;
            if (page.PageSize > total)
            {
                return query.ToPageList(page.PageNumber, total);
            }
            return query.ToPageList(page.PageNumber, page.PageSize);
        }

        #endregion

        #region 导入

        /// <summary>
        /// 导入
        /// </summary>
        /// <param name="excelList">接收的集合</param>
        /// <param name="opUser">当前用户</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool ImportExcelToBaseData(List<MD_PrintTemplateImport> excelList, string opUser, out string error_message)
        {
            error_message = "";
            var flag = true;
            DateTime date = DateTime.Now;
            var entityList = new List<MD_PrintTemplate>();

            try
            {
                foreach (var item in excelList)
                {
                    if (string.IsNullOrEmpty(item.TemplateName))
                    {
                        error_message = "模板名称不能为空";
                        return false;
                    }

                    // 检查模板名称是否重复
                    var existTemplate = GetFirstEntityByFieldValue("TemplateName", item.TemplateName);
                    if (existTemplate != null)
                    {
                        error_message = $"模板名称 '{item.TemplateName}' 已存在";
                        return false;
                    }

                    var entity = new MD_PrintTemplate
                    {
                        Id = Guid.NewGuid().ToString(),
                        TemplateName = item.TemplateName,
                        TemplateJson = item.TemplateJson,
                        Enable = item.Enable,
                        TemplateParameterId = item.TemplateParameterId,
                        Remark = item.Remark,
                        CUser = opUser,
                        CTime = date,
                        IsDelete = false
                    };

                    entityList.Add(entity);
                }

                if (entityList.Count > 0)
                {
                    Insert(entityList);
                }

                return flag;
            }
            catch (Exception ex)
            {
                error_message = ex.Message;
                return false;
            }
        }

        #endregion
    }
}
