using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using SqlSugar;
using HZ.WMS.Entity.MD;

namespace HZ.WMS.Application.MD
{
    /// <summary>
    /// 供应商主数据
    /// </summary>
    public class MD_SupplierApp : BaseApp<MD_Supplier>
    {
        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public MD_SupplierApp() : base()
        {
        }




        #endregion

        #region 同步sap数据

        /// <summary>
        /// 
        /// </summary>
        public void SyncSupplier()
        {
            //获取所有数据
            //List<SupplierEntity> sapSuppliers = SupplierService.GetSuppliers();
            List<MD_Supplier> sapSupplierList = new List<MD_Supplier>();

            //foreach (SupplierEntity entity in sapSuppliers)
            //{
            //    MD_Supplier mdSupplier = new MD_Supplier();
            //    mdSupplier.SupplierCode = entity.SupplierCode;
            //    mdSupplier.SupplierName = entity.SupplierName;
            //    mdSupplier.SupplierStatus = entity.SupplierStatus;
            //    mdSupplier.Email = entity.Email;
            //    mdSupplier.Address = entity.Address;
            //    mdSupplier.City = entity.City;
            //    mdSupplier.ContactPerson = entity.ContactPerson;
            //    mdSupplier.Fax = entity.Fax;
            //    mdSupplier.Phone = entity.Phone;
            //    mdSupplier.CTime = entity.LastChangeTime;
            //    mdSupplier.MTime = entity.LastChangeTime;
            //    sapSupplierList.Add(mdSupplier);
            //}

            List<MD_Supplier> localDbSupplierList = GetList().ToList();

            //1、本地没有，远程有的新增数据
            List<MD_Supplier> addSuppliers = sapSupplierList.Except(localDbSupplierList, new MD_SupplierEquality()).ToList();

            //2、本地有，远程没有的删除数据
            List<MD_Supplier> deletedSuppliers = localDbSupplierList.Except(sapSupplierList, new MD_SupplierEquality()).ToList();


            //3、本地远程都有的更新数据（交集）
            List<MD_Supplier> updateSuppliers = new List<MD_Supplier>();
            foreach (MD_Supplier supplier in localDbSupplierList)
            {
                var entity = sapSupplierList.Where(x => x.SupplierCode == supplier.SupplierCode).FirstOrDefault();
                DateTime localMTime = DateTime.Parse(supplier.MTime.Value.ToString("yyyy-MM-dd HH:mm:ss"));
                DateTime sapMTime = DateTime.Parse(entity.MTime.Value.ToString("yyyy-MM-dd HH:mm:ss"));
                if (entity!=null && localMTime<sapMTime)
                {
                    supplier.SupplierCode = entity.SupplierCode;
                    supplier.SupplierName = entity.SupplierName;
                    supplier.SupplierStatus = entity.SupplierStatus;
                    supplier.Email = entity.Email;
                    supplier.Address = entity.Address;
                    supplier.City = entity.City;
                    supplier.ContactPerson = entity.ContactPerson;
                    supplier.Fax = entity.Fax;
                    supplier.Phone = entity.Phone;
                    supplier.MTime = entity.MTime;
                    updateSuppliers.Add(supplier);
                }
            } 

            if(addSuppliers!=null && addSuppliers.Count() > 0)
            {
                //Delete(mdSupplierList);
                Insert(addSuppliers);

            }

            if (updateSuppliers != null && updateSuppliers.Count() > 0)
            {
                //Delete(mdSupplierList);
                Update(updateSuppliers);

            }

            if (deletedSuppliers != null && deletedSuppliers.Count() > 0)
            {
                
                Delete(deletedSuppliers);

            }


        }



        #endregion

        #region 获取懒加载分页数据

        /// <summary>
        /// 
        /// </summary>
        /// <param name="lastReturnUUID"></param>
        /// <param name="keyword"></param>
        /// <param name="pageSize"></param>
        /// <param name="firstUUID"></param>
        /// <returns></returns>
        public List<MD_Supplier> GetSupplierPage(out string lastReturnUUID, string keyword = "", int pageSize = 30, string firstUUID = "")
        {
            lastReturnUUID = firstUUID;
            List<MD_Supplier> queryList = GetList(x => string.IsNullOrEmpty(keyword) || (x.SupplierCode.Contains(keyword) || x.SupplierName.Contains(keyword))).ToList();
            if (string.IsNullOrEmpty(firstUUID))
            {
                var listResult = queryList.Take(pageSize).ToList();
                if(listResult!=null && listResult.Count() > 0)
                {
                    lastReturnUUID = listResult.Last().SupplierObjectID;
                }
                
                return listResult;
            }
            else
            {
                int firstIndex = queryList.FindIndex(x => x.SupplierObjectID == firstUUID);
                var listResult = queryList.Skip(firstIndex+1).Take(pageSize).ToList();
                if(listResult!=null && listResult.Count > 0)
                {
                    lastReturnUUID = listResult.Last().SupplierObjectID;
                }
                
                return listResult;

            }
            
        }

        #endregion

    }
}
