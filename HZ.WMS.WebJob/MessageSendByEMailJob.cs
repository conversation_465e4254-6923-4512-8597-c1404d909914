using HZ.WMS.Application.Sys;
using Quartz;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.WebJob
{
    public class MessageSendByEMailJob : IJob
    {
        /// <summary>
        /// 自动发送邮件任务
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public Task Execute(IJobExecutionContext context)
        {
            HZ.Core.Logging.LogHelper.Instance.LogDebug("自动发送邮件任务执行开始:" + DateTime.Now.ToString());
            return Task.Run(() =>
            {
                new Sys_MessageApp().AutoSendMessageByEMail();
                HZ.Core.Logging.LogHelper.Instance.LogDebug("自动发送邮件任务执行结束:" + DateTime.Now.ToString());
            });
        }
    }
}
