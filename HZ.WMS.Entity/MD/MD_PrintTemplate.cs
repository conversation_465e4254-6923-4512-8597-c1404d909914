using SqlSugar;
using System.ComponentModel;

namespace HZ.WMS.Entity.MD
{
    /// <summary>
    /// 打印模板
    /// </summary>
    [SugarTable("MD_PrintTemplate")]
    public class MD_PrintTemplate : BaseEntity
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Description("主键ID")]
        public string Id { get; set; }

        /// <summary>
        /// 模板名称
        /// </summary>
        [Description("模板名称")]
        public string TemplateName { get; set; }

        /// <summary>
        /// 模板json
        /// </summary>
        [Description("模板json")]
        public string TemplateJson { get; set; }

        /// <summary>
        /// 是否启用 0否 1启用
        /// </summary>
        [Description("是否启用")]
        public bool? Enable { get; set; }

        /// <summary>
        /// 模板参数Id
        /// </summary>
        [Description("模板参数Id")]
        public string TemplateParameterId { get; set; }
    }
}
