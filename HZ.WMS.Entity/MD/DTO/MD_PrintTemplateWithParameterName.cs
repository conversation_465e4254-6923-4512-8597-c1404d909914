using System;
using System.ComponentModel;

namespace HZ.WMS.Entity.MD.DTO
{
    /// <summary>
    /// 打印模板包含参数模板名称
    /// </summary>
    public class MD_PrintTemplateWithParameterName
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Description("主键ID")]
        public string Id { get; set; }

        /// <summary>
        /// 模板名称
        /// </summary>
        [Description("模板名称")]
        public string TemplateName { get; set; }

        /// <summary>
        /// 模板json
        /// </summary>
        [Description("模板json")]
        public string TemplateJson { get; set; }

        /// <summary>
        /// 是否启用 0否 1启用
        /// </summary>
        [Description("是否启用")]
        public bool? Enable { get; set; }

        /// <summary>
        /// 模板参数Id
        /// </summary>
        [Description("模板参数Id")]
        public string TemplateParameterId { get; set; }

        /// <summary>
        /// 参数模板名称
        /// </summary>
        [Description("参数模板名称")]
        public string TemplateParameterName { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Description("备注")]
        public string Remark { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        [Description("是否删除")]
        public bool IsDelete { get; set; }

        /// <summary>
        /// 创建用户
        /// </summary>
        [Description("创建用户")]
        public string CUser { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [Description("创建时间")]
        public DateTime? CTime { get; set; }

        /// <summary>
        /// 修改用户
        /// </summary>
        [Description("修改用户")]
        public string MUser { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        [Description("修改时间")]
        public DateTime? MTime { get; set; }

        /// <summary>
        /// 删除用户
        /// </summary>
        [Description("删除用户")]
        public string DUser { get; set; }

        /// <summary>
        /// 删除时间
        /// </summary>
        [Description("删除时间")]
        public DateTime? DTime { get; set; }
    }
}
