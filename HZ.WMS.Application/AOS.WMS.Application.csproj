<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\EntityFramework.6.5.0\build\build\net6.0\EntityFramework.props" Condition="Exists('..\packages\EntityFramework.6.5.0\build\build\net6.0\EntityFramework.props')" />
  <Import Project="..\packages\EntityFramework.6.5.0\build\build\EntityFramework.props" Condition="Exists('..\packages\EntityFramework.6.5.0\build\build\EntityFramework.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{28DBE3A0-F19A-49E4-9CFE-6F8177CB17BF}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>HZ.WMS.Application</RootNamespace>
    <AssemblyName>HZ.WMS.Application</AssemblyName>
    <TargetFrameworkVersion>v4.6.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <DocumentationFile>bin\Debug\HZ.WMS.Application.xml</DocumentationFile>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="DevExpress.Data.v17.2">
      <HintPath>..\Lib\DevExpress.Data.v17.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Xpo.v17.2">
      <HintPath>..\Lib\DevExpress.Xpo.v17.2.dll</HintPath>
    </Reference>
      <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
          <HintPath>..\packages\EntityFramework.6.5.0\lib\net45\EntityFramework.dll</HintPath>
      </Reference>
      <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
          <HintPath>..\packages\EntityFramework.6.5.0\lib\net45\EntityFramework.SqlServer.dll</HintPath>
      </Reference>
      <Reference Include="LinqKit, Version=1.3.7.0, Culture=neutral, PublicKeyToken=bc217f8844052a91, processorArchitecture=MSIL">
          <HintPath>..\packages\LinqKit.1.3.7\lib\net45\LinqKit.dll</HintPath>
      </Reference>
      <Reference Include="LinqKit.Core, Version=1.2.7.0, Culture=neutral, PublicKeyToken=bc217f8844052a91, processorArchitecture=MSIL">
        <HintPath>..\packages\LinqKit.Core.1.2.7\lib\net45\LinqKit.Core.dll</HintPath>
      </Reference>
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.2.0\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.2.0\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="LinqKit, Version=1.3.7.0, Culture=neutral, PublicKeyToken=bc217f8844052a91, processorArchitecture=MSIL">
      <HintPath>..\packages\LinqKit.1.3.7\lib\net45\LinqKit.dll</HintPath>
    </Reference>
    <Reference Include="LinqKit.Core, Version=1.2.7.0, Culture=neutral, PublicKeyToken=bc217f8844052a91, processorArchitecture=MSIL">
      <HintPath>..\packages\LinqKit.Core.1.2.7\lib\net45\LinqKit.Core.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.5.0\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="LinqKit, Version=1.3.7.0, Culture=neutral, PublicKeyToken=bc217f8844052a91, processorArchitecture=MSIL">
      <HintPath>..\packages\LinqKit.1.3.7\lib\net45\LinqKit.dll</HintPath>
    </Reference>
    <Reference Include="LinqKit.Core, Version=1.2.7.0, Culture=neutral, PublicKeyToken=bc217f8844052a91, processorArchitecture=MSIL">
      <HintPath>..\packages\LinqKit.Core.1.2.7\lib\net45\LinqKit.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=6.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.6.0.0\lib\net461\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="mscorlib" />
    <Reference Include="Newtonsoft.Json, Version=12.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.12.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="Oracle.ManagedDataAccess, Version=4.122.21.1, Culture=neutral, PublicKeyToken=89b483f429c47342, processorArchitecture=MSIL">
      <HintPath>..\packages\Oracle.ManagedDataAccess.21.18.0\lib\net462\Oracle.ManagedDataAccess.dll</HintPath>
    </Reference>
    <Reference Include="sapnco">
      <HintPath>..\Lib\sapnco.dll</HintPath>
    </Reference>
    <Reference Include="sapnco_utils">
      <HintPath>..\Lib\sapnco_utils.dll</HintPath>
    </Reference>
    <Reference Include="SqlSugar, Version=5.1.4.115, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\SqlSugar.5.1.4.115\lib\SqlSugar.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=4.0.4.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.6.0\lib\net462\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Formats.Asn1, Version=8.0.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Formats.Asn1.8.0.1\lib\net462\System.Formats.Asn1.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory, Version=4.0.2.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.6.0\lib\net462\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.5.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.6.0\lib\net462\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=6.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.1.0\lib\net462\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encodings.Web, Version=6.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encodings.Web.6.0.0\lib\net461\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Json, Version=6.0.0.10, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Json.6.0.10\lib\net461\System.Text.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net461\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Base\BaseApp.cs" />
    <Compile Include="Base\BaseAppExt.cs" />
    <Compile Include="Base\ConnectionPoolCleanupService.cs" />
    <Compile Include="Base\ConnectionPoolManager.cs" />
    <Compile Include="Base\ConnectionStringBuilder.cs" />
    <Compile Include="Base\ContentBase.cs" />
    <Compile Include="Base\DbConfig.cs" />
    <Compile Include="Base\DbConnectionManager.cs" />
    <Compile Include="Base\DbConnectionMonitor.cs" />
    <Compile Include="Base\ExcelExportFormatter.cs" />
    <Compile Include="Base\LanguagePackagePath.cs" />
    <Compile Include="Base\LogHelperExtensions.cs" />
    <Compile Include="Base\SafeDatabaseExecutor.cs" />
    <Compile Include="Base\SimpleConnectionMonitor.cs" />
    <Compile Include="Base\SqlSugarClientFactory.cs" />
    <Compile Include="CableBasic\CustomerPartApp.cs" />
    <Compile Include="CableBasic\ProductionNoRuleApp.cs" />
    <Compile Include="CablePartProduce\Cable_PartWorkOrderApp.cs" />
    <Compile Include="CableProduce\Cable_ProductionOrderApp.cs" />
    <Compile Include="CableProduce\Cable_WorkOrderApp.cs" />
    <Compile Include="CableSale\Cable_DeliveryApp.cs" />
    <Compile Include="CableSale\Cable_ShippingPlanApp.cs" />
    <Compile Include="CableSale\SD_Cable_Sale_PartOrderInfoApp.cs" />
    <Compile Include="KB\PurchaseOrderBoardApp.cs" />
    <Compile Include="KB\SaleOrderBoardApp.cs" />
    <Compile Include="KB\StockWarningBoardApp.cs" />
    <Compile Include="MD\CC_SafepartCustomerApp.cs" />
    <Compile Include="MD\CC_SafepartRuleApp.cs" />
    <Compile Include="MD\MD_BasicSpecificationApp.cs" />
    <Compile Include="MD\MD_BinLimitApp.cs" />
    <Compile Include="MD\MD_BinLocationApp.cs" />
    <Compile Include="MD\MD_CustomerModelDeliveryAdvanceApp.cs" />
    <Compile Include="MD\MD_CustomerProduceApp.cs" />
    <Compile Include="MD\MD_LineBatchApp.cs" />
    <Compile Include="MD\MD_NonStandardConfigApp.cs" />

    <Compile Include="MD\MD_PartMakeCompanyApp.cs" />
    <Compile Include="MD\MD_PartProduceLineApp.cs" />
    <Compile Include="MD\MD_PrintTemplateApp.cs" />
    <Compile Include="MD\MD_PrintTemplateParameterApp.cs" />
    <Compile Include="MD\MD_ProcessInspectionApp.cs" />
    <Compile Include="MD\MD_ProduceLineApp.cs" />
    <Compile Include="MD\MD_ReportStationApp.cs" />
    <Compile Include="MD\MD_ReportStationMaterialApp.cs" />
    <Compile Include="MD\MD_ProduceLineCapacityApp.cs" />
    <Compile Include="MD\MD_ShippingMarkRuleApp.cs" />
    <Compile Include="MD\MD_WorkCenterStationApp.cs" />
    <Compile Include="MD\SD_CustomerAddViewApp.cs" />
    <Compile Include="MD\MD_CustomerApp.cs" />
    <Compile Include="MD\MD_PartCodeApp.cs" />
    <Compile Include="MD\MD_ProductCategoryApp.cs" />
    <Compile Include="MD\MD_ItemApp.cs" />
    <Compile Include="MD\MD_LabelTempleteApp.cs" />
    <Compile Include="MD\MD_ProductionDistributionSettingApp.cs" />
    <Compile Include="MD\MD_Province_City_DistrictApp.cs" />
    <Compile Include="MD\MD_RegionApp.cs" />
    <Compile Include="MD\MD_StockApp.cs" />
    <Compile Include="MD\MD_SupplierApp.cs" />
    <Compile Include="MD\MD_UnitCodeConversionApp.cs" />
    <Compile Include="MD\MD_WarehouseApp.cs" />
    <Compile Include="MD\MD_FreightMileageApp.cs" />
    <Compile Include="MD\MD_CustomerWeightRateC_App.cs" />
    <Compile Include="MD\MD_CustomerWeightRateA_App.cs" />
    <Compile Include="MD\MD_CustomerWeightPriceB_App.cs" />
    <Compile Include="MD\MD_CustomerDistanceRateA_App.cs" />
    <Compile Include="MD\MD_CustomerAddApp.cs" />
    <Compile Include="MM\MM_BarCodeApp.cs" />
    <Compile Include="MM\MM_BarCodeScanApp.cs" />
    <Compile Include="MM\MM_DepReqDetailedApp.cs" />
    <Compile Include="MM\MM_DepRequisitionApp.cs" />
    <Compile Include="MM\MM_DispatchApp.cs" />
    <Compile Include="MM\MM_LendingOrderApp.cs" />
    <Compile Include="MM\MM_LendingOrderDetailApp.cs" />
    <Compile Include="MM\MM_LendingOrder_ReturnApp.cs" />
    <Compile Include="MM\MM_OtherInApp.cs" />
    <Compile Include="MM\MM_OtherInDetailApp.cs" />
    <Compile Include="MM\MM_PickingApplyDetailApp.cs" />
    <Compile Include="MM\MM_RedeployApplyApp.cs" />
    <Compile Include="MM\MM_RedeployApplyDetailApp.cs" />
    <Compile Include="MM\MM_ScrapApplicationApp.cs" />
    <Compile Include="MM\MM_ScrapApplicationDetailApp.cs" />
    <Compile Include="MM\MM_StockTodoPickingApp.cs" />
    <Compile Include="MM\MM_SupplierRepairApp.cs" />
    <Compile Include="MM\MM_SupplierRepairViewApp.cs" />
    <Compile Include="MM\MM_WarehousingApp.cs" />
    <Compile Include="MM\MM_PickingApplyApp.cs" />
    <Compile Include="MM\MM_TakeStockPlanApp.cs" />
    <Compile Include="MM\MM_TakeStockPlanDetailedApp.cs" />
    <Compile Include="MM\MM_TakeStockScanApp.cs" />
    <Compile Include="MM\MM_EquipmentPickingApp.cs" />
    <Compile Include="MM\MM_ReturnApp.cs" />
    <Compile Include="MM\MM_MagnetsWarehousingApp.cs" />
    <Compile Include="MM\MM_StockTodoApp.cs" />
    <Compile Include="MM\MM_StockTodoDetailApp.cs" />
    <Compile Include="PO\PO_PurchaseReceiptApp.cs" />
    <Compile Include="PO\PO_ReturnScanApp.cs" />
    <Compile Include="PP\PP_AssignSerialNoViewApp.cs" />
    <Compile Include="PP\PP_DistributionDetailViewApp.cs" />
    <Compile Include="PP\PP_PackSortingApp.cs" />
    <Compile Include="PP\PP_PackSortingDetailApp.cs" />
    <Compile Include="PP\PP_ProcessInspectionApp.cs" />
    <Compile Include="PP\PP_ProcessInspectionDetailApp.cs" />
    <Compile Include="PP\PP_ProductionReportApp.cs" />
    <Compile Include="PP\PP_ProductionReportExportViewApp.cs" />
    <Compile Include="PP\PP_ReturnMaterialApp.cs" />
    <Compile Include="PP\PP_ReturnMaterialDetailApp.cs" />
    <Compile Include="PP\PP_MaterialDistributionApp.cs" />
    <Compile Include="PP\PP_MaterialDistributionDetailApp.cs" />
    <Compile Include="PP\PP_OverReceiveApp.cs" />
    <Compile Include="PP\PP_OverReceiveDetailApp.cs" />
    <Compile Include="PP\PP_ProductionFeedingApp.cs" />
    <Compile Include="PP\PP_ProductionFeedingDetailApp.cs" />
    <Compile Include="PP\PP_ProductionOrderApp.cs" />
    <Compile Include="PP\PP_SerialNoRelationApp.cs" />
    <Compile Include="PP\PP_SerialNoRelationDetailApp.cs" />
    <Compile Include="PP\PP_ShippingMarkApp.cs" />
    <Compile Include="Produce\Produce_PlanOrderApp.cs" />
    <Compile Include="Produce\Produce_ReportApp.cs" />
    <Compile Include="Produce\Produce_SchedulingDetailApp.cs" />
    <Compile Include="Produce\Produce_PurchaseOrderApp.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="QM\QM_PurchaseInspectionApp.cs" />
    <Compile Include="RPT\RPT_ItemMoveViewApp.cs" />
    <Compile Include="Sale\Produce_SchedulingApp.cs" />
    <Compile Include="Sale\Sale_DeliveryApp.cs" />
    <Compile Include="Sale\Sale_ShippingPlanApp.cs" />
    <Compile Include="SAP\SAPApp.cs" />
    <Compile Include="SD\SD_ShippingPlanApp.cs" />
    <Compile Include="SD\SD_DeliveryScanApp.cs" />
    <Compile Include="SD\SD_ReturnScanApp.cs" />
    <Compile Include="SD\SD_ConsignmentNoteApp.cs" />
    <Compile Include="SD\SD_ShippingPlanDetailApp.cs" />
    <Compile Include="SRM\SRMApp.cs" />
    <Compile Include="Sys\Sys_ApiLogConfigApp.cs" />
    <Compile Include="Sys\Sys_AppVersionApp.cs" />
    <Compile Include="Sys\Sys_DbBackupApp.cs" />
    <Compile Include="Sys\Sys_DbBackupConfigApp.cs" />
    <Compile Include="Sys\Sys_LogApp.cs" />
    <Compile Include="Sys\Sys_MailApp.cs" />
    <Compile Include="Sys\Sys_MailServerConfigApp.cs" />
    <Compile Include="Sys\Sys_MessageApp.cs" />
    <Compile Include="Sys\Sys_MessageNotifySettingApp.cs" />
    <Compile Include="Sys\Sys_MessageTypeApp.cs" />
    <Compile Include="Sys\Sys_OrganizationApp.cs" />
    <Compile Include="Sys\Sys_DictionaryApp.cs" />
    <Compile Include="Sys\Sys_ResourceApp.cs" />
    <Compile Include="Sys\Sys_RoleApp.cs" />
    <Compile Include="Sys\Sys_RoleResourceApp.cs" />
    <Compile Include="Sys\Sys_SAPCompanyInfoApp.cs" />
    <Compile Include="Sys\Sys_SwithConfigApp.cs" />
    <Compile Include="Sys\Sys_UserApp.cs" />
    <Compile Include="Sys\Sys_UserMessageApp.cs" />
    <Compile Include="Sys\Sys_UserRoleApp.cs" />
    <Compile Include="Sys\Sys_UserSapAccountApp.cs" />
    <Compile Include="Util\HttpUtil.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
    <None Include="content\net40\App.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\HZ.Core\AOS.Core.csproj">
      <Project>{62D9F685-5537-494E-8D51-4DAF877F8AF1}</Project>
      <Name>AOS.Core</Name>
    </ProjectReference>
    <ProjectReference Include="..\HZ.WMS.BydDataAdapter\AOS.WMS.SAPData.csproj">
      <Project>{ed97ed39-1a86-4c93-831b-aca2e4c5005a}</Project>
      <Name>AOS.WMS.SAPData</Name>
    </ProjectReference>
    <ProjectReference Include="..\HZ.WMS.Entity\AOS.WMS.Entity.csproj">
      <Project>{A56C0618-C820-42E7-89B5-E5DA01C72729}</Project>
      <Name>AOS.WMS.Entity</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Oracle.ManagedDataAccess.Client.Configuration.Section.xsd" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Enable NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105.The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\EntityFramework.6.5.0\build\build\EntityFramework.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.5.0\build\build\EntityFramework.props'))" />
    <Error Condition="!Exists('..\packages\EntityFramework.6.5.0\build\build\EntityFramework.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.5.0\build\build\EntityFramework.targets'))" />
    <Error Condition="!Exists('..\packages\EntityFramework.6.5.0\build\build\net6.0\EntityFramework.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.5.0\build\build\net6.0\EntityFramework.props'))" />
    <Error Condition="!Exists('..\packages\EntityFramework.6.5.0\build\build\net6.0\EntityFramework.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.5.0\build\build\net6.0\EntityFramework.targets'))" />
  </Target>
  <Import Project="..\packages\EntityFramework.6.5.0\build\build\EntityFramework.targets" Condition="Exists('..\packages\EntityFramework.6.5.0\build\build\EntityFramework.targets')" />
  <Import Project="..\packages\EntityFramework.6.5.0\build\build\net6.0\EntityFramework.targets" Condition="Exists('..\packages\EntityFramework.6.5.0\build\build\net6.0\EntityFramework.targets')" />
</Project>