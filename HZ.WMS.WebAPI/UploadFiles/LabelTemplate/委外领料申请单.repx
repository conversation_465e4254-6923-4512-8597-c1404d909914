/// <XRTypeInfo>
///   <AssemblyFullName>DevExpress.XtraReports.v16.1, Version=16.1.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</AssemblyFullName>
///   <AssemblyLocation>C:\Windows\Microsoft.Net\assembly\GAC_MSIL\DevExpress.XtraReports.v16.1\v4.0_16.1.5.0__b88d1754d700e49a\DevExpress.XtraReports.v16.1.dll</AssemblyLocation>
///   <TypeName>DevExpress.XtraReports.UI.XtraReport</TypeName>
///   <Localization>zh-CN</Localization>
///   <Version>16.1</Version>
///   <Resources>
///     <Resource Name="XtraReportSerialization.委外领料申请">
/// zsrvvgEAAACRAAAAbFN5c3RlbS5SZXNvdXJjZXMuUmVzb3VyY2VSZWFkZXIsIG1zY29ybGliLCBWZXJzaW9uPTQuMC4wLjAsIEN1bHR1cmU9bmV1dHJhbCwgUHVibGljS2V5VG9rZW49Yjc3YTVjNTYxOTM0ZTA4OSNTeXN0ZW0uUmVzb3VyY2VzLlJ1bnRpbWVSZXNvdXJjZVNldAIAAAABAAAAAAAAAFBBRFBBRFA4hdt+AAAAAA8BAABOcwBxAGwARABhAHQAYQBTAG8AdQByAGMAZQAxAC4AUgBlAHMAdQBsAHQAUwBjAGgAZQBtAGEAUwBlAHIAaQBhAGwAaQB6AGEAYgBsAGUAAAAAAAGUClBFUmhkR0ZUWlhRZ1RtRnRaVDBpYzNGc1JHRjBZVk52ZFhKalpURWlQanhXYVdWM0lFNWhiV1U5SWsxTlgxQnBZMnRwYm1kQmNIQnNlU0krUEVacFpXeGtJRTVoYldVOUlrUnZZ
/// MDUxYlNJZ1ZIbHdaVDBpVTNSeWFXNW5JaUF2UGp4R2FXVnNaQ0JPWVcxbFBTSlRkR0YwZFhNaUlGUjVjR1U5SWtsdWRETXlJaUF2UGp4R2FXVnNaQ0JPWVcxbFBTSkVaWFJoYVd4SlJDSWdWSGx3WlQwaVUzUnlhVzVuSWlBdlBqeEdhV1ZzWkNCT1lXMWxQU0pOVFY5UWFXTnJhVzVuUVhCd2JIbEVaWFJoYVd4ZlJHOWpUblZ0SWlCVWVYQmxQU0pUZEhKcGJtY2lJQzgrUEVacFpXeGtJRTVoYldVOUlrSmhjMlZPZFcwaUlGUjVjR1U5SWxOMGNtbHVaeUlnTHo0OFJtbGxiR1FnVG1GdFpUMGlRbUZ6WlV4cGJtVWlJRlI1Y0dVOUlrbHVkRE15SWlBdlBqeEdhV1ZzWkNCT1lXMWxQU0pKZEdWdFEyOWtaU0lnVkhsd1pUMGlVM1J5YVc1bklpQXZQanhHYVdWc1pDQk9ZVzFs
/// UFNKSmRHVnRUbUZ0WlNJZ1ZIbHdaVDBpVTNSeWFXNW5JaUF2UGp4R2FXVnNaQ0JPWVcxbFBTSlRkWEJ3YkdsbGNrTnZaR1VpSUZSNWNHVTlJbE4wY21sdVp5SWdMejQ4Um1sbGJHUWdUbUZ0WlQwaVUzVndjR3hwWlhKT1lXMWxJaUJVZVhCbFBTSlRkSEpwYm1jaUlDOCtQRVpwWld4a0lFNWhiV1U5SWxGMGVTSWdWSGx3WlQwaVJHVmphVzFoYkNJZ0x6NDhSbWxsYkdRZ1RtRnRaVDBpVlc1cGRDSWdWSGx3WlQwaVUzUnlhVzVuSWlBdlBqeEdhV1ZzWkNCT1lXMWxQU0pTWlcxaGNtc2lJRlI1Y0dVOUlsTjBjbWx1WnlJZ0x6NDhSbWxsYkdRZ1RtRnRaVDBpU1hORVpXeGxkR1VpSUZSNWNHVTlJa0p2YjJ4bFlXNGlJQzgrUEVacFpXeGtJRTVoYldVOUlrTlZjMlZ5SWlC
/// VWVYQmxQU0pUZEhKcGJtY2lJQzgrUEVacFpXeGtJRTVoYldVOUlrTlVhVzFsSWlCVWVYQmxQU0pFWVhSbFZHbHRaU0lnTHo0OFJtbGxiR1FnVG1GdFpUMGlUVlZ6WlhJaUlGUjVjR1U5SWxOMGNtbHVaeUlnTHo0OFJtbGxiR1FnVG1GdFpUMGlUVlJwYldVaUlGUjVjR1U5SWtSaGRHVlVhVzFsSWlBdlBqeEdhV1ZzWkNCT1lXMWxQU0pFVlhObGNpSWdWSGx3WlQwaVUzUnlhVzVuSWlBdlBqeEdhV1ZzWkNCT1lXMWxQU0pFVkdsdFpTSWdWSGx3WlQwaVJHRjBaVlJwYldVaUlDOCtQRVpwWld4a0lFNWhiV1U5SWxOMVltTnZiblJ5WVdOMGFXNW5RWEJ3YkdsallYUnBiMjVPZFcwaUlGUjVjR1U5SWxOMGNtbHVaeUlnTHo0OFJtbGxiR1FnVG1GdFpUMGlUVTFmVUdsamEy
/// bHVaMEZ3Y0d4NVJHVjBZV2xzWDFOMFlYUjFjeUlnVkhsd1pUMGlTVzUwTXpJaUlDOCtQQzlXYVdWM1Bqd3ZSR0YwWVZObGREND0=</Resource>
///   </Resources>
/// </XRTypeInfo>
namespace XtraReportSerialization {
    
    public class 委外领料申请 : DevExpress.XtraReports.UI.XtraReport {
        private DevExpress.XtraReports.UI.XRTableCell tableCell7;
        private DevExpress.XtraReports.UI.XRLabel label17;
        private DevExpress.XtraReports.UI.XRLabel label8;
        private DevExpress.XtraReports.UI.XRTableCell tableCell14;
        private DevExpress.XtraReports.UI.CalculatedField calculatedField1;
        private DevExpress.XtraReports.UI.XRLabel label3;
        private DevExpress.XtraReports.UI.ReportFooterBand ReportFooter;
        private DevExpress.XtraReports.UI.XRLabel label20;
        private DevExpress.XtraReports.UI.XRLabel label19;
        private DevExpress.XtraReports.UI.XRTable table2;
        private DevExpress.XtraReports.UI.XRTableRow tableRow2;
        private DevExpress.XtraReports.UI.XRTableCell tableCell1;
        private DevExpress.XtraReports.UI.XRTableCell tableCell2;
        private DevExpress.XtraReports.UI.XRTableCell tableCell16;
        private DevExpress.XtraReports.UI.XRTableCell tableCell3;
        private DevExpress.XtraReports.UI.XRLabel label12;
        private DevExpress.XtraReports.UI.XRTableCell tableCell18;
        private DevExpress.XtraReports.UI.XRTable table3;
        private DevExpress.XtraReports.UI.XRTableRow tableRow5;
        private DevExpress.XtraReports.UI.XRTableCell tableCell31;
        private DevExpress.XtraReports.UI.XRTableCell tableCell26;
        private DevExpress.XtraReports.UI.XRTableCell tableCell27;
        private DevExpress.XtraReports.UI.XRTableCell tableCell30;
        private DevExpress.XtraReports.UI.XRTableCell tableCell5;
        private DevExpress.XtraReports.UI.XRLabel label7;
        private DevExpress.XtraReports.UI.XRLabel label5;
        private DevExpress.XtraReports.UI.XRTableRow tableRow1;
        private DevExpress.XtraReports.UI.XRTableCell tableCell4;
        private DevExpress.XtraReports.UI.XRTableCell tableCell8;
        private DevExpress.XtraReports.UI.XRTableCell tableCell10;
        private DevExpress.XtraReports.UI.XRTable table4;
        private DevExpress.XtraReports.UI.XRTableRow tableRow3;
        private DevExpress.XtraReports.UI.XRTableCell tableCell17;
        private DevExpress.XtraReports.UI.XRLabel label13;
        private DevExpress.XtraReports.UI.FormattingRule formattingRule1;
        private DevExpress.XtraReports.UI.XRLabel label15;
        private DevExpress.XtraReports.UI.XRTable table1;
        private DevExpress.XtraReports.UI.XRTableRow tableRow6;
        private DevExpress.XtraReports.UI.XRTableCell tableCell9;
        private DevExpress.XtraReports.UI.XRTableCell tableCell11;
        private DevExpress.XtraReports.UI.XRTableCell tableCell13;
        private DevExpress.XtraReports.UI.XRTableCell tableCell15;
        private DevExpress.XtraReports.Parameters.Parameter docNums;
        private DevExpress.XtraReports.UI.XRLabel label14;
        private DevExpress.XtraReports.UI.XRLabel label2;
        private DevExpress.DataAccess.Sql.SqlDataSource sqlDataSource1;
        private DevExpress.XtraReports.UI.XRLabel label9;
        private DevExpress.XtraReports.UI.XRBarCode barCode1;
        private DevExpress.XtraReports.UI.BottomMarginBand BottomMargin;
        private DevExpress.XtraReports.UI.XRLabel label1;
        private DevExpress.XtraReports.UI.TopMarginBand TopMargin;
        private DevExpress.XtraReports.UI.XRLabel label4;
        private DevExpress.XtraReports.UI.XRLabel label6;
        private DevExpress.XtraReports.UI.XRLabel label16;
        private DevExpress.XtraReports.UI.DetailBand Detail;
        private System.Resources.ResourceManager _resources;
        private string _resourceString;
        public 委外领料申请() {
            this._resourceString = DevExpress.XtraReports.Serialization.XRResourceManager.GetResourceFor("XtraReportSerialization.委外领料申请");
            this.InitializeComponent();
        }
        private System.Resources.ResourceManager resources {
            get {
                if (_resources == null) {
                    this._resources = new DevExpress.XtraReports.Serialization.XRResourceManager(this._resourceString);
                }
                return this._resources;
            }
        }
        private void InitializeComponent() {
            DevExpress.XtraReports.UI.XRSummary summary1 = new DevExpress.XtraReports.UI.XRSummary();
            DevExpress.XtraReports.UI.XRSummary summary2 = new DevExpress.XtraReports.UI.XRSummary();
            DevExpress.XtraReports.UI.XRSummary summary3 = new DevExpress.XtraReports.UI.XRSummary();
            DevExpress.DataAccess.ConnectionParameters.MsSqlConnectionParameters msSqlConnectionParameters1 = new DevExpress.DataAccess.ConnectionParameters.MsSqlConnectionParameters();
            DevExpress.DataAccess.Sql.SelectQuery selectQuery1 = new DevExpress.DataAccess.Sql.SelectQuery();
            DevExpress.DataAccess.Sql.Column column1 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression1 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Table table5 = new DevExpress.DataAccess.Sql.Table();
            DevExpress.DataAccess.Sql.Column column2 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression2 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column3 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression3 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Table table6 = new DevExpress.DataAccess.Sql.Table();
            DevExpress.DataAccess.Sql.Column column4 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression4 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column5 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression5 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column6 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression6 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column7 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression7 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column8 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression8 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column9 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression9 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column10 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression10 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column11 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression11 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column12 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression12 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column13 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression13 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column14 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression14 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column15 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression15 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column16 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression16 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column17 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression17 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column18 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression18 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column19 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression19 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column20 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression20 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column21 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression21 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column22 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression22 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.QueryParameter queryParameter1 = new DevExpress.DataAccess.Sql.QueryParameter();
            DevExpress.DataAccess.Sql.QueryParameter queryParameter2 = new DevExpress.DataAccess.Sql.QueryParameter();
            DevExpress.DataAccess.Sql.Join join1 = new DevExpress.DataAccess.Sql.Join();
            DevExpress.DataAccess.Sql.RelationColumnInfo relationColumnInfo1 = new DevExpress.DataAccess.Sql.RelationColumnInfo();
            DevExpress.XtraPrinting.BarCode.QRCodeGenerator qRCodeGenerator1 = new DevExpress.XtraPrinting.BarCode.QRCodeGenerator();
            this.tableCell7 = new DevExpress.XtraReports.UI.XRTableCell();
            this.label17 = new DevExpress.XtraReports.UI.XRLabel();
            this.label8 = new DevExpress.XtraReports.UI.XRLabel();
            this.tableCell14 = new DevExpress.XtraReports.UI.XRTableCell();
            this.calculatedField1 = new DevExpress.XtraReports.UI.CalculatedField();
            this.label3 = new DevExpress.XtraReports.UI.XRLabel();
            this.ReportFooter = new DevExpress.XtraReports.UI.ReportFooterBand();
            this.tableCell16 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell3 = new DevExpress.XtraReports.UI.XRTableCell();
            this.label12 = new DevExpress.XtraReports.UI.XRLabel();
            this.tableCell18 = new DevExpress.XtraReports.UI.XRTableCell();
            this.table3 = new DevExpress.XtraReports.UI.XRTable();
            this.tableCell5 = new DevExpress.XtraReports.UI.XRTableCell();
            this.label7 = new DevExpress.XtraReports.UI.XRLabel();
            this.label5 = new DevExpress.XtraReports.UI.XRLabel();
            this.tableRow1 = new DevExpress.XtraReports.UI.XRTableRow();
            this.table4 = new DevExpress.XtraReports.UI.XRTable();
            this.label20 = new DevExpress.XtraReports.UI.XRLabel();
            this.table2 = new DevExpress.XtraReports.UI.XRTable();
            this.label13 = new DevExpress.XtraReports.UI.XRLabel();
            this.formattingRule1 = new DevExpress.XtraReports.UI.FormattingRule();
            this.label15 = new DevExpress.XtraReports.UI.XRLabel();
            this.tableCell2 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableRow3 = new DevExpress.XtraReports.UI.XRTableRow();
            this.tableCell8 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableRow2 = new DevExpress.XtraReports.UI.XRTableRow();
            this.table1 = new DevExpress.XtraReports.UI.XRTable();
            this.docNums = new DevExpress.XtraReports.Parameters.Parameter();
            this.label19 = new DevExpress.XtraReports.UI.XRLabel();
            this.tableCell9 = new DevExpress.XtraReports.UI.XRTableCell();
            this.label14 = new DevExpress.XtraReports.UI.XRLabel();
            this.tableCell4 = new DevExpress.XtraReports.UI.XRTableCell();
            this.label2 = new DevExpress.XtraReports.UI.XRLabel();
            this.sqlDataSource1 = new DevExpress.DataAccess.Sql.SqlDataSource();
            this.label9 = new DevExpress.XtraReports.UI.XRLabel();
            this.barCode1 = new DevExpress.XtraReports.UI.XRBarCode();
            this.tableCell17 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell15 = new DevExpress.XtraReports.UI.XRTableCell();
            this.BottomMargin = new DevExpress.XtraReports.UI.BottomMarginBand();
            this.tableCell11 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableRow5 = new DevExpress.XtraReports.UI.XRTableRow();
            this.tableCell31 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell13 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell10 = new DevExpress.XtraReports.UI.XRTableCell();
            this.label1 = new DevExpress.XtraReports.UI.XRLabel();
            this.TopMargin = new DevExpress.XtraReports.UI.TopMarginBand();
            this.label16 = new DevExpress.XtraReports.UI.XRLabel();
            this.label6 = new DevExpress.XtraReports.UI.XRLabel();
            this.tableRow6 = new DevExpress.XtraReports.UI.XRTableRow();
            this.Detail = new DevExpress.XtraReports.UI.DetailBand();
            this.tableCell30 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell26 = new DevExpress.XtraReports.UI.XRTableCell();
            this.label4 = new DevExpress.XtraReports.UI.XRLabel();
            this.tableCell27 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell1 = new DevExpress.XtraReports.UI.XRTableCell();
            ((System.ComponentModel.ISupportInitialize)(this.table3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.table4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.table2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.table1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // tableCell7
            // 
            this.tableCell7.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
                        | DevExpress.XtraPrinting.BorderSide.Right)));
            this.tableCell7.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "MM_PickingApply.Qty", "{0:#.####}")});
            this.tableCell7.Dpi = 100F;
            this.tableCell7.Name = "tableCell7";
            this.tableCell7.StylePriority.UseBorders = false;
            this.tableCell7.StylePriority.UseTextAlignment = false;
            this.tableCell7.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.tableCell7.Weight = 0.30492114120799385D;
            // 
            // label17
            // 
            this.label17.Dpi = 100F;
            this.label17.LocationFloat = new DevExpress.Utils.PointFloat(0.2083302F, 25F);
            this.label17.Name = "label17";
            this.label17.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.label17.SizeF = new System.Drawing.SizeF(67.48072F, 23F);
            this.label17.StylePriority.UseTextAlignment = false;
            this.label17.Text = "审核人：";
            this.label17.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // label8
            // 
            this.label8.BackColor = System.Drawing.Color.Transparent;
            this.label8.BorderColor = System.Drawing.Color.Black;
            this.label8.BorderDashStyle = DevExpress.XtraPrinting.BorderDashStyle.Solid;
            this.label8.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.label8.BorderWidth = 1F;
            this.label8.Dpi = 100F;
            this.label8.Font = new System.Drawing.Font("宋体", 10.2F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label8.ForeColor = System.Drawing.Color.Black;
            this.label8.LocationFloat = new DevExpress.Utils.PointFloat(0.6250064F, 157.2917F);
            this.label8.Name = "label8";
            this.label8.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.label8.SizeF = new System.Drawing.SizeF(92.0386F, 24.99999F);
            this.label8.StylePriority.UseBackColor = false;
            this.label8.StylePriority.UseBorderColor = false;
            this.label8.StylePriority.UseBorderDashStyle = false;
            this.label8.StylePriority.UseBorders = false;
            this.label8.StylePriority.UseBorderWidth = false;
            this.label8.StylePriority.UseFont = false;
            this.label8.StylePriority.UseForeColor = false;
            this.label8.StylePriority.UsePadding = false;
            this.label8.StylePriority.UseTextAlignment = false;
            this.label8.Text = "申请单号：";
            this.label8.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // tableCell14
            // 
            this.tableCell14.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell14.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "MM_PickingApply.BaseLine")});
            this.tableCell14.Dpi = 100F;
            this.tableCell14.Name = "tableCell14";
            this.tableCell14.StylePriority.UseBorders = false;
            this.tableCell14.StylePriority.UseTextAlignment = false;
            this.tableCell14.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.tableCell14.Weight = 0.417471430720078D;
            // 
            // calculatedField1
            // 
            this.calculatedField1.Expression = "Now()";
            this.calculatedField1.Name = "calculatedField1";
            // 
            // label3
            // 
            this.label3.AutoWidth = true;
            this.label3.CanShrink = true;
            this.label3.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "calculatedField1", "{0:yyyy-MM-dd}")});
            this.label3.Dpi = 100F;
            this.label3.LocationFloat = new DevExpress.Utils.PointFloat(67.68906F, 109.393F);
            this.label3.Multiline = true;
            this.label3.Name = "label3";
            this.label3.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.label3.SizeF = new System.Drawing.SizeF(100F, 23F);
            this.label3.StylePriority.UseTextAlignment = false;
            summary1.Func = DevExpress.XtraReports.UI.SummaryFunc.Avg;
            summary1.IgnoreNullValues = true;
            this.label3.Summary = summary1;
            this.label3.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // ReportFooter
            // 
            this.ReportFooter.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
                        this.label20,
                        this.label19,
                        this.label17,
                        this.table2});
            this.ReportFooter.Dpi = 100F;
            this.ReportFooter.HeightF = 100F;
            this.ReportFooter.Name = "ReportFooter";
            // 
            // tableCell16
            // 
            this.tableCell16.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Right | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell16.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "MM_PickingApply.SupplierCode")});
            this.tableCell16.Dpi = 100F;
            this.tableCell16.Name = "tableCell16";
            this.tableCell16.StylePriority.UseBorders = false;
            this.tableCell16.Weight = 0.64395331036036318D;
            // 
            // tableCell3
            // 
            this.tableCell3.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Right | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell3.Dpi = 100F;
            this.tableCell3.Name = "tableCell3";
            this.tableCell3.StylePriority.UseBorders = false;
            this.tableCell3.Text = "供应商编号：";
            this.tableCell3.Weight = 0.67461819683935365D;
            // 
            // label12
            // 
            this.label12.BackColor = System.Drawing.Color.Transparent;
            this.label12.BorderColor = System.Drawing.Color.Black;
            this.label12.BorderDashStyle = DevExpress.XtraPrinting.BorderDashStyle.Solid;
            this.label12.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Top | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.label12.BorderWidth = 1F;
            this.label12.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "MM_PickingApply.Remark")});
            this.label12.Dpi = 100F;
            this.label12.Font = new System.Drawing.Font("宋体", 10.2F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label12.ForeColor = System.Drawing.Color.Black;
            this.label12.LocationFloat = new DevExpress.Utils.PointFloat(540.5417F, 132.2916F);
            this.label12.Name = "label12";
            this.label12.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.label12.SizeF = new System.Drawing.SizeF(220.4166F, 24.99999F);
            this.label12.StylePriority.UseBackColor = false;
            this.label12.StylePriority.UseBorderColor = false;
            this.label12.StylePriority.UseBorderDashStyle = false;
            this.label12.StylePriority.UseBorders = false;
            this.label12.StylePriority.UseBorderWidth = false;
            this.label12.StylePriority.UseFont = false;
            this.label12.StylePriority.UseForeColor = false;
            this.label12.StylePriority.UsePadding = false;
            this.label12.StylePriority.UseTextAlignment = false;
            this.label12.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // tableCell18
            // 
            this.tableCell18.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Right | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell18.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "MM_PickingApply.SupplierName")});
            this.tableCell18.Dpi = 100F;
            this.tableCell18.Name = "tableCell18";
            this.tableCell18.StylePriority.UseBorders = false;
            this.tableCell18.Weight = 1.9786301444482715D;
            // 
            // table3
            // 
            this.table3.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.table3.Dpi = 100F;
            this.table3.LocationFloat = new DevExpress.Utils.PointFloat(1.249997F, 182.2917F);
            this.table3.Name = "table3";
            this.table3.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
                        this.tableRow5});
            this.table3.SizeF = new System.Drawing.SizeF(624.5F, 50F);
            this.table3.StylePriority.UseBorders = false;
            // 
            // tableCell5
            // 
            this.tableCell5.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
                        | DevExpress.XtraPrinting.BorderSide.Right)));
            this.tableCell5.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "MM_PickingApply.ItemCode")});
            this.tableCell5.Dpi = 100F;
            this.tableCell5.Name = "tableCell5";
            this.tableCell5.StylePriority.UseBorders = false;
            this.tableCell5.StylePriority.UseTextAlignment = false;
            this.tableCell5.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.tableCell5.Weight = 1.1804627690245275D;
            // 
            // label7
            // 
            this.label7.BackColor = System.Drawing.Color.Transparent;
            this.label7.BorderColor = System.Drawing.Color.Black;
            this.label7.BorderDashStyle = DevExpress.XtraPrinting.BorderDashStyle.Solid;
            this.label7.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Top | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.label7.BorderWidth = 1F;
            this.label7.Dpi = 100F;
            this.label7.Font = new System.Drawing.Font("宋体", 10.2F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label7.ForeColor = System.Drawing.Color.Black;
            this.label7.LocationFloat = new DevExpress.Utils.PointFloat(484.8988F, 132.2916F);
            this.label7.Name = "label7";
            this.label7.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.label7.SizeF = new System.Drawing.SizeF(55.64288F, 24.99999F);
            this.label7.StylePriority.UseBackColor = false;
            this.label7.StylePriority.UseBorderColor = false;
            this.label7.StylePriority.UseBorderDashStyle = false;
            this.label7.StylePriority.UseBorders = false;
            this.label7.StylePriority.UseBorderWidth = false;
            this.label7.StylePriority.UseFont = false;
            this.label7.StylePriority.UseForeColor = false;
            this.label7.StylePriority.UsePadding = false;
            this.label7.StylePriority.UseTextAlignment = false;
            this.label7.Text = "备注：";
            this.label7.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // label5
            // 
            this.label5.BackColor = System.Drawing.Color.Transparent;
            this.label5.BorderColor = System.Drawing.Color.Black;
            this.label5.BorderDashStyle = DevExpress.XtraPrinting.BorderDashStyle.Solid;
            this.label5.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
                        | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.label5.BorderWidth = 1F;
            this.label5.Dpi = 100F;
            this.label5.Font = new System.Drawing.Font("宋体", 10.2F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label5.ForeColor = System.Drawing.Color.Black;
            this.label5.LocationFloat = new DevExpress.Utils.PointFloat(0.8333365F, 132.2916F);
            this.label5.Name = "label5";
            this.label5.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.label5.SizeF = new System.Drawing.SizeF(91.83027F, 24.99999F);
            this.label5.StylePriority.UseBackColor = false;
            this.label5.StylePriority.UseBorderColor = false;
            this.label5.StylePriority.UseBorderDashStyle = false;
            this.label5.StylePriority.UseBorders = false;
            this.label5.StylePriority.UseBorderWidth = false;
            this.label5.StylePriority.UseFont = false;
            this.label5.StylePriority.UseForeColor = false;
            this.label5.StylePriority.UsePadding = false;
            this.label5.StylePriority.UseTextAlignment = false;
            this.label5.Text = "单别:";
            this.label5.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // tableRow1
            // 
            this.tableRow1.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
                        | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableRow1.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
                        this.tableCell4,
                        this.tableCell5,
                        this.tableCell7,
                        this.tableCell8,
                        this.tableCell10});
            this.tableRow1.Dpi = 100F;
            this.tableRow1.Name = "tableRow1";
            this.tableRow1.StylePriority.UseBorders = false;
            this.tableRow1.Weight = 0.75D;
            // 
            // table4
            // 
            this.table4.Dpi = 100F;
            this.table4.LocationFloat = new DevExpress.Utils.PointFloat(223.1284F, 157.2917F);
            this.table4.Name = "table4";
            this.table4.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
                        this.tableRow3});
            this.table4.SizeF = new System.Drawing.SizeF(538.0382F, 25F);
            this.table4.StylePriority.UseTextAlignment = false;
            this.table4.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // label20
            // 
            this.label20.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Right | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.label20.Dpi = 100F;
            this.label20.LocationFloat = new DevExpress.Utils.PointFloat(222.5034F, 0F);
            this.label20.Name = "label20";
            this.label20.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.label20.SizeF = new System.Drawing.SizeF(538.6633F, 25F);
            this.label20.StylePriority.UseBorders = false;
            // 
            // table2
            // 
            this.table2.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
                        | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.table2.Dpi = 100F;
            this.table2.LocationFloat = new DevExpress.Utils.PointFloat(0F, 0F);
            this.table2.Name = "table2";
            this.table2.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
                        this.tableRow2});
            this.table2.SizeF = new System.Drawing.SizeF(222.8653F, 25F);
            this.table2.StylePriority.UseBorders = false;
            // 
            // label13
            // 
            this.label13.BackColor = System.Drawing.Color.Transparent;
            this.label13.BorderColor = System.Drawing.Color.Black;
            this.label13.BorderDashStyle = DevExpress.XtraPrinting.BorderDashStyle.Solid;
            this.label13.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Top | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.label13.BorderWidth = 1F;
            this.label13.Dpi = 100F;
            this.label13.Font = new System.Drawing.Font("宋体", 10.2F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label13.ForeColor = System.Drawing.Color.Black;
            this.label13.LocationFloat = new DevExpress.Utils.PointFloat(346.6375F, 132.2916F);
            this.label13.Name = "label13";
            this.label13.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.label13.SizeF = new System.Drawing.SizeF(138.2614F, 24.99999F);
            this.label13.StylePriority.UseBackColor = false;
            this.label13.StylePriority.UseBorderColor = false;
            this.label13.StylePriority.UseBorderDashStyle = false;
            this.label13.StylePriority.UseBorders = false;
            this.label13.StylePriority.UseBorderWidth = false;
            this.label13.StylePriority.UseFont = false;
            this.label13.StylePriority.UseForeColor = false;
            this.label13.StylePriority.UsePadding = false;
            this.label13.StylePriority.UseTextAlignment = false;
            this.label13.Text = "西子富沃德";
            this.label13.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // formattingRule1
            // 
            this.formattingRule1.Name = "formattingRule1";
            // 
            // label15
            // 
            this.label15.BackColor = System.Drawing.Color.Transparent;
            this.label15.BorderColor = System.Drawing.Color.Black;
            this.label15.BorderDashStyle = DevExpress.XtraPrinting.BorderDashStyle.Solid;
            this.label15.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Top | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.label15.BorderWidth = 1F;
            this.label15.Dpi = 100F;
            this.label15.Font = new System.Drawing.Font("宋体", 10.2F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label15.ForeColor = System.Drawing.Color.Black;
            this.label15.LocationFloat = new DevExpress.Utils.PointFloat(288.3746F, 132.2916F);
            this.label15.Name = "label15";
            this.label15.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.label15.SizeF = new System.Drawing.SizeF(58.67953F, 24.99999F);
            this.label15.StylePriority.UseBackColor = false;
            this.label15.StylePriority.UseBorderColor = false;
            this.label15.StylePriority.UseBorderDashStyle = false;
            this.label15.StylePriority.UseBorders = false;
            this.label15.StylePriority.UseBorderWidth = false;
            this.label15.StylePriority.UseFont = false;
            this.label15.StylePriority.UseForeColor = false;
            this.label15.StylePriority.UsePadding = false;
            this.label15.StylePriority.UseTextAlignment = false;
            this.label15.Text = "2002";
            this.label15.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // tableCell2
            // 
            this.tableCell2.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell2.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "MM_PickingApply.Qty")});
            this.tableCell2.Dpi = 100F;
            this.tableCell2.Name = "tableCell2";
            this.tableCell2.StylePriority.UseBorders = false;
            this.tableCell2.StylePriority.UseTextAlignment = false;
            summary2.FormatString = "{0:#.####}";
            summary2.Running = DevExpress.XtraReports.UI.SummaryRunning.Report;
            this.tableCell2.Summary = summary2;
            this.tableCell2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell2.Weight = 1.5523112028959472D;
            // 
            // tableRow3
            // 
            this.tableRow3.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
                        this.tableCell3,
                        this.tableCell16,
                        this.tableCell17,
                        this.tableCell18});
            this.tableRow3.Dpi = 100F;
            this.tableRow3.Name = "tableRow3";
            this.tableRow3.Weight = 1D;
            // 
            // tableCell8
            // 
            this.tableCell8.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
                        | DevExpress.XtraPrinting.BorderSide.Right)));
            this.tableCell8.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "MM_PickingApply.BaseNum")});
            this.tableCell8.Dpi = 100F;
            this.tableCell8.Multiline = true;
            this.tableCell8.Name = "tableCell8";
            this.tableCell8.StylePriority.UseBorders = false;
            this.tableCell8.StylePriority.UseTextAlignment = false;
            this.tableCell8.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.tableCell8.Weight = 0.41900415683343106D;
            // 
            // tableRow2
            // 
            this.tableRow2.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
                        this.tableCell1,
                        this.tableCell2});
            this.tableRow2.Dpi = 100F;
            this.tableRow2.Name = "tableRow2";
            this.tableRow2.Weight = 1D;
            // 
            // table1
            // 
            this.table1.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
                        | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.table1.Dpi = 100F;
            this.table1.LocationFloat = new DevExpress.Utils.PointFloat(0.4166762F, 0F);
            this.table1.Name = "table1";
            this.table1.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
                        this.tableRow1,
                        this.tableRow6});
            this.table1.SizeF = new System.Drawing.SizeF(761.5833F, 38.54167F);
            this.table1.StylePriority.UseBorders = false;
            // 
            // docNums
            // 
            this.docNums.Name = "docNums";
            // 
            // label19
            // 
            this.label19.BorderDashStyle = DevExpress.XtraPrinting.BorderDashStyle.Dot;
            this.label19.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.label19.Dpi = 100F;
            this.label19.LocationFloat = new DevExpress.Utils.PointFloat(67.68904F, 25F);
            this.label19.Name = "label19";
            this.label19.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.label19.SizeF = new System.Drawing.SizeF(155.0227F, 23F);
            this.label19.StylePriority.UseBorderDashStyle = false;
            this.label19.StylePriority.UseBorders = false;
            this.label19.StylePriority.UseTextAlignment = false;
            this.label19.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // tableCell9
            // 
            this.tableCell9.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell9.Dpi = 100F;
            this.tableCell9.Name = "tableCell9";
            this.tableCell9.StylePriority.UseBorders = false;
            this.tableCell9.StylePriority.UseTextAlignment = false;
            this.tableCell9.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell9.Weight = 0.10702258363143388D;
            // 
            // label14
            // 
            this.label14.BackColor = System.Drawing.Color.Transparent;
            this.label14.BorderColor = System.Drawing.Color.Black;
            this.label14.BorderDashStyle = DevExpress.XtraPrinting.BorderDashStyle.Solid;
            this.label14.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Top | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.label14.BorderWidth = 1F;
            this.label14.Dpi = 100F;
            this.label14.Font = new System.Drawing.Font("宋体", 10.2F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label14.ForeColor = System.Drawing.Color.Black;
            this.label14.LocationFloat = new DevExpress.Utils.PointFloat(222.92F, 132.2916F);
            this.label14.Name = "label14";
            this.label14.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.label14.SizeF = new System.Drawing.SizeF(65.45456F, 24.99999F);
            this.label14.StylePriority.UseBackColor = false;
            this.label14.StylePriority.UseBorderColor = false;
            this.label14.StylePriority.UseBorderDashStyle = false;
            this.label14.StylePriority.UseBorders = false;
            this.label14.StylePriority.UseBorderWidth = false;
            this.label14.StylePriority.UseFont = false;
            this.label14.StylePriority.UseForeColor = false;
            this.label14.StylePriority.UsePadding = false;
            this.label14.StylePriority.UseTextAlignment = false;
            this.label14.Text = "工厂：";
            this.label14.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // tableCell4
            // 
            this.tableCell4.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
                        | DevExpress.XtraPrinting.BorderSide.Right)));
            this.tableCell4.Dpi = 100F;
            this.tableCell4.Name = "tableCell4";
            this.tableCell4.StylePriority.UseBorders = false;
            this.tableCell4.StylePriority.UseTextAlignment = false;
            summary3.Func = DevExpress.XtraReports.UI.SummaryFunc.RecordNumber;
            summary3.Running = DevExpress.XtraReports.UI.SummaryRunning.Report;
            this.tableCell4.Summary = summary3;
            this.tableCell4.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.tableCell4.Weight = 0.10769302852291629D;
            // 
            // label2
            // 
            this.label2.BorderWidth = 0F;
            this.label2.Dpi = 100F;
            this.label2.Font = new System.Drawing.Font("宋体", 16F);
            this.label2.LocationFloat = new DevExpress.Utils.PointFloat(147.7083F, 82.08335F);
            this.label2.Multiline = true;
            this.label2.Name = "label2";
            this.label2.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.label2.SizeF = new System.Drawing.SizeF(478.0417F, 26.66667F);
            this.label2.StylePriority.UseBorderWidth = false;
            this.label2.StylePriority.UseFont = false;
            this.label2.StylePriority.UseTextAlignment = false;
            this.label2.Text = "领料申请单";
            this.label2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // sqlDataSource1
            // 
            this.sqlDataSource1.ConnectionName = "10.18.0.94_WMS_Connection";
            msSqlConnectionParameters1.AuthorizationType = DevExpress.DataAccess.ConnectionParameters.MsSqlAuthorizationType.SqlServer;
            msSqlConnectionParameters1.DatabaseName = "XZ_WMS";
            msSqlConnectionParameters1.Password = "Xzfwd@1234";
            msSqlConnectionParameters1.ServerName = "10.18.0.94";
            msSqlConnectionParameters1.UserName = "sa";
            this.sqlDataSource1.ConnectionParameters = msSqlConnectionParameters1;
            this.sqlDataSource1.Name = "sqlDataSource1";
            columnExpression1.ColumnName = "DocNum";
            table5.MetaSerializable = "30|30|125|260";
            table5.Name = "MM_PickingApply";
            columnExpression1.Table = table5;
            column1.Expression = columnExpression1;
            columnExpression2.ColumnName = "Status";
            columnExpression2.Table = table5;
            column2.Expression = columnExpression2;
            columnExpression3.ColumnName = "DetailID";
            table6.MetaSerializable = "185|30|125|500";
            table6.Name = "MM_PickingApplyDetail";
            columnExpression3.Table = table6;
            column3.Expression = columnExpression3;
            column4.Alias = "MM_PickingApplyDetail_DocNum";
            columnExpression4.ColumnName = "DocNum";
            columnExpression4.Table = table6;
            column4.Expression = columnExpression4;
            columnExpression5.ColumnName = "BaseNum";
            columnExpression5.Table = table6;
            column5.Expression = columnExpression5;
            columnExpression6.ColumnName = "BaseLine";
            columnExpression6.Table = table6;
            column6.Expression = columnExpression6;
            columnExpression7.ColumnName = "ItemCode";
            columnExpression7.Table = table6;
            column7.Expression = columnExpression7;
            columnExpression8.ColumnName = "ItemName";
            columnExpression8.Table = table6;
            column8.Expression = columnExpression8;
            columnExpression9.ColumnName = "SupplierCode";
            columnExpression9.Table = table6;
            column9.Expression = columnExpression9;
            columnExpression10.ColumnName = "SupplierName";
            columnExpression10.Table = table6;
            column10.Expression = columnExpression10;
            columnExpression11.ColumnName = "Qty";
            columnExpression11.Table = table6;
            column11.Expression = columnExpression11;
            columnExpression12.ColumnName = "Unit";
            columnExpression12.Table = table6;
            column12.Expression = columnExpression12;
            columnExpression13.ColumnName = "Remark";
            columnExpression13.Table = table6;
            column13.Expression = columnExpression13;
            columnExpression14.ColumnName = "IsDelete";
            columnExpression14.Table = table6;
            column14.Expression = columnExpression14;
            columnExpression15.ColumnName = "CUser";
            columnExpression15.Table = table6;
            column15.Expression = columnExpression15;
            columnExpression16.ColumnName = "CTime";
            columnExpression16.Table = table6;
            column16.Expression = columnExpression16;
            columnExpression17.ColumnName = "MUser";
            columnExpression17.Table = table6;
            column17.Expression = columnExpression17;
            columnExpression18.ColumnName = "MTime";
            columnExpression18.Table = table6;
            column18.Expression = columnExpression18;
            columnExpression19.ColumnName = "DUser";
            columnExpression19.Table = table6;
            column19.Expression = columnExpression19;
            columnExpression20.ColumnName = "DTime";
            columnExpression20.Table = table6;
            column20.Expression = columnExpression20;
            columnExpression21.ColumnName = "SubcontractingApplicationNum";
            columnExpression21.Table = table6;
            column21.Expression = columnExpression21;
            column22.Alias = "MM_PickingApplyDetail_Status";
            columnExpression22.ColumnName = "Status";
            columnExpression22.Table = table6;
            column22.Expression = columnExpression22;
            selectQuery1.Columns.Add(column1);
            selectQuery1.Columns.Add(column2);
            selectQuery1.Columns.Add(column3);
            selectQuery1.Columns.Add(column4);
            selectQuery1.Columns.Add(column5);
            selectQuery1.Columns.Add(column6);
            selectQuery1.Columns.Add(column7);
            selectQuery1.Columns.Add(column8);
            selectQuery1.Columns.Add(column9);
            selectQuery1.Columns.Add(column10);
            selectQuery1.Columns.Add(column11);
            selectQuery1.Columns.Add(column12);
            selectQuery1.Columns.Add(column13);
            selectQuery1.Columns.Add(column14);
            selectQuery1.Columns.Add(column15);
            selectQuery1.Columns.Add(column16);
            selectQuery1.Columns.Add(column17);
            selectQuery1.Columns.Add(column18);
            selectQuery1.Columns.Add(column19);
            selectQuery1.Columns.Add(column20);
            selectQuery1.Columns.Add(column21);
            selectQuery1.Columns.Add(column22);
            selectQuery1.FilterString = "[MM_PickingApply.DocNum] In (?docNums) And [MM_PickingApplyDetail.IsDelete] = \'0\'" +
                "";
            selectQuery1.Name = "MM_PickingApply";
            queryParameter1.Name = "docNum";
            queryParameter1.Type = typeof(string);
            queryParameter2.Name = "docNums";
            queryParameter2.Type = typeof(DevExpress.DataAccess.Expression);
            queryParameter2.Value = new DevExpress.DataAccess.Expression("[Parameters.docNums]", typeof(string[]));
            selectQuery1.Parameters.Add(queryParameter1);
            selectQuery1.Parameters.Add(queryParameter2);
            relationColumnInfo1.NestedKeyColumn = "DocNum";
            relationColumnInfo1.ParentKeyColumn = "DocNum";
            join1.KeyColumns.Add(relationColumnInfo1);
            join1.Nested = table6;
            join1.Parent = table5;
            selectQuery1.Relations.Add(join1);
            selectQuery1.Tables.Add(table5);
            selectQuery1.Tables.Add(table6);
            this.sqlDataSource1.Queries.AddRange(new DevExpress.DataAccess.Sql.SqlQuery[] {
                        selectQuery1});
            this.sqlDataSource1.ResultSchemaSerializable = resources.GetString("sqlDataSource1.ResultSchemaSerializable");
            // 
            // label9
            // 
            this.label9.BackColor = System.Drawing.Color.Transparent;
            this.label9.BorderColor = System.Drawing.Color.Black;
            this.label9.BorderDashStyle = DevExpress.XtraPrinting.BorderDashStyle.Solid;
            this.label9.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Right | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.label9.BorderWidth = 1F;
            this.label9.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "MM_PickingApply.MM_PickingApplyDetail_DocNum")});
            this.label9.Dpi = 100F;
            this.label9.Font = new System.Drawing.Font("宋体", 10.2F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label9.ForeColor = System.Drawing.Color.Black;
            this.label9.LocationFloat = new DevExpress.Utils.PointFloat(92.6636F, 157.2917F);
            this.label9.Name = "label9";
            this.label9.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.label9.SizeF = new System.Drawing.SizeF(130.0483F, 24.99999F);
            this.label9.StylePriority.UseBackColor = false;
            this.label9.StylePriority.UseBorderColor = false;
            this.label9.StylePriority.UseBorderDashStyle = false;
            this.label9.StylePriority.UseBorders = false;
            this.label9.StylePriority.UseBorderWidth = false;
            this.label9.StylePriority.UseFont = false;
            this.label9.StylePriority.UseForeColor = false;
            this.label9.StylePriority.UsePadding = false;
            this.label9.StylePriority.UseTextAlignment = false;
            this.label9.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // barCode1
            // 
            this.barCode1.Alignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.barCode1.AutoModule = true;
            this.barCode1.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.barCode1.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "MM_PickingApply.DocNum")});
            this.barCode1.Dpi = 100F;
            this.barCode1.Font = new System.Drawing.Font("宋体", 7.8F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.barCode1.LocationFloat = new DevExpress.Utils.PointFloat(625.7501F, 25.30589F);
            this.barCode1.Name = "barCode1";
            this.barCode1.Padding = new DevExpress.XtraPrinting.PaddingInfo(10, 10, 0, 0, 100F);
            this.barCode1.ShowText = false;
            this.barCode1.SizeF = new System.Drawing.SizeF(104.9999F, 83.44412F);
            this.barCode1.StylePriority.UseBorders = false;
            this.barCode1.StylePriority.UseFont = false;
            this.barCode1.StylePriority.UseTextAlignment = false;
            qRCodeGenerator1.CompactionMode = DevExpress.XtraPrinting.BarCode.QRCodeCompactionMode.Byte;
            this.barCode1.Symbology = qRCodeGenerator1;
            this.barCode1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // tableCell17
            // 
            this.tableCell17.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Right | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell17.Dpi = 100F;
            this.tableCell17.Name = "tableCell17";
            this.tableCell17.StylePriority.UseBorders = false;
            this.tableCell17.Text = "供应商名称：";
            this.tableCell17.Weight = 0.7043476336573492D;
            // 
            // tableCell15
            // 
            this.tableCell15.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell15.Dpi = 100F;
            this.tableCell15.Name = "tableCell15";
            this.tableCell15.StylePriority.UseBorders = false;
            this.tableCell15.StylePriority.UseTextAlignment = false;
            this.tableCell15.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell15.Weight = 0.43998256838606303D;
            // 
            // BottomMargin
            // 
            this.BottomMargin.Dpi = 100F;
            this.BottomMargin.HeightF = 22F;
            this.BottomMargin.Name = "BottomMargin";
            // 
            // tableCell11
            // 
            this.tableCell11.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell11.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "MM_PickingApply.ItemName")});
            this.tableCell11.Dpi = 100F;
            this.tableCell11.Name = "tableCell11";
            this.tableCell11.StylePriority.UseBorders = false;
            this.tableCell11.StylePriority.UseTextAlignment = false;
            this.tableCell11.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.tableCell11.Weight = 1.1804627199112836D;
            // 
            // tableRow5
            // 
            this.tableRow5.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
                        this.tableCell31,
                        this.tableCell26,
                        this.tableCell27,
                        this.tableCell30});
            this.tableRow5.Dpi = 100F;
            this.tableRow5.Name = "tableRow5";
            this.tableRow5.Weight = 1D;
            // 
            // tableCell31
            // 
            this.tableCell31.Dpi = 100F;
            this.tableCell31.Name = "tableCell31";
            this.tableCell31.StylePriority.UseTextAlignment = false;
            this.tableCell31.Text = "序号";
            this.tableCell31.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.tableCell31.Weight = 0.090860403108820112D;
            // 
            // tableCell13
            // 
            this.tableCell13.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell13.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "MM_PickingApply.Unit")});
            this.tableCell13.Dpi = 100F;
            this.tableCell13.Name = "tableCell13";
            this.tableCell13.StylePriority.UseBorders = false;
            this.tableCell13.StylePriority.UseTextAlignment = false;
            this.tableCell13.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.tableCell13.Weight = 0.30635790004290891D;
            // 
            // tableCell10
            // 
            this.tableCell10.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
                        | DevExpress.XtraPrinting.BorderSide.Right)));
            this.tableCell10.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "MM_PickingApply.Remark")});
            this.tableCell10.Dpi = 100F;
            this.tableCell10.Name = "tableCell10";
            this.tableCell10.StylePriority.UseBorders = false;
            this.tableCell10.StylePriority.UseTextAlignment = false;
            this.tableCell10.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.tableCell10.Weight = 0.43921610710289882D;
            // 
            // label1
            // 
            this.label1.Dpi = 100F;
            this.label1.LocationFloat = new DevExpress.Utils.PointFloat(0.6798427F, 109.393F);
            this.label1.Name = "label1";
            this.label1.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.label1.SizeF = new System.Drawing.SizeF(91.98376F, 22.89861F);
            this.label1.StylePriority.UseTextAlignment = false;
            this.label1.Text = "制表日期:";
            this.label1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // TopMargin
            // 
            this.TopMargin.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
                        this.table4,
                        this.barCode1,
                        this.label4,
                        this.label9,
                        this.label8,
                        this.label7,
                        this.label6,
                        this.label5,
                        this.table3,
                        this.label1,
                        this.label2,
                        this.label14,
                        this.label15,
                        this.label12,
                        this.label13,
                        this.label16,
                        this.label3});
            this.TopMargin.Dpi = 100F;
            this.TopMargin.HeightF = 232.2917F;
            this.TopMargin.Name = "TopMargin";
            // 
            // label16
            // 
            this.label16.BackColor = System.Drawing.Color.Transparent;
            this.label16.BorderColor = System.Drawing.Color.Black;
            this.label16.BorderDashStyle = DevExpress.XtraPrinting.BorderDashStyle.Solid;
            this.label16.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Right | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.label16.BorderWidth = 1F;
            this.label16.Dpi = 100F;
            this.label16.Font = new System.Drawing.Font("宋体", 10.2F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label16.ForeColor = System.Drawing.Color.Black;
            this.label16.LocationFloat = new DevExpress.Utils.PointFloat(625.7501F, 182.2917F);
            this.label16.Name = "label16";
            this.label16.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.label16.SizeF = new System.Drawing.SizeF(135.8332F, 50F);
            this.label16.StylePriority.UseBackColor = false;
            this.label16.StylePriority.UseBorderColor = false;
            this.label16.StylePriority.UseBorderDashStyle = false;
            this.label16.StylePriority.UseBorders = false;
            this.label16.StylePriority.UseBorderWidth = false;
            this.label16.StylePriority.UseFont = false;
            this.label16.StylePriority.UseForeColor = false;
            this.label16.StylePriority.UsePadding = false;
            this.label16.StylePriority.UseTextAlignment = false;
            this.label16.Text = "备注";
            this.label16.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // label6
            // 
            this.label6.BackColor = System.Drawing.Color.Transparent;
            this.label6.BorderColor = System.Drawing.Color.Black;
            this.label6.BorderDashStyle = DevExpress.XtraPrinting.BorderDashStyle.Solid;
            this.label6.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Top | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.label6.BorderWidth = 1F;
            this.label6.Dpi = 100F;
            this.label6.Font = new System.Drawing.Font("宋体", 10.2F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label6.ForeColor = System.Drawing.Color.Black;
            this.label6.LocationFloat = new DevExpress.Utils.PointFloat(92.6636F, 132.2916F);
            this.label6.Name = "label6";
            this.label6.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.label6.SizeF = new System.Drawing.SizeF(130.0483F, 24.99999F);
            this.label6.StylePriority.UseBackColor = false;
            this.label6.StylePriority.UseBorderColor = false;
            this.label6.StylePriority.UseBorderDashStyle = false;
            this.label6.StylePriority.UseBorders = false;
            this.label6.StylePriority.UseBorderWidth = false;
            this.label6.StylePriority.UseFont = false;
            this.label6.StylePriority.UseForeColor = false;
            this.label6.StylePriority.UsePadding = false;
            this.label6.StylePriority.UseTextAlignment = false;
            this.label6.Text = "申请单";
            this.label6.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // tableRow6
            // 
            this.tableRow6.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
                        this.tableCell9,
                        this.tableCell11,
                        this.tableCell13,
                        this.tableCell14,
                        this.tableCell15});
            this.tableRow6.Dpi = 100F;
            this.tableRow6.Name = "tableRow6";
            this.tableRow6.Weight = 0.79166671752929685D;
            // 
            // Detail
            // 
            this.Detail.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
                        this.table1});
            this.Detail.Dpi = 100F;
            this.Detail.HeightF = 38.54167F;
            this.Detail.Name = "Detail";
            // 
            // tableCell30
            // 
            this.tableCell30.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell30.Dpi = 100F;
            this.tableCell30.Multiline = true;
            this.tableCell30.Name = "tableCell30";
            this.tableCell30.StylePriority.UseBorders = false;
            this.tableCell30.StylePriority.UseTextAlignment = false;
            this.tableCell30.Text = "采购单号\r\n采购行号";
            this.tableCell30.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.tableCell30.Weight = 0.36620729435613536D;
            // 
            // tableCell26
            // 
            this.tableCell26.Dpi = 100F;
            this.tableCell26.Multiline = true;
            this.tableCell26.Name = "tableCell26";
            this.tableCell26.StylePriority.UseTextAlignment = false;
            this.tableCell26.Text = "物料编号\r\n物料名称";
            this.tableCell26.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.tableCell26.Weight = 1.0279582573401029D;
            // 
            // label4
            // 
            this.label4.BorderWidth = 0F;
            this.label4.Dpi = 100F;
            this.label4.Font = new System.Drawing.Font("宋体", 20F);
            this.label4.LocationFloat = new DevExpress.Utils.PointFloat(147.7083F, 44.79167F);
            this.label4.Multiline = true;
            this.label4.Name = "label4";
            this.label4.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.label4.SizeF = new System.Drawing.SizeF(478.0417F, 37.29168F);
            this.label4.StylePriority.UseBorderWidth = false;
            this.label4.StylePriority.UseFont = false;
            this.label4.StylePriority.UseTextAlignment = false;
            this.label4.Text = "浙江西子富沃德电机有限公司";
            this.label4.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // tableCell27
            // 
            this.tableCell27.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell27.Dpi = 100F;
            this.tableCell27.Multiline = true;
            this.tableCell27.Name = "tableCell27";
            this.tableCell27.StylePriority.UseBorders = false;
            this.tableCell27.StylePriority.UseTextAlignment = false;
            this.tableCell27.Text = "数量\r\n单位";
            this.tableCell27.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.tableCell27.Weight = 0.26536116594720943D;
            // 
            // tableCell1
            // 
            this.tableCell1.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell1.Dpi = 100F;
            this.tableCell1.Name = "tableCell1";
            this.tableCell1.StylePriority.UseBorders = false;
            this.tableCell1.StylePriority.UseTextAlignment = false;
            this.tableCell1.Text = "数量合计";
            this.tableCell1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.tableCell1.Weight = 0.67634277949505306D;
            // 
            // 委外领料申请
            // 
            this.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
                        this.TopMargin,
                        this.Detail,
                        this.BottomMargin,
                        this.ReportFooter});
            this.CalculatedFields.AddRange(new DevExpress.XtraReports.UI.CalculatedField[] {
                        this.calculatedField1});
            this.ComponentStorage.AddRange(new System.ComponentModel.IComponent[] {
                        this.sqlDataSource1});
            this.DataMember = "MM_PickingApply";
            this.DataSource = this.sqlDataSource1;
            this.DisplayName = "委外领料申请";
            this.Dpi = 100F;
            this.Font = new System.Drawing.Font("宋体", 10.2F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.FormattingRuleSheet.AddRange(new DevExpress.XtraReports.UI.FormattingRule[] {
                        this.formattingRule1});
            this.Margins = new System.Drawing.Printing.Margins(30, 35, 232, 22);
            this.Name = "委外领料申请";
            this.PageHeight = 1169;
            this.PageWidth = 827;
            this.PaperKind = System.Drawing.Printing.PaperKind.A4;
            this.Parameters.AddRange(new DevExpress.XtraReports.Parameters.Parameter[] {
                        this.docNums});
            this.Version = "16.1";
            ((System.ComponentModel.ISupportInitialize)(this.table3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.table4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.table2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.table1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();
        }
    }
}
