using System.ComponentModel;
using SqlSugar;
//Chloe框架


namespace HZ.WMS.Entity.Sys
{
	/// <summary>
    /// 
    /// </summary>
    [SugarTable("Sys_DbBackupConfig")]
    public class Sys_DbBackupConfig:BaseEntity
    {
                /// <summary>
        /// 数据库备份记录ID
        /// </summary>
        [Description("数据库备份记录ID")]
        [SugarColumn(IsPrimaryKey = true)]
        public string DbBackupSettingID {get;set;}
        /// <summary>
        /// 数据库名称
        /// </summary>
        [Description("数据库名称")]
        public string DbName {get;set;}
        /// <summary>
        /// 数据库服务器
        /// </summary>
        [Description("数据库服务器")]
        public string DbServer {get;set;}
        /// <summary>
        /// 备份文件路径
        /// </summary>
        [Description("备份文件路径")]
        public string BackupFilePath {get;set;}
        /// <summary>
        /// 
        /// </summary>
        [Description("")]
        public string RestoreFilePath {get;set;}
        /// <summary>
        /// 是否压缩备份
        /// </summary>
        [Description("是否压缩备份")]
        public bool? IsCompressed {get;set;}
        /// <summary>
        /// 登录账号
        /// </summary>
        [Description("登录账号")]
        public string DbAccount {get;set;}
        /// <summary>
        /// 登录密码
        /// </summary>
        [Description("登录密码")]
        public string DbPassword {get;set;}

    }
}

