using SqlSugar;
using HZ.WMS.Entity.MM;
using HZ.WMS.Entity.PP;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HZ.WMS.Entity.EAP;

namespace HZ.WMS.Application.PP
{
    /// <summary>
    /// 完工入库视图
    /// </summary>
    public class PP_ProductionReportExportViewApp : BaseApp<PP_ProductionReportExport_View>
    {
        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public PP_ProductionReportExportViewApp() : base(){ }


        #endregion

        #region 获取EAP出厂编号

        public List<PP_ProductionReportExport_View> HandleEapSerialNo(List<PP_ProductionReportExport_View> assignSerialList)
        {
            if (assignSerialList != null)
            {
                var pageNo = assignSerialList.Count() / 500;
                if (assignSerialList.Count() % 500 > 0)
                {
                    pageNo = pageNo + 1;
                }

                List<SGEAP_CUS_Order> cusOrderList = new List<SGEAP_CUS_Order>();

                for (int i = 0; i < pageNo; i++)
                {
                    int startNo = i * 500;
                    int endNo = (i + 1) * 500;
                    if (i == pageNo - 1)
                    {
                        endNo = assignSerialList.Count;
                    }
                    List<string> contractNoList = new List<string>();
                    List<string> orderNoList = new List<string>();
                    for (int j = startNo; j < endNo; j++)
                    {
                        contractNoList.Add(assignSerialList[j].ZORD_CONT);
                        orderNoList.Add(assignSerialList[j].CustomerOrderNum);
                    }
                    cusOrderList.AddRange(DbContextForEAP.Queryable<SGEAP_CUS_Order>().Where(t => contractNoList.Contains(t.ContractNo) && orderNoList.Contains(t.OrderNo))?.ToList());
                }

                Dictionary<string, string> dic = new Dictionary<string, string>();
                foreach (SGEAP_CUS_Order cusOrder in cusOrderList)
                {

                    if (!dic.ContainsKey(cusOrder.ContractNo + cusOrder.OrderNo))
                    {
                        dic.Add(cusOrder.ContractNo + cusOrder.OrderNo, cusOrder.SerialNo);
                    }
                }

                foreach (PP_ProductionReportExport_View assignSerialNo in assignSerialList)
                {
                    if (dic.ContainsKey(assignSerialNo.ZORD_CONT + assignSerialNo.CustomerOrderNum))
                    {
                        assignSerialNo.EapSerialNo = dic[assignSerialNo.ZORD_CONT + assignSerialNo.CustomerOrderNum];
                    }
                }

            }

            return assignSerialList;
        }

        #endregion


    }
}
