using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.SD
{
    /// <summary>
    /// 交运计划申请明细
    /// </summary>
    [SugarTable("SD_ShippingPlanDetail")]
    public class SD_ShippingPlanDetail : BaseEntity
    {
        /// <summary> 
        /// wms单号
        /// </summary> 
        [Description("发运单号")]
        public string DocNum { get; set; }

        /// <summary> 
        /// 订单类型
        /// </summary> 
        [Description("订单类型")]
        public string SalesOrderType { get; set; }

        /// <summary> 
        /// 销售单号
        /// </summary> 
        [Description("订单号")]
        public string SalesOrderNumber { get; set; }

        /// <summary> 
        /// 销售单行号
        /// </summary> 
        [Description("订单行号")]
        public int? SalesLine { get; set; }

        /// <summary> 
        /// 客户编号
        /// </summary> 
        [Description("客户编号")]
        public string CustomerCode { get; set; }

        /// <summary> 
        /// 客户订单号
        /// </summary> 
        [Description("客户订单号")]
        public string CustomerOrderNum { get; set; }

        /// <summary> 
        /// 合同单号
        /// </summary> 
        [Description("合同单号")]
        public string CONT { get; set; }

        /// <summary> 
        /// 客户名称
        /// </summary> 
        [Description("客户名称")]
        public string CustomerName { get; set; }

        /// <summary> 
        /// 承诺的交货日期
        /// </summary> 
        [Description("承诺的交货日期")]
        public DateTime? DeliveryDate { get; set; }

        /// <summary> 
        /// 出厂编号
        /// </summary> 
        [Description("出厂编号")]
        public string OUTNO { get; set; }

        /// <summary> 
        /// 物料名称
        /// </summary> 
        [Description("物料名称")]
        public string ItemName { get; set; }

        /// <summary> 
        /// 客户件号
        /// 211123-cc添加
        /// </summary> 
        [Description("客户件号")]
        public string CustomerItemCode { get; set; }

        /// <summary> 
        /// 数量
        /// </summary> 
        [Description("数量")]
        public decimal? ShippingPlanDetailQty { get; set; }

        /// <summary>
        /// 结算地址
        /// </summary>
        [Description("结算地址")]
        public string SettlementAdd { get; set; }

        /// <summary>
        /// 联系人
        /// </summary>
        [Description("联系人")]
        public string Contact { get; set; }

        /// <summary>
        /// 联系方式
        /// </summary>
        [Description("联系方式")]
        public string Telephone { get; set; }

        /// <summary> 
        /// 客户地址
        /// </summary> 
        [Description("客户地址")]
        public string CustomerAdd { get; set; }

        /// <summary> 
        /// 物料编号
        /// </summary> 
        [Description("物料编号")]
        public string ItemCode { get; set; }

        /// <summary> 
        /// 重量
        /// </summary> 
        [Description("重量")]
        public decimal? Weight { get; set; }

        /// <summary>
        /// 物流供应商编号
        /// </summary>
        [Description("物流供应商编号")]
        public string SupplierCode { get; set; }

        /// <summary>
        /// 物流供应商名称
        /// </summary>
        [Description("物流供应商")]
        public string SupplierName { get; set; }

        /// <summary> 
        /// 销售交货批
        /// </summary> 
        [Description("销售交货批")]
        public string BatchNum { get; set; }

        /// <summary>
        /// 计划入库时间
        /// </summary>
        [Description("计划入库时间")]
        public DateTime? PlanTime { get; set; }

        /// <summary>
        /// 实际入库时间
        /// </summary>
        [Description("实际入库时间")]
        public DateTime? ActualTime { get; set; }

        /// <summary> 
        /// 明细ID
        /// </summary> 
        [SugarColumn(IsPrimaryKey = true)]
        [Description("明细ID")]
        public string ShippingPlanDetailID { get; set; }

        /// <summary> 
        /// wms行号
        /// </summary> 
        [Description("发运行号")]
        public int? Line { get; set; }

        /// <summary> 
        /// 销售类型
        /// </summary> 
        [Description("销售类型")]
        public string SalesType { get; set; }

        /// <summary> 
        /// 销售组织
        /// </summary> 
        [Description("销售组织")]
        public string SalesOrganization { get; set; }

        /// <summary> 
        /// 创建日期
        /// </summary> 
        [Description("创建日期")]
        public DateTime? CreationDate { get; set; }

        /// <summary> 
        /// 时间
        /// </summary> 
        [Description("时间")]
        public DateTime? Time { get; set; }

        /// <summary> 
        /// 凭证日期
        /// </summary> 
        [Description("凭证日期")]
        public DateTime? VoucherDate { get; set; }

        /// <summary> 
        /// 分销渠道
        /// </summary> 
        [Description("分销渠道")]
        public string DistributionChannels { get; set; }

        /// <summary> 
        /// 产品组
        /// </summary> 
        [Description("产品组")]
        public string ProductGroup { get; set; }

        /// <summary> 
        /// 客户参考
        /// </summary> 
        [Description("客户参考")]
        public string BSTNK { get; set; }

        /// <summary> 
        /// 项目
        /// </summary> 
        [Description("项目名称")]
        public string Project { get; set; }

        /// <summary> 
        /// 项目类别
        /// </summary> 
        [Description("项目类别")]
        public string ProjectCategory { get; set; }

        /// <summary> 
        /// 利润中心
        /// </summary> 
        [Description("利润中心")]
        public string PRCTR { get; set; }

        /// <summary>
        /// 装运点/收货点
        /// </summary>
        [Description("装运点/收货点")]
        public string VSTEL { get; set; }

        /// <summary>
        /// 参考单据的单据编号
        /// </summary>
        [Description("参考单据的单据编号")]
        public string VGBEL { get; set; }

        /// <summary>
        /// 参考项目的项目号
        /// </summary>
        [Description("参考项目的项目号")]
        public int? VGPOS { get; set; }

        /// <summary> 
        /// 单位
        /// </summary> 
        [Description("单位")]
        public string Unit { get; set; }

        /// <summary> 
        /// 仓库编号
        /// </summary> 
        [Description("仓库编号")]
        public string WhsCode { get; set; }

        /// <summary> 
        /// 仓库名称
        /// </summary> 
        [Description("仓库名称")]
        public string WhsName { get; set; }

        /// <summary>
        /// 发货人编号
        /// </summary>
        [Description("发货人编号")]
        public string DeliveryUser { get; set; }

        /// <summary>
        /// 发货人名称
        /// </summary>
        [Description("发货人名称")]
        public string DeliveryUserName { get; set; }

        /// <summary>
        /// 生产备注
        /// 220412-cc添加
        /// </summary>
        [Description("生产备注")]
        public string ProductionRemark { get; set; }

        /// <summary>
        /// 发货方式
        /// 220412-cc添加
        /// </summary>
        [Description("发货方式")]
        public string ShippingType { get; set; }

        /// <summary>
        /// 是否交货导入
        /// </summary>
        [Description("是否交货导入")]
        public int? IsDeliveryImport { get; set; }

    }
}
