using System.Collections.Generic;
using SqlSugar;
//Chloe框架

namespace HZ.WMS.Entity.MD
{
    /// <summary>
    /// 供应商主数据
    /// </summary>
    public class MD_Supplier : BaseEntity
    {
        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        
        public string SupplierObjectID { get; set; }

        /// <summary>
        /// 供应商编号
        /// </summary>

        public string SupplierCode { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        public string SupplierName { get; set; }

        /// <summary>
        /// 供应商状态
        /// </summary>
        public int SupplierStatus { get; set; }

        /// <summary>
        /// 供应商邮件
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// 供应商城市
        /// </summary>
        public string City { get; set; }

        /// <summary>
        /// 联系电话
        /// </summary>
        public string Phone { get; set; }

        /// <summary>
        /// 传真
        /// </summary>
        public string Fax { get; set; }

        /// <summary>
        /// 联系人
        /// </summary>

        public string ContactPerson { get; set; }

        /// <summary>
        /// 地址
        /// </summary>
        public string Address { get; set; }
    }

    /// <summary>
    /// 
    /// </summary>
    public class MD_SupplierEquality : IEqualityComparer<MD_Supplier>
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="x"></param>
        /// <param name="y"></param>
        /// <returns></returns>
        public bool Equals(MD_Supplier x, MD_Supplier y)
        {
            return x.SupplierCode == y.SupplierCode;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public int GetHashCode(MD_Supplier obj)
        {
            if (obj == null)
            {
                return 0;
            }
            else
            {
                return obj.ToString().GetHashCode();
            }
        }
    }
}
