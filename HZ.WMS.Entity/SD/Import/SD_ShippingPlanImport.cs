using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.SD.Import
{
    public class SD_ShippingPlanImport
    {
        /// <summary> 
        /// 明细ID
        /// </summary> 
        [SugarColumn(IsPrimaryKey = true)]
        public string 明细ID { get; set; }

        /// <summary> 
        /// wms单号
        /// </summary> 
        public string 发运单号 { get; set; }

        /// <summary> 
        /// wms行号
        /// </summary> 
        public int? 发运行号 { get; set; }

        /// <summary> 
        /// 销售单号
        /// </summary> 
        public string 销售单号 { get; set; }

        /// <summary> 
        /// 销售单行号
        /// </summary> 
        public int? 销售单行号 { get; set; }

        /// <summary> 
        /// 销售类型
        /// </summary> 
        public string 销售类型 { get; set; }

        /// <summary> 
        /// 销售组织
        /// </summary> 
        public string 销售组织 { get; set; }

        /// <summary> 
        /// 创建日期
        /// </summary> 
        public DateTime? 创建日期 { get; set; }

        /// <summary> 
        /// 时间
        /// </summary> 
        public DateTime? 时间 { get; set; }

        /// <summary> 
        /// 凭证日期
        /// </summary> 
        public DateTime? 凭证日期 { get; set; }

        /// <summary> 
        /// 分销渠道
        /// </summary> 
        public string 分销渠道 { get; set; }

        /// <summary> 
        /// 产品组
        /// </summary> 
        public string 产品组 { get; set; }

        /// <summary> 
        /// 客户编号
        /// </summary> 
        public string 客户编号 { get; set; }

        /// <summary> 
        /// 客户名称
        /// </summary> 
        public string 客户名称 { get; set; }

        /// <summary> 
        /// 客户地址
        /// </summary> 
        public string 客户地址 { get; set; }

        /// <summary> 
        /// 客户参考
        /// </summary> 
        public string 客户参考 { get; set; }

        /// <summary> 
        /// 项目
        /// </summary> 
        public string 项目 { get; set; }

        /// <summary> 
        /// 项目类别
        /// </summary> 
        public string 项目类别 { get; set; }

        /// <summary> 
        /// 销售交货批
        /// </summary> 
        public string 销售交货批 { get; set; }

        /// <summary> 
        /// 物料编号
        /// </summary> 
        public string 物料编号 { get; set; }

        /// <summary> 
        /// 物料名称
        /// </summary> 
        public string 物料名称 { get; set; }

        /// <summary> 
        /// 数量
        /// </summary> 
        public decimal? 数量 { get; set; }

        /// <summary> 
        /// 单位
        /// </summary> 
        public string 单位 { get; set; }

        /// <summary> 
        /// 仓库编号
        /// </summary> 
        public string 仓库编号 { get; set; }

        /// <summary> 
        /// 仓库名称
        /// </summary> 
        public string 仓库名称 { get; set; }

        /// <summary> 
        /// 承诺的交货日期
        /// </summary> 
        public DateTime? 承诺的交货日期 { get; set; }

        ///// <summary> 
        ///// 承诺的交货日期
        ///// </summary> 
        //public string 承诺的交货日期 { get; set; }

        /// <summary> 
        /// 合同单号
        /// </summary> 
        public string 合同单号 { get; set; }

        /// <summary> 
        /// 生产主机编号
        /// </summary> 
        public string 生产主机编号 { get; set; }

        /// <summary> 
        /// 利润中心
        /// </summary> 
        public string 利润中心 { get; set; }

        /// <summary>
        /// 装运点/收货点
        /// </summary>
        public string 装运点收货点 { get; set; }

        /// <summary>
        /// 参考单据的单据编号
        /// </summary>
        public string 参考单据的单据编号 { get; set; }

        /// <summary>
        /// 参考项目的项目号
        /// </summary>
        public int? 参考项目的项目号 { get; set; }

        /// <summary>
        /// 结算地址
        /// </summary>
        public string 结算地址 { get; set; }

        /// <summary>
        /// 物流供应商编号
        /// </summary>
        public string 物流供应商编号 { get; set; }

        /// <summary>
        /// 物流供应商名称
        /// </summary>
        public string 物流供应商 { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string 备注 { get; set; }
      
    }
}
