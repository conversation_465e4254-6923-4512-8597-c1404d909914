using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.PP
{
    /// <summary>
    /// 车间退料子表
    /// </summary>
    public class PP_ReturnMaterialDetail : BaseEntity
    {
        /// <summary>
        /// 主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string ID { get; set; }
        /// <summary> 
        /// 车间退料单号
        /// </summary> 
        [Description("车间退料单号")]
        public string DocNum { get; set; }
        /// <summary> 
        /// 车间退料行号
        /// </summary> 
        [Description("车间退料行号")]
        public int? Line { get; set; }
        /// <summary>
        /// 生产订单
        /// </summary>
        [Description("生产订单")]
        public string ProductionOrderNo { get; set; }
        /// <summary>
        /// 组件行项目号
        /// </summary>
        [Description("组件行项目号")]
        public decimal? ComponentLineNo { get; set; }
        /// <summary> 
        /// 物料编号
        /// </summary> 
        [Description("物料编号")]
        public string ComponentCode { get; set; }
        /// <summary> 
        /// 物料描述
        /// </summary> 
        [Description("物料描述")]
        public string MaterialName { get; set; }
        /// <summary> 
        /// 需求数量
        /// </summary> 
        [Description("需求数量")]
        public decimal? DemandQty { get; set; }
        /// <summary> 
        /// 组件单位
        /// </summary> 
        [Description("组件单位")]
        public string ComponentUnit { get; set; }
        /// <summary>
        /// 移动类型,工废：261，料废:311 跟SAP无关做报表用
        /// </summary>
        [Description("移动类型")]
        public string MovementType { get; set; }
        /// <summary> 
        /// 工厂代码
        /// </summary> 
        [Description("工厂代码")]
        public string FactoryCode { get; set; }
        /// <summary> 
        /// 转出仓库编号
        /// </summary> 
        [Description("转出仓库编号")]
        public string OutWhsCode { get; set; }
        /// <summary> 
        /// 转出仓库名称
        /// </summary> 
        [Description("转出仓库名称")]
        public string OutWhsName { get; set; }
        /// <summary> 
        /// 转出区域编号
        /// </summary> 
        [Description("转出区域编号")]
        public string OutRegionCode { get; set; }
        /// <summary> 
        /// 转出区域名称
        /// </summary> 
        [Description("转出区域名称")]
        public string OutRegionName { get; set; }
        /// <summary> 
        /// 转出库位编号
        /// </summary> 
        [Description("转出库位编号")]
        public string OutBinCode { get; set; }
        /// <summary> 
        /// 转出库位名称
        /// </summary> 
        [Description("转出库位名称")]
        public string OutBinName { get; set; }
        /// <summary> 
        /// 转入仓库编号
        /// </summary> 
        [Description("转入仓库编号")]
        public string InWhsCode { get; set; }
        /// <summary> 
        /// 转入仓库名称
        /// </summary> 
        [Description("转入仓库名称")]
        public string InWhsName { get; set; }
        /// <summary> 
        /// 转入区域编号
        /// </summary> 
        [Description("转入区域编号")]
        public string InRegionCode { get; set; }
        /// <summary> 
        /// 转入区域名称
        /// </summary> 
        [Description("转入区域名称")]
        public string InRegionName { get; set; }
        /// <summary> 
        /// 转入库位编号
        /// </summary> 
        [Description("转入库位编号")]
        public string InBinCode { get; set; }
        /// <summary> 
        /// 转入库位名称
        /// </summary> 
        [Description("转入库位名称")]
        public string InBinName { get; set; }
        /// <summary>
        /// 销售订单
        /// </summary>
        [Description("销售订单")]
        public string SalesOrderNo { get; set; }
        /// <summary>
        /// 销售订单行项目
        /// </summary>
        [Description("销售订单行项目")]
        public int? SalesOrderLineNo { get; set; }
        /// <summary>
        /// 特殊库存标识
        /// </summary>
        [Description("特殊库存标识")]
        public string SpecialInventory { get; set; }
        /// <summary>
        /// 评估类别，为B时评估类型为01自制02外购
        /// </summary>
        [Description("评估类别")]
        public string AssessmentCategory { get; set; }
        /// <summary>
        /// 评估类型，01自制02外购
        /// </summary>
        [Description("评估类型")]
        public string AssessmentType { get; set; }
        /// <summary>
        /// 是否过账
        /// </summary>
        [Description("是否过账")]
        public bool? IsPosted { get; set; } = false;
        /// <summary>
        /// 过账人
        /// </summary>
        [Description("过账人")]
        public string PostUser { get; set; }
        /// <summary>
        /// 过账时间
        /// </summary>
        [Description("过账时间")]
        public DateTime? PostTime { get; set; }
        /// <summary>
        /// 手动过账时间
        /// </summary>
        [Description("手动过账时间")]
        public DateTime? ManualPostTime { get; set; }
        /// <summary> 
        /// sap生成单号
        /// </summary> 
        [Description("sap生成单号")]
        public string SapDocNum { get; set; }
        /// <summary> 
        /// sap生成行号
        /// </summary> 
        [Description("sap生成行号")]
        public int? SapLine { get; set; }
    }
}
