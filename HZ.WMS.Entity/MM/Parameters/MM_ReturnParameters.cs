using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.MM.Parameters
{
    /// <summary>
    /// 委外退料接收参数的类
    /// </summary>
    public class MM_ReturnParameters
    {
        /// <summary>
        /// 单据号
        /// </summary>
        public string DocNum { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }

        /// <summary>
        /// 删除明细数组
        /// </summary>
        public string[] deletedetail { get; set; }

        /// <summary>
        /// 订单明细集合
        /// </summary>
        public List<MM_ReturnDetail> detailed { get; set; }

        /// <summary>
        /// 委外退料
        /// </summary>
        public List<MM_Return> entities { get; set; }

        /// <summary>
        /// 过账时间
        /// </summary>
        public DateTime ManualPostTime { get; set; }

        /// <summary>
        /// 库位编号
        /// </summary>
        public string BinLocationCode { get; set; }

        /// <summary>
        /// 库位名称
        /// </summary>
        public string BinLocationName { get; set; }

        /// <summary>
        /// 区域编号
        /// </summary>
        public string RegionCode { get; set; }

        /// <summary>
        /// 区域名称
        /// </summary>
        public string RegionName { get; set; }

        /// <summary>
        /// 仓库编号
        /// </summary>
        public string WhsCode { get; set; }

        /// <summary>
        /// 仓库名称
        /// </summary>
        public string WhsName { get; set; }

    }
}
