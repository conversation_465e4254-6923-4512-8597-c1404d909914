using SqlSugar;
using System;
using System.ComponentModel;

namespace HZ.WMS.Entity.MM
{
    /// <summary>
    /// 返还单明细
    /// </summary>
    public class MM_LendingOrder_Return : BaseEntity
    {
        /// <summary>
        /// 主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Description("明细ID")]
        public string ID { get; set; }

        /// <summary>
        /// 返还单号
        /// </summary>
        [Description("返还单号")]
        public string DocNum { get; set; }




        /// <summary>
        /// 归还单号
        /// </summary>
        [Description("借出单号")]
        public string LendingOrderNum { get; set; }

        /// <summary>
        /// 归还单Id
        /// </summary>
        [Description("借出单Id")]
        public string LendingOrderId { get; set; }


        /// <summary>
        /// 出厂编号
        /// </summary>
        [Description("出厂编号")]
        public string BarCode { get; set; }
        
        /// <summary>
        /// 物料编码
        /// </summary>
        [Description("物料编码")]
        public string ItemCode { get; set; }

        /// <summary>
        /// 物料描述
        /// </summary>
        [Description("物料描述")]
        public string ItemName { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量")]
        public decimal? Qty { get; set; }

        /// <summary> 
        /// 单位
        /// </summary> 
        [Description("单位")]
        public string Unit { get; set; }

        /// <summary> 
        /// 归还仓库编号
        /// </summary> 
        [Description("归还仓库编号")]
        public string WhsCode { get; set; }

        /// <summary> 
        /// 归还仓库
        /// </summary> 
        [Description("归还仓库")]
        public string WhsName { get; set; }

        /// <summary> 
        /// 区域编号
        /// </summary> 
        [Description("区域编号")]
        public string RegionCode { get; set; }

        /// <summary> 
        /// 区域
        /// </summary> 
        [Description("区域")]
        public string RegionName { get; set; }

        /// <summary> 
        /// 库位编号
        /// </summary> 
        [Description("库位编号")]
        public string BinLocationCode { get; set; }

        /// <summary> 
        /// 库位
        /// </summary> 
        [Description("库位")]
        public string BinLocationName { get; set; }



        /// <summary>
        /// 办理人员编号
        /// </summary>
        [Description("办理人员编号")]
        public string HandlenCode { get; set; }

        /// <summary>
        /// 办理人员
        /// </summary>
        [Description("办理人员")]
        public string HandlenName { get; set; }


    }
}
