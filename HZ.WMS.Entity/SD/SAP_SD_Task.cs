using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.SD
{
    public class SAP_SD_Task : BaseEntity
    {
        /// <summary> 
        /// 任务数据库uid
        /// </summary> 
        [SugarColumn(IsPrimaryKey = true)]
        [Description("任务数据库uid")]
        public string TaskUUID { get; set; }
        /// <summary>
        /// 任务ID
        /// </summary>
        [Description("任务ID")]
        public string SiteLogisticsTaskID { get; set; }
        /// <summary>
        /// 任务UUID
        /// </summary>
        [Description("任务UUID")]
        public string SiteLogisticsTaskUUID { get; set; }
        /// <summary>
        /// 任务类别 21 拣货， 23 退货
        /// </summary>
        [Description("任务类别")]
        public string OperationTypeCode { get; set; }
        /// <summary>
        /// 相关任务UUID
        /// </summary>
        [Description("相关任务UUID")]
        public string ReferencedObjectUUID { get; set; }
        /// <summary>
        /// 活动UUID
        /// </summary>
        [Description("活动UUID")]
        public string SiteLogisticsLotOperationActivityUUID { get; set; }
        /// <summary>
        /// 客户编号
        /// </summary>
        [Description("客户编号")]
        public string CustomerPartyKey { get; set; }
        /// <summary>
        /// 客户名称
        /// </summary>
        [Description("客户名称")]
        public string CustomerPartyName { get; set; }
        /// <summary>
        /// 最早执行开始日期
        /// </summary>
        [Description("最早执行开始日期")]
        public DateTime? EarliestExecutionStartDate { get; set; }
        /// <summary>
        /// 最新执行结束日期
        /// </summary>
        [Description("最新执行结束日期")]
        public DateTime? LatestExecutionEndDate { get; set; }
        /// <summary>
        /// 任务状态（是否生成波次单）
        /// </summary>
        [Description("任务状态")]
        public bool TaskState { get; set; }

    }

    #region SAP查询返回数据
    /// <summary>
    /// 
    /// </summary>
    public class SAPTaskModels
    {
        /// <summary>
        /// 
        /// </summary>
        public List<SAP_SD_Task> SAP_SD_TaskList { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public List<SAP_SD_TaskDetail> SAP_SD_TaskDetailList { get; set; }
    }
    #endregion
}
