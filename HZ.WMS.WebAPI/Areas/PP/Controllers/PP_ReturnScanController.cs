using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;
using System.Web;
using System.Configuration;
using HZ.WMS.WebAPI.Controllers;
using HZ.Core.Http;
using HZ.WMS.Entity.PP;
using HZ.WMS.Application.PP;
using HZ.WMS.Entity.Sys;
using HZ.WMS.Application;

namespace HZ.WMS.WebAPI.Areas.PP.Controllers
{
    /// <summary>
    /// 生产退料单
    /// </summary>
    public class PP_ReturnScanController : ApiBaseController
    {
        private PP_ReturnMaterialApp _app = new PP_ReturnMaterialApp();

        #region 查询

        /// <summary>
        /// 查询主表分页列表
        /// </summary>
        /// <param name="page">分页</param>
        /// <param name="keyword"></param>
        /// <param name="dateValue">日期</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]string keyword, [FromUri] DateTime[] dateValue, [FromUri] bool? isPosted)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];

                var itemsData = _app.GetPageList(page, t => (string.IsNullOrEmpty(keyword)
                        || t.DocNum.Contains(keyword)
                        || t.CUser.Contains(keyword))
                        && t.ExamineStatus == 1//查询已审核状态的数据
                        && (t.CTime >= fromTime && t.CTime <= toTime)
                        && (isPosted == null || t.IsPosted == isPosted)).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 过账

        /// <summary>
        /// 过账
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult DoPost([FromBody]List<PP_ReturnMaterial> entities)
        {
            var result = new ResponseData();
            try
            {
                if (entities.Any(w => w.ExamineStatus != 1))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "存在未经过仓库审核的数据，不允许过账";
                }
                else
                {
                    string error_message = "";
                    Sys_User currentUser = GetCurrentUser();
                    bool bSubmit = _app.DoPost(entities, currentUser.LoginAccount, out error_message);

                    if (!bSubmit)
                    {
                        result.Code = (int)WMSStatusCode.Failed;
                        result.Message = error_message;
                    }
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion
    }
}
