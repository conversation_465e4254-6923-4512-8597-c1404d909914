using System;
using SqlSugar;
namespace HZ.WMS.Entity.Base
{
    /// <summary>
    /// 0库位主信息
    /// </summary>
    [SugarTable("Base_BinLocation")]
    public class BinLocationEntity : BaseEntity
    {
        /// <summary>
        /// 库位ID
        /// </summary>
        [SugarColumn(SqlParameterDbType = System.Data.DbType.String, IsPrimaryKey = true)]
        public string BinID { get; set; }
        /// <summary>
        /// 库位编号
        /// </summary>
        public string BinLocationCode { get; set; }
        /// <summary>
        /// 库位名称
        /// </summary>
        public string BinLocationName { get; set; }
        /// <summary>
        /// 所属区域编号
        /// </summary>
        public string RegionCode { get; set; }
        /// <summary>
        /// 所属区域名称
        /// </summary>
        public string RegionName { get; set; }
        /// <summary>
        /// 所属仓库编号
        /// </summary>
        public string WhsCode { get; set; }
        /// <summary>
        /// 所属仓库名称
        /// </summary>
        public string WhsName { get; set; }
    }
}
