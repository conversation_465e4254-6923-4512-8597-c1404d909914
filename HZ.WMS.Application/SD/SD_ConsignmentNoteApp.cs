using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using HZ.WMS.Application.MD;
using HZ.WMS.Application.Sys;
using HZ.WMS.Entity;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.MD.ViewModel;
using HZ.WMS.Entity.SAP;
using HZ.WMS.Entity.SD;
using HZ.WMS.Entity.SD.Parameters;
using HZ.WMS.Entity.SD.ViewModel;
using HZ.WMS.Entity.Sys;
using SqlSugar;
using DbType = System.Data.DbType;

namespace HZ.WMS.Application.SD
{
    /// <summary>
    /// 托运单(方法层)
    /// 没有out_message消息的函数，意味着只在最前端做未处理异常捕捉
    /// </summary>

    public class SD_ConsignmentNoteApp : BaseApp<SD_ConsignmentNote>
    {
        #region 初始化

        private BaseApp<BaseEntity> _baseApp = new BaseApp<BaseEntity>();
     

        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public SD_ConsignmentNoteApp() : base(){}

        #endregion

        /// <summary>
        /// 托运单明细(方法层)
        /// </summary>
        public class SD_ConsignmentNoteDetailApp : BaseApp<SD_ConsignmentNoteDetail> { }

        private SD_ConsignmentNoteDetailApp _detailApp = new SD_ConsignmentNoteDetailApp();

        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="ids">删除数组</param>
        /// <param name="opUser"></param>
        /// <param name="error_message"></param>
        public bool Delete(string[] DocNums, string opUser, out string error_message)
        {
            bool bDeleted = true;
            var querys = new List<SD_ConsignmentNote>();
            var queryDetails = new List<SD_ConsignmentNoteDetail>();
            try
            {
                foreach (string key in DocNums)
                {
                    var item = GetList(x => (string.IsNullOrEmpty(key) || x.DocNum.Contains(key))).ToList();
                    if (item != null)
                    {
                        querys.AddRange(item);
                    }
                    var itemdetail = _detailApp.GetList(x => (string.IsNullOrEmpty(key) || x.DocNum.Contains(key))).ToList();
                    if (itemdetail != null)
                    {
                        queryDetails.AddRange(itemdetail);
                    }
                }
                //List<PO_ReturnScan> entities = GetListByKeys(ids);
                if (CheckDelete(querys, out error_message))
                {
                    try
                    {
                        //_stockApp.DbContext = this.DbContext;
                        //_poBarCodeApp.DbContext = this.DbContext;
                        //foreach (PO_ReturnScan poScan in entities)
                        //{
                        //    //出库还原
                        //    MD_Stock stockInfo = new MD_Stock() {
                        //        //BarCode = poScan.BarCode,
                        //        //SupplierCode =poScan.SupplierCode,
                        //        //SupplierName = poScan.SupplierName,
                        //        //SupplierBatch = poScan.SupplierBatch,
                        //        //BatchNum = poScan.BatchNum,
                        //        //PTime = poScan.PTime,
                        //        //ItemCode = poScan.ItemCode,
                        //        //ItemName = poScan.ItemName,
                        //        //ItmsGrpCode = poScan.ItmsGrpCode,
                        //        //ItmsGrpName = poScan.ItmsGrpName,
                        //        //Qty = poScan.Qty,
                        //        //Unit = poScan.Unit,
                        //        RegionCode = poScan.OutRegionCode,
                        //        RegionName = poScan.OutRegionName,
                        //        BinLocationCode = poScan.OutBinLocationCode,
                        //        BinLocationName = poScan.OutBinLocationName,
                        //        CUser = opUser,
                        //        MUser = opUser
                        //    };

                        //    if (!_stockApp.StockIn(stockInfo, opUser, out error_message))
                        //    {
                        //        throw new Exception("Common.error", new Exception(error_message));
                        //    }
                        //}

                        DbContext.Ado.BeginTran();
                        _detailApp.DbContext = DbContext;

                        //删除主表信息
                        Delete(querys, opUser);

                        //删除明细表信息
                        _detailApp.Delete(queryDetails, opUser);

                        DbContext.Ado.CommitTran();
                    }
                    catch (Exception ex)
                    {
                        bDeleted = false;
                        error_message = ex.Message;
                        if (DbContext.Ado.IsAnyTran())
                            DbContext.Ado.RollbackTran();
                    }
                }
                else
                {
                    bDeleted = false;
                }
            }
            catch (Exception ex)
            {
                error_message = ex.Message;
                bDeleted = false;
            }

            return bDeleted;
        }

        /// <summary>
        /// 删除校验
        /// </summary>
        /// <param name="entities"></param>
        /// <param name="error_message"></param>
        /// <returns></returns>
        private bool CheckDelete(List<SD_ConsignmentNote> entities, out string error_message)
        {
            bool isPass = true;
            error_message = "";
            //foreach (SD_DeliveryScan sd in entities)
            //{
            //    if (sd.IsDelivery == true)
            //    {
            //        error_message = "已确认装箱数据不允许删除!";
            //        isPass = false;
            //        break;
            //    }
            //}

            return isPass;
        }

        #endregion

        #region 自动保存

        /// <summary>
        /// 自动保存
        /// </summary>
        /// <param name="DocNum">销售发运计划单据号</param>
        /// <param name="entities">发运计划信息</param>
        /// <param name="user">用户</param>
        /// <param name="error_message">错误信息</param>
        /// <returns></returns>
        public bool AutoSave(string DocNum, SD_ShippingPlan entities, string user, out string error_message)
        {
            error_message = "";
            try
            {
                Sys_DictionaryApp Sys_DictionaryApp = new Sys_DictionaryApp();
                SD_ShippingPlanApp _PlanApp = new SD_ShippingPlanApp();
                SD_ShippingPlanDetailApp _PlanDetailApp = new SD_ShippingPlanDetailApp();

                SD_CustomerAddViewApp _CustomerAddApp = new SD_CustomerAddViewApp();
                MD_FreightMileageApp _FreightMileageApp = new MD_FreightMileageApp();
                MD_CustomerDistanceRateA_App _CustomerDistanceRateA_App = new MD_CustomerDistanceRateA_App();
                MD_CustomerWeightRateA_App _CustomerWeightRateA_App = new MD_CustomerWeightRateA_App();
                MD_CustomerWeightPriceB_App _CustomerWeightPriceB_App = new MD_CustomerWeightPriceB_App();
                MD_CustomerWeightRateC_App _CustomerWeightRateC_App = new MD_CustomerWeightRateC_App();


                List<SD_ConsignmentNote> NoteList = new List<SD_ConsignmentNote>();
                List<SD_ConsignmentNoteDetail> NoteDetailList = new List<SD_ConsignmentNoteDetail>();

                List<SD_ShippingPlan> infoPlan = _PlanApp.GetList(x => x.DocNum == DocNum)?.ToList();
                List<SD_ShippingPlanDetail> infoPlanDetail = _PlanDetailApp.GetList(x => x.DocNum == DocNum)?.ToList();

                string[] ItemCodes = infoPlanDetail.Select(x => x.ItemCode).Distinct().ToArray();

                List<XZ_SAP_MARC> ItemCodeList = this.DbContextForSAP.Queryable<XZ_SAP_MARC>().Where(t => ItemCodes.Contains(t.MATNR) && t.Status == false).ToList();

                DateTime ctime = DateTime.Now;
                var mainList = infoPlanDetail.GroupBy(g => new
                {
                    g.CustomerAdd,
                    g.BatchNum
                }).Select(q => new
                {
                    CustomerAdd = q.Key.CustomerAdd,
                    BatchNum = q.Key.BatchNum
                });
                //按客户地址/批次分组
                foreach (var mainInfo in mainList)
                {
                    List<SD_ShippingPlanDetail> conditionList = infoPlanDetail.Where(x => x.CustomerAdd == mainInfo.CustomerAdd && x.BatchNum == mainInfo.BatchNum)?.ToList();

                    //MD_CustomerAdd CustomerAdd = _CustomerAddApp.GetList(x => x.CustomerAdd == mainInfo.CustomerAdd)?.ToList().FirstOrDefault();
                    //if (CustomerAdd == null)
                    //{
                    //    error_message = "未查询到销售客户地址信息";
                    //    return false;
                    //}
                    string SettlementAdd = conditionList[0].SettlementAdd;// CustomerAdd.SettlementAdd;//结算地址

                    //MD_FreightMileage Mileageinfo = _FreightMileageApp.GetList(x => x.SettlementAdd == SettlementAdd)?.ToList().FirstOrDefault();
                    MD_FreightMileage_View Mileageinfo = this.DbContext.Queryable<MD_FreightMileage_View>().Where(x => x.SettlementAdd == SettlementAdd)?.ToList().FirstOrDefault();


                    if (Mileageinfo == null)
                    {
                        error_message ="结算地址：【"+SettlementAdd+ "】未查询到运费计算里程";
                        return false;
                    }

                    //直发的类型不需要生成托运单
                    if (!Mileageinfo.IsZF)
                    {
                        string LogisticsSupplierCode = Mileageinfo.SupplierCode;//物料供应商
                        string LogisticsSupplierName = Mileageinfo.SupplierName;
                        int Mileage =Convert.ToInt32(Mileageinfo.Mileage);//结算里程


                        #region 主表
                        string docNum = this.GetNewDocNum("SD", "SC");
                        SD_ConsignmentNote note = new SD_ConsignmentNote();
                        note.DocNum = docNum;
                        note.ShippingPlanNum = DocNum;
                        note.DeliveryDate = infoPlan.Where(x => x.DocNum == DocNum).ToList().FirstOrDefault().DeliveryDate;
                        note.CustomerCode = conditionList[0].CustomerCode;
                        note.CustomerName = conditionList[0].CustomerName;
                        note.CustomerAdd = conditionList[0].CustomerAdd;
                        note.CustomerRegion = SettlementAdd;//结算地址
                        note.LogisticsSupplierCode = LogisticsSupplierCode;
                        note.LogisticsSupplierName = LogisticsSupplierName;
                        note.CostCenter = "2002S0102";
                        note.ShippingDepar = "合同物流部";
                        note.ShippingType = "";
                        note.Shipper = "";
                        note.CarNum = "";
                        note.Type = 1;
                        note.Status = 0;
                        note.IsDelete = false;
                        note.CTime = ctime;
                        note.CUser = user;
                        #endregion

                        #region 子表
                        List<SD_ConsignmentNoteDetail> DetailList = new List<SD_ConsignmentNoteDetail>();
                        decimal? Weight = 0;
                        foreach (var mod in conditionList)
                        {
                            var query = ItemCodeList.Where(x => x.MATNR == mod.ItemCode).ToList().FirstOrDefault();
                            SD_ConsignmentNoteDetail noteDetail = new SD_ConsignmentNoteDetail();
                            noteDetail.DocNum = docNum;
                            noteDetail.BaseNum = mod.SalesOrderNumber;
                            noteDetail.BaseLine = mod.SalesLine;
                            noteDetail.ItemCode = mod.ItemCode;
                            noteDetail.ItemName = mod.ItemName;
                            noteDetail.Qty = mod.ShippingPlanDetailQty;
                            noteDetail.Unit = mod.Unit;
                            noteDetail.ItmsGrpCode = query.MATKL;
                            noteDetail.Weight = query.BRGEW;
                            noteDetail.Mileage = Mileage;
                            noteDetail.Telephone = conditionList[0].Telephone;
                            noteDetail.Contact = conditionList[0].Contact;
                            noteDetail.DeliverDate = note.DeliveryDate;
                            noteDetail.ContractNo = mod.CONT;
                            noteDetail.CustomerOrderNum = mod.CustomerOrderNum;
                            noteDetail.BarCode = mod.OUTNO;
                            noteDetail.IsDelete = false;
                            noteDetail.CTime = ctime;
                            noteDetail.CUser = user;
                     
                            DetailList.Add(noteDetail);
                            Weight += noteDetail.Weight;
                        }

                        if (Weight == 0)
                        {
                            error_message = "存在重量为0的数据";
                            return false;
                        }
                        decimal? Weight1 = Weight / 1000; // kg转吨
                        List<MD_CustomerDistanceRateA> DistanceRateA = _CustomerDistanceRateA_App.GetList().Where(x => x.SupplierCode == LogisticsSupplierCode && x.SettlementAdd == SettlementAdd)?.ToList();
                        if (DistanceRateA.Count == 0)//查不到结算地址的取标准的
                            DistanceRateA = _CustomerDistanceRateA_App.GetList().Where(x => x.SupplierCode == LogisticsSupplierCode && string.IsNullOrEmpty(x.SettlementAdd))?.ToList();
                        List<MD_CustomerWeightRateA> WeightRateA = _CustomerWeightRateA_App.GetList().Where(x => x.SupplierCode == LogisticsSupplierCode && x.SettlementAdd == SettlementAdd)?.ToList();
                        if (WeightRateA.Count == 0)//查不到结算地址的取标准的
                            WeightRateA = _CustomerWeightRateA_App.GetList().Where(x => x.SupplierCode == LogisticsSupplierCode && string.IsNullOrEmpty(x.SettlementAdd))?.ToList();
                        List<MD_CustomerWeightPriceB> WeightPriceB = _CustomerWeightPriceB_App.GetList().Where(x => x.SupplierCode == LogisticsSupplierCode && x.SettlementAdd == SettlementAdd && x.WeightFrom <= Weight1 && x.WeightTo >= Weight1)?.ToList();
                        if (WeightPriceB.Count == 0)//查不到结算地址的取标准的
                            WeightPriceB = _CustomerWeightPriceB_App.GetList().Where(x => x.SupplierCode == LogisticsSupplierCode && string.IsNullOrEmpty(x.SettlementAdd) && x.WeightFrom <= Weight1 && x.WeightTo >= Weight1)?.ToList();
                        List<MD_CustomerWeightRateC> WeightRateC = _CustomerWeightRateC_App.GetList().Where(x => x.SupplierCode == LogisticsSupplierCode && x.SettlementAdd == SettlementAdd && x.WeightFrom <= Weight1 && x.WeightTo >= Weight1)?.ToList();
                        if (WeightRateC.Count == 0)//查不到结算地址的取标准的
                            WeightRateC = _CustomerWeightRateC_App.GetList().Where(x => x.SupplierCode == LogisticsSupplierCode && string.IsNullOrEmpty(x.SettlementAdd) && x.WeightFrom <= Weight1 && x.WeightTo >= Weight1)?.ToList();

                        //正规逻辑计算运维

                        note.TotalTheoreticalAmount = 0;
                        if (WeightRateA.Count > 0 || DistanceRateA.Count > 0)
                        {
                            //A模式 重量*费率*距离*费率
                            decimal? DistanceRate = 1;
                            decimal? WeightRate = 1;
                            if (DistanceRateA.Count > 0)
                                DistanceRate = DistanceRateA.Where(x => x.DistanceFrom <= Mileage && x.DistanceTo >= Mileage)?.ToList().FirstOrDefault().Rate;
                            if (WeightRateA.Count > 0)
                                WeightRate = WeightRateA.Where(x => (x.WeightFrom <= Weight1 && x.WeightTo >= Weight1))?.ToList().FirstOrDefault().Rate;
                            note.TotalTheoreticalAmount = Weight1 * WeightRate * Mileage * DistanceRate;
                        }
                        else if (WeightPriceB.Count > 0)
                        {
                            //B模式 按重量一口价
                            note.TotalTheoreticalAmount = WeightPriceB.ToList().FirstOrDefault().Price;
                        }
                        else if (WeightRateC.Count > 0)
                        {
                            //C模式 供应商按重量计价
                            decimal? WeightRate = WeightRateC.ToList().FirstOrDefault().Rate;
                            note.TotalTheoreticalAmount = Weight1 * WeightRate;
                        }
                        else
                        {
                            error_message = "未找到【" + LogisticsSupplierCode + "】运费计算数据";
                            return false;
                        }
                        note.SpecialExpenses = 0;
                        //特殊逻辑计算运维
                        List<Sys_Dictionary> DictionaryList = Sys_DictionaryApp.GetList(x => x.TypeCode == "SD002" && x.EnumValue == LogisticsSupplierCode)?.ToList();
                        if (DictionaryList.Count > 0)
                        {
                            //不满多少钱按多少钱算 例如：不满120 按120算
                            decimal amount = Convert.ToDecimal(DictionaryList.ToList().FirstOrDefault().EnumValue1);
                            if (note.TotalTheoreticalAmount < amount)
                                note.TotalTheoreticalAmount = amount;
                        }
                        else
                        {
                            //按供应商 结算地址 发货数，算特殊费用
                            DictionaryList = Sys_DictionaryApp.GetList(x => x.TypeCode == "SD003" && x.EnumValue == LogisticsSupplierCode && x.EnumValue1 == SettlementAdd)?.ToList();
                            if (DictionaryList.Count > 0)
                            {
                                if (DetailList.Count <= Convert.ToInt32(DictionaryList.ToList().FirstOrDefault().EnumValue2))
                                    note.SpecialExpenses += Convert.ToDecimal(DictionaryList.ToList().FirstOrDefault().Remark);
                            }
                            else
                            {
                                //按客户 补特殊费用
                                DictionaryList = Sys_DictionaryApp.GetList(x => x.TypeCode == "SD004" && x.EnumValue == conditionList[0].CustomerCode)?.ToList();
                                if (DictionaryList.Count > 0)
                                {
                                    note.SpecialExpenses += Convert.ToDecimal(DictionaryList.ToList().FirstOrDefault().EnumValue1);
                                }
                            }
                        }
                        #endregion

                      
                        note.TotalWeight = Weight;
                        //把总金额按重量比例分配给明细行
              
                        note.TotalTheoreticalAmount = note.TotalTheoreticalAmount / Convert.ToDecimal(1.13); //不含税价格(/1.13)
                        decimal? sumAmount = note.TotalTheoreticalAmount + note.SpecialExpenses;
                        foreach (SD_ConsignmentNoteDetail mod in DetailList)
                        {
                            mod.RowTheoreticalAmount = mod.Weight / Weight * sumAmount;
                        }

                        NoteList.Add(note);
                        NoteDetailList.AddRange(DetailList);
                    }
                }

                if (NoteList.Count > 0)
                {
                    DbContext.Ado.BeginTran();
                    _detailApp.DbContext = this.DbContext;
                    _PlanApp.DbContext = this.DbContext;

                    //主表插入
                    if (this.Insert(NoteList) > 0)
                    {
                        //明细表插入
                        _detailApp.Insert(NoteDetailList);
                        _PlanApp.Update(entities);
                    }

                    DbContext.Ado.CommitTran();
                    return true;
                }
                else
                {
                    error_message = "没有生成托运单配置数据";
                    return false;

                }


            }
            catch (Exception ex)
            {
                if (DbContext.Ado.IsAnyTran())
                {
                    DbContext.Ado.RollbackTran();
                }
                error_message = ex.Message;
                return false;
            }
        }

        #endregion

        #region 手动保存

        /// <summary>
        /// 手动保存
        /// </summary>
        /// <param name="Parameters">参数集合</param>
        /// <param name="user">用户</param>
        /// <param name="error_message">错误信息</param>
        /// <returns></returns>
        public bool Save(SD_ConsignmentNoteParameters Parameters, string user, out string error_message)
        {
            error_message = "";
            DateTime ctime = DateTime.Now;
            try
            {
                #region 主表

                var note = new SD_ConsignmentNote
                {
                    DocNum = Parameters.DocNum,
                    CustomerID=Parameters.CustomerID,
                    CustomerCode = Parameters.CustomerCode,
                    CustomerName = Parameters.CustomerName,
                    CustomerAdd = Parameters.CustomerAdd,
                    CustomerRegion = Parameters.CustomerRegion,//结算地址
                    SpecialExpenses=Parameters.SpecialExpenses,
                    Remark=Parameters.Remark,
                    TotalTheoreticalAmount = Parameters.TotalTheoreticalAmount,
                    TotalWeight = Parameters.TotalWeight,
                    LogisticsSupplierCode = Parameters.LogisticsSupplierCode,
                    LogisticsSupplierName = Parameters.LogisticsSupplierName,
                    CostCenter = Parameters.CostCenter,
                    ShippingDepar = Parameters.ShippingDepar,
                    ShippingType = Parameters.ShippingType,
                    Shipper = Parameters.Shipper,
                    CarNum = Parameters.CarNum,
                    Type = 2,
                    Status = 0,
                    IsDelete = false,
                    CTime = ctime,
                    CUser = user,
                    DeliveryDate=Parameters.DeliveryDate
                };

                #endregion

                #region 子表

                decimal? Weight = 0;
                int Line = 0;
                //子表
                Parameters.detailed.ForEach(x =>
                {
                    Line = Line + 1;
                    //Weight = ItemCodeList.Where(x => x.MATNR == mod.ItemCode).ToList().FirstOrDefault().NTGEW,
                    x.DocNum = Parameters.DocNum;
                    x.IsDelete = false;
                    x.CTime = ctime;
                    x.CUser = user;
                    x.Line = Line;
                    Weight += x.Weight;
                });

                #endregion

                //把总金额按重量比例分配给明细行
                decimal? sumAmount = Parameters.TotalTheoreticalAmount + Parameters.SpecialExpenses;
                foreach (var mod in Parameters.detailed)
                {
                    mod.RowTheoreticalAmount = mod.Weight / Weight * sumAmount;
                }

                DbContext.Ado.BeginTran();
                _detailApp.DbContext = this.DbContext;

                //主表插入
                Insert(note);

                //明细表插入
                _detailApp.Insert(Parameters.detailed);

                DbContext.Ado.CommitTran();
                return true;

            }
            catch (Exception ex)
            {
                if(DbContext.Ado.IsAnyTran())
                    DbContext.Ado.RollbackTran();

                error_message = ex.Message;
                return false;
            }
        }

        #endregion
        
        #region 自动保存-新发运

        /// <summary>
        /// 自动保存
        /// </summary>
        /// <param name="DocNum">销售发运计划单据号</param>
        /// <param name="entities">发运计划信息</param>
        /// <param name="user">用户</param>
        /// <param name="error_message">错误信息</param>
        /// <returns></returns>
        public bool AutoSaveNew(List<string> ids, Sale_ShippingPlan entities, string user, out string error_message)
        {
            error_message = "";
            try
            {
                Sys_DictionaryApp Sys_DictionaryApp = new Sys_DictionaryApp();
                Sale_ShippingPlanApp _saleShippingPlanApp = new Sale_ShippingPlanApp();
                SD_ShippingPlanApp _PlanApp = new SD_ShippingPlanApp();
                
                MD_CustomerDistanceRateA_App _CustomerDistanceRateA_App = new MD_CustomerDistanceRateA_App();
                MD_CustomerWeightRateA_App _CustomerWeightRateA_App = new MD_CustomerWeightRateA_App();
                MD_CustomerWeightPriceB_App _CustomerWeightPriceB_App = new MD_CustomerWeightPriceB_App();
                MD_CustomerWeightRateC_App _CustomerWeightRateC_App = new MD_CustomerWeightRateC_App();


                List<SD_ConsignmentNote> NoteList = new List<SD_ConsignmentNote>();
                List<SD_ConsignmentNoteDetail> NoteDetailList = new List<SD_ConsignmentNoteDetail>();

                var saleShippingPlanList = _saleShippingPlanApp.GetList(x => ids.Contains(x.DocNum))?.ToList();

                string[] ItemCodes = saleShippingPlanList.Select(x => x.ItemCode).Distinct().ToArray();

                List<XZ_SAP_MARC> ItemCodeList = DbContextForSAP.Queryable<XZ_SAP_MARC>().Where(t => ItemCodes.Contains(t.MATNR)&&t.Status==false).ToList();

                DateTime ctime = DateTime.Now;
                var mainList = saleShippingPlanList.GroupBy(g => new
                {
                    g.CustomerAdd,
                    g.BatchNum
                }).Select(q => new
                {
                    CustomerAdd = q.Key.CustomerAdd,
                    BatchNum = q.Key.BatchNum
                });
                //按客户地址/批次分组
                foreach (var mainInfo in mainList)
                {
                    List<Sale_ShippingPlan> conditionList = saleShippingPlanList.Where(x => x.CustomerAdd == mainInfo.CustomerAdd && x.BatchNum == mainInfo.BatchNum)?.ToList();

                    string SettlementAdd = conditionList[0].SettlementAdd;// CustomerAdd.SettlementAdd;//结算地址

                    MD_FreightMileage_View Mileageinfo = DbContext.Queryable<MD_FreightMileage_View>().Where(x => x.SettlementAdd == SettlementAdd)?.ToList().FirstOrDefault();


                    if (Mileageinfo == null)
                    {
                        error_message ="结算地址：【"+SettlementAdd+ "】未查询到 运费计算里程";
                        return false;
                    }

                    //直发的类型不需要生成托运单
                    if (!Mileageinfo.IsZF)
                    {
                        string LogisticsSupplierCode = Mileageinfo.SupplierCode;//物料供应商
                        string LogisticsSupplierName = Mileageinfo.SupplierName;
                        int Mileage =Convert.ToInt32(Mileageinfo.Mileage);//结算里程


                        #region 主表
                        string docNum = this.GetNewDocNum("SD", "SC");
                        SD_ConsignmentNote note = new SD_ConsignmentNote();
                        note.DocNum = docNum;
                        // note.ShippingPlanNum = DocNum;
                        // note.DeliveryDate = saleShippingPlanList.Where(x => x.DocNum == DocNum).FirstOrDefault().DeliveryDate;
                        note.CustomerCode = conditionList[0].CustomerCode;
                        note.CustomerName = conditionList[0].CustomerName;
                        note.CustomerAdd = conditionList[0].CustomerAdd;
                        note.CustomerRegion = SettlementAdd;//结算地址
                        note.LogisticsSupplierCode = LogisticsSupplierCode;
                        note.LogisticsSupplierName = LogisticsSupplierName;
                        note.CostCenter = "2002S0102";
                        note.ShippingDepar = "合同物流部";
                        note.ShippingType = "";
                        note.Shipper = "";
                        note.CarNum = "";
                        note.Type = 1;
                        note.Status = 0;
                        note.IsDelete = false;
                        note.CTime = ctime;
                        note.CUser = user;
                        #endregion

                        #region 子表
                        List<SD_ConsignmentNoteDetail> DetailList = new List<SD_ConsignmentNoteDetail>();
                        decimal? Weight = 0;
                        int line = 10;
                        foreach (var mod in conditionList)
                        {
                            var query = ItemCodeList.Where(x => x.MATNR == mod.ItemCode).FirstOrDefault();
                            SD_ConsignmentNoteDetail noteDetail = new SD_ConsignmentNoteDetail();
                            noteDetail.DocNum = docNum;
                            noteDetail.Line = line;
                            noteDetail.BaseNum = mod.SaleSapNo;
                            noteDetail.BaseLine = mod.SaleSapLine;
                            noteDetail.ItemCode = mod.ItemCode;
                            noteDetail.ItemName = mod.ItemName;
                            noteDetail.Qty = mod.Quantity;
                            noteDetail.Unit = mod.Unit;
                            noteDetail.ItmsGrpCode = query.MATKL;
                            noteDetail.Weight = query.BRGEW;
                            noteDetail.Mileage = Mileage;
                            noteDetail.Telephone = conditionList[0].Telephone;
                            noteDetail.Contact = conditionList[0].Contact;
                            noteDetail.DeliverDate = note.DeliveryDate;
                            noteDetail.ContractNo = mod.ContractNo;
                            noteDetail.CustomerOrderNum = mod.CustomerOrderNo;
                            noteDetail.BarCode = mod.SerialNo;
                            noteDetail.IsDelete = false;
                            noteDetail.CTime = ctime;
                            noteDetail.CUser = user;
                     
                            DetailList.Add(noteDetail);
                            Weight += noteDetail.Weight;
                            line += 10;
                        }

                        if (Weight == 0)
                        {
                            error_message = "存在重量为0的数据";
                            return false;
                        }
                        decimal? Weight1 = Weight / 1000; // kg转吨
                        List<MD_CustomerDistanceRateA> DistanceRateA = _CustomerDistanceRateA_App.GetList().Where(x => x.SupplierCode == LogisticsSupplierCode && x.SettlementAdd == SettlementAdd)?.ToList();
                        if (DistanceRateA.Count == 0)//查不到结算地址的取标准的
                            DistanceRateA = _CustomerDistanceRateA_App.GetList().Where(x => x.SupplierCode == LogisticsSupplierCode && string.IsNullOrEmpty(x.SettlementAdd))?.ToList();
                        List<MD_CustomerWeightRateA> WeightRateA = _CustomerWeightRateA_App.GetList().Where(x => x.SupplierCode == LogisticsSupplierCode && x.SettlementAdd == SettlementAdd)?.ToList();
                        if (WeightRateA.Count == 0)//查不到结算地址的取标准的
                            WeightRateA = _CustomerWeightRateA_App.GetList().Where(x => x.SupplierCode == LogisticsSupplierCode && string.IsNullOrEmpty(x.SettlementAdd))?.ToList();
                        List<MD_CustomerWeightPriceB> WeightPriceB = _CustomerWeightPriceB_App.GetList().Where(x => x.SupplierCode == LogisticsSupplierCode && x.SettlementAdd == SettlementAdd && x.WeightFrom <= Weight1 && x.WeightTo >= Weight1)?.ToList();
                        if (WeightPriceB.Count == 0)//查不到结算地址的取标准的
                            WeightPriceB = _CustomerWeightPriceB_App.GetList().Where(x => x.SupplierCode == LogisticsSupplierCode && string.IsNullOrEmpty(x.SettlementAdd) && x.WeightFrom <= Weight1 && x.WeightTo >= Weight1)?.ToList();
                        List<MD_CustomerWeightRateC> WeightRateC = _CustomerWeightRateC_App.GetList().Where(x => x.SupplierCode == LogisticsSupplierCode && x.SettlementAdd == SettlementAdd && x.WeightFrom <= Weight1 && x.WeightTo >= Weight1)?.ToList();
                        if (WeightRateC.Count == 0)//查不到结算地址的取标准的
                            WeightRateC = _CustomerWeightRateC_App.GetList().Where(x => x.SupplierCode == LogisticsSupplierCode && string.IsNullOrEmpty(x.SettlementAdd) && x.WeightFrom <= Weight1 && x.WeightTo >= Weight1)?.ToList();

                        //正规逻辑计算运维

                        note.TotalTheoreticalAmount = 0;
                        if (WeightRateA.Count > 0 || DistanceRateA.Count > 0)
                        {
                            //A模式 重量*费率*距离*费率
                            decimal? DistanceRate = 1;
                            decimal? WeightRate = 1;
                            if (DistanceRateA.Count > 0)
                                DistanceRate = DistanceRateA.Where(x => x.DistanceFrom <= Mileage && x.DistanceTo >= Mileage)?.FirstOrDefault().Rate;
                            if (WeightRateA.Count > 0)
                                WeightRate = WeightRateA.Where(x => (x.WeightFrom <= Weight1 && x.WeightTo >= Weight1))?.FirstOrDefault().Rate;
                            note.TotalTheoreticalAmount = Weight1 * WeightRate * Mileage * DistanceRate;
                        }
                        else if (WeightPriceB.Count > 0)
                        {
                            //B模式 按重量一口价
                            note.TotalTheoreticalAmount = WeightPriceB.FirstOrDefault().Price;
                        }
                        else if (WeightRateC.Count > 0)
                        {
                            //C模式 供应商按重量计价
                            decimal? WeightRate = WeightRateC.FirstOrDefault().Rate;
                            note.TotalTheoreticalAmount = Weight1 * WeightRate;
                        }
                        else
                        {
                            error_message = "未找到【" + LogisticsSupplierCode + "】运费计算数据";
                            return false;
                        }
                        note.SpecialExpenses = 0;
                        //特殊逻辑计算运维
                        List<Sys_Dictionary> DictionaryList = Sys_DictionaryApp.GetList(x => x.TypeCode == "SD002" && x.EnumValue == LogisticsSupplierCode)?.ToList();
                        if (DictionaryList.Count > 0)
                        {
                            //不满多少钱按多少钱算 例如：不满120 按120算
                            decimal amount = Convert.ToDecimal(DictionaryList.FirstOrDefault().EnumValue1);
                            if (note.TotalTheoreticalAmount < amount)
                                note.TotalTheoreticalAmount = amount;
                        }
                        else
                        {
                            //按供应商 结算地址 发货数，算特殊费用
                            DictionaryList = Sys_DictionaryApp.GetList(x => x.TypeCode == "SD003" && x.EnumValue == LogisticsSupplierCode && x.EnumValue1 == SettlementAdd)?.ToList();
                            if (DictionaryList.Count > 0)
                            {
                                if (DetailList.Count <= Convert.ToInt32(DictionaryList.FirstOrDefault().EnumValue2))
                                    note.SpecialExpenses += Convert.ToDecimal(DictionaryList.FirstOrDefault().Remark);
                            }
                            else
                            {
                                //按客户 补特殊费用
                                DictionaryList = Sys_DictionaryApp.GetList(x => x.TypeCode == "SD004" && x.EnumValue == conditionList[0].CustomerCode)?.ToList();
                                if (DictionaryList.Count > 0)
                                {
                                    note.SpecialExpenses += Convert.ToDecimal(DictionaryList.FirstOrDefault().EnumValue1);
                                }
                            }
                        }
                        #endregion

                      
                        note.TotalWeight = Weight;
                        //把总金额按重量比例分配给明细行
              
                        note.TotalTheoreticalAmount = note.TotalTheoreticalAmount / Convert.ToDecimal(1.13); //不含税价格(/1.13)
                        decimal? sumAmount = note.TotalTheoreticalAmount + note.SpecialExpenses;
                        foreach (SD_ConsignmentNoteDetail mod in DetailList)
                        {
                            mod.RowTheoreticalAmount = mod.Weight / Weight * sumAmount;
                        }

                        NoteList.Add(note);
                        NoteDetailList.AddRange(DetailList);
                    }
                }

                if (NoteList.Count > 0)
                {
                    DbContext.Ado.BeginTran();
                    _detailApp.DbContext = this.DbContext;
                    _PlanApp.DbContext = this.DbContext;

                    //主表插入
                    if (this.Insert(NoteList) > 0)
                    {
                        //明细表插入
                        _detailApp.Insert(NoteDetailList);
                        // _PlanApp.Update(entities);
                    }

                    DbContext.Ado.CommitTran();
                    return true;
                }
                else
                {
                    error_message = "没有生成托运单配置数据";
                    return false;

                }


            }
            catch (Exception ex)
            {
                if (DbContext.Ado.IsAnyTran())
                {
                    DbContext.Ado.RollbackTran();
                }
                error_message = ex.Message;
                return false;
            }
        }

        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="DocNum">单据号</param>
        /// <param name="Remark">备注</param>
        /// <param name="deletedetail">删除数组</param>
        /// <param name="entities">集合</param>
        /// <param name="user">用户</param>
        /// <param name="error_message">错误信息</param>
        /// <returns></returns>
        public bool Update(SD_ConsignmentNoteParameters Parameters, string user, out string error_message)
        {
            error_message = "";
            DateTime time= DateTime.Now;
            try
            {
                //根据主键ID查询明细信息
                var deletedetailArray = _detailApp.GetListByKeys(Parameters.deletedetail);

                //根据申请单号查询主表信息
                var query = GetList(x => x.DocNum == Parameters.DocNum).ToList().FirstOrDefault();

                var AddmsDetailList = new List<SD_ConsignmentNoteDetail>();
                var UpdatemsDetailList = new List<SD_ConsignmentNoteDetail>();

                decimal? Weight = 0;
                foreach (var msdetail in Parameters.detailed)
                {
                    if (string.IsNullOrEmpty(msdetail.ConsignmentNoteDetailID))
                    {
                        msdetail.DocNum = Parameters.DocNum;
                        msdetail.IsDelete = false;
                        msdetail.CUser = user;
                        msdetail.CTime = time;
                        AddmsDetailList.Add(msdetail);
                    }
                    else
                    {
                        msdetail.MUser = user;
                        msdetail.MTime = time;
                        UpdatemsDetailList.Add(msdetail);
                    }
                    Weight += msdetail.Weight;
                }

                //把总金额按重量比例分配给明细行
                decimal? sumAmount = Parameters.TotalTheoreticalAmount + Parameters.SpecialExpenses;
                foreach (var mod in Parameters.detailed)
                {
                    mod.RowTheoreticalAmount = mod.Weight / Weight * sumAmount;
                }

                DbContext.Ado.BeginTran();
                _detailApp.DbContext = this.DbContext;

                //删除明细信息
                if (deletedetailArray.Count > 0)
                {
                    _detailApp.Delete(deletedetailArray, user);
                }
                //更新主表信息
                if (query != null)
                {
                    if (query.Type == 1)
                    {
                        query.Remark = Parameters.Remark;
                        query.TotalTheoreticalAmount = Parameters.TotalTheoreticalAmount;
                        query.SpecialExpenses = Parameters.SpecialExpenses;
                        query.ShippingDepar = Parameters.ShippingDepar;
                        query.ShippingType = Parameters.ShippingType;
                        query.Shipper = Parameters.Shipper;
                        query.CarNum = Parameters.CarNum;
                    }
                    else if (query.Type == 2)
                    {
                        query.CustomerID = Parameters.CustomerID;
                        query.CustomerCode = Parameters.CustomerCode;
                        query.CustomerName = Parameters.CustomerName;
                        query.CustomerAdd = Parameters.CustomerAdd;
                        query.CustomerRegion = Parameters.CustomerRegion;//结算地址
                        query.SpecialExpenses = Parameters.SpecialExpenses;
                        query.Remark = Parameters.Remark;
                        query.TotalTheoreticalAmount = Parameters.TotalTheoreticalAmount;
                        query.TotalWeight = Parameters.TotalWeight;
                        query.LogisticsSupplierCode = Parameters.LogisticsSupplierCode;
                        query.LogisticsSupplierName = Parameters.LogisticsSupplierName;
                        query.ShippingDepar = Parameters.ShippingDepar;
                        query.ShippingType = Parameters.ShippingType;
                        query.Shipper = Parameters.Shipper;
                        query.CarNum = Parameters.CarNum;
                    }
                    query.MUser = user;
                    query.MTime = time;
                    
                    base.Update(query);
                }
                //插入明细信息
                if (AddmsDetailList.Count > 0)
                {
                    _detailApp.Insert(AddmsDetailList);
                }
                //更新明细信息
                if (UpdatemsDetailList.Count > 0)
                {
                    _detailApp.Update(UpdatemsDetailList);
                }

                DbContext.Ado.CommitTran();

                return true;
            }
            catch (Exception ex)
            {
                DbContext.Ado.RollbackTran();
                error_message = ex.Message;
                return false;
            }
        }

        #endregion

        #region 导出

        /// <summary>
        /// 导出
        /// </summary>
        /// <param name="condition">条件</param>
        /// <returns></returns>
        public ISugarQueryable<SD_ConsignmentNote_View> GetConsignmentNote_ViewInfo(string keyword)
        {
            var query = DbContext.Queryable<SD_ConsignmentNote, SD_ConsignmentNoteDetail>((note, notedetail) => new object[]
             {
                 JoinType.Left,notedetail.DocNum==note.DocNum && note.IsDelete==false
             })
             .Where((note, notedetail) => note.IsDelete == false)
             .Where((note, notedetail)=> string.IsNullOrEmpty(keyword) || note.DocNum.Contains(keyword)
                        || note.CUser.Contains(keyword))
             .Select((note, notedetail) => new SD_ConsignmentNote_View
             {
                 DocNum = note.DocNum,
                 ShippingPlanNum = note.ShippingPlanNum,
                 DeliveryDate = note.DeliveryDate,
                 CustomerID=note.CustomerID,
                 CustomerCode = note.CustomerCode,
                 CustomerName = note.CustomerName,
                 CustomerAdd = note.CustomerAdd,
                 CustomerRegion = note.CustomerRegion,
                 LogisticsSupplierCode = note.LogisticsSupplierCode,
                 LogisticsSupplierName = note.LogisticsSupplierName,
                 ShippingDepar=note.ShippingDepar,
                 ShippingType=note.ShippingType,
                 Shipper=note.Shipper,
                 CarNum=note.CarNum,
                 SpecialExpenses=note.SpecialExpenses,
                 TotalTheoreticalAmount=note.TotalTheoreticalAmount,
                 TotalWeight=note.TotalWeight,
                 Status=note.Status,
                 Type=note.Type,
                 Remark=note.Remark,
                 Line=notedetail.Line,
                 BaseEntry=notedetail.BaseEntry,
                 BaseNum=notedetail.BaseNum,
                 BaseType=notedetail.BaseType,
                 ContractNo=notedetail.ContractNo,
                 ItemCode = notedetail.ItemCode,
                 ItemName = notedetail.ItemName,
                 ItmsGrpCode = notedetail.ItmsGrpCode,
                 ItmsGrpName = notedetail.ItmsGrpName,
                 Qty = notedetail.Qty,
                 Unit = notedetail.Unit,
                 Contact = notedetail.Contact,
                 Telephone = notedetail.Telephone,
                 Mileage = notedetail.Mileage,
                 MileageRate = notedetail.MileageRate,
                 Weight = notedetail.Weight,
                 WeightRate = notedetail.WeightRate,
                 WeightUnit = notedetail.WeightUnit,
                 RowTheoreticalAmount = notedetail.RowTheoreticalAmount,
                 CUser=note.CUser,
                 CTime=note.CTime
             });

            return query;
        }

        #endregion

        #region 完成

        /// <summary>
        /// 完成
        /// </summary>
        /// <param name="DocNums">单号</param>
        /// <param name="opUser">当前用户</param>
        /// <param name="error_message">错误消息</param>
        public bool Finish(string[] DocNums, string opUser, out string error_message)
        {
            error_message = "";
           
            string DocNum = string.Join(",", DocNums.ToArray());
            var pm1 = new SugarParameter("@DocNum", DocNum);
            var outPm = new SugarParameter("@cout", null, DbType.UInt32,ParameterDirection.Output) ;
            DbContext.Ado.UseStoredProcedure().ExecuteCommand("PROC_UpdateConsignmentNoteStatusByDocNum", pm1, outPm);
            //if (Convert.ToInt32(outPm.Value) > 0)
            return true;
            // else
            // return false;
        }

        #endregion
    }
}

