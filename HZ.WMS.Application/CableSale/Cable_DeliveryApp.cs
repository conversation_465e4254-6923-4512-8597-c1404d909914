using System;
using System.Collections.Generic;
using System.Linq;
using AOS.WMS.Entity.CableProduce;
using HZ.Core.Http;
using HZ.WMS.Application.SAP;
using HZ.WMS.Application.Sys;
using HZ.WMS.Entity;
using HZ.WMS.Entity.PP;
using HZ.WMS.Entity.SAP;
using HZ.WMS.Entity.Sys;
using SqlSugar;

namespace HZ.WMS.Application.PP
{
    /// <summary>
    /// 生产订单主表
    /// </summary>
    public class Cable_DeliveryApp : BaseApp<Delivery>
    {
        private BaseApp<BaseEntity> _baseApp = new BaseApp<BaseEntity>();
        private Sys_SAPCompanyInfoApp _sap = new Sys_SAPCompanyInfoApp();

        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public Cable_DeliveryApp() : base()
        {
        }

        #endregion

        /**
         * 获取 计划订单列表
         */
        public List<Delivery> GetPlanOrderPageList(Pagination page, Cable_ProductionOrderListReq productionOrderListReq)
        {
            return GetPageList(page, t =>
                (string.IsNullOrEmpty(productionOrderListReq.ContractNo) || t.ContractNo.Equals(productionOrderListReq.ContractNo))
                                                                       && (string.IsNullOrEmpty(productionOrderListReq.SapNo) || t.SapNo.Equals(productionOrderListReq.SapNo))
                                                                       && (productionOrderListReq.SapLine == null || t.SapLine.Equals(productionOrderListReq.SapLine)) &&
                                                                       t.CTime >= productionOrderListReq.GetStartCreateDate() &&
                                                                       t.CTime < productionOrderListReq.GetEndCreateDate());
        }


        public bool Deletes(string[] ids, string loginAccount)
        {
            DeleteByKeys(ids, loginAccount);
            return true;
        }

        /**
         * 过账
         */
        public ResponseData Post(List<string> ids, Sys_User currentUser)
        {
            var list = GetList(t => ids.Contains(t.Id)).ToList();
            
            foreach (var delivery in list)
            {
                ZFGWMS001 param = new ZFGWMS001();
                
                param.VBELN = delivery.SapNo;
                param.LFDAT = DateTime.Now;
                param.WAUHR = DateTime.Now;
                
                List<ZFGWMS001_1> lst = new List<ZFGWMS001_1>();
                ZFGWMS001_1 zfgwms0011 = new ZFGWMS001_1();
                zfgwms0011.VGPOS = delivery.SapLine;
                zfgwms0011.LGORT = delivery.StoreCode;
                zfgwms0011.LFIMG = delivery.Quantity;
                zfgwms0011.ZEREMARK = "";
                zfgwms0011.ZJHBS = "";
                lst.Add(zfgwms0011);
                
                var res = _sap.ZFGWMS001("001", param, DateTime.Now, lst, out bool isPosted, out string rstMessage);
                if (isPosted)
                {
                    delivery.DeliveryNo = res[0].sapDocNum;
                    delivery.PostUser = currentUser.UserName;
                    delivery.PostTime = DateTime.Now;
                    delivery.IsPost = true;
                }
                else
                {
                    delivery.PostUser = currentUser.UserName;
                    delivery.PostTime = DateTime.Now;
                    delivery.IsPost = false;
                    delivery.PostErrMsg = rstMessage;
                }
                Update(delivery);
            }
            return new ResponseData((int)WMSStatusCode.Success,"过账成功");
        }

        /**
         * 取消过账
         */
        public ResponseData CancelPost(List<string> ids, Sys_User currentUser)
        {
            
            var list = GetList(t => ids.Contains(t.Id)).ToList();
            var notPostCount = list.Count(t => t.IsPost == false);
            if (notPostCount > 0)
            {
                return new ResponseData((int)WMSStatusCode.Failed,"包含未过账的订单，请更正后重新提交");
            }
            foreach (var delivery in list)
            {
                //查询Sap账期时间
                DateTime ManualPostTime = new SAPApp().GetSAPpostTime(Convert.ToDateTime(delivery.PostTime));
                var res = _sap.ZFGWMS025("001", delivery.DeliveryNo, ManualPostTime, out bool isPosted, out string rstMessage);
                if (isPosted)
                {
                    delivery.DeliveryNo = null;
                    delivery.PostUser = null;
                    delivery.PostTime = null;
                    delivery.IsPost = false;
                }
                Update(delivery);
            }
            return new ResponseData((int)WMSStatusCode.Success,"取消过账成功");
        }
    }
}