
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio 14
VisualStudioVersion = 14.0.25420.1
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AOS.WMS.Entity", "HZ.WMS.Entity\AOS.WMS.Entity.csproj", "{A56C0618-C820-42E7-89B5-E5DA01C72729}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AOS.WMS.Application", "HZ.WMS.Application\AOS.WMS.Application.csproj", "{28DBE3A0-F19A-49E4-9CFE-6F8177CB17BF}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AOS.Core", "HZ.Core\AOS.Core.csproj", "{62D9F685-5537-494E-8D51-4DAF877F8AF1}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AOS.WMS.SAPData", "HZ.WMS.BydDataAdapter\AOS.WMS.SAPData.csproj", "{ED97ED39-1A86-4C93-831B-ACA2E4C5005A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AOS.WMS.WebAPI", "HZ.WMS.WebAPI\AOS.WMS.WebAPI.csproj", "{539182E9-B8C9-44C8-807D-4833D0CA7927}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AOS.WMS.WebJob", "HZ.WMS.WebJob\AOS.WMS.WebJob.csproj", "{533772A9-34FE-41E5-BE23-74C0480B2895}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AOS.WMS.DbConfigTool", "HZ.WMS.DbConfigTool\AOS.WMS.DbConfigTool.csproj", "{184E2F6E-BFC9-4A8F-8396-68759F578D56}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{A56C0618-C820-42E7-89B5-E5DA01C72729}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A56C0618-C820-42E7-89B5-E5DA01C72729}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A56C0618-C820-42E7-89B5-E5DA01C72729}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A56C0618-C820-42E7-89B5-E5DA01C72729}.Release|Any CPU.Build.0 = Release|Any CPU
		{28DBE3A0-F19A-49E4-9CFE-6F8177CB17BF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{28DBE3A0-F19A-49E4-9CFE-6F8177CB17BF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{28DBE3A0-F19A-49E4-9CFE-6F8177CB17BF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{28DBE3A0-F19A-49E4-9CFE-6F8177CB17BF}.Release|Any CPU.Build.0 = Release|Any CPU
		{62D9F685-5537-494E-8D51-4DAF877F8AF1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{62D9F685-5537-494E-8D51-4DAF877F8AF1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{62D9F685-5537-494E-8D51-4DAF877F8AF1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{62D9F685-5537-494E-8D51-4DAF877F8AF1}.Release|Any CPU.Build.0 = Release|Any CPU
		{ED97ED39-1A86-4C93-831B-ACA2E4C5005A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{ED97ED39-1A86-4C93-831B-ACA2E4C5005A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{ED97ED39-1A86-4C93-831B-ACA2E4C5005A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{ED97ED39-1A86-4C93-831B-ACA2E4C5005A}.Release|Any CPU.Build.0 = Release|Any CPU
		{539182E9-B8C9-44C8-807D-4833D0CA7927}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{539182E9-B8C9-44C8-807D-4833D0CA7927}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{539182E9-B8C9-44C8-807D-4833D0CA7927}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{539182E9-B8C9-44C8-807D-4833D0CA7927}.Release|Any CPU.Build.0 = Release|Any CPU
		{533772A9-34FE-41E5-BE23-74C0480B2895}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{533772A9-34FE-41E5-BE23-74C0480B2895}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{533772A9-34FE-41E5-BE23-74C0480B2895}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{533772A9-34FE-41E5-BE23-74C0480B2895}.Release|Any CPU.Build.0 = Release|Any CPU
		{184E2F6E-BFC9-4A8F-8396-68759F578D56}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{184E2F6E-BFC9-4A8F-8396-68759F578D56}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{184E2F6E-BFC9-4A8F-8396-68759F578D56}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{184E2F6E-BFC9-4A8F-8396-68759F578D56}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {A319DD62-C14E-4EC0-BE57-84262D8E15F2}
	EndGlobalSection
EndGlobal
