using System;
using System.ComponentModel;
using HZ.Core.Utilities;
using HZ.WMS.Entity;

namespace AOS.WMS.Entity.CableProduce
{
    public class Cable_PrintUpdate : BaseEntity
    {

        /// <summary>
        /// Ids
        /// </summary>
        [Description("Ids")]
        public string[] Ids { get; set; }
        
        /// <summary>
        /// 识别号 1 工单 2电缆箱排产清单 3分支线排产 4附加线排产 5 随行电缆裁线 6标签 7清单A4 8清单A5 9唛头
        /// </summary>
        [Description("识别号")]
        public int Identification { get; set; }
        

    }
}