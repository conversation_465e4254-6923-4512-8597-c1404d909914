using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.PP.ViewModel
{
    /// <summary>
    /// 主机总装线/巨通线/蒂森线
    /// </summary>
    public class PP_ExportHostLine_View
    {
        /// <summary>
        /// 生产订单
        /// </summary>
        public string ProductionOrderNo { get; set; }
        /// <summary>
        /// 开始日期(装配日期)
        /// </summary>
        public DateTime? StartTime { get; set; }
        /// <summary>
        /// 生产调度员,101是主机，组件
        /// </summary>
        public string ProductionScheduler { get; set; }
        /// <summary>
        /// 工作中心
        /// </summary>
        public string ProductionLineCode { get; set; }
        /// <summary>
        /// 合同号
        /// </summary>
        [Description("合同号")]
        public string ContractNo { get; set; }
        /// <summary>
        /// 出厂编号
        /// </summary>
        [Description("出厂编号")]
        public string SerialNo { get; set; }
        /// <summary>
        /// 发货单位
        /// </summary>
        [Description("客户")]
        public string Shipper { get; set; }
        /// <summary>
        /// 批次
        /// </summary>
        [Description("批次")]
        public string Batch { get; set; }
        /// <summary>
        /// 装配线
        /// </summary>
        [Description("装配线")]
        public string AssemblyLineNo { get; set; }
        /// <summary>
        /// 接受编号
        /// </summary>
        [Description("接受编号")]
        public string AcceptanceNo { get; set; }
        /// <summary>
        /// 远程松闸
        /// </summary>
        [Description("远程松闸")]
        public string RemoteBrake { get; set; }
        /// <summary>
        /// 盘车齿轮
        /// </summary>
        [Description("盘车齿轮")]
        public string BarringGear { get; set; }
        /// <summary>
        /// 制动器电压
        /// </summary>
        [Description("制动器电压")]
        public string BrakeVoltage { get; set; }
        /// <summary>
        /// 油漆
        /// </summary>
        [Description("油漆")]
        public string Paint { get; set; }
        /// <summary>
        /// 产品型号(物料名称)
        /// </summary>
        [Description("主机型号")]
        public string MaterialName { get; set; }
        /// <summary>
        /// 机座
        /// </summary>
        [Description("机座")]
        public string StatorFrame { get; set; }
        /// <summary>
        /// 数量1
        /// </summary>
        [Description("数量1")]
        public string FrameQty { get; set; }
        /// <summary>
        /// 转子
        /// </summary>
        [Description("转子")]
        public string RotorRim { get; set; }
        /// <summary>
        /// 数量2
        /// </summary>
        [Description("数量2")]
        public string RotorRimQty { get; set; }
        /// <summary>
        /// 曳引轮
        /// </summary>
        [Description("曳引轮")]
        public string TractionWheel { get; set; }
        /// <summary>
        /// 数量3
        /// </summary>
        [Description("数量3")]
        public string WheelQty { get; set; }
        /// <summary>
        /// 定子铁芯
        /// </summary>
        [Description("定子铁芯")]
        public string StatorCore { get; set; }
        /// <summary>
        /// 数量4
        /// </summary>
        [Description("数量4")]
        public string StatorCoreQty { get; set; }
        /// <summary>
        /// 磁钢
        /// </summary>
        [Description("磁钢")]
        public string MagnetSteel { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量5")]
        public string MagnetSteelQty { get; set; }
        /// <summary>
        /// 调心滚子轴承
        /// </summary>
        [Description("调心滚子轴承")]
        public string SpericallRoller { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量6")]
        public string SpericallQty { get; set; }
        /// <summary>
        /// 物料描述
        /// </summary>
        [Description("物料描述")]
        public string SpericallDes { get; set; }
        /// <summary>
        /// 深沟球轴承
        /// </summary>
        [Description("深沟球轴承")]
        public string DeepGroove { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量7")]
        public string DeepGrooveQty { get; set; }
        /// <summary>
        /// 物料描述
        /// </summary>
        [Description("物料描述")]
        public string DeepGrooveDes { get; set; }
        /// <summary>
        /// 其他轴承
        /// </summary>
        [Description("其他轴承")]
        public string OtherBearing { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量8")]
        public string BearingQty { get; set; }
        /// <summary>
        /// 物料描述
        /// </summary>
        [Description("物料描述")]
        public string BearingDes { get; set; }
        /// <summary>
        /// 编码器
        /// </summary>
        [Description("编码器")]
        public string Encoder { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量9")]
        public string EncoderQty { get; set; }
        /// <summary>
        /// 编码器线
        /// </summary>
        [Description("编码器线")]
        public string EncoderLine { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量10")]
        public string EncoderLineQty { get; set; }
        /// <summary>
        /// 护罩
        /// </summary>
        [Description("护罩")]
        public string Shield { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量11")]
        public string ShieldQty { get; set; }
        /// <summary>
        /// 减震垫
        /// </summary>
        [Description("减震垫")]
        public string Cushioning { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量12")]
        public string CushioningQty { get; set; }
        /// <summary>
        /// 包装箱
        /// </summary>
        [Description("包装箱")]
        public string PackingBox { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量13")]
        public string PackingBoxQty { get; set; }
        /// <summary>
        /// 刷字
        /// </summary>
        [Description("刷字")]
        public string BrushWord { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量14")]
        public string BrushWordQty { get; set; }
        /// <summary>
        /// 刹车线
        /// </summary>
        [Description("刹车线")]
        public string BrakeLine { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量15")]
        public string BrakeLineQty { get; set; }
        /// <summary>
        /// 盘车手轮
        /// </summary>
        [Description("盘车手轮")]
        public string HandWheel { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量16")]
        public string HandWheelQty { get; set; }
        /// <summary>
        /// 远程控制盒
        /// </summary>
        [Description("远程控制盒")]
        public string ReConBox { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量17")]
        public string ReConBoxQty { get; set; }
        /// <summary>
        /// 锥孔镶嵌轴
        /// </summary>
        [Description("锥孔镶嵌轴")]
        public string TainShaft { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量18")]
        public string TainShaftQty { get; set; }
        /// <summary>
        /// 编码器托架
        /// </summary>
        [Description("编码器托架")]
        public string EncoderBracket { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量19")]
        public string EnBracketQty { get; set; }
        /// <summary>
        /// 铜压板
        /// </summary>
        [Description("铜压板")]
        public string CopperPlate { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量20")]
        public string PlateQty { get; set; }
        /// <summary>
        /// 齿圈
        /// </summary>
        [Description("齿圈")]
        public string RingGear { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量21")]
        public string RingGearQty { get; set; }
        /// <summary>
        /// 盘车小齿轮
        /// </summary>
        [Description("盘车小齿轮")]
        public string CrankPinion { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量22")]
        public string CrankPinionQty { get; set; }
        /// <summary>
        /// 接线盒组件
        /// </summary>
        [Description("接线盒组件")]
        public string JunctionBox { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量23")]
        public string JuncBoxQty { get; set; }
        /// <summary>
        /// 接线盒座
        /// </summary>
        [Description("接线盒座")]
        public string JunBoxSeat { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量24")]
        public string BoxSeatQty { get; set; }
        /// <summary>
        /// 接线盒盖
        /// </summary>
        [Description("接线盒盖")]
        public string JunBoxCover { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量25")]
        public string BoxCoverQty { get; set; }
        /// <summary>
        /// 密封体
        /// </summary>
        [Description("密封体")]
        public string SealBody { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量26")]
        public string SealBodyQty { get; set; }
        /// <summary>
        /// 盖板
        /// </summary>
        [Description("盖板")]
        public string CoverPlate { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量27")]
        public string CoverPlateQty { get; set; }
        /// <summary>
        /// 油封
        /// </summary>
        [Description("油封")]
        public string OilSeal { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量28")]
        public string OilSealQty { get; set; }
        /// <summary>
        /// 刹车片垫板
        /// </summary>
        [Description("刹车片垫板")]
        public string BrakePad { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量29")]
        public string BrakePadQty { get; set; }
        /// <summary>
        /// 骨架
        /// </summary>
        [Description("骨架")]
        public string Skeleton { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量30")]
        public string SkeletonQty { get; set; }
        /// <summary>
        /// 挡绳杆
        /// </summary>
        [Description("挡绳杆")]
        public string RopeRod { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量31")]
        public string RopeRodQty { get; set; }
        /// <summary>
        /// 轴用弹性挡圈
        /// </summary>
        [Description("轴用弹性挡圈")]
        public string ExternalCirclips { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量32")]
        public string ExCirQty { get; set; }
        /// <summary>
        /// 编码器防护罩
        /// </summary>
        [Description("编码器防护罩")]
        public string EncoderShield { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量33")]
        public string EnShieldQty { get; set; }
        /// <summary>
        /// 机座前支架
        /// </summary>
        [Description("机座前支架")]
        public string FrameBracket { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量34")]
        public string BracketQty { get; set; }
        /// <summary>
        /// 波形弹性垫圈
        /// </summary>
        [Description("波形弹性垫圈")]
        public string WaveWasher { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量35")]
        public string WaveWasherQty { get; set; }
        /// <summary>
        /// 轴套
        /// </summary>
        [Description("轴套")]
        public string ShaftSleeve { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量36")]
        public string ShaftQty { get; set; }
        /// <summary>
        /// 静板(左)
        /// </summary>
        [Description("静板(左)")]
        public string StaticPlateL { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量37")]
        public string PlateLQty { get; set; }
        /// <summary>
        /// 静板(右)
        /// </summary>
        [Description("静板(右)")]
        public string StaticPlateR { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量38")]
        public string PlateRQty { get; set; }
        /// <summary>
        /// 动板(左)
        /// </summary>
        [Description("动板(左)")]
        public string MovingPlateL { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量39")]
        public string MovingLQty { get; set; }
        /// <summary>
        /// 动板(右)
        /// </summary>
        [Description("动板(右)")]
        public string MovingPlateR { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量40")]
        public string MovingRQty { get; set; }
        /// <summary>
        /// 轴承盖
        /// </summary>
        [Description("轴承盖")]
        public string BearingCap { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量41")]
        public string BearingCapQty { get; set; }
        /// <summary>
        /// 滑套
        /// </summary>
        [Description("滑套")]
        public string SlidingSleeve { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量42")]
        public string SleeveQty { get; set; }
        /// <summary>
        /// 前轴承盖
        /// </summary>
        [Description("前轴承盖")]
        public string FrontBearCap { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量43")]
        public string FrontBearQty { get; set; }
        /// <summary>
        /// 盘车座
        /// </summary>
        [Description("盘车座")]
        public string TurningSeat { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量44")]
        public string TurningSeatQty { get; set; }
        /// <summary>
        /// 盘车孔盖板
        /// </summary>
        [Description("盘车孔盖板")]
        public string HoleCover { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量45")]
        public string HoleCoverQty { get; set; }
        /// <summary>
        /// 定子压板
        /// </summary>
        [Description("定子压板")]
        public string StatorPlate { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量46")]
        public string StatorPlateQty { get; set; }
        /// <summary>
        /// 盘车开关
        /// </summary>
        [Description("盘车开关")]
        public string SpeedSwitch { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量47")]
        public string SwitchQty { get; set; }
        /// <summary>
        /// 接线端子组件
        /// </summary>
        [Description("接线端子组件")]
        public string TerAssembly { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量48")]
        public string TerAssemblyQty { get; set; }
        /// <summary>
        /// 松闸扳手
        /// </summary>
        [Description("松闸扳手")]
        public string LooseWrench { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量49")]
        public string LooseWrenchQty { get; set; }
        /// <summary>
        /// 远程支撑杆
        /// </summary>
        [Description("远程支撑杆")]
        public string RemoteRod { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量50")]
        public string RemoteRodQty { get; set; }
        /// <summary>
        /// 吊耳
        /// </summary>
        [Description("吊耳")]
        public string LiftingLug { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量51")]
        public string LugQty { get; set; }
        /// <summary>
        /// 远程松闸座
        /// </summary>
        [Description("远程松闸座")]
        public string ReHolder { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量52")]
        public string ReHolderQty { get; set; }
        /// <summary>
        /// 后盖板
        /// </summary>
        [Description("后盖板")]
        public string BackPlate { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量53")]
        public string BackPlateQty { get; set; }
        /// <summary>
        /// 支架前端盖
        /// </summary>
        [Description("支架前端盖")]
        public string FrontCover { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量54")]
        public string FrontCoverQty { get; set; }
        /// <summary>
        /// 挡绳板
        /// </summary>
        [Description("挡绳板")]
        public string RopePlate { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量55")]
        public string RopePlateQty { get; set; }
        /// <summary>
        /// 通风安装板
        /// </summary>
        [Description("通风安装板")]
        public string VenPlate { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量56")]
        public string VenPlateQty { get; set; }
        /// <summary>
        /// 支架
        /// </summary>
        [Description("支架")]
        public string Stents { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量57")]
        public string StentsQty { get; set; }
        /// <summary>
        /// 带绕组定子铁芯
        /// </summary>
        [Description("带绕组定子铁芯")]
        public string WindingStator { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量58")]
        public string WindingQty { get; set; }
    }
}
