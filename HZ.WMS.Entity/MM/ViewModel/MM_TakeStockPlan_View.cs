using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.MM.ViewModel
{
    /// <summary>
    /// 
    /// </summary>
    public class MM_TakeStockPlan_View 
    {
        /// <summary> 
        /// 计划单号
        /// </summary> 
        [Description("盘点计划单号")]
        public string DocNum { get; set; }
        /// <summary> 
        /// 责任人
        /// </summary> 
        [Description("责任人")]
        public string PUser { get; set; }
        /// <summary> 
        /// 状态(未开始/已开始/已完成)
        /// 默认 1=未开始
        /// </summary> 
        [Description("状态")]
        public int? Status { get; set; } = 1;

        /// <summary> 
        /// 仓库编号
        /// </summary> 
         [Description("仓库编号")]
        public string WhsCode { get; set; }

        /// <summary> 
        /// 仓库名称
        /// </summary> 
          [Description("仓库名称")]
        public string WhsName { get; set; }

        /// <summary>
        /// 区域编号
        /// </summary>
        [Description("区域编号")]
        public string RegionCode { get; set; }
        /// <summary>
        /// 区域名称
        /// </summary>
        [Description("区域名称")]
        public string RegionName { get; set; }
        /// <summary>
        /// 库位编号
        /// </summary>
        [Description("库位编号")]
        public string BinLocationCode { get; set; }
        /// <summary>
        /// 库位名称
        /// </summary>
        [Description("库位名称")]
        public string BinLocationName { get; set; }

        /// <summary> 
        /// 出厂编号
        /// </summary> 
        [Description("出厂编号")]
        public string BarCode { get; set; }

        /// <summary>
        /// 物料编号
        /// </summary>
        [Description("物料编号")]
        public string ItemCode { get; set; }
        /// <summary>
        /// 物料名称
        /// </summary>
        [Description("物料名称")]
        public string ItemName { get; set; }


        /// <summary>
        /// 库存数量
        /// </summary>
        [Description("库存数量")]
        public decimal? Qty { get; set; }

        /// <summary>
        /// 盘点数量
        /// </summary>
        [Description("盘点数量")]
        public decimal? SQty { get; set; }


        /// <summary> 
        /// 特殊库存
        /// </summary> 
        [Description("特殊库存")]
        public string SpecialStock { get; set; }

        /// <summary> 
        /// 销售订单
        /// </summary> 
        [Description("销售订单")]
        public string SaleNum { get; set; }

        /// <summary> 
        /// 销售订单项目
        /// </summary> 
        [Description("销售订单项目")]
        public int? SaleLine { get; set; }

        /// <summary> 
        /// 评估类型
        /// </summary> 
        [Description("评估类型")]
        public string AssessType { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [Description("创建时间")]
        public DateTime? CTime { get; set; }
    }
}
