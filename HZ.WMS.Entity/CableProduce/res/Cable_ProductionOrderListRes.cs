using System.ComponentModel;
using SqlSugar;

namespace AOS.WMS.Entity.CableProduce
{
    public class Cable_ProductionOrderListRes : Cable_ProductionOrder
    {
        /// <summary>
        /// 客户名称
        /// </summary>
        [Description("客户名称")]
        public string CustomerName { get; set; }
        
        /// <summary>
        /// 合同号
        /// </summary>
        [Description("合同号")]
        public string ContractNo { get; set; }
        
        /// <summary>
        /// 批次
        /// </summary>
        [Description("批次")]
        public string BatchNum { get; set; }
        
        /// <summary>
        /// 客户订单号
        /// </summary>
        [Description("客户订单号")]
        public string CustomerOrderNum { get; set; }
        
        /// <summary>
        /// 排产序号
        /// </summary>
        [Description("排产序号")]
        public string ProductionNo { get; set; }
        
    }
}