using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.IO;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using AOS.OMS.Application.Util;
using AOS.OMS.Entity.Sale;
using HZ.Core.Http;
using HZ.Core.Office;
using HZ.WMS.Application.MD;
using HZ.WMS.Application.SD;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.Produce;
using HZ.WMS.Entity.Produce.Req;
using HZ.WMS.Entity.Produce.Res;
using HZ.WMS.Entity.SD;
using HZ.WMS.Entity.Sys;
using Newtonsoft.Json;
using SqlSugar;

namespace HZ.WMS.Application.Produce
{
    /// <summary>
    /// 生产报工应用服务
    /// </summary>
    public class Produce_ReportApp : BaseApp<Produce_Report>
    {
        
        #region 初始化

        Produce_SchedulingDetailApp _schedulingDetailApp = new Produce_SchedulingDetailApp();
        Produce_ShedulingApp _shedulingApp = new Produce_ShedulingApp();
        MD_ReportStationApp _reportStationApp = new MD_ReportStationApp();

        #endregion

        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public Produce_ReportApp() : base(){}

        #endregion

        #region 查询分页

        /// <summary>
        /// 获取分页列表
        /// </summary>
        /// <param name="page">分页参数</param>
        /// <param name="req">查询条件</param>
        /// <returns>分页数据列表</returns>
        public List<Produce_Report> GetPageList(Pagination page, Produce_ReportListReq req)
        {
            if (string.IsNullOrEmpty(page.Sort))
            {
                page.Sort = "CTime desc";
            }

            // 使用直接的条件构建方式
            var query = DbContext.Queryable<Produce_Report>()
                .Where(x => !x.IsDelete);

            // 添加查询条件
            if (!string.IsNullOrEmpty(req.ProduceReportNo))
            {
                query = query.Where(t => t.ProduceReportNo.Contains(req.ProduceReportNo));
            }
            if (!string.IsNullOrEmpty(req.ProduceOrderNo))
            {
                query = query.Where(t => t.ProduceOrderNo == req.ProduceOrderNo);
            }
            if (!string.IsNullOrEmpty(req.SerialNo))
            {
                query = query.Where(t => t.SerialNo.Contains(req.SerialNo));
            }
            if (!string.IsNullOrEmpty(req.MaterialNo))
            {
                query = query.Where(t => t.MaterialCode.Contains(req.MaterialNo));
            }
            if (!string.IsNullOrEmpty(req.MaterialName))
            {
                query = query.Where(t => t.MaterialName.Contains(req.MaterialName));
            }
            if (!string.IsNullOrEmpty(req.WorkCenterCode))
            {
                query = query.Where(t => t.WorkCenterCode == req.WorkCenterCode);
            }
            if (req.StartDate.HasValue)
            {
                query = query.Where(t => t.AssemblDate >= req.StartDate);
            }
            if (req.EndDate.HasValue)
            {
                query = query.Where(t => t.AssemblDate <= req.EndDate);
            }
            if (req.IsCompleted.HasValue)
            {
                query = query.Where(t => t.IsCompleted == req.IsCompleted);
            }
            if (req.IsPosted.HasValue)
            {
                query = query.Where(t => t.IsPosted == req.IsPosted);
            }

            // 排序
            query = query.OrderBy(page.Sort);

            // 执行分页查询
            int total = query.Count();
            page.Total = total;
            if (page.PageSize > total)
            {
                return query.ToPageList(page.PageNumber, total);
            }
            return query.ToPageList(page.PageNumber, page.PageSize);
        }

        #endregion

        #region 获取报工明细

        public List<Produce_ReportDetail> GetDetailList(string id)
        {
            return DbContext.Queryable<Produce_ReportDetail>().Where(t => !t.IsDelete && t.Pid == id).OrderBy(t => t.StationSort).ToList();
        }

        #endregion

        #region 获取实体

        /// <summary>
        /// 获取实体
        /// </summary>
        /// <param name="id">主键</param>
        /// <returns>实体</returns>
        public Produce_Report GetEntity(string id)
        {
            return GetEntityByKey(id);
        }

        #endregion
        
        #region 根据生产订单号获取详情

        public Produce_Report GetEntityByProduceOrderNo(string produceOrderNo)
        {
            return GetList(t => t.ProduceOrderNo == produceOrderNo).ToList().FirstOrDefault();
        }

        #endregion

        #region 获取明细

        /// <summary>
        /// 获取报工明细
        /// </summary>
        /// <param name="pid">父ID</param>
        /// <returns>明细列表</returns>
        public List<Produce_ReportDetail> GetDetails(string pid)
        {
            return DbContext.Queryable<Produce_ReportDetail>()
                .Where(t => t.Pid == pid && !t.IsDelete)
                .OrderBy(t => t.StationSort)
                .ToList();
        }

        #endregion

        #region 保存

        /// <summary>
        /// 保存数据
        /// </summary>
        /// <param name="entity">实体</param>
        /// <param name="details">明细</param>
        /// <param name="userName">用户名</param>
        /// <returns>保存结果</returns>
        public bool SaveForm(Produce_Report entity, List<Produce_ReportDetail> details, string userName)
        {
            try
            {
                DbContext.Ado.BeginTran();

                if (string.IsNullOrEmpty(entity.Id)) // 新增
                {
                    entity.Id = Guid.NewGuid().ToString();
                    entity.IsDelete = false;
                    entity.CUser = userName;
                    entity.CTime = DateTime.Now;

                    Insert(entity);

                    // 保存明细
                    if (details != null && details.Count > 0)
                    {
                        foreach (var detail in details)
                        {
                            detail.Id = Guid.NewGuid().ToString();
                            detail.Pid = entity.Id;
                            detail.IsDelete = false;
                            detail.CUser = userName;
                            detail.CTime = DateTime.Now;

                            DbContext.Insertable(detail).ExecuteCommand();
                        }
                    }
                }
                else // 修改
                {
                    var original = GetEntityByKey(entity.Id);
                    if (original == null)
                    {
                        return false;
                    }

                    // 更新主表
                    entity.MUser = userName;
                    entity.MTime = DateTime.Now;
                    entity.CUser = original.CUser;
                    entity.CTime = original.CTime;
                    entity.IsDelete = original.IsDelete;

                    Update(entity);

                    // 删除明细
                    var oldDetails = DbContext.Queryable<Produce_ReportDetail>()
                        .Where(x => x.Pid == entity.Id)
                        .ToList();
                        
                    foreach (var detail in oldDetails)
                    {
                        detail.IsDelete = true;
                        detail.DUser = userName;
                        detail.DTime = DateTime.Now;
                        DbContext.Updateable(detail).ExecuteCommand();
                    }

                    // 保存新明细
                    if (details != null && details.Count > 0)
                    {
                        foreach (var detail in details)
                        {
                            detail.Id = Guid.NewGuid().ToString();
                            detail.Pid = entity.Id;
                            detail.IsDelete = false;
                            detail.CUser = userName;
                            detail.CTime = DateTime.Now;

                            DbContext.Insertable(detail).ExecuteCommand();
                        }
                    }
                }

                DbContext.Ado.CommitTran();
                return true;
            }
            catch (Exception)
            {
                DbContext.Ado.RollbackTran();
                return false;
            }
        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除数据
        /// </summary>
        /// <param name="keyValue">主键</param>
        /// <param name="userName">用户名</param>
        /// <returns>删除结果</returns>
        public bool DeleteForm(string keyValue, string userName)
        {
            try
            {
                DbContext.Ado.BeginTran();

                // 删除主表
                var entity = GetEntityByKey(keyValue);
                if (entity != null)
                {
                    entity.IsDelete = true;
                    entity.DUser = userName;
                    entity.DTime = DateTime.Now;

                    Update(entity);

                    // 删除明细
                    var details = DbContext.Queryable<Produce_ReportDetail>()
                        .Where(x => x.Pid == entity.Id)
                        .ToList();
                        
                    foreach (var detail in details)
                    {
                        detail.IsDelete = true;
                        detail.DUser = userName;
                        detail.DTime = DateTime.Now;
                        DbContext.Updateable(detail).ExecuteCommand();
                    }
                }

                DbContext.Ado.CommitTran();
                return true;
            }
            catch (Exception)
            {
                DbContext.Ado.RollbackTran();
                return false;
            }
        }

        #endregion

        /// <summary>
        /// 检查前序工序是否已报工
        /// </summary>
        /// <param name="produceOrderNo">生产订单号</param>
        /// <param name="currentProcessNo">当前工序号</param>
        /// <returns>是否有待报工工序</returns>
        public bool CheckPreviousProcess(string produceOrderNo, decimal currentProcessNo)
        {
            // 获取前面工序的所有报工记录
            var previousProcesses = DbContext.Queryable<Produce_Report, Produce_ReportDetail>((r, d) => new JoinQueryInfos(
                    JoinType.Inner, r.Id == d.Pid))
                .Where((r, d) => r.ProduceOrderNo == produceOrderNo && 
                                 r.IsDelete == false && 
                                 d.IsDelete == false && 
                                 (r.IsCompleted == null || r.IsCompleted == false))
                .Select((r, d) => new { 
                    StationSort = d.StationSort 
                }).ToList();

            // 在内存中过滤那些排序号小于当前工序号的记录
            var pendingCount = previousProcesses.Count(p => {
                if (p.StationSort == null) return false;
                if (decimal.TryParse(p.StationSort.ToString(), out decimal sortNo))
                {
                    return sortNo < currentProcessNo;
                }
                return false;
            });

            return pendingCount > 0;
        }

        /// <summary>
        /// 执行报工操作
        /// </summary>
        /// <param name="req">报工请求</param>
        /// <param name="userName">用户名</param>
        /// <param name="message">返回信息</param>
        /// <returns>报工结果</returns>
        public bool DoReport(Produce_ReportScanReq req, string userName, out string message)
        {
            message = string.Empty;

            // 验证数据
            if (string.IsNullOrEmpty(req.ProduceOrderNo))
            {
                message = "生产订单号不能为空";
                return false;
            }

            if (!req.ProcessNo.HasValue)
            {
                message = "工序号不能为空";
                return false;
            }

            if (!req.ReportQty.HasValue || req.ReportQty <= 0)
            {
                message = "报工数量必须大于0";
                return false;
            }

            if ((!req.QualifiedQty.HasValue || req.QualifiedQty <= 0) && (!req.UnqualifiedQty.HasValue || req.UnqualifiedQty <= 0))
            {
                message = "合格数量和不合格数量至少有一项大于0";
                return false;
            }

            if (req.QualifiedQty.HasValue && req.UnqualifiedQty.HasValue && (req.QualifiedQty + req.UnqualifiedQty) != req.ReportQty)
            {
                message = "合格数量和不合格数量之和必须等于报工数量";
                return false;
            }

            // 获取生产订单信息
            var orderInfo = DbContext.Queryable<Produce_Scheduling>()
                .Where(x => x.ProduceOrderNo == req.ProduceOrderNo && !x.IsDelete)
                .ToList().FirstOrDefault();

            if (orderInfo == null)
            {
                message = "未找到对应的生产订单信息";
                return false;
            }

            // 查找对应的工序信息
            var processInfoList = DbContext.Queryable<Produce_ReportDetail>()
                .InnerJoin<Produce_Report>((d, r) => d.Pid == r.Id)
                .Where((d, r) => r.ProduceOrderNo == req.ProduceOrderNo && 
                        r.IsDelete == false && d.IsDelete == false)
                .Select((d, r) => new { 
                    WorkCenterCode = d.WorkCenterCode,
                    WorkCenterName = d.WorkCenterName,
                    StationCode = d.StationCode,
                    StationName = d.StationName,
                    StationSort = d.StationSort
                })
                .ToList();

            // 在内存中筛选正确的工序号
            var processInfo = processInfoList.FirstOrDefault(p => {
                if (p.StationSort == null) return false;
                if (decimal.TryParse(p.StationSort.ToString(), out decimal sortNo))
                {
                    return sortNo == req.ProcessNo;
                }
                return false;
            });

            if (processInfo == null)
            {
                message = "未找到对应的工序信息";
                return false;
            }

            // 检查前面工序是否已报工
            var hasPendingProcess = CheckPreviousProcess(req.ProduceOrderNo, Convert.ToInt32(req.ProcessNo));
            if (hasPendingProcess)
            {
                message = "前面有工序未报工，请先完成前面的工序报工";
                return false;
            }

            try
            {
                DbContext.Ado.BeginTran();

                // 创建报工记录
                Produce_Report report = new Produce_Report
                {
                    Id = Guid.NewGuid().ToString(),
                    ProduceReportNo = "RPT" + DateTime.Now.ToString("yyyyMMddHHmmss"),
                    SerialNo = orderInfo.SerialNo,
                    ProduceOrderNo = req.ProduceOrderNo,
                    OrderType = orderInfo.OrderType,
                    OrderQty = orderInfo.Quantity,
                    Unit = orderInfo.Unit,
                    ReportTotal = req.ReportQty,
                    QualifiedQty = req.QualifiedQty,
                    UnqualifiedQty = req.UnqualifiedQty,
                    UnqualifiedRemarks = req.UnqualifiedRemarks,
                    AssemblDate = DateTime.Now,
                    CustomerName = orderInfo.CustomerName,
                    IsCompleted = true,
                    IsPosted = false,
                    WorkCenterCode = processInfo.WorkCenterCode,
                    WorkCenterName = processInfo.WorkCenterName,
                    CurrentStationCode = processInfo.StationCode,
                    IsDelete = false,
                    CUser = userName,
                    CTime = DateTime.Now
                };

                Insert(report);

                // 创建报工明细
                Produce_ReportDetail detail = new Produce_ReportDetail
                {
                    Id = Guid.NewGuid().ToString(),
                    Pid = report.Id,
                    WorkCenterName = processInfo.WorkCenterName,
                    WorkCenterCode = processInfo.WorkCenterCode,
                    ConfirmTime = DateTime.Now,
                    Status = 1,
                    EmployeeCode = req.EmployeeNumber,
                    EmployeeName = req.EmployeeName,
                    StationCode = processInfo.StationCode,
                    StationName = processInfo.StationName,
                    StationSort = processInfo.StationSort,
                    IsDelete = false,
                    CUser = userName,
                    CTime = DateTime.Now
                };

                DbContext.Insertable(detail).ExecuteCommand();

                DbContext.Ado.CommitTran();
                message = "报工成功";
                return true;
            }
            catch (Exception ex)
            {
                DbContext.Ado.RollbackTran();
                message = "报工失败：" + ex.Message;
                return false;
            }
        }


        #region 创建初始报工信息

        /// <summary>
        /// 创建初始报工信息
        /// </summary>
        /// <param name="schedulingIds">排产ID列表</param>
        /// <param name="userName">当前用户</param>
        /// <returns>创建结果</returns>
        public bool CreateInitialReportInfo(string[] schedulingIds, string userName)
        {
            try
            {
                DbContext.Ado.BeginTran();

                // 获取排产数据
                var schedulings = _shedulingApp.GetList(x => schedulingIds.Contains(x.Id)).ToList();

                // 只处理成功的生产订单（状态为3）
                schedulings = schedulings.Where(t => t.Status == 3).ToList();
                if (schedulings.Count == 0)
                {
                    DbContext.Ado.CommitTran();
                    return true; // 没有需要处理的订单，直接返回成功
                }

                var schedulingDict = schedulings.ToDictionary(t => t.Id, t => t);

                var schedulingDetails = _schedulingDetailApp.GetList(x => schedulingIds.Contains(x.Pid)).ToList();

                // 获取物料对应的工作中心
                var materialCodeList = schedulingDetails.Select(t => t.MaterialCode).ToList();
                string queryMatnrVersionUri = ConfigurationManager.AppSettings["SapUrl"] + ConfigurationManager.AppSettings["GetMatnrVersion"];
                string token = ConfigurationManager.AppSettings["SapToken"];
                List<GetMatnrVersionReq> lineQueries = new List<GetMatnrVersionReq>();
                foreach (var material in materialCodeList)
                {
                    GetMatnrVersionReq getMatnrVersionReq = new GetMatnrVersionReq();
                    getMatnrVersionReq.WERKS = "2002";
                    getMatnrVersionReq.MATNR = material;
                    getMatnrVersionReq.VERID = "0001";
                    getMatnrVersionReq.ADATU = DateTime.Now.ToString("yyyyMMdd");
                    lineQueries.Add(getMatnrVersionReq);
                }
                string postRst = HttpUtil.HttpPost(queryMatnrVersionUri + "?token=" + token, JsonConvert.SerializeObject(lineQueries), "POST");
                var result = JsonConvert.DeserializeObject<List<GetMatnrVersionRes>>(postRst);
                var getMatnrVersionDict = result.ToDictionary(t => t.MATNR, t => t);

                var stationList = result.Select(t => t.ARBPL).ToList();

                // 查询工作中心站点信息
                var reportStationList = _reportStationApp.GetList(t => stationList.Contains(t.WorkCenterCode)).ToList();

                var reportStationDict = reportStationList.GroupBy(t => t.WorkCenterCode).ToDictionary(t => t.Key, t => t.ToList());

                foreach (var schedulingDetail in schedulingDetails)
                {
                    
                    if (!schedulingDict.ContainsKey(schedulingDetail.Id))
                    {
                        continue;
                    }
                    
                    if (!getMatnrVersionDict.ContainsKey(schedulingDetail.MaterialCode))
                    {
                        continue;
                    }

                    var scheduling = schedulingDict[schedulingDetail.Id];
                    var matnrVersion = getMatnrVersionDict[schedulingDetail.MaterialCode];
                    
                    // 创建报工基础信息
                    Produce_Report report = new Produce_Report
                    {
                        Id = Guid.NewGuid().ToString(),
                        ProduceReportNo = "RI" + DateTime.Now.ToString("yyyyMMddHHmmss") + "_" + schedulings.IndexOf(scheduling),
                        SerialNo = scheduling.SerialNo,
                        ProduceOrderNo = schedulingDetail.ProduceOrderNo,
                        MaterialCode = schedulingDetail.ProduceOrderNo,
                        ProduceScheduler = userName,
                        OrderType = scheduling.OrderType,
                        OrderQty = scheduling.Quantity,
                        Unit = scheduling.Unit,
                        ReportTotal = 0,
                        QualifiedQty = 0,
                        UnqualifiedQty = 0,
                        AssemblDate = scheduling.ProduceSchedulingDate,
                        CustomerName = scheduling.CustomerName,
                        SapNo = scheduling.SaleSapNo,
                        SapLine = scheduling.SaleSapLine.ToString(),
                        WorkCenterName = matnrVersion.KTEXT,
                        WorkCenterCode = matnrVersion.ARBPL,
                        IsCompleted = false,
                        IsPosted = false,
                        IsDelete = false,
                        CUser = userName,
                        CTime = DateTime.Now
                    };
                    List<Produce_ReportDetail> reportDetails = new List<Produce_ReportDetail>();
                    // 使用本地工作中心站点数据创建报工明细
                    if (reportStationDict.ContainsKey(report.WorkCenterCode))
                    {
                        var reportStations = reportStationDict[report.WorkCenterCode];
                        
                        foreach (var reportStation in reportStations)
                        {
                            // 创建报工站点明细
                            Produce_ReportDetail reportDetail = new Produce_ReportDetail
                            {
                                Id = Guid.NewGuid().ToString(),
                                Pid = report.Id,
                                MaterialCode = schedulingDetail.MaterialCode,
                                WorkCenterName = reportStation.WorkCenterName,
                                WorkCenterCode = reportStation.WorkCenterCode,
                                Status = 2, // 待报工状态
                                EmployeeName = userName,
                                StationName = reportStation.StationName,
                                StationCode = reportStation.StationCode,
                                StationSort = reportStation.StationSort,
                                IsDelete = false,
                                CUser = userName,
                                CTime = DateTime.Now
                            };
                            reportDetails.Add(reportDetail);
                        }
                    }
                    Insert(report);
                    DbContext.Insertable(reportDetails).ExecuteCommand();
                }
                DbContext.Ado.CommitTran();
                return true;
            }
            catch (Exception)
            {
                DbContext.Ado.RollbackTran();
                return false;
            }
        }

        #endregion

        #region 导出

        /// <summary>
        /// 导出数据
        /// </summary>
        /// <param name="req">查询条件</param>
        /// <returns>Excel数据流</returns>
        public byte[] ExportData(Produce_ReportListReq req)
        {
            // 查询数据
            var pagination = new Pagination { PageSize = int.MaxValue };
            var data = GetPageList(pagination, req);

            // 创建数据表
            DataTable dt = new DataTable("生产报工数据");
            dt.Columns.Add("生产报工单号");
            dt.Columns.Add("出厂编号");
            dt.Columns.Add("生产订单号");
            dt.Columns.Add("主机生产订单号");
            dt.Columns.Add("生产调度员");
            dt.Columns.Add("物料编码");
            dt.Columns.Add("物料描述");
            dt.Columns.Add("订单类型");
            dt.Columns.Add("订单数量");
            dt.Columns.Add("单位");
            dt.Columns.Add("报工数量");
            dt.Columns.Add("合格数量");
            dt.Columns.Add("不合格数量");
            dt.Columns.Add("不合格备注");
            dt.Columns.Add("装配日期");
            dt.Columns.Add("工作中心");
            dt.Columns.Add("状态");

            // 填充数据
            foreach (var item in data)
            {
                DataRow row = dt.NewRow();
                row["生产报工单号"] = item.ProduceReportNo;
                row["出厂编号"] = item.SerialNo;
                row["生产订单号"] = item.ProduceOrderNo;
                row["主机生产订单号"] = item.HostProduceOrderNo;
                row["生产调度员"] = item.ProduceScheduler;
                row["物料编码"] = item.MaterialCode;
                row["物料描述"] = item.MaterialName;
                row["订单类型"] = item.OrderType;
                row["订单数量"] = item.OrderQty.HasValue ? item.OrderQty.Value.ToString() : "0";
                row["单位"] = item.Unit;
                row["报工数量"] = item.ReportTotal.HasValue ? item.ReportTotal.Value.ToString() : "0";
                row["合格数量"] = item.QualifiedQty.HasValue ? item.QualifiedQty.Value.ToString() : "0";
                row["不合格数量"] = item.UnqualifiedQty.HasValue ? item.UnqualifiedQty.Value.ToString() : "0";
                row["不合格备注"] = item.UnqualifiedRemarks;
                row["装配日期"] = item.AssemblDate.HasValue ? item.AssemblDate.Value.ToString("yyyy-MM-dd") : "";
                row["工作中心"] = item.WorkCenterName;
                row["状态"] = item.IsCompleted == true ? "已完成" : "未完成";
                dt.Rows.Add(row);
            }

            // 创建内存流
            MemoryStream ms = new MemoryStream();
            // 这里使用现有工具类导出数据
            ExcelUtil excelUtil = new ExcelUtil();
            ms = excelUtil.CreateExcel(dt);
            return ms.ToArray();
        }

        #endregion

        #region 导入模板

        /// <summary>
        /// 获取导入模板
        /// </summary>
        /// <returns>Excel数据流</returns>
        public byte[] GetImportTemplate()
        {
            // 创建数据表
            DataTable dt = new DataTable("生产报工导入模板");
            dt.Columns.Add("生产订单号(*)");
            dt.Columns.Add("出厂编号");
            dt.Columns.Add("物料编码");
            dt.Columns.Add("物料描述");
            dt.Columns.Add("报工数量(*)");
            dt.Columns.Add("合格数量(*)");
            dt.Columns.Add("不合格数量");
            dt.Columns.Add("不合格备注");
            dt.Columns.Add("工作中心编码(*)");
            dt.Columns.Add("工作中心名称");
            dt.Columns.Add("备注");

            // 创建内存流
            MemoryStream ms = new MemoryStream();
            // 这里使用现有工具类导出数据
            ExcelUtil excelUtil = new ExcelUtil();
            ms = excelUtil.CreateExcel(dt);
            return ms.ToArray();
        }

        #endregion

        #region 站点扫描

        /// <summary>
        /// 站点扫描
        /// </summary>
        /// <param name="stationCode">站点代码</param>
        /// <param name="produceOrderNo">生产工单号</param>
        /// <param name="userName">用户名</param>
        /// <param name="message">返回消息</param>
        /// <returns>处理结果</returns>
        public bool ScanStation(string stationCode, string produceOrderNo, string userName, out string message)
        {
            message = string.Empty;
            
            // 参数验证
            if (string.IsNullOrEmpty(stationCode))
            {
                message = "站点代码不能为空";
                return false;
            }

            if (string.IsNullOrEmpty(produceOrderNo))
            {
                message = "生产工单号不能为空";
                return false;
            }

            try
            {
                // 查找对应的生产报工记录
                var report = DbContext.Queryable<Produce_Report>()
                    .Where(r => r.ProduceOrderNo == produceOrderNo && !r.IsDelete)
                    .ToList().FirstOrDefault();

                if (report == null)
                {
                    message = "未找到对应的生产报工记录";
                    return false;
                }

                // 查找当前站点明细
                var currentDetail = DbContext.Queryable<Produce_ReportDetail>()
                    .Where(d => d.Pid == report.Id && d.StationCode == stationCode && !d.IsDelete)
                    .ToList().FirstOrDefault();

                if (currentDetail == null)
                {
                    message = "未找到对应的站点明细记录";
                    return false;
                }
                
                // 记录扫描历史
                DateTime now = DateTime.Now;
                
                // 重复扫描校验
                if (currentDetail.Status == 1)
                {
                    // 查询扫描时间
                    var confirmTime = currentDetail.ConfirmTime.HasValue ? currentDetail.ConfirmTime.Value.ToString("yyyy-MM-dd HH:mm:ss") : "未知";
                    message = $"该站点已经扫描过当前生产订单，扫描时间：{confirmTime}，操作人：{currentDetail.EmployeeName}";
                    return false;
                }

                // 获取所有站点明细并按站点序号排序
                var allDetails = DbContext.Queryable<Produce_ReportDetail>()
                    .Where(d => d.Pid == report.Id && !d.IsDelete)
                    .OrderBy(d => d.StationSort)
                    .ToList();

                // 获取当前站点的序号
                decimal currentSort = currentDetail.StationSort ?? 0;

                // 检查比当前序号小的站点是否已扫描
                foreach (var detail in allDetails)
                {
                    decimal detailSort = detail.StationSort ?? 0;
                    if (detailSort < currentSort && (detail.Status == null || detail.Status == 0))
                    {
                        message = $"站点 {detail.StationName}({detail.StationCode}) 尚未扫描，请先完成前面的站点扫描";
                        return false;
                    }
                }

                DbContext.Ado.BeginTran();

                // 更新当前站点明细状态
                currentDetail.Status = 1; // 已扫描状态
                currentDetail.ConfirmTime = now;
                currentDetail.EmployeeCode = userName;
                currentDetail.EmployeeName = userName;
                currentDetail.MUser = userName;
                currentDetail.MTime = now;

                DbContext.Updateable(currentDetail).ExecuteCommand();

                // 判断是否为最后一个站点，如果是则更新主表状态为已完成
                var lastDetail = allDetails.OrderByDescending(d => d.StationSort).FirstOrDefault();
                if (lastDetail != null && lastDetail.StationCode == stationCode)
                {
                    report.IsCompleted = true;
                    report.MUser = userName;
                    report.MTime = now;
                    
                    DbContext.Updateable(report).ExecuteCommand();

                    // 最后一个站点扫描完成后，更新OMS中的报工完成时间
                    UpdateOmsReportCompleteTime(report);

                    // 最后一个站点扫描完成后，异步调用报工接口
                    CallDlvAufnrAsync(report);
                }

                DbContext.Ado.CommitTran();

                message = "站点扫描成功";
                return true;
            }
            catch (Exception ex)
            {
                if (DbContext.Ado.IsAnyTran())
                {
                    DbContext.Ado.RollbackTran();
                }
                message = "站点扫描失败：" + ex.Message;
                return false;
            }
        }

        /// <summary>
        /// 更新OMS报工完成时间
        /// </summary>
        /// <param name="report">报工记录</param>
        private void UpdateOmsReportCompleteTime(Produce_Report report)
        {
            try
            {
                if (string.IsNullOrEmpty(report.ProduceSchedulingId))
                {
                    return;
                }

                // 查询排产信息，获取OrderId
                var scheduling = _shedulingApp.GetEntityByKey(report.ProduceSchedulingId);
                if (scheduling == null || string.IsNullOrEmpty(scheduling.OrderId))
                {
                    return;
                }

                // 使用OMS数据库连接更新SD_Host_OrderDetails表
                DbContextForOMS.Updateable<AOS.OMS.Entity.Sale.SD_Host_OrderDetails>()
                    .SetColumns(t => new AOS.OMS.Entity.Sale.SD_Host_OrderDetails
                    {
                        ReportCompleteTime = DateTime.Now
                    })
                    .Where(t => t.Id == scheduling.OrderId)
                    .ExecuteCommand();
            }
            catch (Exception ex)
            {
                // 记录错误但不中断主流程，因为这是一个附加功能
                Console.WriteLine($"更新OMS报工完成时间失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 站点扫描
        /// </summary>
        /// <param name="req">扫描请求</param>
        /// <param name="userName">用户名</param>
        /// <param name="message">返回消息</param>
        /// <returns>处理结果</returns>
        public bool ScanStation(Produce_StationScanReq req, string userName, out string message)
        {
            if (req == null)
            {
                message = "请求参数不能为空";
                return false;
            }
            
            return ScanStation(req.StationCode, req.ProduceOrderNo, userName, out message);
        }

        #endregion

        #region 获取当前用户关联的站点列表

        /// <summary>
        /// 获取当前用户关联的站点列表
        /// </summary>
        /// <param name="currentUser">当前用户</param>
        /// <returns>用户关联的站点列表</returns>
        public List<Produce_UserStationRes> GetSelfStationList(Sys_User currentUser)
        {
            if (currentUser == null)
            {
                return new List<Produce_UserStationRes>();
            }

            // 查询用户关联的站点ID列表
            var userRelationList = DbContext.Queryable<MD_ReportStationUserRelation>()
                .Where(t => t.UserId == currentUser.UserID && !t.IsDelete)
                .ToList();
            
            if (userRelationList == null || userRelationList.Count == 0)
            {
                return new List<Produce_UserStationRes>();
            }

            // 获取站点ID列表
            var stationIds = userRelationList.Select(t => t.StationId).ToList();
            
            // 查询站点详细信息
            var stationList = DbContext.Queryable<MD_ReportStation>()
                .Where(t => stationIds.Contains(t.Id) && !t.IsDelete)
                .OrderBy(t => t.StationSort)
                .ToList();

            // 构建返回结果，包含站点信息和关联的工作中心
            var result = stationList.Select(station => new Produce_UserStationRes
            {
                StationId = station.Id,
                WorkCenterCode = station.WorkCenterCode,
                WorkCenterName = station.WorkCenterName,
                StationCode = station.StationCode,
                StationName = station.StationName,
                StationSort = station.StationSort,
                ProcessNo = station.ProcessNo,
                ProcessShortText = station.ProcessShortText,
                Enable = station.Enable
            }).ToList();

            return result;
        }

        #endregion

        #region 报工接口调用

        /// <summary>
        /// 异步调用DlvAufnr报工接口
        /// </summary>
        /// <param name="report">生产报工信息</param>
        private void CallDlvAufnrAsync(Produce_Report report)
        {
            Task.Run(() =>
            {
                try
                {
                    // 构造DlvAufnr请求数据
                    var dlvAufnrRequest = new DlvAufnr
                    {
                        DataIn = new DlvAufnr.Table001079[]
                        {
                            new DlvAufnr.Table001079
                            {
                                AUFNR = report.ProduceOrderNo
                            }
                        }
                    };

                    // 获取SAP接口配置
                    string sapUrl = ConfigurationManager.AppSettings["SapUrl"];
                    string dlvAufnrEndpoint = ConfigurationManager.AppSettings["DlvAufnr"];
                    string token = ConfigurationManager.AppSettings["SapToken"];

                    if (string.IsNullOrEmpty(sapUrl) || string.IsNullOrEmpty(dlvAufnrEndpoint))
                    {
                        UpdateReportPostStatus(report.Id, false, "DlvAufnr接口配置不完整，无法调用报工接口", "SYSTEM");
                        Console.WriteLine("DlvAufnr接口配置不完整，无法调用报工接口");
                        return;
                    }

                    string requestUrl = sapUrl + dlvAufnrEndpoint + "?token=" + token;
                    string requestBody = JsonConvert.SerializeObject(dlvAufnrRequest.DataIn);

                    // 异步调用DlvAufnr接口
                    string response = HttpUtil.HttpPost(requestUrl, requestBody, "POST");

                    // 解析响应结果
                    var dlvAufnrResponse = JsonConvert.DeserializeObject<DlvAufnr>(response);

                    if (dlvAufnrResponse != null)
                    {
                        if (dlvAufnrResponse.ETYPE == "S")
                        {
                            // 接口调用成功，更新过账状态
                            UpdateReportPostStatus(report.Id, true, "DlvAufnr报工接口调用成功", "SYSTEM");
                            Console.WriteLine($"DlvAufnr报工接口调用成功，工单号：{report.ProduceOrderNo}");
                        }
                        else
                        {
                            // 接口调用失败，记录错误信息
                            string errorMessage = $"DlvAufnr报工接口调用失败：{dlvAufnrResponse.EMSG}";
                            UpdateReportPostStatus(report.Id, false, errorMessage, "SYSTEM");
                            Console.WriteLine($"DlvAufnr报工接口调用失败，工单号：{report.ProduceOrderNo}，错误信息：{dlvAufnrResponse.EMSG}");
                        }
                    }
                    else
                    {
                        // 响应解析失败
                        string errorMessage = "DlvAufnr报工接口响应解析失败";
                        UpdateReportPostStatus(report.Id, false, errorMessage, "SYSTEM");
                        Console.WriteLine($"DlvAufnr报工接口响应解析失败，工单号：{report.ProduceOrderNo}");
                    }
                }
                catch (Exception ex)
                {
                    // 记录错误但不中断主流程，因为这是一个异步附加功能
                    string errorMessage = $"DlvAufnr报工接口异步调用异常：{ex.Message}";
                    UpdateReportPostStatus(report.Id, false, errorMessage, "SYSTEM");
                    Console.WriteLine($"DlvAufnr报工接口异步调用失败，工单号：{report.ProduceOrderNo}，错误信息：{ex.Message}");
                }
            });
        }

        /// <summary>
        /// 更新报工记录的过账状态
        /// </summary>
        /// <param name="reportId">报工记录ID</param>
        /// <param name="isSuccess">是否成功</param>
        /// <param name="message">消息</param>
        /// <param name="postUser">过账用户</param>
        private void UpdateReportPostStatus(string reportId, bool isSuccess, string message, string postUser)
        {
            try
            {
                var report = GetEntityByKey(reportId);
                if (report != null)
                {
                    if (isSuccess)
                    {
                        // 成功时更新过账状态
                        report.IsPosted = true;
                        report.PostTime = DateTime.Now;
                        report.PostUser = postUser;
                        report.PostMessage = message;
                    }
                    else
                    {
                        // 失败时只记录错误消息
                        report.IsPosted = false;
                        report.PostMessage = message;
                    }

                    report.MUser = postUser;
                    report.MTime = DateTime.Now;

                    // 更新数据库
                    Update(report);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"更新报工过账状态失败，报工ID：{reportId}，错误信息：{ex.Message}");
            }
        }

        #endregion
        
    }
}
