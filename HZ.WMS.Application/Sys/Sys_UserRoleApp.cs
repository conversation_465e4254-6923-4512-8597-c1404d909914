using SqlSugar;
using System;
using System.Collections.Generic;

using HZ.Core.Security;
using HZ.WMS.Entity.Sys;
using HZ.Core.Http;

namespace HZ.WMS.Application.Sys
{
    /// <summary>
    /// 没有out_message消息的函数，意味着只在最前端做未处理异常捕捉
    /// </summary>

    public class Sys_UserRoleApp : BaseApp<Sys_UserRole>
    {
		#region 默认构造函数
        
        /// <summary>
        /// 默认构造函数
        /// </summary>
        public Sys_UserRoleApp() : base()
        {
        }

        
        

        #endregion

        #region 重置用户角色

        /// <summary>
        /// 重置用户角色
        /// </summary>
        /// <param name="userid">用户ID</param>
        /// <param name="roleids">角色ID[多角色]</param>
        /// <param name="operateUser">操作者</param>
        /// <returns>true：重置成功</returns>

        public bool ResetUserRoles(string userid, string roleids,string operateUser)
        {
            List<Sys_UserRole> list = new List<Sys_UserRole>();
            base.DbContext.Ado.BeginTran();
            base.HardDelete(t => t.UserID ==userid);
            string[] roles = roleids.Split('|'); 
            foreach(string role in roles)
            {
                Sys_UserRole ur = new Sys_UserRole();
                ur.UserID = userid;
                ur.RoleID = role;
                ur.CUser = operateUser;
                ur.MUser = operateUser;
                list.Add(ur);
            }
            base.Insert(list);
            base.DbContext.Ado.CommitTran();
            return true;
        }

        #endregion 

    }
}

