<?xml version="1.0" encoding="utf-8"?>
<!--
  有关如何配置 ASP.NET 应用程序的详细信息，请访问
  https://go.microsoft.com/fwlink/?LinkId=301879
  -->
<configuration>
  <configSections>
    <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler,log4net" />
    <section name="oracle.manageddataaccess.client" type="OracleInternal.Common.ODPMSectionHandler, Oracle.ManagedDataAccess, Version=**********, Culture=neutral, PublicKeyToken=89b483f429c47342" />
  </configSections>
  <!-- <configSections> -->
  <!--   <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler,log4net" /> -->
  <!-- </configSections> -->
  <appSettings>
    <add key="webpages:Version" value="*******" />
    <add key="webpages:Enabled" value="false" />
    <add key="ClientValidationEnabled" value="true" />
    <add key="UnobtrusiveJavaScriptEnabled" value="true" />
    <add key="log4net.Internal.Debug" value="false" />
    <!--Excel导出设置-->
    <add key="ExportMaxRowsLimit" value="300000" />
    <add key="ExportExcelType" value="xls" />
    <add key="Lic" value="RyKlJelRYxEF+M9yw7/qqmch4X5ize4eRL/jrACu3p6v/QCHF8oX5ti16GXsAmQOcDzNTrDM9TksYFj/z/KtjQ==" />
    <!--SAP接口 URL-->
    <!-- 测试服 -->
    <add key="SapUrl" value="http://**********:9000/api/sap" />
    <!-- 正式服 -->
    <!-- <add key="SapUrl" value="http://**********:9001/api/sap" /> -->
    <add key="SapToken" value="gFBzZCr6tcexwWu1hM2PDSKqGHnCF3zK" />
    <add key="GetSaleBom" value="/GetSaleBomToPS" />
    <!--获取物料生产版本 拿线体用-->
    <add key="GetMatnrVersion" value="/GetMatnrVersion" />
    <!--创建生产订单-->
    <add key="CreateProdOrder" value="/CreateProdOrder" />
    <!--预排产创建采购申请-->
    <add key="CreatePruchaseReq" value="/CreatePruchaseReq" />
    <!--创建计划订单-->
    <add key="CreatePlanOrder" value="/CreatePlanOrder" />
    <!--更新计划订单-->
    <add key="EditPlanOrder" value="/EditPlanOrder" />
    <!--创建采购订单-->
    <add key="CreatePruchaseOrder" value="/CreatePruchaseOrder" />
    <!--获取工单组件-->
    <add key="GetAufnrComp" value="/GetAufnrComp" />
    <!--获取物料价格-->
    <add key="CheckMatnrPrice" value="/CheckMatnrPrice" />
    <!--报工-->
    <add key="DlvAufnr" value="/DlvAufnr" />
    <!--修改生产订单-->
    <add key="EditAufnr" value="/EditAufnr" />
    <!--打印设置-->
    <!--<add key="PrintSite" value="https://wmstest3.faradynemotors.cn:9527/FLDAPI/"/>-->
    <add key="LabelTemplateUploadPath" value="UploadFiles\LabelTemplate\" />
    <add key="PP_OverReceiveUploadPath" value="UploadFiles\PP_OverReceive\" />
    <add key="PP_ReturnScanApplicationUploadPath" value="UploadFiles\PP_ReturnScanApplication\" />
    <add key="PrintPath" value="PrintTempPath\" />
    <!--FLDAPI-->
    <!--<add key="ApiVisualPathName" value="FLDAPI" />-->
    <add key="ApiVisualPathName" value="AOS.WMS.WebAPI" />
    <!--定时任务-->
    <add key="AutoRunJob" value="true" />
    <!--抓取多长时间内有更新的数据 单位：分钟-->
    <add key="CrawOrderJobTimeBefore" value="600" />
    <!--SAP接口账户设置-->
    <!--<add key="SapInterfaceUser" value="_WMS0526" />
    <add key="SapInterfacePassword" value="Wm$Sim0526" />-->
    <!--正式版地址-->
    <add key="SapInterfaceUser" value="_WMS0622" />
    <add key="SapInterfacePassword" value="Wm$482944" />
    <!--SAP库存查询服务url-->
    <!--https://my600071.sapbyd.cn/sap/byd/odata/scm_inboundlogistics_analytics.svc/RPSCMINVV02_Q0001QueryResults?$select=CLOG_AREA_UUID,CMATERIAL_UUID,T1MATERIAL_UUIDsMATR_INT_ID,KCON_HAND_STOCK,CON_HAND_STOCK_UOM&$filter=(CSITE_UUID eq 'SZ1')&$top=1000000&$format=json-->
    <add key="StockQueryUrl" value="https://my600071.sapbyd.cn/sap/byd/odata/scm_inboundlogistics_analytics.svc/RPSCMINVV02_Q0001QueryResults" />
    <add key="SaleOrderReferenceQueryUrl" value="https://my600071.sapbyd.cn/sap/byd/odata/cust/v1/sitelogisticstasks/SiteLogisticsTaskCollection" />
    <add key="StockQueryUser" value="QWMS0526" />
    <add key="StockQueryPassword" value="Huazhi0526" />
    <!--ALL | CONFIG | NONE-->
    <add key="LogType" value="CONFIG" />
    <add key="SupplierNotifyMessageTypeIDList" value="7EFAF0A8-25EB-4C7F-A254-9CEFD5AF832E,7KFAF0A8-25EB-4C7F-A254-9CEFD5AF832E" />
    <!--跨域访问设置-->
    <add key="cors_allowOrigins" value="*" />
    <add key="cors_allowHeaders" value="*" />
    <add key="cors_allowMethods" value="*" />

    <!-- 数据库连接池配置 - 优化以解决连接池超时问题 -->
    <add key="ConnectionPool.MinSize" value="10" />
    <add key="ConnectionPool.MaxSize" value="200" />
    <add key="ConnectionPool.ConnectionTimeout" value="60" />
    <add key="ConnectionPool.CommandTimeout" value="600" />
    <add key="ConnectionPool.RetryCount" value="5" />
    <add key="ConnectionPool.RetryInterval" value="3" />
    <add key="ConnectionPool.ConnectionLifetime" value="600" />
    <add key="ConnectionPool.IdleTimeout" value="300" />
    <!-- 新增连接池优化参数 -->
    <add key="ConnectionPool.PoolBlockingPeriod" value="Auto" />
    <add key="ConnectionPool.LoadBalanceTimeout" value="0" />
    <add key="ConnectionPool.MultipleActiveResultSets" value="false" />
    <add key="ConnectionPool.Enlist" value="true" />
    <add key="ConnectionPool.ApplicationIntent" value="ReadWrite" />
    <add key="ConnectionPool.ConnectRetryCount" value="3" />
    <add key="ConnectionPool.ConnectRetryInterval" value="10" />
    <!-- 连接池清理配置 -->
    <add key="ConnectionPool.CleanupInterval" value="300000" />
    <add key="ConnectionPool.HealthCheckInterval" value="60000" />
    <add key="ConnectionPool.MaxConnectionTime" value="5000" />

    <!-- 连接池监控配置 -->
    <add key="ConnectionPool.HealthCheck.Enabled" value="true" />
    <add key="ConnectionPool.HealthCheck.Interval" value="300" />
    <add key="ConnectionPool.WarningThreshold" value="80" />
    <add key="ConnectionPool.CleanupInterval" value="5" />
  </appSettings>
  <!--
    有关 web.config 更改的说明，请参见 http://go.microsoft.com/fwlink/?LinkId=235367。

    可在 <httpRuntime> 标记上设置以下特性。
      <system.Web>
        <httpRuntime targetFramework="4.6" />
      </system.Web>
  -->
  <system.web>
    <compilation debug="true" targetFramework="4.6" />
    <httpRuntime />
    <pages controlRenderingCompatibilityVersion="4.0" />
  </system.web>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Antlr3.Runtime" publicKeyToken="eb42632606e9261f" />
        <bindingRedirect oldVersion="0.0.0.0-3.5.0.2" newVersion="3.5.0.2" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" culture="neutral" publicKeyToken="30ad4fe6b2a6aeed" />
        <bindingRedirect oldVersion="0.0.0.0-12.0.0.0" newVersion="12.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Optimization" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-1.1.0.0" newVersion="1.1.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-1.6.5135.21930" newVersion="1.6.5135.21930" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Helpers" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.WebPages" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-5.2.4.0" newVersion="5.2.4.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Http" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.2.7.0" newVersion="5.2.7.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Net.Http.Formatting" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.2.7.0" newVersion="5.2.7.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="BouncyCastle.Crypto" publicKeyToken="0e99375e54769942" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Chloe" publicKeyToken="null" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="ICSharpCode.SharpZipLib" publicKeyToken="1b03e6acf1164f73" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="NPOI" publicKeyToken="0df73ec7942b34e1" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="NPOI.OpenXml4Net" publicKeyToken="0df73ec7942b34e1" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="NPOI.OpenXmlFormats" publicKeyToken="0df73ec7942b34e1" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Oracle.ManagedDataAccess" publicKeyToken="89b483f429c47342" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-**********" newVersion="**********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.4.0" newVersion="4.0.4.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.2.0" newVersion="4.0.2.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Numerics.Vectors" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.1.5.0" newVersion="4.1.5.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.1.0" newVersion="6.0.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Text.Json" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.10" newVersion="6.0.0.10" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <system.webServer>
    <modules runAllManagedModulesForAllRequests="true">
      <remove name="WebDAVModule" />
    </modules>
    <handlers>
      <remove name="ExtensionlessUrlHandler-Integrated-4.0" />
      <remove name="OPTIONSVerbHandler" />
      <remove name="TRACEVerbHandler" />
      <add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="*" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0" />
    </handlers>
    <staticContent>
      <mimeMap fileExtension=".apk" mimeType="application/vnd.andriod" />
    </staticContent>
  </system.webServer>
  <connectionStrings>
    <!--94服务器连接地址 - 优化连接池配置-->
    <add name="DbConnection" connectionString="04ytv6KDr57/SNNAGEbRNO5xb6X2tpzGQaBjpK22g+IX3VBomDhzQY5AjfWnCJRdnb89p8gVdRnEyicNr7+lmHcM7uFmXU3P" />
    <add name="DbConnectionForSAP" connectionString="04ytv6KDr57/SNNAGEbRNO5xb6X2tpzGH0HwimsZ8tyzNS1R8DmngrInEX9/5zxDMkh9ZCvUIh6AHbHIX8PVVw5WwkftzqhT" />
    <add name="DbConnectionForSRM" connectionString="04ytv6KDr57/SNNAGEbRNO5xb6X2tpzG8RETwxplaN/McP1fZKnDyRRRxf4D05ZiLpJUV2+8hQMv+5NdzwEU2mx01MvNSSRu" />
    <add name="DbConnectionForEAP" connectionString="04ytv6KDr56gA7rt2zL8I/lSoHG0cWe0pInfB+S0rFmhsb1DLrIrzKuWBjIKkgv+obB5z6XCZWZlv+FWMG4GAA==" />
    <add name="DbConnectionForOMS" connectionString="04ytv6KDr57/SNNAGEbRNO5xb6X2tpzG9FuvDZ8IcQA4arUZpyPmLUc1X43V7ij2y+GSvuUIdFcOUZ8o4mS/jHJ6aVBP98sQ" />
    <!--客户测试机 -->
    <!-- <add name="DbConnection" connectionString="04ytv6KDr56gA7rt2zL8I5RAhKz8np4YRsSaQFxike00h09Yaro4z6KR85jel7+9MvcKzbGDyQeelAzvgLOHAw==" /> -->
    <!-- <add name="DbConnectionForSAP" connectionString="04ytv6KDr56gA7rt2zL8I5RAhKz8np4YiZ8oYm1kBCeuoP7MgydhYI+7Pxd6gzxy8zxZ8TCOj0N2YNkwiaXgEA==" /> -->
    <!-- <add name="DbConnectionForOMS" connectionString="04ytv6KDr56gA7rt2zL8I5RAhKz8np4YbCqI2abaeWms2R2ytgb3NPiv3PWwJFFy04CJXrd5vTZABF4O0iJ7LA==" /> -->
    <!-- <add name="DbConnectionForEAP" connectionString="04ytv6KDr56gA7rt2zL8I/lSoHG0cWe0pInfB+S0rFmhsb1DLrIrzKuWBjIKkgv+obB5z6XCZWZlv+FWMG4GAA==" /> -->
    <!-- <add name="DbConnectionForSRM" connectionString="04ytv6KDr56gA7rt2zL8I5RAhKz8np4Y8mqSnTIEZphsQ/aHDWH/ys54ghoQw6nDfU3oMDDiUvgwMowikUw15g==" /> -->
    <!-- Oracle数据库连接配置 -->
    <add name="DbConnectionForOracle" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=orcl)));User Id=fwd;Password=*********;" />
  </connectionStrings>
  <system.net>
    <mailSettings>
      <smtp from="">
        <network host="" />
      </smtp>
    </mailSettings>
  </system.net>
  <system.data>
    <DbProviderFactories>
      <remove invariant="Oracle.ManagedDataAccess.Client" />
      <add name="ODP.NET, Managed Driver" invariant="Oracle.ManagedDataAccess.Client" description="Oracle Data Provider for .NET, Managed Driver" type="Oracle.ManagedDataAccess.Client.OracleClientFactory, Oracle.ManagedDataAccess, Version=**********, Culture=neutral, PublicKeyToken=89b483f429c47342" />
    </DbProviderFactories>
  </system.data>
</configuration>