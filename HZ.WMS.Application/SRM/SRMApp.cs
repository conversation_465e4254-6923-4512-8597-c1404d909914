using SqlSugar;
using HZ.WMS.Entity.SRM.ViewModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Application.SRM
{
    /// <summary>
    ///SRM方法层
    /// </summary>
    public class SRMApp : ContentBase
    {
        #region 供应商主数据

        #region 查询供应商主数据

        /// <summary>
        /// 查询所有供应商主数据
        /// </summary>
        /// <returns></returns>
        public ISugarQueryable<SRM_S_SupplierInfo_View> GetSRM_SupplierInfo()
        {
            var query = DbContext.Queryable<SRM_S_SupplierInfo_View>();
            return query;
        }

        #endregion

        #endregion
    }
}
