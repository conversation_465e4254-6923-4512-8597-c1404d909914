using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.PP.ViewModel
{
    /// <summary>
    /// 生产订单明细表
    /// </summary>
    public class PP_ProductionOrderDetail_View
    {
        /// <summary>
        /// 生产订单
        /// </summary>
        public string ProductionOrderNo { get; set; }
        /// <summary>
        /// 预留编号
        /// </summary>
        public int? ReservedNo { get; set; }
        /// <summary>
        /// 预留行号
        /// </summary>
        public decimal ComponentLineNo { get; set; }
        /// <summary>
        /// 组件编码
        /// </summary>
        public string ComponentCode { get; set; }
        /// <summary>
        /// 物料描述
        /// </summary>
        public string MaterialName { get; set; }
        /// <summary>
        /// 需求数量
        /// </summary>
        public decimal DemandQty { get; set; }
        /// <summary>
        /// 需求日期
        /// </summary>
        public DateTime? DemandDate { get; set; }
        /// <summary>
        /// 组件单位
        /// </summary>
        public string ComponentUnit { get; set; }
        /// <summary>
        /// 发料库存地，线边库
        /// </summary>
        public string DeliverLocation { get; set; }
        /// <summary>
        /// 反冲标识
        /// </summary>
        public string IsBackflush { get; set; }
        /// <summary>
        /// 特殊库存标识
        /// </summary>
        public string SpecialInventory { get; set; }
        /// <summary>
        /// 销售订单
        /// </summary>
        public string SalesOrderNo { get; set; }
        /// <summary>
        /// 销售订单行项目
        /// </summary>
        public int? SalesOrderLineNo { get; set; }
        /// <summary>
        /// 移动类型
        /// </summary>
        public string MovementType { get; set; }
        /// <summary>
        /// 删除标识,L代表已删除，S代表锁定，为空代表正常
        /// </summary>
        public string IsDeleteSAP { get; set; }
        /// <summary>
        /// 物料组
        /// </summary>
        public string MaterialGroupCode { get; set; }
        /// <summary>
        /// 物料组描述
        /// </summary>
        public string MaterialGroupDes { get; set; }
        /// <summary>
        /// 虚拟项目标识
        /// </summary>
        public string IsVirtual { get; set; }
    }
}
