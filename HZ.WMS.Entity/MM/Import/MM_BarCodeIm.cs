using System.ComponentModel;
//Chloe框架


namespace HZ.WMS.Entity.MM
{
    /// <summary>
    /// 物料标签
    /// </summary>
    public class MM_BarCodeIm 
    {


        /// <summary> 
        /// 物料编号
        /// </summary> 
        [Description("产品件号")]
        public string ItemCode { get; set; }

       
        /// <summary> 
        /// 数量
        /// </summary> 
        [Description("数量")]
        public decimal? Qty { get; set; }

        /// <summary> 
        /// 库存单位
        /// </summary> 
        [Description("单位")]
        public string Unit { get; set; }


        /// <summary> 
        /// 仓库
        /// </summary> 
        [Description("仓库")]
        public string PrintTemplate { get; set; }

        /// <summary> 
        /// 库位
        /// </summary> 
        [Description("库位")]
        public string Remark { get; set; }

    }
}


