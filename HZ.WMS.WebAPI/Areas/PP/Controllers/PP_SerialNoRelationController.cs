using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;
using HZ.Core.Http;
using HZ.Core.Office;
using HZ.WMS.Application;
using HZ.WMS.Application.PP;
using HZ.WMS.Entity.PP;
using HZ.WMS.Entity.PP.Dto;
using HZ.WMS.Entity.PP.ViewModel;
using HZ.WMS.Entity.Sys;
using HZ.WMS.WebAPI.Controllers;

namespace HZ.WMS.WebAPI.Areas.PP.Controllers
{
    /// <summary>
    /// 生产序列号关联
    /// </summary>
    public class PP_SerialNoRelationController : ApiBaseController
    {
        private PP_SerialNoRelationApp _app = new PP_SerialNoRelationApp();
        private PP_SerialNoRelationDetailApp detailApp = new PP_SerialNoRelationDetailApp();
        private PP_ProductionOrderApp orderApp = new PP_ProductionOrderApp();
        private PP_ProductionReportApp reportApp = new PP_ProductionReportApp();

        #region 查询

        /// <summary>
        /// 获取主表分页列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]string keyword, [FromUri]string ProductionLineDes, [FromUri]string ProductionScheduler, [FromUri] DateTime[] dateValue)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];

                var itemsData = _app.GetPageList(page,
                    t => (string.IsNullOrEmpty(keyword)
                        || t.SerialNo.Contains(keyword)
                        || t.DocNum.Contains(keyword)
                        || t.ProductionOrderNo.Contains(keyword)
                        || t.MaterialNo.Contains(keyword)
                        || t.MaterialName.Contains(keyword)
                        || t.ContractNo.Contains(keyword)
                        || t.SalesOrderNo.Contains(keyword)
                        || t.Shippers.Contains(keyword))
                        && (string.IsNullOrEmpty(ProductionLineDes) || t.ProductionLineCode.Contains(ProductionLineDes) || t.ProductionLineDes.Contains(ProductionLineDes))
                        && (string.IsNullOrEmpty(ProductionScheduler) || t.ProductionScheduler.Contains(ProductionScheduler))
                        && (t.StartTime >= fromTime && t.StartTime < toTime)
                        )
                    .ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 查询子表分页列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetDetailPageList([FromUri]Pagination page, [FromUri]string DocNum)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = detailApp.GetPageList(page, w => DocNum == w.DocNum).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 查询子表列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetDetailList([FromUri]string DocNum)
        {
            var result = new ResponseData();
            try
            {
                result.Data = detailApp.GetList(w => DocNum == w.DocNum).ToList();
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取系统生成的单号

        /// <summary>
        /// 获取系统生成的单号
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetDocNum()
        {
            var result = new ResponseData();
            try
            {
                result.Data = base.GenerateDocNum(DocType.PP, DocFixedNumDef.PP_SerialNoRelation);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 根据序列号查询生产订单

        [HttpGet]
        public IHttpActionResult GetOrderBySerialNo([FromUri]string serialNo)
        {
            var result = new ResponseData();
            try
            {
                var order = orderApp.GetFirstEntity(w => w.SerialNo == serialNo);
                var material = new List<PP_FeedingDetail_View>();
                if (order != null)
                {
                    //主表订单数量
                    var parent = orderApp.GetSapOrderList(w => w.ProductionOrderNo == order.ProductionOrderNo).ToList().FirstOrDefault();
                    //material = detailApp.GetSapOrderDetailList(
                    //    w => w.ProductionOrderNo == order.ProductionOrderNo
                    //    && w.IsBackflush.ToUpper() != "X").ToList();
                    //if (order.OrderQty < parent?.OrderQty)
                    //{
                    //    foreach (var item in material)
                    //    {
                    //        item.DemandQty = item.DemandQty / parent.OrderQty * order.OrderQty;//=>子表需求数量/主表订单数量*主表实际投料数量
                    //    }
                    //}
                }
                result.Data = new { Order = order, Materials = material };
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 查询物料

        /// <summary>
        /// 投料时，组件扫序列号，原材料扫物料号 
        /// </summary>
        /// <param name="productionOrderNo"></param>
        /// <param name="componentCode">物料号</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetMaterial([FromUri]string productionOrderNo, [FromUri]string componentCode)
        {
            var result = new ResponseData();
            try
            {



                //根据序列号查组件
                //var MaterialNo = orderApp.GetFirstEntity(w => w.SerialNo == componentCode)?.MaterialNo;
                //if(string.IsNullOrEmpty(MaterialNo))
                //{
                //    MaterialNo = componentCode;//如果序列号查不到，则为物料号
                //}
                //物料号解析
                string MaterialNo = componentCode;

                var item = detailApp.GetSapOrderDetailList(
                        w => w.ProductionOrderNo == productionOrderNo
                        && w.ComponentCode == MaterialNo)
                        .ToList().FirstOrDefault();
                result.Data = item;

                //if(item?.IsBackflush.ToUpper() == "X")
                //{
                //    result.Code = (int)WMSStatusCode.Failed;
                //    result.Message = "倒冲料不允许投料！";
                //}
                //else
                //{
                //    result.Data = item;
                //}
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 根据订单查询物料

        /// <summary>
        /// 根据订单查询物料
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetMaterialByOrder([FromUri]string OrderNo)
        {
            var result = new ResponseData();
            try
            {
                result.Data = detailApp.GetSapOrderDetailList(
                    w => w.ProductionOrderNo == OrderNo
                    && w.IsBackflush.ToUpper() != "X").ToList(); 
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 增加



        [HttpPost]
        public IHttpActionResult Add([FromBody]PP_SerialNoRelation_Dto productionFeeding)
        {
            var result = new ResponseData();
            try
            {


                Sys_User user = GetCurrentUser();
                var feeding = new PP_SerialNoRelation
                {
                    ScanningCode = productionFeeding.ScanningCode,
                    SerialNo = productionFeeding.SerialNo,
                    DocNum = productionFeeding.DocNum,
                    ProductionOrderNo = productionFeeding.ProductionOrderNo,
                    FactoryCode = productionFeeding.FactoryCode,
                    MaterialNo = productionFeeding.MaterialNo,
                    MaterialName = productionFeeding.MaterialName,
                    OrderType = productionFeeding.OrderType,
                    OrderQty = productionFeeding.OrderQty,
                    Unit = productionFeeding.Unit,
                    ContractNo = productionFeeding.ContractNo,
                    SalesOrderNo = productionFeeding.SalesOrderNo,
                    Shippers = productionFeeding.Shippers,
                    ProductionLineCode = productionFeeding.ProductionLineCode,
                    ProductionLineDes = productionFeeding.ProductionLineDes,
                    ProductionScheduler = productionFeeding.ProductionScheduler,
                    DeliveryTime = productionFeeding.DeliveryTime,
                    StartTime = productionFeeding.StartTime,
                    Remark = productionFeeding.Remark,
                    CUser = user.LoginAccount,
                };
                var detailList = productionFeeding.DetailList;
                detailList.ForEach(item =>
                {
                    item.DocNum = productionFeeding.DocNum;
                    item.SerialNo = productionFeeding.SerialNo;
                    item.MianMaterialNo = productionFeeding.MaterialNo;
                    item.MianMaterialName = productionFeeding.MaterialName;
                    item.CUser = user.LoginAccount;
                });

                if (!_app.Add(feeding, detailList))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 更新

        [HttpPost]
        public IHttpActionResult Update([FromBody]PP_SerialNoRelation_Dto productionFeeding)
        {
            var result = new ResponseData();
            try
            {
        
                Sys_User user = GetCurrentUser();
                if (!_app.Edit(productionFeeding, user.LoginAccount))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                }else
                    result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除

        [HttpDelete]
        public IHttpActionResult Delete([FromBody]string[] DocNums)
        {
            var result = new ResponseData();
            try
            {
                Sys_User user = GetCurrentUser();
                var list = _app.GetList(t => DocNums.Contains(t.DocNum)).ToList();
        
                    var detailList = detailApp.GetList(w => DocNums.Contains(w.DocNum)).ToList();
                    _app.Delete(user.LoginAccount, list, detailList);
                
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion


        #region 导出

        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri]Pagination page, [FromUri]string keyword, [FromUri]string ProductionLineDes, [FromUri]string ProductionScheduler, [FromUri] DateTime[] dateValue)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];

                var list = _app.GetList(
                    t => (string.IsNullOrEmpty(keyword)
                        || t.SerialNo.Contains(keyword)
                        || t.DocNum.Contains(keyword)
                        || t.ProductionOrderNo.Contains(keyword)
                        || t.MaterialNo.Contains(keyword)
                        || t.MaterialName.Contains(keyword)
                        || t.ContractNo.Contains(keyword)
                        || t.SalesOrderNo.Contains(keyword)
                        || t.Shippers.Contains(keyword))
                        && (string.IsNullOrEmpty(ProductionLineDes) || t.ProductionLineCode.Contains(ProductionLineDes) || t.ProductionLineDes.Contains(ProductionLineDes))
                        && (string.IsNullOrEmpty(ProductionScheduler) || t.ProductionScheduler.Contains(ProductionScheduler))
                        && (t.StartTime >= fromTime && t.StartTime < toTime)
                      )
                    .Select(s => s.DocNum)
                    .ToList();

                var itemsData = detailApp.GetList(t => list.Contains(t.DocNum)).ToList();

                List<ExcelColumn<PP_SerialNoRelationDetail>> columns = ExcelService.FetchDefaultColumnList<PP_SerialNoRelationDetail>();
                string[] ignoreField = new string[]
                {
                    "ID","Line"
                    ,"FeedingLineNo"
                    ,"AssessmentCategory"
                    ,"AssessmentType"
                    ,"SpecialInventory"
                    ,"IsBackflush"
                    ,"IsDelete"
                    ,"MUser"
                    ,"MTime"
                    ,"DUser"
                    ,"DTime"
                };
                List<ExcelColumn<PP_SerialNoRelationDetail>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<PP_SerialNoRelationDetail> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                //columns.ForEach((column) =>
                //{
                //    if (column.ColumnName == "IsPosted")
                //    {
                //        column.Formattor = ExcelExportFormatter.IsOrNoFormatter;
                //    }
                //});

                return ExportToExcelFile<PP_SerialNoRelationDetail>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

      
    }
}
