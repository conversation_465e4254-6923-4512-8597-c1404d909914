using System;
using System.ComponentModel;

namespace HZ.WMS.Entity.PP.Import
{
    /// <summary>
    /// 唛头打印导入
    /// </summary>
    public class PP_ShippingMarkImport
    {
        /// <summary>
        /// 打印状态
        /// </summary>
        [Description("打印状态")]
        public string ShippingMarkPrintStatus { get; set; }

        /// <summary>
        /// 打印系统
        /// </summary>
        [Description("打印系统")]
        public string PrintSystem { get; set; }

        /// <summary>
        /// 纸张大小
        /// </summary>
        [Description("纸张大小")]
        public string PaperSize { get; set; }

        /// <summary>
        /// 纸张类别
        /// </summary>
        [Description("纸张类别")]
        public string PaperType { get; set; }

        /// <summary>
        /// SAP生产订单号
        /// </summary>
        [Description("SAP生产订单号")]
        public string SapProduceNo { get; set; }

        /// <summary>
        /// 出厂编号
        /// </summary>
        [Description("出厂编号")]
        public string FactoryNo { get; set; }

        /// <summary>
        /// 订单号
        /// </summary>
        [Description("订单号")]
        public string OrderNo { get; set; }

        /// <summary>
        /// 合同号
        /// </summary>
        [Description("合同号")]
        public string ContractNo { get; set; }

        /// <summary>
        /// 发货单位
        /// </summary>
        [Description("发货单位")]
        public string DeliveryCustomer { get; set; }

        /// <summary>
        /// 产品型号
        /// </summary>
        [Description("产品型号")]
        public string ProduceModel { get; set; }

        /// <summary>
        /// 产品件号
        /// </summary>
        [Description("产品件号")]
        public string ProducePart { get; set; }

        /// <summary>
        /// 装配线号
        /// </summary>
        [Description("装配线号")]
        public string AssemblyLine { get; set; }

        /// <summary>
        /// 装配日期
        /// </summary>
        [Description("装配日期")]
        public DateTime? AssemblyDate { get; set; }

        /// <summary>
        /// 额定载重
        /// </summary>
        [Description("额定载重")]
        public string RatedLoad { get; set; }

        /// <summary>
        /// 额定速度
        /// </summary>
        [Description("额定速度")]
        public string RatedSpeed { get; set; }

        /// <summary>
        /// 额定电压
        /// </summary>
        [Description("额定电压")]
        public string RatedVoltage { get; set; }

        /// <summary>
        /// 铭牌要求
        /// </summary>
        [Description("铭牌要求")]
        public string NameplateRequirements { get; set; }

        /// <summary>
        /// 曳引比
        /// </summary>
        [Description("曳引比")]
        public string TractionRatio { get; set; }

        /// <summary>
        /// 客户型号
        /// </summary>
        [Description("客户型号")]
        public string CustomerModel { get; set; }

        /// <summary>
        /// 箱板刷字
        /// </summary>
        [Description("箱板刷字")]
        public string BoxBoardBrushing { get; set; }

        /// <summary>
        /// 是否出口
        /// </summary>
        [Description("是否出口")]
        public string IsExport { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        [Description("项目名称")]
        public string ProjectName { get; set; }

        /// <summary>
        /// 接受编号
        /// </summary>
        [Description("接受编号")]
        public string AcceptNo { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量")]
        public string Quantity { get; set; }

        /// <summary>
        /// 制动器电压
        /// </summary>
        [Description("制动器电压")]
        public string BrakeVoltage { get; set; }

        /// <summary>
        /// SAP销售订单号
        /// </summary>
        [Description("SAP销售订单号")]
        public string SapSalesOrderNo { get; set; }

        /// <summary>
        /// SAP销售订单行号
        /// </summary>
        [Description("SAP销售订单行号")]
        public string SapSalesOrderLineNo { get; set; }

        /// <summary>
        /// 生产管理员
        /// </summary>
        [Description("生产管理员")]
        public string ProductionManager { get; set; }

        /// <summary>
        /// 装箱尺寸
        /// </summary>
        [Description("装箱尺寸")]
        public string PackingSize { get; set; }

        /// <summary>
        /// 装箱净重
        /// </summary>
        [Description("装箱净重")]
        public string PackingNetWeight { get; set; }

        /// <summary>
        /// 装箱毛重
        /// </summary>
        [Description("装箱毛重")]
        public string PackingGrossWeight { get; set; }

        /// <summary>
        /// 功率
        /// </summary>
        [Description("功率")]
        public string Power { get; set; }

        /// <summary>
        /// 节径
        /// </summary>
        [Description("节径")]
        public string PitchDiameter { get; set; }

        /// <summary>
        /// 绳槽
        /// </summary>
        [Description("绳槽")]
        public string RopeGroove { get; set; }

        /// <summary>
        /// 槽距
        /// </summary>
        [Description("槽距")]
        public string GrooveDistance { get; set; }

        /// <summary>
        /// 唛头打印时间
        /// </summary>
        [Description("唛头打印时间")]
        public DateTime? ShippingMarkPrintTime { get; set; }

        /// <summary>
        /// 清单打印状态
        /// </summary>
        [Description("清单打印状态")]
        public string ListPrintStatus { get; set; }

        /// <summary>
        /// 清单打印时间
        /// </summary>
        [Description("清单打印时间")]
        public DateTime? ListPrintTime { get; set; }

        /// <summary>
        /// 打印方向
        /// </summary>
        [Description("打印方向")]
        public string PrintDirection { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Description("备注")]
        public string Remark { get; set; }
    }
}
