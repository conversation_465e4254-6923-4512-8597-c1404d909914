using System;
using System.Data;
using System.Threading.Tasks;
using SqlSugar;
using HZ.Core.Logging;

namespace HZ.WMS.Application
{
    /// <summary>
    /// 安全的数据库执行器 - 解决连接状态问题
    /// </summary>
    public static class SafeDatabaseExecutor
    {
        /// <summary>
        /// 安全执行查询操作
        /// </summary>
        /// <typeparam name="T">返回类型</typeparam>
        /// <param name="dbKey">数据库连接键</param>
        /// <param name="operation">操作函数</param>
        /// <param name="maxRetries">最大重试次数</param>
        /// <returns></returns>
        public static T ExecuteWithRetry<T>(string dbKey, Func<SqlSugarClient, T> operation, int maxRetries = 3)
        {
            Exception lastException = null;
            
            for (int i = 0; i < maxRetries; i++)
            {
                try
                {
                    using (var client = GetFreshClient(dbKey))
                    {
                        // 确保连接状态正常
                        EnsureConnectionState(client);
                        return operation(client);
                    }
                }
                catch (Exception ex) when (IsConnectionRelatedError(ex))
                {
                    lastException = ex;
                    LogHelper.Instance.LogError($"数据库连接错误，第 {i + 1} 次重试: {ex.Message}");
                    
                    if (i < maxRetries - 1)
                    {
                        // 等待一段时间后重试
                        System.Threading.Thread.Sleep(1000 * (i + 1));
                    }
                }
            }
            
            throw new Exception($"数据库操作失败，已重试 {maxRetries} 次", lastException);
        }

        /// <summary>
        /// 安全执行异步查询操作
        /// </summary>
        /// <typeparam name="T">返回类型</typeparam>
        /// <param name="dbKey">数据库连接键</param>
        /// <param name="operation">异步操作函数</param>
        /// <param name="maxRetries">最大重试次数</param>
        /// <returns></returns>
        public static async Task<T> ExecuteWithRetryAsync<T>(string dbKey, Func<SqlSugarClient, Task<T>> operation, int maxRetries = 3)
        {
            Exception lastException = null;
            
            for (int i = 0; i < maxRetries; i++)
            {
                try
                {
                    using (var client = GetFreshClient(dbKey))
                    {
                        // 确保连接状态正常
                        EnsureConnectionState(client);
                        return await operation(client);
                    }
                }
                catch (Exception ex) when (IsConnectionRelatedError(ex))
                {
                    lastException = ex;
                    LogHelper.Instance.LogError($"数据库连接错误，第 {i + 1} 次重试: {ex.Message}");
                    
                    if (i < maxRetries - 1)
                    {
                        // 等待一段时间后重试
                        await Task.Delay(1000 * (i + 1));
                    }
                }
            }
            
            throw new Exception($"数据库操作失败，已重试 {maxRetries} 次", lastException);
        }

        /// <summary>
        /// 获取新的客户端实例
        /// </summary>
        /// <param name="dbKey"></param>
        /// <returns></returns>
        private static SqlSugarClient GetFreshClient(string dbKey)
        {
            // 每次都获取新的客户端实例，避免连接状态问题
            return SqlSugarClientFactory.GetClient(dbKey);
        }

        /// <summary>
        /// 确保连接状态正常
        /// </summary>
        /// <param name="client"></param>
        private static void EnsureConnectionState(SqlSugarClient client)
        {
            try
            {
                // 检查连接状态
                if (client.Ado.Connection.State != ConnectionState.Open)
                {
                    client.Ado.Connection.Open();
                }
                
                // 执行简单查询测试连接
                client.Ado.GetString("SELECT 1");
            }
            catch (Exception ex)
            {
                LogHelper.Instance.LogError($"连接状态检查失败: {ex.Message}");
                
                // 尝试重新打开连接
                try
                {
                    if (client.Ado.Connection.State != ConnectionState.Closed)
                    {
                        client.Ado.Connection.Close();
                    }
                    client.Ado.Connection.Open();
                }
                catch (Exception reopenEx)
                {
                    throw new Exception("无法重新建立数据库连接", reopenEx);
                }
            }
        }

        /// <summary>
        /// 判断是否为连接相关错误
        /// </summary>
        /// <param name="ex"></param>
        /// <returns></returns>
        private static bool IsConnectionRelatedError(Exception ex)
        {
            var message = ex.Message.ToLower();
            return message.Contains("executereader") ||
                   message.Contains("connection") ||
                   message.Contains("timeout") ||
                   message.Contains("pool") ||
                   message.Contains("已打开且可用") ||
                   message.Contains("当前状态") ||
                   message.Contains("网络相关") ||
                   message.Contains("传输级错误");
        }

        /// <summary>
        /// 安全执行非查询操作
        /// </summary>
        /// <param name="dbKey">数据库连接键</param>
        /// <param name="operation">操作函数</param>
        /// <param name="maxRetries">最大重试次数</param>
        /// <returns></returns>
        public static int ExecuteNonQueryWithRetry(string dbKey, Func<SqlSugarClient, int> operation, int maxRetries = 3)
        {
            return ExecuteWithRetry(dbKey, operation, maxRetries);
        }

        /// <summary>
        /// 安全执行事务操作
        /// </summary>
        /// <typeparam name="T">返回类型</typeparam>
        /// <param name="dbKey">数据库连接键</param>
        /// <param name="operation">事务操作函数</param>
        /// <param name="maxRetries">最大重试次数</param>
        /// <returns></returns>
        public static T ExecuteTransactionWithRetry<T>(string dbKey, Func<SqlSugarClient, T> operation, int maxRetries = 3)
        {
            return ExecuteWithRetry(dbKey, client =>
            {
                try
                {
                    client.Ado.BeginTran();
                    var result = operation(client);
                    client.Ado.CommitTran();
                    return result;
                }
                catch
                {
                    if (client.Ado.IsAnyTran())
                    {
                        client.Ado.RollbackTran();
                    }
                    throw;
                }
            }, maxRetries);
        }
    }
}
