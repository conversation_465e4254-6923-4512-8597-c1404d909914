using System;

namespace HZ.WMS.Entity.Produce.Req
{
    /// <summary>
    /// 生产报工列表查询请求
    /// </summary>
    public class Produce_ReportListReq
    {
        /// <summary>
        /// 生产报工单号
        /// </summary>
        public string ProduceReportNo { get; set; }

        /// <summary>
        /// 出厂编号
        /// </summary>
        public string SerialNo { get; set; }

        /// <summary>
        /// 生产订单号
        /// </summary>
        public string ProduceOrderNo { get; set; }

        /// <summary>
        /// 物料编码
        /// </summary>
        public string MaterialNo { get; set; }

        /// <summary>
        /// 物料描述
        /// </summary>
        public string MaterialName { get; set; }

        /// <summary>
        /// 工作中心编码
        /// </summary>
        public string WorkCenterCode { get; set; }

        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// 结束日期
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// 是否完成 0-未完成  1-已完成
        /// </summary>
        public bool? IsCompleted { get; set; }

        /// <summary>
        /// 是否过账 0-未过账  1-已过帐
        /// </summary>
        public bool? IsPosted { get; set; }
    }
} 