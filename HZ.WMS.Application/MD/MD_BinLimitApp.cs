using HZ.WMS.Application.Sys;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.Sys;
using System;
using System.Collections.Generic;
using System.Linq;
using SqlSugar;
using SqlSugar;

namespace HZ.WMS.Application.MD
{
    /// <summary>
    /// 库位限制
    /// </summary>
    public class MD_BinLimitApp : BaseApp<MD_BinLimit>
    {
        private MD_BinLocationApp _binLocationApp = new MD_BinLocationApp();


        #region 保存设置

        /// <summary>
        /// 
        /// </summary>
        /// <param name="roleId"></param>
        /// <param name="binIds"></param>
        /// <param name="opUser"></param>
        /// <param name="error_message"></param>
        /// <returns></returns>
        public bool SaveBinLimitSetting(string roleId, string[] binIds, string opUser, out string error_message)
        {
            error_message = "";
            Sys_Resource resourceList = new Sys_ResourceApp().GetEntityByKey(roleId);
            List<MD_BinLocation> binLocationList = new MD_BinLocationApp().GetListByKeys(binIds);
            List<MD_BinLimit> listBinLimit = new List<MD_BinLimit>();
            bool bResult = false;
            try
            {
                DbContext.Ado.BeginTran();
                //
                HardDelete(x => x.MenuCode == resourceList.ResourceID);
                binLocationList.ForEach((item) =>
                {
                    MD_BinLimit entity = new MD_BinLimit();
                    entity.MenuCode = resourceList.ResourceID;
                    entity.MenuName = resourceList.ResourceTitle;      //后续在考虑
                    entity.RegionCode = item.RegionCode;
                    entity.RegionName = item.RegionName;
                    entity.BinLocationCode = item.BinLocationCode;
                    entity.BinLocationName = item.BinLocationName;
                    entity.MUser = opUser;
                    entity.MTime = DateTime.Now;
                    entity.CUser = opUser;
                    entity.CTime = DateTime.Now;

                    listBinLimit.Add(entity);

                });

                Insert(listBinLimit);
                bResult = true;
                DbContext.Ado.CommitTran();


            }
            catch (Exception ex)
            {

                error_message = ex.Message;
                DbContext.Ado.RollbackTran();
            }

            return bResult;
        }
        /// <summary>
        /// 新增限制
        /// </summary>
        /// <param name="roleId"></param>
        /// <param name="binIds"></param>
        /// <param name="opUser"></param>
        /// <param name="error_message"></param>
        /// <returns></returns>
        public bool AddBinLimitSetting(string roleId, string[] binIds, string opUser, out string error_message)
        {
            error_message = "";
            Sys_Resource resourceList = new Sys_ResourceApp().GetEntityByKey(roleId);
            List<MD_BinLocation> binLocationList = new MD_BinLocationApp().GetListByKeys(binIds);
            List<MD_BinLimit> listBinLimit = new List<MD_BinLimit>();
            bool bResult = false;
            try
            {
                var binlocationCodes = binLocationList.Select(x => x.BinLocationCode).ToArray();
                var isHave = this.GetList(x => binlocationCodes.Contains(x.BinLocationCode) && x.MenuCode == roleId).ToList();
                DbContext.Ado.BeginTran();
                this.Delete(isHave);
                //
                binLocationList.ForEach((item) =>
                {
                    MD_BinLimit entity = new MD_BinLimit();
                    entity.MenuCode = resourceList.ResourceID;
                    entity.MenuName = resourceList.ResourceTitle;      //后续在考虑
                    entity.RegionCode = item.RegionCode;
                    entity.RegionName = item.RegionName;
                    entity.BinLocationCode = item.BinLocationCode;
                    entity.BinLocationName = item.BinLocationName;
                    entity.MUser = opUser;
                    entity.MTime = DateTime.Now;
                    entity.CUser = opUser;
                    entity.CTime = DateTime.Now;

                    listBinLimit.Add(entity);

                });

                Insert(listBinLimit);
                bResult = true;
                DbContext.Ado.CommitTran();


            }
            catch (Exception ex)
            {

                error_message = ex.Message;
                DbContext.Ado.RollbackTran();
            }

            return bResult;
        }

        #endregion

        #region 根据MenuCode查询已存在的库位限制
        /// <summary>
        /// 根据MenuCode查询已存在的库位限制
        /// </summary>
        /// <param name="menuCode"></param>
        /// <returns></returns>
        public List<MD_BinLimit> GetBinLimitByMenuCode(string menuCode)
        {
            List<MD_BinLimit> binLimitInfo = GetList(x => x.MenuCode == menuCode).ToList();   // 已质检条码

            return binLimitInfo;
        }

        #endregion

        #region 获取已选择模块已有的库位
        /// <summary>
        /// 获取已选择模块已有的库位
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public List<MD_BinLocation> GetExistBinFromBinLocation(string[] ids)
        {
            List<MD_BinLocation> binLoctionList = new MD_BinLocationApp().GetList(x => ids.Contains(x.BinLocationCode)).ToList();

            return binLoctionList;
        }

        #endregion

        #region 库位限制校验(如果没有设置菜单，则不校验限制)

        /// <summary>
        /// 
        /// </summary>
        /// <param name="menuCode"></param>
        /// <param name="binLocationCode"></param>
        /// <param name="error_message"></param>
        /// <returns></returns>
        public bool ValidateBinLocationLimit(string menuCode, string binLocationCode, out string error_message)
        {
            error_message = "";
            MD_BinLocation binLoc = new MD_BinLocationApp().GetList(x => x.BinLocationCode == binLocationCode && !x.IsDelete)?.ToList().FirstOrDefault();
            if (binLoc == null)
            {
                error_message = "Common.NotExistBinLocation";   //不存在的库位
                return false;
            }

            ISugarQueryable<MD_BinLimit> mdSetting = GetList(x => x.MenuCode == menuCode);
            if (mdSetting != null && mdSetting.Count() > 0)
            {
                MD_BinLimit limit = mdSetting.Where(x => x.BinLocationCode == binLocationCode).ToList().FirstOrDefault();
                if (limit != null)
                {
                    return true;
                }
                else
                {
                    error_message = "Common.BinLocationLimitNotPass";
                    return false;
                }
            }
            else
            {
                return true;
            }

        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="menuCode"></param>
        /// <param name="binLocationCodes"></param>
        /// <param name="error_message"></param>
        /// <returns></returns>
        public bool ValidateBinLocationLimit(string menuCode, string[] binLocationCodes, out string error_message)
        {
            error_message = "";
            foreach (string binLocation in binLocationCodes)
            {
                if (ValidateBinLocationLimit(menuCode, binLocation, out error_message) == false)
                    return false;
            }
            return true;

        }




        #endregion

    }
}
