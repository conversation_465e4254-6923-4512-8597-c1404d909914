using SqlSugar;
using System;
using System.Collections.Generic;

using HZ.Core.Security;
using HZ.WMS.Entity.Sys;
using HZ.Core.Http;
using System.Linq;
using HZ.WMS.Entity;

namespace HZ.WMS.Application.Sys
{
    /// <summary>
    /// 涉及资源操作基本都要分客户端来进行，不同客户端对于权限的操作并不相同
    /// </summary>

    public class Sys_ResourceApp : BaseApp<Sys_Resource>
    {
        private Sys_RoleApp _roleApp = new Sys_RoleApp();

		#region 默认构造函数
        
        /// <summary>
        /// 默认构造函数
        /// </summary>
        public Sys_ResourceApp() : base()
        {
        }

        
        

        #endregion

        #region 获取用户资源列表

        /// <summary>
        /// 获取用户客户端路由列表（如果用户配有相关角色，则关联相关角色权限）
        /// </summary>
        /// <param name="user"></param>
        /// <param name="appId"></param>
        /// <returns></returns>
        public List<Sys_Resource> GetUserResources(Sys_User user, string appId)
        {
            // 角色：  管理员 - 开发人员 - 业务用户(用户角色唯一，菜单角色不唯一)
            
            return base.DbContext.Queryable<Sys_Resource, Sys_RoleResource, Sys_UserRole>((sr, srr, sur) => new object[] {
                    JoinType.Inner,sr.ResourceID==srr.ResourceID && !sr.IsDelete && !srr.IsDelete,
                    JoinType.Inner,srr.RoleID==sur.RoleID && sur.UserID==user.UserID && !sur.IsDelete
                })
                .Select((sr, srr, sur) => sr)
                .Where(t => t.AppID == appId)
                .ToList();

        }


        #endregion

        #region 获取用户特定模块资源列表

        /// <summary>
        /// 获取用户模块资源（同一个模块下不能有不同的AppID）
        /// </summary>
        /// <param name="user"></param>
        /// <param name="moduleId"></param>
        /// <returns></returns>
        public List<Sys_Resource> GetUserModuleResources(Sys_User user, string moduleId)
        {

            return
                base.DbContext.Queryable<Sys_Resource, Sys_RoleResource, Sys_UserRole>((sr, srr, sur) => new object[] {
                    JoinType.Inner,sr.ResourceID==srr.ResourceID && !sr.IsDelete && !srr.IsDelete && sr.FathResourceID == moduleId,
                    JoinType.Inner,srr.RoleID==sur.RoleID && sur.UserID==user.UserID && !sur.IsDelete
                })
                .Select((sr, srr, sur) => sr)
                .OrderBy(x=>x.SortNo)
                .OrderBy(t=>t.ResourceID)   //按照ID调节顺序
                .ToList()
                .Distinct(new EntityComparer<Sys_Resource>())
                .ToList();

        }


        #endregion

        #region 获取用户特定模块资源列表

        ///// <summary>
        ///// 
        ///// </summary>
        ///// <param name="user"></param>
        ///// <param name="appId"></param>
        ///// <returns></returns>
        //public List<Sys_Resource> GetUserTopLevelResources(Sys_User user, string appId)
        //{
        //    return
        //        base.DbContext.JoinQuery<Sys_Resource, Sys_RoleResource, Sys_UserRole>((sr, srr, sur) => new object[] {
        //            JoinType.InnerJoin,sr.ResourceID==srr.ResourceID && !sr.IsDelete && !srr.IsDelete,
        //            JoinType.InnerJoin,srr.RoleID==sur.RoleID && sur.UserID==user.UserID && !sur.IsDelete
        //        })
        //        .Select((sr, srr, sur) => sr)
        //        .ToList()
        //        .Where(t => t.AppID == appId && string.IsNullOrEmpty(t.FathResourceID))
        //        .ToList();
        //}


        /// <summary>
        /// 替换掉GetUserTopLevelResources
        /// </summary>
        /// <param name="user"></param>
        /// <param name="appId"></param>
        /// <returns></returns>
        public List<Sys_Resource> GetUserAppModules(Sys_User user ,string appId)
        {
            string[] userRoles = new Sys_RoleApp().GetUserRoles(user.UserID).Select(x=>x.RoleID).ToArray();
            var query =
                base.DbContext.Queryable<Sys_Resource, Sys_RoleResource, Sys_Role>((sr, srr, r) => new object[] {
                    JoinType.Inner,sr.ResourceID==srr.ResourceID  && !sr.IsDelete && !srr.IsDelete,
                    JoinType.Inner,srr.RoleID == r.RoleID && !r.IsDelete && userRoles.Contains(r.RoleID)
                })
                .Select((sr, srr, r) => sr)
                .Where(t => t.AppID == appId && t.ResourceType == (int)ResourceType.Module);
            return query.Distinct().ToList().OrderBy(x => x.ResourceID).ToList();
        }


        #endregion

        #region 获取用户所有权限按钮

        /// <summary>
        /// 获取用户所有权限按钮
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="appId"></param>
        /// <returns></returns>
        public List<Sys_Resource> GetUserPermissionButtons(string userId,string appId)
        {
            List<Sys_Role> roleList = _roleApp.GetUserRoles(userId);
            return GetRoleAppResources(roleList, appId)
                .Where(t => t.ResourceType == (int)ResourceType.Button)
                .ToList();

        }


        #endregion

        #region 获取用户后台路由

        /// <summary>
        /// 获取用户后台路由
        /// </summary>
        /// <param name="user"></param>
        /// <param name="appId"></param>
        /// <returns></returns>
        public List<Sys_Resource> GetUserPermissionRoutes(Sys_User user, string appId)
        {
            List<Sys_Resource> userRoutes = new List<Sys_Resource>();
            List<Sys_Role> roleList = _roleApp.GetUserRoles(user.UserID);
            var userPermissionRoutes = GetRoleAppResources(roleList, appId)
                .Where(t => t.ResourceType == (int)ResourceType.Module || t.ResourceType == (int)ResourceType.Menu)
                .Where(t => t.Hidden != true).OrderBy(x => x.SortNo).ThenBy(r => r.ResourceID)
                .ToList();
                
            if (user.UserRole!=(int)UserRoleType.Developer)
            {
                userRoutes= userPermissionRoutes.Where(t => t.IsDeveloperResource!=true).OrderBy(x=>x.SortNo).ThenBy(r=>r.ResourceID).ToList();
            }
            else
            {
                userRoutes = userPermissionRoutes;
            }
            // 添加所有隐藏路由
            List<Sys_Resource> hiddenRoute = GetList(x => x.Hidden == true).ToList();

            return userRoutes.Concat(hiddenRoute).ToList();

        }

        #endregion

        #region 获取角色资源

        /// <summary>
        /// 获取角色资源
        /// </summary>
        /// <param name="role"></param>
        /// <param name="appId"></param>
        /// <returns></returns>
        public List<Sys_Resource> GetRoleAppResources(Sys_Role role,string appId)
        {
            return
                base.DbContext.Queryable<Sys_Resource, Sys_RoleResource>((sr, srr) => new object[] {
                    JoinType.Inner,sr.ResourceID==srr.ResourceID && !sr.IsDelete && !srr.IsDelete && srr.RoleID == role.RoleID,
                })
                .Select((sr, srr) => sr)
                .ToList()
                .Where(t => t.AppID == appId)
                .ToList();
        }


        /// <summary>
        /// 获取角色资源
        /// </summary>
        /// <param name="roles"></param>
        /// <param name="appId"></param>
        /// <returns></returns>
        public List<Sys_Resource> GetRoleAppResources(List<Sys_Role> roles, string appId)
        {
            string[] roleIds = roles.Select(t => t.RoleID).Distinct().ToArray();
            return
                base.DbContext.Queryable<Sys_Resource, Sys_RoleResource>((sr, srr) => new object[] {
                    JoinType.Inner,sr.ResourceID == srr.ResourceID  && !sr.IsDelete && !srr.IsDelete && roleIds.Contains(srr.RoleID)
                })
                .Select((sr, srr) => sr)
                .Where(sr => sr.AppID == appId)
                .ToList()
                .Distinct(new Sys_RecourceComparer())
                .ToList();
        }



        #region 获取角色特定资源列表(根据角色ID)

        /// <summary>
        /// 获取角色指定客户端资源列表
        /// 按照角色设定权限
        /// </summary>
        /// <param name="roleid"></param>
        /// <param name="appid"></param>
        /// <returns></returns>
        public List<Sys_Resource> GetAppRoleResources(string roleid, string appid)
        {
            return
                base.DbContext.Queryable<Sys_Resource, Sys_RoleResource>((sr, srr) => new object[] {
                    JoinType.Inner,sr.ResourceID==srr.ResourceID && !sr.IsDelete && !srr.IsDelete && srr.RoleID == roleid,
                })
                .Select((sr, srr) => sr)
                .ToList()
                .Where(t => t.AppID == appid).OrderBy(x=>x.SortNo)
                .ToList();
        }


        #endregion


        #endregion

    }



}

