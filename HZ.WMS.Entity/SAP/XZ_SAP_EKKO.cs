using System;
using System.ComponentModel;
using SqlSugar;

namespace HZ.WMS.Entity.SAP
{
    /// <summary>
    /// 采购订单主表    
    /// </summary>
    [SugarTable("XZ_SAP_EKKO")]
   public class XZ_SAP_EKKO
    {
        /// <summary>
        /// 采购订单类型
        /// </summary>
        [Description("采购订单类型")]
        public string BSART { get; set; }
        /// <summary>
        /// 采购订单类型描述
        /// </summary>
        [Description("采购订单类型描述")]
        public string BATXT { get; set; }
        /// <summary>
        /// 采购订单编号
        /// </summary>
        [Description("采购订单编号")]
        public string EBELN { get; set; }
        /// <summary>
        /// 公司代码
        /// </summary>
        [Description("公司代码")]
        public string BUKRS { get; set; }
        /// <summary>
        /// 采购组织
        /// </summary>
        [Description("采购组织")]
        public string EKORG { get; set; }
        /// <summary>
        /// 采购组
        /// </summary>
        [Description("采购组")]
        public string EKGRP { get; set; }
        /// <summary>
        /// 创建日期
        /// </summary>
        [Description("创建日期")]
        public DateTime AEDAT { get; set; }
        /// <summary>
        /// 创建者
        /// </summary>
        [Description("创建者")]
        public string ERNAM { get; set; }
        /// <summary>
        /// 供应商编号
        /// </summary>
        [Description("供应商编号")]
        public string LIFNR { get; set; }
        /// <summary>
        /// 供应商名称
        /// </summary>
        [Description("供应商名称")]
        public string NAME1 { get; set; }
        /// <summary>
        /// 付款条件编码
        /// </summary>
        [Description("付款条件编码")]
        public string ZTERM { get; set; }
        /// <summary>
        /// OA流程单号
        /// </summary>
        [Description("OA流程单号")]
        public string ZNUMBER { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        [Description("状态")]
        public bool Status { get; set; }
    }
}
