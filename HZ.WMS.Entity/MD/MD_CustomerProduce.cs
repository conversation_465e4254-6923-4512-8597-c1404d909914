using SqlSugar;
using System.ComponentModel;

namespace HZ.WMS.Entity.MD
{
    /// <summary>
    /// 物料主数据
    /// </summary>
    [SugarTable("MD_CustomerProduce")]
    public class MD_CustomerProduce : BaseEntity
    {

        /// <summary>
        /// Id
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Description("Id")]
        public string Id { get; set; }

        /// <summary>
        /// 客户名称
        /// </summary>
        [Description("客户名称")]
        public string CustomerName { get; set; }

        /// <summary>
        /// 客户代码
        /// </summary>
        [Description("客户代码")]
        public string CustomerCode { get; set; }

        /// <summary>
        /// 生产序号
        /// </summary>
        [Description("生产序号")]
        public decimal ProduceSort { get; set; }

    }
}