using SqlSugar;
using System.ComponentModel;

namespace HZ.WMS.Entity.MD
{
    /// <summary>
    /// 非标参数配置
    /// </summary>
    [SugarTable("MD_NonStandardConfig")]
    public class MD_NonStandardConfig : BaseEntity
    {
        /// <summary>
        /// Id
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Description("Id")]
        public string Id { get; set; }

        /// <summary>
        /// 接受编号
        /// </summary>
        [Description("接受编号")]
        [SugarColumn(Length = 32)]
        public string AcceptNo { get; set; }

        /// <summary>
        /// 系列型号
        /// </summary>
        [Description("系列型号")]
        [SugarColumn(Length = 32)]
        public string SeriesModel { get; set; }

        /// <summary>
        /// SAP件号
        /// </summary>
        [Description("SAP件号")]
        [SugarColumn(Length = 32)]
        public string SAPPartNo { get; set; }

        /// <summary>
        /// SAP产品型号
        /// </summary>
        [Description("SAP产品型号")]
        [SugarColumn(Length = 32)]
        public string SAPProductModel { get; set; }

        /// <summary>
        /// 曳引比
        /// </summary>
        [Description("曳引比")]
        [SugarColumn(Length = 32)]
        public string TractionRatio { get; set; }

        /// <summary>
        /// 极对数
        /// </summary>
        [Description("极对数")]
        [SugarColumn(Length = 32)]
        public string PolePairsNum { get; set; }

        /// <summary>
        /// 极数
        /// </summary>
        [Description("极数")]
        [SugarColumn(Length = 32)]
        public string PolesNo { get; set; }

        /// <summary>
        /// 防护等级
        /// </summary>
        [Description("防护等级")]
        [SugarColumn(Length = 32)]
        public string ProtectionGrade { get; set; }

        /// <summary>
        /// 启动次数
        /// </summary>
        [Description("启动次数")]
        [SugarColumn(Length = 32)]
        public string StartNo { get; set; }

        /// <summary>
        /// 节径
        /// </summary>
        [Description("节径")]
        [SugarColumn(Length = 32)]
        public string PitchDiameter { get; set; }

        /// <summary>
        /// 绳槽
        /// </summary>
        [Description("绳槽")]
        [SugarColumn(Length = 32)]
        public string RopeGroove { get; set; }

        /// <summary>
        /// 槽距
        /// </summary>
        [Description("槽距")]
        [SugarColumn(Length = 32)]
        public string GrooveDistance { get; set; }

        /// <summary>
        /// 主机重量
        /// </summary>
        [Description("主机重量")]
        [SugarColumn(Length = 32)]
        public string HostWeight { get; set; }

        /// <summary>
        /// 额定功率
        /// </summary>
        [Description("额定功率")]
        [SugarColumn(Length = 32)]
        public string RatedPower { get; set; }

        /// <summary>
        /// 额定载重
        /// </summary>
        [Description("额定载重")]
        [SugarColumn(Length = 32)]
        public string RatedLoad { get; set; }

        /// <summary>
        /// 额定速度
        /// </summary>
        [Description("额定速度")]
        [SugarColumn(Length = 32)]
        public string RatedSpeed { get; set; }

        /// <summary>
        /// 额定转速
        /// </summary>
        [Description("额定转速")]
        [SugarColumn(Length = 32)]
        public string RatedRotationSpeed { get; set; }

        /// <summary>
        /// 额定转矩
        /// </summary>
        [Description("额定转矩")]
        [SugarColumn(Length = 32)]
        public string RatedTorque { get; set; }

        /// <summary>
        /// 额定电压
        /// </summary>
        [Description("额定电压")]
        [SugarColumn(Length = 32)]
        public string RatedVoltage { get; set; }

        /// <summary>
        /// 额定电流
        /// </summary>
        [Description("额定电流")]
        [SugarColumn(Length = 32)]
        public string RatedCurrent { get; set; }

        /// <summary>
        /// 额定频率
        /// </summary>
        [Description("额定频率")]
        [SugarColumn(Length = 32)]
        public string RatedFrequency { get; set; }

        /// <summary>
        /// 效率
        /// </summary>
        [Description("效率")]
        [SugarColumn(Length = 32)]
        public string Efficiency { get; set; }

        /// <summary>
        /// 定子电阻
        /// </summary>
        [Description("定子电阻")]
        [SugarColumn(Length = 32)]
        public string StatorResistance { get; set; }

        /// <summary>
        /// 定子电抗
        /// </summary>
        [Description("定子电抗")]
        [SugarColumn(Length = 32)]
        public string StatorReactance { get; set; }

        /// <summary>
        /// 反电动势
        /// </summary>
        [Description("反电动势")]
        [SugarColumn(Length = 32)]
        public string BackElectromotiveForce { get; set; }

        /// <summary>
        /// 电感
        /// </summary>
        [Description("电感")]
        [SugarColumn(Length = 32)]
        public string Inductance { get; set; }

        /// <summary>
        /// 相电阻
        /// </summary>
        [Description("相电阻")]
        [SugarColumn(Length = 32)]
        public string PhaseResistance { get; set; }

        /// <summary>
        /// 装箱尺寸
        /// </summary>
        [Description("装箱尺寸")]
        [SugarColumn(Length = 64)]
        public string PackingSize { get; set; }

        /// <summary>
        /// 装箱净重
        /// </summary>
        [Description("装箱净重")]
        [SugarColumn(Length = 32)]
        public string PackingNetWeight { get; set; }

        /// <summary>
        /// 装箱毛重
        /// </summary>
        [Description("装箱毛重")]
        [SugarColumn(Length = 32)]
        public string PackingGrossWeight { get; set; }
    }
}
