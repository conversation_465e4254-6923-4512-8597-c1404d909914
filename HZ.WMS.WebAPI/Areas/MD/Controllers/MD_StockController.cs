using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;
using HZ.Core.Http;
using HZ.Core.Office;
using HZ.WMS.Application;
using HZ.WMS.Application.MD;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.Sys;
using HZ.WMS.WebAPI.Controllers;

namespace HZ.WMS.WebAPI.Areas.MD.Controllers
{
    /// <summary>
    /// 库存主数据
    /// </summary>
    public class MD_StockController : ApiBaseController
    {
        private MD_StockApp _app = new MD_StockApp();


        #region 库存报表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <param name="fromTime"></param>
        /// <param name="toTime"></param>
        /// <param name="regionCode"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPage([FromUri]Pagination page, [FromUri]string keyword, [FromUri]string fromTime, [FromUri]string toTime, [FromUri] string regionCode)
        {
            var result = new ResponseData();
            try
            {
                List<MD_Stock> query = null;

                if (string.IsNullOrEmpty(fromTime))
                {
                    fromTime = "1900-01-01";
                }

                if (string.IsNullOrEmpty(toTime))
                {
                    toTime = "2099-01-01";
                }


                query = _app.GetPageList(page, x => x.CTime >= Convert.ToDateTime(fromTime).Date && x.CTime < Convert.ToDateTime(toTime).Date.AddDays(1)
                    && (string.IsNullOrEmpty(keyword)
                        || x.SupplierCode.Contains(keyword)
                        || x.SupplierName.Contains(keyword)
                        || x.BarCode.Contains(keyword)
                        || x.BatchNum.Contains(keyword)
                        || x.SupplierBatch.Contains(keyword)
                        || x.ItemCode.Contains(keyword)
                        || x.ItemName.Contains(keyword)
                        || x.RegionCode.Contains(keyword)
                        || x.RegionName.Contains(keyword)
                        || x.BinLocationCode.Contains(keyword)
                        || x.BinLocationName.Contains(keyword)
                        || x.Remark.Contains(keyword))&&x.Qty>0);
                var itemsData = query.ToList();
                //PO_BarCodeApp _barcodeApp = new PO_BarCodeApp();
                //var isConsignData = _barcodeApp.GetList(x => x.IsConsign == true)?.ToList();
                //itemsData.ForEach(item =>
                //{
                //    var flag = isConsignData.Where(x => x.BarCode == item.BarCode)?.ToList().FirstOrDefault();
                //    if (flag != null)
                //    {
                //        item.IsDelete = true;
                //    }
                //});
                itemsData = itemsData.Where(x => !x.IsDelete)?.ToList();
                result.Data = new ResponsePageData { total = itemsData.Count, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }



        /// <summary>
        /// 查询分页列表 SAP+WMS
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <param name="fromTime"></param>
        /// <param name="toTime"></param>
        /// <param name="regionCode"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetStockList([FromUri]string ItemCode,  [FromUri] string WhsCode, [FromUri]string SpecialStock, [FromUri]string SupplierCode)
        {
            var result = new ResponseData();
            try
            {
                //ISugarQueryable<MD_Stock> query = null;

                var query = _app.GetStockList(ItemCode, WhsCode, SpecialStock, SupplierCode);
                var itemsData = query.ToList();
                //PO_BarCodeApp _barcodeApp = new PO_BarCodeApp();
                //var isConsignData = _barcodeApp.GetList(x => x.IsConsign == true)?.ToList();
                //itemsData.ForEach(item =>
                //{
                //    var flag = isConsignData.Where(x => x.BarCode == item.BarCode)?.ToList().FirstOrDefault();
                //    if (flag != null)
                //    {
                //        item.IsDelete = true;
                //    }
                //});
                //itemsData = itemsData.Where(x => !x.IsDelete)?.ToList();
                result.Data = new ResponsePageData { total = itemsData.Count, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }



        /// <summary>
        /// 查询分页列表 SAP+WMS
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <param name="fromTime"></param>
        /// <param name="toTime"></param>
        /// <param name="regionCode"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetStockList_PP([FromUri]string ItemCode, [FromUri] string WhsCode, [FromUri]string SpecialStock, [FromUri]string SupplierCode)
        {
            var result = new ResponseData();
            try
            {
                //ISugarQueryable<MD_Stock> query = null;

                var query = _app.GetStockList_PP(ItemCode, WhsCode, SpecialStock, SupplierCode);
                var itemsData = query.ToList();
                //PO_BarCodeApp _barcodeApp = new PO_BarCodeApp();
                //var isConsignData = _barcodeApp.GetList(x => x.IsConsign == true)?.ToList();
                //itemsData.ForEach(item =>
                //{
                //    var flag = isConsignData.Where(x => x.BarCode == item.BarCode)?.ToList().FirstOrDefault();
                //    if (flag != null)
                //    {
                //        item.IsDelete = true;
                //    }
                //});
                //itemsData = itemsData.Where(x => !x.IsDelete)?.ToList();
                result.Data = new ResponsePageData { total = itemsData.Count, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion




        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <param name="regionCode"></param>
        /// <param name="binLocationCode"></param>
        /// <returns></returns>
        //[HttpGet]
        //public IHttpActionResult GetPageToMain([FromUri]Pagination page, [FromUri]string keyword, [FromUri] string regionCode, [FromUri] string binLocationCode = "")
        //{
        //    var result = new ResponseData();
        //    try
        //    {
        //        var query = _app.DbContext.Queryable<MD_Stock>()
        //             .Where(x => !x.IsDelete)
        //             .Where(x =>
        //                 (string.IsNullOrEmpty(regionCode) || x.RegionCode == regionCode) &&
        //                 (string.IsNullOrEmpty(binLocationCode) || x.BinLocationCode == binLocationCode) &&
        //                 (string.IsNullOrEmpty(keyword)
        //                 || x.BoxBarCode.Contains(keyword)
        //                 || x.SupplierCode.Contains(keyword)
        //                 || x.SupplierName.Contains(keyword)
        //                 || x.BarCode.Contains(keyword)
        //                 || x.BatchNum.Contains(keyword)
        //                 || x.SupplierBatch.Contains(keyword)
        //                 || x.ItemCode.Contains(keyword)
        //                 || x.ItemName.Contains(keyword)
        //                 || x.RegionCode.Contains(keyword)
        //                 || x.RegionName.Contains(keyword)
        //                 || x.BinLocationCode.Contains(keyword)
        //                 || x.BinLocationName.Contains(keyword)
        //                 || x.Remark.Contains(keyword)))
        //                 .GroupBy(t => new { t.ItemCode, t.ItemName, t.RegionCode, t.RegionName, t.BinLocationCode })
        //                 .Select(y => new MD_Stock
        //                 {
        //                     ItemCode = y.ItemCode,
        //                     ItemName = y.ItemName,
        //                     RegionCode = y.RegionCode,
        //                     RegionName = y.RegionName,
        //                     BinLocationCode = y.BinLocationCode
        //                 });
        //        List<MD_Stock> itemsData = query.ToList();
        //        int total = query.Count();
        //        page.Total = total;
        //        var itemPageData = new List<MD_Stock>();
        //        if (page.PageSize > total)
        //        {
        //            itemPageData = itemsData.Skip(page.PageSize * (page.PageNumber - 1)).Take(total).ToList();
        //        }
        //        else
        //        {
        //            itemPageData = itemsData.Skip(page.PageSize * (page.PageNumber - 1)).Take(page.PageSize).ToList();
        //        }
        //        result.Data = new ResponsePageData { total = page.Total, items = itemPageData };
        //        result.Code = (int)WMSStatusCode.Success;
        //    }
        //    catch (Exception ex)
        //    {
        //        result.Code = (int)WMSStatusCode.UnHandledException;
        //        result.Message = ex.InnerException?.Message ?? ex.Message;
        //    }
        //    return Json(result);
        //}
        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <param name="regionCode"></param>
        /// <param name="binLocationCode"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageToMain([FromUri]Pagination page, [FromUri]string keyword, [FromUri] string regionCode, [FromUri] string binLocationCode = "")
        {
            var result = new ResponseData();
            try
            {
                Sys_User currLoginUser = GetCurrentUser();
                var itemPageData = _app.GetPageToMain(page, keyword, currLoginUser).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemPageData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 物料查询
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <param name="regionCode"></param>
        /// <param name="binLocationCode"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPage([FromUri]Pagination page, [FromUri]string keyword, [FromUri] string WhsCode)
        {
            var result = new ResponseData();
            try
            {
                List<MD_Stock> query = null;
                query = _app.GetPageList(page, x =>
                (string.IsNullOrEmpty(WhsCode) || x.WhsCode == WhsCode || x.WhsName == WhsCode) &&
                (string.IsNullOrEmpty(keyword)
                        || x.SupplierCode.Contains(keyword)
                        || x.SupplierName.Contains(keyword)
                        || x.BarCode.Contains(keyword)
                        || x.BatchNum.Contains(keyword)
                        || x.SupplierBatch.Contains(keyword)
                        || x.ItemCode.Contains(keyword)
                        || x.ItemName.Contains(keyword)
                        || x.Remark.Contains(keyword)) && x.Qty > 0);
                var itemsData = query.ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        /// <summary>
        /// PDA查询库存
        /// </summary>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetEntityListToPDA([FromUri]string keyword, [FromUri] string WhsCode)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetAllList(x => !x.IsDelete && (string.IsNullOrEmpty(keyword)
                || x.BarCode.Contains(keyword)
                || x.ItemCode.Contains(keyword)
                || x.BinLocationCode.Contains(keyword)
                || x.RegionCode.Contains(keyword))).OrderBy(x => x.BatchNum).ToList();
                result.Data = itemsData;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {

                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }


        #region 导出

        /// <summary>
        /// 
        /// </summary>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri]string ItemCode, [FromUri] string WhsCode, [FromUri]string SpecialStock, [FromUri]string SupplierCode)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetStockList(ItemCode, WhsCode, SpecialStock, SupplierCode);

                //PO_BarCodeApp _barcodeApp = new PO_BarCodeApp();
                //var isConsignData = _barcodeApp.GetList(x => x.IsConsign == true)?.ToList();
                //itemsData.ForEach(item =>
                //{
                //    var flag = isConsignData.Where(x => x.BarCode == item.BarCode)?.ToList().FirstOrDefault();
                //    item.Remark = "0";
                //    if (flag != null)
                //    {
                //        item.Remark = "1";
                //    }
                //});
                //var itemsData = _app.GetList().ToList();
                List<ExcelColumn<MD_Stock>> columns = ExcelService.FetchDefaultColumnList<MD_Stock>();
                string[] ignoreField = new string[] { "BoxBarCode", "BatchNum", "SupplierBatch", "PTime", "ItmsGrpCode", "ItmsGrpName", "RegionCode", "RegionName",
                    "BinLocationCode","BinLocationName","Remark","MUser","MTime","IsDelete", "DTime", "DUser", "StockID"  };

                List<ExcelColumn<MD_Stock>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<MD_Stock> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                columns.ForEach((column) =>
                {
                    if (column.ColumnName == "Remark")
                    {
                        column.Formattor = ExcelExportFormatter.IsConsignFormatter;
                    }
                });

                return ExportToExcelFile<MD_Stock>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }
        #endregion

    }
}

