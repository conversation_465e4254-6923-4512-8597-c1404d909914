using SqlSugar;
using HZ.WMS.Application.MD;
using HZ.WMS.Application.SAP;
using HZ.WMS.Application.Sys;
using HZ.WMS.Entity.PP;
using HZ.WMS.Entity.PP.Dto;
using HZ.WMS.Entity.SAP;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Application.PP
{
    /// <summary>
    /// 超额领料主表
    /// </summary>
    public class PP_OverReceiveApp : BaseApp<PP_OverReceive>
    {
        private PP_OverReceiveDetailApp detailApp = new PP_OverReceiveDetailApp();
        private Sys_SAPCompanyInfoApp sapApp = new Sys_SAPCompanyInfoApp();

        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public PP_OverReceiveApp() : base()
        {
        }

        #endregion

        #region 添加

        public bool Add(PP_OverReceive overReceive, List<PP_OverReceiveDetail> overReceiveDetails)
        {
            var mark = false;
            try
            {
                //this.DbContext.Ado.BeginTran();
                this.DbContext.Ado.BeginTran();
                base.Insert(overReceive);
                detailApp.Insert(overReceiveDetails);
                this.DbContext.Ado.CommitTran();
                mark = true;
            }
            catch (Exception ex)
            {
                mark = false;
                this.DbContext.Ado.RollbackTran();
                throw ex;
            }
            return mark;
        }

        #endregion

        #region 编辑

        public bool Edit(PP_OverReceive_Dto overReceive, string user)
        {
            bool mark = false;
            try
            {
                var order = this.GetEntityByKey(overReceive.ID);
                var entity = new PP_OverReceive
                {
                    ID = overReceive.ID,
                    OverReceiveNo = overReceive.OverReceiveNo,
                    MovementType = overReceive.MovementType,
                    FileName = overReceive.FileName,
                    FilePath = overReceive.FilePath,
                    Remark = overReceive.Remark,
                    CUser = order?.CUser,
                    CTime = order?.CTime,
                    MUser = user,
                    ManualPostTime = overReceive.ManualPostTime,
                };

                var addList = new List<PP_OverReceiveDetail>();
                var updateList = new List<PP_OverReceiveDetail>();
                foreach (var item in overReceive.DetailList)
                {
                    if(string.IsNullOrEmpty(item.ID))
                    {
                        item.OverReceiveNo = overReceive.OverReceiveNo;
                        item.MovementType = overReceive.MovementType;
                        item.ManualPostTime = overReceive.ManualPostTime;
                        item.CUser = user;
                        addList.Add(item);
                    }
                    else
                    {
                        item.OverReceiveNo = overReceive.OverReceiveNo;
                        item.MovementType = overReceive.MovementType;
                        item.ManualPostTime = overReceive.ManualPostTime;
                        item.MUser = user;
                        updateList.Add(item);
                    }
                }
                var deleteList = detailApp.GetListByKeys(overReceive.DelDetailIds);
                if (deleteList != null && deleteList.Count > 0)
                {
                    deleteList.ForEach(item =>
                    {
                        item.DUser = user;
                        item.IsDelete = true;
                    });
                    updateList.AddRange(deleteList);
                }

                this.DbContext.Ado.BeginTran();
                base.Update(entity);
                detailApp.Update(updateList);
                detailApp.Insert(addList);
                this.DbContext.Ado.CommitTran();
                mark = true;
            }
            catch (Exception ex)
            {
                mark = false;
                this.DbContext.Ado.RollbackTran();
                throw ex;
            }
            return mark;
        }

        #endregion

        #region 删除

        public bool Delete(string user,List<PP_OverReceive> overReceives,List<PP_OverReceiveDetail> overReceiveDetails)
        {
            var mark = false;
            try
            {
                //this.DbContext.Ado.BeginTran();
                this.DbContext.Ado.BeginTran();
                base.Delete(overReceives, user);
                detailApp.Delete(overReceiveDetails, user);
                this.DbContext.Ado.CommitTran();
                mark = true;
            }
            catch (Exception ex)
            {
                mark = false;
                this.DbContext.Ado.RollbackTran();
                throw ex;
            }
            return mark;
        }

        #endregion

        #region 校验

        /// <summary>
        /// 1、过账数据不允许删除 2、库存是否锁定
        /// </summary>
        /// <param name="entities"></param>
        /// <param name="error_message"></param>
        /// <returns></returns>
        public bool ScanDataDeleteValidation(List<PP_OverReceive> entities, out string error_message)
        {
            bool isPass = true;
            error_message = "";
            foreach (PP_OverReceive po in entities)
            {
                if (po.IsPosted == true)
                {
                    error_message = "ui.Message.PODeletePostingWarning"; // "已过账数据不允许删除!";
                    isPass = false;
                    break;
                }

                if (po.ExamineStatus > 0)
                {
                    error_message = "已审核数据不允许删除"; // "已过账数据不允许删除!";
                    isPass = false;
                    break;
                }

                // 入库校验
                //if(_stockApp.CheckStockLocked(po.ItemCode, po.OutBinLocationCode, po.OutRegionCode))
                //{
                //    //throw new Exception("Common.error", new Exception("Common.StockIsLocked"));
                //    error_message = "Common.StockIsLocked"; //库存锁定，不允许出入库
                //    return false;
                //}
            }
            return isPass;
        }

        #endregion

        #region 超额领料过账

        public bool DoPost(List<PP_OverReceive> entities, string operater, out string error_message)
        {
            error_message = "过账成功";
            try
            {
                //SAP过账
                foreach (var entity in entities)
                {
                    int Line = 0;
                    List<ZFGWMS004> zFGWMs = new List<ZFGWMS004>();
                    var overReceiveNo = entity.OverReceiveNo;
                    var detailList = detailApp.GetList(w => w.OverReceiveNo == entity.OverReceiveNo).ToList();
                    foreach (var item in detailList)
                    {
                        //获取库位
                        var location = new MD_BinLocationApp().GetList(w => w.WhsCode == item.OutWarehouse).ToList().FirstOrDefault();
                        //调接口减本地库存
                        if (item.SpecialInventory.ToUpper() == "E")
                        {
                            var mark = new MD_StockApp().StockOut(item.ComponentCode, location?.BinLocationCode, item.SalesOrderNo, Decimal.ToInt32(item.SalesOrderLineNo ?? 0), item.DemandQty, operater, out error_message);
                            //if (!mark) return mark;
                        }
                        else
                        {
                            var mark = new MD_StockApp().StockOut(item.ComponentCode, location?.BinLocationCode, item.DemandQty, operater, out error_message);
                            //if (!mark) return mark;
                        }
                        //加库存
                        var _location = new MD_BinLocationApp().GetList(w => w.WhsCode == item.DeliverLocation).ToList().FirstOrDefault();
                        var flg = new MD_StockApp().StockIn(
                            new Entity.MD.MD_Stock
                            {
                                ItemCode = item.ComponentCode,
                                ItemName = item.MaterialName,
                                Qty = item.DemandQty,
                                Unit = item.ComponentUnit,
                                RegionCode = _location?.RegionCode,
                                RegionName = _location?.RegionName,
                                BinLocationCode = _location?.BinLocationCode,
                                BinLocationName = _location?.BinLocationName,
                                WhsCode = _location?.WhsCode,
                                WhsName = _location?.WhsName,
                                SaleNum = item.SalesOrderNo,
                                SaleLine = Decimal.ToInt32(item.SalesOrderLineNo ?? 0),
                                AssessType = item.AssessmentType,
                                SpecialStock = item.SpecialInventory,
                            }, operater, out error_message);
                        //if (!flg) return flg;
                        error_message = "过账成功";

                        Line++;
                        item.OverReceiveLineNo = Line;

                        zFGWMs.Add(new ZFGWMS004
                        {
                            ZNUM = Line,
                            AUFNR  = item.ProductionOrderNo,
                            MATNR = item.ComponentCode,
                            WERKS = item.FactoryCode ?? "2002",
                            BWART = item.MovementType,
                            MENGE = item.DemandQty,
                            MEINS = item.ComponentUnit,
                            LGORT = item.OutWarehouse,
                            UMLGO = item.DeliverLocation,
                            SOBKZ = item.SpecialInventory,
                            KDAUF = item.SalesOrderNo,
                            KDPOS = Decimal.ToInt32(item.SalesOrderLineNo ?? 0),
                            BWTAR = item.AssessmentType,
                            SGTXT = item.Remark,
                        });
                    }

                    var isPosted = false;
                    DateTime date = DateTime.Now;
                    //查询Sap账期时间
                    DateTime ManualPostTime = new SAPApp().GetSAPpostTime(Convert.ToDateTime(entity.ManualPostTime));
                    var result = sapApp.ZFGWMS004("001", overReceiveNo, entity.MovementType == "261" ? "1" : "2", overReceiveNo, ManualPostTime, zFGWMs, out isPosted, out error_message);

                    if (result != null && result.Count > 0)
                    {
                        List<PP_OverReceiveDetail> querydetails = new List<PP_OverReceiveDetail>();
                        foreach (var sap in result)
                        {
                            var querydetail = detailList.FirstOrDefault(x => x.OverReceiveNo == sap.DocNum && x.OverReceiveLineNo == sap.line);
                            if (querydetail != null)
                            {
                                querydetail.IsPosted = true;
                                querydetail.PostUser = operater;
                                querydetail.PostTime = date;
                                querydetail.ManualPostTime = ManualPostTime;
                                querydetail.MUser = operater;
                                querydetail.SapDocNum = sap.sapDocNum;
                                querydetail.SapLine = sap.sapline;
                                querydetails.Add(querydetail);
                            }
                        }

                        entity.IsPosted = true;
                        entity.PostUser = operater;
                        entity.PostTime = date;
                        entity.MUser = operater;
                        entity.MTime = date;
                        entity.ManualPostTime = ManualPostTime;

                        this.DbContext.Ado.BeginTran();
                        Update(entity);
                        detailApp.Update(querydetails);
                        this.DbContext.Ado.CommitTran();
                    }
                    else
                    {
                        error_message = "单号：" + overReceiveNo + "，过账失败，失败原因：" + error_message;
                        return isPosted;
                    }
                }
            }
            catch (Exception ex)
            {
                this.DbContext.Ado.RollbackTran();
                error_message = ex.InnerException?.Message ?? ex.Message;
                return false;
            }
            return true;
        }

        #endregion
    }
}
