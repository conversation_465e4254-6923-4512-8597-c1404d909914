<?xml version="1.0" encoding="UTF-8"?>

<!-- This file contains job definitions in schema version 2.0 format -->

<job-scheduling-data xmlns = "http://quartznet.sourceforge.net/JobSchedulingData" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" version="2.0">

  <processing-directives>
    <overwrite-existing-data>true</overwrite-existing-data>
  </processing-directives>

  <schedule>
    <!--自动发送邮件通知任务-->
    <job>
      <name>MessageSendByEMailJob</name>
      <description>自动发送邮件通知任务</description>
      <job-type>HZ.WMS.WebJob.MessageSendByEMailJob,HZ.WMS.WebJob</job-type>
      <durable>true</durable>
      <recover>false</recover>
    </job>
    <trigger>
      <cron>
        <name>MessageSendByEMailJobTrigger</name>
        <job-name>MessageSendByEMailJob</job-name>
        <misfire-instruction>SmartPolicy</misfire-instruction>
        <!--cron表达式: 从0分开始,每10分钟执行一次,
            考虑到抓取比较慢,要保证每次任务有足够时间执行,
            时间间隔尽量设置的久一点-->
        <cron-expression>0 0/10 * * * ? *</cron-expression>
      </cron>d
    </trigger>

    <!--生产订单序列号分配任务配置-->
    <job>
      <name>ProductionSerialNoAssignJob</name>
      <description>生产订单序列号分配任务</description>
      <job-type>HZ.WMS.WebJob.ProductionSerialNoAssignJob,HZ.WMS.WebJob</job-type>
      <durable>true</durable>
      <recover>false</recover>
    </job>
    <trigger>
      <cron>
        <name>ProductionSerialNoAssignJobTrigger</name>
        <job-name>ProductionSerialNoAssignJob</job-name>
        <misfire-instruction>SmartPolicy</misfire-instruction>
        <!--cron表达式: 从0分开始,每10分钟执行一次,
            考虑到抓取比较慢,要保证每次任务有足够时间执行,
            时间间隔尽量设置的久一点
            -->
        <cron-expression>0 0/10 * * * ? *</cron-expression>
      </cron>
    </trigger>
  </schedule>
</job-scheduling-data>