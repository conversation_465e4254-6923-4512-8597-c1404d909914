using System;
using System.ComponentModel;
using HZ.Core.Utilities;
using HZ.WMS.Entity;

namespace AOS.WMS.Entity.CableProduce
{
    public class Cable_DeliveryListReq : BaseEntity
    {
        
        /// <summary>
        /// 销售单号
        /// </summary>
        [Description("销售单号")]
        public string SapNo { get; set; }

        /// <summary>
        /// 销售行号
        /// </summary>
        [Description("销售行号")]
        public int? SapLine { get; set; }

        /// <summary>
        /// 合同号
        /// </summary>
        [Description("合同号")]
        public string ContractNo { get; set; }
        
        /// <summary>
        /// 装配时间
        /// </summary>
        [Description("交货时间")]
        public DateTime[] DeliveryDate { get; set; }
        
        /// <summary>
        /// 创建时间
        /// </summary>
        [Description("创建时间")]
        public DateTime[] CreateDate { get; set; }

        /// <summary>
        /// 开始交货时间
        /// </summary>
        public DateTime? GetStartDeliveryDate()
        {
            return DateUtil.QueryDateTimesFormat(DeliveryDate)[0];
        }

        /// <summary>
        /// 结束交货时间
        /// </summary>
        public DateTime? GetEndDeliveryDate()
        {
            return DateUtil.QueryDateTimesFormat(DeliveryDate)[1];
        }

        /// <summary>
        /// 开始创建时间
        /// </summary>
        public DateTime? GetStartCreateDate()
        {
            return DateUtil.QueryDateTimesFormat(CreateDate)[0];
        }

        /// <summary>
        /// 结束创建时间
        /// </summary>
        public DateTime? GetEndCreateDate()
        {
            return DateUtil.QueryDateTimesFormat(CreateDate)[1];
        }
    }
}