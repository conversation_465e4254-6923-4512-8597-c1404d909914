using System.ComponentModel;
using SqlSugar;
//Chloe框架


namespace HZ.WMS.Entity.Sys
{
	/// <summary>
    /// 
    /// </summary>
    [SugarTable("Sys_Log")]
    public class Sys_Log:BaseEntity
    {
                /// <summary>
        /// 系统日志ID
        /// </summary>
        [Description("系统日志ID")]
        [SugarColumn(IsPrimaryKey = true)]
        public string LogID {get;set;}
        /// <summary>
        /// API路径
        /// </summary>
        [Description("API路径")]
        public string ApiUrl {get;set;}
        /// <summary>
        /// 日志类型[1: 业务日志 2:Debug日志]
        /// </summary>
        [Description("日志类型[1: 业务日志 2:Debug日志]")]
        public int? LogType {get;set;}
        /// <summary>
        /// 功能分类
        /// </summary>
        [Description("功能分类")]
        public int? ApiType {get;set;}
        /// <summary>
        /// 功能模块
        /// </summary>
        [Description("功能模块")]
        public string ApiModule {get;set;}
        /// <summary>
        /// API功能描述
        /// </summary>
        [Description("API功能描述")]
        public string ApiDescription {get;set;}
        /// <summary>
        /// 操作用户
        /// </summary>
        [Description("操作用户")]
        public string OperateUser {get;set;}
        /// <summary>
        /// 客户端主机名
        /// </summary>
        [Description("客户端主机名")]
        public string ClientHost {get;set;}
        /// <summary>
        /// 客户端IP
        /// </summary>
        [Description("客户端IP")]
        public string ClientIP {get;set;}
        /// <summary>
        /// 客户端浏览器
        /// </summary>
        [Description("客户端浏览器")]
        public string ClientBrowser {get;set;}
        /// <summary>
        /// 客户端操作系统
        /// </summary>
        [Description("客户端操作系统")]
        public string ClientOS {get;set;}
        /// <summary>
        /// 请求方法[GET,POST,DELETE]
        /// </summary>
        [Description("请求方法[GET,POST,DELETE]")]
        public string RequestType {get;set;}
        /// <summary>
        /// 请求参数
        /// </summary>
        [Description("请求参数")]
        public string RequestParms {get;set;}
        /// <summary>
        /// 响应数据
        /// </summary>
        [Description("响应数据")]
        public string ResponseData {get;set;}
        /// <summary>
        /// 成功/失败
        /// </summary>
        [Description("成功/失败")]
        public string Result {get;set;}

    }
}

