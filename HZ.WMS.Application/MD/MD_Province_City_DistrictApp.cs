using HZ.WMS.Entity.MD;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Application.MD
{
    /// <summary>
    /// 省-市-区/县
    /// </summary>
    public class MD_Province_City_DistrictApp : BaseApp<MD_Province_City_District>
    {
        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public MD_Province_City_DistrictApp() : base(){}
        
        #endregion
    }
}
