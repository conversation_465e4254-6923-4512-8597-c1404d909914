using System;
using System.ComponentModel;
//Chloe框架


namespace HZ.WMS.Entity.MM
{
    public class RPT_StockAdjustHistory
    {
        /// <summary>
        /// 物料编号
        /// </summary>
        [Description("物料编号")]
        public string ItemCode { get; set; }
        /// <summary>
        /// 物料名称
        /// </summary>
        [Description("物料名称")]
        public string ItemName { get; set; }

        /// <summary>
        /// 调整数量
        /// </summary>
        [Description("调整数量")]
        public decimal? AdjustQty { get; set; }
        /// <summary>
        /// 调整单位
        /// </summary>
        [Description("调整单位")]
        public string AdjustUnit { get; set; }
        /// <summary>
        /// 区域编号
        /// </summary>
        [Description("区域编号")]
        public string AdjustRegion { get; set; }
        /// <summary>
        /// 调整方向
        /// </summary>
        [Description("调整方向")]
        public string AdjustType { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        [Description("创建时间")]
        public DateTime? CTime { get; set; }
    }
}
