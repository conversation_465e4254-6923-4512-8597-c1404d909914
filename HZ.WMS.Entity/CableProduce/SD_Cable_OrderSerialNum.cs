using System.ComponentModel;
using HZ.WMS.Entity;
using SqlSugar;

namespace AOS.OMS.Entity.Sale
{
    /// <summary>
    /// 订单序列号类
    /// </summary>
    public class SD_Cable_OrderSerialNum : BaseEntity
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Description("主键ID")]
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 订单编号
        /// </summary>
        [Description("订单编号")]
        public string OrderNum { get; set; }

        /// <summary>
        /// 合同号
        /// </summary>
        [Description("合同号")]
        public string ContractNum { get; set; }

        /// <summary>
        /// 序列号
        /// </summary>
        [Description("序列号")]
        public string SerialNum { get; set; }

    }
}
