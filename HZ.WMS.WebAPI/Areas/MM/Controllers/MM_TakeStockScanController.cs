using SqlSugar;
using HZ.Core.Http;
using HZ.Core.Office;
using HZ.WMS.Application;
using HZ.WMS.Application.MD;
//
using HZ.WMS.Application.MM;
using HZ.WMS.Entity.MM;
using HZ.WMS.Entity.Sys;
using HZ.WMS.WebAPI.Controllers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;

namespace HZ.WMS.WebAPI.Areas.MM.Controllers
{
    public class MM_TakeStockScanController : ApiBaseController
    {
        private MM_TakeStockScanApp _app = new MM_TakeStockScanApp();

        #region 查询分页列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="pagination"></param>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]string keyword)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetPageList(page, x => string.IsNullOrEmpty(keyword)
                     || x.DocNum.Contains(keyword)
                     || x.BoxBarCode.Contains(keyword)
                     || x.BarCode.Contains(keyword)
                     || x.BatchNum.Contains(keyword)
                     || x.ItemCode.Contains(keyword)
                     || x.ItemName.Contains(keyword)
                     || x.ItmsGrpCode.Contains(keyword)
                     || x.ItmsGrpName.Contains(keyword)
                     || x.WhsCode.Contains(keyword)
                     || x.WhsName.Contains(keyword)
                     || x.RegionCode.Contains(keyword)
                     || x.RegionName.Contains(keyword)
                     || x.BinLocationCode.Contains(keyword)
                     || x.BinLocationName.Contains(keyword)
                     || x.Remark.Contains(keyword))?.ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 添加
        /// <summary>
        /// 添加111
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Add([FromBody]MM_TakeStockScan entity)
        {
            var result = new ResponseData();
            try
            {
                Sys_User currLoginUser = GetCurrentUser();
                entity.CUser = currLoginUser.LoginAccount;
                entity.MUser = currLoginUser.LoginAccount;
                entity.CTime = DateTime.Now;
                entity.MTime = DateTime.Now;
                result.Data = _app.Insert(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取详情
        /// <summary>
        /// 获取详情
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetEntity([FromUri]string ID)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.GetEntityByKey(ID);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Update([FromBody]MM_TakeStockScan entity)
        {
            var result = new ResponseData();
            try
            {
                entity.MUser = GetCurrentUser().LoginAccount;
                entity.MTime = DateTime.Now;
                result.Data = _app.Update(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除（包括批量）

        /// <summary>
        /// 删除
        /// </summary>
        /// <returns></returns>
        [HttpDelete]
        public IHttpActionResult Delete([FromBody]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.DeleteByIDS(ids, GetCurrentUser().LoginAccount);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion


        #region 导出

        /// <summary>
        /// 
        /// </summary>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] string keyword, [FromUri] DateTime[] dateTimes, [FromUri] bool? isPosted)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateTimes);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];
                var itemsData = _app.GetList(x => (x.CTime >= fromTime && x.CTime <= toTime) && (string.IsNullOrEmpty(keyword)
                                                                         || x.DocNum.Contains(keyword)
                                                                         || x.BarCode.Contains(keyword)
                                                                         || x.BatchNum.Contains(keyword)
                                                                         || x.ItemCode.Contains(keyword)
                                                                         || x.ItemName.Contains(keyword)
                                                                         || x.WhsCode.Contains(keyword)
                                                                         ) && (isPosted == null || x.IsPosted == isPosted)).Where(x => !x.IsDelete).ToList();


                List<ExcelColumn<MM_TakeStockScan>> columns = ExcelService.FetchDefaultColumnList<MM_TakeStockScan>();
                string[] ignoreField = new string[] {
                    "ScanID", "BoxBarCode","BatchNum" ,"PTime", "ItmsGrpCode", "ItmsGrpName","StockQty", "DiffQty",
                    "Unit","WhsName","RegionCode","RegionName", "BinLocationCode", "BinLocationName", "IsConfirm",
                    "PostUser", "PostTime", "SapDocNum","SapLine",
                    "SupplierName", "SAPmark","SAPmessage","IsDelete", "CUser", "CTime",
                    "MUser", "MTime", "DUser", "DTime"
                };

                List<ExcelColumn<MM_TakeStockScan>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<MM_TakeStockScan> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                columns.ForEach((column) =>
                {
                    if (column.ColumnName == "IsPosted")
                    {
                        column.Formattor = ExcelExportFormatter.IsOrNoFormatterForBoolean;
                    }
                });

                return ExportToExcelFile<MM_TakeStockScan>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        /// <summary>
        /// 盘点计划差异导出
        /// </summary>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFileCY([FromUri] string keyword, [FromUri] DateTime[] dateTimes, [FromUri]string state)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetListCY(keyword, dateTimes, state);
                //var itemsData = _app.GetList().ToList();
                List<ExcelColumn<MM_TakeStockScan>> columns = ExcelService.FetchDefaultColumnList<MM_TakeStockScan>();
                string[] ignoreField = new string[] { "IsDelete", "DTime", "DUser" ,"ScanID",
                    "ItmsGrpCode",
                    "ItmsGrpName",
                    "MUser",
                    "MTime",
                    "WhsCode",
                    "WhsName",
                    "BinLocationName","RegionCode"};

                List<ExcelColumn<MM_TakeStockScan>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<MM_TakeStockScan> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                columns.ForEach((column) =>
                {
                    if (column.ColumnName == "IsPosted")
                    {
                        column.Formattor = ExcelExportFormatter.IsOrNoFormatterForBoolean;
                    }
                });

                return ExportToExcelFile<MM_TakeStockScan>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }



        #endregion


        /// <summary>
        /// 盘点扫描查询显示列表
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <param name="dateValue"></param>
        /// <param name="isConfirm"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPage([FromUri]Pagination page, [FromUri]string keyword, [FromUri] DateTime[] dateValue, [FromUri] bool? isPosted)

        {
            var result = new ResponseData();
            try
            {
                Sys_User currentUser = GetCurrentUser();

                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];

                var itemsData = _app.GetPageList(page, x => (x.CTime >= fromTime && x.CTime <= toTime) && (string.IsNullOrEmpty(keyword)
                                                                         || x.DocNum.Contains(keyword)
                                                                         || x.BarCode.Contains(keyword)
                                                                         || x.BatchNum.Contains(keyword)
                                                                         || x.ItemCode.Contains(keyword)
                                                                         || x.ItemName.Contains(keyword)
                                                                         || x.WhsCode.Contains(keyword)
                                                                         ) && (isPosted == null || x.IsPosted == isPosted)).Where(x => !x.IsDelete).ToList();



                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 盘点差异确认查询显示列表
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <param name="dateValue"></param>
        /// <param name="isConfirm"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageChaYi([FromUri]Pagination page, [FromUri]string keyword, [FromUri] DateTime[] dateValue, [FromUri] string isConfirm)

        {
            var result = new ResponseData();
            try
            {
                Sys_User currentUser = GetCurrentUser();

                DateTime fromTime = DateTime.Now.AddDays(-100).Date;
                DateTime toTime = DateTime.Now.Date;

                if (dateValue != null && dateValue.Length >= 2)
                {
                    fromTime = dateValue[0];
                    toTime = dateValue[1];
                }
                var itemsData = _app.GetPageList(page, keyword, fromTime, toTime, isConfirm);
                //var itemsData = _app.GetPageList(page, x => (x.CTime >= fromTime && x.CTime <= toTime) && (string.IsNullOrEmpty(keyword)
                //                                || x.DocNum.Contains(keyword)
                //                                || x.BarCode.Contains(keyword)
                //                                || x.BatchNum.Contains(keyword)
                //                                || x.ItemCode.Contains(keyword)
                //                                || x.ItemName.Contains(keyword)
                //                                || x.WhsCode.Contains(keyword)
                //                                || x.BinLocationCode.Contains(keyword)
                //                                || x.BinLocationName.Contains(keyword)
                //                                )) 
                //                                .Where(x => !x.IsDelete && (isConfirm == null || x.IsConfirm == bool.Parse(isConfirm))).ToList();

                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 获取系统生成的单号
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetDocNum()
        {
            var result = new ResponseData();
            try
            {
                result.Data = base.GenerateDocNum(MM_FixedNumDef.DocType, MM_FixedNumDef.MM_TakeStockPlan);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 添加明细
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult AddList([FromBody]AddMM_TakeStockScanListDto dto)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";

                result.Data = _app.AddList(dto.Data, this.GetCurrentUser().LoginAccount);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        ///// <summary>
        ///// 获取详情
        ///// </summary>
        ///// <param name="docNum"></param>
        ///// <param name="barcode"></param>
        ///// <param name="binLocationCode"></param>
        ///// <returns></returns>
        //[HttpGet]
        //public IHttpActionResult MakeScaningEntity([FromUri]string docNum, [FromUri]string barcode, [FromUri]string binLocationCode = "")
        //{
        //    var result = new ResponseData();
        //    try
        //    {
        //        MD_StockApp MD_StockApp = new MD_StockApp();
        //        var stockEntity = MD_StockApp.GetFirstEntity(x => x.BarCode == barcode
        //              && (string.IsNullOrEmpty(binLocationCode) || x.BinLocationCode == binLocationCode));
        //        if (stockEntity == null)
        //        {
        //            result.Code = (int)WMSStatusCode.Failed;
        //            result.Message = "Common.barcodeIsUnusable";
        //        }
        //        else
        //        {
        //            MM_TakeStockPlanDetailedApp MM_TakeStockPlanDetailedApp = new MM_TakeStockPlanDetailedApp();
        //            int? count = MM_TakeStockPlanDetailedApp.GetAllList(
        //                  x => x.DocNum == docNum
        //                  && x.RegionCode == stockEntity.RegionCode
        //                  && x.ItemCode == stockEntity.ItemCode
        //                  && !x.IsDelete)?.Count();
        //            if (count > 0)
        //            {
        //                result.Data = stockEntity;
        //                result.Code = (int)WMSStatusCode.Success;
        //            }
        //            else
        //            {
        //                result.Code = (int)WMSStatusCode.Failed;
        //                result.Message = "Common.barcodeIsUnusable";
        //            }
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        result.Code = (int)WMSStatusCode.UnHandledException;
        //        result.Message = ex.InnerException?.Message ?? ex.Message;
        //    }
        //    return Json(result);
        //}



        public class AddMM_TakeStockScanListDto
        {
            public List<MM_TakeStockScan> Data { get; set; }
        }

        #region 导入

        /// <summary>
        /// 导入
        /// </summary>
        /// <param name="entitys">接收的导入集合</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult ImportExcelToData([FromBody]List<MM_TakeStockScanImport> entitys)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                if (_app.ImprotExcelToBaseData(entitys, GetCurrentUser().LoginAccount, out error_message))
                {
                    result.Data = true;
                    result.Code = (int)WMSStatusCode.Success;
                }
                else
                {
                    result.Data = false;
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
            return Json(result);
        }

        #endregion

        #region 导入模板

        /// <summary>
        /// 导入模板
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelModel()
        {
            var result = new ResponseData();
            try
            {
                var columns = ExcelService.FetchDefaultColumnList<MM_TakeStockScan>();
                string modelName = "盘点明细-导入模板";
                //排除不需要的列名称 字符串
                string[] ignoreField = new string[]
                {
                    "ScanID","Line", "BoxBarCode","BatchNum" ,"PTime", "ItemName", "ItmsGrpCode", "ItmsGrpName", "DiffQty",
                    "Unit","WhsName","RegionCode","RegionName", "BinLocationCode", "BinLocationName", "IsConfirm",
                    "IsPosted", "PostUser", "PostTime", "SapDocNum","SapLine","SupplierCode",
                    "SupplierName", "SAPmark","SAPmessage","IsDelete", "CUser", "CTime","PostTime","Remark",
                    "MUser", "MTime", "DUser", "DTime"
                };
                var itemsData = new List<MM_TakeStockScan>() { };
                var ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<MM_TakeStockScan> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                //需要格式转换的字段
                //columns.ForEach((column) =>
                //{
                //    if (column.ColumnName == "CTime")
                //    {
                //        column.Formattor = ExcelExportFormatter.DateTimeFormatter;
                //    }
                //});
                return ExportToExcelFile<MM_TakeStockScan>(itemsData, columns, GetCurrentUser().UserName + "_" + modelName);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion


        #region 过账

        /// <summary>
        /// 过账
        /// </summary>
        /// <param name="entities">接收的集合</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult DoPost([FromBody]List<MM_TakeStockScan> entities)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                Sys_User currentUser = GetCurrentUser();
                bool bPost = _app.DoPost(entities, currentUser.LoginAccount, out error_message);
                if (!bPost)//过账失败==2000，前台处理过账失败后重新刷新界面信息
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                    result.MessageParam = 2010;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2000;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }

        #endregion
    }
}

