using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.MM.ViewModel
{
    /// <summary>
    /// 委外入库查询SRM报检单信息
    /// </summary>
    public class MM_WarehousingForSRMInspection_View
    {
        /// <summary>
        /// 报检单号
        /// </summary>
        [Description("报检单号")]
        public string InspectionNo { get; set; }

        /// <summary>
        /// 报检单行号
        /// </summary>
        [Description("报检单行号")]
        public int InspectionLine { get; set; }

        /// <summary>
        /// 采购订单编号
        /// </summary>
        [Description("采购订单编号")]
        public string OrderNo { get; set; }

        /// <summary>
        /// 采购订单行号
        /// </summary>
        [Description("采购订单行号")]
        public decimal? OrderLine { get; set; }

        /// <summary>
        /// 物料编号
        /// </summary>
        [Description("物料编号")]
        public string ItemCode { get; set; }

        /// <summary>
        /// 物料名称
        /// </summary>
        [Description("物料名称")]
        public string ItemName { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量")]
        public decimal? InspectionQty { get; set; }

        /// <summary>
        /// 仓库编号
        /// </summary>
        [Description("仓库编号")]
        public string WhsCode { get; set; }

        /// <summary>
        /// 仓库名称
        /// </summary>
        [Description("仓库名称")]
        public string WhsName { get; set; }

        /// <summary>
        /// 销售订单号
        /// </summary>
        [Description("销售订单号")]
        public string SaleNo { get; set; }

        /// <summary>
        /// 销售订单行号
        /// </summary>
        [Description("销售订单行号")]
        public decimal? SaleLineNo { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Description("备注")]
        public string Remark { get; set; }

    }
}
