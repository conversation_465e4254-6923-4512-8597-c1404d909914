using System.ComponentModel;

namespace HZ.WMS.Entity.SAP.Req
{
    public class CheckMatnrPrice
    {
        [Description("消息类型")]
        public string ETYPE { get; set; }

        [Description("消息文本")]
        public string EMSG { get; set; }

        [Description("DATA_IN")]
        public Table001089[] DataIn { get; set; }

        [Description("DATA_OUT")]
        public Table001089[] DataOut { get; set; }

        public class Table001089
        {
            [Description("工厂")]
            public string WERKS { get; set; }

            [Description("物料编码")]
            public string MATNR { get; set; }

            [Description("行状态")]
            public string ETYPE { get; set; }

            [Description("行消息")]
            public string EMSG { get; set; }
        }
    }
}