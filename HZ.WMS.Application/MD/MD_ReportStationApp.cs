using System;
using System.Collections.Generic;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.MD.Import;
using System.Linq;
using HZ.WMS.Entity.Sys;

namespace HZ.WMS.Application.MD
{
    /// <summary>
    /// 报工站点
    /// </summary>
    public class MD_ReportStationApp : BaseApp<MD_ReportStation>
    {
        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public MD_ReportStationApp() : base() { }

        #endregion

        #region 导入

        /// <summary>
        /// 导入
        /// </summary>
        /// <param name="excelList">接收的集合</param>
        /// <param name="opUser">当前用户</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool ImportExcelToBaseData(List<MD_ReportStationImport> excelList, string opUser, out string error_message)
        {
            error_message = "";
            var flag = true;
            DateTime date = DateTime.Now;
            var entityList = new List<MD_ReportStation>();

            try
            {
                // 验证工作中心站点是否存在
                var workCenterStationApp = new MD_WorkCenterStationApp();
                
                foreach (var item in excelList)
                {
                    // 验证工作中心站点是否存在
                    if (!workCenterStationApp.ValidateWorkCenterStationExists(item.WorkCenterCode, item.StationCode))
                    {
                        error_message = $"工作中心编码'{item.WorkCenterCode}'和站点代码'{item.StationCode}'的组合不存在，请先在工作中心站点模块中添加";
                        return false;
                    }
                    
                    var entity = new MD_ReportStation
                    {
                        Id = Guid.NewGuid().ToString(),
                        WorkCenterName = item.WorkCenterName,
                        WorkCenterCode = item.WorkCenterCode,
                        ProcessNo = item.ProcessNo,
                        ProcessShortText = item.ProcessShortText,
                        StationCode = item.StationCode,
                        StationName = item.StationName,
                        Enable = item.Enable,
                        Remark = item.Remark,
                        IsDelete = false,
                        CUser = opUser,
                        CTime = date,
                        MUser = opUser,
                        MTime = date
                    };

                    entityList.Add(entity);
                }

                if (entityList.Count > 0)
                {
                    DbContext.Insertable(entityList).ExecuteCommand();
                }
            }
            catch (Exception ex)
            {
                flag = false;
                error_message = ex.Message;
            }

            return flag;
        }

        #endregion
        
        #region 验证方法
        
        /// <summary>
        /// 验证工作中心站点信息是否存在
        /// </summary>
        /// <param name="workCenterCode">工作中心编码</param>
        /// <param name="stationCode">站点代码</param>
        /// <param name="error">错误信息</param>
        /// <returns>验证结果</returns>
        public bool ValidateWorkCenterStationInfo(string workCenterCode, string stationCode, out string error)
        {
            error = string.Empty;
            var workCenterStationApp = new MD_WorkCenterStationApp();
            
            if (!workCenterStationApp.ValidateWorkCenterStationExists(workCenterCode, stationCode))
            {
                error = $"工作中心编码'{workCenterCode}'和站点代码'{stationCode}'的组合不存在，请先在工作中心站点模块中添加";
                return false;
            }
            
            return true;
        }
        
        #endregion

        #region 用户关联关系操作

        /// <summary>
        /// 添加报工站点并创建用户关联关系
        /// </summary>
        /// <param name="entity">报工站点实体</param>
        /// <param name="userIds">用户ID列表</param>
        /// <param name="opUser">操作用户</param>
        /// <returns>添加结果</returns>
        public bool InsertWithUserRelations(MD_ReportStation entity, List<string> userIds, string opUser)
        {
            try
            {
                DbContext.Ado.BeginTran();
                
                // 插入报工站点
                var station = Insert(entity);
                
                // 创建用户关联关系
                if (userIds != null && userIds.Count > 0)
                {
                    CreateStationUserRelations(station.Id, userIds, opUser);
                }
                
                DbContext.Ado.CommitTran();
                return true;
            }
            catch (Exception)
            {
                DbContext.Ado.RollbackTran();
                throw;
            }
        }

        /// <summary>
        /// 更新报工站点并更新用户关联关系
        /// </summary>
        /// <param name="entity">报工站点实体</param>
        /// <param name="userIds">用户ID列表</param>
        /// <param name="opUser">操作用户</param>
        /// <returns>更新结果</returns>
        public bool UpdateWithUserRelations(MD_ReportStation entity, List<string> userIds, string opUser)
        {
            try
            {
                DbContext.Ado.BeginTran();
                
                // 更新报工站点
                Update(entity);
                
                // 删除旧的用户关联关系
                DeleteStationUserRelations(entity.Id);
                
                // 创建新的用户关联关系
                if (userIds != null && userIds.Count > 0)
                {
                    CreateStationUserRelations(entity.Id, userIds, opUser);
                }
                
                DbContext.Ado.CommitTran();
                return true;
            }
            catch (Exception)
            {
                DbContext.Ado.RollbackTran();
                throw;
            }
        }

        /// <summary>
        /// 创建站点用户关联关系
        /// </summary>
        /// <param name="stationId">站点ID</param>
        /// <param name="userIds">用户ID列表</param>
        /// <param name="opUser">操作用户</param>
        private void CreateStationUserRelations(string stationId, List<string> userIds, string opUser)
        {
            var relationList = new List<MD_ReportStationUserRelation>();
            DateTime now = DateTime.Now;
            
            foreach (var userId in userIds)
            {
                var relation = new MD_ReportStationUserRelation
                {
                    Id = Guid.NewGuid().ToString(),
                    StationId = stationId,
                    UserId = userId,
                    IsDelete = false,
                    CUser = opUser,
                    CTime = now,
                    MUser = opUser,
                    MTime = now
                };
                
                relationList.Add(relation);
            }
            
            if (relationList.Count > 0)
            {
                var relationApp = new BaseApp<MD_ReportStationUserRelation>();
                relationApp.Insert(relationList);
            }
        }

        /// <summary>
        /// 删除站点用户关联关系
        /// </summary>
        /// <param name="stationId">站点ID</param>
        private void DeleteStationUserRelations(string stationId)
        {
            var relationApp = new BaseApp<MD_ReportStationUserRelation>();
            var relations = relationApp.GetList(t => t.StationId == stationId).ToList();
            
            if (relations.Count > 0)
            {
                relationApp.HardDelete(relations);
            }
        }

        /// <summary>
        /// 获取站点关联的用户列表
        /// </summary>
        /// <param name="stationId">站点ID</param>
        /// <returns>用户列表</returns>
        public List<Sys_User> GetStationUserRelations(string stationId)
        {
            var relationApp = new BaseApp<MD_ReportStationUserRelation>();
            var userApp = new BaseApp<Sys_User>();
            
            var relations = relationApp.GetList(t => t.StationId == stationId).ToList();
            var userIds = relations.Select(r => r.UserId).ToList();
            
            if (userIds.Count > 0)
            {
                return userApp.GetList(u => userIds.Contains(u.UserID)).ToList();
            }
            
            return new List<Sys_User>();
        }

        #endregion
    }
}
