using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Web;
using System.Web.Http;
using DevExpress.DataAccess.Sql;
using DevExpress.XtraReports.UI;
using HZ.Core.Helper;
using HZ.Core.Http;
using HZ.Core.Office;
using HZ.WMS.Application;
using HZ.WMS.Application.MD;
using HZ.WMS.Application.MM;
using HZ.WMS.Entity.MM;
using HZ.WMS.Entity.MM.Import;
using HZ.WMS.Entity.Sys;
using HZ.WMS.WebAPI.Controllers;

namespace HZ.WMS.WebAPI.Areas.MM.Controllers
{
    /// <summary>
    /// 物料标签
    /// </summary>
    public class MM_BarCodeController : ApiBaseController
    {
        private MM_BarCodeApp _app = new MM_BarCodeApp();
        private MD_LabelTempleteApp _labelTemplateApp = new MD_LabelTempleteApp();

        #region 查询分页列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="page">分页</param>
        /// <param name="dateValue">日期区间</param>
        /// <param name="keyword">参数</param>
        /// <param name="Remark">备注</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri] DateTime[] dateValue, [FromUri]string keyword, [FromUri]string Remark, [FromUri]string PrintTemplate)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormatTodayDate(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];
                page.Sort = "CTime desc,BarCode";
                //var a = "CTime desc,BarCode".Replace("ascending", "asc").Replace("descending", "desc");
                var itemsData = _app.GetPageList(page, x =>
                  (string.IsNullOrEmpty(Remark) || x.Remark.Contains(Remark)) &&
                  (string.IsNullOrEmpty(PrintTemplate) || x.PrintTemplate.Contains(PrintTemplate)) &&
                  (string.IsNullOrEmpty(keyword)
                    || x.BaseNum.Contains(keyword) || x.PartCode.Contains(keyword)
                    || x.BarCode.Contains(keyword) || x.BatchNum.Contains(keyword)
                    || x.ItemCode.Contains(keyword) || x.ItemName.Contains(keyword)
                    || x.CUser.Contains(keyword) || x.Unit.Contains(keyword))
                    && (x.CTime >= fromTime && x.CTime <= toTime)
                    )?.ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 添加

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="entity">参数</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Add([FromBody]MM_BarCode entity)
        {
            var result = new ResponseData();
            try
            {
                Sys_User currLoginUser = GetCurrentUser();
                var query = new List<MM_BarCode>();
                string objJson = JsonHelper.ObjectToJson(entity);
                //string BatchNum = base.GenerateDocNum(DocType.MD, DocFixedNumDef.BatchNo);
                for (int i= 0;i<entity.printQty;i++)
                {
                    MM_BarCode tmp = JsonHelper.JsonToObject<MM_BarCode>(objJson);
                    tmp.BarID = Guid.NewGuid().ToString();
                    //tmp.BarCode = base.GenerateDocNum(DocType.MM, DocFixedNumDef.MM_BarCode);
                    tmp.BatchNum = "";// BatchNum;
                    tmp.CUser = currLoginUser.LoginAccount;
                    tmp.CTime = DateTime.Now;
                    tmp.IsDelete = false;
                    tmp.PrintTemplate = "";
                    query.Add(tmp);
                }
                result.Data = _app.Insert(query);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="entity">参数</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Update([FromBody]MM_BarCode entity)
        {
            var result = new ResponseData();
            try
            {
                Sys_User currLoginUser = GetCurrentUser();
                var query = new List<MM_BarCode>();
                string objJson = JsonHelper.ObjectToJson(entity);
                for (int i = 0; i < entity.printQty; i++)
                {
                    MM_BarCode tmp = JsonHelper.JsonToObject<MM_BarCode>(objJson);
                    tmp.MUser = currLoginUser.LoginAccount;
                    tmp.MTime = DateTime.Now;
                    query.Add(tmp);
                }
                result.Data = _app.Update(query);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="ids">ID数组</param>
        /// <returns></returns>
        [HttpDelete]
        public IHttpActionResult Delete([FromBody]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.DeleteByKeys(ids, GetCurrentUser().LoginAccount);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 标签打印

        /// <summary>
        /// 标签打印
        /// </summary>
        /// <param name="barcodes">出场编号数组</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Print(string[] barcodes)
        {
            var result = new ResponseData();
            try
            {
                string templateCode = "物料标签.repx";

                //MD_LabelTemplete template = _labelTemplateApp.GetFirstEntity(x => x.TempleteType == 1);     //采购标签
                var template = _app.GetList(x => barcodes.Contains(x.BarCode))?.ToList();
                // Create a report instance.
                XtraReport report =
                    XtraReport.FromFile(HttpContext.Current.Server.MapPath("~/") + ConfigurationManager.AppSettings["LabelTemplateUploadPath"]?.ToString() + templateCode, true);

                SqlDataSource dataSource = report.DataSource as SqlDataSource;
                QueryParameter parameter = new QueryParameter()
                {
                    Name = "paramBarCode",
                    Type = typeof(List<string>),
                    Value = barcodes.ToList()
                };
                dataSource.Queries[0].Parameters[0] = parameter;
                //dataSource.Fill();

                return base.PrintToPDF(report);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 标签打印物料编号
        /// </summary>
        /// <param name="itemcodes">物料编号数组</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult PrintItemCode(string[] itemcodes)
        {
            var result = new ResponseData();
            try
            {
                string templateCode = "物料标签(物料).repx";
                //MD_LabelTemplete template = _labelTemplateApp.GetFirstEntity(x => x.TempleteType == 1);     //采购标签
                //var template = _app.GetList(x => itemcodes.Contains(x.ItemCode))?.ToList();
                // Create a report instance.
                XtraReport report =
                    XtraReport.FromFile(HttpContext.Current.Server.MapPath("~/") + ConfigurationManager.AppSettings["LabelTemplateUploadPath"]?.ToString() + templateCode, true);

                SqlDataSource dataSource = report.DataSource as SqlDataSource;
                QueryParameter parameter = new QueryParameter()
                {
                    Name = "paramItemCode",
                    Type = typeof(List<string>),
                    Value = itemcodes.ToList()
                };
                dataSource.Queries[0].Parameters[0] = parameter;
                //dataSource.Fill();

                return base.PrintToPDF(report);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        [HttpPost]
        public IHttpActionResult PrintItemCode_2(string[] itemcodes)
        {
            var result = new ResponseData();
            try
            {
                string templateCode = "物料标签(无条码).repx";
                //MD_LabelTemplete template = _labelTemplateApp.GetFirstEntity(x => x.TempleteType == 1);     //采购标签
                //var template = _app.GetList(x => itemcodes.Contains(x.ItemCode))?.ToList();
                // Create a report instance.
                XtraReport report =
                    XtraReport.FromFile(HttpContext.Current.Server.MapPath("~/") + ConfigurationManager.AppSettings["LabelTemplateUploadPath"]?.ToString() + templateCode, true);

                SqlDataSource dataSource = report.DataSource as SqlDataSource;
                QueryParameter parameter = new QueryParameter()
                {
                    Name = "paramItemCode",
                    Type = typeof(List<string>),
                    Value = itemcodes.ToList()
                };
                dataSource.Queries[0].Parameters[0] = parameter;
                //dataSource.Fill();

                return base.PrintToPDF(report);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 导出

        /// <summary>
        /// 导出
        /// </summary>
        /// <param name="keyword">参数</param>
        /// <param name="Remark">备注</param>
        /// <param name="dateTimes">日期区间</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] string keyword, [FromUri]string Remark,  [FromUri]string PrintTemplate,[FromUri] DateTime[] dateTimes)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateTimes);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];

                var itemsData = _app.GetList(x =>
                (string.IsNullOrEmpty(Remark) || x.Remark.Contains(Remark)) &&
                 (string.IsNullOrEmpty(PrintTemplate) || x.PrintTemplate.Contains(PrintTemplate)) &&
                (string.IsNullOrEmpty(keyword)
                    || x.BaseNum.Contains(keyword) || x.PartCode.Contains(keyword)
                    || x.BarCode.Contains(keyword) || x.BatchNum.Contains(keyword)
                    || x.ItemCode.Contains(keyword) || x.ItemName.Contains(keyword)
                    || x.CUser.Contains(keyword) || x.Unit.Contains(keyword))
                    && x.CTime >= fromTime && x.CTime <= toTime
                ).ToList();

                var columns = ExcelService.FetchDefaultColumnList<MM_BarCode>();
                string[] ignoreField = new string[] {
                    "IsDelete",
                    "DTime",
                    "DUser",
                    "BarID",
                    "MUser",
                    "MTime"
                };

                var ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (var userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                return ExportToExcelFile<MM_BarCode>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #region 导入

        /// <summary>
        /// 导入序列号
        /// </summary>
        /// <param name="entitys">接收的集合</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult ImportExcelToData([FromBody]List<MM_BarCodeImport> entitys)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                if (_app.ImprotExcelToBaseData(entitys, GetCurrentUser().LoginAccount, out error_message))
                {
                    result.Data = true;
                    result.Code = (int)WMSStatusCode.Success;
                }
                else
                {
                    result.Data = false;
                    result.Code = (int)WMSStatusCode.Failed;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
            return Json(result);
        }



        /// <summary>
        /// 导入物料
        /// </summary>
        /// <param name="entitys">接收的集合</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult ImportExcelToDataItem([FromBody]List<MM_BarCodeImport> entitys)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                if (_app.ImprotExcelToBaseDataItem(entitys, GetCurrentUser().LoginAccount, out error_message))
                {
                    result.Data = true;
                    result.Code = (int)WMSStatusCode.Success;
                }
                else
                {
                    result.Data = false;
                    result.Code = (int)WMSStatusCode.Failed;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
            return Json(result);
        }

        #endregion

        #region 导入模板

        /// <summary>
        /// 导入模板
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelModel()
        {
            var result = new ResponseData();
            try
            {
                var columns = ExcelService.FetchDefaultColumnList<MM_BarCode>();
                string modelName = "物料标签-序列号导入模板";
                //排除不需要的列名称 字符串
                string[] ignoreField = new string[]
                {
                    "BarID","BatchNum", "BaseLine", "Unit", "printQty","ItemName","Unit","printQty","PrintTemplate",
                    "IsDelete", "CUser","MUser", "MTime", "DUser", "DTime","Qty"
                };
                var itemsData = new List<MM_BarCode>()
                {
                    new MM_BarCode(){}
                };
                var ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (var userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                return ExportToExcelFile<MM_BarCode>(itemsData, columns, GetCurrentUser().UserName + "_" + modelName);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }


        #endregion

        #region 导入物料模板

        /// <summary>
        /// 导入物料模板
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelModelItem()
        {
            var result = new ResponseData();
            try
            {
                var columns = ExcelService.FetchDefaultColumnList<MM_BarCodeIm>();
                string modelName = "物料标签-导入模板";
                //排除不需要的列名称 字符串
                string[] ignoreField = new string[]
                {
                    "IsDelete", "CUser","MUser", "MTime", "DUser", "DTime"
                };
                var itemsData = new List<MM_BarCodeIm>()
                {
                    new MM_BarCodeIm(){}
                };
                var ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (var userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                return ExportToExcelFile<MM_BarCodeIm>(itemsData, columns, GetCurrentUser().UserName + "_" + modelName);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }


        #endregion

    }
}

