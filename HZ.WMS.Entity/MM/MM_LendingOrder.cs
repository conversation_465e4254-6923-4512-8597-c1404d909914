using SqlSugar;
using System;
using System.ComponentModel;

namespace HZ.WMS.Entity.MM
{
    /// <summary>
    /// 借出单
    /// </summary>
    public class MM_LendingOrder : BaseEntity
    {
        /// <summary>
        /// 主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Description("ID")]
        public string ID { get; set; }

        /// <summary>
        /// 借出单号
        /// </summary>
        [Description("借出单号")]
        public string DocNum { get; set; }

        /// <summary>
        /// 状态 0:未审核 1：审核中 2：已审核 3：已取消
        /// </summary>
        [Description("状态")]
        public string Status { get; set; }

        /// <summary>
        /// 交易对象 1:人员 2：客户 3：供应商
        /// </summary>
        [Description("交易对象")]
        public int? TradingPartners { get; set; }

        /// <summary>
        /// 交易对象名称
        /// </summary>
        [Description("交易对象名称")]
        public string TradingPartnersName { get; set; }

        /// <summary>
        /// 对象编号
        /// </summary>
        [Description("对象编号")]
        public string PartnersNum { get; set; }

        /// <summary>
        /// 对象全称
        /// </summary>
        [Description("对象全称")]
        public string PartnersName { get; set; }

        /// <summary> 
        /// "审核人
        /// </summary> 
        [Description("审核人")]
        public string AuditUser { get; set; }

        /// <summary> 
        /// "审核日期
        /// </summary> 
        [Description("审核日期")]
        public DateTime? AuditDate { get; set; }


        /// <summary>
        /// 办理人员编号
        /// </summary>
        [Description("办理人员编号")]
        public string HandlenCode { get; set; }

        /// <summary>
        /// 办理人员
        /// </summary>
        [Description("办理人员")]
        public string HandlenName { get; set; }



        /// <summary> 
        /// 取消人
        /// </summary> 
        [Description("取消人")]
        public string RejectUser { get; set; }

        /// <summary> 
        /// 取消日期
        /// </summary> 
        [Description("取消日期")]
        public DateTime? RejectDate { get; set; }

        /// <summary> 
        /// 是否过账 1:已过帐 0：未过账
        /// </summary> 
        [Description("是否过账")]
        public bool? IsPosted { get; set; }

        /// <summary> 
        /// 过账人
        /// </summary> 
        [Description("过账人")]
        public string PostUser { get; set; }

        /// <summary> 
        /// 过账时间
        /// </summary> 
        [Description("过账时间")]
        public DateTime? PostTime { get; set; }

        /// <summary>
        /// 手动过账时间
        /// </summary>
        [Description("手动过账时间")]
        public DateTime? ManualPostTime { get; set; }

        /// <summary>
        /// 借出单类型 0：同步SAP ；1:不同步SAP；2：期初 3:OA同步来的
        /// </summary>
        [Description("借出单类型")]
        public string LendingType { get; set; }

        /// <summary>
        /// SAP过账标示 S 成功,E 错误,W 警告,I 信息,A 中断
        /// </summary>
        [Description("SAP过账标示")]
        public string SAPmark { get; set; }

        /// <summary>
        /// SAP过账消息
        /// </summary>
        [Description("SAP过账消息")]
        public string SAPmessage { get; set; }
    }
}