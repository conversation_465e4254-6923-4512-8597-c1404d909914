using SqlSugar;
using HZ.WMS.Entity.MM;
using HZ.WMS.Entity.PP;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Application.MM
{
    /// <summary>
    /// 返修
    /// </summary>
    public class MM_SupplierRepairViewApp : BaseApp<MM_SupplierRepair_View>
    {
        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public MM_SupplierRepairViewApp() : base(){ }


        #endregion


    }
}
