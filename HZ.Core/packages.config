<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="BouncyCastle" version="1.8.5" targetFramework="net45" />
  <package id="MailKit" version="2.2.0" targetFramework="net45" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="6.0.0" targetFramework="net462" />
  <package id="MimeKit" version="2.2.0" targetFramework="net45" />
  <package id="Newtonsoft.Json" version="12.0.2" targetFramework="net45" />
  <package id="NLog" version="5.2.7" targetFramework="net46" />
  <package id="NPOI" version="2.5.6" targetFramework="net46" />
  <package id="Oracle.ManagedDataAccess" version="21.18.0" targetFramework="net462" />
  <package id="SharpZipLib" version="1.3.3" targetFramework="net46" />
  <package id="Portable.BouncyCastle" version="1.8.9" targetFramework="net46" />
  <package id="System.Buffers" version="4.6.0" targetFramework="net462" />
  <package id="System.Text.Json" version="6.0.10" targetFramework="net462" />
  <package id="System.Formats.Asn1" version="8.0.1" targetFramework="net462" />
  <package id="System.Memory" version="4.6.0" targetFramework="net462" />
  <package id="System.Numerics.Vectors" version="4.6.0" targetFramework="net462" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.1.0" targetFramework="net462" />
  <package id="System.Text.Encodings.Web" version="6.0.0" targetFramework="net462" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net462" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net462" />
</packages>