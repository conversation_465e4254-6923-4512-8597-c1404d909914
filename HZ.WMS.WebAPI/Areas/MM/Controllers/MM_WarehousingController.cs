using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;
using HZ.Core.Http;
using HZ.Core.Office;
using HZ.WMS.Application;
using HZ.WMS.Application.MM;
using HZ.WMS.Entity.MM;
using HZ.WMS.Entity.MM.Parameters;
using HZ.WMS.Entity.MM.ViewModel;
using HZ.WMS.Entity.Sys;
using HZ.WMS.WebAPI.Controllers;
using static HZ.WMS.Application.MM.MM_WarehousingApp;

namespace HZ.WMS.WebAPI.Areas.MM.Controllers
{
    /// <summary>
    /// 委外入库
    /// </summary>
    public class MM_WarehousingController : ApiBaseController
    {
        #region 初始化

        private MM_WarehousingApp _app = new MM_WarehousingApp();
        private MM_WarehousingDetailApp _detailApp = new MM_WarehousingDetailApp();

        #endregion

        #region PC

        #region 查询分页列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="page">分页</param>
        /// <param name="keyword">参数</param>
        /// <param name="dateValue">日期区间</param>
        /// <param name="isPosted">是否过账</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]string keyword,
            [FromUri]string Supplier, [FromUri]string InspectionNum, [FromUri]string Item, 
            [FromUri]string BaseNum, [FromUri] DateTime[] dateValue, [FromUri] bool? isPosted,
             [FromUri] DateTime[] PostdateValue, [FromUri] DateTime[] ManualPostdateValue
            )
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];


                var querDateTimes1 = FormatProcessor.QueryDateTimesFormat(ManualPostdateValue);
                DateTime fromManualTime = querDateTimes1[0];
                DateTime toManualTime = querDateTimes1[1];
                if (ManualPostdateValue.Length == 0)
                {
                    toManualTime = toManualTime.AddYears(1);
                }

                var querDateTimes2 = FormatProcessor.QueryDateTimesFormat(PostdateValue);
                DateTime fromPostTime = querDateTimes2[0];
                DateTime toPostTime = querDateTimes2[1];
                var itemsData = new List<MM_Warehousing>();
                if (PostdateValue.Length > 0)
                {
                    itemsData = _app.GetPageList(page, t => (string.IsNullOrEmpty(keyword)
                        || t.DocNum.Contains(keyword)
                        || t.Remark.Contains(keyword) || t.WhsCode.Contains(keyword)
                        || t.WhsName.Contains(keyword)
                        || t.CUser.Contains(keyword))
                        && (string.IsNullOrEmpty(Supplier) || t.SupplierCode.Contains(Supplier) || t.SupplierName.Contains(Supplier))
                        && (string.IsNullOrEmpty(InspectionNum) || t.InspectionNum.Contains(InspectionNum))
                        && (string.IsNullOrEmpty(Item) || t.ItemCode.Contains(Item) || t.ItemName.Contains(Item))
                        && (string.IsNullOrEmpty(BaseNum) || t.BaseNum.Contains(BaseNum))
                        && (t.CTime >= fromTime && t.CTime <= toTime)
                        && (t.ManualPostTime >= fromManualTime && t.ManualPostTime <= toManualTime)
                        && (t.PostTime >= fromPostTime && t.PostTime <= toPostTime)
                        && (isPosted == null || t.IsPosted == isPosted)).ToList();
                }
                else
                {
                    itemsData = _app.GetPageList(page, t => (string.IsNullOrEmpty(keyword)
                   || t.DocNum.Contains(keyword)
                   || t.Remark.Contains(keyword) || t.WhsCode.Contains(keyword)
                   || t.WhsName.Contains(keyword)
                   || t.CUser.Contains(keyword))
                   && (string.IsNullOrEmpty(Supplier) || t.SupplierCode.Contains(Supplier) || t.SupplierName.Contains(Supplier))
                   && (string.IsNullOrEmpty(InspectionNum) || t.InspectionNum.Contains(InspectionNum))
                   && (string.IsNullOrEmpty(Item) || t.ItemCode.Contains(Item) || t.ItemName.Contains(Item))
                   && (string.IsNullOrEmpty(BaseNum) || t.BaseNum.Contains(BaseNum))
                   && (t.CTime >= fromTime && t.CTime <= toTime)
                   && (t.ManualPostTime >= fromManualTime && t.ManualPostTime <= toManualTime)
                   && (isPosted == null || t.IsPosted == isPosted)).ToList();
                }
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 查询子表的分页列表

        /// <summary>
        /// 查询子表的分页列表
        /// </summary>
        /// <param name="page">分页</param>
        /// <param name="DocNum">单号</param>
        /// <param name="BaseNum">采购单号</param>
        /// <param name="BaseLine">采购单行号</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetDetailedPageList([FromUri]Pagination page, [FromUri]string DocNum,[FromUri]string BaseNum,[FromUri]string BaseLine)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _detailApp.GetPageList(page, x => x.DocNum == DocNum && x.BaseNum==BaseNum && x.BaseLine ==Convert.ToInt32(BaseLine)).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="DocNums">根据单号进行删除</param>
        /// <returns></returns>
        [HttpDelete]
        //[AllowAnonymous]
        public IHttpActionResult Delete([FromBody]string[] DocNums)
        {
            //[FromBody]string[] ids
            var result = new ResponseData();
            try
            {
                string error_message = "";
                bool bDelete = _app.Deletes(DocNums, GetCurrentUser().LoginAccount, out error_message);
                if (!bDelete || !string.IsNullOrEmpty(error_message))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }

            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 导出

        /// <summary>
        /// 导出
        /// </summary>
        /// <param name="keyword">参数</param>
        /// <param name="dateValue">日期区间</param>
        /// <param name="isPosted">是否过账 1：已过帐 0：未过账</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] string keyword, [FromUri]string Supplier, [FromUri]string InspectionNum, 
            [FromUri]string Item, [FromUri]string BaseNum, [FromUri] DateTime[] dateValue, [FromUri] bool? isPosted, [FromUri]List<string> DocNums,
             [FromUri] DateTime[] PostdateValue, [FromUri] DateTime[] ManualPostdateValue)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = new List<MM_WarehousingExport_View>();
                if (DocNums == null || DocNums.Count < 1)
                {
                    var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                    DateTime fromTime = querDateTimes[0];
                    DateTime toTime = querDateTimes[1];

                    var querDateTimes1 = FormatProcessor.QueryDateTimesFormat(ManualPostdateValue);
                    DateTime fromManualTime = querDateTimes1[0];
                    DateTime toManualTime = querDateTimes1[1];
                    if (ManualPostdateValue.Length == 0)
                    {
                        toManualTime = toManualTime.AddYears(1);
                    }

                    var querDateTimes2 = FormatProcessor.QueryDateTimesFormat(PostdateValue);
                    DateTime fromPostTime = querDateTimes2[0];
                    DateTime toPostTime = querDateTimes2[1];


                    if (PostdateValue.Length > 0)
                    {
                        itemsData = _app.GetWarehousingExport_View(t => (string.IsNullOrEmpty(keyword)
                        || t.DocNum.Contains(keyword)
                            || t.ZRemark.Contains(keyword) || t.WhsCode.Contains(keyword)
                            || t.WhsName.Contains(keyword)
                            || t.CUser.Contains(keyword))
                            && (string.IsNullOrEmpty(Supplier) || t.SupplierCode.Contains(Supplier) || t.SupplierName.Contains(Supplier))
                            && (string.IsNullOrEmpty(InspectionNum) || t.InspectionNum.Contains(InspectionNum))
                            && (string.IsNullOrEmpty(Item) || t.ItemCode.Contains(Item) || t.ItemName.Contains(Item))
                            && (string.IsNullOrEmpty(BaseNum) || t.BaseNum.Contains(BaseNum))
                            && (isPosted == null || t.IsPosted == isPosted)
                            && (t.ManualPostTime >= fromManualTime && t.ManualPostTime <= toManualTime)
                            &&  (t.PostTime >= fromPostTime && t.PostTime <= toPostTime)
                            && (t.CTime >= fromTime && t.CTime <= toTime)).ToList();
                        //).Where(x => x.CTime >= fromTime && x.CTime <= toTime).ToList();
                        //var itemsData = _app.GetList().ToList();
                    }
                    else
                    {
                        itemsData = _app.GetWarehousingExport_View(t => (string.IsNullOrEmpty(keyword)
                       || t.DocNum.Contains(keyword)
                           || t.ZRemark.Contains(keyword) || t.WhsCode.Contains(keyword)
                           || t.WhsName.Contains(keyword)
                           || t.CUser.Contains(keyword))
                           && (string.IsNullOrEmpty(Supplier) || t.SupplierCode.Contains(Supplier) || t.SupplierName.Contains(Supplier))
                           && (string.IsNullOrEmpty(InspectionNum) || t.InspectionNum.Contains(InspectionNum))
                           && (string.IsNullOrEmpty(Item) || t.ItemCode.Contains(Item) || t.ItemName.Contains(Item))
                           && (string.IsNullOrEmpty(BaseNum) || t.BaseNum.Contains(BaseNum))
                           && (isPosted == null || t.IsPosted == isPosted)
                           && (t.ManualPostTime >= fromManualTime && t.ManualPostTime <= toManualTime)
                           && (t.CTime >= fromTime && t.CTime <= toTime)).ToList();
                    }
                }
                else
                {
                    itemsData = _app.GetWarehousingExport_View(x => DocNums.Contains(x.DocNum)).ToList();
                }
                var columns = ExcelService.FetchDefaultColumnList<MM_WarehousingExport_View>();
                string[] ignoreField = new string[]
                {
                    "WhsCode",
                    "RegionCode",
                    "BinLocationCode",
                    "SaleNo",
                    "SaleLineNo",
                    "InspectionLine"
                };
                var ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (var userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                columns.ForEach((column) =>
                {
                    if (column.ColumnName == "IsPosted")
                    {
                        column.Formattor = ExcelExportFormatter.IsOrNoFormatterForBoolean;
                    }
                });

                return ExportToExcelFile(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion
        
        #region 过账

        /// <summary>
        ///过账
        /// </summary>
        /// <param name="entities"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult DoPost([FromBody]List<MM_Warehousing> entities)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                Sys_User currentUser = GetCurrentUser();
                bool bPost = _app.DoPost(entities,null, currentUser.LoginAccount, out error_message);
                if (!bPost)//过账失败==2000，前台处理过账失败后重新刷新界面信息
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2010;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2000;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }

        #endregion

        #region 冲销过账

        /// <summary>
        /// 过账SAP
        /// </summary>
        /// <param name="entities">主信息</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult PassPost(List<MM_Warehousing> entities)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";


                Sys_User currentUser = GetCurrentUser();
                bool bPost = _app.PassPost(entities, currentUser.LoginAccount, out error_message);
                if (!bPost)//过账失败==2000，前台处理过账失败后重新刷新界面信息
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2010;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2000;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }

        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="entities">接收的集合</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Update([FromBody]List<MM_Warehousing> entities)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                Sys_User currentUser = GetCurrentUser();

                if (_app.Update(entities) > 0)
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2000;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                    result.MessageParam = 2010;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }

        #endregion

        #endregion

        #region Mobile

        #region 查询SRM报检单信息

        /// <summary>
        /// 查询SRM报检单信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetSRMInspection([FromUri] string InspectionNum)
        {
            string error_message;
            var result = new ResponseData();
            if (_app.CheckDate(InspectionNum, out error_message))//校验
            {
                try
                {
                    var itemsData = _app.GetSRMInspection(InspectionNum, out error_message).ToList();
                    if (itemsData != null && itemsData.Count > 0)
                    {
                        result.Data = itemsData;
                        result.Code = (int)WMSStatusCode.Success;
                    }
                    else
                    {
                        result.Code = (int)WMSStatusCode.Success;
                        result.Message = error_message;
                    }
                }
                catch (Exception ex)
                {
                    result.Code = (int)WMSStatusCode.UnHandledException;
                    result.Message = ex.InnerException?.Message ?? ex.Message;
                }
            }
            else
            {
                result.Code = (int)WMSStatusCode.Success;
                result.Message = error_message;
            }
            return Json(result);
        }

        #endregion

        #region 查询采购订单组件-委外入库使用

        /// <summary>
        /// 条件查询采购订单组件-委外入库使用
        /// </summary>
        /// <param name="BaseNum">集合</param>
        /// <param name="BaseLine">采购订单行号</param>
        /// <returns></returns>
        [HttpGet]
        //[AllowAnonymous] //添加此属性，PostMan可以直接通过接口调试，而不通过前端界面
        public IHttpActionResult GetWarehousingForRESBM([FromUri]string BaseNum, [FromUri]int BaseLine,[FromUri] decimal InspectionQty)
        {
            var result = new ResponseData();
            string error_message = "";
            try
            {
                var itemsData = _app.GetWarehousingForRESBM(BaseNum, BaseLine, InspectionQty, out error_message);
                if (itemsData != null && itemsData.Count > 0)
                {
                    result.Data = itemsData;
                    result.Code = (int)WMSStatusCode.Success;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 保存

        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="Parameters">参数汇总</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Save([FromBody]MM_WarehousingParameters Parameters)
        {

            var result = new ResponseData();
            try
            {
                string error_message = "";
                string type = ""; //类型 1:提交失败 2：过账失败
                Sys_User currentUser = GetCurrentUser();

                bool bSubmit = _app.Save(Parameters.ResbmList, Parameters.ManualPostTime, currentUser.LoginAccount, out error_message,out type);
                if (!bSubmit && type == "1")
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
                else if (!bSubmit && type == "2")
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }

        #endregion

        #endregion

  }
}