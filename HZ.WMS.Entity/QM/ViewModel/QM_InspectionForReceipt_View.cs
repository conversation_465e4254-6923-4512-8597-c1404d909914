using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.QM.ViewModel
{
    /// <summary>
    /// 采购检验关联采购收货信息
    /// </summary>
    public class QM_InspectionForReceipt_View
    {
        /// <summary> 
        /// ID
        /// </summary> 
        [Description("ID")]
        public string InspectionID { get; set; }

        /// <summary> 
        /// 报检单号
        /// </summary> 
        [Description("报检单号")]
        public string InspectionNum { get; set; }

        /// <summary> 
        /// 报检单行号
        /// </summary> 
        [Description("报检单行号")]
        public int? InspectionLine { get; set; }

        /// <summary> 
        /// 单号
        /// </summary> 
        [Description("采购单号")]
        public string BaseNum { get; set; }

        /// <summary> 
        /// 行号
        /// </summary> 
        [Description("采购单行号")]
        public int? BaseLine { get; set; }

        /// <summary> 
        /// 采购订单类型
        /// </summary> 
        [Description("采购订单类型")]
        public string BaseType { get; set; }

        /// <summary> 
        /// 批次
        /// </summary> 
        [Description("批次")]
        public string Batch { get; set; }

        /// <summary> 
        /// 批次
        /// </summary> 
        [Description("条码")]
        public string BarCode { get; set; }

        /// <summary> 
        /// 报检日期
        /// </summary> 
        [Description("报检日期")]
        public DateTime? InspectionTime { get; set; }

        /// <summary>
        /// 仓库编号
        /// </summary>
        [Description("仓库编号")]
        public string WhsCode { get; set; }

        /// <summary>
        /// 仓库名称
        /// </summary>
        [Description("仓库名称")]
        public string WhsName { get; set; }

        /// <summary>
        /// 库位编号
        /// </summary>
        public string BinLocationCode { get; set; }

        /// <summary>
        /// 库位名称
        /// </summary>
        public string BinLocationName { get; set; }

        /// <summary>
        /// 所属区域编号
        /// </summary>
        public string RegionCode { get; set; }

        /// <summary>
        /// 所属区域名称
        /// </summary>
        public string RegionName { get; set; }

        /// <summary> 
        /// 物料编号
        /// </summary> 
        [Description("物料编号")]
        public string ItemCode { get; set; }

        /// <summary> 
        /// 物料名称
        /// </summary> 
        [Description("物料名称")]
        public string ItemName { get; set; }

        /// <summary> 
        /// 单位
        /// </summary> 
        [Description("单位")]
        public string Unit { get; set; }

        /// <summary> 
        /// 合格数量
        /// </summary> 
        [Description("合格数量")]
        public decimal? QualifiedQty { get; set; }

        /// <summary> 
        /// 供应商编号
        /// </summary> 
        [Description("供应商编号")]
        public string SupplierCode { get; set; }

        /// <summary> 
        /// 供应商名称
        /// </summary> 
        [Description("供应商名称")]
        public string SupplierName { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Description("备注")]
        public string Remark { get; set; }

        /// <summary>
        /// 销售订单号
        /// </summary>
        [Description("销售订单号")]
        public string SalesOrderNum { get; set; }

        /// <summary>
        /// 销售订单行号
        /// </summary>
        [Description("销售订单行号")]
        public int? SalesOrderLine { get; set; }

        /// <summary>
        /// 采购订单交期
        /// </summary>
        [Description("采购订单交期")]
        public DateTime? DeliveryDate { get; set; }

    }
}
