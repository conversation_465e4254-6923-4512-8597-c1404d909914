using System;
using System.Data.SqlClient;
using System.Configuration;
using System.Reflection;
using HZ.Core.Security;

namespace HZ.WMS.Application
{
    /// <summary>
    /// 数据库连接字符串构建器
    /// </summary>
    public static class ConnectionStringBuilder
    {
        /// <summary>
        /// 构建优化的连接字符串
        /// </summary>
        /// <param name="dbKey">数据库连接键</param>
        /// <returns>优化后的连接字符串</returns>
        public static string BuildOptimizedConnectionString(string dbKey)
        {
            string baseConnectionString;

            // 获取基础连接字符串
            if (dbKey == "DbConnectionForOracle")
            {
                baseConnectionString = ConfigurationManager.ConnectionStrings[dbKey].ConnectionString;
                return baseConnectionString; // Oracle连接字符串不需要额外优化
            }
            else
            {
                try
                {
                    baseConnectionString = DES.Decrypt(ConfigurationManager.ConnectionStrings[dbKey].ConnectionString);
                }
                catch (Exception ex)
                {
                    // 如果解密失败，记录错误并返回原始连接字符串
                    HZ.Core.Logging.LogHelper.Instance.LogError($"连接字符串解密失败 - {dbKey}: {ex.Message}");
                    baseConnectionString = ConfigurationManager.ConnectionStrings[dbKey].ConnectionString;
                }
            }

            try
            {
                // 构建SqlConnectionStringBuilder来优化连接字符串
                var builder = new SqlConnectionStringBuilder(baseConnectionString);

                // 从配置文件读取连接池参数
                var minPoolSize = int.Parse(ConfigurationManager.AppSettings["ConnectionPool.MinSize"] ?? "10");
                var maxPoolSize = int.Parse(ConfigurationManager.AppSettings["ConnectionPool.MaxSize"] ?? "200");
                var connectionTimeout = int.Parse(ConfigurationManager.AppSettings["ConnectionPool.ConnectionTimeout"] ?? "60");
                var connectionLifetime = int.Parse(ConfigurationManager.AppSettings["ConnectionPool.ConnectionLifetime"] ?? "600");
                var connectRetryCount = int.Parse(ConfigurationManager.AppSettings["ConnectionPool.ConnectRetryCount"] ?? "3");
                var connectRetryInterval = int.Parse(ConfigurationManager.AppSettings["ConnectionPool.ConnectRetryInterval"] ?? "10");

                // 基本连接池配置
                builder.Pooling = true;                    // 启用连接池
                builder.MinPoolSize = minPoolSize;         // 最小连接池大小
                builder.MaxPoolSize = maxPoolSize;         // 最大连接池大小
                builder.ConnectTimeout = connectionTimeout; // 连接超时时间（秒）

                // 性能优化
                var marsEnabled = bool.Parse(ConfigurationManager.AppSettings["ConnectionPool.MultipleActiveResultSets"] ?? "false");
                var enlistEnabled = bool.Parse(ConfigurationManager.AppSettings["ConnectionPool.Enlist"] ?? "true");
                builder.MultipleActiveResultSets = marsEnabled;  // 根据配置设置MARS
                builder.Enlist = enlistEnabled;                  // 根据配置设置自动事务登记

                // 连接重试配置
                builder.ConnectRetryCount = connectRetryCount;
                builder.ConnectRetryInterval = connectRetryInterval;

                // 连接生命周期管理
                try
                {
                    var loadBalanceTimeout = int.Parse(ConfigurationManager.AppSettings["ConnectionPool.LoadBalanceTimeout"] ?? "0");
                    if (loadBalanceTimeout > 0)
                    {
                        builder.LoadBalanceTimeout = loadBalanceTimeout;
                    }
                }
                catch (ArgumentException)
                {
                    // 某些版本可能不支持此属性，忽略
                }

                // 添加连接池清理参数
                try
                {
                    // 设置连接生命周期
                    if (builder.ConnectionString.IndexOf("Connection Lifetime", StringComparison.OrdinalIgnoreCase) == -1)
                    {
                        builder.Add("Connection Lifetime", connectionLifetime.ToString());
                    }

                    // 设置连接重置选项
                    if (builder.ConnectionString.IndexOf("Connection Reset", StringComparison.OrdinalIgnoreCase) == -1)
                    {
                        builder.Add("Connection Reset", "true");
                    }

                    // 设置应用程序意图
                    var applicationIntent = ConfigurationManager.AppSettings["ConnectionPool.ApplicationIntent"] ?? "ReadWrite";
                    if (builder.ConnectionString.IndexOf("ApplicationIntent", StringComparison.OrdinalIgnoreCase) == -1)
                    {
                        builder.Add("ApplicationIntent", applicationIntent);
                    }

                    // 设置连接池阻塞期间
                    var poolBlockingPeriod = ConfigurationManager.AppSettings["ConnectionPool.PoolBlockingPeriod"] ?? "Auto";
                    if (builder.ConnectionString.IndexOf("PoolBlockingPeriod", StringComparison.OrdinalIgnoreCase) == -1)
                    {
                        builder.Add("PoolBlockingPeriod", poolBlockingPeriod);
                    }
                }
                catch (ArgumentException)
                {
                    // 某些版本可能不支持这些属性，忽略
                }

                return builder.ConnectionString;
            }
            catch (Exception ex)
            {
                // 如果连接字符串构建失败，记录错误并返回原始连接字符串
                // HZ.Core.Logging.LogHelper.Instance.LogConfigurationError(dbKey, "构建优化连接字符串", ex);
                return baseConnectionString;
            }
        }

        /// <summary>
        /// 解析连接字符串的连接池配置
        /// </summary>
        /// <param name="connectionString">连接字符串</param>
        /// <returns>连接池配置信息</returns>
        public static ConnectionPoolConfig ParseConnectionPoolConfig(string connectionString)
        {
            return GetConnectionPoolConfig(connectionString);
        }

        /// <summary>
        /// 获取连接字符串的连接池信息
        /// </summary>
        /// <param name="connectionString">连接字符串</param>
        /// <returns>连接池配置信息</returns>
        public static ConnectionPoolConfig GetConnectionPoolConfig(string connectionString)
        {
            try
            {
                var builder = new SqlConnectionStringBuilder(connectionString);

                return new ConnectionPoolConfig
                {
                    Pooling = builder.Pooling,
                    MinPoolSize = builder.MinPoolSize,
                    MaxPoolSize = builder.MaxPoolSize,
                    ConnectionTimeout = builder.ConnectTimeout,
                    CommandTimeout = int.Parse(ConfigurationManager.AppSettings["ConnectionPool.CommandTimeout"] ?? "300"),
                    LoadBalanceTimeout = builder.LoadBalanceTimeout
                };
            }
            catch (Exception ex)
            {
                // 如果解析失败，返回默认配置
                // HZ.Core.Logging.LogHelper.Instance.LogConfigurationError(connectionString, "解析连接池配置", ex);
                return new ConnectionPoolConfig
                {
                    Pooling = true,
                    MinPoolSize = 5,
                    MaxPoolSize = 100,
                    ConnectionTimeout = 30,
                    CommandTimeout = 300,
                    LoadBalanceTimeout = 0
                };
            }
        }
    }

    /// <summary>
    /// 连接池配置信息
    /// </summary>
    public class ConnectionPoolConfig
    {
        public bool Pooling { get; set; }
        public int MinPoolSize { get; set; }
        public int MaxPoolSize { get; set; }
        public int ConnectionTimeout { get; set; }
        public int CommandTimeout { get; set; }
        public int LoadBalanceTimeout { get; set; }
    }
}
