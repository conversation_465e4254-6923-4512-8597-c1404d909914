using System;
using System.Configuration;
using System.Data.SqlClient;
using System.Threading.Tasks;
using System.Threading;
using HZ.Core.Logging;

namespace HZ.WMS.Application.Base
{
    /// <summary>
    /// 连接池管理器 - 用于监控和优化数据库连接池
    /// </summary>
    public static class ConnectionPoolManager
    {
        private static Timer _cleanupTimer;
        private static readonly object _lockObject = new object();

        /// <summary>
        /// 初始化连接池管理器
        /// </summary>
        public static void Initialize()
        {
            try
            {
                // 启动定期清理任务
                var cleanupInterval = int.Parse(ConfigurationManager.AppSettings["ConnectionPool.CleanupInterval"] ?? "300000"); // 默认5分钟
                _cleanupTimer = new Timer(CleanupConnectionPools, null, TimeSpan.FromSeconds(30), TimeSpan.FromMilliseconds(cleanupInterval));
                
                LogHelper.Instance.LogInfo("连接池管理器已启动");
            }
            catch (Exception ex)
            {
                LogHelper.Instance.LogError("连接池管理器启动失败", ex);
            }
        }

        /// <summary>
        /// 清理连接池
        /// </summary>
        /// <param name="state"></param>
        private static void CleanupConnectionPools(object state)
        {
            if (!Monitor.TryEnter(_lockObject, TimeSpan.FromSeconds(5)))
            {
                return; // 如果无法获取锁，跳过此次清理
            }

            try
            {
                // 清理所有连接池
                SqlConnection.ClearAllPools();
                LogHelper.Instance.LogInfo("连接池清理完成");
            }
            catch (Exception ex)
            {
                LogHelper.Instance.LogError("连接池清理失败", ex);
            }
            finally
            {
                Monitor.Exit(_lockObject);
            }
        }

        /// <summary>
        /// 手动清理指定连接字符串的连接池
        /// </summary>
        /// <param name="connectionString">连接字符串</param>
        public static void ClearConnectionPool(string connectionString)
        {
            try
            {
                using (var connection = new SqlConnection(connectionString))
                {
                    SqlConnection.ClearPool(connection);
                }
                LogHelper.Instance.LogInfo($"连接池清理完成: {GetConnectionStringInfo(connectionString)}");
            }
            catch (Exception ex)
            {
                LogHelper.Instance.LogError($"连接池清理失败: {GetConnectionStringInfo(connectionString)}", ex);
            }
        }

        /// <summary>
        /// 测试连接池健康状态
        /// </summary>
        /// <param name="connectionString">连接字符串</param>
        /// <returns>连接池健康状态</returns>
        public static async Task<ConnectionPoolHealthStatus> CheckConnectionPoolHealthAsync(string connectionString)
        {
            var status = new ConnectionPoolHealthStatus
            {
                ConnectionString = GetConnectionStringInfo(connectionString),
                CheckTime = DateTime.Now
            };

            try
            {
                var startTime = DateTime.Now;
                
                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    
                    var connectionTime = (DateTime.Now - startTime).TotalMilliseconds;
                    status.ConnectionTime = connectionTime;
                    status.IsHealthy = connectionTime < 5000; // 5秒内连接成功认为健康
                    
                    // 执行简单查询测试
                    using (var command = new SqlCommand("SELECT 1", connection))
                    {
                        var queryStartTime = DateTime.Now;
                        await command.ExecuteScalarAsync();
                        status.QueryTime = (DateTime.Now - queryStartTime).TotalMilliseconds;
                    }
                }
                
                status.ErrorMessage = null;
            }
            catch (Exception ex)
            {
                status.IsHealthy = false;
                status.ErrorMessage = ex.Message;
                status.ConnectionTime = -1;
                status.QueryTime = -1;
            }

            return status;
        }

        /// <summary>
        /// 获取连接字符串信息（隐藏敏感信息）
        /// </summary>
        /// <param name="connectionString">连接字符串</param>
        /// <returns>安全的连接字符串信息</returns>
        private static string GetConnectionStringInfo(string connectionString)
        {
            try
            {
                var builder = new SqlConnectionStringBuilder(connectionString);
                return $"Server={builder.DataSource}, Database={builder.InitialCatalog}, MaxPoolSize={builder.MaxPoolSize}";
            }
            catch
            {
                return "连接字符串解析失败";
            }
        }

        /// <summary>
        /// 停止连接池管理器
        /// </summary>
        public static void Stop()
        {
            try
            {
                _cleanupTimer?.Dispose();
                _cleanupTimer = null;
                LogHelper.Instance.LogInfo("连接池管理器已停止");
            }
            catch (Exception ex)
            {
                LogHelper.Instance.LogError("连接池管理器停止失败", ex);
            }
        }
    }

    /// <summary>
    /// 连接池健康状态
    /// </summary>
    public class ConnectionPoolHealthStatus
    {
        /// <summary>
        /// 连接字符串信息
        /// </summary>
        public string ConnectionString { get; set; }

        /// <summary>
        /// 是否健康
        /// </summary>
        public bool IsHealthy { get; set; }

        /// <summary>
        /// 连接时间（毫秒）
        /// </summary>
        public double ConnectionTime { get; set; }

        /// <summary>
        /// 查询时间（毫秒）
        /// </summary>
        public double QueryTime { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 检查时间
        /// </summary>
        public DateTime CheckTime { get; set; }
    }
}
