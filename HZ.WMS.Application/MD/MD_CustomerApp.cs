using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using SqlSugar;
using HZ.WMS.Entity.MD;
using HZ.Core.Http;

namespace HZ.WMS.Application.MD
{
    /// <summary>
    /// 供应商主数据
    /// </summary>
    public class MD_CustomerApp : BaseApp<MD_Customer>
    {
        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public MD_CustomerApp() : base()
        {
        }




        #endregion

        #region 同步sap数据

        /// <summary>
        /// 
        /// </summary>
        public void SyncCustomer()
        {
            //获取所有数据
            //List<CustomerEntity> sapCustomers = CustomerService.GetCustomersForActityStates();
            List<MD_Customer> sapCustomerList = new List<MD_Customer>();

            //foreach (CustomerEntity entity in sapCustomers)
            //{
            //    MD_Customer customer = new MD_Customer();
            //    customer.InternalID = entity.InternalID;
            //    customer.CategoryCode = entity.CategoryCode;
            //    customer.LifeCycleStatusCode = entity.LifeCycleStatusCode;
            //    customer.FirstLineName = entity.FirstLineName;
            //    customer.FamilyName = entity.FamilyName;
            //    customer.EMailURI = entity.EMailURI;
            //    customer.Address = entity.Address;
            //    customer.CityName = entity.CityName;
            //    customer.ContactPerson = entity.ContactPerson;
            //    customer.Fax = entity.Fax;
            //    customer.Phone = entity.Phone;
            //    sapCustomerList.Add(customer);
            //}

            List<MD_Customer> localDbCustomerList = GetList().ToList();

            //1、本地没有，远程有的新增数据
            List<MD_Customer> addCustomer = sapCustomerList.Except(localDbCustomerList, new MD_CustomerEquality()).ToList();

            //2、本地有，远程没有的删除数据
            List<MD_Customer> deletedCustomer = localDbCustomerList.Except(sapCustomerList, new MD_CustomerEquality()).ToList();


            //3、本地远程都有的更新数据（交集）
            List<MD_Customer> updateCustomer = new List<MD_Customer>();
            foreach (MD_Customer customer in localDbCustomerList)
            {
                var entity = sapCustomerList.Where(x => x.InternalID == customer.InternalID).FirstOrDefault();
                DateTime localMTime = DateTime.Parse(customer.MTime.Value.ToString("yyyy-MM-dd HH:mm:ss"));
                DateTime sapMTime = DateTime.Parse(entity.MTime.Value.ToString("yyyy-MM-dd HH:mm:ss"));
                if (entity != null && localMTime < sapMTime)
                {
                    customer.InternalID = entity.InternalID;
                    customer.CategoryCode = entity.CategoryCode;
                    customer.LifeCycleStatusCode = entity.LifeCycleStatusCode;
                    customer.FirstLineName = entity.FirstLineName;
                    customer.FamilyName = entity.FamilyName;
                    customer.EMailURI = entity.EMailURI;
                    customer.Address = entity.Address;
                    customer.CityName = entity.CityName;
                    customer.ContactPerson = entity.ContactPerson;
                    customer.Fax = entity.Fax;
                    customer.Phone = entity.Phone;
                    customer.MTime = entity.MTime;
                    updateCustomer.Add(customer);
                }
            }

            if (addCustomer != null && addCustomer.Count() > 0)
            {
                //Delete(mdSupplierList);
                Insert(addCustomer);

            }

            if (updateCustomer != null && updateCustomer.Count() > 0)
            {
                //Delete(mdSupplierList);
                Update(updateCustomer);

            }

            if (deletedCustomer != null && deletedCustomer.Count() > 0)
            {

                Delete(deletedCustomer);

            }
        }
        #endregion

        #region 销售波次单查询客户信息
        /// <summary>
        /// 
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <returns></returns>
        public List<MD_Customer> GetPageListToSales(Pagination page, string keyword)
        {
            if (string.IsNullOrEmpty(page.Sort))
            {
                page.Sort = "InternalID desc";
            }
            else
            {
                page.Sort = page.Sort.Replace("ascending", "asc").Replace("descending", "desc");
            }
            var itemsData = this.DbContext.Queryable<MD_Customer>()
                     .Where(x =>
                         string.IsNullOrEmpty(keyword)
                        || x.InternalID.Contains(keyword)
                        || x.FamilyName.Contains(keyword)
                        || x.FirstLineName.Contains(keyword)).OrderBy(page.Sort).ToList();
            int total = itemsData.Count();
            page.Total = total;
            var itemPageData = new List<MD_Customer>();
            if (page.PageSize > total)
            {
                itemPageData = itemsData.Skip(page.PageSize * (page.PageNumber - 1)).Take(total).ToList();
            }
            else
            {
                itemPageData = itemsData.Skip(page.PageSize * (page.PageNumber - 1)).Take(page.PageSize).ToList();
            }
            return itemPageData;
        }
        #endregion
    }
}
