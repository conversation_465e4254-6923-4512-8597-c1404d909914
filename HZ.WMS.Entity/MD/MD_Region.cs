using SqlSugar;
//Chloe框架


namespace HZ.WMS.Entity.MD
{
    /// <summary>
    /// 区域
    /// </summary>
    public class MD_Region:BaseEntity
    {

        /// <summary> 
        /// 区域ID
        /// </summary> 
        [SugarColumn(IsPrimaryKey = true)]
        public string RegionID {get;set;} 
        /// <summary> 
        /// 区域编号
        /// </summary> 
        [UniqueCode]
        public string RegionCode {get;set;} 
        /// <summary> 
        /// 区域名称
        /// </summary> 
        public string RegionName {get;set;} 
        /// <summary> 
        /// 所属仓库编号
        /// </summary> 
        public string WhsCode {get;set;} 
        /// <summary> 
        /// 所属仓库名称
        /// </summary> 
        public string WhsName {get;set;} 

    }
}


