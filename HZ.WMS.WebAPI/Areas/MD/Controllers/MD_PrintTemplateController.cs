using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;
using HZ.Core.Http;
using HZ.Core.Office;
using HZ.WMS.Application.MD;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.MD.Import;
using HZ.WMS.Entity.MD.DTO;
using HZ.WMS.WebAPI.Controllers;

namespace HZ.WMS.WebAPI.Areas.MD.Controllers
{
    /// <summary>
    /// 打印模板管理
    /// </summary>
    public class MD_PrintTemplateController : ApiBaseController
    {
        private MD_PrintTemplateApp _app = new MD_PrintTemplateApp();
        private MD_PrintTemplateParameterApp _parameterApp = new MD_PrintTemplateParameterApp();

        #region 查询分页列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="page">分页参数</param>
        /// <param name="req">查询参数</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri] Pagination page, [FromUri] MD_PrintTemplateListReq req)
        {
            var result = new ResponseData();
            try
            {
                var data = _app.GetPageListWithParameterName(page, req);
                result.Data = new { rows = data, total = page.Total };

                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 根据ID获取实体

        /// <summary>
        /// 根据ID获取实体
        /// </summary>
        /// <param name="id">主键ID</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetById([FromUri] string id)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.GetEntityByKey(id);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 根据模板Key获取打印模板列表

        /// <summary>
        /// 根据模板Key获取打印模板列表
        /// </summary>
        /// <param name="templateKey">模板Key</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetByTemplateKey([FromUri] string templateKey)
        {
            var result = new ResponseData();
            try
            {
                // 首先根据 TemplateKey 获取参数模板
                var parameterTemplate = _parameterApp.GetFirstEntity(x => x.TemplateKey == templateKey && x.Enable == true);
                if (parameterTemplate == null)
                {
                    result.Data = new List<MD_PrintTemplate>();
                    result.Code = (int)WMSStatusCode.Success;
                    return Json(result);
                }

                // 根据参数模板ID获取对应的打印模板列表
                var data = _app.GetList(x =>
                    x.TemplateParameterId == parameterTemplate.Id
                    && x.Enable == true).ToList();

                result.Data = data;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取参数模板列表

        /// <summary>
        /// 获取参数模板列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetParameterTemplateList()
        {
            var result = new ResponseData();
            try
            {
                var data = _parameterApp.GetList(x => x.Enable == true).Select(x => new
                {
                    x.Id,
                    x.TemplateName
                }).ToList();

                result.Data = data;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Update([FromBody] MD_PrintTemplate entity)
        {
            var result = new ResponseData();
            try
            {
                entity.MUser = GetCurrentUser().LoginAccount;
                entity.MTime = DateTime.Now;

                result.Data = _app.Update(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 添加

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Insert([FromBody] MD_PrintTemplate entity)
        {
            var result = new ResponseData();
            try
            {
                entity.CUser = GetCurrentUser().LoginAccount;
                entity.CTime = DateTime.Now;

                result.Data = _app.Insert(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="ids">主键ID集合</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Delete([FromBody] List<string> ids)
        {
            var result = new ResponseData();
            try
            {
                var entities = _app.GetList(x => ids.Contains(x.Id)).ToList();
                result.Data = _app.Delete(entities, GetCurrentUser().LoginAccount);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 导出

        /// <summary>
        /// 导出模板
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelModel()
        {
            var result = new ResponseData();
            try
            {
                var columns = ExcelService.FetchDefaultColumnList<MD_PrintTemplate>();
                string modelName = "打印模板-导入模板";
                //排除不需要的列名称 字符串
                string[] ignoreField = new string[]
                {
                    "Id", "Remark", "IsDelete", "CUser", "CTime",
                    "MUser", "MTime", "DUser", "DTime"
                };
                var itemsData = new List<MD_PrintTemplate>() { };
                var ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<MD_PrintTemplate> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }

                return ExportToExcelFile<MD_PrintTemplate>(itemsData, columns,
                    GetCurrentUser().UserName + "_" + modelName);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        /// <summary>
        /// 导出数据
        /// </summary>
        /// <param name="req">查询参数</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] MD_PrintTemplateListReq req)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetList(x => 
                    (string.IsNullOrEmpty(req.Keyword) || x.TemplateName.Contains(req.Keyword))
                    && (req.Enable == null || x.Enable == req.Enable)).ToList();

                List<ExcelColumn<MD_PrintTemplate>> columns = ExcelService.FetchDefaultColumnList<MD_PrintTemplate>();
                string[] ignoreField = new string[] { "IsDelete", "DTime", "DUser", "MUser", "MTime" };
                var ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<MD_PrintTemplate> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }

                return ExportToExcelFile<MD_PrintTemplate>(itemsData, columns,
                    GetCurrentUser().UserName + "_打印模板数据");
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #region 导入

        /// <summary>
        /// 导入
        /// </summary>
        /// <param name="entitys">接收的导入集合</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult ImportExcelToData([FromBody] List<MD_PrintTemplateImport> entitys)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                if (_app.ImportExcelToBaseData(entitys, GetCurrentUser().LoginAccount, out error_message))
                {
                    result.Data = true;
                    result.Code = (int)WMSStatusCode.Success;
                }
                else
                {
                    result.Data = false;
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
            return Json(result);
        }

        #endregion
    }
}
