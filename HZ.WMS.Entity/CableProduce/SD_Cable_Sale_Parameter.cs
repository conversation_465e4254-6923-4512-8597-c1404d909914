using System.Collections.Generic;
using System.ComponentModel;
using HZ.WMS.Entity;
using SqlSugar;

namespace AOS.OMS.Entity.Sale
{
    /// <summary>
    /// 订单类
    /// </summary>
    public class SD_Cable_Sale_Parameter : BaseEntity
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Description("主键ID")]
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 订单编号
        /// </summary>
        [Description("订单编号")]
        public string OrderNum { get; set; }

        /// <summary>
        /// 不停层
        /// </summary>
        [Description("不停层")]
        public string NonStopLayer { get; set; }

        /// <summary>
        /// 基站
        /// </summary>
        [Description("基站")]
        public string BaseStation { get; set; }

        /// <summary>
        /// 提升高度
        /// </summary>
        [Description("提升高度")]
        public string LiftingHeight { get; set; }

        /// <summary>
        /// 顶层高度
        /// </summary>
        [Description("顶层高度")]
        public decimal? TopHeight { get; set; }

        /// <summary>
        /// 底坑深度
        /// </summary>
        [Description("底坑深度")]
        public decimal? PitDepth { get; set; }

        /// <summary>
        /// 机房引出线(米)
        /// </summary>
        [Description("机房引出线(米)")]
        public decimal? MachineRoomCable { get; set; }

        /// <summary>
        /// 控制柜移动距离
        /// </summary>
        [Description("控制柜移动距离")]
        public decimal? CabinetMovementDistance { get; set; }

        /// <summary>
        /// 机房类型
        /// </summary>
        [Description("机房类型")]
        public string MachineType { get; set; }

        /// <summary>
        /// 梯型
        /// </summary>
        [Description("梯型")]
        public string Trapezium { get; set; }

        /// <summary>
        /// 系统
        /// </summary>
        [Description("系统")]
        public string SystemName { get; set; }

        /// <summary>
        /// 单/双/贯通
        /// </summary>
        [Description("单/双/贯通")]
        public string PassageType { get; set; }

        /// <summary>
        /// 前门开门楼层
        /// </summary>
        [Description("前门开门楼层")]
        public string FrontDoorOpenFloor { get; set; }

        /// <summary>
        /// 后门开门楼层
        /// </summary>
        [Description("后门开门楼层")]
        public string RearDoorOpenFloor { get; set; }

        /// <summary>
        /// 是否观光梯
        /// </summary>
        [Description("是否观光梯")]
        public string IsSightseeingLift { get; set; }

        /// <summary>
        /// 速度
        /// </summary>
        [Description("速度")]
        public decimal? Speed { get; set; }

        /// <summary>
        /// 提前开门/再平层
        /// </summary>
        [Description("提前开门/再平层")]
        public string AdvanceDoorOpen { get; set; }

        /// <summary>
        /// 包装箱状态
        /// </summary>
        [Description("包装箱状态")]
        public string PackageBoxStatus { get; set; }

        /// <summary>
        /// 印刷
        /// </summary>
        [Description("印刷")]
        public string Print { get; set; }

        /// <summary>
        /// 预估重量
        /// </summary>
        [Description("预估重量")]
        public decimal? EstimatedWeight { get; set; }

        /// <summary>
        /// 木箱尺寸
        /// </summary>
        [Description("木箱尺寸")]
        public string WoodenBoxSize { get; set; }

        /// <summary>
        /// 木盘尺寸
        /// </summary>
        [Description("木盘尺寸")]
        public string WoodenTraySize { get; set; }

        /// <summary>
        /// 批号
        /// </summary>
        [Description("批号")]
        public string BatchNo { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        [Description("项目名称")]
        public string EntryName { get; set; }

        /// <summary>
        /// 父主键
        /// </summary>
        [Description("父主键")]
        public string Pid { get; set; }

        /// <summary>
        /// 控制系统
        /// </summary>
        [Description("控制系统")]
        public string ControlSystem { get; set; }

        /// <summary>
        /// 控制方式
        /// </summary>
        [Description("控制方式")]
        public string ControlMethod { get; set; }

        /// <summary>
        /// 楼层数
        /// </summary>
        [Description("楼层数")]
        public string FloorNo { get; set; }

        /// <summary>
        /// 停站数
        /// </summary>
        [Description("停站数")]
        public string StationStopNo { get; set; }

        /// <summary>
        /// 开门数
        /// </summary>
        [Description("开门数")]
        public string OpenNo { get; set; }

        /// <summary>
        /// 双贯通层
        /// </summary>
        [Description("双贯通层")]
        public string DoubleThroughLayer { get; set; }

        /// <summary>
        /// 服务楼层
        /// </summary>
        [Description("服务楼层")]
        public string ServerLayer { get; set; }

        /// <summary>
        /// 自动再平层
        /// </summary>
        [Description("自动再平层")]
        public string AutoReLeveling { get; set; }

        /// <summary>
        /// 提前开门
        /// </summary>
        [Description("提前开门")]
        public string AdvanceOpen { get; set; }
        
        
        /// <summary>
        /// 井道净宽
        /// </summary>
        [Description("井道净宽")]
        public decimal? HoistwayWide { get; set; }

        /// <summary>
        /// 井道净深
        /// </summary>
        [Description("井道净深")]
        public decimal? HoistwayDepth { get; set; }

        /// <summary>
        /// 停电应急疏散装置
        /// </summary>
        [Description("停电应急疏散装置")]
        public string PowerOutEmergencyEvacuationDevice { get; set; }

        /// <summary>
        /// 控制柜位移加长
        /// </summary>
        [Description("控制柜位移加长")]
        public string ControlCabinetDisplacementExtension { get; set; }

        /// <summary>
        /// 下单客户
        /// </summary>
        [Description("下单客户")]
        public string OrderingCustom { get; set; }
        
        /// <summary>
        /// 物料编码
        /// </summary>
        [Description("物料编码")]
        public string MaterialCode { get; set; }
        
        /// <summary>
        /// 载重
        /// </summary>
        [Description("载重")]
        public string Load { get; set; }
        
        /// <summary>
        /// 外呼板接口
        /// </summary>
        [Description("外呼板接口")]
        public string OutboundCallBoardInterface { get; set; }
        
        /// <summary>
        /// 电梯类型
        /// </summary>
        [Description("电梯类型")]
        public string ElevatorType { get; set; }
        
        /// <summary>
        /// 订单楼层明细
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<SD_Cable_Sale_OrderFloor> OrderFloorList { get; set; }
        
    }
}
