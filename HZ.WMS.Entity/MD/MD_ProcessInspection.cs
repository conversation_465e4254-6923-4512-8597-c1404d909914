using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.MD
{
    /// <summary>
    /// 生产过程检验设置
    /// </summary>
    [SugarTable("MD_ProcessInspection")]
    public class MD_ProcessInspection : BaseEntity
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Description("主键ID")]
        [SugarColumn(IsPrimaryKey = true)]
        public string ID { get; set; }
        /// <summary>
        /// 生产线编号
        /// </summary>
        [Description("生产线编号")]
        public string ProductionLineCode { get; set; }
        /// <summary>
        /// 生产线名称
        /// </summary>
        [Description("生产线名称")]
        public string ProductionLineDes { get; set; }
        /// <summary>
        /// 工序编号
        /// </summary>
        [Description("工序编号")]
        public string WorkingProcedureCode { get; set; }
        /// <summary>
        /// 工序描述
        /// </summary>
        [Description("工序描述")]
        public string WorkingProcedureDes { get; set; }
        /// <summary>
        /// 检验项
        /// </summary>
        [Description("检验项")]
        public string InspectionItem { get; set; }
        /// <summary>
        /// 合格值范围上限
        /// </summary>
        [Description("合格值范围上限")]
        public decimal? UpperLimit { get; set; }
        /// <summary>
        /// 合格值范围下限
        /// </summary>
        [Description("合格值范围下限")]
        public decimal? LowerLimit { get; set; }
    }
}
