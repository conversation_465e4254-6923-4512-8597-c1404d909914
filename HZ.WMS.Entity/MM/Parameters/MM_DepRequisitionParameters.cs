using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.MM.Parameters
{
    /// <summary>
    /// 接收参数的类
    /// </summary>
    public class MM_DepRequisitionParameters
    {
        /// <summary>
        /// 部门领料单明细
        /// </summary>
        public List<MM_DepReqDetailed> DetailedList { get; set; }

        /// <summary>
        /// 删除明细数组
        /// </summary>
        public string[] deldetailArray { get; set; }

        /// <summary>
        /// 手动过账时间
        /// </summary>
        public DateTime? ManualPostTime { get; set; }

        /// <summary>
        /// 领料单号
        /// </summary>
        public string DocNum { get; set; }

        /// <summary>
        /// 办理人员编号
        /// </summary>
        public string HandlenCode { get; set; }

        /// <summary>
        /// 办理人员
        /// </summary>
        public string HandlenName { get; set; }


        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }



        /// <summary>
        /// 移动类型
        /// </summary>
        public string MovementType { get; set; }

        /// <summary>
        /// 移动类型名称
        /// </summary>
        public string MovementTypeName { get; set; }
    }
}
