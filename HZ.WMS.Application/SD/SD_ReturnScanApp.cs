using SqlSugar;
using System;
using System.Collections.Generic;
using HZ.WMS.Application.Sys;
using HZ.WMS.Entity.SD;
using HZ.Core.Http;
using HZ.WMS.Application.MD;

using System.Linq;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.SAP;
using HZ.WMS.Application.SAP;

namespace HZ.WMS.Application.SD
{
    /// <summary>
    /// 没有out_message消息的函数，意味着只在最前端做未处理异常捕捉
    /// </summary>

    public class SD_ReturnScanApp : BaseApp<SD_ReturnScan>
    {
        #region 初始化

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public SD_ReturnScanApp() : base() { }

        private BaseApp<SD_ReturnScan> _baseApp = new BaseApp<SD_ReturnScan>();
        private MD_StockApp stockApp = new MD_StockApp();

        #endregion

        #region 保存

        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="entities">集合</param>
        /// <param name="ManualPostTime">手动过账时间</param>
        /// <param name="user">当前用户</param>
        /// <param name="error_message">错误消息</param>
        /// <param name="type">类型 1:提交失败 2：过账失败</param>
        /// <returns></returns>
        public bool Save(List<SD_ReturnScan> entities, DateTime ManualPostTime,string user, out string error_message, out string type)
        {
            error_message = "提交成功";
            type = "";
            try
            {
                string DocNum = _baseApp.GetNewDocNum(DocType.SD, DocFixedNumDef.SD_ReturnScan);
                entities.ForEach(x =>
                {
                    x.DocNum = DocNum;
                    x.CUser = user;
                    x.CTime = DateTime.Now;
                    x.IsDelete = false;
                    x.IsPosted = false;
                    x.ManualPostTime = ManualPostTime;
                });

                DbContext.Ado.BeginTran();

                Insert(entities);

                DbContext.Ado.CommitTran();

                //自动发货过账
                Sys_SwithConfigApp Sys_SwithConfigApp = new Sys_SwithConfigApp();
                if (Sys_SwithConfigApp.IsSDReturnScanAutoPost)
                {
                    string postMsg = "";
                    bool bPost = this.DoPost(entities, user, out postMsg);
                    if (!bPost)
                    {
                        error_message = postMsg;
                        type = "2";
                        return false;
                    }
                    else
                    {
                        error_message = "提交并" + postMsg;
                    }
                }
                return true;
            }
            catch (System.Exception ex)
            {
                error_message = ex.Message;
                type = "1";
                if (DbContext.Ado.IsAnyTran())
                    DbContext.Ado.RollbackTran();

                return false;
            }
        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="opUser"></param>
        /// <param name="error_message"></param>
        public bool Delete(string[] ids, string opUser, out string error_message)
        {
            bool bDeleted = true;
            List<SD_ReturnScan> entities = GetListByKeys(ids);
            if (ValidateDelete(entities, out error_message))
            {
                try
                {
                    DbContext.Ado.BeginTran();
                    //_stockingScanApp.DbContext = this.DbContext;
                    //stockApp.DbContext = this.DbContext;
                    //_returnBarCodeApp.DbContext = this.DbContext;
                    Delete(entities);
                    //if (this.Delete(entities) > 0)
                    //{
                    //    //entities.ForEach(x =>
                    //    //{
                    //    //    string errorMsg = string.Empty;
                    //    //    if (!stockApp.StockOut(x.BarCode, x.InBinLocationCode, x.Qty, x.Unit, opUser, out errorMsg))
                    //    //    {
                    //    //        throw new Exception("Common.error", new Exception(errorMsg));
                    //    //    }
                    //    //});

                    //}
                    //else
                    //{
                    //    throw new Exception("Common.error", new Exception("Common.operationFailed"));
                    //}

                    DbContext.Ado.CommitTran();
                }
                catch (Exception ex)
                {
                    bDeleted = false;
                    error_message = ex.Message;
                    DbContext.Ado.RollbackTran();
                }
            }
            else
            {
                bDeleted = false;
            }
            return bDeleted;
        }


        #endregion

        #region 校验

        /// <summary>
        /// 校验
        /// </summary>
        /// <param name="entities"></param>
        /// <param name="error_message"></param>
        /// <returns></returns>
        private bool ValidateDelete(List<SD_ReturnScan> entities, out string error_message)
        {
            bool isPass = true;
            error_message = "";
            foreach (SD_ReturnScan detail in entities)
            {
                if (detail.IsPosted == true)
                {
                    error_message = "已过账数据不允许删除!";
                    isPass = false;
                    break;
                }
            }

            return isPass;
        }

        #endregion

        #region 过账

        /// <summary>
        /// 采购订单明细行下，批次不同不允许一起过账（等后续解决）
        /// </summary>
        /// <param name="entities"></param>
        /// <param name="postUser"></param>
        /// <param name="error_message"></param>
        /// <returns></returns>
        public bool DoPost(List<SD_ReturnScan> entities, string postUser, out string error_message)
        {
            error_message = "";
            MD_StockApp _stockApp = new MD_StockApp();
            Sys_SAPCompanyInfoApp _SAPCompanyInfoApp = new Sys_SAPCompanyInfoApp();
            try
            {
                //多条数据后根据单号、销售订单号进行分组
                var mainInfoList = entities.GroupBy(g => new
                {
                    g.DocNum,
                    g.BaseNum
                }).Select(q => new
                {
                    DocNum = q.Key.DocNum,
                    BaseNum = q.Key.BaseNum
                });

                DateTime time = DateTime.Now;
                //按单号分组
                foreach (var mainInfo in mainInfoList)
                {
                    var zfgwms = new ZFGWMS001();
                    //List<ZFGWMS001> zfgwmsList = new List<ZFGWMS001>();
                   
                    int Line = 0;
                    var conditionList = entities.Where(x => x.DocNum == mainInfo.DocNum && x.BaseNum == mainInfo.BaseNum)?.ToList();

                    foreach (var condition in conditionList)
                    {
                        var zfgwms_1List = new List<ZFGWMS001_1>();
                        condition.Line = Line += 1;
                        //eq.Line= Line ;

                        zfgwms.VBELN = condition.BaseNum;
                        zfgwms.LFDAT = Convert.ToDateTime(Convert.ToDateTime(condition.CTime).ToString("yyyy-MM-dd"));
                        zfgwms.WAUHR = Convert.ToDateTime(Convert.ToDateTime(condition.CTime).ToString("HH:mm:ss"));//待定

                        var zfgwms_1 = new ZFGWMS001_1();
                        zfgwms_1.LGORT = condition.WhsCode;
                        zfgwms_1.LFIMG = condition.Qty;
                        //zfgwms_1.VGPOS = condition.VGPOS;//以后需要再拿回来
                        zfgwms_1.VGPOS = condition.BaseLine;

                        zfgwms_1.ZJHBS = condition.DocNum + "-" + condition.Line.ToString();
                        zfgwms_1.ZEREMARK = condition.Remark;
                        zfgwms_1List.Add(zfgwms_1);


                        bool ispost = false;
                        //string rtnErrMsg = "";
                        //purchaseReceipt.CompanyCode 公司代码
                        //查询Sap账期时间
                        DateTime ManualPostTime = new SAPApp().GetSAPpostTime(Convert.ToDateTime(conditionList[0].ManualPostTime));
                        List<SAPRETURN> saplist = _SAPCompanyInfoApp.ZFGWMS001("001", zfgwms, ManualPostTime, zfgwms_1List, out ispost, out error_message);
                        if (saplist != null && saplist.Count > 0)
                        {
                            //List<SD_DeliveryScan> queryList = new List<SD_DeliveryScan>();
                            foreach (SAPRETURN sap in saplist)
                            {
                                //var querys = conditionList.Where(x => x.DocNum == mainInfo.DocNum && x.BaseNum == sap.basenum).ToList();
                                //foreach (var query in querys)
                                //{
                                condition.IsPosted = true;
                                condition.PostUser = postUser;
                                condition.PostTime = time;
                                condition.MUser = postUser;
                                condition.MTime = time;
                                condition.SapDocNum = sap.sapDocNum;
                                condition.ManualPostTime = ManualPostTime;
                                condition.SAPmark = "S";

                                    if (Update(condition) > 0)
                                    {
                                        //待完善
                                        // 插入收货记录成功才能更新库存
                                        string stockMsg = string.Empty;
                                        MD_Stock stock = new MD_Stock();
                                        stock.BarCode = condition.BarCode ?? "";
                                        stock.BatchNum = condition.BatchNum ?? "";

                                        stock.ItemCode = condition.ItemCode;
                                        stock.ItemName = condition.ItemName;
                                        stock.ItmsGrpCode = condition.ItmsGrpCode;
                                        stock.ItmsGrpName = condition.ItmsGrpName;
                                        stock.WhsCode = condition.WhsCode;
                                        stock.WhsName = condition.WhsName;
                                        stock.RegionCode = condition.RegionCode;
                                        stock.RegionName = condition.RegionName;
                                        stock.BinLocationCode = condition.BinLocationCode;
                                        stock.BinLocationName = condition.BinLocationName;
                                        stock.Qty = condition.Qty;
                                        stock.Unit = condition.Unit;
                                        stock.IsDelete = false;
                                        stock.SaleNum = condition.BaseNum ?? "";
                                        stock.SaleLine = condition.BaseLine ?? 0;
                                        if (!string.IsNullOrEmpty(stock.SaleNum.Trim()))
                                            stock.SpecialStock = "E";//销售库存

                                        if (!_stockApp.StockIn(stock, postUser, out stockMsg))
                                        {
                                            error_message = stockMsg;
                                            //return false;
                                        }
                                   // }

                                    //queryList.Add(query);
                                }
                            }
                            //更新
                            //Update(queryList);
                        }
                        else
                        {

                            List<SD_ReturnScan> listpo = conditionList.Where(x => x.DocNum == mainInfo.DocNum).ToList();
                            foreach (SD_ReturnScan info in listpo)
                            {
                                info.SAPmark = "E";
                                info.SAPmessage = error_message;
                                info.MTime = DateTime.Now;
                            }
                            Update(listpo);

                            error_message = "单号：" + mainInfo.DocNum + "，过账失败，失败原因：" + error_message;
                            return ispost;
                        }
                    }
                }

                if (string.IsNullOrEmpty(error_message))
                    error_message = "过账成功";
                return true;
            }
            catch (Exception ex)
            {
                error_message = ex.Message;
                return false;
            }
        }

        #endregion

    }
}

