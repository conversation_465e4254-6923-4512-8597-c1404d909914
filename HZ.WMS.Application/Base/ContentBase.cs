using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using HZ.Core.Logging;
using HZ.Core.Security;
using HZ.WMS.Entity;
using SqlSugar;

namespace HZ.WMS.Application
{
    /// <summary>
    /// 
    /// </summary>
    public abstract class ContentBase : IDisposable
    {
        /// <summary>
        /// 
        /// </summary>
        public class T : BaseEntity
        {
        }

        #region 连接EAP中间库

        SqlSugarClient _dbContextForEAP;

        private string _dbKeyForEAP = "DbConnectionForEAP";

        /// <summary>
        /// 构造函数-EAP中间库
        /// </summary>
        public SqlSugarClient DbContextForEAP
        {
            get
            {
                SqlSugarClient Db = new SqlSugarClient(new ConnectionConfig()
                    {
                        ConnectionString = DbConfig.GetDbConnectionString(this._dbKeyForEAP),
                        DbType = DbType.SqlServer,
                        IsAutoCloseConnection = true
                    },
                    db =>
                    {
                        //5.1.3.24统一了语法和SqlSugarScope一样，老版本AOP可以写外面

                        db.Aop.OnLogExecuted = (sql, pars) =>
                        {
                            // Console.WriteLine(sql);//输出sql,查看执行sql 性能无影响

                            //获取原生SQL推荐 5.1.4.63  性能OK

                            bool isQuery = IsQuerySql(sql);
                            TimeSpan sqlTime = db.Ado.SqlExecutionTime;
                            if (!isQuery)
                            {
                                string nativeSql = UtilMethods.GetNativeSql(sql, pars);
                                LogHelper.Instance.LogInfo("数据操作记录 - 操作人：" + getCurrentAccount() + " - 用时：" +
                                                           sqlTime.Milliseconds + " ms" + "\nSQL：" + nativeSql +
                                                           "\n参数列表：\n" + pars);
                            }
                            else if (sqlTime.Seconds > 10)
                            {
                                string nativeSql = UtilMethods.GetNativeSql(sql, pars);
                                LogHelper.Instance.LogDebug("慢查询记录 - 操作人：" + getCurrentAccount() + " - 用时：" +
                                                            sqlTime.Milliseconds + " ms" + "\nSQL：" + nativeSql +
                                                            "\n参数列表：\n" + pars);
                            }

                            //获取无参数化SQL 对性能有影响，特别大的SQL参数多的，调试使用
                            //UtilMethods.GetSqlString(DbType.SqlServer,sql,pars)
                        };

                        //注意多租户 有几个设置几个
                        //db.GetConnection(i).Aop

                        //设置超时时间 单位 秒
                        db.Ado.CommandTimeOut = 300;
                    });
                return Db;
            }
            set { this._dbContextForEAP = value; }
        }

        #endregion

        #region 连接SAP中间库

        SqlSugarClient _dbContextForSAP;

        private string _dbKeyForSAP = "DbConnectionForSAP";

        /// <summary>
        /// 构造函数-SAP中间库
        /// </summary>
        public SqlSugarClient DbContextForSAP
        {
            get
            {
                SqlSugarClient Db = new SqlSugarClient(new ConnectionConfig()
                    {
                        ConnectionString = DbConfig.GetDbConnectionString(this._dbKeyForSAP),
                        DbType = DbType.SqlServer,
                        IsAutoCloseConnection = true
                    },
                    db =>
                    {
                        //5.1.3.24统一了语法和SqlSugarScope一样，老版本AOP可以写外面

                        db.Aop.OnLogExecuted = (sql, pars) =>
                        {
                            // Console.WriteLine(sql);//输出sql,查看执行sql 性能无影响

                            //获取原生SQL推荐 5.1.4.63  性能OK

                            bool isQuery = IsQuerySql(sql);
                            TimeSpan sqlTime = db.Ado.SqlExecutionTime;
                            if (!isQuery)
                            {
                                string nativeSql = UtilMethods.GetNativeSql(sql, pars);
                                LogHelper.Instance.LogInfo("数据操作记录 - 操作人：" + getCurrentAccount() + " - 用时：" +
                                                           sqlTime.Milliseconds + " ms" + "\nSQL：" + nativeSql +
                                                           "\n参数列表：\n" + pars);
                            }
                            else if (sqlTime.Seconds > 10)
                            {
                                string nativeSql = UtilMethods.GetNativeSql(sql, pars);
                                LogHelper.Instance.LogDebug("慢查询记录 - 操作人：" + getCurrentAccount() + " - 用时：" +
                                                            sqlTime.Milliseconds + " ms" + "\nSQL：" + nativeSql +
                                                            "\n参数列表：\n" + pars);
                            }

                            //获取无参数化SQL 对性能有影响，特别大的SQL参数多的，调试使用
                            //UtilMethods.GetSqlString(DbType.SqlServer,sql,pars)
                        };

                        //注意多租户 有几个设置几个
                        //db.GetConnection(i).Aop

                        //设置超时时间 单位 秒
                        db.Ado.CommandTimeOut = 300;
                    });
                return Db;
            }
            set { this._dbContextForSAP = value; }
        }

        #endregion

        #region 连接SRM中间库

        SqlSugarClient _dbContextForSRM;

        private string _dbKeyForSRM = "DbConnectionForSRM";

        /// <summary>
        /// 构造函数-SRM数据库
        /// </summary>
        public SqlSugarClient DbContextForSRM
        {
            get
            {
                SqlSugarClient Db = new SqlSugarClient(new ConnectionConfig()
                    {
                        ConnectionString = DbConfig.GetDbConnectionString(this._dbKeyForSRM),
                        DbType = DbType.SqlServer,
                        IsAutoCloseConnection = true
                    },
                    db =>
                    {
                        //5.1.3.24统一了语法和SqlSugarScope一样，老版本AOP可以写外面

                        db.Aop.OnLogExecuted = (sql, pars) =>
                        {
                            // Console.WriteLine(sql);//输出sql,查看执行sql 性能无影响

                            //获取原生SQL推荐 5.1.4.63  性能OK

                            bool isQuery = IsQuerySql(sql);
                            TimeSpan sqlTime = db.Ado.SqlExecutionTime;
                            if (!isQuery)
                            {
                                string nativeSql = UtilMethods.GetNativeSql(sql, pars);
                                LogHelper.Instance.LogInfo("数据操作记录 - 操作人：" + getCurrentAccount() + " - 用时：" +
                                                           sqlTime.Milliseconds + " ms" + "\nSQL：" + nativeSql +
                                                           "\n参数列表：\n" + pars);
                            }
                            else if (sqlTime.Seconds > 10)
                            {
                                string nativeSql = UtilMethods.GetNativeSql(sql, pars);
                                LogHelper.Instance.LogDebug("慢查询记录 - 操作人：" + getCurrentAccount() + " - 用时：" +
                                                            sqlTime.Milliseconds + " ms" + "\nSQL：" + nativeSql +
                                                            "\n参数列表：\n" + pars);
                            }

                            //获取无参数化SQL 对性能有影响，特别大的SQL参数多的，调试使用
                            //UtilMethods.GetSqlString(DbType.SqlServer,sql,pars)
                        };

                        //注意多租户 有几个设置几个
                        //db.GetConnection(i).Aop

                        //设置超时时间 单位 秒
                        db.Ado.CommandTimeOut = 300;
                    });
                return Db;
            }
            set { this.DbContextForSRM = value; }
        }

        #endregion

        #region 连接OMS中间库

        SqlSugarClient _dbContextForOMS;

        private string _dbKeyForOMS = "DbConnectionForOMS";

        /// <summary>
        /// 构造函数-SRM数据库
        /// </summary>
        public SqlSugarClient DbContextForOMS
        {
            get
            {
                SqlSugarClient Db = new SqlSugarClient(new ConnectionConfig()
                    {
                        ConnectionString = DbConfig.GetDbConnectionString(this._dbKeyForOMS),
                        DbType = DbType.SqlServer,
                        IsAutoCloseConnection = true
                    },
                    db =>
                    {
                        //5.1.3.24统一了语法和SqlSugarScope一样，老版本AOP可以写外面

                        db.Aop.OnLogExecuted = (sql, pars) =>
                        {
                            // Console.WriteLine(sql);//输出sql,查看执行sql 性能无影响

                            //获取原生SQL推荐 5.1.4.63  性能OK

                            bool isQuery = IsQuerySql(sql);
                            TimeSpan sqlTime = db.Ado.SqlExecutionTime;
                            if (!isQuery)
                            {
                                string nativeSql = UtilMethods.GetNativeSql(sql, pars);
                                LogHelper.Instance.LogInfo("数据操作记录 - 操作人：" + getCurrentAccount() + " - 用时：" +
                                                           sqlTime.Milliseconds + " ms" + "\nSQL：" + nativeSql +
                                                           "\n参数列表：\n" + pars);
                            }
                            else if (sqlTime.Seconds > 10)
                            {
                                string nativeSql = UtilMethods.GetNativeSql(sql, pars);
                                LogHelper.Instance.LogDebug("慢查询记录 - 操作人：" + getCurrentAccount() + " - 用时：" +
                                                            sqlTime.Milliseconds + " ms" + "\nSQL：" + nativeSql +
                                                            "\n参数列表：\n" + pars);
                            }

                            //获取无参数化SQL 对性能有影响，特别大的SQL参数多的，调试使用
                            //UtilMethods.GetSqlString(DbType.SqlServer,sql,pars)
                        };

                        //注意多租户 有几个设置几个
                        //db.GetConnection(i).Aop

                        //设置超时时间 单位 秒
                        db.Ado.CommandTimeOut = 300;
                    });
                return Db;
            }
            set { this._dbContextForOMS = value; }
        }

        #endregion

        #region 连接Oracle数据库

        SqlSugarClient _dbContextForOracle;

        private string _dbKeyForOracle = "DbConnectionForOracle";

        /// <summary>
        /// 构造函数-Oracle数据库
        /// </summary>
        public SqlSugarClient DbContextForOracle
        {
            get
            {
                SqlSugarClient Db = new SqlSugarClient(new ConnectionConfig()
                    {
                        ConnectionString = DbConfig.GetDbConnectionStringForOracle(this._dbKeyForOracle),
                        DbType = DbType.Oracle,
                        IsAutoCloseConnection = true,
                        InitKeyType = InitKeyType.Attribute,
                        MoreSettings = new ConnMoreSettings
                        {
                            IsAutoRemoveDataCache = true,
                            IsAutoToUpper=false
                        }
                    },
                    db =>
                    {
                        //5.1.3.24统一了语法和SqlSugarScope一样，老版本AOP可以写外面

                        db.Aop.OnLogExecuted = (sql, pars) =>
                        {
                            // Console.WriteLine(sql);//输出sql,查看执行sql 性能无影响

                            //获取原生SQL推荐 5.1.4.63  性能OK

                            bool isQuery = IsQuerySql(sql);
                            TimeSpan sqlTime = db.Ado.SqlExecutionTime;
                            if (!isQuery)
                            {
                                string nativeSql = UtilMethods.GetNativeSql(sql, pars);
                                LogHelper.Instance.LogInfo("Oracle数据操作记录 - 操作人：" + getCurrentAccount() + " - 用时：" +
                                                           sqlTime.Milliseconds + " ms" + "\nSQL：" + nativeSql +
                                                           "\n参数列表：\n" + pars);
                            }
                            else if (sqlTime.Seconds > 10)
                            {
                                string nativeSql = UtilMethods.GetNativeSql(sql, pars);
                                LogHelper.Instance.LogDebug("Oracle慢查询记录 - 操作人：" + getCurrentAccount() + " - 用时：" +
                                                            sqlTime.Milliseconds + " ms" + "\nSQL：" + nativeSql +
                                                            "\n参数列表：\n" + pars);
                            }

                            //获取无参数化SQL 对性能有影响，特别大的SQL参数多的，调试使用
                            //UtilMethods.GetSqlString(DbType.Oracle,sql,pars)
                        };

                        //注意多租户 有几个设置几个
                        //db.GetConnection(i).Aop

                        //设置超时时间 单位 秒
                        db.Ado.CommandTimeOut = 300;
                    });
                return Db;
            }
            set { this._dbContextForOracle = value; }
        }

        #endregion

        SqlSugarClient _dbContextSqlSugar;

        private string _dbKey;

        /// <summary>
        /// 
        /// </summary>
        protected ContentBase()
        {
            this._dbKey = "DbConnection";
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="dbKey"></param>
        protected ContentBase(string dbKey)
        {
            this._dbKey = dbKey;
        }

        /// <summary>
        /// 构造函数 - 使用连接池管理器
        /// </summary>
        public SqlSugarClient DbContext
        {
            get
            {
                // 每次都创建新的连接实例，避免连接状态问题
                return CreateSafeDbContext();
            }
            set { this._dbContextSqlSugar = value; }
        }

        /// <summary>
        /// 创建安全的数据库上下文
        /// </summary>
        /// <returns></returns>
        private SqlSugarClient CreateSafeDbContext()
        {
            var connectionString = ConnectionStringBuilder.BuildOptimizedConnectionString(this._dbKey);
            var dbType = this._dbKey == "DbConnectionForOracle" ? DbType.Oracle : DbType.SqlServer;

            var config = new ConnectionConfig()
            {
                ConnectionString = connectionString,
                DbType = dbType,
                IsAutoCloseConnection = true, // 确保连接自动关闭
                MoreSettings = new ConnMoreSettings
                {
                    IsAutoRemoveDataCache = true,
                    IsAutoToUpper = false,
                    IsWithNoLockQuery = true,
                    DefaultCacheDurationInSeconds = 600
                }
            };

            var client = new SqlSugarClient(config);

            // AOP配置
            client.Aop.OnLogExecuted = (sql, pars) =>
            {
                var isQuery = IsQuerySql(sql);
                var sqlTime = client.Ado.SqlExecutionTime;

                if (!isQuery)
                {
                    var nativeSql = UtilMethods.GetNativeSql(sql, pars);
                    LogHelper.Instance.LogInfo($"数据操作记录 - 用时：{sqlTime.Milliseconds} ms\nSQL：{nativeSql}");
                }
                else if (sqlTime.Seconds > 10)
                {
                    var nativeSql = UtilMethods.GetNativeSql(sql, pars);
                    LogHelper.Instance.LogDebug($"慢查询记录 - 用时：{sqlTime.Milliseconds} ms\nSQL：{nativeSql}");
                }
            };

            // 连接异常处理
            client.Aop.OnError = (exp) =>
            {
                if (exp.Message.Contains("timeout") || exp.Message.Contains("pool") ||
                    exp.Message.Contains("ExecuteReader") || exp.Message.Contains("Connection"))
                {
                    LogHelper.Instance.LogError($"{this._dbKey} 连接相关异常: {exp.Message}");
                }
                else
                {
                    LogHelper.Instance.LogError($"数据库操作异常: {exp.Message}");
                }
            };

            // 设置超时时间
            client.Ado.CommandTimeOut = 300;

            return client;
        }

        /// <summary>
        /// 安全执行数据库操作
        /// </summary>
        /// <typeparam name="T">返回类型</typeparam>
        /// <param name="operation">操作函数</param>
        /// <returns></returns>
        protected T ExecuteSafely<T>(Func<SqlSugarClient, T> operation)
        {
            return ExecuteWithRetry(operation, 3);
        }

        /// <summary>
        /// 安全执行异步数据库操作
        /// </summary>
        /// <typeparam name="T">返回类型</typeparam>
        /// <param name="operation">异步操作函数</param>
        /// <returns></returns>
        protected async Task<T> ExecuteSafelyAsync<T>(Func<SqlSugarClient, Task<T>> operation)
        {
            return await ExecuteWithRetryAsync(operation, 3);
        }

        /// <summary>
        /// 带重试的数据库操作执行
        /// </summary>
        /// <typeparam name="T">返回类型</typeparam>
        /// <param name="operation">操作函数</param>
        /// <param name="maxRetries">最大重试次数</param>
        /// <returns></returns>
        private T ExecuteWithRetry<T>(Func<SqlSugarClient, T> operation, int maxRetries)
        {
            Exception lastException = null;

            for (int i = 0; i < maxRetries; i++)
            {
                try
                {
                    using (var client = CreateSafeDbContext())
                    {
                        // 确保连接状态正常
                        EnsureConnectionState(client);
                        return operation(client);
                    }
                }
                catch (Exception ex) when (IsConnectionRelatedError(ex))
                {
                    lastException = ex;
                    LogHelper.Instance.LogError($"数据库连接错误，第 {i + 1} 次重试: {ex.Message}");

                    if (i < maxRetries - 1)
                    {
                        // 等待一段时间后重试
                        System.Threading.Thread.Sleep(1000 * (i + 1));
                    }
                }
            }

            throw new Exception($"数据库操作失败，已重试 {maxRetries} 次", lastException);
        }

        /// <summary>
        /// 带重试的异步数据库操作执行
        /// </summary>
        /// <typeparam name="T">返回类型</typeparam>
        /// <param name="operation">异步操作函数</param>
        /// <param name="maxRetries">最大重试次数</param>
        /// <returns></returns>
        private async Task<T> ExecuteWithRetryAsync<T>(Func<SqlSugarClient, Task<T>> operation, int maxRetries)
        {
            Exception lastException = null;

            for (int i = 0; i < maxRetries; i++)
            {
                try
                {
                    using (var client = CreateSafeDbContext())
                    {
                        // 确保连接状态正常
                        EnsureConnectionState(client);
                        return await operation(client);
                    }
                }
                catch (Exception ex) when (IsConnectionRelatedError(ex))
                {
                    lastException = ex;
                    LogHelper.Instance.LogError($"数据库连接错误，第 {i + 1} 次重试: {ex.Message}");

                    if (i < maxRetries - 1)
                    {
                        // 等待一段时间后重试
                        await Task.Delay(1000 * (i + 1));
                    }
                }
            }

            throw new Exception($"数据库操作失败，已重试 {maxRetries} 次", lastException);
        }

        /// <summary>
        /// 确保连接状态正常
        /// </summary>
        /// <param name="client"></param>
        private void EnsureConnectionState(SqlSugarClient client)
        {
            try
            {
                // 检查连接状态
                if (client.Ado.Connection.State != System.Data.ConnectionState.Open)
                {
                    client.Ado.Connection.Open();
                }

                // 执行简单查询测试连接
                client.Ado.GetString("SELECT 1");
            }
            catch (Exception ex)
            {
                LogHelper.Instance.LogError($"连接状态检查失败: {ex.Message}");

                // 尝试重新打开连接
                try
                {
                    if (client.Ado.Connection.State != System.Data.ConnectionState.Closed)
                    {
                        client.Ado.Connection.Close();
                    }
                    client.Ado.Connection.Open();
                }
                catch (Exception reopenEx)
                {
                    throw new Exception("无法重新建立数据库连接", reopenEx);
                }
            }
        }

        /// <summary>
        /// 判断是否为连接相关错误
        /// </summary>
        /// <param name="ex"></param>
        /// <returns></returns>
        private bool IsConnectionRelatedError(Exception ex)
        {
            var message = ex.Message.ToLower();
            return message.Contains("executereader") ||
                   message.Contains("connection") ||
                   message.Contains("timeout") ||
                   message.Contains("pool") ||
                   message.Contains("已打开且可用") ||
                   message.Contains("当前状态") ||
                   message.Contains("网络相关") ||
                   message.Contains("传输级错误");
        }

        /// <summary>
        /// 原始DbContext实现（备用）
        /// </summary>
        public SqlSugarClient DbContextOriginal
        {
            get
            {
                SqlSugarClient Db = new SqlSugarClient(new ConnectionConfig()
                    {
                        ConnectionString = ConnectionStringBuilder.BuildOptimizedConnectionString(this._dbKey),
                        DbType = DbType.SqlServer,
                        IsAutoCloseConnection = true
                    },
                    db =>
                    {
                        //5.1.3.24统一了语法和SqlSugarScope一样，老版本AOP可以写外面

                        db.Aop.OnLogExecuted = (sql, pars) =>
                        {
                            // Console.WriteLine(sql);//输出sql,查看执行sql 性能无影响

                            //获取原生SQL推荐 5.1.4.63  性能OK

                            bool isQuery = IsQuerySql(sql);
                            TimeSpan sqlTime = db.Ado.SqlExecutionTime;
                            if (!isQuery)
                            {
                                string nativeSql = UtilMethods.GetNativeSql(sql, pars);
                                LogHelper.Instance.LogInfo("数据操作记录 - 操作人：" + getCurrentAccount() + " - 用时：" +
                                                           sqlTime.Milliseconds + " ms" + "\nSQL：" + nativeSql +
                                                           "\n参数列表：\n" + pars);
                            }
                            else if (sqlTime.Seconds > 10)
                            {
                                string nativeSql = UtilMethods.GetNativeSql(sql, pars);
                                LogHelper.Instance.LogDebug("慢查询记录 - 操作人：" + getCurrentAccount() + " - 用时：" +
                                                            sqlTime.Milliseconds + " ms" + "\nSQL：" + nativeSql +
                                                            "\n参数列表：\n" + pars);
                            }

                            //获取无参数化SQL 对性能有影响，特别大的SQL参数多的，调试使用
                            //UtilMethods.GetSqlString(DbType.SqlServer,sql,pars)
                        };

                        //注意多租户 有几个设置几个
                        //db.GetConnection(i).Aop

                        //设置超时时间 单位 秒
                        db.Ado.CommandTimeOut = 300;
                    });
                return Db;
            }
            set { this._dbContextSqlSugar = value; }
        }

        /* 判断是否执行的查询语句 */
        private bool IsQuerySql(string sql)
        {
            string result = sql.Trim();
            // 假设 SELECT、SHOW、DESCRIBE、EXPLAIN 为查询语句
            string[] queryKeywords = { "SELECT", "SHOW", "DESCRIBE", "EXPLAIN" };

            // 使用无大小写的比较方式判断 SQL 是否以查询关键词开头
            foreach (var keyword in queryKeywords)
            {
                if (result.StartsWith(keyword, StringComparison.OrdinalIgnoreCase))
                {
                    return true;
                }
            }

            return false;
        }

        /* 获取操作人 */
        private string getCurrentAccount()
        {
            var request = HttpContext.Current.Request;
            var token = request.Headers.GetValues("X-Token");
            var account = "";
            if (token != null && token.Count() > 0)
            {
                account = TokenUtil.GetUid(token[0]);
            }

            return account;
        }

        /// <summary>
        /// 批量插入
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="entities"></param>
        public void BulkInsert<T>(List<T> entities) where T : class, new()
        {
            DbContext.Insertable(entities).ExecuteCommand();
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (this._dbContextSqlSugar != null)
            {
                this._dbContextSqlSugar.Dispose();
            }

            this.Dispose(true);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="disposing"></param>
        protected virtual void Dispose(bool disposing)
        {
        }
    }
}