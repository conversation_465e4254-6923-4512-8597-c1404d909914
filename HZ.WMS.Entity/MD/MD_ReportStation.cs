using SqlSugar;
using System.ComponentModel;

namespace HZ.WMS.Entity.MD
{
    /// <summary>
    /// 报工站点
    /// </summary>
    public class MD_ReportStation : BaseEntity
    {
        /// <summary>
        /// 主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Description("主键")]
        public string Id { get; set; }

        /// <summary>
        /// 工作中心名称
        /// </summary>
        [Description("工作中心名称")]
        public string WorkCenterName { get; set; }

        /// <summary>
        /// 工作中心编码
        /// </summary>
        [Description("工作中心编码")]
        public string WorkCenterCode { get; set; }

        /// <summary>
        /// 工序号
        /// </summary>
        [Description("工序号")]
        public int? ProcessNo { get; set; }

        /// <summary>
        /// 工序短文本
        /// </summary>
        [Description("工序短文本")]
        public string ProcessShortText { get; set; }

        /// <summary>
        /// 站点代码
        /// </summary>
        [Description("站点代码")]
        public string StationCode { get; set; }

        /// <summary>
        /// 站点名称
        /// </summary>
        [Description("站点名称")]
        public string StationName { get; set; }

        /// <summary>
        /// 站点序号
        /// </summary>
        [Description("站点序号")]
        public decimal? StationSort { get; set; }

        /// <summary>
        /// 启用报工 0未启用 1启用
        /// </summary>
        [Description("启用报工")]
        public int? Enable { get; set; }
    }
}
