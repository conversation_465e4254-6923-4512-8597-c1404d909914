using System;
using System.ComponentModel;
using HZ.WMS.Entity;
using SqlSugar;

namespace AOS.OMS.Entity.Sale
{
    /// <summary>
    /// 订单明细类
    /// </summary>
    public class SD_Sale_OrderDetails : BaseEntity
    {
        /// <summary>
        /// 数据主键ID
        /// </summary>
        [Description("数据主键ID")]
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 订单编号
        /// </summary>
        [Description("订单编号")]
        public string OrderNum { get; set; }

        /// <summary>
        /// 订单分录行号
        /// </summary>
        [Description("订单分录行号")]
        public int? LineNum { get; set; }

        /// <summary>
        /// 行类别,1-销售 2-otis首次 3-试用 4-借用 5-赠送
        /// </summary>  
        [Description("行类别")]
        public int OrderType { get; set; }

        /// <summary>
        /// 客户订单号
        /// </summary>
        [Description("客户订单号")]
        public string CustomerOrderNum { get; set; }

        /// <summary>
        /// 合同号
        /// </summary>
        [Description("合同号")]
        public string ContractNum { get; set; }

        /// <summary>
        /// 出厂编号
        /// </summary>
        [Description("出厂编号")]
        public string SerialValue { get; set; }

        /// <summary>
        /// 客户型号
        /// </summary>  
        [Description("客户型号")]
        public string CustomerModel { get; set; }

        /// <summary>
        /// 客户件号
        /// </summary>
        [Description("客户件号")]
        public string CustomerItemCode { get; set; }

        /// <summary>
        /// 富沃德件号
        /// </summary>
        [Description("富沃德件号")]
        public string FWDItemCode { get; set; }

        /// <summary>
        /// 物料描述
        /// </summary>
        [Description("物料描述")]
        public string FWDItemDisc { get; set; }

        /// <summary>
        /// 非标接受编号
        /// </summary>
        public string NonstandardNum { get; set; }

        /// <summary>
        /// 主机非标编号
        /// </summary>
        [Description("主机非标编号")]
        public string Host_Nonstandard { get; set; }

        /// <summary>
        /// 曳引轮非标编号
        /// </summary>
        [Description("曳引轮非标编号")]
        public string YYL_Nonstandard { get; set; }

        /// <summary>
        /// 包材非标编号
        /// </summary>
        [Description("包材非标编号")]
        public string Package_Nonstandard { get; set; }

        /// <summary>
        /// 系列
        /// </summary>
        [Description("系列")]
        public string Series { get; set; }

        /// <summary>
        /// 机型
        /// </summary>
        [Description("机型")]
        public string ProductModel { get; set; }

        /// <summary>
        /// 额定载重
        /// </summary>
        [Description("额定载重")]
        public string RatedLoad { get; set; }

        /// <summary>
        /// 额定梯速
        /// </summary>
        [Description("额定梯速")]
        public string TISU { get; set; }

        /// <summary>
        /// 额定电压
        /// </summary>
        [Description("额定电压")]
        public string DIANYA { get; set; }

        /// <summary>
        /// 制动器电压
        /// </summary>
        [Description("制动器电压")]
        public string ZDQDIANYA { get; set; }

        /// <summary>
        /// 制动器型号
        /// </summary>
        [Description("制动器型号")]
        public string ZDQMODEL { get; set; }

        /// <summary>
        /// 编码器
        /// </summary>
        [Description("编码器")]
        public string BMQ { get; set; }

        /// <summary>
        /// 盘车装置
        /// </summary>
        [Description("盘车装置")]
        public string PCZH { get; set; }

        /// <summary>
        /// 远程松闸
        /// </summary>
        [Description("远程松闸")]
        public string YCSZ { get; set; }

        /// <summary>
        /// 曳引比
        /// </summary>
        [Description("曳引比")]
        public string YYB { get; set; }

        /// <summary>
        /// 曳引轮节径
        /// </summary>
        [Description("曳引轮节径")]
        public string YYLJJ { get; set; }

        /// <summary>
        /// 曳引轮-槽数
        /// </summary>
        [Description("曳引轮-槽数")]
        public string YYLCS { get; set; }

        /// <summary>
        /// 曳引轮-槽距
        /// </summary>
        [Description("曳引轮-槽距")]
        public string YYLCJ { get; set; }

        /// <summary>
        /// 曳引轮-绳槽直径
        /// </summary>
        [Description("曳引轮-绳槽直径")]
        public string YYLSCZJ { get; set; }

        /// <summary>
        /// 曳引轮-β
        /// </summary>
        [Description("曳引轮-β")]
        public string YYLBETA { get; set; }

        /// <summary>
        /// 曳引轮-γ
        /// </summary>
        [Description("曳引轮-γ")]
        public string YYLYZ { get; set; }

        /// <summary>
        /// 曳引轮-槽型
        /// </summary>
        [Description("曳引轮-槽型")]
        public string YYLCX { get; set; }

        /// <summary>
        /// 曳引轮
        /// </summary>
        [Description("曳引轮")]
        public string YYL { get; set; }

        /// <summary>
        /// 曳引轮件号
        /// </summary>
        [Description("曳引轮件号")]
        public string YYLItemCode { get; set; }

        /// <summary>
        /// 曳引轮描述
        /// </summary>
        [Description("曳引轮描述")]
        public string YYLItemDisc { get; set; }

        /// <summary>
        /// 曳引轮数量
        /// </summary>
        [Description("曳引轮数量")]
        public int? YYLQuantity { get; set; }

        /// <summary>
        /// 进/出口
        /// </summary>
        [Description("进/出口")]
        public string JCK { get; set; }

        /// <summary>
        /// 油漆
        /// </summary>
        [Description("油漆")]
        public string YOUQI { get; set; }

        /// <summary>
        /// 刷字
        /// </summary>
        [Description("刷字")]
        public string SHUAZI { get; set; }

        /// <summary>
        /// 主机铭牌
        /// </summary>
        [Description("主机铭牌")]
        public string ZJMP { get; set; }

        /// <summary>
        /// 软管
        /// </summary>
        [Description("软管")]
        public string RUANGUAN { get; set; }

        /// <summary>
        /// 编码器线长
        /// </summary>
        [Description("编码器线长")]
        public string BMQXC { get; set; }

        /// <summary>
        /// 松闸线长
        /// </summary>
        [Description("松闸线长")]
        public string SZXC { get; set; }

        /// <summary>
        /// 减震垫
        /// </summary>
        [Description("减震垫")]
        public string JZD { get; set; }

        /// <summary>
        /// 变频器
        /// </summary>
        [Description("变频器")]
        public string BPQ { get; set; }

        /// <summary>
        /// 防护罩
        /// </summary>
        [Description("防护罩")]
        public string FHZ { get; set; }

        /// <summary>
        /// 打包件
        /// </summary>
        [Description("打包件")]
        public string DBJ { get; set; }

        /// <summary>
        /// 能效等级
        /// </summary>
        [Description("能效等级")]
        public string NXDJ { get; set; }

        /// <summary>
        /// 电机左右置
        /// </summary>
        [Description("电机左右置")]
        public string DJZYZ { get; set; }

        /// <summary>
        /// 客户代码VC
        /// </summary>
        [Description("客户代码VC")]
        public string KHA { get; set; }

        /// <summary>
        /// 生产备注
        /// </summary>
        [Description("生产备注")]
        public string ProductionRemark { get; set; }

        /// <summary>
        /// 客户发货地址
        /// </summary>  
        [Description("客户发货地址")]
        public string CustomerAddress { get; set; }

        /// <summary>
        /// 结算地址
        /// </summary>  
        [Description("结算地址")]
        public string SettlementAdd { get; set; }

        /// <summary>
        /// 项目名称描述
        /// </summary>
        [Description("项目名称描述")]
        public string ProjectDisc { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量")]
        public int? Quantity { get; set; }

        /// <summary>
        /// 货币
        /// </summary>
        [Description("货币")]
        public string HuoBi { get; set; }

        /// <summary>
        /// 价格
        /// </summary>
        [Description("价格")]
        public decimal? Price { get; set; }

        /// <summary>
        /// 含税价
        /// </summary>  
        [Description("含税价")]
        public decimal? TaxRatePrice { get; set; }

        /// <summary>
        /// 长协价格
        /// </summary>
        [Description("长协价格")]
        public decimal? AgreementPrice { get; set; }

        /// <summary>
        /// 佣金
        /// </summary>
        [Description("佣金")]
        public decimal? YongJin { get; set; }

        /// <summary>
        /// 返利
        /// </summary>
        [Description("返利")]
        public decimal? FanLi { get; set; }

        /// <summary>
        /// 运费
        /// </summary>
        [Description("运费")]
        public decimal? Freight { get; set; }

        /// <summary>
        /// 送货批次
        /// </summary>
        [Description("送货批次")]
        public string BatchNum { get; set; }

        /// <summary>
        /// 库存地点
        /// </summary>
        [Description("库存地点")]
        public string Inventory { get; set; }

        /// <summary>
        /// 订单到达日期
        /// </summary>
        [Description("订单到达日期")]
        public DateTime? ReleaseDate { get; set; }

        /// <summary>
        /// 过账操作人
        /// </summary>  
        [Description("过账操作人")]
        public string PostUser { get; set; }

        /// <summary>
        /// 过账日期
        /// </summary>  
        [Description("过账日期")]
        public DateTime? PostDate { get; set; }

        /// <summary>
        /// 状态
        /// </summary>  
        [Description("状态")]
        public string Status { get; set; }

        /// <summary>
        /// SAP过账单号
        /// </summary>  
        [Description("SAP过账单号")]
        public string SAP_NO { get; set; }

        /// <summary>
        /// SAP过账单号
        /// </summary>  
        [Description("SAP过账行号")]
        public int? SAP_LINE { get; set; }

        /// <summary>
        /// 催货日期
        /// </summary>
        [Description("催货日期")]
        public DateTime? ExpeditingDate { get; set; }

        /// <summary>
        /// 操作催货日期
        /// </summary>
        [Description("操作催货日期")]
        public DateTime? OperateExpeditingDate { get; set; }

        /// <summary>
        /// 发运状态
        /// </summary>
        [Description("发运状态")]
        public string ShipmentStatus { get; set; }

        /// <summary>
        /// 发运日期
        /// </summary>
        [Description("发运日期")]
        public DateTime? ShipmentDate { get; set; }

        /// <summary>
        /// 交货日期
        /// </summary>
        [Description("交货日期")]
        public DateTime? DeliveryDate { get; set; }

        /// <summary>
        /// 外包尺寸
        /// </summary>
        [Description("外包尺寸")]
        public string WBCC { get; set; }

        /// <summary>
        /// 制动器接线
        /// </summary>
        [Description("制动器接线")]
        public string ZDQJX { get; set; }

        /// <summary>
        /// 复绕轮组件
        /// </summary>
        [Description("复绕轮组件")]
        public string FSLZJ { get; set; }

        /// <summary>
        /// 轮轴圆弧
        /// </summary>
        [Description("轮轴圆弧")]
        public string LZYH { get; set; }

        /// <summary>
        /// 绕法
        /// </summary>
        [Description("绕法")]
        public string RAOFA { get; set; }


        /// <summary>
        /// 部件类型
        /// </summary>
        [Description("部件类型")]
        public string PartOrderType { get; set; }

        /// <summary>
        /// 安全钳类型
        /// </summary>
        [Description("安全钳类型")]
        public string AQQMODEL { get; set; }

        /// <summary>
        /// 安全钳铭牌ID
        /// </summary>
        [Description("安全钳铭牌ID")]
        public string AQQMINGPAIID { get; set; }

        /// <summary>
        /// 安全钳铭牌
        /// </summary>
        [Description("安全钳铭牌")]
        public string AQQMINGPAI { get; set; }

        /// <summary>
        /// 安全钳包装箱ID
        /// </summary>
        [Description("安全钳包装箱ID")]
        public string AQQBZXID { get; set; }

        /// <summary>
        /// 安全钳包装箱
        /// </summary>
        [Description("安全钳包装箱")]
        public string AQQBZX { get; set; }

        /// <summary>
        /// 安全钳说明书ID
        /// </summary>
        [Description("安全钳说明书ID")]
        public string AQQSMSID { get; set; }

        /// <summary>
        /// 安全钳说明书
        /// </summary>
        [Description("安全钳说明书")]
        public string AQQSMS { get; set; }

        /// <summary>
        /// 安全钳标签ID
        /// </summary>
        [Description("安全钳标签ID")]
        public string AQQBiaoQianID { get; set; }

        /// <summary>
        /// 安全钳标签
        /// </summary>
        [Description("安全钳标签")]
        public string AQQBiaoQian { get; set; }

        /// <summary>
        /// 限速器型号
        /// </summary>
        [Description("限速器型号")]
        public string XSQMODEL { get; set; }

        /// <summary>
        /// 限速器铭牌ID
        /// </summary>
        [Description("限速器铭牌ID")]
        public string XSQMINGPAIID { get; set; }

        /// <summary>
        /// 限速器铭牌
        /// </summary>
        [Description("限速器铭牌")]
        public string XSQMINGPAI { get; set; }

        /// <summary>
        /// 限速器包装箱ID
        /// </summary>
        [Description("限速器包装箱ID")]
        public string XSQBZXID { get; set; }

        /// <summary>
        /// 限速器包装箱
        /// </summary>
        [Description("限速器包装箱")]
        public string XSQBZX { get; set; }

        /// <summary>
        /// 限速器说明书ID
        /// </summary>
        [Description("限速器说明书ID")]
        public string XSQSMSID { get; set; }

        /// <summary>
        /// 限速器说明书
        /// </summary>
        [Description("限速器说明书")]
        public string XSQSMS { get; set; }

        /// <summary>
        /// 限速器标签ID
        /// </summary>
        [Description("限速器标签ID")]
        public string XSQBiaoQianID { get; set; }

        /// <summary>
        /// 限速器标签
        /// </summary>
        [Description("限速器标签")]
        public string XSQBiaoQian { get; set; }

        /// <summary>
        /// A值
        /// </summary>
        [Description("A值")]
        public string AVALUE { get; set; }

        /// <summary>
        /// A值
        /// </summary>
        [Description("履行备注")]
        public string PerformRemark { get; set; }
    }
}