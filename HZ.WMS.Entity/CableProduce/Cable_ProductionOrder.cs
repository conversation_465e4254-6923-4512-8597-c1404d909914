using System;
using System.ComponentModel;
using HZ.WMS.Entity;
using SqlSugar;

namespace AOS.WMS.Entity.CableProduce
{
    public class Cable_ProductionOrder : BaseEntity
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Description("主键ID")]
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }
        
        /// <summary>
        /// 父主键ID
        /// </summary>
        [Description("父主键ID")]
        public string Pid { get; set; }

        /// <summary>
        /// 订单编号
        /// </summary>
        [Description("订单编号")]
        public string OmsOrderNum { get; set; }

        /// <summary>
        /// 订单行号
        /// </summary>
        [Description("订单行号")]
        public int? OmsLineNum { get; set; }
        
        /// <summary>
        /// 订单ID
        /// </summary>
        [Description("订单ID")]
        public string OrderId { get; set; }

        /// <summary>
        /// 订单状态
        /// </summary>
        [Description("订单状态")]
        public int Status { get; set; }

        /// <summary>
        /// 订单类型(1:主机订单 2:部件订单 3:售后部件 4:售后主机)
        /// </summary>
        [Description("订单类型")]
        public int OrderType { get; set; }

        /// <summary>
        /// 同步SAP销售单号
        /// </summary>
        [Description("同步SAP销售单号")]
        public string SapNo { get; set; }
        /// <summary>
        /// 同步SAP销售单号
        /// </summary>
        [Description("同步SAP销售行号")]
        public int SapLine { get; set; }

        /// <summary>
        /// 物料编码
        /// </summary>
        [Description("物料编码")]
        public string MaterialCode { get; set; }

        /// <summary>
        /// 客户件号
        /// </summary>
        [Description("客户件号")]
        public string CustomerPartNo { get; set; }

        /// <summary>
        /// 规格型号
        /// </summary>
        [Description("规格型号")]
        public string SpecificationModel { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量")]
        public decimal Quantity { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [Description("单位")]
        public string Unit { get; set; }

        /// <summary>
        /// 子件单位
        /// </summary>
        [Description("子件单位")]
        public string ComponentUnit { get; set; }

        /// <summary>
        /// 单根数量
        /// </summary>
        [Description("单根数量")]
        public string SingleQuantity { get; set; }

        /// <summary>
        /// 四线组
        /// </summary>
        [Description("四线组")]
        public string FourLineGroup { get; set; }

        /// <summary>
        /// 分类
        /// </summary>
        [Description("分类")]
        public string Classify { get; set; }

        /// <summary>
        /// 物料描述
        /// </summary>
        [Description("物料描述")]
        public string MaterialDes { get; set; }

        /// <summary>
        /// 合同号
        /// </summary>
        [Description("合同号")]
        public string ContractNo { get; set; }

        /// <summary>
        /// 计划订单状态
        /// </summary>
        [Description("计划订单状态")]
        public string PlanStatus { get; set; }

        /// <summary>
        /// 计划订单消息
        /// </summary>
        [Description("计划订单消息")]
        public string PlanMessage { get; set; }

        /// <summary>
        /// 计划订单号
        /// </summary>
        [Description("计划订单号")]
        public string PlanNo { get; set; }
        
        /// <summary>
        /// 计划订单创建时间
        /// </summary>
        [Description("计划订单创建时间")]
        public DateTime? PlanCTime { get; set; }

        /// <summary>
        /// 生产订单状态
        /// </summary>
        [Description("生产订单状态")]
        public string ProduceStatus { get; set; }

        /// <summary>
        /// 生产订单消息
        /// </summary>
        [Description("生产订单消息")]
        public string ProduceMessage { get; set; }

        /// <summary>
        /// 生产订单号
        /// </summary>
        [Description("生产订单号")]
        public string ProduceNo { get; set; }

        /// <summary>
        /// 生产释放状态
        /// </summary>
        [Description("生产释放状态")]
        public string ProduceReleaseStatus { get; set; }

        /// <summary>
        /// 生产释放消息
        /// </summary>
        [Description("生产释放消息")]
        public string ProduceReleaseMessage { get; set; }
        
        /// <summary>
        /// 生产订单创建时间
        /// </summary>
        [Description("生产订单创建时间")]
        public DateTime? ProduceCTime { get; set; }

        /// <summary>
        /// 报工日期
        /// </summary>
        [Description("报工日期")]
        public DateTime? ReportingWorkDate { get; set; }

        /// <summary>
        /// 装配日期
        /// </summary>
        [Description("装配日期")]
        public DateTime? AssembleDate { get; set; }

        /// <summary>
        /// 工序短文本
        /// </summary>
        [Description("工序短文本")]
        public string ProcessText { get; set; }

        /// <summary>
        /// 报工状态
        /// </summary>
        [Description("报工状态")]
        public string ReportingWorkStatus { get; set; }

        /// <summary>
        /// 报工消息
        /// </summary>
        [Description("报工消息")]
        public string ReportingWorkMessage { get; set; }
        
        /// <summary>
        /// 生产订单创建时间
        /// </summary>
        [Description("报工创建时间")]
        public DateTime? ReportingCTime { get; set; }

        /// <summary>
        /// 客户订单号
        /// </summary>
        [Description("客户订单号")]
        public string CustomerOrderNum { get; set; }

        /// <summary>
        /// 批次
        /// </summary>
        [Description("批次")]
        public string BatchNum { get; set; }

        /// <summary>
        /// 序列号
        /// </summary>
        [Description("序列号")]
        public string SerialNo { get; set; }

        /// <summary>
        /// 序列号分配时间
        /// </summary>
        [Description("序列号分配时间")]
        public DateTime? SerialNoCtime { get; set; }

        /// <summary>
        /// 子件编码
        /// </summary>
        [Description("子件编码")]
        public string ComponentCode { get; set; }
        
        /// <summary>
        /// 件号
        /// </summary>
        [Description("件号")]
        public string PartCode { get; set; }
        
        /// <summary>
        /// 释放时间
        /// </summary>
        [Description("释放时间")]
        public DateTime? ProduceReleaseCTime { get; set; }
        
        /// <summary>
        /// 释放人
        /// </summary>
        [Description("释放人")]
        public string ProduceReleaseCUser { get; set; }
        
        /// <summary>
        /// 报工人
        /// </summary>
        [Description("报工人")]
        public string ReportingCUser { get; set; }
        
        /// <summary>
        /// 计划订单创建人
        /// </summary>
        [Description("计划订单创建人")]
        public string PlanCUser { get; set; }
        
        /// <summary>
        /// 生产订单创建人
        /// </summary>
        [Description("生产订单创建人")]
        public string ProduceCUser { get; set; }
    }
}