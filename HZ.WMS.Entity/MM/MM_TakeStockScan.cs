//Chloe框架
using SqlSugar;
using System;
using System.ComponentModel;

namespace HZ.WMS.Entity.MM
{
    [SugarTable("MM_TakeStockScan")]
    public class MM_TakeStockScan : BaseEntity
    {
        /// <summary> 
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string ScanID { get; set; }
        /// <summary>
        /// 盘点计划单号
        /// </summary>
        [Description("盘点计划单号")]
        public string DocNum { get; set; }
        /// <summary>
        /// 包装码
        /// </summary>
        [Description("包装码")]
        public string BoxBarCode { get; set; }
        /// <summary>
        /// 出厂编号
        /// </summary>
        [Description("出厂编号")]
        public string BarCode { get; set; }
        /// <summary>
        /// 批次
        /// </summary>
        [Description("批次")]
        public string BatchNum { get; set; }

        /// <summary>
        /// 行号
        /// </summary>
        [Description("行号")]
        public int? Line { get; set; }

        
        /// <summary>
        /// 生产日期
        /// </summary>
        [Description("生产日期")]
        public DateTime? PTime { get; set; }
        /// <summary>
        /// 物料编号
        /// </summary>
        [Description("物料编号")]
        public string ItemCode { get; set; }
        /// <summary>
        /// 物料名称
        /// </summary>
        [Description("物料名称")]
        public string ItemName { get; set; }
        /// <summary>
        /// 物料组编号
        /// </summary>
        [Description("物料组编号")]
        public string ItmsGrpCode { get; set; }
        /// <summary>
        /// 物料组名称
        /// </summary>
        [Description("物料组名称")]
        public string ItmsGrpName { get; set; }
        /// <summary>
        /// 库存数量
        /// </summary>
        [Description("库存数量")]
        public decimal? StockQty { get; set; }
        /// <summary>
        /// 盘点数量
        /// </summary>
        [Description("盘点数量")]
        public decimal? ScanQty { get; set; }
        /// <summary>
        /// 差异数量
        /// </summary>
        [Description("差异数量")]
        public decimal? DiffQty { get; set; }
        /// <summary>
        /// 库存单位
        /// </summary>
        [Description("库存单位")]
        public string Unit { get; set; }
        /// <summary>
        /// 仓库编号
        /// </summary>
        [Description("仓库编号")]
        public string WhsCode { get; set; }
        /// <summary>
        /// 仓库名称
        /// </summary>
        [Description("仓库名称")]
        public string WhsName { get; set; }
        /// <summary>
        /// 区域编号
        /// </summary>
        [Description("区域编号")]
        public string RegionCode { get; set; }
        /// <summary>
        /// 区域名称
        /// </summary>
        [Description("区域名称")]
        public string RegionName { get; set; }
        /// <summary>
        /// 库位编号
        /// </summary>
        [Description("库位编号")]
        public string BinLocationCode { get; set; }
        /// <summary>
        /// 库位名称
        /// </summary>
        [Description("库位名称")]
        public string BinLocationName { get; set; }
        /// <summary>
        /// 是否已确认
        /// </summary>
        [Description("是否已确认")]
        public bool IsConfirm { get; set; } = false;
        /// <summary>
        /// 供应商编号
        /// </summary>
        [Description("供应商编号")]
        public string SupplierCode { get; set; }

        /// <summary>
        /// 供应商
        /// </summary>
        [Description("供应商")]
        public string SupplierName { get; set; }



        /// <summary>
        /// 销售单号
        /// </summary>
        [Description("销售单号")]
        public string SaleNum { get; set; }

        /// <summary>
        /// 销售行号
        /// </summary>
        [Description("销售行号")]
        public int? SaleLine { get; set; }

        /// <summary>
        /// 评估类型
        /// </summary>
        [Description("评估类型")]
        public string AssessType { get; set; }


        /// <summary>
        /// 特殊库存 O:带供应商代码的,E:带销售订单的,T: 是在途库存
        /// </summary>
        [Description("特殊库存")]
        public string SpecialStock { get; set; }


        /// <summary> 
        /// 过账时间
        /// </summary> 
        [Description("过账时间")]
        public DateTime? ManualPostTime { get; set; }

        /// <summary> 
        /// 是否已过账
        /// </summary> 
        [Description("是否已过账")]
        public bool? IsPosted { get; set; }

        /// <summary> 
        /// 过账人
        /// </summary> 
        [Description("过账人")]
        public string PostUser { get; set; }

        /// <summary> 
        /// 过账时间
        /// </summary> 
        [Description("过账时间")]
        public DateTime? PostTime { get; set; }

        /// <summary>
        /// SAP物料凭证单号
        /// </summary>
        [Description("SAP物料凭证单号")]
        public string SapDocNum { get; set; }

        /// <summary>
        /// SAP物料凭证行号
        /// </summary>
        [Description("SAP物料凭证行号")]
        public int? SapLine { get; set; }

        /// <summary>
        /// SAP过账标示
        /// </summary>
        [Description("SAP过账标示")]
        public string SAPmark { get; set; }

        /// <summary>
        /// SAP过账信息
        /// </summary>
        [Description("SAP过账信息")]
        public string SAPmessage { get; set; }
    }
}


