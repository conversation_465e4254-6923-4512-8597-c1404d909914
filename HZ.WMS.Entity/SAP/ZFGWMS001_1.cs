using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.SAP
{
    public class ZFGWMS001_1
    {
        /// <summary>
        /// 销售和分销凭证号
        /// </summary>
        public string VBELN { get; set; }
        /// <summary>
        /// 销售和分销凭证的项目号
        /// </summary>
        public int? POSNR { get; set; }
        /// <summary>
        /// 销售凭证项目类别
        /// </summary>
        public string PSTYV { get; set; }
        /// <summary>
        /// 物料编号
        /// </summary>
        public string MATNR { get; set; }
        /// <summary>
        /// 工厂
        /// </summary>
        public string WERKS { get; set; }
        /// <summary>
        /// 库存地点
        /// </summary>
        public string LGORT { get; set; }
        /// <summary>
        /// 批号
        /// </summary>
        public string CHARG { get; set; }

        /// <summary>
        /// 实际已交货量（按销售单位）
        /// </summary>
        public decimal? LFIMG { get; set; }
        /// <summary>
        /// 基本计量单位
        /// </summary>
        public string MEINS { get; set; }
        /// <summary>
        /// 净重
        /// </summary>
        public decimal? NTGEW { get; set; }
        /// <summary>
        /// 毛重
        /// </summary>
        public decimal? BRGEW { get; set; }
        /// <summary>
        /// 重量单位
        /// </summary>
        public string GEWEI { get; set; }
        /// <summary>
        /// 参考单据的单据编号
        /// </summary>
        public string VGBEL { get; set; }

        /// <summary>
        /// 参考项目的项目号
        /// </summary>
        public int? VGPOS { get; set; }
        /// <summary>
        /// 利润中心
        /// </summary>
        public string PRCTR { get; set; }
        /// <summary>
        /// 拣配状态/入库状态（项目） 
        /// </summary>
        public string KOSTA { get; set; }

        /// <summary>
        /// WMS交货标识 WMS发货单号+行号
        /// </summary>
        public string ZJHBS { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string ZEREMARK { get; set; }
        
    }
}
