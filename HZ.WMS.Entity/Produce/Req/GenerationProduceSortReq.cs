using System;
using System.ComponentModel;

namespace HZ.WMS.Entity.SD
{
    /// <summary>
    /// 生成排产序号
    /// </summary>
    public class GenerationProduceSortReq
    {

        /// <summary>
        /// 排产日期
        /// </summary>
        [Description("排产日期")]
        public DateTime[] ProduceSchedulingDate { get; set; }

        /// <summary>
        /// 生产线
        /// </summary>
        [Description("生产线")]
        public string ProduceLine { get; set; }

        /// <summary>
        /// 是否重新生成序号
        /// </summary>
        [Description("是否重新生成序号")]
        public bool IsRegenerate { get; set; } = false;

    }
}