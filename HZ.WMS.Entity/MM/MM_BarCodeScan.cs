using System.ComponentModel;
using SqlSugar;
//Chloe框架


namespace HZ.WMS.Entity.MM
{
    [SugarTable("MM_BarCodeScan")]
    public class MM_BarCodeScan : BaseEntity
    {
        /// <summary> 
        /// 条码ID
        /// </summary> 
        [SugarColumn(IsPrimaryKey = true)]
        [Description("条码ID")]
        public string BarID { get; set; }
        /// <summary> 
        /// 条码
        /// </summary> 
        [Description("条码")]
        public string BarCode { get; set; }
        /// <summary> 
        /// 批次
        /// </summary> 
        [Description("批次")]
        public string BatchNum { get; set; }

        /// <summary> 
        /// 物料编号
        /// </summary> 
        [Description("物料编号")]
        public string ItemCode { get; set; }
        /// <summary> 
        /// 物料名称
        /// </summary> 
        [Description("物料名称")]
        public string ItemName { get; set; }
        /// <summary> 
        /// 销售订单号
        /// </summary> 
        [Description("销售订单号")]
        public string BaseNum { get; set; }
        /// <summary> 
        /// 销售订单行号
        /// </summary> 
        [Description("销售订单行号")]
        public string BaseLine { get; set; }
        /// <summary> 
        /// 数量
        /// </summary> 
        [Description("数量")]
        public decimal? Qty { get; set; }
        /// <summary> 
        /// 库存单位
        /// </summary> 
        [Description("库存单位")]
        public string Unit { get; set; }
        /// <summary> 
        /// 仓库编号
        /// </summary> 
        [Description("仓库编号")]
        public string WhsCode { get; set; }

        /// <summary> 
        /// 仓库名称
        /// </summary> 
        [Description("仓库名称")]
        public string WhsName { get; set; }

        /// <summary> 
        /// 区域编号
        /// </summary> 
        [Description("区域编号")]
        public string RegionCode { get; set; }

        /// <summary> 
        /// 区域名称
        /// </summary> 
        [Description("区域名称")]
        public string RegionName { get; set; }

        /// <summary> 
        /// 库位编号
        /// </summary> 
        [Description("库位编号")]
        public string BinLocationCode { get; set; }

        /// <summary> 
        /// 库位名称
        /// </summary> 
        [Description("转入库位名称")]
        public string BinLocationName { get; set; }


    }
}


