using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;
using HZ.Core.Http;
using HZ.Core.Office;
using HZ.WMS.Application.Sys;
using HZ.WMS.Entity.Sys;
using HZ.WMS.WebAPI.Controllers;
namespace HZ.WMS.WebAPI.Areas.Sys.Controllers
{
    /// <summary>
    /// 角色管理
    /// </summary>
    public class Sys_RoleController : ApiBaseController
    {
        private Sys_RoleApp _app = new Sys_RoleApp();

        #region 查询分页信息

        /// <summary>
        /// 查询分页信息
        /// </summary>
        /// <param name="page">分页</param>
        /// <param name="keyword">参数</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri] Pagination page ,[FromUri] string keyword)
        {
            var result = new ResponseData();
            try
            {
                var itemData = _app.GetPageList(page, x => string.IsNullOrEmpty(keyword) || x.RoleDesc.Contains(keyword) || x.Remark.Contains(keyword)).ToList().OrderBy(x=>x.Remark).ThenBy(x=>x.CTime);
                result.Data = new ResponsePageData { total = page.Total, items = itemData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 条件查询信息

        /// <summary>
        /// 条件查询信息
        /// </summary>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetList([FromUri]string keyword)
        {
            var result = new ResponseData();
            try
            {
                var itemData = _app.GetList(x=>string.IsNullOrEmpty(keyword) || x.RoleDesc.Contains(keyword)).ToList();
                result.Data = itemData;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 添加
        /// <summary>
        /// 
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Add([FromBody]Sys_Role entity)
        {
            var result = new ResponseData();
            try
            {
                Sys_User currLoginUser = GetCurrentUser();
                entity.MUser = currLoginUser.LoginAccount;
                entity.CUser = currLoginUser.LoginAccount;
                entity.MTime = DateTime.Now;
                entity.CTime = DateTime.Now;

                result.Data = _app.Insert(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取详情
        /// <summary>
        /// 获取详情
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetEntity([FromUri]string ID)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.GetEntityByKey(ID);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Update([FromBody]Sys_Role entity)
        {
            var result = new ResponseData();
            try
            {
                entity.MUser = GetCurrentUser().LoginAccount;
                entity.MTime = DateTime.Now;
                result.Data = _app.Update(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除（包括批量）

        /// <summary>
        /// 删除
        /// </summary>
        /// <returns></returns>
        [HttpDelete]
        public IHttpActionResult Delete([FromBody]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                _app.Delete(ids, GetCurrentUser().LoginAccount, out error_message);
                if (!string.IsNullOrEmpty(error_message))
                {
                    result.Message = error_message;
                    result.Code = (int)WMSStatusCode.Failed;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取用户角色


        /// <summary>
        /// 获取用户角色
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetUserRoles([FromUri]string userid)
        {
            var result = new ResponseData();
            try
            {
                result.Data = new { RoleList = _app.GetList().ToList(), UserRoleList = _app.GetUserRoles(userid) };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion


        #region 导出

        /// <summary>
        /// 
        /// </summary>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] string keyword)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetList().Where(t => string.IsNullOrEmpty(keyword) || t.RoleDesc.Contains(keyword)).ToList();
                //var itemsData = _app.GetList().ToList();
                List<ExcelColumn<Sys_Role>> columns = ExcelService.FetchDefaultColumnList<Sys_Role>();
                string[] ignoreField = new string[] { "IsDelete", "DTime", "DUser" };

                List<ExcelColumn<Sys_Role>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<Sys_Role> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }

                //columns.ForEach((column) =>
                //{
                //    if (column.ColumnName == "OrganizationID")
                //    {
                //        column.Formattor = ExcelExportFormatter.OrganizationFormatter;
                //    }

                //    if (column.ColumnName == "IsEnable")
                //    {
                //        column.Formattor = ExcelExportFormatter.IsEnableFormatter;
                //    }

                //    if (column.ColumnName == "Gender")
                //    {
                //        column.Formattor = ExcelExportFormatter.GenderFormatter;
                //    }
                //});

                return ExportToExcelFile<Sys_Role>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }
        #endregion





    }
}

