//Chloe框架
using SqlSugar;
using System.Collections.Generic;

namespace HZ.WMS.Entity.MD
{
    /// <summary>
    /// 物料主数据
    /// </summary>
    [SugarTable("MD_Item")]
    public class MD_Item : BaseEntity
    {
        /// <summary> 
        /// 物料ID
        /// </summary> 
        [SugarColumn(IsPrimaryKey = true)]
        public string ItemID { get; set; }

        /// <summary>
        /// 物料编号
        /// </summary>
        public string ItemCode { get; set; }

        /// <summary>
        /// 物料名称
        /// </summary>
        public string ItemName { get; set; }

        /// <summary>
        /// 库存管理方式 1：启用批次 2：启用序列号 0：不启用
        /// </summary>
        public int? StockManWay { get; set; }

        /// <summary>
        /// 是否包装物料 0：否 1：是
        /// </summary>
        public bool? IsPackaging { get; set; }

        /// <summary>
        /// 是否确认 0：否 1：是
        /// </summary>
        public bool? IsConfirm { get; set; }

        /// <summary>
        /// 功率
        /// </summary>
        public string Power { get; set; }

    }
}


