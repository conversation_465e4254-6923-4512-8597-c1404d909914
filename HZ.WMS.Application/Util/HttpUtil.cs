using System;
using System.IO;
using System.Net;
using System.Text;
using Newtonsoft.Json;

namespace AOS.OMS.Application.Util
{
    public class HttpUtil
    {
        #region 调用WMS发运计划接口

        public static string HttpPostLogin(string url)
        {
            WebClient webClient = new WebClient();
            String jsonResult = webClient.DownloadString(url);
            dynamic jsonRst = JsonConvert.DeserializeObject(jsonResult);
            if (jsonRst != null)
            {
                return jsonRst.Data.Token;
            }

            return string.Empty;
        }

        public static string WmsHttpPost(string token, string url, string body, string postMethodType)
        {
            Encoding encoding = Encoding.UTF8;
            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(url);
            request.Headers.Add("x-token", token);
            request.Headers.Add("appid", "P0001");
            request.Method = postMethodType;
            request.Accept = "text/html, application/xhtml+xml, */*";
            request.ContentType = "application/json";
            byte[] buffer = encoding.GetBytes(body);
            request.ContentLength = buffer.Length;
            request.GetRequestStream().Write(buffer, 0, buffer.Length);
            HttpWebResponse response = (HttpWebResponse)request.GetResponse();
            using (StreamReader reader = new StreamReader(response.GetResponseStream(), Encoding.UTF8))
            {
                return reader.ReadToEnd();
            }
        }

        public static string WsmHttpPost(string url, string body, string postMethodType)
        {
            Encoding encoding = Encoding.UTF8;
            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(url);
            request.Headers.Add("appid", "P0001");
            request.Headers.Add("x-token", "");
            request.Method = postMethodType;
            request.Accept = "text/html, application/xhtml+xml, */*";
            request.ContentType = "application/json";
            byte[] buffer = encoding.GetBytes(body);
            request.ContentLength = buffer.Length;
            request.GetRequestStream().Write(buffer, 0, buffer.Length);
            HttpWebResponse response = (HttpWebResponse)request.GetResponse();
            using (StreamReader reader = new StreamReader(response.GetResponseStream(), Encoding.UTF8))
            {
                return reader.ReadToEnd();
            }
        }
        
        
        public static string HttpPost(string url, string body, string postMethodType)
        {
            Encoding encoding = Encoding.UTF8;
            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(url);
            request.Method = postMethodType;
            request.Accept = "text/html, application/xhtml+xml, */*";
            request.ContentType = "application/json";
            request.Timeout = 1000 * 60 * 30;
            byte[] buffer = encoding.GetBytes(body);
            request.ContentLength = buffer.Length;
            request.GetRequestStream().Write(buffer, 0, buffer.Length);
            HttpWebResponse response = (HttpWebResponse)request.GetResponse();
            using (StreamReader reader = new StreamReader(response.GetResponseStream(), Encoding.UTF8))
            {
                return reader.ReadToEnd();
            }
        }

        #endregion
    }
}