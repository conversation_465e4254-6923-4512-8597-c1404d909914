using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;
using AOS.WMS.Entity.CableProduce;
using HZ.Core.Http;
using HZ.WMS.Application.PP;
using HZ.WMS.WebAPI.Controllers;

namespace HZ.WMS.WebAPI.Areas.PP.Controllers
{
    /// <summary>
    /// 生产序列号分配
    /// </summary>
    public class ProductionOrderController : ApiBaseController
    {
        private Cable_ProductionOrderApp _app = new Cable_ProductionOrderApp();

        #region 获取计划订单列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri] Pagination page,
            [FromUri] Cable_ProductionOrderListReq productionOrderListReq)
        {
            var result = new ResponseData();
            try
            {
                page.Sort = "CTime desc";
                var itemsData = _app.GetPageList(page, productionOrderListReq);
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 创建计划订单

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult CreatePlanOrder([FromBody] Cable_ProductionOrderReq req)
        {
            var result = new ResponseData();
            try
            {
                return Json(_app.CreatePlanOrder(req.ids.ToList(), req.assembleDate, GetCurrentUser()));
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 计划转生产订单

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult PlanConvertProduceOrder([FromBody] string[] ids)
        {
            var result = new ResponseData();
            try
            {
                return Json(_app.PlanConvertProduceOrder(ids.ToList(), GetCurrentUser()));
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion
        
        #region 生产订单确认

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult ProduceOrderConfirm([FromBody] string[] ids)
        {
            var result = new ResponseData();
            try
            {
                return Json(_app.ProduceOrderConfirm(ids.ToList(), GetCurrentUser()));
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 序列号分配

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult DispenseSerialNo([FromBody] List<string> ids)
        {
            var result = new ResponseData();
            try
            {
                return Json(_app.DispenseSerialNo(ids));
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 取消序列号分配

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult UnDispenseSerialNo([FromBody] List<string> ids)
        {
            var result = new ResponseData();
            try
            {
                return Json(_app.UnDispenseSerialNo(ids, GetCurrentUser()));
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 生产报工

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult ProduceReportWork([FromBody] Cable_ProductionOrderReq req)
        {
            var result = new ResponseData();
            try
            {
                return Json(_app.ProduceReportWork(req.ids.ToList(), req.assembleDate, GetCurrentUser()));
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 取消生产报工

        /// <summary>
        /// 取消生产报工
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult UnProduceReportWork([FromBody] string[] ids)
        {
            var result = new ResponseData();
            try
            {
                return Json(_app.UnProduceReportWork(ids.ToList(), GetCurrentUser()));
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion
        
    }
}