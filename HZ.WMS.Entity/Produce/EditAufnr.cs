using System.ComponentModel;

namespace HZ.WMS.Entity.Produce
{
    public class EditAufnr
    {
        [Description("消息类型")]
        public string ETYPE { get; set; }

        [Description("消息文本")]
        public string EMSG { get; set; }

        [Description("DATA_IN")]
        public Table001080[] DataIn { get; set; }

        [Description("DATA_OUT")]
        public Table001080[] DataOut { get; set; }

        public class Table001080
        {
            [Description("工厂")]
            public string WERKS { get; set; }

            [Description("销售订单号")]
            public string VBELN { get; set; }

            [Description("销售订单行")]
            public string POSNR { get; set; }

            [Description("开始日期")]
            public string PSTTR { get; set; }

            [Description("生产版本")]
            public string VERID { get; set; }
            
            [Description("物料编码")]
            public string MATNR { get; set; }

            [Description("工单号")]
            public string AUFNR { get; set; }

            [Description("消息类型")]
            public string ETYPE { get; set; }

            [Description("消息文本")]
            public string EMSG { get; set; }
        }
    }
}