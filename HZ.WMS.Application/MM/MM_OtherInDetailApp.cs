using SqlSugar;
using HZ.WMS.Entity.MM;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Application.MM
{
    /// <summary>
    /// 其他入库详情
    /// </summary>
    public class MM_OtherInDetailApp : BaseApp<MM_OtherInDetail>
    {
        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public MM_OtherInDetailApp() : base(){}

        #endregion

        #region 更新

        /// <summary>
        /// 
        /// </summary>
        public int Update(List<MM_OtherInDetail> addlist, List<MM_OtherInDetail> deletelist, List<MM_OtherInDetail> updatelist)
        {
            int iCount = 0;
            try
            {
                DbContext.Ado.BeginTran();
                if (addlist != null && addlist.Count > 0) base.Insert(addlist);
                if (deletelist != null && deletelist.Count > 0) base.Delete(deletelist);
                if (updatelist != null && updatelist.Count > 0) base.Update(updatelist);
                iCount = 1;
                DbContext.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                DbContext.Ado.RollbackTran();
                iCount = 0;
                throw ex; 
            }
            return iCount;
        }

        #endregion

        
    }
}
