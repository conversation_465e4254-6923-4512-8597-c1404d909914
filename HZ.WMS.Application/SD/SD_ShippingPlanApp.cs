using SqlSugar;
using HZ.Core.Http;
using HZ.WMS.Application.Sys;
using HZ.WMS.Entity.SD;
using HZ.WMS.Entity.SAP;
using HZ.WMS.Entity.SAP.View;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using System.Data;
using HZ.WMS.Entity.SD.ViewModel;
using HZ.WMS.Application.MD;
using HZ.WMS.Entity.SD.Import;
using HZ.WMS.Entity;
using HZ.WMS.Entity.PP;
using HZ.WMS.Entity.MD.ViewModel;
using DbType = SqlSugar.DbType;

namespace HZ.WMS.Application.SD
{
    /// <summary>
    /// 发运计划
    /// 没有out_message消息的函数，意味着只在最前端做未处理异常捕捉
    /// </summary>
    public class SD_ShippingPlanApp : BaseApp<SD_ShippingPlan>
    {
        #region 初始化

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public SD_ShippingPlanApp() : base(){}

        SD_ShippingPlanDetailApp _detailApp = new SD_ShippingPlanDetailApp();
        BaseApp<BaseEntity> _baseApp = new BaseApp<BaseEntity>();


        #endregion

        #region 删除

        /// <summary>
        /// 删除校验
        /// 
        /// </summary>
        /// <param name="DocNums"></param>
        /// <param name="error_message"></param>
        /// <param name="strArray">数组</param>
        /// <returns></returns>
        public bool CheckDelete(List<SD_ShippingPlan> querys, out string error_message)
        {
            bool isPass = true;
            error_message = "";

            //foreach (SD_ShippingPlan query in querys)
            //{
            //    if (query.IsPosted == true)
            //    {
            //        error_message = "ui.Message.PODeletePostingWarning"; // "已过账数据不允许删除!";
            //        isPass = false;
            //        break;
            //    }

            //    // 入库校验
            //    //if(_stockApp.CheckStockLocked(po.ItemCode, po.OutBinLocationCode, po.OutRegionCode))
            //    //{
            //    //    //throw new Exception("Common.error", new Exception("Common.StockIsLocked"));
            //    //    error_message = "Common.StockIsLocked"; //库存锁定，不允许出入库
            //    //    return false;
            //    //}
            //}

            return isPass;
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="DocNums">单号</param>
        /// <param name="opUser">当前用户</param>
        /// <param name="error_message">错误消息</param>
        public bool Deletes(string[] DocNums, string opUser, out string error_message)
        {
            error_message = "";
            bool bDeleted = true;

            List<SD_ShippingPlan> querys = new List<SD_ShippingPlan>();
            List<SD_ShippingPlanDetail> querydetails = new List<SD_ShippingPlanDetail>();
            foreach (string key in DocNums)
            {
                List<SD_ShippingPlan> query = GetList(x => (string.IsNullOrEmpty(key) || x.DocNum.Contains(key))).ToList();
                if (query != null)
                {
                    querys.AddRange(query);
                }
                List<SD_ShippingPlanDetail> querydetail = _detailApp.GetList(x => (string.IsNullOrEmpty(key) || x.DocNum.Contains(key))).ToList();
                if (querydetail != null)
                {
                    querydetails.AddRange(querydetail);
                }
            }
            //if (CheckDelete(querys, out error_message)) //删除校验
            //{
            try
            {
                DbContext.Ado.BeginTran();
                _detailApp.DbContext = this.DbContext;
                //删除主表信息
                Delete(querys, opUser);

                //删除明细表信息
                _detailApp.Delete(querydetails);

                DbContext.Ado.CommitTran();//提交事务
            }
            catch (Exception ex)
            {
                bDeleted = false;
                error_message = ex.Message;
                DbContext.Ado.RollbackTran();//失败回滚
            }
            //}
            //else
            //{
            //    bDeleted = false;
            //}
            return bDeleted;
        }

        #endregion

        #region 完成

        /// <summary>
        /// 完成
        /// </summary>
        /// <param name="DocNums">单号</param>
        /// <param name="opUser">当前用户</param>
        /// <param name="error_message">错误消息</param>
        public bool Finish(string[] DocNums, string opUser, out string error_message)
        {
            error_message = "";
            //1.手动更新发运计划，2.删除未交货数据。3.托运单需要处理，调用存储过程
            string DocNum = string.Join(",", DocNums.ToArray());
            var pm1 = new SugarParameter("@DocNum", DocNum);
            var outPm = new SugarParameter("@cout", null, System.Data.DbType.UInt32,ParameterDirection.Output);
            DbContext.Ado.UseStoredProcedure().ExecuteCommand("PROC_UpdateConsignmentNoteByDocNum", pm1, outPm);
            //if (Convert.ToInt32(outPm.Value) > 0)
                return true;
           // else
               // return false;
        }

        #endregion

        #region 保存

        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="DocNum">单据号</param>
        /// <param name="Remark">备注</param>
        /// <param name="querydetails">添加明细</param>
        /// <param name="DeliveryDate">采购交货日期</param>
        /// <param name="user">用户</param>
        /// <param name="error_message">错误信息</param>
        /// <returns></returns>
        public bool Save(string DocNum ,string Remark, List<SD_ShippingPlanDetail> querydetails, DateTime DeliveryDate, string user, out string error_message)
        {
            error_message = "";
            SD_CustomerAddViewApp _CustomerAddApp = new SD_CustomerAddViewApp();
            MD_FreightMileageApp _FreightMileageApp = new MD_FreightMileageApp();
            try
            {
                //detailed.Where((x, i) => detailed.FindIndex(z => z.DocNum == x.DocNum) == i) 去除某一列重复值
                //detailed.Distinct() 去重

                SD_ShippingPlan query = new SD_ShippingPlan();
                query.DocNum = DocNum;
                query.DeliveryDate =Convert.ToDateTime(DeliveryDate.ToString("yyyy-MM-dd"));
                query.IsDelete = false;
                query.CUser = user;
                query.CTime = DateTime.Now;
                query.Remark = Remark;
                query.ShippingPlanStatus = 0;

                foreach (SD_ShippingPlanDetail x in querydetails)
                {
                    //根据客户地址查询结算地址等信息
                    var CustomerAdd = _CustomerAddApp.GetList(t => t.CustomerAdd == x.CustomerAdd)?.ToList().FirstOrDefault();
                    if (CustomerAdd == null)
                    {
                        x.SettlementAdd = "";
                        x.Contact = "";
                        x.Telephone = "";
                    }
                    else
                    {
                        x.SettlementAdd = CustomerAdd.SettlementAdd;
                        x.Contact = CustomerAdd.Contact;
                        x.Telephone = CustomerAdd.Telephone;
                    }
                    //根据结算地址查询物流供应商信息
                    //var Mileageinfo = _FreightMileageApp.GetList(t => t.SettlementAdd == x.SettlementAdd)?.ToList().FirstOrDefault();

                    MD_FreightMileage_View Mileageinfo = this.DbContext.Queryable<MD_FreightMileage_View>().Where(T => T.SettlementAdd == x.SettlementAdd)?.ToList().FirstOrDefault();

                    if (Mileageinfo == null)
                    {
                        x.SupplierCode = "";
                        x.SupplierName = "";
                    }
                    else
                    {
                        x.SupplierCode = Mileageinfo.SupplierCode;
                        x.SupplierName = Mileageinfo.SupplierName;
                        x.DeliveryUser = Mileageinfo.UserCode;
                        x.DeliveryUserName = Mileageinfo.UserName;
                    }
                    //获取出厂编号
                    //if (string.IsNullOrEmpty(x.OUTNO.Trim()))
                    //{
                    //    //查询出厂编号信息 要抓库存数据！
                    //    var query2 = DbContext.Queryable<PP_ProductionOrder>().Where(t => t.SalesOrderNo == x.SalesOrderNumber && t.SalesOrderLineNo == x.SalesLine && t.IsDelete != true).ToList().FirstOrDefault();
                    //    if (query2 != null)
                    //    {
                    //        x.OUTNO = query2.SerialNo;
                    //    }
                    //}

                   
                    x.DeliveryDate = Convert.ToDateTime(DeliveryDate.ToString("yyyy-MM-dd"));
                    x.DocNum = DocNum;
                    x.IsDelete = false;
                    x.CUser = user;
                    x.CTime = DateTime.Now;
                }

                DbContext.Ado.BeginTran();
                _detailApp.DbContext = this.DbContext;
                //交运计划信息插入
                this.Insert(query);

                //交运计划信息明细插入
                _detailApp.Insert(querydetails);

                DbContext.Ado.CommitTran();

                return true;
            }
            catch (Exception ex)
            {
                if (DbContext.Ado.IsAnyTran())
                    DbContext.Ado.RollbackTran();
                error_message = ex.Message;
                return false;
            }
        }

        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="DocNum">单据号</param>
        /// <param name="Remark">备注</param>
        /// <param name="deleteDetailArray">删除数组</param>
        /// <param name="querydetails">添加明细</param>
        /// <param name="user">用户</param>
        /// <param name="error_message">错误信息</param>
        /// <returns></returns>
        public bool Update(string DocNum,string Remark,string[] deleteDetailArray, List<SD_ShippingPlanDetail> querydetails, string user, out string error_message)
        {
            error_message = "";
            try
            {
                //根据主键ID查询委外申请单明细信息
                List<SD_ShippingPlanDetail> entities = _detailApp.GetListByKeys(deleteDetailArray);

                //根据申请单号查询委外申请单主表信息
                SD_ShippingPlan query = GetList(x => (string.IsNullOrEmpty(DocNum) || x.DocNum == DocNum)).ToList().FirstOrDefault();


                List<SD_ShippingPlanDetail> AddDetailList = new List<SD_ShippingPlanDetail>();
                List<SD_ShippingPlanDetail> UpdateDetailList = new List<SD_ShippingPlanDetail>();
                //插入子表信息 podetailed
                foreach (SD_ShippingPlanDetail querydetail in querydetails)
                {
                    if (string.IsNullOrEmpty(querydetail.ShippingPlanDetailID) && querydetail.ShippingPlanDetailID == null)
                    {
                        querydetail.DocNum = DocNum;
                        querydetail.IsDelete = false;
                        querydetail.CUser = user;
                        querydetail.CTime = DateTime.Now;
                        AddDetailList.Add(querydetail);
                    }
                    else
                    {
                        querydetail.MUser = user;
                        querydetail.MTime = DateTime.Now;
                        UpdateDetailList.Add(querydetail);
                    }
                    //listmsdetail.Add(podetailed);
                }

                DbContext.Ado.BeginTran();
                _detailApp.DbContext = this.DbContext;
                //删除委外申请单明细信息
                if (entities.Count>0)
                {
                    _detailApp.Delete(entities, user);
                }
                //更新委外申请单信息
                if (query !=null)
                {
                    query.Remark = Remark;
                    base.Update(query);
                }
                //插入委外申请单明细信息
                //this.Update(listms);
                if (AddDetailList.Count > 0)
                {
                    _detailApp.Insert(AddDetailList);
                }
                //更新委外申请单明细信息
                if (UpdateDetailList.Count > 0)
                {
                    _detailApp.Update(UpdateDetailList);
                }

                DbContext.Ado.CommitTran();

                return true;
            }
            catch (Exception ex)
            {
                if (DbContext.Ado.IsAnyTran())
                    DbContext.Ado.RollbackTran();
                error_message = ex.Message;
                return false;
            }
        }

        #endregion

        #region 同步SRM

        /// <summary>
        /// 同步SRM
        /// </summary>
        /// <param name="entities">集合</param>
        /// <param name="user">用户</param>
        /// <param name="error_message">错误信息</param>
        /// <returns></returns>
        public bool UploadSRM(List<SD_ShippingPlan> entities, string user, out string error_message)
        {
            error_message = "";
            //校验发运计划的状态是否为已同步托运单
            if (entities.Any(x => x.ShippingPlanStatus == 1))
            {
                error_message = "已同步的信息不允许重复同步";
                return false;
            }

            entities.ForEach(x => {
                x.ShippingPlanStatus = 1;
                x.MTime = DateTime.Now;
                x.MUser = user;
            });
       
            try
            {
                //DbContext.Ado.BeginTran();
                //_consignmentApp.DbContext = this.DbContext;

                //更新
                //Update(entities);

                //DbContext.Ado.CommitTran();
                 SD_ConsignmentNoteApp _consignmentApp = new SD_ConsignmentNoteApp();
                foreach (var x in entities)
                {
                    bool noteDetail = _consignmentApp.AutoSave(x.DocNum,x, user, out error_message);
                    if (!noteDetail || !string.IsNullOrEmpty(error_message))
                    {
                        return false;
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                error_message = ex.Message;
                return false;
            }
        }

        #endregion

        #region 查询发运计划

        /// <summary>
        /// 查询发运计划
        /// </summary>
        /// <param name="InspectionNum">报检单号</param>
        /// <param name="error_message">错误消息</param>
        public List<SD_DeliveryScan_View> GetShippingPlanDetail(string DocNum, out string error_message)
        {
            error_message = "";
            var query = DbContext.Queryable<SD_DeliveryScan_View>().Where(x => x.DocNum == DocNum).ToList();
            if (query == null || query.Count <= 0)
            {
                error_message = "发运单号[" + DocNum + "]已发料完成！";
            }
            return query;
        }

        #endregion

        #region 导入

        /// <summary>
        /// 导入
        /// </summary>
        /// <param name="excelList"></param>
        /// <param name="opUser"></param>
        /// <returns></returns>
        public bool ImprotExcelToBaseData(List<SD_ShippingPlanImport> excelList, string opUser, out string error_message)
        {
            error_message = "";
            var flag = true;
            DateTime date = DateTime.Now;
            SD_CustomerAddViewApp _CustomerAddApp = new SD_CustomerAddViewApp();
            MD_FreightMileageApp _FreightMileageApp = new MD_FreightMileageApp();
            BaseApp<BaseEntity> _baseApp = new BaseApp<BaseEntity>();
            String DocNum= _baseApp.GetNewDocNum(DocType.SD, DocFixedNumDef.SD_ShippingPlan);
            var query = new SD_ShippingPlan();
            var querydetail = new List<SD_ShippingPlanDetail>();
            //暂时只进行插入操作，有需要在做更新操作
            foreach (var x in excelList)
            {
                var y = DbContext.Queryable<SAP_VBAK_View>().Where(t => t.VBELN == x.销售单号 && t.MATNR == x.物料编号).ToList().FirstOrDefault();
                if (y == null)
                {
                    error_message = "销售单号[" + x.销售单号 + "]物料号[" + x.物料编号 + "]为查询到销售订单信息，不允许导入";
                    return false;
                }

                //主表信息
                query.DocNum = DocNum;
                query.IsDelete = false;
                query.CUser = opUser;
                query.CTime = date;
                query.ShippingPlanStatus = 0;

                //明细信息
                var z = new SD_ShippingPlanDetail();
                z.DocNum = DocNum;
                z.SalesOrderNumber = x.销售单号;
                z.SalesLine = x.销售单行号;
                z.SalesType = y.AUART;
                z.SalesOrganization = y.VKORG;
                z.VoucherDate = y.AUDAT;
                z.DistributionChannels = y.VTWEG;
                z.ProductGroup = y.SPART;
                z.CustomerCode = x.客户编号;
                z.CustomerName = x.客户名称;
                z.CustomerAdd = x.客户地址;
                z.BatchNum = y.ZDELBA.ToString();
                z.ItemCode = x.物料编号;
                z.ItemName = x.物料名称;
                z.ShippingPlanDetailQty = x.数量;
                z.Unit = y.MEINS;
                z.WhsCode = x.仓库编号;
                z.WhsName = x.仓库名称;
                z.IsDelete = false;
                z.DeliveryDate = x.承诺的交货日期; ;
                //z.DeliveryDate = DateTime.FromOADate(double.Parse(x.承诺的交货日期.ToString())); ;
                z.CONT = x.合同单号;
                z.OUTNO = x.生产主机编号;
                z.PRCTR = y.PRCTR;
                z.VSTEL = y.VSTEL;
                z.VGBEL = y.VGBEL;
                z.VGPOS = y.VGPOS;
                //根据客户地址查询结算地址
                z.SettlementAdd = _CustomerAddApp.GetList(t => t.CustomerAdd == x.客户地址)?.ToList().FirstOrDefault().SettlementAdd ?? "";

                //根据结算地址查询物流供应商信息
                //var Mileageinfo = _FreightMileageApp.GetList(t => t.SettlementAdd == z.SettlementAdd)?.ToList().FirstOrDefault();

                MD_FreightMileage_View Mileageinfo = this.DbContext.Queryable<MD_FreightMileage_View>().Where(T => T.SettlementAdd == z.SettlementAdd)?.ToList().FirstOrDefault();


                if (Mileageinfo == null)
                {
                    z.SupplierCode = "";
                    z.SupplierName = "";
                }
                else
                {
                    z.SupplierCode = Mileageinfo.SupplierCode;
                    z.SupplierName = Mileageinfo.SupplierName;
                }
                z.CUser = opUser;
                z.CTime = date;
                querydetail.Add(z);
            };
            Insert(query);
            flag = _detailApp.Insert(querydetail) > 0;
            return flag;
        }

        #endregion

        #region 查询导出数据

        /// <summary>
        /// 查询导出数据
        /// </summary>
        /// <param name="keyword">参数</param>
        /// <param name="fromTime">开始时间</param>
        /// <param name="toTime">结束时间</param>
        public List<SD_ShippingPlanDetail> GetExportInfo(string keyword, int? PlanStatus, DateTime fromTime, DateTime toTime, 
            string Customer, string CustomerOrderNum, string CONT,string PSStatus)
        {

            List<SD_ShippingPlanDetail> itemsData = new List<SD_ShippingPlanDetail>();

            List<SD_ShippingPlan> Planitems = new List<SD_ShippingPlan>();

            if (string.IsNullOrEmpty(Customer) && string.IsNullOrEmpty(CustomerOrderNum) && string.IsNullOrEmpty(CONT))
            {
                Planitems = GetList(t => (string.IsNullOrEmpty(keyword)
                   || t.DocNum.Contains(keyword) || t.SalesOrderType.Contains(keyword)
                   || t.CUser.Contains(keyword)
                   ) && (PlanStatus == null || t.ShippingPlanStatus == PlanStatus)
                    && (string.IsNullOrEmpty(PSStatus) || t.PSStatus == PSStatus)
                   && (t.DeliveryDate >= fromTime && t.DeliveryDate <= toTime)
                   ).ToList().OrderBy(t => t.ShippingPlanStatus).ToList();

                string[] DocNums = Planitems.Select(x => x.DocNum).ToArray();

                 //itemsData = _detailApp.GetList().Where(t => DocNums.Contains(t.DocNum)).ToList();//220502-cc注释
                //220502-cc添加
                itemsData = _detailApp.GetList().Where(t => DocNums.Contains(t.DocNum)).OrderBy(o=>o.DocNum).OrderBy(o=>o.CONT).ToList();

                foreach (SD_ShippingPlan info in Planitems)
                {
                    info.PSStatus = "已下载";
                }
                Update(Planitems);
            }
            else
            {
                var items = _detailApp.GetList(t => (string.IsNullOrEmpty(keyword) || t.DocNum.Contains(keyword) || t.CUser.Contains(keyword) || t.SalesOrderType.Contains(keyword))
                    && (string.IsNullOrEmpty(Customer) || t.CustomerCode.Contains(Customer) || t.CustomerName.Contains(Customer))
                    && (string.IsNullOrEmpty(CustomerOrderNum) || t.CustomerOrderNum.Contains(CustomerOrderNum) || t.SalesOrderNumber.Contains(CustomerOrderNum))
                    && (string.IsNullOrEmpty(CONT) || t.CONT.Contains(CONT))
                    && (t.DeliveryDate >= fromTime && t.DeliveryDate <= toTime)
                    ).ToList();
                
                // 不处理下载标记
                // var DocNums = items.Select(x => x.DocNum).Distinct().ToArray();
                //
                // Planitems = GetList(t => DocNums.Contains(t.DocNum) && (PlanStatus == null || t.ShippingPlanStatus == PlanStatus)
                // && (string.IsNullOrEmpty(PSStatus) || t.PSStatus == PSStatus) 
                // && (t.DeliveryDate >= fromTime && t.DeliveryDate <= toTime)
                //  ).ToList();
                //
                // string[] DocNums1 = Planitems.Select(x => x.DocNum).ToArray();

                // itemsData = _detailApp.GetList().Where(t => DocNums.Contains(t.DocNum)).OrderBy(o => o.DocNum).ThenBy(o => o.CONT).ToList();

                itemsData = items;
            }
            

            //List<SD_ShippingPlan> items = GetList().Where(t => string.IsNullOrEmpty(keyword)
            //          || t.DocNum.Contains(keyword)
            //          || t.CUser.Contains(keyword)
            //          ).Where(x => x.DeliveryDate >= fromTime && x.DeliveryDate <= toTime && (PlanStatus == null || x.ShippingPlanStatus == PlanStatus) && !x.IsDelete).ToList();

            //string[] DocNums = items.Select(x => x.DocNum).ToArray();

            //var itemsData = _detailApp.GetList().Where(t => DocNums.Contains(t.DocNum)).ToList();

            //itemsData.ForEach(x=>
            //{
            //     if (string.IsNullOrEmpty(x.OUTNO))
            //     {
            //         var query = DbContext.Queryable<PP_ProductionOrder>().Where(t => t.SalesOrderNo == x.SalesOrderNumber && t.SalesOrderLineNo == x.SalesLine && t.IsDelete != true).ToList().FirstOrDefault();
            //         if (query != null)
            //         {
            //             x.OUTNO = query.SerialNo;
            //         }
            //     }
            // });

            return itemsData;
        }

        #endregion

        #region 查询未发货的发运计划

        /// <summary>
        /// 查询未发货的发运计划
        /// </summary>
        /// <param name="DeliveryUser">用户</param>
        public List<SD_NoDeliveryShippingPlan_View> GetNoDeliveryShippingPlan(string DeliveryUser)
        {
            var query = DbContext.Queryable<SD_NoDeliveryShippingPlan_View>().Where(x => (string.IsNullOrEmpty(DeliveryUser) || x.DeliveryUser == DeliveryUser)).ToList();

            return query;
        }

        #endregion

        #region OMS同步WMS发运计划保存

        /// <summary>
        /// OMS同步WMS发运计划保存
        /// </summary>
        /// <param name="ShiPingOMS"></param>
        /// <param name="user">用户</param>
        /// <param name="error_message">错误信息</param>
        /// <returns></returns>
        public bool SaveForOMS(List<SD_ShippingForOMS> ShiPingOMS, string user, out string error_message)
        {
            error_message = "";
            SD_CustomerAddViewApp _CustomerAddApp = new SD_CustomerAddViewApp();
            MD_FreightMileageApp _FreightMileageApp = new MD_FreightMileageApp();
            MD_StockApp _stockApp = new MD_StockApp();
            try
            {

                foreach (SD_ShippingForOMS x in ShiPingOMS)
                {
                    if (_detailApp.GetList(t => t.SalesOrderNumber == x.SalesOrderNumber && t.SalesLine == x.SalesLine).ToList().FirstOrDefault() != null)
                    {
                        error_message = "销售单号[" + x.SalesOrderNumber + "],销售行号[" + x.SalesLine + "]已存在发运计划信息，请勿重复生成";
                        return false;
                    }
                }

                //detailed.Where((x, i) => detailed.FindIndex(z => z.DocNum == x.DocNum) == i) 去除某一列重复值
                //detailed.Distinct() 去重

                string DocNum = "";
                if (ShiPingOMS[0].SalesOrderType.Contains("售后"))//售后
                {
                    DocNum = _baseApp.GetNewDocNum(DocType.SD, DocFixedNumDef.SD_ShippingPlan);
                }
                else
                {
                    DocNum = _baseApp.GetNewDocNum(DocType.SD, DocFixedNumDef.SD_ShippingPlanXS);
                }
                DateTime date = DateTime.Now;
                var query = new SD_ShippingPlan
                {
                    DocNum = DocNum,
                    SalesOrderType= ShiPingOMS[0].SalesOrderType,
                    PSStatus="未下载",
                    DeliveryDate = Convert.ToDateTime(Convert.ToDateTime(ShiPingOMS[0].DeliveryDate).ToString("yyyy-MM-dd")),
                    IsDelete = false,
                    CUser = user,
                    CTime = date,
                    Remark = "",
                    ShippingPlanStatus = 0,
                };

                var querydetails = new List<SD_ShippingPlanDetail>();
                foreach (SD_ShippingForOMS x in ShiPingOMS)
                {
                    var y = new SD_ShippingPlanDetail();
                    y.DeliveryDate = x.PromisedDeliveryDate;
                    y.DocNum = DocNum;
                    y.IsDelete = false;
                    y.CUser = user;
                    y.CTime = date;
                    y.SalesOrderNumber = x.SalesOrderNumber;
                    y.SalesLine = x.SalesLine;
                    y.SalesType = x.SalesType;
                    y.SalesOrganization = x.SalesOrganization;
                    y.VoucherDate = x.VoucherDate;
                    y.CustomerCode = x.CustomerCode;
                    y.CustomerName = x.CustomerName;
                    y.CustomerAdd = x.CustomerAdd;
                    y.BatchNum = x.BatchNum;
                    y.ItemCode = x.ItemCode;
                    y.ItemName = x.ItemName;
                    y.ShippingPlanDetailQty = x.ShippingPlanDetailQty;
                    y.CONT = x.CONT;
                    y.SettlementAdd = x.SettlementAdd;
                    y.CustomerOrderNum = x.CustomerOrderNum;
                    y.SalesOrderType = x.SalesOrderType;
                    y.ActualTime = null;
                    y.PlanTime = null;
                    y.Project = x.Project;
                    y.Remark = x.Remark; //明细备注-220412-cc添加
                    y.ProductionRemark = x.ProductionRemark; //生产备注-220412-cc添加
                    y.ShippingType = x.ShippingType; //发货方式备注-220412-cc添加
                    y.CustomerItemCode = x.CustomerItemCode; //客户件号-211123-cc添加
                    //根据客户地址查询结算地址等信息
                    var CustomerAdd = _CustomerAddApp.GetList(t => t.CustomerAdd == y.CustomerAdd)?.ToList().FirstOrDefault();
                    if (CustomerAdd != null)
                    {
                        var customerAdd = _CustomerAddApp.GetList(t => t.CustomerAdd == y.CustomerAdd)?.ToList().FirstOrDefault();
                        if (customerAdd != null)
                        {
                            //y.SettlementAdd = CustomerAdd.SettlementAdd;
                            y.Contact = customerAdd.Contact;
                            y.Telephone = customerAdd.Telephone;
                        }
                        else
                        {
                            //y.SettlementAdd = "";
                            y.Contact = "";
                            y.Telephone = "";
                        }
                    }
                    else
                    {
                        y.Contact = x.Contact;
                        y.Telephone = x.Telephone;
                    }
                    //根据结算地址查询物流供应商信息
                    //var Mileageinfo = _FreightMileageApp.GetList(t => t.SettlementAdd == y.SettlementAdd)?.ToList().FirstOrDefault();

                    MD_FreightMileage_View Mileageinfo = this.DbContext.Queryable<MD_FreightMileage_View>().Where(T => T.SettlementAdd == y.SettlementAdd)?.ToList().FirstOrDefault();

                    if (Mileageinfo != null)
                    {
                        y.SupplierCode = Mileageinfo.SupplierCode;
                        y.SupplierName = Mileageinfo.SupplierName;
                        y.DeliveryUser = Mileageinfo.UserCode;
                        y.DeliveryUserName = Mileageinfo.UserName;
                    }

                    //获取出厂编号
                    if (string.IsNullOrEmpty(string.IsNullOrEmpty(y.OUTNO) ? "" : y.OUTNO.Trim()))
                    {
                        //查询出厂编号信息 要抓库存数据！
                        var barcode=_stockApp.GetList(t => t.SaleNum == y.SalesOrderNumber && t.SaleLine == y.SalesLine&& t.ItemCode==y.ItemCode).ToList().FirstOrDefault();
                        y.OUTNO = barcode !=null ? barcode.BarCode : "";
                    }
                    var z = DbContext.Queryable<SAP_VBAK_View>().Where(t => t.VBELN == y.SalesOrderNumber && t.POSNR == y.SalesLine).ToList().FirstOrDefault();
                    if (z != null)
                    {
                        y.CreationDate = z.ERDAT;
                        y.DistributionChannels = z.VTWEG;
                        y.ProductGroup = z.SPART;
                        y.BSTNK = z.BSTNK;
                        y.ProjectCategory = z.PSTYV;
                        y.Unit = z.MEINS;
                        y.WhsCode = z.LGORT;
                        var whsname = DbContextForSAP.Queryable<XZ_SAP_T001L>().Where(t => t.LGORT == y.WhsCode).ToList().FirstOrDefault();
                        y.WhsName = whsname != null ? whsname.LGOBE :"";
                        y.PRCTR = z.PRCTR;
                        y.VSTEL = z.VSTEL;
                        y.VGBEL = z.VGBEL;
                        y.VGPOS = z.VGPOS;
                    }
                    querydetails.Add(y);
                }

                DbContext.Ado.BeginTran();
                _detailApp.DbContext = this.DbContext;
                //交运计划信息插入
                this.Insert(query);

                //交运计划信息明细插入
                _detailApp.Insert(querydetails);

                DbContext.Ado.CommitTran();


                //更新发运计划部分信息
                base.SqlQuery("PROC_UpdateShippingPlanInfo", CommandType.StoredProcedure);

                return true;
            }
            catch (Exception ex)
            {
                if (DbContext.Ado.IsAnyTran())
                    DbContext.Ado.RollbackTran();
                error_message = ex.Message;
                return false;
            }
        }

        #endregion

    }
}
