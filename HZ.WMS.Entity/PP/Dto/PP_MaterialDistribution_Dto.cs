using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.PP.Dto
{
    /// <summary>
    /// 生产物料配送
    /// </summary>
    public class PP_MaterialDistribution_Dto
    {
        /// <summary>
        /// 手动过账时间
        /// </summary>
        public DateTime? ManualPostTime { get; set; }
        /// <summary>
        /// 主表配送单号
        /// </summary>
        public string DeliveryOrderNo { get; set; }
        /// <summary>
        /// 删除id
        /// </summary>
        public string[] DelDetailIds { get; set; }
        /// <summary>
        /// 生产物料配送明细
        /// </summary>
        public List<PP_MaterialDistributionDetail> details { get; set; }
    }

    /// <summary>
    /// 导出汇总表
    /// </summary>
    public class ExportSummary
    {
        /// <summary>
        /// 组件编码
        /// </summary>
        [Description("组件编码")]
        public string ComponentCode { get; set; }
        /// <summary>
        /// 物料描述
        /// </summary>
        [Description("物料描述")]
        public string MaterialName { get; set; }
        /// <summary>
        /// 线体编码
        /// </summary>
        [Description("线体编码")]
        public string ProductionLineCode { get; set; }
        /// <summary>
        /// 线体描述
        /// </summary>
        [Description("线体描述")]
        public string ProductionLineDes { get; set; }
        /// <summary>
        /// 员工号
        /// </summary>
        [Description("员工号")]
        public string EmployeeNumber { get; set; }
        /// <summary>
        /// 员工姓名
        /// </summary>
        [Description("员工姓名")]
        public string EmployeeName { get; set; }
        /// <summary>
        /// 生产批次
        /// </summary>
        [Description("生产批次")]
        public string ProductionBatch { get; set; }
        /// <summary>
        /// 需求数量
        /// </summary>
        [Description("需求数量")]
        public decimal DemandQty { get; set; }
        /// <summary>
        /// 单位
        /// </summary>
        [Description("单位")]
        public string ComponentUnit { get; set; }
        /// <summary>
        /// 转出仓库
        /// </summary>
        [Description("转出仓库")]
        public string OutWarehouse { get; set; }
        /// <summary>
        /// 发料库存地
        /// </summary>
        [Description("转入仓库")]
        public string DeliverLocation { get; set; }
        /// <summary>
        /// 物料组
        /// </summary>
        [Description("物料组")]
        public string MaterialGroupCode { get; set; }
        /// <summary>
        /// 物料组描述
        /// </summary>
        [Description("物料组描述")]
        public string MaterialGroupDes { get; set; }
    }
}
