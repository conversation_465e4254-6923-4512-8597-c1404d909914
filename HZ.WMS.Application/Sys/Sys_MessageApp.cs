using SqlSugar;
using System;
using System.Collections.Generic;
using HZ.Core.Security;
using HZ.WMS.Entity.Sys;
using HZ.Core.Http;
using HZ.Core;
using System.Linq;
using System.Threading.Tasks;
using HZ.WMS.Entity.PP;
using HZ.WMS.Application.PP;
using HZ.Core.Office;
using System.IO;
using HZ.WMS.Entity.PP.ViewModel;

namespace HZ.WMS.Application.Sys
{
    /// <summary>
    /// 没有out_message消息的函数，意味着只在最前端做未处理异常捕捉
    /// </summary>

    public class Sys_MessageApp : BaseApp<Sys_Message>
    {
        private Sys_MessageTypeApp _messageTypeApp = new Sys_MessageTypeApp();
        private Sys_MessageNotifySettingApp _notifyApp = new Sys_MessageNotifySettingApp();
        private Sys_UserMessageApp _userMessageApp = new Sys_UserMessageApp();
        private Sys_MailServerConfigApp _mailConfigApp = new Sys_MailServerConfigApp();
        private Sys_UserApp _userApp = new Sys_UserApp();
        private Sys_MailApp mailApp = new Sys_MailApp();
        private PP_ProductionOrderApp _app = new PP_ProductionOrderApp();

        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public Sys_MessageApp() : base()
        {
        }

        #endregion

        #region 添加通知消息

        /// <summary>
        /// 添加消息：根据配置自动到用户消息表
        /// </summary>
        /// <param name="entity">消息体</param>
        /// <param name="bAutoSendEMail">是否同时自动发送邮件 默认:false 不自动发送</param>
        /// <returns></returns>
        public bool AddNotifyMessage(Sys_Message entity,bool bAutoSendEMail=false)
        {
            try
            {
                Sys_MessageType messageType = _messageTypeApp.GetEntityByKey(entity.MessageTypeID);
                // 获取当前消息分类下需要通知的用户清单
                List<Sys_User> notifyUserList = base.DbContext.Queryable<Sys_MessageNotifySetting>()
                    .Where(t => !t.IsDelete && t.MessageTypeID == entity.MessageTypeID)
                    .InnerJoin<Sys_User>((setting, user) => setting.UserID == user.UserID && !user.IsDelete)
                    .Select((setting, user) => user)
                    .ToList();

                List<Sys_UserMessage> userMessageList = new List<Sys_UserMessage>();
                notifyUserList.ForEach((item) =>
                {
                    userMessageList.Add(new Sys_UserMessage
                    {
                        MessageID = entity.MessageID,
                        MessageTitle = entity.MessageTitle,
                        MessageContent = entity.MessageBody,
                        UserID = item.UserID,
                        UserName = item.UserName,
                        MessageTypeID = messageType.MessageTypeID,
                        MessageTypeDesc = messageType.MessageTypeDesc,
                        NotifyType = messageType.IsNotifyByEmail==true ? "站内信 | 邮件" : "站内信",
                        Publisher = "",
                        PublishTime = DateTime.Now,
                        IsReaded = false,
                        ReadTime = null
                    });
                });
                base.DbContext.Ado.BeginTran();
                base.Insert(entity);
                _userMessageApp.DbContext = this.DbContext;
                _userMessageApp.Insert(userMessageList);
                base.DbContext.Ado.CommitTran();

                // 发送邮件采用异步方式，不放入事务，因为本身无法保证发送成功
                Sys_MailServerConfig mailConfig = _mailConfigApp.GetMailServerConfig();
                if (messageType.IsNotifyByEmail==true && mailConfig != null && bAutoSendEMail)
                {
                    HZ.Core.Email myMail = new Email(mailConfig.MailServerHost,(int)mailConfig.MailServerPort,mailConfig.MailServerAccount,mailConfig.MailServerPassword);
                    myMail.SenderDisplayName = mailConfig.SenderDisplayName??mailConfig.MailServerAccount;
                    // 获取需要发送邮件的用户列表
                    List<string> toMailList = notifyUserList.Select(t => t.Email).ToList();
                    myMail.ActionSendCompletedCallback += new System.Net.Mail.SendCompletedEventHandler(MailSendComputeCallBack);       // 注册回调事件
                    myMail.SendEmailAsync(toMailList, null, entity.MessageTitle, entity.MessageBody, userMessageList);      // userToken =>e.UserState userMessageList
                }

                return true;
            }
            catch(Exception ex)
            {
                if (DbContext.Ado.IsAnyTran())
                {
                    base.DbContext.Ado.RollbackTran();
                }
                return false;
            }
        }

        #endregion

        #region 邮件发送成功回调事件

        /// <summary>
        /// 不能同时多次执行
        /// </summary>
        /// <param name="sender">用户消息</param>
        /// <param name="e"></param>
        private void MailSendComputeCallBack(object sender, System.ComponentModel.AsyncCompletedEventArgs e)
        {
            try
            {
                //Task.Run((Action)(() => {
                //}));

                // 回调参数
                if (e.UserState != null)
                {
                    if(e.UserState is List<Sys_UserMessage>)
                    {
                        List<Sys_UserMessage> messageList = e.UserState as List<Sys_UserMessage>;
                        if (messageList != null && messageList.Count > 0)
                        {
                            string messageTypeId = messageList[0].MessageTypeID;
                            List<Sys_Mail> mailList = new List<Sys_Mail>();
                            Sys_MailServerConfig mailConfig = _mailConfigApp.GetMailServerConfig();
                            List<Sys_User> listUser = base.DbContext.Queryable<Sys_MessageNotifySetting>()
                                    .Where(t => !t.IsDelete && t.MessageTypeID == messageTypeId)
                                    .InnerJoin<Sys_User>((setting, user) => setting.UserID == user.UserID && !user.IsDelete)
                                    .Select((setting, user) => user)
                                    .ToList();

                            messageList.ForEach((item) =>
                            {
                                Sys_User user = _userApp.GetEntityByKey(item.UserID);
                                mailList.Add(new Sys_Mail
                                {
                                    MessageID = item.MessageID,
                                    ReceiverMail = user.Email,
                                    UserID = item.UserID,
                                    UserName = item.UserName,
                                    MessageTypeID = item.MessageTypeID,
                                    MessageTypeDesc = item.MessageTypeDesc,
                                    MailSubject = item.MessageTitle,
                                    MailBody = item.MessageContent,
                                    SenderMail = mailConfig.MailServerAccount,
                                    SendTime = DateTime.Now,
                                });
                            });

                            new Sys_MailApp().Insert(mailList);
                        }
                    }

                    if(e.UserState is List<Sys_Mail>)
                    {
                        List<Sys_Mail> mailList = e.UserState as List<Sys_Mail>;
                        if (mailList != null && mailList.Count > 0)
                        {
                            new Sys_MailApp().InsertWithTran(mailList);
                        }
                    }
                }
            }
            catch(Exception ex)
            {
                HZ.Core.Logging.LogHelper.Instance.LogDebug(ex.Message);
            }
        }

        #endregion

        #region 自动发送消息邮件

        /// <summary>
        /// 自动发送消息邮件:
        ///    一天之内，需要发送给特定用户通知的消息，一天之内发送失败，则重复发送，
        /// </summary>
        public void AutoSendMessageByEMail()
        {
            try
            {
                //获取需要发送邮件的消息
                List<Sys_UserMessage> _needSendEmailMessageList = CreateUserMessageBySetting();

                if (_needSendEmailMessageList == null || _needSendEmailMessageList.Count <= 0) return;
                Sys_MailServerConfig mailConfig = _mailConfigApp.GetMailServerConfig();
                
                string[] toMailList = _needSendEmailMessageList.Select(x => x.UserID).Distinct().ToArray();
                List<Sys_User> notifyUserList = base.DbContext.Queryable<Sys_User>().Where(x => toMailList.Contains(x.UserID)).ToList();
                List<Sys_Mail> sendedUserMailList = new List<Sys_Mail>();
                
                if (mailConfig != null && _needSendEmailMessageList != null && _needSendEmailMessageList.Count() > 0)
                {
                    _needSendEmailMessageList.ForEach((u) =>
                    {
                        Sys_Mail mail = new Sys_Mail();
                        Sys_User user = notifyUserList.Where(x => x.UserID == u.UserID).ToList().FirstOrDefault();
                        mail.UserID = u.UserID;
                        mail.MessageID = u.MessageID;
                        mail.MessageTypeID = u.MessageTypeID;
                        mail.MessageTypeDesc = u.MessageTypeDesc;
                        mail.MailSubject = u.MessageTitle;
                        mail.MailBody = u.MessageContent;
                        mail.IsDelete = false;
                        mail.UserName = user?.UserName;
                        mail.ReceiverMail = user?.Email;
                        mail.SenderMail = mailConfig.MailServerAccount;
                        mail.SendTime = DateTime.Now;
                        mail.CUser = "WebJob";
                        mail.MUser = "WebJob";

                        sendedUserMailList.Add(mail);
                    });
                    new Sys_MailApp().Insert(sendedUserMailList);

                    //myMail.ActionSendCompletedCallback += new System.Net.Mail.SendCompletedEventHandler(MailSendComputeCallBack);       // 注册回调事件
                    var messageList = _needSendEmailMessageList.GroupBy(x => x.MessageID);
                    foreach (var item in messageList)
                    {
                        HZ.Core.Email myMail = new Email(mailConfig.MailServerHost, (int)mailConfig.MailServerPort, mailConfig.MailServerAccount, mailConfig.MailServerPassword);
                        myMail.SenderDisplayName = mailConfig.SenderDisplayName ?? mailConfig.MailServerAccount;

                        // 获取需要发送邮件的用户列表
                        string[] userList = item.Select(x => x.UserID).ToArray();
                        List<Sys_User> mailUserList = notifyUserList.Where(x => userList.Contains(x.UserID)).ToList();

                        myMail.SendEmailAsync(mailUserList.Select(x => x.Email).ToList(), null, item.ToList().FirstOrDefault().MessageTitle, item.ToList().FirstOrDefault().MessageContent, null);      // userToken =>e.UserState userMessageList
                    }
                }
            }
            catch(Exception ex)
            {
                HZ.Core.Log.LogUtil.WriteLog("AutoSendMessageByEMail:Message" + ex.Message);
                HZ.Core.Log.LogUtil.WriteLog("AutoSendMessageByEMail:StackTrace" + ex.StackTrace.ToString());
            }
        }

        #endregion

        #region 生成特定用户通知消息

        /// <summary>
        /// 供应商提醒，针对供应商特殊设置
        /// </summary>
        /// <returns></returns>
        public List<Sys_UserMessage> CreateUserMessageBySetting()
        {
            //string[] supplierMessageTypeID = new string[] { "7EFAF0A8-25EB-4C7F-A254-9CEFD5AF832E", "7KFAF0A8-25EB-4C7F-A254-9CEFD5AF832E" };
            string[] supplierMessageTypeID =  HZ.Core.Configuration.AppConfigHelper.ReadSetting("SupplierNotifyMessageTypeIDList")?.Split(',');

            List<Sys_UserMessage> innerUserMesssageList = new List<Sys_UserMessage>();
            List<Sys_UserMessage> supplierUserMesssageList = new List<Sys_UserMessage>();
            //1、内部用户消息
            innerUserMesssageList = this.DbContext.Queryable<Sys_Message, Sys_MessageNotifySetting, Sys_MessageType, Sys_User, Sys_UserMessage>((m, mns, mt, u, um) => new object[]{
                JoinType.Inner,m.MessageTypeID == mns.MessageTypeID,
                JoinType.Inner,m.MessageTypeID == mt.MessageTypeID,
                JoinType.Inner,mns.UserID == u.UserID && !u.IsDelete && u.IsEnable==true,
                JoinType.Left,mns.UserID == um.UserID && m.MessageID == um.MessageID
            })
            .Where((m, mns, mt, u, um) => !supplierMessageTypeID.Contains(m.MessageTypeID) && um.UserMessageID == null && m.CTime>DateTime.Now.AddDays(-7))
            .Select((m, mns, mt, u, um) => new Sys_UserMessage
            {
                //UserMessageID = Guid.NewGuid().ToString().ToUpper(),
                MessageID = m.MessageID,
                MessageTypeID = m.MessageTypeID,
                MessageTypeDesc = mt.MessageTypeDesc,
                UserID = mns.UserID,
                UserName = u.UserName,
                MessageTitle = m.MessageTitle,
                MessageContent = m.MessageBody,
                NotifyType = mt.IsNotifyByEmail == true? "站内信 | 邮件" : "站内信",
                IsReaded= false,
                CUser ="WebJob",
                CTime=DateTime.Now,
                MUser=null,
                MTime =null,
                DUser = null,
                DTime = null
            })
            .ToList();

            //2、供应商消息(Remark字段放入供应商账号)
            supplierUserMesssageList = this.DbContext.Queryable<Sys_Message, Sys_MessageNotifySetting, Sys_MessageType, Sys_User, Sys_UserMessage>((m, mns, mt, u, um) => new object[]{
                JoinType.Inner,m.MessageTypeID == mns.MessageTypeID,
                JoinType.Inner,m.MessageTypeID == mt.MessageTypeID,
                JoinType.Inner,mns.UserID == u.UserID && !u.IsDelete && u.IsEnable==true && u.IsSupplier==true && m.Remark == u.LoginAccount,
                JoinType.Left,mns.UserID == um.UserID && m.MessageID == um.MessageID
            })
            .Where((m, mns, mt, u, um) => supplierMessageTypeID.Contains(m.MessageTypeID) && um.UserMessageID == null)
            .Select((m, mns, mt, u, um) => new Sys_UserMessage
            {
                //UserMessageID = Guid.NewGuid().ToString().ToUpper(),
                MessageID = m.MessageID,
                MessageTypeID = m.MessageTypeID,
                MessageTypeDesc = mt.MessageTypeDesc,
                UserID = mns.UserID,
                UserName = u.UserName,
                MessageTitle = m.MessageTitle,
                MessageContent = m.MessageBody,
                NotifyType = mt.IsNotifyByEmail == true ? "站内信 | 邮件" : "站内信",
                CUser = "WebJob",
                CTime = DateTime.Now,
                IsReaded = false,
                MUser = null,
                MTime = null,
                DUser = null,
                DTime = null
            })
            .ToList();

            innerUserMesssageList.AddRange(supplierUserMesssageList);

            innerUserMesssageList.ForEach((item) =>
            {
                item.UserMessageID = Guid.NewGuid().ToString().ToUpper();
            });

            try
            {
                _userMessageApp.Insert(innerUserMesssageList);
            }
            catch(Exception ex)
            {
                HZ.Core.Logging.LogHelper.Instance.LogDebug(ex.Message);
            }

            return innerUserMesssageList;
        }

        #endregion

        #region 生产发送邮件

        public void ProductionSend(List<PP_ProductionOrder> orders,string operater)
        {
            Sys_MailServerConfig mailConfig = _mailConfigApp.GetMailServerConfig();
            HZ.Core.Email myMail = new Email(mailConfig.MailServerHost, (int)mailConfig.MailServerPort, mailConfig.MailServerAccount, mailConfig.MailServerPassword);
            myMail.SenderDisplayName = mailConfig.SenderDisplayName ?? mailConfig.MailServerAccount;

            var lineList = orders.Select(g => g.ProductionLineCode).Distinct();
            foreach (var item in lineList)
            {
                List<Sys_Mail> sendedUserMailList = new List<Sys_Mail>();
                var userids = _notifyApp.GetList(w => w.MessageTypeID == item).Select(s => s.UserID).ToList();
                var userList = _userApp.GetList(w => userids.Contains(w.UserID)).ToList();
                
                myMail.ActionSendCompletedCallback += new System.Net.Mail.SendCompletedEventHandler(MailSendComputeCallBack);// 注册回调事件
                var tem = orders.Select(g => g.ProductionOrderNo).Distinct();
                var itemsData = _app.GetList(x => tem.Contains(x.ProductionOrderNo) && x.ProductionLineCode == item).Select(s => s.ProductionOrderNo).ToList();
                string emailTitle = "排产单通知";
                var me = GetPath(itemsData,out emailTitle);

                string MessageTitle = emailTitle;
                string MessageContent = emailTitle;
                userList.ForEach(user =>
                {
                    sendedUserMailList.Add(new Sys_Mail
                    {
                        MessageTypeID = item,
                        MessageTypeDesc = item,
                        UserID = user.UserID,
                        UserName = user.UserName,
                        MailSubject = MessageTitle,
                        MailBody = MessageContent,
                        SenderMail = mailConfig.MailServerAccount,
                        ReceiverMail = user.Email,
                        SendTime = DateTime.Now,
                        CUser = operater,
                    });
                });
                myMail.SendEmailAsync(userList.Select(x => x.Email).ToList(), null, MessageTitle, MessageContent, sendedUserMailList, me, emailTitle + ".xls");
            }
        }

        public MemoryStream GetPath(IEnumerable<string> orders,out string emailTitle)
        {
            var itemsData = _app.ExportHostScheduling(null, x => orders.Contains(x.ProductionOrderNo)).ToList();
            List<ExcelColumn<PP_ExportHostScheduling_View>> columns = ExcelService.FetchDefaultColumnList<PP_ExportHostScheduling_View>();
            string[] ignoreField = new string[]
            {
                    "StartTime"
                    ,"ProductionOrderNo"
            };
            List<ExcelColumn<PP_ExportHostScheduling_View>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
            foreach (ExcelColumn<PP_ExportHostScheduling_View> userColumn in ignoreFieldList)
            {
                columns.Remove(userColumn);
            }
            //return ExportToExcelFile<PP_ExportHostScheduling_View>(itemsData, columns);
            var group = itemsData.GroupBy(g => g.StartTime);
            if (group?.Count() > 1)
            {
                //改序号
                foreach (var item in group)
                {
                    int line = 1;
                    foreach (var value in item)
                    {
                        value.SequenceNo = line;
                        line++;
                    }
                }
                var dic = group.ToDictionary(w => $"车间{w.Key.Value.GetDateTimeFormats('M')[0].ToString()}装配", w => w.ToList());
                emailTitle = $"正式排产：{DateTime.Now.GetDateTimeFormats('M')[0].ToString()}主机装配指令单";
                return ExcelService.ExportToExcelByGroup<string, PP_ExportHostScheduling_View>(dic, ExcelFileType.xls, columns);
            }
            else
            {
                emailTitle = $"正式排产：{DateTime.Now.GetDateTimeFormats('M')[0].ToString()}主机装配指令单";
                if (itemsData?.ToList().FirstOrDefault() != null)
                {
                    emailTitle = emailTitle + $"（车间{itemsData?.ToList().FirstOrDefault()?.StartTime.Value.GetDateTimeFormats('M')[0].ToString()}装配)";
                }
                //return ExportToExcelFile<PP_ExportHostScheduling_View>(itemsData, columns, title);
                return ExcelService.ExportToExcel<PP_ExportHostScheduling_View>(itemsData, ExcelFileType.xlsx, "Sheet1", columns);
            }
        }

        #endregion
    }
}
