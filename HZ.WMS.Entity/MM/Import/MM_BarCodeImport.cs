using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.MM.Import
{
    /// <summary>
    /// 物料标签导入
    /// </summary>
    public class MM_BarCodeImport
    {
        /// <summary> 
        /// 条码
        /// </summary> 
        public string 出厂编号 { get; set; }

        /// <summary> 
        /// 批次
        /// </summary> 
        public string 批次 { get; set; }

        /// <summary> 
        /// 物料编号
        /// </summary> 
        public string 产品件号 { get; set; }

        /// <summary> 
        /// 物料名称
        /// </summary> 
        public string 物料名称 { get; set; }

        /// <summary> 
        /// 合同编号
        /// </summary> 
        public string 合同号 { get; set; }

        /// <summary> 
        /// 销售订单行号
        /// </summary> 
        public int? 销售订单行号 { get; set; }

        /// <summary> 
        /// 数量
        /// </summary> 
        public decimal? 数量 { get; set; }

        /// <summary> 
        /// 库存单位
        /// </summary> 
        public string 单位 { get; set; }

        /// <summary> 
        /// 打印数量
        /// </summary> 
        public int? 打印数量 { get; set; }

        /// <summary> 
        /// 仓库
        /// </summary> 
        public string 仓库 { get; set; }

        /// <summary> 
        /// 部件代码
        /// </summary> 
        public string 部件代码 { get; set; }

        /// <summary> 
        /// 备注
        /// </summary> 
        public string 装配线号 { get; set; }

        /// <summary> 
        /// 创建日期
        /// </summary> 
        public DateTime? 日期 { get; set; }

        /// <summary> 
        /// 品号
        /// </summary> 
        public string 品号 { get; set; }

        /// <summary> 
        /// 库位
        /// </summary> 
        public string 库位 { get; set; }
    }
}
