using HZ.Core.Http;
using HZ.Core.Office;
using HZ.WMS.Application;
using HZ.WMS.Application.MM;
using HZ.WMS.Entity.MM;
using HZ.WMS.Entity.MM.Parameters;
using HZ.WMS.Entity.Sys;
using HZ.WMS.WebAPI.Controllers;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Web;
using System.Web.Http;
using static HZ.WMS.Application.ContentBase;
using static HZ.WMS.Application.MM.MM_ReturnApp;

namespace HZ.WMS.WebAPI.Areas.MM.Controllers
{
    /// <summary>
    /// 委外退料
    /// </summary>
    public class MM_ReturnController : ApiBaseController
    {
        #region 初始化

        private MM_ReturnApp _app = new MM_ReturnApp();
        private MM_ReturnDetailApp _detailapp = new MM_ReturnDetailApp();

        #endregion
        
        #region 查询分页列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="page">分页</param>
        /// <param name="keyword">参数</param>
        /// <param name="dateValue">日期区间</param>
        /// <param name="IsPosted">是否过账 0：未过账 1：已过帐</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri] Pagination page, [FromUri] string keyword,
            [FromUri] DateTime[] dateValue, [FromUri] bool? IsPosted)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];
                List<string> strList = new List<string>();
                Console.WriteLine("strList:", strList.Count);
                if (!string.IsNullOrEmpty(keyword))
                {
                    strList = _app.GetDocNumByInfo(keyword, fromTime, toTime);
                }
                var itemsData = _app.GetPageList(page, t => (string.IsNullOrEmpty(keyword)
                        || t.DocNum.Contains(keyword)
                        || t.CUser.Contains(keyword)
                        || strList.Count == 0 
                        || strList.Contains(t.DocNum)
                ) && (t.CTime >= fromTime && t.CTime <= toTime)
                && (IsPosted == null || t.IsPosted == IsPosted)).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 查询明细分页列表

        /// <summary>
        /// 查询明细分页列表
        /// </summary>
        /// <param name="page">分页</param>
        /// <param name="keyword">参数</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageDetailList([FromUri]Pagination page, [FromUri]string keyword)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _detailapp.GetPageList(page, t => t.DocNum == keyword).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 编辑根据单号查询明细信息

        /// <summary>
        /// 编辑根据单号查询明细信息
        /// </summary>
        /// <param name="keyword">参数</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetList([FromUri]string keyword)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _detailapp.GetList(t => t.DocNum == keyword).ToList();
                    result.Data = itemsData;//new ResponsePageData { total = page.Total, items = itemsData };
                    result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="DocNums">根据单号进行删除</param>
        /// <returns></returns>
        //[HttpDelete]
        [HttpDelete]
        //[AllowAnonymous]
        public IHttpActionResult Delete([FromBody]string[] DocNums)
        {
            //[FromBody]string[] ids
            var result = new ResponseData();
            try
            {
                string error_message = "";
                //GetCurrentUser().LoginAccount
                bool bDelete = _app.Deletes(DocNums, GetCurrentUser().LoginAccount, out error_message);
                if (!bDelete || !string.IsNullOrEmpty(error_message))
                {
                    result.Code = (int)WMSStatusCode.UnHandledException;
                    result.Message = error_message;
                }

            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 导出

        /// <summary>
        /// 导出
        /// </summary>
        /// <param name="keyword">参数</param>
        /// <param name="dateValue">日期区间</param>
        /// <param name="IsPosted">是否过账 1：已过帐 0：未过账</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] string keyword, [FromUri] DateTime[] dateValue, [FromUri] bool? IsPosted)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];
                var itemsData = _detailapp.GetList(t => string.IsNullOrEmpty(keyword)
                        || t.DocNum.Contains(keyword) || t.BinLocationName.Contains(keyword)
                        || t.ItemCode.Contains(keyword) || t.SupplierCode.Contains(keyword)
                        || t.SupplierName.Contains(keyword) || t.ItemName.Contains(keyword)
                        || t.WhsCode.Contains(keyword) || t.WhsName.Contains(keyword)
                        || t.RegionCode.Contains(keyword) //|| t.PostUser.Contains(keyword) 
                        || t.CUser.Contains(keyword) || t.RegionCode.Contains(keyword)
                        || t.RegionName.Contains(keyword) || t.BinLocationCode.Contains(keyword)
                        && (IsPosted == null || t.IsPosted == IsPosted)
                        && t.CTime >= fromTime && t.CTime <= toTime).ToList();
                var columns = ExcelService.FetchDefaultColumnList<MM_ReturnDetail>();
                string[] ignoreField = new string[] 
                {
                    "OutsourcingReturnDetailID","Line","BatchNum","ItmsGrpCode","ItmsGrpName",
                    "WhsCode","RegionCode","BinLocationCode","CompanyCode","FactoryCode","MovementType",
                    "SpecialInventory","IsDelete","MUser","MTime","DUser","DTime",
                };
                var ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (var userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                columns.ForEach((column) =>
                {
                    if (column.ColumnName == "IsPosted")
                    {
                        column.Formattor = ExcelExportFormatter.IsOrNoFormatterForBoolean;
                    }
                });

                return ExportToExcelFile(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #region 过账

        /// <summary>
        ///过账
        /// </summary>
        /// <param name="Parameters">参数汇总</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult DoPost([FromBody]MM_ReturnParameters Parameters)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                Sys_User currentUser = GetCurrentUser();
                bool bPost = _app.DoPost(Parameters.entities,null, currentUser.LoginAccount, out error_message);
                if (!bPost)//过账失败==2000，前台处理过账失败后重新刷新界面信息
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2010;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2000;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }

        #endregion

        #region 冲销过账

        /// <summary>
        /// 过账SAP
        /// </summary>
        /// <param name="entities">主信息</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult PassPost([FromBody]MM_ReturnParameters Parameters)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";


                Sys_User currentUser = GetCurrentUser();
                bool bPost = _app.PassPost(Parameters.entities, currentUser.LoginAccount, out error_message);
                if (!bPost)//过账失败==2000，前台处理过账失败后重新刷新界面信息
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2010;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2000;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }

        #endregion

        #region 打印

        /// <summary>
        /// 打印
        /// </summary>
        /// <param name="docNums">单号</param>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IHttpActionResult Print([FromUri]string[] docNums)
        {
            var result = new ResponseData();
            string templateCode = "委外退料单.repx";
            if (docNums == null || docNums.Length == 0 || string.IsNullOrEmpty(templateCode))
            {
                result.Code = (int)WMSStatusCode.Failed;
            }
            else
            {
                DevExpress.XtraReports.UI.XtraReport report =
                    DevExpress.XtraReports.UI.XtraReport.FromFile(HttpContext.Current.Server.MapPath("~/") + ConfigurationManager.AppSettings["LabelTemplateUploadPath"]?.ToString() + templateCode, true);
                DevExpress.DataAccess.Sql.SqlDataSource dataSource = report.DataSource as DevExpress.DataAccess.Sql.SqlDataSource;
                DevExpress.DataAccess.Sql.QueryParameter parameter = new DevExpress.DataAccess.Sql.QueryParameter()
                {
                    Name = "docNums",
                    Type = typeof(string[]),
                    Value = docNums[0]
                };
                dataSource.Queries[0].Parameters[0] = parameter;
                return base.PrintToPDF(report);
            }
            return Json(result);
        }

        #endregion

        #region 保存

        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="Parameters">参数汇总</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Save([FromBody]MM_ReturnParameters Parameters)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                string type = ""; //类型 1:提交失败 2：过账失败
                Sys_User currentUser = GetCurrentUser();
                bool bSave = _app.Save(Parameters, currentUser.LoginAccount, out error_message, out type);
                if (!bSave && type == "1")
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
                else if (!bSave && type == "2")
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2010;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2000;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }

        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="Parameters">参数汇总</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Update([FromBody]MM_ReturnParameters Parameters)
        {

            var result = new ResponseData();
            try
            {
                string error_message = "";
                string type = ""; //类型 1:提交失败 2：过账失败
                Sys_User currentUser = GetCurrentUser();
                bool bUpdate = _app.Update(Parameters, currentUser.LoginAccount, out error_message,out type);
                if (!bUpdate && type == "1")
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
                else if (!bUpdate && type == "2")
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2010;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2000;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }

        #endregion

        #region 获取系统生成的单号

        /// <summary>
        /// 获取系统生成的单号
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IHttpActionResult GetDocNum()
        {
            var result = new ResponseData();
            try
            {
                result.Data = base.GenerateDocNum(DocType.MM, DocFixedNumDef.MM_Return);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取供应商库存

        /// <summary>
        /// 获取供应商库存
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IHttpActionResult GetSupplierStock([FromUri]Pagination page, [FromUri]string keyword)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetSupplierStock(page, x => (string.IsNullOrEmpty(keyword) 
                                                                  || x.ItemCode.Contains(keyword) || x.ItemName.Contains(keyword)
                                                                  || x.SupplierCode.Contains(keyword) || x.SupplierName.Contains(keyword)
                                                                  || x.Unit.Contains(keyword))).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion



    }
}