using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.MD
{
    /// <summary>
    /// 生产配送配置表
    /// </summary>
    [SugarTable("MD_ProductionDistributionSetting")]
    public class MD_ProductionDistributionSetting : BaseEntity
    {
        /// <summary>
        /// 主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string ID { get; set; }
        /// <summary>
        /// 员工号
        /// </summary>
        [Description("员工号")]
        public string EmployeeNumber { get; set; }
        /// <summary>
        /// 员工姓名
        /// </summary>
        [Description("员工姓名")]
        public string EmployeeName { get; set; }
        /// <summary>
        /// 线体编码
        /// </summary>
        [Description("线体编码")]
        public string ProductionLineCode { get; set; }
        /// <summary>
        /// 线体描述
        /// </summary>
        [Description("线体描述")]
        public string ProductionLineDes { get; set; }
        /// <summary>
        /// 物料描述 模糊查询
        /// </summary>
        [Description("物料描述")]
        public string ItemName { get; set; }
        /// <summary>
        /// 物料组编码
        /// </summary>
        [Description("物料组编码")]
        public string MaterialGroupCode { get; set; }
        /// <summary>
        /// 物料组描述
        /// </summary>
        [Description("物料组描述")]
        public string MaterialGroupDes { get; set; }
    }
}
