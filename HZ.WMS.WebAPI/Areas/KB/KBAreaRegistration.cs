using System.Web.Mvc;

namespace HZ.WMS.WebAPI.Areas.KB
{
    public class KBAreaRegistration : AreaRegistration 
    {
        public override string AreaName 
        {
            get 
            {
                return "KB";
            }
        }

        public override void RegisterArea(AreaRegistrationContext context) 
        {
            context.MapRoute(
                "KB_default",
                "KB/{controller}/{action}/{id}",
                new { action = "Index", id = UrlParameter.Optional }
            );
        }
    }
}