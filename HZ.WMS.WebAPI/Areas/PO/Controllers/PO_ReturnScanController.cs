using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using HZ.Core.Security;
using HZ.WMS.Application.PO;
using HZ.WMS.Entity.PO;
using HZ.Core.Http;
using HZ.WMS.WebAPI.Controllers;
using Newtonsoft.Json;
using HZ.Core.Vue;
using HZ.WMS.Entity.Sys;
using HZ.Core.Office;
using HZ.WMS.Application;
using HZ.WMS.Entity.SAP.View;
using static HZ.WMS.Application.PO.PO_ReturnScanApp;
using HZ.WMS.Entity.PO.Parameters;
using System.Web;
using System.Configuration;

namespace HZ.WMS.WebAPI.Areas.PO.Controllers
{
    /// <summary>
    /// 采购退货
    /// </summary>
    public class PO_ReturnScanController : ApiBaseController
    {
        #region 初始化

        /// <summary>
        /// 采购退货
        /// </summary>
        private PO_ReturnScanApp _app = new PO_ReturnScanApp();

        /// <summary>
        /// 采购退货明细
        /// </summary>
        private PO_ReturnScanDetailedApp _detailedApp = new PO_ReturnScanDetailedApp();

        #endregion

        #region 查询分页列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="page">分页</param>
        /// <param name="keyword"></param>
        /// <param name="dateValue">日期</param>
        /// <param name="isPosted">是否过账</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]string keyword, [FromUri] DateTime[] dateValue, [FromUri] bool? isPosted)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];
                var itemsData = _app.GetPageList(page, t => (string.IsNullOrEmpty(keyword)
                        || t.DocNum.Contains(keyword) 
                        || t.SupplierCode.Contains(keyword) || t.SupplierName.Contains(keyword)
                        || t.PostUser.Contains(keyword) || t.Remark.Contains(keyword)
                        || t.CUser.Contains(keyword))
                        && (t.CTime >= fromTime && t.CTime <= toTime)
                        && (isPosted == null || t.IsPosted == isPosted)).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 查询子表的分页列表

        /// <summary>
        /// 查询子表的分页列表
        /// </summary>
        /// <param name="page">分页</param>
        /// <param name="DocNum">单号</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetDetailedPageList([FromUri]Pagination page, [FromUri]string DocNum)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _detailedApp.GetPageList(page,x => x.DocNum == DocNum).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 编辑根据单号查询明细信息

        /// <summary>
        /// 编辑根据单号查询明细信息
        /// </summary>
        /// <param name="keyword">参数</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetList([FromUri]string keyword)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _detailedApp.GetList(t => t.DocNum ==keyword).ToList();
                result.Data = itemsData;//new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 保存

        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="Parameters">参数类</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult SubmitScanInfo([FromBody]PO_ReturnScanParameters Parameters)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                string type = ""; //类型 1:提交失败 2：过账失败
                Sys_User currentUser = GetCurrentUser();
                bool bSave = _app.Save(Parameters, currentUser.LoginAccount, out error_message,out type);
                if (!bSave && type == "1")
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
                else if (!bSave && type == "2")
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2010;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2000;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }

        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="Parameters">参数类</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Update([FromBody]PO_ReturnScanParameters Parameters)
        {

            var result = new ResponseData();
            try
            {
                string error_message = "";
                string type = ""; //类型 1:提交失败 2：过账失败
                Sys_User currentUser = GetCurrentUser();
                bool bUpdate = _app.Update(Parameters, currentUser.LoginAccount, out error_message, out type);
                if (!bUpdate && type == "1")
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
                else if (!bUpdate && type == "2")
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2010;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2000;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="DocNums">根据单号进行删除</param>
        /// <returns></returns>
        [HttpDelete]
        public IHttpActionResult Delete([FromBody]string[] DocNums)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                //int i = _app.Update(ids, GetCurrentUser().LoginAccount, out error_message);
                bool bDelete = _app.Delete(DocNums, GetCurrentUser().LoginAccount, out error_message);
                if (!bDelete || !string.IsNullOrEmpty(error_message))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }

            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 过账

        /// <summary>
        ///过账
        /// </summary>
        /// <param name="Parameters">参数类</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult DoPost([FromBody]PO_ReturnScanParameters Parameters)
        {

            var result = new ResponseData();
            try
            {
                string error_message = "";
                Sys_User currentUser = GetCurrentUser();
                bool bPost = _app.DoPost(Parameters.entities,null, currentUser.LoginAccount, out error_message);
                if (!bPost)//过账失败==2000，前台处理过账失败后重新刷新界面信息
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2010;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2000;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }
        #endregion

        #region 导出

        /// <summary>
        /// 导出
        /// </summary>
        /// <param name="keyword">参数</param>
        /// <param name="dateValue">日期区间</param>
        /// <param name="isPosted">是否过账 1：已过帐 0：未过账</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] string keyword, [FromUri] DateTime[] dateValue, [FromUri] string isPosted)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];
                var itemsData = _detailedApp.GetList().Where(t => string.IsNullOrEmpty(keyword) || t.DocNum.Contains(keyword) 
                                                                  || t.PostUser.Contains(keyword) || t.CUser.Contains(keyword)
                ).Where(x => x.CTime >= fromTime && x.CTime <= toTime 
                        && !x.IsDelete 
                        && (string.IsNullOrEmpty(isPosted) || x.IsPosted == bool.Parse(isPosted))).ToList();
                //var itemsData = _app.GetList().ToList();
                var columns = ExcelService.FetchDefaultColumnList<PO_ReturnScanDetailed>();
                string[] ignoreField = new string[] 
                {
                    "ReturnScanDetailedID", "CompanyCode", "FactoryCode", "MovementType", "SpecialInventory", "EvaluationType",
                    "BaseEntry", "SalesOrderNum", "SalesOrderLine", "BarCode", "WhsCode","RegionCode", "BinLocationCode","Line","BatchNum","SupplierBatch",
                    "IsDelete","MUser","MTime", "DUser", "DTime"
                };

                var ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<PO_ReturnScanDetailed> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                columns.ForEach((column) =>
                {
                    if (column.ColumnName == "IsPosted")
                    {
                        column.Formattor = ExcelExportFormatter.IsOrNoFormatterForBoolean;
                    }
                });

                return ExportToExcelFile(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #region 获取系统生成的单号

        /// <summary>
        /// 获取系统生成的单号
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetDocNum()
        {
            var result = new ResponseData();
            try
            {
                result.Data = base.GenerateDocNum(DocType.PO, DocFixedNumDef.PO_ReturnScan);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 打印

        /// <summary>
        /// 打印
        /// </summary>
        /// <param name="docNums"></param>
        /// <param name="templateCode"></param>
        /// <returns></returns>
        [HttpGet]
        //[AllowAnonymous]
        public IHttpActionResult Print([FromUri]string[] docNums)
        {
            var result = new ResponseData();
            string templateCode = "采购退货单.repx";
            if (docNums == null || docNums.Length == 0 || string.IsNullOrEmpty(templateCode))
            {
                result.Code = (int)WMSStatusCode.Failed;
            }
            else
            {
                //MD_LabelTempleteApp _labelTemplateApp = new MD_LabelTempleteApp();
                //MD_LabelTemplete template = _labelTemplateApp.GetFirstEntity(x => x.Remark == templateCode);
                //if (template == null)
                //{
                //    result.Code = (int)WMSStatusCode.Failed;
                //}
                //else
                //{
                DevExpress.XtraReports.UI.XtraReport report =
                    DevExpress.XtraReports.UI.XtraReport.FromFile(HttpContext.Current.Server.MapPath("~/") + ConfigurationManager.AppSettings["LabelTemplateUploadPath"]?.ToString() + templateCode, true);
                DevExpress.DataAccess.Sql.SqlDataSource dataSource = report.DataSource as DevExpress.DataAccess.Sql.SqlDataSource;
                //switch (templateCode)
                //{
                //    case "PP_BarCodeReturn":
                //    case "PP_BarCode":
                //    case "MM_BarCode":
                DevExpress.DataAccess.Sql.QueryParameter parameter = new DevExpress.DataAccess.Sql.QueryParameter()
                {
                    Name = "docNums",
                    Type = typeof(string),
                    Value = docNums[0]
                };

                dataSource.Queries[0].Parameters[0] = parameter;
                //dataSource.Fill();
                return base.PrintToPDF(report);

                //default:
                //    break;


            }
            return Json(result);
        }

        #endregion

    }
}

