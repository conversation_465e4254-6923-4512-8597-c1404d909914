using SqlSugar;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using HZ.WMS.Application.MD;
using HZ.WMS.Application.QM;
using HZ.WMS.Application.SAP;
using HZ.WMS.Application.Sys;
using HZ.WMS.Entity;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.PO;
using HZ.WMS.Entity.PO.Parameters;
using HZ.WMS.Entity.QM.ViewModel;
using HZ.WMS.Entity.SAP;
using HZ.WMS.Entity.SAP.View;
using HZ.WMS.Entity.SRM;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace HZ.WMS.Application.PO
{
    /// <summary>
    /// 采购入库
    /// </summary>
    public class PO_PurchaseReceiptApp : BaseApp<PO_PurchaseReceipt>
    {
        #region 初始化

         BaseApp<BaseEntity> _baseApp = new BaseApp<BaseEntity>();
         QM_PurchaseInspectionApp _purchaseinspectionApp = new QM_PurchaseInspectionApp();

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public PO_PurchaseReceiptApp() : base(){}

        #endregion

        #region PC

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="ids">ID数组</param>
        /// <param name="opUser">当前用户</param>
        /// <param name="error_message">错误消息</param>
        public bool Deletes(string[] ids, string opUser, out string error_message)
        {
            error_message = "";
            bool bDeleted = true;
            var entities = GetListByKeys(ids);
            if (entities.Any(x=>x.IsPosted==true))//删除校验
            {
                error_message = "ui.Message.PODeletePostingWarning"; // "已过账数据不允许删除!";
                return false;
            }
            try
            {
                DbContext.Ado.BeginTran();//开启事务
                   
                Delete(entities,opUser);

                DbContext.Ado.CommitTran();//提交事务
            }
            catch (Exception ex)
            {
                bDeleted = false;
                error_message = ex.Message;
                if(DbContext.Ado.IsAnyTran())
                    DbContext.Ado.RollbackTran();//失败回滚
            }
            
            return bDeleted;
        }

        #endregion

        #region 过账

        /// <summary>
        /// 过账
        /// </summary>
        /// <param name="entities">采购收货集合</param>
        /// <param name="opUser">当前过账用户</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool DoPost(List<PO_PurchaseReceipt> entities, string opUser, out string error_message)
        {
            error_message = "";
            try
            {
                MD_StockApp _stockApp = new MD_StockApp();
                Sys_SAPCompanyInfoApp _SAPCompanyInfoApp = new Sys_SAPCompanyInfoApp();
                QM_PurchaseInspectionApp _insApp = new QM_PurchaseInspectionApp();

                //多条数据后根据单号进行分组
                var mainInfoList = entities.GroupBy(g => new
                {
                    g.DocNum
                }).Select(q => new
                {
                    DocNum = q.Key.DocNum
                });

                //按订单号分组
                foreach (var mainInfo in mainInfoList)
                {
                    var list = new List<ZFGWMS002>();
                    var list1 = new List<ZFGWMS002_1>();
                    int Line = 0;
                    var conditionList = entities.Where(x => x.DocNum == mainInfo.DocNum)?.ToList();
                    conditionList.ForEach(x=> 
                    {
                        Line = Line + 1;
                        x.Line = Line;
                        list.Add(new ZFGWMS002
                        {
                            ZNUM = Line,
                            EBELN = x.BaseNum,
                            EBELP = x.BaseLine,
                            MATNR = x.ItemCode,
                            WERKS = x.FactoryCode ?? "2002",
                            BWART = x.MovementType ?? "101",
                            MENGE = x.PurchaseReceiptQty,
                            MEINS = x.Unit,
                            LGORT = x.WhsCode
                        });
                    });

                    bool ispost = false;
                    DateTime time = DateTime.Now;

                    //查询Sap账期时间
                    DateTime ManualPostTime = new SAPApp().GetSAPpostTime(Convert.ToDateTime(conditionList[0].ManualPostTime));
                    //purchaseReceipt.CompanyCode 公司代码
                    var saplist = _SAPCompanyInfoApp.ZFGWMS002(conditionList[0].CompanyCode ?? "001", mainInfo.DocNum, "", ManualPostTime, list, list1, out ispost, out error_message);
                    if (saplist != null && saplist.Count > 0)
                    {
                        //List<PO_PurchaseReceipt> queryList = new List<PO_PurchaseReceipt>();

                        //DbContext.Ado.BeginTran();
                        //_stockApp.DbContext = this.DbContext;
                        foreach (var sap in saplist)
                        {
                            var query = conditionList.Where(x => x.DocNum == mainInfo.DocNum && x.BaseNum == sap.basenum && x.BaseLine == sap.baseline).ToList().FirstOrDefault();
                            if (query != null)
                            {
                                query.IsPosted = true;
                                query.PostUser = opUser;
                                query.PostTime = time;
                                query.MUser = opUser;
                                query.MTime = time;
                                query.SapDocNum = sap.sapDocNum;
                                query.SapLine = sap.sapline;
                                query.ManualPostTime = ManualPostTime;
                                query.SAPmark = "S";

                                //待完善，完善内容 事务，库存缺少销售订单信息。

                                if (Update(query) > 0)//更新成功后更新库存
                                {
                                    // 插入收货记录成功才能更新库存
                                    string stockMsg = string.Empty;
                                    MD_Stock stock = new MD_Stock();
                                    stock.BarCode = query.BarCode ?? "";
                                    stock.BatchNum = query.BatchNum ?? "";
                                    stock.ItemCode = query.ItemCode.Trim();
                                    stock.ItemName = query.ItemName.Trim();
                                    stock.ItmsGrpCode = query.ItmsGrpCode;
                                    stock.ItmsGrpName = query.ItmsGrpName;
                                    stock.WhsCode = query.WhsCode;
                                    stock.WhsName = query.WhsName;
                                    stock.RegionCode = query.RegionCode;
                                    stock.RegionName = query.RegionName;
                                    stock.BinLocationCode = query.BinLocationCode;
                                    stock.BinLocationName = query.BinLocationName;
                                    stock.Qty = query.PurchaseReceiptQty;
                                    stock.Unit = query.Unit;
                                    stock.IsDelete = false;
                                    //根据采购单号+行号查询评估类型信息 及采购入库数量比例
                                    SAP_EKKO_EKPO_View mod = DbContext.Queryable<SAP_EKKO_EKPO_View>().Where(x => x.EBELN == query.BaseNum && x.EBELP == query.BaseLine).ToList().FirstOrDefault();
                                    if (mod != null)
                                    {
                                        stock.Qty = stock.Qty * mod.UMREZ;
                                        stock.AssessType = mod.BWTAR ?? "";
                                    }
                                    stock.SaleNum = query.SalesOrderNum ?? "";
                                    stock.SaleLine = query.SalesOrderLine ?? 0;
                                    if (!string.IsNullOrEmpty(string.IsNullOrEmpty(query.SalesOrderNum) ? "" : query.SalesOrderNum.Trim()))
                                        stock.SpecialStock = "E";//销售库存

                                    if (!_stockApp.StockIn(stock, opUser, out stockMsg))
                                    {
                                        error_message = stockMsg;
                                        //return false;
                                    }

                                    //过账成功后回写SRM报检单入库数量
                                    var insquery = DbContextForSRM.Queryable<P_InspectionDetail>().Where(t => t.InspectionNo == query.InspectionNum && t.OrderNo == query.BaseNum
                                                                                             && t.OrderLine == query.BaseLine && t.ItemCode == query.ItemCode
                                                                                             && t.IsDelete == false).ToList().FirstOrDefault();
                                    if (insquery != null)
                                    {
                                        if (insquery.StorageQty == null)
                                        {
                                            insquery.StorageQty = 0;
                                        }
                                        insquery.StorageQty += query.PurchaseReceiptQty;
                                        //insquery.Status = "3";
                                        _insApp.UpdateInspectionForSRM(insquery);
                                    }
                                }
                            }
                        }

                        //DbContext.Ado.CommitTran();

                    }
                    else
                    {
                        List<PO_PurchaseReceipt> listpo = conditionList.Where(x => x.DocNum == mainInfo.DocNum).ToList();
                        foreach (PO_PurchaseReceipt info in listpo)
                        {
                            info.SAPmark = "E";
                            info.SAPmessage = error_message;
                        }
                        Update(listpo);

                        error_message = "单号[" + mainInfo.DocNum + "],过账失败，失败原因：" + error_message;
                        return false;
                    }
                }
                if (string.IsNullOrEmpty(error_message))
                {
                    error_message = "过账成功";
                }
                return true;
            }
            catch (Exception ex)
            {
                error_message = ex.Message;
                return false;
            }

        }

        #endregion



        #region 取消过账

        /// <summary>
        /// 取消过账
        /// </summary>
        /// <param name="entities">采购收货集合</param>
        /// <param name="opUser">当前过账用户</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool PassPost(List<PO_PurchaseReceipt> entities, string opUser, out string error_message)
        {
            error_message = "";
            try
            {
                MD_StockApp _stockApp = new MD_StockApp();
                Sys_SAPCompanyInfoApp _SAPCompanyInfoApp = new Sys_SAPCompanyInfoApp();
                QM_PurchaseInspectionApp _insApp = new QM_PurchaseInspectionApp();

                //多条数据后根据单号进行分组
                var mainInfoList = entities.GroupBy(g => new
                {
                    g.SapDocNum
                }).Select(q => new
                {
                    SapDocNum = q.Key.SapDocNum
                });

                //按订单号分组
                foreach (var mainInfo in mainInfoList)
                {
                    List<string> list = new List<string>();
                    var conditionList = entities.Where(x => x.SapDocNum == mainInfo.SapDocNum)?.ToList();
                    conditionList.ForEach(x =>
                    {
                        list.Add(x.SapLine.ToString());

                    });

                    bool ispost = false;
                    DateTime time = DateTime.Now;

                    //查询Sap账期时间
                    DateTime ManualPostTime = new SAPApp().GetSAPpostTime(Convert.ToDateTime(conditionList[0].ManualPostTime));
                    //purchaseReceipt.CompanyCode 公司代码
                    var saplist = _SAPCompanyInfoApp.ZFGWMS021(conditionList[0].CompanyCode ?? "001", mainInfo.SapDocNum , list,ManualPostTime, out ispost, out error_message);
                    if (string.IsNullOrEmpty(error_message))
                    {
                        foreach (var query in conditionList)
                        {

                            query.IsDelete = true;
                            query.DUser = opUser;
                            query.DTime = time;
                            query.SAPmark = "已冲销";

                            //待完善，完善内容 事务，库存缺少销售订单信息。
                            if (Update(query) > 0)//更新成功后更新库存
                            {
                                //评估类型为空的话，走正常库存逻辑
                                if (string.IsNullOrEmpty(string.IsNullOrEmpty(query.EvaluationType) ? "" : query.EvaluationType.Trim()))
                                {
                                    string errorMsg = string.Empty;
                                    if (!_stockApp.StockOut(query.ItemCode, query.BinLocationCode, query.SalesOrderNum, Convert.ToInt32(query.SalesOrderLine), query.PurchaseReceiptQty, opUser, out errorMsg))
                                    {
                                        error_message = errorMsg;
                                        //return false;
                                    }
                                }
                                else//评估类型不为空的话，走评估类型的库存逻辑
                                {
                                    string errorMsg = string.Empty;
                                    if (!_stockApp.StockOutForAssmentType(query.ItemCode, query.BinLocationCode, query.SalesOrderNum, Convert.ToInt32(query.SalesOrderLine), query.PurchaseReceiptQty, query.EvaluationType, opUser, out errorMsg))
                                    {
                                        error_message = errorMsg;
                                        //return false;
                                    }
                                }


                                //过账成功后回写SRM报检单入库数量
                                var insquery = DbContextForSRM.Queryable<P_InspectionDetail>().Where(t => t.InspectionNo == query.InspectionNum && t.OrderNo == query.BaseNum
                                                                                         && t.OrderLine == query.BaseLine && t.ItemCode == query.ItemCode
                                                                                         && t.IsDelete == false).ToList().FirstOrDefault();
                                if (insquery != null)
                                {
                                    if (insquery.StorageQty == null)
                                    {
                                        insquery.StorageQty = 0;
                                    }
                                    insquery.StorageQty -= query.PurchaseReceiptQty;
                                    //insquery.Status = "3";
                                    _insApp.UpdateInspectionForSRM(insquery);
                                }

                            }
                        }

                        //DbContext.Ado.CommitTran();

                    }
                    else
                    {
                        error_message = "单号[" + mainInfo.SapDocNum + "],冲销过账失败，失败原因：" + error_message;
                        return false;
                    }
                }
                if (string.IsNullOrEmpty(error_message))
                {
                    error_message = "冲销过账成功";
                }
                return true;
            }
            catch (Exception ex)
            {
                error_message = ex.Message;
                return false;
            }

        }

        #endregion

        #endregion

        #region Mobile

        #region 查询采购检验关联采购收货信息

        /// <summary>
        /// 校验
        /// </summary>
        /// <param name="InspectionNum">报检单号</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool ValidationInspection(string InspectionNum, out string error_message)
        {
            error_message = "";
            var query = _purchaseinspectionApp.GetList(x => x.InspectionNum == InspectionNum && x.IsPosted.Equals(true)).ToList();
            if (query == null || query.Count <= 0)
            {
                error_message = "报检单号[" + InspectionNum + "]未进行采购检验";
                return false;
            }
            return true;
        }

        /// <summary>
        /// 查询采购检验关联采购收货信息
        /// </summary>
        /// <param name="InspectionNum">报检单号</param>
        /// <param name="error_message">错误消息</param>
        public List<QM_InspectionForReceipt_View> GetInspectionForReceipt(string InspectionNum, out string error_message)
        {
            error_message = "";
            var query = DbContext.Queryable<QM_InspectionForReceipt_View>()
                .Where(x => x.InspectionNum == InspectionNum)
                .OrderBy(t => t.ItemName)
              .OrderBy(r => r.ItemCode)
              .OrderBy(r => r.BaseNum)
              .OrderBy(r => r.Remark).ToList();
            if (query == null || query.Count <= 0)
            {
                error_message = "报检单号[" + InspectionNum + "]已入库完成！";
            }
            return query;
        }

        #endregion

        #region 保存

        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="Parameters">参数类</param>
        /// <param name="user">用户信息</param>
        /// <param name="error_message">错误消息返回</param>
        /// <param name="type">类型 1:提交失败 2：过账失败 </param>
        public bool Save(PO_PurchaseReceiptParameters Parameters, string user, out string error_message, out string type)
        {
            error_message = "";
            type = "";

            var InspectionNums = Parameters.entities.Select(x => x.InspectionNum).Distinct().ToArray();

            var query1 = DbContext.Queryable<QM_InspectionForReceipt_View>().Where(x => InspectionNums.Contains(x.InspectionNum)).ToList();


            var list = Parameters.entities.GroupBy(g => new
            {
                g.BaseNum,
                g.ItemCode,
                g.WhsCode,
                g.InspectionNum,
                g.InspectionLine
            }).Select(q => new
            {
                BaseNum = q.Key.BaseNum,
                ItemCode = q.Key.ItemCode,
                WhsCode = q.Key.WhsCode,
                InspectionNum = q.Key.InspectionNum,
                InspectionLine = q.Key.InspectionLine,
                QualifiedQty = q.Sum(s => s.QualifiedQty)
            }).ToList();


            foreach (var x in list)
            {
                if (string.IsNullOrEmpty(x.WhsCode.Trim()))
                {
                    error_message = "采购单号[" + x.BaseNum + "],物料号[" + x.ItemCode + "],仓库信息为空,请选择仓库";
                    type = "1";
                    return false;
                }

                //校验是否数据量够

                decimal? QualifiedQty = query1.Where(v => v.InspectionNum == x.InspectionNum && v.InspectionLine == x.InspectionLine).ToList().FirstOrDefault()?.QualifiedQty;
                if (x.QualifiedQty > (QualifiedQty ?? 0))
                {
                    error_message = "检验单号[" + x.InspectionNum + "],行号[" + x.InspectionLine + "],没有可入库数量,请重新扫描";
                    type = "1";
                    return false;
                }
            }
            
            //调用仓库系统参数
            var jsonArray = new JArray();
            List<string> whsCodes = new List<string>();
            whsCodes.Add("X008");
            whsCodes.Add("X002");

            MD_ItemApp _itemApp = new MD_ItemApp();
            try
            {
                string DocNum = _baseApp.GetNewDocNum(DocType.PO, DocFixedNumDef.PO_PurchaseReceipt);
                DateTime date = DateTime.Now;
                var query= new List<PO_PurchaseReceipt>();

                Parameters.entities.ForEach(x =>
                {
                    string Batch=null;
                    //查询是否启用批次管理
                    //var Item = _itemApp.GetList(t=> t.ItemCode==x.ItemCode && t.StockManWay==1)?.ToList().FirstOrDefault();
                    //if (Item !=null)
                    //{
                    //    Batch = _baseApp.GetNewDocNum(DocType.MD, DocFixedNumDef.BatchNo);// GetList(t => t.BatchNum == x.Batch).Max(i => i.BatchNum);
                    //    //if (string.IsNullOrEmpty(Batch))
                    //    //{
                    //    //    Batch = Convert.ToDateTime(DateTime.Now).ToString("yyyyMMdd") + 001;
                    //    //}
                    //    //else
                    //    //{
                    //    //    //截取日期后的最大值
                    //    //    string count = Batch.Substring(8, Batch.Length - 1);
                    //    //    Batch= Convert.ToDateTime(DateTime.Now).ToString("yyyyMMdd") + Convert.ToInt32(count+1);
                    //    //}
                    //}

                    query.Add(new PO_PurchaseReceipt
                    {
                        DocNum = DocNum,
                        CompanyCode = "001",
                        FactoryCode = "2002",
                        MovementType = "101",
                        BaseNum = x.BaseNum,
                        BaseLine = x.BaseLine,
                        //BatchNum = x.Batch,
                        BatchNum = Batch,
                        SupplierCode = x.SupplierCode,
                        SupplierName = x.SupplierName,
                        ItemCode = x.ItemCode,
                        ItemName = x.ItemName,
                        PurchaseReceiptQty = x.QualifiedQty,
                        Unit = x.Unit,
                        WhsCode = x.WhsCode,
                        WhsName = x.WhsName,
                        RegionCode = x.RegionCode,
                        RegionName = x.RegionName,
                        BinLocationCode = x.BinLocationCode,
                        BinLocationName = x.BinLocationName,
                        IsPosted = false,
                        Remark = x.Remark,
                        IsDelete = false,
                        CUser = user,
                        CTime = date,
                        ManualPostTime = Parameters.ManualPostTime,
                        InspectionNum=x.InspectionNum,
                        InspectionLine=x.InspectionLine,
                        SalesOrderNum=x.SalesOrderNum,
                        SalesOrderLine=x.SalesOrderLine,
                        DeliveryDate=x.DeliveryDate
                    });
                    
                    if (whsCodes.Contains(x.WhsCode))
                    {
                        var jsonObject = new JObject();
                        jsonObject.Add("inOutNumber",new JValue(x.QualifiedQty));
                        jsonObject.Add("inspectionFormNumber",new JValue(x.InspectionNum));
                        jsonObject.Add("inOutType",new JValue(2));
                        jsonObject.Add("operationType",1);
                        var jsonObj = new JObject();
                        jsonObj.Add("partCode",new JValue(x.ItemCode));
                        jsonObject.Add("materialStorageInfo",jsonObj);
                        jsonArray.Add(jsonObject);
                    }
                });
                
                // 第三方入库
                try
                {
                    string postRst = HttpPost("https://se.hzforward.com/prod-api/material/storageInOut/materialReceipt", jsonArray.ToString(), "POST");
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                }


                DbContext.Ado.BeginTran();

                //批量插入
                Insert(query);

                DbContext.Ado.CommitTran();

                Sys_SwithConfigApp _switchApp = new Sys_SwithConfigApp();
                //是否自动过账判定
                if (_switchApp.IsPOPurchaseReceiptAutoPost)
                {
                    string postMsg = "";
                    bool bpost= DoPost(query, user, out postMsg);
                    if (!bpost)
                    {
                        error_message = "提交成功"+postMsg;
                        type = "2";
                        return false;
                    }
                    else
                    {
                        error_message = "提交并"+postMsg;
                    }
                }
                if(string.IsNullOrEmpty(error_message))
                    error_message = "提交成功";
                return true;
            }
            catch (Exception ex)
            {
                if(DbContext.Ado.IsAnyTran())
                    DbContext.Ado.RollbackTran();
                error_message = ex.Message;
                type = "1";
                return false;
            }
        }

        #endregion

        #endregion

        /// <summary>
        /// 发运计划接口请求
        /// </summary>
        /// <param name="token"></param>
        /// <param name="url"></param>
        /// <param name="body"></param>
        /// <param name="postMethodType"></param>
        /// <returns></returns>
        private string HttpPost(string url, string body, string postMethodType)
        {
            Encoding encoding = Encoding.UTF8;
            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(url);
            request.Method = postMethodType;
            request.Accept = "text/html, application/xhtml+xml, */*";
            request.ContentType = "application/json";
            byte[] buffer = encoding.GetBytes(body);
            request.ContentLength = buffer.Length;
            request.GetRequestStream().Write(buffer, 0, buffer.Length);
            HttpWebResponse response = (HttpWebResponse)request.GetResponse();
            using (StreamReader reader = new StreamReader(response.GetResponseStream(), Encoding.UTF8))
            {
                return reader.ReadToEnd();
            }
        }

    }
}
