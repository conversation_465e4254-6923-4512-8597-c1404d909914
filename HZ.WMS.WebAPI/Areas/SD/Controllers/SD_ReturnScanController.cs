using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;

//
using HZ.Core.Security;
using HZ.WMS.Application.SD;
using HZ.WMS.Application.SAP;
using HZ.WMS.Entity.SD;
using HZ.WMS.Entity.Sys;
using HZ.Core.Http;
using HZ.WMS.WebAPI.Controllers;
using Newtonsoft.Json;
using HZ.Core.Vue;
using HZ.WMS.Application.MD;
using HZ.Core.Extensions;
using HZ.WMS.Application;
using SqlSugar;
using HZ.Core.Office;
using HZ.WMS.Entity.SD.Parameters;

namespace HZ.WMS.WebAPI.Areas.SD.Controllers
{
    /// <summary>
    /// 销售退货
    /// </summary>
    public class SD_ReturnScanController : ApiBaseController
    {
        #region 初始化

        private BaseApp<SD_ReturnScan> _baseApp = new BaseApp<SD_ReturnScan>();
        private SD_ReturnScanApp _app = new SD_ReturnScanApp();
        private SD_DeliveryScanApp _deliveryScanApp = new SD_DeliveryScanApp();

        private MD_WarehouseApp _whsApp = new MD_WarehouseApp();
        private MD_RegionApp _regionApp = new MD_RegionApp();
        private MD_BinLocationApp _binLocationApp = new MD_BinLocationApp();
        private SAPApp _xzsapApp = new SAPApp();
        private SD_DeliveryScanApp _deliveryscanApp = new SD_DeliveryScanApp();
        private MD_StockApp _stockApp = new MD_StockApp();

        #endregion

        #region PC

        #region 查询分页列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]string keyword, [FromUri] DateTime[] dateValue, [FromUri] bool? isPosted)
        {
            var result = new ResponseData();
            try
            {
                Sys_User currentUser = GetCurrentUser();

                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];
                var itemsData = _app.GetPageList(page, t =>( string.IsNullOrEmpty(keyword) 
                        || t.DocNum.Contains(keyword)
                        || t.BaseNum.Contains(keyword) || t.ItemCode.Contains(keyword)
                        || t.ItemName.Contains(keyword) || t.CustomerName.Contains(keyword) 
                        || t.CustomerCode.Contains(keyword) || t.BatchNum.Contains(keyword)
                        || t.PostUser.Contains(keyword) || t.CUser.Contains(keyword))
                        && (t.CTime >= fromTime && t.CTime <= toTime)
                        && (isPosted == null || t.IsPosted == isPosted)).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <returns></returns>
        [HttpDelete]
        public IHttpActionResult Delete([FromBody]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                bool bDelete = _app.Delete(ids, GetCurrentUser().LoginAccount, out error_message);
                if (!bDelete)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }

            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 仓库-废弃

        #region 获取销售退货仓库

        /// <summary>
        /// 获取销售退货仓库
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetWarehouse()
        {

            var result = new ResponseData();
            try
            {
                result.Data = _whsApp.GetDefaultWarehouse();       // 法拉鼎只有一个仓库
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }


        #endregion

        #region 获取区域

        /// <summary>
        /// 获取区域
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetSdRegion()
        {

            var result = new ResponseData();
            try
            {
                result.Data = _regionApp.GetList(x => x.RegionCode == MD_RegionApp.INSPECTION_REGIONCODE).ToList();       // 销售退货默认进入不良库？
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }

        #endregion

        #region 获取库位

        /// <summary>
        /// 获取库位
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetSdBinLocation(string regionCode)
        {

            var result = new ResponseData();
            try
            {
                result.Data = _regionApp.GetRegionBinLocation(regionCode).ToList();
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }
        #endregion

        #endregion

        #region 扫描条码(二维码)拉取相关信息-废弃

        /// <summary>
        /// 扫描条码(二维码)拉取相关信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetDetailByBarCode([FromUri] string barCode)
        {

            var result = new ResponseData();
            try
            {
                string error_message = "";
                if (_baseApp.IsWmsBarCode(barCode)) //如果是条码
                {
                    //判断条码是否已退货扫描
                    //if (!_app.GetList(t => t.BarCode == barCode).Any())  //没有退货扫描
                    //{
                    //    if (barCode.ExtractLetterPart() == DocFixedNumDef.SD_BarCodeReturn) //如果是退货条码
                    //    {
                          
                    //        SD_BarCodeReturn barcode = _barcodeApp.GetBarCodeInfo(barCode);
                    //        result.Data = barcode;
                    //    }
                    //    else
                    //    {
                    //        error_message = "扫描的条码不正确，请确认后扫描!";
                    //    }
                    //    ////不是退货条码查询交货记录
                    //    //else
                    //    //{
                    //    //    SD_DeliveryScan deliveryScan = _deliveryScanApp.GetDeliveryScanByBarCode(barCode);
                    //    //    result.Data = deliveryScan;
                    //    //}
                    //}
                    //else
                    //{
                    //    error_message = "该条码已退货扫描，不需要重复扫描";
                    //}
                }
                else
                {
                    error_message = "扫描的条码不正确，请确认后扫描!";
                }

                if (!string.IsNullOrEmpty(error_message))
                {
                    result.Message = error_message;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 获取条码明细（质检确认扫描使用）
        /// </summary>
        /// <param name="barCode"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetSDReturnBarCodeDetail([FromUri] string barCode)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                if (_baseApp.IsWmsBarCode(barCode)) //如果是条码
                {
                    //判断条码是否已退货扫描
                    //if (!_app.GetList(t => t.BarCode == barCode).Any())  //没有退货扫描
                    //{
                    //    SD_BarCodeReturn barcode = _barcodeApp.GetBarCodeInfo(barCode);
                    //    if (barcode == null)
                    //    {
                    //        result.Data = null;
                    //    }
                    //    else if ((bool)barcode.InspectionStatus)
                    //    {
                    //        error_message = "该条码已质检，不需要重复质检";
                    //    }
                    //    else
                    //    {
                    //        result.Data = barcode;
                    //    }
                    //}
                    //else
                    //{
                    //    error_message = "该条码已退货扫描，不需要重复扫描";
                    //}
                }
                else
                {
                    error_message = "扫描的条码不正确，请确认后扫描!";
                }

                if (!string.IsNullOrEmpty(error_message))
                {
                    result.Message = error_message;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 过账

        /// <summary>
        /// 过账
        /// </summary>
        /// <param name="details"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult DoPost([FromBody] List<SD_ReturnScan> details)
        {

            var result = new ResponseData();
            try
            {
                Sys_User sysUser = GetCurrentUser();
                string error_message = "";
                bool postResult = _app.DoPost(details, sysUser.LoginAccount, out error_message);
                if (!postResult)//过账失败==2000，前台处理过账失败后重新刷新界面信息
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2010;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2000;
                }
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }

        #endregion

        #region 导出

        /// <summary>
        /// 
        /// </summary>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] string keyword, [FromUri] DateTime[] dateTimes, [FromUri]bool? isPosted)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateTimes);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];
                var itemsData = _app.GetList().Where(t => (
                        string.IsNullOrEmpty(keyword) || t.DocNum.Contains(keyword)
                        || t.BaseNum.Contains(keyword) || t.ItemCode.Contains(keyword)
                        || t.ItemName.Contains(keyword)
                        || t.CustomerName.Contains(keyword) || t.CustomerCode.Contains(keyword) || t.BatchNum.Contains(keyword)
                        || t.PostUser.Contains(keyword)
                        || t.CUser.Contains(keyword)
                        )
                        && (t.CTime >= fromTime && t.CTime <= toTime)
                        && (isPosted == null || t.IsPosted == isPosted)).ToList();
                //var itemsData = _app.GetList().ToList();
                List<ExcelColumn<SD_ReturnScan>> columns = ExcelService.FetchDefaultColumnList<SD_ReturnScan>();
                string[] ignoreField = new string[]
                {
                    "ReturnScanID", "Line",
                    "BaseEntry", "BatchNum","ItmsGrpCode","ItmsGrpName",
                    "IsDelete", "DTime", "DUser",
                    "Remark","MUser","MTime",
                };

                List<ExcelColumn<SD_ReturnScan>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<SD_ReturnScan> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                columns.ForEach((column) =>
                {
                    if (column.ColumnName == "IsPosted")
                    {
                        column.Formattor = ExcelExportFormatter.IsOrNoFormatterForBoolean;
                    }
                });

                return ExportToExcelFile(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }
        #endregion

        #endregion

        #region Mobile

        #region 查询销售订单退料类型

        /// <summary>
        /// 查询销售订单退料类型
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IHttpActionResult GetXZ_SAP_VBAKAll([FromUri] string BaseNum)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _xzsapApp.GetSDReturn(x=>string.IsNullOrEmpty(BaseNum) || x.VBELN.Contains(BaseNum)).ToList();
                if (itemsData != null && itemsData.Count > 0)
                {
                    result.Data = itemsData;
                    result.Code = (int)WMSStatusCode.Success;
                }
                else
                {
                    result.Message = "退货订单["+BaseNum+"]暂未查询到信息";
                    result.Code = (int)WMSStatusCode.Success;
                }
               
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 根据序列号查询销售交货和库存

        /// <summary>
        /// 根据序列号查询销售交货和库存
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IHttpActionResult GetDeliveryScanAndStockForBarCode([FromUri]string BarCode, [FromUri]string BaseNum)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _deliveryscanApp.GetList(x=>x.BarCode== BarCode).ToList().FirstOrDefault();
                if (itemsData == null)
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = "未查询到交货信息，请检查";
                }
                else
                {
                    var SAPVBAK = _xzsapApp.GetSDReturn(x =>  x.MATNR == itemsData.ItemCode || x.VBELN == BaseNum).ToList();
                    if (SAPVBAK == null)
                    {
                        result.Code = (int)WMSStatusCode.Success;
                        result.Message = "物料信息未包含在此退货订单内，请检查";
                    }
                    else
                    {
                        result.Data = itemsData;
                        result.Code = (int)WMSStatusCode.Success;
                    }
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 保存

        /// <summary>
        /// 保存
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Save([FromBody] SD_ReturnScanParameters Parameters)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                string type = ""; //类型 1:提交失败 2：过账失败
                Sys_User currentUser = GetCurrentUser();
                //库位限制校验
                //if (new MD_BinLimitApp().ValidateBinLocationLimit("300003", entities.Select(x => x.InBinLocationCode).ToArray(), out error_message) == false)
                //{
                //    throw new Exception("Common.error", new Exception(error_message));
                //}
                bool bSubmit = _app.Save(Parameters.entities, Parameters.ManualPostTime,currentUser.LoginAccount, out error_message,out type);
                if (!bSubmit && type == "1")
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
                else if (!bSubmit && type == "2")
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }
        
        #endregion
        
        #endregion

    }
}

