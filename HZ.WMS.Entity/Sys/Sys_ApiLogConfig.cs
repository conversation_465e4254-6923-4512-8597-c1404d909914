using System.ComponentModel;
using SqlSugar;
//Chloe框架


namespace HZ.WMS.Entity.Sys
{
	/// <summary>
    /// 
    /// </summary>
    [SugarTable("Sys_ApiLogConfig")]
    public class Sys_ApiLogConfig:BaseEntity
    {
                /// <summary>
        /// API配置ID
        /// </summary>
        [Description("API配置ID")]
        [SugarColumn(IsPrimaryKey = true)]
        public string ApiLogConfigID {get;set;}
        /// <summary>
        /// API相对路径
        /// </summary>
        [Description("API相对路径")]
        public string ApiUrl {get;set;}
        /// <summary>
        /// API分类
        /// </summary>
        [Description("API分类")]
        public int? ApiType {get;set;}
        /// <summary>
        /// 请求支持方式
        /// </summary>
        [Description("请求支持方式")]
        public string RequestSupportType {get;set;}
        /// <summary>
        /// 所属模块
        /// </summary>
        [Description("所属模块")]
        public string BelongModule {get;set;}
        /// <summary>
        /// 功能描述
        /// </summary>
        [Description("功能描述")]
        public string ApiDescription {get;set;}

    }
}

