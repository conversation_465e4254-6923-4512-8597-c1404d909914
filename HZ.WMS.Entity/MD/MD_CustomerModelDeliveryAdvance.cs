using SqlSugar;
using System.ComponentModel;

namespace HZ.WMS.Entity.MD
{
    /// <summary>
    /// 物料主数据
    /// </summary>
    [SugarTable("MD_CustomerModelDeliveryAdvance")]
    public class MD_CustomerModelDeliveryAdvance : BaseEntity
    {

        /// <summary>
        /// Id
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Description("Id")]
        public string Id { get; set; }

        /// <summary>
        /// 客户名称
        /// </summary>
        [Description("客户名称")]
        public string CustomerName { get; set; }

        /// <summary>
        /// 客户代码
        /// </summary>
        [Description("客户代码")]
        public string CustomerCode { get; set; }

        /// <summary>
        /// 生产序号
        /// </summary>
        [Description("生产序号")]
        public string ProduceModel { get; set; }

        /// <summary>
        /// 提前天数
        /// </summary>
        [Description("提前天数")]
        public int AdvanceDays { get; set; }

    }
}