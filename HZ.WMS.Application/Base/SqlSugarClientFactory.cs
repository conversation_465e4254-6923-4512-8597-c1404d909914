using System;
using System.Collections.Concurrent;
using System.Configuration;
using HZ.Core.Security;
using SqlSugar;

namespace HZ.WMS.Application
{
    /// <summary>
    /// SqlSugarClient 工厂类 - 确保类型兼容性和线程安全
    /// </summary>
    public static class SqlSugarClientFactory
    {
        /// <summary>
        /// 获取 SqlSugarClient 实例 - 每次创建新的实例以避免并发问题
        /// </summary>
        /// <param name="dbKey">数据库连接键</param>
        /// <returns></returns>
        public static SqlSugarClient GetClient(string dbKey)
        {
            // 每次都创建新的客户端实例，避免连接状态问题
            return CreateClient(dbKey);
        }

        /// <summary>
        /// 创建 SqlSugarClient 实例
        /// </summary>
        /// <param name="dbKey"></param>
        /// <returns></returns>
        private static SqlSugarClient CreateClient(string dbKey)
        {
            try
            {
                // 使用优化的连接字符串
                var connectionString = ConnectionStringBuilder.BuildOptimizedConnectionString(dbKey);
                var dbType = GetDbType(dbKey);

                var config = new ConnectionConfig()
                {
                    ConnectionString = connectionString,
                    DbType = dbType,
                    IsAutoCloseConnection = true, // 修改为 true，确保连接自动关闭
                    InitKeyType = InitKeyType.Attribute,
                    // 连接池配置优化
                    MoreSettings = new ConnMoreSettings
                    {
                        IsAutoRemoveDataCache = true,
                        IsAutoToUpper = false,
                        // 启用连接池优化
                        IsWithNoLockQuery = true,
                        // 设置缓存时间
                        DefaultCacheDurationInSeconds = 300 // 减少缓存时间
                    }
                };

                var client = new SqlSugarClient(config);

                // AOP配置
                client.Aop.OnLogExecuted = (sql, pars) =>
                {
                    // 日志记录逻辑
                    var isQuery = IsQuerySql(sql);
                    var sqlTime = client.Ado.SqlExecutionTime;

                    if (!isQuery)
                    {
                        var nativeSql = UtilMethods.GetNativeSql(sql, pars);
                        HZ.Core.Logging.LogHelper.Instance.LogInfo($"数据操作记录 - 用时：{sqlTime.Milliseconds} ms\nSQL：{nativeSql}");
                    }
                    else if (sqlTime.Seconds > 10)
                    {
                        var nativeSql = UtilMethods.GetNativeSql(sql, pars);
                        HZ.Core.Logging.LogHelper.Instance.LogDebug($"慢查询记录 - 用时：{sqlTime.Milliseconds} ms\nSQL：{nativeSql}");
                    }
                };

                // 连接异常处理
                client.Aop.OnError = (exp) =>
                {
                    // 记录连接相关异常
                    if (exp.Message.Contains("timeout") || exp.Message.Contains("pool") ||
                        exp.Message.Contains("ExecuteReader") || exp.Message.Contains("Connection"))
                    {
                        HZ.Core.Logging.LogHelper.Instance.LogError($"{dbKey} 数据库操作异常: {exp.Message}");
                    }
                    else
                    {
                        HZ.Core.Logging.LogHelper.Instance.LogError($"数据库操作异常: {exp.Message}");
                    }
                };

                // 设置超时时间 - 减少到5分钟
                client.Ado.CommandTimeOut = 300;

                return client;
            }
            catch (Exception ex)
            {
                // HZ.Core.Logging.LogHelper.Instance.LogConnectionPoolError(dbKey, "创建数据库客户端", ex);
                throw;
            }
        }

        /// <summary>
        /// 获取连接字符串
        /// </summary>
        /// <param name="dbKey"></param>
        /// <returns></returns>
        private static string GetConnectionString(string dbKey)
        {
            if (dbKey == "DbConnectionForOracle")
            {
                return ConfigurationManager.ConnectionStrings[dbKey].ConnectionString;
            }
            else
            {
                return DES.Decrypt(ConfigurationManager.ConnectionStrings[dbKey].ConnectionString);
            }
        }

        /// <summary>
        /// 获取数据库类型
        /// </summary>
        /// <param name="dbKey"></param>
        /// <returns></returns>
        private static DbType GetDbType(string dbKey)
        {
            return dbKey == "DbConnectionForOracle" ? DbType.Oracle : DbType.SqlServer;
        }

        /// <summary>
        /// 判断是否为查询SQL
        /// </summary>
        /// <param name="sql"></param>
        /// <returns></returns>
        private static bool IsQuerySql(string sql)
        {
            string result = sql.Trim();
            string[] queryKeywords = { "SELECT", "SHOW", "DESCRIBE", "EXPLAIN" };

            foreach (var keyword in queryKeywords)
            {
                if (result.StartsWith(keyword, StringComparison.OrdinalIgnoreCase))
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// 释放所有客户端 - 由于每次都创建新实例，这里主要用于清理
        /// </summary>
        public static void DisposeAll()
        {
            // 由于每次都创建新的客户端实例，这里主要记录日志
            HZ.Core.Logging.LogHelper.Instance.LogInfo("清理数据库客户端工厂");
        }

        /// <summary>
        /// 获取客户端池状态
        /// </summary>
        /// <returns></returns>
        public static string GetPoolStatus()
        {
            return "使用动态创建模式，每次创建新的客户端实例";
        }
    }
}
