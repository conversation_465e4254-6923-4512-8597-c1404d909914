using SqlSugar;
using System;
using System.ComponentModel;

namespace HZ.WMS.Entity.MD
{
    /// <summary>
    /// 基础规格表
    /// </summary>
    [SugarTable("MD_BasicSpecification")]
    public class MD_BasicSpecification : BaseEntity
    {
        /// <summary>
        /// Id
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Description("Id")]
        public string Id { get; set; }

        /// <summary>
        /// 系列型号
        /// </summary>
        [Description("系列型号")]
        public string SeriesModel { get; set; }

        /// <summary>
        /// 能效备案型号
        /// </summary>
        [Description("能效备案型号")]
        public string EnergyEfficiencyModel { get; set; }

        /// <summary>
        /// Forvorda件号
        /// </summary>
        [Description("Forvorda件号")]
        public string ForvordaPartNo { get; set; }

        /// <summary>
        /// Forvorda产品型号
        /// </summary>
        [Description("Forvorda产品型号")]
        public string ForvordaProductModel { get; set; }

        /// <summary>
        /// SAP件号
        /// </summary>
        [Description("SAP件号")]
        public string SAPPartNo { get; set; }

        /// <summary>
        /// SAP产品型号
        /// </summary>
        [Description("SAP产品型号")]
        public string SAPProductModel { get; set; }

        /// <summary>
        /// 曳引比
        /// </summary>
        [Description("曳引比")]
        public string TractionRatio { get; set; }

        /// <summary>
        /// 额定载重
        /// </summary>
        [Description("额定载重")]
        public string RatedLoad { get; set; }

        /// <summary>
        /// 额定速度
        /// </summary>
        [Description("额定速度")]
        public string RatedSpeed { get; set; }

        /// <summary>
        /// 额定转速
        /// </summary>
        [Description("额定转速")]
        public string RatedRotationSpeed { get; set; }

        /// <summary>
        /// 额定转矩
        /// </summary>
        [Description("额定转矩")]
        public string RatedTorque { get; set; }

        /// <summary>
        /// 许用径向载荷
        /// </summary>
        [Description("许用径向载荷")]
        public string AllowableRadialLoad { get; set; }

        /// <summary>
        /// 主机重量
        /// </summary>
        [Description("主机重量")]
        public string HostWeight { get; set; }

        /// <summary>
        /// 额定功率
        /// </summary>
        [Description("额定功率")]
        public string RatedPower { get; set; }

        /// <summary>
        /// 额定电压
        /// </summary>
        [Description("额定电压")]
        public string RatedVoltage { get; set; }

        /// <summary>
        /// 额定电流
        /// </summary>
        [Description("额定电流")]
        public string RatedCurrent { get; set; }

        /// <summary>
        /// 相电阻
        /// </summary>
        [Description("相电阻")]
        public string PhaseResistance { get; set; }

        /// <summary>
        /// 电感
        /// </summary>
        [Description("电感")]
        public string Inductance { get; set; }

        /// <summary>
        /// 极数
        /// </summary>
        [Description("极数")]
        public string PoleNumber { get; set; }

        /// <summary>
        /// 额定频率
        /// </summary>
        [Description("额定频率")]
        public string RatedFrequency { get; set; }

        /// <summary>
        /// 效率
        /// </summary>
        [Description("效率")]
        public string Efficiency { get; set; }

        /// <summary>
        /// 防护等级
        /// </summary>
        [Description("防护等级")]
        public string ProtectionLevel { get; set; }

        /// <summary>
        /// 启动次数
        /// </summary>
        [Description("启动次数")]
        public string StartupTimes { get; set; }

        /// <summary>
        /// 极对数
        /// </summary>
        [Description("极对数")]
        public string PolePairs { get; set; }

        /// <summary>
        /// 定子电阻
        /// </summary>
        [Description("定子电阻")]
        public string StatorResistance { get; set; }

        /// <summary>
        /// 定子电抗
        /// </summary>
        [Description("定子电抗")]
        public string StatorReactance { get; set; }

        /// <summary>
        /// 反电动势
        /// </summary>
        [Description("反电动势")]
        public string BackEMF { get; set; }

        /// <summary>
        /// 型号
        /// </summary>
        [Description("型号")]
        public string Model { get; set; }

        /// <summary>
        /// 电流
        /// </summary>
        [Description("电流")]
        public string Current { get; set; }

        /// <summary>
        /// 电压
        /// </summary>
        [Description("电压")]
        public string Voltage { get; set; }

        /// <summary>
        /// 制动力矩
        /// </summary>
        [Description("制动力矩")]
        public string BrakingTorque { get; set; }

        /// <summary>
        /// 节径
        /// </summary>
        [Description("节径")]
        public string PitchDiameter { get; set; }

        /// <summary>
        /// 绳槽
        /// </summary>
        [Description("绳槽")]
        public string RopeGroove { get; set; }

        /// <summary>
        /// β角
        /// </summary>
        [Description("β角")]
        public string BetaAngle { get; set; }

        /// <summary>
        /// γ角
        /// </summary>
        [Description("γ角")]
        public string GammaAngle { get; set; }

        /// <summary>
        /// 槽距
        /// </summary>
        [Description("槽距")]
        public string GrooveDistance { get; set; }

        /// <summary>
        /// 编码器
        /// </summary>
        [Description("编码器")]
        public string Encoder { get; set; }

        /// <summary>
        /// 盘车装置/手动松闸扳手
        /// </summary>
        [Description("盘车装置/手动松闸扳手")]
        public string ManualDevice { get; set; }

        /// <summary>
        /// 远程手动松闸装置
        /// </summary>
        [Description("远程手动松闸装置")]
        public string RemoteManualDevice { get; set; }

        /// <summary>
        /// 装箱尺寸
        /// </summary>
        [Description("装箱尺寸")]
        public string PackingSize { get; set; }

        /// <summary>
        /// 装箱毛重
        /// </summary>
        [Description("装箱毛重")]
        public string PackingGrossWeight { get; set; }

        /// <summary>
        /// 装箱净重
        /// </summary>
        [Description("装箱净重")]
        public string PackingNetWeight { get; set; }
    }
}
