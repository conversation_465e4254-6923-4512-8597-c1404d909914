using System;
using System.Collections.Generic;
using System.ComponentModel;
using HZ.WMS.Entity;
using SqlSugar;

namespace AOS.OMS.Entity.Sale
{
    /// <summary>
    /// 订单类
    /// </summary>
    public class SD_Cable_Sale_PartOrderInfo : BaseEntity
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Description("主键ID")]
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 订单编号
        /// </summary>
        [Description("订单编号")]
        public string OrderNum { get; set; }

        /// <summary>
        /// 客户编号
        /// </summary>
        [Description("客户编号")]
        public string CustomerCode { get; set; }

        /// <summary>
        /// 客户名称
        /// </summary>
        [Description("客户名称")]
        public string CustomerName { get; set; }

        /// <summary>
        /// 客户地址
        /// </summary>
        [Description("客户地址")]
        public string CustomerAddress { get; set; }

        /// <summary>
        /// 客户结算地址
        /// </summary>
        [Description("客户结算地址")]
        public string SettlementAdd { get; set; }

        /// <summary>
        /// 订单状态
        /// </summary>
        [Description("订单状态")]
        public int Status { get; set; }

        /// <summary>
        /// 发运状态
        /// </summary>
        [Description("发运状态")]
        public int ShipmentStatus { get; set; }

        /// <summary>
        /// 订单类型(1:主机订单 2:部件订单 3:售后部件 4:售后主机)
        /// </summary>
        [Description("订单类型")]
        public int OrderType { get; set; }

        /// <summary>
        /// 订单日期
        /// </summary>
        [Description("订单日期")]
        public DateTime? OrderDate { get; set; }

        /// <summary>
        /// 交货日期
        /// </summary>
        [Description("交货日期")]
        public DateTime? DeliveryDate { get; set; }

        /// <summary>
        /// 过账用户
        /// </summary>
        [Description("过账用户")]
        public string PostUser { get; set; }

        /// <summary>
        /// 过账日期
        /// </summary>
        [Description("过账日期")]
        public DateTime? PostDate { get; set; }

        /// <summary>
        /// 过账消息
        /// </summary>
        [Description("过账消息")]
        public string PostMessage { get; set; }

        /// <summary>
        /// 过账状态
        /// </summary>
        [Description("过账状态")]
        public int? PostStatus { get; set; }

        /// <summary>
        /// 同步SAP销售单号
        /// </summary>
        [Description("同步SAP销售单号")]
        public string SapNo { get; set; }

        /// <summary>
        /// 合同号
        /// </summary>
        [Description("合同号")]
        public string ContractNo { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        [Description("项目名称")]
        public string ProjectDisc { get; set; }

        /// <summary>
        /// 履行备注
        /// </summary>
        [Description("履行备注")]
        public string PerformRemark { get; set; }

        /// <summary>
        /// 生产备注
        /// </summary>
        [Description("生产备注")]
        public string ProductionRemark { get; set; }

        /// <summary>
        /// 发运日期
        /// </summary>
        [Description("发运日期")]
        public DateTime? ShipmentDate { get; set; }

        /// <summary>
        /// 采购订单号
        /// </summary>
        [Description("采购订单号")]
        public string CustomerOrderNum { get; set; }

        /// <summary>
        /// 货币
        /// </summary>
        [Description("货币")]
        public string HuoBi { get; set; }

        /// <summary>
        /// 不含税价格
        /// </summary>
        [Description("不含税价格")]
        public decimal? NotTaxRatePrice { get; set; }

        /// <summary>
        /// 含税价格
        /// </summary>
        [Description("含税价格")]
        public decimal? TaxRatePrice { get; set; }

        /// <summary>
        /// 完工批次
        /// </summary>
        [Description("完工批次")]
        public string FinishBatch { get; set; }

        /// <summary>
        /// 暂估运费
        /// </summary>
        [Description("暂估运费")]
        public decimal? Freight { get; set; }

        /// <summary>
        /// 催货日期
        /// </summary>
        [Description("催货日期")]
        public DateTime? ExpeditingDate { get; set; }

        /// <summary>
        /// 排产日期
        /// </summary>
        [Description("排产日期")]
        public DateTime? ProductionDate { get; set; }

        /// <summary>
        /// 排产序号
        /// </summary>
        [Description("排产序号")]
        public string ProductionNo { get; set; }

        /// <summary>
        /// 发运单号
        /// </summary>
        [Description("发运单号")]
        public string ShipmentNum { get; set; }

        /// <summary>
        /// 设置发运时间
        /// </summary>
        [Description("设置发运时间")]
        public DateTime? SetShipmentDate { get; set; }

        /// <summary>
        /// 发运下载标记
        /// </summary>
        [Description("发运下载标记")]
        public int ShipmentDownFlag { get; set; }

        /// <summary>
        /// 设置发运用户
        /// </summary>
        [Description("设置发运用户")]
        public string SetShipmentUser { get; set; }


        /// <summary>
        /// 订单类型名称
        /// </summary>
        [Description("订单类型名称")]
        public string OrderTypeName { get; set; }

        /// <summary>
        /// 完工日期
        /// </summary>
        [Description("完工日期")]
        public DateTime? FinishDate { get; set; }

        /// <summary>
        /// 电扶货梯
        /// </summary>
        [Description("电扶货梯")]
        public string ElectricCargoElevator { get; set; }

        /// <summary>
        /// 工地模块
        /// </summary>
        [Description("工地模块")]
        public string ConstructionModule { get; set; }

        /// <summary>
        /// 顺序号
        /// </summary>
        [Description("顺序号")]
        public string SequenceNo { get; set; }

        /// <summary>
        /// 装箱标记
        /// </summary>
        [Description("装箱标记")]
        public int? PackingFlag { get; set; }
        
        /// <summary>
        /// 明细数
        /// </summary>
        [Description("明细数")]
        public int? DetailNo { get; set; }

        /// <summary>
        /// 订单明细
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public List<SD_Cable_Sale_PartOrderDetails> OrderDetailList { get; set; }
    }
}