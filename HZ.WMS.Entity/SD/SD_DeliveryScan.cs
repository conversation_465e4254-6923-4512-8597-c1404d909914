using System;
using System.ComponentModel;
using SqlSugar;

namespace HZ.WMS.Entity.SD
{
    /// <summary>
    /// 销售交货
    /// </summary>
    [SugarTable("SD_DeliveryScan")]
    public class SD_DeliveryScan : BaseEntity
    {
        /// <summary> 
        /// 销售交货ID
        /// </summary> 
        [Description("销售交货ID")]
        [SugarColumn(IsPrimaryKey = true)]
        public string DeliveryScanID { get; set; }

        /// <summary> 
        /// wms单号
        /// </summary> 
        [Description("交货单号")]
        public string DocNum { get; set; }

        /// <summary> 
        /// wms行号
        /// </summary> 
        [Description("wms行号")]
        public int? Line { get; set; }

        /// <summary> 
        /// 发运计划单号
        /// </summary> 
        [Description("发运计划单号")]
        public string ShippingPlanDocNum { get; set; }

        /// <summary> 
        /// 销售订单编号
        /// </summary> 
        [Description("销售订单编号")]
        public string BaseEntry { get; set; }

        /// <summary> 
        /// 客户订单号
        /// </summary> 
        [Description("客户订单号")]
        public string CustomerOrderNum { get; set; }


        /// <summary> 
        /// 销售订单号
        /// </summary> 
        [Description("销售订单号")]
        public string BaseNum { get; set; }

        /// <summary> 
        /// 销售单行号
        /// </summary> 
        [Description("销售单行号")]
        public int? BaseLine { get; set; }

        /// <summary>
        /// 销售订单类型
        /// </summary>
        [Description("销售订单类型")]
        public string BaseType { get; set; }

        /// <summary> 
        /// 销售组织
        /// </summary> 
        [Description("销售组织")]
        public string SalesOrganization { get; set; }

        /// <summary> 
        /// 交货日期
        /// </summary> 
        [Description("交货日期")]
        public DateTime? DeliveryDate { get; set; }

        /// <summary> 
        /// 客户编号
        /// </summary> 
        [Description("客户编号")]
        public string CustomerCode { get; set; }

        /// <summary> 
        /// 客户名称
        /// </summary> 
        [Description("客户名称")]
        public string CustomerName { get; set; }

        /// <summary>
        /// 客户地址
        /// </summary>
        [Description("客户地址")]
        public string CustomerAdd { get; set; }

        /// <summary> 
        /// 批次
        /// </summary> 
        [Description("批次")]
        public string BatchNum { get; set; }

        /// <summary>
        /// 序列号
        /// </summary>
        [Description("序列号")]
        public string BarCode { get; set; }


        /// <summary> 
        /// 物料编号
        /// </summary> 
        [Description("物料编号")]
        public string ItemCode { get; set; }

        /// <summary> 
        /// 物料名称
        /// </summary> 
        [Description("物料名称")]
        public string ItemName { get; set; }


        /// <summary> 
        /// 物料组编号
        /// </summary> 
        [Description("物料组编号")]
        public string ItmsGrpCode { get; set; }

        /// <summary> 
        /// 物料组名称
        /// </summary> 
        [Description("物料组名称")]
        public string ItmsGrpName { get; set; }

        /// <summary> 
        /// 交货数量
        /// </summary> 
        [Description("交货数量")]
        public decimal? DeliveryScanQty { get; set; }

        /// <summary> 
        /// 库存单位
        /// </summary> 
        [Description("库存单位")]
        public string Unit { get; set; }

        /// <summary> 
        /// 仓库编号
        /// </summary> 
        [Description("仓库编号")]
        public string WhsCode { get; set; }

        /// <summary> 
        /// 仓库名称
        /// </summary> 
        [Description("仓库名称")]
        public string WhsName { get; set; }

        /// <summary> 
        /// 区域编号
        /// </summary> 
        [Description("区域编号")]
        public string RegionCode { get; set; }

        /// <summary> 
        /// 区域名称
        /// </summary> 
        [Description("区域名称")]
        public string RegionName { get; set; }

        /// <summary> 
        /// 库位编号
        /// </summary> 
        [Description("库位编号")]
        public string BinLocationCode { get; set; }

        /// <summary> 
        /// 库位名称
        /// </summary> 
        [Description("库位名称")]
        public string BinLocationName { get; set; }

        /// <summary> 
        /// 是否已装箱单
        /// </summary> 
        [Description("是否已装箱单")]
        public bool? IsDelivery { get; set; }

        /// <summary> 
        /// 过账时间
        /// </summary> 
        [Description("过账时间")]
        public DateTime? ManualPostTime { get; set; }

        /// <summary> 
        /// 是否已过账
        /// </summary> 
        [Description("是否已过账")]
        public bool? IsPosted { get; set; }

        /// <summary> 
        /// 过账人
        /// </summary> 
        [Description("过账人")]
        public string PostUser { get; set; }

        /// <summary> 
        /// 过账时间
        /// </summary> 
        [Description("过账时间")]
        public DateTime? PostTime { get; set; }

        /// <summary>
        /// SAP物料凭证单号
        /// </summary>
        [Description("SAP物料凭证单号")]
        public string SapDocNum { get; set; }

        /// <summary>
        /// SAP物料凭证行号
        /// </summary>
        [Description("SAP物料凭证行号")]
        public int? SapLine { get; set; }

        /// <summary> 
        /// 项目类别
        /// </summary> 
        [Description("项目类别")]
        public string ProjectCategory { get; set; }

        /// <summary>
        /// 公司代码
        /// </summary>
        [Description("公司代码")]
        public string CompanyCode { get; set; }

        /// <summary>
        /// 工厂代码
        /// </summary>
        [Description("工厂代码")]
        public string FactoryCode { get; set; }

        /// <summary>
        /// 利润中心
        /// </summary>
        [Description("利润中心")]
        public string ProfitCenter { get; set; }

        /// <summary>
        /// 项目状态
        /// </summary>
        [Description("项目状态")]
        public string ProjectStatus { get; set; }


        /// <summary>
        /// 销售组织
        /// </summary>
        [Description("销售组织")]
        public string VKORG { get; set; }

        /// <summary>
        /// 装运点/收货点
        /// </summary>
        [Description("装运点/收货点")]
        public string VSTEL { get; set; }

        /// <summary>
        /// 参考单据的单据编号
        /// </summary>
        [Description("参考单据的单据编号")]
        public string VGBEL { get; set; }

        /// <summary>
        /// 参考项目的项目号
        /// </summary>
        [Description("参考项目的项目号")]
        public int? VGPOS { get; set; }

        /// <summary> 
        /// 项目
        /// </summary> 
        [Description("项目名称")]
        public string Project { get; set; }

        /// <summary> 
        /// 合同单号
        /// </summary> 
        [Description("合同单号")]
        public string CONT { get; set; }

        /// <summary>
        /// 结算地址
        /// </summary>
        [Description("结算地址")]
        public string SettlementAdd { get; set; }

        /// <summary>
        /// 物流供应商编号
        /// </summary>
        [Description("物流供应商编号")]
        public string SupplierCode { get; set; }

        /// <summary>
        /// 物流供应商名称
        /// </summary>
        [Description("物流供应商")]
        public string SupplierName { get; set; }


        /// <summary>
        /// SAP过账标示
        /// </summary>
        [Description("SAP过账标示")]
        public string SAPmark { get; set; }

        /// <summary>
        /// SAP过账信息
        /// </summary>
        [Description("SAP过账信息")]
        public string SAPmessage { get; set; }

        /// <summary>
        /// Eap出厂编号
        /// </summary>
        [Description("Eap出厂编号")]
        [SugarColumn(IsIgnore = true)]
        public string EapSerialNo { get; set; }

        /// <summary>
        /// 价格
        /// </summary>
        [Description("价格")]
        [SugarColumn(IsIgnore = true)]
        public decimal Price { get; set; }

        /// <summary>
        /// 创建人名称
        /// </summary>
        [Description("创建人名称")]
        [SugarColumn(IsIgnore = true)]
        public string CUserName { get; set; }

        /// <summary>
        /// 联系人
        /// </summary>
        [Description("联系人")]
        [SugarColumn(IsIgnore = true)]
        public string Contact { get; set; }

        /// <summary>
        /// 联系方式
        /// </summary>
        [Description("联系方式")]
        [SugarColumn(IsIgnore = true)]
        public string Telephone { get; set; }
    }
}