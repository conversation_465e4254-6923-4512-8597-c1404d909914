using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.PP.ViewModel
{
    /// <summary>
    /// 生产订单主表
    /// </summary>
    public class PP_ProductionOrder_View
    {
        /// <summary>
        /// 出厂编号 = 开始日期 + 4位流水号
        /// </summary>
        public string SerialNo { get; set; }
        /// <summary>
        /// 生产订单
        /// </summary>
        public string ProductionOrderNo { get; set; }
        /// <summary>
        /// 工厂
        /// </summary>
        public string FactoryCode { get; set; }
        /// <summary>
        /// 线体编码
        /// </summary>
        public string ProductionLineCode { get; set; }
        /// <summary>
        /// 生产批次
        /// </summary>
        public string ProductionBatch { get; set; }
        /// <summary>
        /// 生产批次交货开始时间
        /// </summary>
        public DateTime? BatchStartTime { get; set; }
        /// <summary>
        /// 生产批次交货结束时间
        /// </summary>
        public DateTime? BatchEndTime { get; set; }
        /// <summary>
        /// 生产排序
        /// </summary>
        public decimal? ProductionSequencing { get; set; }
        /// <summary>
        /// 物料编码
        /// </summary>
        public string MaterialNo { get; set; }
        /// <summary>
        /// 物料描述
        /// </summary>
        public string MaterialName { get; set; }
        /// <summary>
        /// 订单类型
        /// </summary>
        public string OrderType { get; set; }
        /// <summary>
        /// 生产调度员,101是主机，组件
        /// </summary>
        public string ProductionScheduler { get; set; }
        /// <summary>
        /// 生产调度员描述
        /// </summary>
        public string SchedulerDes { get; set; }
        /// <summary>
        /// 生产版本
        /// </summary>
        public string ProductionVersion { get; set; }
        /// <summary>
        /// MRP控制者
        /// </summary>
        public string MRPController { get; set; }
        /// <summary>
        /// MRP控制者描述
        /// </summary>
        public string MRPControllerDes { get; set; }
        /// <summary>
        /// 订单数量
        /// </summary>
        public decimal OrderQty { get; set; }
        /// <summary>
        /// 单位
        /// </summary>
        public string Unit { get; set; }
        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime? StartTime { get; set; }
        /// <summary>
        /// 完成日期
        /// </summary>
        public DateTime? EndTime { get; set; }
        /// <summary>
        /// 收货库存地点
        /// </summary>
        public string ReceivingLocation { get; set; }
        /// <summary>
        /// 采购申请
        /// </summary>
        public string PurchaseRequest { get; set; }
        /// <summary>
        /// 采购申请行项目
        /// </summary>
        public decimal? PurchaseRequestLineNo { get; set; }
        /// <summary>
        /// 订单状态
        /// </summary>
        public string OrderStatus { get; set; }
        /// <summary>
        /// 销售订单
        /// </summary>
        public string SalesOrderNo { get; set; }
        /// <summary>
        /// 销售订单行项目
        /// </summary>
        public decimal? SalesOrderLineNo { get; set; }
        /// <summary>
        /// 主机生产订单
        /// </summary>
        public string HostProductionOrderNo { get; set; }
        /// <summary>
        /// 评估类型，01自制02外购
        /// </summary>
        public string AssessmentType { get; set; }
        /// <summary>
        /// 合同号
        /// </summary>
        public string ContractNo { get; set; }
        /// <summary>
        /// 发货单位
        /// </summary>
        public string Shippers { get; set; }
        /// <summary>
        /// 线体描述
        /// </summary>
        public string ProductionLineDes { get; set; }
        /// <summary>
        /// 交货时间
        /// </summary>
        public DateTime? DeliveryTime { get; set; }
    }
}
