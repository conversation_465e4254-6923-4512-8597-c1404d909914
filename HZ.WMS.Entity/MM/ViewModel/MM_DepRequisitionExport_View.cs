using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.MM.ViewModel
{
    /// <summary>
    /// 部门领料导出视图
    /// </summary>
    public class MM_DepRequisitionExport_View : MM_DepReqDetailed
    {

        /// <summary>
        /// 办理人员编号
        /// </summary>
        [Description("办理人员编号")]
        public string HandlenCode { get; set; }

        /// <summary>
        /// 办理人员
        /// </summary>
        [Description("办理人员")]
        public string HandlenName { get; set; }

        /// <summary>
        /// 状态 0:未审核 1：审核中 2：已审核 3：已取消
        /// </summary>
        [Description("状态")]
        public string ZStatus { get; set; }
        /// <summary> 
        /// 是否作废
        /// </summary> 
        [Description("是否作废")]
        public bool? IsCancel { get; set; }

    }
    
}
