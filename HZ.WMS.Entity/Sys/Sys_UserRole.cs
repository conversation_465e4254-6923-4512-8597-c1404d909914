using System.ComponentModel;
using SqlSugar;
//Chloe框架


namespace HZ.WMS.Entity.Sys
{
	/// <summary>
    /// 
    /// </summary>
    [SugarTable("Sys_UserRole")]
    public class Sys_UserRole:BaseEntity
    {
                /// <summary>
        /// 用户角色ID
        /// </summary>
        [Description("用户角色ID")]
        [SugarColumn(IsPrimaryKey = true)]
        public string UserRoleID {get;set;}
        /// <summary>
        /// 角色ID
        /// </summary>
        [Description("角色ID")]
        public string RoleID {get;set;}
        /// <summary>
        /// 用户ID
        /// </summary>
        [Description("用户ID")]
        public string UserID {get;set;}

    }
}

