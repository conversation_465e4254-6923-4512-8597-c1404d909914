using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Linq.Expressions;
using AOS.Core.Utilities;
using AOS.OMS.Entity.Sale;
using HZ.Core.Http;
using HZ.WMS.Application.MD;
using HZ.WMS.Application.SAP;
using HZ.WMS.Application.Sys;
using HZ.WMS.Entity.MD.ViewModel;
using HZ.WMS.Entity.SAP;
using HZ.WMS.Entity.SAP.View;
using HZ.WMS.Entity.SD;
using HZ.WMS.Entity.SD.ViewModel;
using HZ.WMS.Entity.Sys;
using LinqKit;
using SqlSugar;
using DbType = System.Data.DbType;
using TreeNode = AOS.OMS.Entity.Sale.OrderTree.TreeNode;

namespace HZ.WMS.Application.SD
{
    /// <summary>
    /// 发运计划
    /// 没有out_message消息的函数，意味着只在最前端做未处理异常捕捉
    /// </summary>
    public class Sale_ShippingPlanApp : BaseApp<Sale_ShippingPlan>
    {

        #region 分页查询

        public object GetPageList(Pagination page, Sale_ShippingPlanListReq req)
        {
            if (string.IsNullOrEmpty(page.Sort))
            {
                page.Sort = "CTime desc";
            }
            else
            {
                page.Sort = page.Sort.Replace("ascending", "asc").Replace("descending", "desc");
            }
            var condition = GetTreeCondition(req);
            return GetPageList(page, condition).ToList();
        }

        #endregion

        #region 完成

        /// <summary>
        /// 完成
        /// </summary>
        /// <param name="DocNums">单号</param>
        /// <param name="opUser">当前用户</param>
        /// <param name="errorMessage">错误消息</param>
        public bool Finish(string[] DocNums, string opUser, out string errorMessage)
        {
            errorMessage = "";
            //1.手动更新发运计划，2.删除未交货数据。3.托运单需要处理，调用存储过程
            string DocNum = string.Join(",", DocNums.ToArray());
            var pm1 = new SugarParameter("@DocNum", DocNum);
            var outPm = new SugarParameter("@cout", null, DbType.UInt32,ParameterDirection.Output) ;
            DbContext.Ado.UseStoredProcedure().ExecuteCommand("PROC_UpdateConsignmentNoteByDocNum", pm1, outPm);
            
            //if (Convert.ToInt32(outPm.Value) > 0)
            return true;
            // else
            // return false;
        }

        #endregion

        #region 查询发运计划

        /// <summary>
        /// 查询发运计划
        /// </summary>
        /// <param name="InspectionNum">报检单号</param>
        /// <param name="errorMessage">错误消息</param>
        public List<SD_DeliveryScan_View> GetShippingPlanDetail(string DocNum, out string errorMessage)
        {
            errorMessage = "";
            var query = DbContext.Queryable<SD_DeliveryScan_View>().Where(x => x.DocNum == DocNum).ToList();
            if (query == null || query.Count <= 0)
            {
                errorMessage = "发运单号[" + DocNum + "]已发料完成！";
            }

            return query;
        }

        #endregion

        #region 查询导出数据

        /// <summary>
        /// 查询导出数据
        /// </summary>
        /// <param name="keyword">参数</param>
        /// <param name="fromTime">开始时间</param>
        /// <param name="toTime">结束时间</param>
        public List<Sale_ShippingPlan> GetExportInfo(Sale_ShippingPlanListReq req)
        {
            return GetList(GetCondition(req)).ToList();
        }

        #endregion

        #region 查询未发货的发运计划

        /// <summary>
        /// 查询未发货的发运计划
        /// </summary>
        /// <param name="DeliveryUser">用户</param>
        public List<SD_NoDeliveryShippingPlan_View> GetNoDeliveryShippingPlan(string DeliveryUser)
        {
            var query = DbContext.Queryable<SD_NoDeliveryShippingPlan_View>().Where(x => (string.IsNullOrEmpty(DeliveryUser) || x.DeliveryUser == DeliveryUser)).ToList();

            return query;
        }

        #endregion
        
        #region 获取树条件

        private Expression<Func<Sale_ShippingPlan, bool>> GetTreeCondition(Sale_ShippingPlanListReq req)
        {
            var condition = GetCondition(req);
            var conditionChild = PredicateBuilder.New<Sale_ShippingPlan>();
            if (req.TreeReqList != null && req.TreeReqList.Count > 0)
            {
                foreach (var treeReq in req.TreeReqList)
                {
                    if (treeReq.Level == 0)
                    {
                        conditionChild = conditionChild.Or(PredicateBuilder.New<Sale_ShippingPlan>(t =>
                            t.DeliveryDate >= DateUtil.GetStartTime(DateTime.Parse(treeReq.Value))
                            && t.DeliveryDate <= DateUtil.GetStartTime(DateTime.Parse(treeReq.Value))
                        ));
                    }
                    else if (treeReq.Level == 1)
                    {
                        var arr = treeReq.Value.Split('&');
                        string customerName = arr[1];
                        string deliveryDate = arr[0];
                        conditionChild = conditionChild.Or(PredicateBuilder.New<Sale_ShippingPlan>(t =>
                            t.DeliveryDate >= DateUtil.GetStartTime(DateTime.Parse(deliveryDate))
                            && t.DeliveryDate <= DateUtil.GetStartTime(DateTime.Parse(deliveryDate))
                            && t.CustomerName == customerName
                        ));
                    }
                    else if (treeReq.Level == 2)
                    {
                        conditionChild = conditionChild.Or(PredicateBuilder.New<Sale_ShippingPlan>(t =>
                            t.Id == treeReq.Value
                        ));
                    }
                }

                condition = condition.And(conditionChild);
            }

            return condition;
        }
        
        #endregion

        #region 获取条件

        private Expression<Func<Sale_ShippingPlan, bool>> GetCondition(Sale_ShippingPlanListReq req)
        {
            var searchCondition = PredicateBuilder.New<Sale_ShippingPlan>(x =>
                (req.OrderType == null || x.OrderType.Equals(req.OrderType))
                && (string.IsNullOrEmpty(req.Keyword) || x.DocNum.StartsWith(req.Keyword) ||
                    x.SaleSapNo.StartsWith(req.Keyword) || x.ItemCode.StartsWith(req.ItemCode) || x.ItemName.StartsWith(req.ItemName))
                && (string.IsNullOrEmpty(req.CustomerName) || x.CustomerName.StartsWith(req.CustomerName))
                && (string.IsNullOrEmpty(req.ContractNo) || x.ContractNo.StartsWith(req.ContractNo))
                && (string.IsNullOrEmpty(req.CustomerOrderNo) || x.CustomerOrderNo.StartsWith(req.CustomerOrderNo))
                && (string.IsNullOrEmpty(req.CUser) || x.CUser.StartsWith(req.CUser))
                && (req.Status == null || x.Status == req.Status)
                && (req.IsDeliveryImport == null || x.IsDeliveryImport == req.IsDeliveryImport)
                && (req.IsDownload == null || x.IsDownload == req.IsDownload)
            );
            
            // 日期处理
            if (req.DeliveryDate != null && req.DeliveryDate.Length > 1)
            {
                var startDate = req.DeliveryDate[0];
                var endDate = req.DeliveryDate[1];
                searchCondition = searchCondition.And(PredicateBuilder.New<Sale_ShippingPlan>(order =>
                    order.DeliveryDate >= DateUtil.GetStartTime(startDate) &&
                    order.DeliveryDate <= DateUtil.GetEndTime(endDate)));
            }

            if (req.CTime != null && req.CTime.Length > 1)
            {
                var startDate = req.CTime[0];
                var endDate = req.CTime[1];
                searchCondition = searchCondition.And(PredicateBuilder.New<Sale_ShippingPlan>(order =>
                    order.CTime >= DateUtil.GetStartTime(startDate) &&
                    order.CTime <= DateUtil.GetEndTime(endDate)));
            }

            return searchCondition;
        }

        #endregion

        #region 保存OMS同步信息

        public bool SaveForOMS(List<Sale_ShippingForOms> shippingList, out string errorMessage)
        {
            errorMessage = "";
            SD_CustomerAddViewApp _CustomerAddApp = new SD_CustomerAddViewApp();
            try
            {
                foreach (Sale_ShippingForOms x in shippingList)
                {
                    if (GetList(t => t.SaleSapNo == x.SaleSapNo && t.SaleSapLine == x.SaleSapLine).ToList().FirstOrDefault() != null)
                    {
                        errorMessage = "销售单号[" + x.SaleSapNo + "],销售行号[" + x.SaleSapLine + "]已存在发运计划信息，请勿重复生成";
                        return false;
                    }
                }
                string docNum = this.GetNewDocNum(DocType.SD, DocFixedNumDef.SD_ShippingPlan);
                DateTime date = DateTime.Now;

                var queryDetails = new List<Sale_ShippingPlan>();
                int docLine = 10;
                foreach (Sale_ShippingForOms x in shippingList)
                {
                    var y = new Sale_ShippingPlan();
                    y.CompanyCode = "001";
                    y.FactoryCode = "2002";
                    y.Pid = x.Pid;
                    y.DeliveryDate = x.PromisedDeliveryDate;
                    y.DocNum = docNum;
                    y.DocLine = docLine + 10;
                    y.IsDownload = 0;
                    y.Status = 0;
                    y.IsDelete = false;
                    y.CUser = x.CUser;
                    y.CTime = date;
                    y.SaleSapNo = x.SaleSapNo;
                    y.SaleSapLine = x.SaleSapLine;
                    y.SaleType = x.SaleType;
                    y.SaleOrganization = x.SaleOrganization;
                    y.VoucherDate = x.VoucherDate;
                    y.CustomerCode = x.CustomerCode;
                    y.CustomerName = x.CustomerName;
                    y.CustomerAdd = x.CustomerAdd;
                    y.BatchNum = x.BatchNum;
                    y.ItemCode = x.ItemCode;
                    y.ItemName = x.ItemName;
                    y.Quantity = x.Quantity;
                    y.ContractNo = x.ContractNo;
                    y.SettlementAdd = x.SettlementAdd;
                    y.CustomerOrderNo = x.CustomerOrderNo;
                    y.OrderType = x.OrderType;
                    y.ActualStockTime = null;
                    y.PlanStockTime = null;
                    y.ProjectName = x.ProjectName;
                    y.Remark = x.Remark;
                    y.ProductionRemark = x.ProductionRemark;
                    y.ShippingType = x.ShippingType;
                    y.CustomerItemCode = x.CustomerPart;
                    //根据客户地址查询结算地址等信息
                    if (string.IsNullOrEmpty(x.Contact) || string.IsNullOrEmpty(x.Telephone))
                    {
                        var customerAdd = _CustomerAddApp.GetList(t => t.CustomerAdd == y.CustomerAdd)?.ToList().FirstOrDefault();
                        if (customerAdd != null)
                        {
                            y.SettlementAdd = customerAdd.SettlementAdd;
                            y.Contact = customerAdd.Contact;
                            y.Telephone = customerAdd.Telephone;
                        }
                        else
                        {
                            y.SettlementAdd = "";
                            y.Contact = "";
                            y.Telephone = "";
                        }
                    }
                    else
                    {
                        y.Contact = x.Contact;
                        y.Telephone = x.Telephone;
                    }
                    //根据结算地址查询物流供应商信息
                    MD_FreightMileage_View Mileageinfo = this.DbContext.Queryable<MD_FreightMileage_View>().Where(T => T.SettlementAdd == y.SettlementAdd)?.ToList().FirstOrDefault();
                    if (Mileageinfo != null)
                    {
                        y.SupplierCode = Mileageinfo.SupplierCode;
                        y.SupplierName = Mileageinfo.SupplierName;
                        y.DeliveryAccount = Mileageinfo.UserCode;
                        y.DeliveryUserName = Mileageinfo.UserName;
                    }
                    var z = DbContext.Queryable<SAP_VBAK_View>().Where(t => t.VBELN == y.SaleSapNo && t.POSNR == y.SaleSapLine).ToList().FirstOrDefault();
                    if (z != null)
                    {
                        y.VoucherDate = z.ERDAT;
                        y.DistributionChannels = z.VTWEG;
                        y.ProductGroup = z.SPART;
                        y.ShippingReference = z.BSTNK;
                        y.ProjectCategory = z.PSTYV;
                        y.Unit = z.MEINS;
                        y.WhsCode = z.LGORT;
                        var whsname = DbContextForSAP.Queryable<XZ_SAP_T001L>().Where(t => t.LGORT == y.WhsCode).ToList().FirstOrDefault();
                        y.WhsName = whsname != null ? whsname.LGOBE :"";
                        y.ProfitCenter = z.PRCTR;
                        y.ShipmentPoint = z.VSTEL;
                        y.ReferenceDocNo = z.VGBEL;
                        y.ReferenceDocLine = z.VGPOS;
                    }
                    queryDetails.Add(y);
                }

                DbContext.Ado.BeginTran();
                
                //交运计划信息插入
                Insert(queryDetails);

                DbContext.Ado.CommitTran();

                return true;
            }
            catch (Exception ex)
            {
                if (DbContext.Ado.IsAnyTran())
                    DbContext.Ado.RollbackTran();
                errorMessage = ex.Message;
                return false;
            }
        }

        #endregion

        #region 获取统计树

        public List<TreeNode> GetTree(Sale_ShippingPlanListReq req, HostOrderTreeReq treeReq)
        {
            var condition = GetCondition(req);
            var resList = new List<TreeNode>();

            if (treeReq.Level == 0)
            {
                var countList = DbContext.Queryable<Sale_ShippingPlan>()
                    .Where(condition)
                    .Where(t => t.IsDelete == false)
                    .GroupBy(t => t.DeliveryDate) // 单字段分组无需匿名对象
                    .Select(t => new { 
                        t.DeliveryDate, 
                        TotalCount = SqlFunc.AggregateCount(1) // 正确写法
                    })
                    .ToList();
                foreach (var count in countList)
                {
                    TreeNode node = new TreeNode();
                    node.Label = DateUtil.ParseStr(count.DeliveryDate) + " (" + count.TotalCount + ")";
                    node.Value = DateUtil.ParseStr(count.DeliveryDate);
                    node.Leaf = count.TotalCount == 0;
                    node.Level = 0;
                    node.Branch = DateUtil.ParseStr(count.DeliveryDate);
                    resList.Add(node);
                }
            }
            else if (treeReq.Level == 1)
            {
                // 先解析日期参数（建议业务层处理）
                var targetDate = DateTime.Parse(treeReq.Value);
                var startTime = DateUtil.GetStartTime(targetDate);
                var endTime = DateUtil.GetEndTime(targetDate); // 修复结束时间应为 GetEndTime

                var countList = DbContext.Queryable<Sale_ShippingPlan>()
                    .Where(condition)
                    .Where(t => t.IsDelete == false 
                                && t.DeliveryDate >= startTime 
                                && t.DeliveryDate <= endTime)  // 使用预处理好的时间范围
                    .GroupBy(t => t.CustomerName)      // 单字段分组无需匿名对象
                    .Select(t => new {
                        t.CustomerName,
                        TotalCount = SqlFunc.AggregateCount(1)  // 正确统计写法
                    })
                    .ToList();
                foreach (var count in countList)
                {
                    TreeNode node = new TreeNode();
                    node.Label = count.CustomerName + " (" + count.TotalCount + ")";
                    node.Value = treeReq.Value + "&" + count.CustomerName;
                    node.Leaf = count.TotalCount == 0;
                    node.Level = 1;
                    node.Branch = treeReq.Value;
                    resList.Add(node);
                }
            }
            else if (treeReq.Level == 2)
            {
                var arr = treeReq.Value.Split('&');
                string customerName = arr[1];
                string deliveryDate = arr[0];
                var detailList = DbContext.Queryable<Sale_ShippingPlan>()
                    .Where(condition)
                    .Where(t => t.IsDelete == false
                                && t.CustomerName == customerName
                                && t.DeliveryDate >= DateUtil.GetStartTime(DateTime.Parse(deliveryDate))
                                && t.DeliveryDate <= DateUtil.GetStartTime(DateTime.Parse(deliveryDate))).ToList();
                foreach (var detail in detailList)
                {
                    TreeNode node = new TreeNode();
                    node.Label = detail.ContractNo;
                    node.Value = detail.Id;
                    node.Leaf = true;
                    node.Level = 2;
                    node.Branch = arr[0];
                    resList.Add(node);
                }
            }

            return resList;
        }

        #endregion

        #region 同步销售交货

        public bool SyncSaleDelivery(SyncSaleDeliveryReq req, Sys_User getCurrentUser, out string errorMessage)
        {
            errorMessage = "";
            try
            {
                var shippingPlanList = GetList(t => req.Ids.Contains(t.Id)).ToList();
                var itemCodes = shippingPlanList.Select(t => t.ItemCode).ToList();
                var itemCodeList = DbContextForSAP.Queryable<XZ_SAP_MARC>().Where(x => itemCodes.Contains(x.MATNR))?.ToList();
                var whsCodeList = new MD_BinLocationApp().GetList(x => x.WhsCode == req.WhsCode).ToList();
                foreach (var shippingPlan in shippingPlanList)
                {
                    shippingPlan.Status = 2;
                    shippingPlan.WhsCode = req.WhsCode;
                    // 匹配仓库
                    var whsCode = whsCodeList.Where(t => t.WhsCode == shippingPlan.WhsCode).FirstOrDefault();
                    shippingPlan.WhsName = whsCode == null ? "" : whsCode.WhsName;
                    shippingPlan.RegionCode = whsCode == null ? "" : whsCode.RegionCode;
                    shippingPlan.RegionName = whsCode == null ? "" : whsCode.RegionName;
                    shippingPlan.BinLocationCode = whsCode == null ? "" : whsCode.BinLocationCode;
                    shippingPlan.BinLocationName = whsCode == null ? "" : whsCode.BinLocationName;
                    // 匹配物料信息
                    var item = itemCodeList.Where(v => v.MATNR == shippingPlan.ItemCode)?.FirstOrDefault();
                    shippingPlan.ItemName = item == null ? "" : item.MAKTX;
                    shippingPlan.Unit = item == null ? "" : item.MEINS;
                    // 交货设置人信息
                    shippingPlan.DeliveryUserName = getCurrentUser.UserName;
                    shippingPlan.DeliveryAccount = getCurrentUser.LoginAccount;
                    shippingPlan.DeliverySetTime = DateTime.Now;
                }
                // 更新原订单信息
                var pidList = shippingPlanList.Select(t => t.Pid).ToList();
                var orderDetailList = DbContextForOMS.Queryable<SD_Host_OrderDetails>().Where(t => pidList.Contains(t.Id) && t.IsDelete == false).ToList();
                foreach (var orderDetail in orderDetailList)
                {
                    orderDetail.ShipmentStatus = 2;
                    DbContextForOMS.Updateable(orderDetail).ExecuteCommand();
                }
                // 更新发运信息
                Update(shippingPlanList);
                return true;
            }
            catch (Exception e)
            {
                errorMessage = e.Message;
                return false;
            }
        }

        #endregion
        
        #region 过账

        /// <summary>
        /// 过账
        /// 销售发货过账：
        ///    根据销售订单生成一个大的发货任务，根据WMS波次发货任务，分批确认
        ///    本过账只针对一个销售订单
        /// </summary>
        /// <param name="entities">页面箱子列表</param>
        /// <param name="user">过账用户</param>
        /// <param name="error_message"></param>
        /// <returns></returns>
        public bool DoPost(List<Sale_ShippingPlan> entities, string user, out string error_message)
        {
            MD_StockApp _stockApp = new MD_StockApp();
            Sys_SAPCompanyInfoApp _SAPCompanyInfoApp = new Sys_SAPCompanyInfoApp();
            error_message = "";
            try
            {
                //多条数据后根据单号进行分组
                var mainInfoList = entities.GroupBy(g => new
                {
                    g.DocNum,
                    g.SaleSapNo
                }).Select(q => new
                {
                    DocNum = q.Key.DocNum,
                    BaseNum = q.Key.SaleSapNo
                });

                DateTime time = DateTime.Now;
                //按单号分组
                foreach (var mainInfo in mainInfoList)
                {
                    ZFGWMS001 zfgwms = new ZFGWMS001();

                    int Line = 0;
                    List<Sale_ShippingPlan> conditionList = entities
                        .Where(x => x.DocNum == mainInfo.DocNum && x.SaleSapNo == mainInfo.BaseNum)?.ToList();

                    foreach (Sale_ShippingPlan condition in conditionList)
                    {
                        List<ZFGWMS001_1> zfgwms_1List = new List<ZFGWMS001_1>();
                        condition.SapLine = Line += 1;
                        ZFGWMS001_1 zfgwms_1 = new ZFGWMS001_1();

                        DateTime ManualPostTime = new SAPApp().GetSAPpostTime(Convert.ToDateTime(condition.ManualPostTime));

                        zfgwms.VBELN = condition.SapNo;
                        zfgwms.LFDAT = Convert.ToDateTime(Convert.ToDateTime(ManualPostTime).ToString("yyyy-MM-dd"));
                        ;
                        zfgwms.WAUHR = Convert.ToDateTime(Convert.ToDateTime(ManualPostTime).ToString("HH:mm:ss")); //待定

                        zfgwms_1.LGORT = condition.WhsCode;
                        zfgwms_1.LFIMG = condition.Quantity;
                        zfgwms_1.VGPOS = condition.SapLine;

                        zfgwms_1.ZJHBS = condition.DocNum + "-" + condition.SapLine;
                        zfgwms_1.ZEREMARK = condition.Remark;
                        zfgwms_1List.Add(zfgwms_1);

                        condition.SapLine = condition.SapLine ?? 0 + 1; //记录过账次数

                        bool ispost = false;
                        string rtnErrMsg = "";
                        //purchaseReceipt.CompanyCode 公司代码
                        //查询Sap账期时间
                        List<SAPRETURN> saplist = _SAPCompanyInfoApp.ZFGWMS001("001", zfgwms, ManualPostTime,
                            zfgwms_1List, out ispost, out error_message);
                        if (saplist != null && saplist.Count > 0)
                        {
                            foreach (SAPRETURN sap in saplist)
                            {
                                condition.IsPosted = true;
                                condition.PostUser = user;
                                condition.PostTime = time;
                                condition.MUser = user;
                                condition.MTime = time;
                                condition.SapNo = sap.sapDocNum;
                                condition.ManualPostTime = ManualPostTime;
                                condition.SapMark = "S";
                                condition.SapMessage = "";
                                Update(condition);
                            }
                        }
                        else
                        {
                            condition.SapMark = "E";
                            condition.SapMessage = error_message;
                            condition.MUser = user;
                            condition.MTime = DateTime.Now;
                            Update(condition);
                        }
                    }
                }

                if (string.IsNullOrEmpty(error_message))
                    error_message = "过账成功";
                return true;
            }
            catch (Exception ex)
            {
                error_message = ex.Message;
                return false;
            }
        }

        #endregion

        #region 冲销过账

        /// <summary>
        /// 取消过账
        /// </summary>
        /// <param name="entities">采购收货集合</param>
        /// <param name="opUser">当前过账用户</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool PassPost(string[] ids, string opUser, out string error_message)
        {
            error_message = "";

            Sys_SAPCompanyInfoApp _SAPCompanyInfoApp = new Sys_SAPCompanyInfoApp();
            var entities = GetList(x => ids.Contains(x.Id) && x.IsPosted == true).ToList();

            //多条数据后根据单号进行分组
            var mainInfoList = entities.GroupBy(g => new
            {
                g.SapNo
            }).Select(q => new
            {
                SapDocNum = q.Key.SapNo
            });

            //按订单号分组
            foreach (var mainInfo in mainInfoList)
            {
                var conditionList = entities.Where(x => x.SapNo == mainInfo.SapDocNum)?.ToList();

                try
                {
                    bool ispost = false;
                    DateTime time = DateTime.Now;

                    //查询Sap账期时间
                    DateTime ManualPostTime =
                        new SAPApp().GetSAPpostTime(Convert.ToDateTime(conditionList[0].ManualPostTime));
                    //purchaseReceipt.CompanyCode 公司代码
                    var saplist = _SAPCompanyInfoApp.ZFGWMS025("001", mainInfo.SapDocNum, ManualPostTime, out ispost,
                        out error_message);
                    if (string.IsNullOrEmpty(error_message))
                    {
                        foreach (var query in conditionList)
                        {
                            query.DUser = opUser;
                            query.DTime = time;
                            query.Status = 2;
                            query.SapMark = "已冲销";

                            //待完善，完善内容 事务，库存缺少销售订单信息。
                            if (Update(query) > 0) //更新成功后更新库存
                            {
                                //更新库存
                            }
                        }

                    }
                    else
                    {
                        error_message = "单号[" + conditionList[0].DocNum + "],冲销过账失败，失败原因：" + error_message;
                        return false;
                    }
                }
                catch (Exception ex)
                {
                    error_message = "单号[" + conditionList[0].DocNum + "],冲销过账失败，失败原因：" + ex.Message;
                    return false;
                }
            }

            if (string.IsNullOrEmpty(error_message))
            {
                error_message = "冲销过账成功";
            }

            return true;
        }

        #endregion
        
        #region 同步SRM

        /// <summary>
        /// 同步SRM
        /// </summary>
        /// <param name="entities">集合</param>
        /// <param name="user">用户</param>
        /// <param name="error_message">错误信息</param>
        /// <returns></returns>
        public bool UploadSRM(List<Sale_ShippingPlan> entities, string user, out string error_message)
        {
            error_message = "";
            //校验发运计划的状态是否为已同步托运单
            if (entities.Any(x => x.SrmStatus == 1))
            {
                error_message = "已同步的信息不允许重复同步";
                return false;
            }

            entities.ForEach(x => {
                x.SrmStatus = 1;
                x.MTime = DateTime.Now;
                x.MUser = user;
            });
       
            try
            {
                //DbContext.Session.BeginTransaction();
                //_consignmentApp.DbContext = this.DbContext;

                //更新
                //Update(entities);

                //DbContext.Session.CommitTransaction();
                SD_ConsignmentNoteApp _consignmentApp = new SD_ConsignmentNoteApp();
                foreach (var x in entities)
                {
                    var ids = entities.Select(t => t.Id).ToList();
                    bool noteDetail = _consignmentApp.AutoSaveNew(ids,x, user, out error_message);
                    if (!noteDetail || !string.IsNullOrEmpty(error_message))
                    {
                        return false;
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                error_message = ex.Message;
                return false;
            }
        }

        #endregion
    }
}