using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.SAP
{
    /// <summary>
    /// 资产卡片
    /// </summary>
    public class XZ_SAP_ANLA
    {
        /// <summary>
        /// 主资产号
        /// </summary>
        [Description("主资产号")]
        public string ANLN1 { get; set; }

        /// <summary>
        /// 资产描述
        /// </summary>
        [Description("资产描述")]
        public string TXT50 { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        [Description("状态")]
        public bool? Status { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [Description("创建时间")]
        public DateTime? CDate { get; set; }
    }
}
