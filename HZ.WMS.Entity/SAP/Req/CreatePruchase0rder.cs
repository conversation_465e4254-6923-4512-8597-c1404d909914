using System.ComponentModel;

namespace HZ.WMS.Entity.Produce.Req
{
    public class CreatePruchase0rder
    {
        
        [Description("消息类型")]
        public string ETYPE { get; set; }

        [Description("消息文本")]
        public string EMSG { get; set; }

        [Description("DATA_IN")]
        public Table001061[] DataIn { get; set; }

        [Description("DATA_OUT")]
        public Table001061[] DataOut { get; set; }

        public class Table001061
        {
            
            [Description("工厂")]
            public string WERKS { get; set; }

            [Description("销售订单号")]
            public string VBELN { get; set; }

            [Description("销售订单行")]
            public string POSNR { get; set; }

            [Description("排产日期")]
            public string LFDAT { get; set; }

            [Description("线体")]
            public string XT { get; set; }

            [Description("批次")]
            public string PC { get; set; }
            
            
        }
        
    }
}