using HZ.WMS.Entity.SD;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.Core.Office
{
    /// <summary>
    /// 
    /// </summary>
    public static class ExcelService
    {
        #region 获取Workbook

        /// <summary>
        /// 
        /// </summary>
        /// <param name="excelType"></param>
        /// <returns></returns>
        public static IWorkbook GetWorkbook(ExcelFileType excelType)
        {
            if (ExcelFileType.xls.Equals(excelType))
                return new HSSFWorkbook();
            else
                return new XSSFWorkbook();
        }

        #endregion

        #region 导出Excel

        public static MemoryStream ExportToExcelByGroup<S,T>(Dictionary<S, List<T>> list, ExcelFileType fileType, List<ExcelColumn<T>> columns = null)
        {
            //MemoryStream xlsFile = new MemoryStream();
            NPOIMemoryStream xlsFile = new NPOIMemoryStream();
            IWorkbook workbook = GetWorkbook(fileType);
            if(list == null || list.Count() < 1)
            {
                string sheetName = "Sheet1";
                ISheet sheet = workbook.CreateSheet(sheetName);
                columns = columns ?? ExcelSheet.FetchDefaultColumnList<T>();
                columns.ForEach((item) => {
                    item.HeadCellStyle = GetDefaultHeaderStyle(workbook);
                    item.DataCellStyle = GetDefaultDataStyle(workbook, item.DataType);
                });
                sheet.FillDataWithHeader(new List<T>(), columns);
                sheet.AutoColumnWidth();        // 根据内容自动计算列宽
            }
            else
            {
                int line = 1;
                foreach (var dic in list)
                {
                    string sheetName = "Sheet";
                    if (!string.IsNullOrEmpty(dic.Key?.ToString()))
                    {
                        sheetName = dic.Key?.ToString();
                    }
                    else
                    {
                        sheetName = $"{sheetName}{line}";
                        line++;
                    }
                    ISheet sheet = workbook.CreateSheet(sheetName);
                    columns = columns ?? ExcelSheet.FetchDefaultColumnList<T>();
                    columns.ForEach((item) => {
                        item.HeadCellStyle = GetDefaultHeaderStyle(workbook);
                        item.DataCellStyle = GetDefaultDataStyle(workbook, item.DataType);
                    });
                    sheet.FillDataWithHeader(dic.Value, columns);
                    sheet.AutoColumnWidth();        // 根据内容自动计算列宽
                }
            }
            workbook.Write(xlsFile);
            xlsFile.Seek(0, SeekOrigin.Begin);
            return xlsFile;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="list"></param>
        /// <param name="fileType"></param>
        /// <param name="sheetName"></param>
        /// <param name="columns"></param>
        /// <returns></returns>
        public static MemoryStream ExportToExcel<T>(List<T> list, ExcelFileType fileType, string sheetName = "Sheet1", List<ExcelColumn<T>> columns = null)
        {
            NPOIMemoryStream xlsFile = new NPOIMemoryStream();
            IWorkbook workbook = GetWorkbook(fileType);
            ISheet sheet = workbook.CreateSheet(sheetName);
            columns = columns ?? ExcelSheet.FetchDefaultColumnList<T>();
            columns.ForEach((item) => {
                item.HeadCellStyle = GetDefaultHeaderStyle(workbook);
                item.DataCellStyle = GetDefaultDataStyle(workbook, item.DataType);
            });
            sheet.FillDataWithHeader(list, columns);
            sheet.AutoColumnWidth();        // 根据内容自动计算列宽
            workbook.Write(xlsFile);
            xlsFile.Seek(0, SeekOrigin.Begin);
            return xlsFile;
        }

        #endregion

        #region 发运计划导出

        /// <summary>
        /// 发运计划导出
        /// </summary>
        /// <typeparam name=""></typeparam>
        /// <param name="list"></param>
        /// <param name="fileType"></param>
        /// <param name="sheetName"></param>
        /// <param name="columns"></param>
        /// <returns></returns>
        public static MemoryStream ExportToExcelForShippingPlan(List<SD_ShippingPlanDetail> list, ExcelFileType fileType, string sheetName, List<ExcelColumn<SD_ShippingPlanDetail>> columns = null)
        {
            MemoryStream xlsFile = new MemoryStream();
            IWorkbook workbook = GetWorkbook(fileType);
            if (sheetName == "物流供应商")
            {
                //多条数据后根据物流供应商进行分组
                var mainInfoList = list.GroupBy(g => new
                {
                    g.SupplierCode,
                    g.SupplierName
                }).Select(q => new
                {
                    SupplierCode = q.Key.SupplierCode,
                    SupplierName = q.Key.SupplierName,
                });
                int Line = 0;
                foreach (var mainInfo in mainInfoList)
                {
                    var conditionList = list.Where(x => x.SupplierCode == mainInfo.SupplierCode)?.ToList();
                    string SupplierName = "Sheet";
                    if (string.IsNullOrEmpty(mainInfo.SupplierCode))
                    {
                        SupplierName = SupplierName + (Line += 1);
                    }
                    else
                    {
                        SupplierName = mainInfo.SupplierName;
                    }
                    ISheet sheet = workbook.CreateSheet(SupplierName);
                    columns = columns ?? ExcelSheet.FetchDefaultColumnList<SD_ShippingPlanDetail>();
                    columns.ForEach((item) => {
                        item.HeadCellStyle = GetDefaultHeaderStyle(workbook);
                        item.DataCellStyle = GetDefaultDataStyle(workbook, item.DataType);
                    });
                    sheet.FillDataWithHeader(conditionList, columns);
                    sheet.AutoColumnWidth();        // 根据内容自动计算列宽
                }
            }
            else//结算地址
            {
                //多条数据后根据结算地址进行分组
                var mainInfoList = list.GroupBy(g => new
                {
                    g.SettlementAdd
                }).Select(q => new
                {
                    SettlementAdd = q.Key.SettlementAdd,
                });
                int Line = 0;
                foreach (var mainInfo in mainInfoList)
                {
                    var conditionList = list.Where(x => x.SettlementAdd == mainInfo.SettlementAdd)?.ToList();
                    string SettlementAdd = "Sheet";
                    if (string.IsNullOrEmpty(mainInfo.SettlementAdd))
                    {
                        SettlementAdd = SettlementAdd + (Line += 1);
                    }
                    else
                    {
                        SettlementAdd = mainInfo.SettlementAdd;
                    }
                    ISheet sheet = workbook.CreateSheet(SettlementAdd);
                    columns = columns ?? ExcelSheet.FetchDefaultColumnList<SD_ShippingPlanDetail>();
                    columns.ForEach((item) => {
                        item.HeadCellStyle = GetDefaultHeaderStyle(workbook);
                        item.DataCellStyle = GetDefaultDataStyle(workbook, item.DataType);
                    });
                    sheet.FillDataWithHeader(conditionList, columns);
                    sheet.AutoColumnWidth();        // 根据内容自动计算列宽
                }
            }
            
            workbook.Write(xlsFile);
            xlsFile.Seek(0, SeekOrigin.Begin);
            return xlsFile;
        }

        #endregion

        #region 导出到文件

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="exportFileName"></param>
        /// <param name="list"></param>
        /// <param name="columns"></param>
        /// <param name="fileType"></param>
        /// <param name="sheetName"></param>
        public static void SaveAs<T>(string exportFileName, List<T> list,List<ExcelColumn<T>> columns=null, ExcelFileType fileType = ExcelFileType.xls, string sheetName = "Sheet1")
        {


            IWorkbook workbook = GetWorkbook(fileType);
            ISheet sheet = workbook.CreateSheet(sheetName);
            columns = columns ?? ExcelSheet.FetchDefaultColumnList<T>();
            columns.ForEach((item) => {
                item.HeadCellStyle = GetDefaultHeaderStyle(workbook);
                item.DataCellStyle = GetDefaultDataStyle(workbook, item.DataType);
            });
            sheet.FillDataWithHeader(list, columns);
            sheet.AutoColumnWidth();        // 根据内容自动计算列宽

            using (FileStream fs = new FileStream(exportFileName, FileMode.Create, FileAccess.Write))
            {
                workbook.Write(fs);
            }

        }

        #endregion

        #region 泛型获取默认数据列

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        public static List<ExcelColumn<T>> FetchDefaultColumnList<T>()
        {
            return ExcelSheet.FetchDefaultColumnList<T>();
        }

        #endregion

        #region 获取默认表头样式

        /// <summary>
        /// 默认表头样式
        /// </summary>
        /// <param name="workbook"></param>
        /// <returns></returns>
        public static ICellStyle GetDefaultHeaderStyle(IWorkbook workbook)
        {
            ICellStyle style = workbook.CreateCellStyle();
            style.Alignment = HorizontalAlignment.Center;
            style.VerticalAlignment = VerticalAlignment.Center;
            style.BorderBottom = BorderStyle.Thin;
            style.BorderTop = BorderStyle.Thin;
            style.BorderLeft = BorderStyle.Thin;
            style.BorderRight = BorderStyle.Thin;
            style.FillForegroundColor = NPOI.HSSF.Util.HSSFColor.LightGreen.Index;//背景色
            style.FillPattern = FillPattern.Squares;
            style.FillBackgroundColor = NPOI.HSSF.Util.HSSFColor.LightGreen.Index;//前景色

            IFont font = workbook.CreateFont();
            font.FontHeight = 10 * 20;
            font.Boldweight =(short)FontBoldWeight.Bold;        //设置粗体

            style.SetFont(font);
            return style;
        }


        #endregion

        #region 获取默认数据样式
        /// <summary>
        /// 默认内容样式
        /// </summary>
        /// <param name="workBook"></param>
        /// <returns></returns>
        public static ICellStyle GetDefaultDataStyle(IWorkbook workBook)
        {
            ICellStyle style = workBook.CreateCellStyle();
            style.Alignment = HorizontalAlignment.Center;
            style.VerticalAlignment = VerticalAlignment.Center;
            style.BorderBottom = BorderStyle.Thin;
            style.BorderTop = BorderStyle.Thin;
            style.BorderLeft = BorderStyle.Thin;
            style.BorderRight = BorderStyle.Thin;
            style.FillForegroundColor = NPOI.HSSF.Util.HSSFColor.White.Index;//背景色
            style.FillBackgroundColor = NPOI.HSSF.Util.HSSFColor.White.Index;//前景色
            style.FillPattern = FillPattern.Squares;
            style.WrapText = true;  // 自动换行
            SetCellDataFormat(workBook, style, ExcelCellDataFormat.Text);

            IFont font = workBook.CreateFont();
            font.FontHeight = 10 * 20;
            style.SetFont(font);
            return style;
        }

        public static ICellStyle GetDefaultDataStyle(IWorkbook workBook,Type type)
        {
            ICellStyle style = workBook.CreateCellStyle();
            style.Alignment = HorizontalAlignment.Center;
            style.VerticalAlignment = VerticalAlignment.Center;
            style.BorderBottom = BorderStyle.Thin;
            style.BorderTop = BorderStyle.Thin;
            style.BorderLeft = BorderStyle.Thin;
            style.BorderRight = BorderStyle.Thin;
            style.FillForegroundColor = NPOI.HSSF.Util.HSSFColor.White.Index;//背景色
            style.FillBackgroundColor = NPOI.HSSF.Util.HSSFColor.White.Index;//前景色
            style.FillPattern = FillPattern.Squares;
            style.WrapText = true;  // 自动换行
            SetCellDataFormat(workBook, style, GetExcelCellDataFormat(type));

            IFont font = workBook.CreateFont();
            font.FontHeight = 10 * 20;
            style.SetFont(font);
            return style;
        }


        #endregion

        #region 获取数据格式
        /// <summary>
        /// 
        /// </summary>
        /// <param name="workbook"></param>
        /// <param name="cellStyle"></param>
        /// <param name="excelDataFormat"></param>
        public static void SetCellDataFormat(IWorkbook workbook ,ICellStyle cellStyle ,ExcelCellDataFormat excelDataFormat)
        {
            //cellStyle.DataFormat = HSSFDataFormat.GetBuiltinFormat("text");
            IDataFormat format = workbook.CreateDataFormat();

            switch (excelDataFormat)
            {
                case ExcelCellDataFormat.StandardDate:
                    cellStyle.DataFormat = format.GetFormat("yyyy-mm-dd");
                    break;
                //case ExcelCellDataFormat.Numeric:
                //    cellStyle.DataFormat = HSSFDataFormat.GetBuiltinFormat("0.00");
                //    break;
                //case ExcelCellDataFormat.Currency:
                //    cellStyle.DataFormat = format.GetFormat("￥#,##0");
                //    break;
                //case ExcelCellDataFormat.Percent:
                //    cellStyle.DataFormat = HSSFDataFormat.GetBuiltinFormat("0.00%");
                //    break;
                //case ExcelCellDataFormat.ChineseCurrency:
                //    cellStyle.DataFormat = format.GetFormat("[DbNum2][$-804]0");
                //    break;
                //case ExcelCellDataFormat.Scientific:
                //    cellStyle.DataFormat = HSSFDataFormat.GetBuiltinFormat("0.00E+00");
                //    break;
                default:
                    cellStyle.DataFormat = HSSFDataFormat.GetBuiltinFormat("text");
                    break;
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        public static ExcelCellDataFormat GetExcelCellDataFormat(Type type)
        {
            ExcelCellDataFormat result = ExcelCellDataFormat.Text;
            switch (type.ToString())
            {
                case "System.String":
                    result = ExcelCellDataFormat.Text;
                    break;
                case "System.DateTime":
                    result = ExcelCellDataFormat.StandardDate;
                    break;
                case "System.Boolean":
                    result = ExcelCellDataFormat.Text;
                    break;
                case "System.Int16":
                case "System.Int32":
                case "System.Int64":
                case "System.Byte":
                    result = ExcelCellDataFormat.Convention;
                    break;
                case "System.Decimal"://浮点型
                    result = ExcelCellDataFormat.Currency;
                    break;
                case "System.Double":
                    result = ExcelCellDataFormat.Numeric;
                    break;
                default:
                    break;
            }

            return result;
        }

        #endregion
    }
}
