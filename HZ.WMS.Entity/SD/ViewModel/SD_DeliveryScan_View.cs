using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.SD.ViewModel

{
    /// <summary>
    /// 销售发货查询发运计划
    /// </summary>
    public class SD_DeliveryScan_View
    {

        /// <summary> 
        /// wms单号
        /// </summary> 
        [Description("wms单号")]
        public string DocNum { get; set; }

        /// <summary> 
        /// 客户编号
        /// </summary> 
        [Description("客户编号")]
        public string CustomerCode { get; set; }

        /// <summary> 
        /// 客户名称
        /// </summary> 
        [Description("客户名称")]
        public string CustomerName { get; set; }

        /// <summary> 
        /// 客户地址
        /// </summary> 
        [Description("客户地址")]
        public string CustomerAdd { get; set; }


        /// <summary> 
        /// 项目类别
        /// </summary> 
        [Description("项目类别")]
        public string ProjectCategory { get; set; }

        /// <summary> 
        /// 销售交货批
        /// </summary> 
        [Description("销售交货批")]
        public string BatchNum { get; set; }

        /// <summary> 
        /// 利润中心
        /// </summary> 
        [Description("利润中心")]
        public string PRCTR { get; set; }


        /// <summary> 
        /// 销售单号
        /// </summary> 
        [Description("销售单号")]
        public string SaleNum { get; set; }

        /// <summary> 
        /// 销售单行号
        /// </summary> 
        [Description("销售单行号")]
        public int? SaleLine { get; set; }

        /// <summary> 
        /// 销售类型
        /// </summary> 
        [Description("销售类型")]
        public string SalesType { get; set; }

        /// <summary> 
        /// 物料编号
        /// </summary> 
        [Description("物料编号")]
        public string ItemCode { get; set; }

        /// <summary> 
        /// 物料名称
        /// </summary> 
        [Description("物料名称")]
        public string ItemName { get; set; }

        /// <summary> 
        /// 数量
        /// </summary> 
        [Description("数量")]
        public decimal? ShippingPlanDetailQty { get; set; }

        /// <summary> 
        /// 单位
        /// </summary> 
        [Description("单位")]
        public string Unit { get; set; }

        /// <summary> 
        /// 仓库编号
        /// </summary> 
        [Description("仓库编号")]
        public string WhsCode { get; set; }

        /// <summary> 
        /// 仓库名称
        /// </summary> 
        [Description("仓库名称")]
        public string WhsName { get; set; }


        /// <summary> 
        /// 区域编号
        /// </summary> 
        [Description("区域编号")]
        public string RegionCode { get; set; }

        /// <summary> 
        /// 区域名称
        /// </summary> 
        [Description("区域名称")]
        public string RegionName { get; set; }

        /// <summary> 
        /// 库位编号
        /// </summary> 
        [Description("库位编号")]
        public string BinLocationCode { get; set; }

        /// <summary> 
        /// 库位名称
        /// </summary> 
        [Description("库位名称")]
        public string BinLocationName { get; set; }

        /// <summary> 
        /// 承诺的交货日期
        /// </summary> 
        [Description("承诺的交货日期")]
        public DateTime? DeliveryDate { get; set; }

        /// <summary>
        /// 装运点/收货点
        /// </summary>
        [Description("装运点/收货点")]
        public string VSTEL { get; set; }



        /// <summary> 
        /// 销售组织
        /// </summary> 
        [Description("销售组织")]
        public string SalesOrganization { get; set; }


        /// <summary>
        /// 参考单据的单据编号
        /// </summary>
        [Description("参考单据的单据编号")]
        public string VGBEL { get; set; }

        /// <summary>
        /// 参考项目的项目号
        /// </summary>
        [Description("参考项目的项目号")]
        public int? VGPOS { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Description("备注")]
        public string Remark { get; set; }

        /// <summary> 
        /// 合同单号
        /// </summary> 
        [Description("合同单号")]
        public string CONT { get; set; }

        /// <summary>
        /// 结算地址
        /// </summary>
        [Description("结算地址")]
        public string SettlementAdd { get; set; }

        /// <summary>
        /// 物流供应商编号
        /// </summary>
        [Description("物流供应商编号")]
        public string SupplierCode { get; set; }

        /// <summary>
        /// 物流供应商名称
        /// </summary>
        [Description("物流供应商")]
        public string SupplierName { get; set; }



        /// <summary>
        /// 客户订单号
        /// </summary>
        [Description("客户订单号")]
        public string CustomerOrderNum { get; set; }
        

    }
}
