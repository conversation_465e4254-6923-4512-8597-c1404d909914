<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{539182E9-B8C9-44C8-807D-4833D0CA7927}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>HZ.WMS.WebAPI</RootNamespace>
    <AssemblyName>HZ.WMS.WebAPI</AssemblyName>
    <TargetFrameworkVersion>v4.6.2</TargetFrameworkVersion>
    <MvcBuildViews>false</MvcBuildViews>
    <UseIISExpress>true</UseIISExpress>
    <Use64BitIISExpress />
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>3</WarningLevel>
    <DocumentationFile>Publish\HZ.WMS.WebAPI.xml</DocumentationFile>
    <IncludeIisSettings>false</IncludeIisSettings>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <DocumentationFile>bin\HZ.WMS.WebAPI.xml</DocumentationFile>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Chloe">
      <HintPath>..\packages\Chloe.3.9.0\lib\net40\Chloe.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.DataAccess.v17.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Lib\DevExpress.DataAccess.v17.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Pdf.v17.2.Core">
      <HintPath>..\Lib\DevExpress.Pdf.v17.2.Core.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Printing.v17.2.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Lib\DevExpress.Printing.v17.2.Core.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraReports.v17.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Lib\DevExpress.XtraReports.v17.2.dll</HintPath>
    </Reference>
    <Reference Include="ICSharpCode.SharpZipLib, Version=********, Culture=neutral, PublicKeyToken=1b03e6acf1164f73, processorArchitecture=MSIL">
      <HintPath>..\packages\SharpZipLib.1.3.3\lib\net45\ICSharpCode.SharpZipLib.dll</HintPath>
    </Reference>
    <Reference Include="LinqKit.Core, Version=*******, Culture=neutral, PublicKeyToken=bc217f8844052a91, processorArchitecture=MSIL">
      <HintPath>..\packages\LinqKit.Core.1.2.7\lib\net45\LinqKit.Core.dll</HintPath>
    </Reference>
    <Reference Include="log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a, processorArchitecture=MSIL">
      <HintPath>..\packages\log4net.2.0.8\lib\net45-full\log4net.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.6.0.0\lib\net461\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="mscorlib" />
    <Reference Include="Newtonsoft.Json, Version=********, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.12.0.2\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="NLog, Version=5.0.0.0, Culture=neutral, PublicKeyToken=5120e14c03d0593c, processorArchitecture=MSIL">
      <HintPath>..\packages\NLog.5.2.7\lib\net46\NLog.dll</HintPath>
    </Reference>
    <Reference Include="Oracle.ManagedDataAccess, Version=4.122.21.1, Culture=neutral, PublicKeyToken=89b483f429c47342, processorArchitecture=MSIL">
      <HintPath>..\packages\Oracle.ManagedDataAccess.21.18.0\lib\net462\Oracle.ManagedDataAccess.dll</HintPath>
    </Reference>
    <Reference Include="SqlSugar, Version=5.1.4.125, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\SqlSugar.5.1.4.126\lib\SqlSugar.dll</HintPath>
    </Reference>
    <Reference Include="NPOI, Version=2.5.6.0, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.5.6\lib\net45\NPOI.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OOXML, Version=2.5.6.0, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.5.6\lib\net45\NPOI.OOXML.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OpenXml4Net, Version=2.5.6.0, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.5.6\lib\net45\NPOI.OpenXml4Net.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OpenXmlFormats, Version=2.5.6.0, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.5.6\lib\net45\NPOI.OpenXmlFormats.dll</HintPath>
    </Reference>
    <Reference Include="Swashbuckle.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=cd1bb07a5ac7c7bc, processorArchitecture=MSIL">
      <HintPath>..\packages\Swashbuckle.Core.5.6.0\lib\net40\Swashbuckle.Core.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=4.0.4.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.6.0\lib\net462\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.Data">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Formats.Asn1, Version=8.0.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Formats.Asn1.8.0.1\lib\net462\System.Formats.Asn1.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression" />
    <Reference Include="System.Memory, Version=4.0.2.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.6.0\lib\net462\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.Formatting, Version=5.2.7.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Client.5.2.7\lib\net45\System.Net.Http.Formatting.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.5.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.6.0\lib\net462\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Caching" />
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=6.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.1.0\lib\net462\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encodings.Web, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encodings.Web.6.0.0\lib\net461\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Json, Version=6.0.0.10, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Json.6.0.10\lib\net461\System.Text.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net461\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Cors, Version=5.2.7.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Cors.5.2.7\lib\net45\System.Web.Cors.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Http, Version=5.2.7.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Core.5.2.7\lib\net45\System.Web.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http.Cors, Version=5.2.7.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Cors.5.2.7\lib\net45\System.Web.Http.Cors.dll</HintPath>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Abstractions" />
    <Reference Include="System.Web.Routing" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="Microsoft.Web.Infrastructure, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.Web.Infrastructure.1.0.0.0\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http">
    </Reference>
    <Reference Include="System.Net.Http.WebRequest">
    </Reference>
    <Reference Include="System.Web.Helpers, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.4\lib\net45\System.Web.Helpers.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http.WebHost, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.WebHost.5.2.4\lib\net45\System.Web.Http.WebHost.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Mvc, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.Mvc.5.2.4\lib\net45\System.Web.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Optimization">
      <HintPath>..\packages\Microsoft.AspNet.Web.Optimization.1.1.3\lib\net40\System.Web.Optimization.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.Razor.3.2.4\lib\net45\System.Web.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.4\lib\net45\System.Web.WebPages.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.4\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.4\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="WebActivatorEx, Version=2.0.0.0, Culture=neutral, PublicKeyToken=7b26dc2a43f6a0d4, processorArchitecture=MSIL">
      <HintPath>..\packages\WebActivatorEx.2.0\lib\net40\WebActivatorEx.dll</HintPath>
    </Reference>
    <Reference Include="WebGrease">
      <Private>True</Private>
      <HintPath>..\packages\WebGrease.1.6.0\lib\WebGrease.dll</HintPath>
    </Reference>
    <Reference Include="Antlr3.Runtime">
      <Private>True</Private>
      <HintPath>..\packages\Antlr.3.5.0.2\lib\Antlr3.Runtime.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="App_Start\AddRequiredHeaderParameter.cs" />
    <Compile Include="App_Start\BundleConfig.cs" />
    <Compile Include="App_Start\DatabaseConfig.cs" />
    <Compile Include="App_Start\FilterConfig.cs" />
    <Compile Include="App_Start\RouteConfig.cs" />
    <Compile Include="App_Start\SwaggerConfig.cs" />
    <Compile Include="App_Start\WebApiConfig.cs" />
    <Compile Include="Areas\CableBasic\CustomerPartController.cs" />
    <Compile Include="Areas\CablePartProduce\PartWorkOrderController.cs" />
    <Compile Include="Areas\CableProduce\PartScanController.cs" />
    <Compile Include="Areas\CableProduce\ProductionOrderController.cs" />
    <Compile Include="Areas\CableProduce\WorkOrderController.cs" />
    <Compile Include="Areas\CableSale\DeliveryController.cs" />
    <Compile Include="Areas\CableSale\ShippingPlanController.cs" />
    <Compile Include="Areas\HelpPage\ApiDescriptionExtensions.cs" />
    <Compile Include="Areas\HelpPage\App_Start\HelpPageConfig.cs" />
    <Compile Include="Areas\HelpPage\Controllers\HelpController.cs" />
    <Compile Include="Areas\HelpPage\HelpPageAreaRegistration.cs" />
    <Compile Include="Areas\HelpPage\HelpPageConfigurationExtensions.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\CollectionModelDescription.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\ComplexTypeModelDescription.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\DictionaryModelDescription.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\EnumTypeModelDescription.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\EnumValueDescription.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\IModelDocumentationProvider.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\KeyValuePairModelDescription.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\ModelDescription.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\ModelDescriptionGenerator.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\ModelNameAttribute.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\ModelNameHelper.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\ParameterAnnotation.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\ParameterDescription.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\SimpleTypeModelDescription.cs" />
    <Compile Include="Areas\HelpPage\Models\HelpPageApiModel.cs" />
    <Compile Include="Areas\HelpPage\SampleGeneration\HelpPageSampleGenerator.cs" />
    <Compile Include="Areas\HelpPage\SampleGeneration\HelpPageSampleKey.cs" />
    <Compile Include="Areas\HelpPage\SampleGeneration\ImageSample.cs" />
    <Compile Include="Areas\HelpPage\SampleGeneration\InvalidSample.cs" />
    <Compile Include="Areas\HelpPage\SampleGeneration\ObjectGenerator.cs" />
    <Compile Include="Areas\HelpPage\SampleGeneration\SampleDirection.cs" />
    <Compile Include="Areas\HelpPage\SampleGeneration\TextSample.cs" />
    <Compile Include="Areas\HelpPage\XmlDocumentationProvider.cs" />
    <Compile Include="Areas\KB\Controllers\PurchaseOrderController.cs" />
    <Compile Include="Areas\KB\Controllers\SaleOrderTaskController.cs" />
    <Compile Include="Areas\KB\Controllers\StockWarningController.cs" />
    <Compile Include="Areas\KB\KBAreaRegistration.cs" />
    <Compile Include="Areas\MD\Controllers\MD_BasicSpecificationController.cs" />
    <Compile Include="Areas\MD\Controllers\MD_BinLimitController.cs" />
    <Compile Include="Areas\MD\Controllers\MD_BinLocationController.cs" />
    <Compile Include="Areas\MD\Controllers\MD_CustomerController.cs" />
    <Compile Include="Areas\MD\Controllers\MD_CustomerModelDeliveryAdvanceController.cs" />
    <Compile Include="Areas\MD\Controllers\MD_CustomerProduceController.cs" />
    <Compile Include="Areas\MD\Controllers\MD_ItemController.cs" />
    <Compile Include="Areas\MD\Controllers\MD_LabelTemplateController.cs" />
    <Compile Include="Areas\MD\Controllers\MD_LineBatchController.cs" />
    <Compile Include="Areas\MD\Controllers\MD_NonStandardConfigController.cs" />
    <Compile Include="Areas\MD\Controllers\MD_PartMakeCompanyController.cs" />
    <Compile Include="Areas\MD\Controllers\MD_PartProduceLineController.cs" />
    <Compile Include="Areas\MD\Controllers\MD_PrintTemplateController.cs" />
    <Compile Include="Areas\MD\Controllers\MD_PrintTemplateParameterController.cs" />
    <Compile Include="Areas\MD\Controllers\MD_ProcessInspectionController.cs" />
    <Compile Include="Areas\MD\Controllers\MD_ProduceLineCapacityController.cs" />
    <Compile Include="Areas\MD\Controllers\MD_ProduceLineController.cs" />
    <Compile Include="Areas\MD\Controllers\MD_ProductionDistributionSettingController.cs" />
    <Compile Include="Areas\MD\Controllers\MD_RegionController.cs" />
    <Compile Include="Areas\MD\Controllers\MD_ReportStationController.cs" />
    <Compile Include="Areas\MD\Controllers\MD_ReportStationMaterialController.cs" />
    <Compile Include="Areas\MD\Controllers\MD_ShippingMarkRuleController.cs" />
    <Compile Include="Areas\MD\Controllers\MD_StockController.cs" />
    <Compile Include="Areas\MD\Controllers\MD_SupplierController.cs" />
    <Compile Include="Areas\MD\Controllers\MD_WarehouseController.cs" />
    <Compile Include="Areas\MD\Controllers\MD_WorkCenterStationController.cs" />
    <Compile Include="Areas\MD\MDAreaRegistration.cs" />
    <Compile Include="Areas\MD\Controllers\MD_FreightMileageController.cs" />
    <Compile Include="Areas\MD\Controllers\MD_CustomerWeightRateCController.cs" />
    <Compile Include="Areas\MD\Controllers\MD_CustomerWeightRateAController.cs" />
    <Compile Include="Areas\MD\Controllers\MD_CustomerWeightPriceBController.cs" />
    <Compile Include="Areas\MD\Controllers\MD_CustomerDistanceRateAController.cs" />
    <Compile Include="Areas\MD\Controllers\MD_CustomerAddController.cs" />
    <Compile Include="Areas\MM\Controllers\MM_BarCodeController.cs" />
    <Compile Include="Areas\MM\Controllers\MM_BarCodeScanController.cs" />
    <Compile Include="Areas\MM\Controllers\MM_DepRequisitionController.cs" />
    <Compile Include="Areas\MM\Controllers\MM_LendingOrderReturnController.cs" />
    <Compile Include="Areas\MM\Controllers\MM_MagnetsWarehousingController.cs" />
    <Compile Include="Areas\MM\Controllers\MM_OtherInController.cs" />
    <Compile Include="Areas\MM\Controllers\MM_PickingApplyController.cs" />
    <Compile Include="Areas\MM\Controllers\MM_RedeployApplyController.cs" />
    <Compile Include="Areas\MM\Controllers\MM_ScrapApplicationController.cs" />
    <Compile Include="Areas\MM\Controllers\MM_StockTodoPickingController.cs" />
    <Compile Include="Areas\MM\Controllers\MM_SupplierRepairController.cs" />
    <Compile Include="Areas\MM\Controllers\MM_TakeStockPlanController.cs" />
    <Compile Include="Areas\MM\Controllers\MM_TakeStockPlanDetailedController.cs" />
    <Compile Include="Areas\MM\Controllers\MM_TakeStockScanController.cs" />
    <Compile Include="Areas\MM\Controllers\MM_StockTodoController.cs" />
    <Compile Include="Areas\MM\MMAreaRegistration.cs" />
    <Compile Include="Areas\MM\Controllers\MM_DispatchController.cs" />
    <Compile Include="Areas\MM\Controllers\MM_EquipmentPickingController.cs" />
    <Compile Include="Areas\MM\Controllers\MM_ReturnController.cs" />
    <Compile Include="Areas\MM\Controllers\MM_WarehousingController.cs" />
    <Compile Include="Areas\MM\Controllers\MM_LendingOrderController.cs" />
    <Compile Include="Areas\PO\Controllers\PO_PurchaseReceiptController.cs" />
    <Compile Include="Areas\PO\Controllers\PO_ReturnScanController.cs" />
    <Compile Include="Areas\PO\POAreaRegistration.cs" />
    <Compile Include="Areas\PP\Controllers\PP_MaterialReportController.cs" />
    <Compile Include="Areas\PP\Controllers\PP_PackSortingController.cs" />
    <Compile Include="Areas\PP\Controllers\PP_ProcessInspectionController.cs" />
    <Compile Include="Areas\PP\Controllers\PP_ProductionReportController.cs" />
    <Compile Include="Areas\PP\Controllers\PP_ProductionReturnController.cs" />
    <Compile Include="Areas\PP\Controllers\PP_ProductionWarehousingController.cs" />
    <Compile Include="Areas\PP\Controllers\PP_ReturnScanApplicationController.cs" />
    <Compile Include="Areas\PP\Controllers\PP_MaterialDistributionController.cs" />
    <Compile Include="Areas\PP\Controllers\PP_OverReceiveController.cs" />
    <Compile Include="Areas\PP\Controllers\PP_ProductionSerialNumberAssignmentController.cs" />
    <Compile Include="Areas\PP\Controllers\PP_ReturnScanController.cs" />
    <Compile Include="Areas\PP\Controllers\PP_SerialNoRelationController.cs" />
    <Compile Include="Areas\PP\Controllers\PP_ShippingMarkController.cs" />
    <Compile Include="Areas\PP\Controllers\PP_WorkshopSchedulingController.cs" />
    <Compile Include="Areas\PP\Controllers\PP_ProductionFeedingController.cs" />
    <Compile Include="Areas\PP\PPAreaRegistration.cs" />
    <Compile Include="Areas\Produce\Controllers\Produce_PlanOrderController.cs" />
    <Compile Include="Areas\Produce\Controllers\Produce_ReportController.cs" />
    <Compile Include="Areas\Produce\Controllers\Produce_SchedulingDetailController.cs" />
    <Compile Include="Areas\Produce\Controllers\Produce_WarehousingController.cs" />
    <Compile Include="Areas\Produce\Controllers\Produce_SchedulingController.cs" />
    <Compile Include="Areas\Produce\Controllers\Produce_PurchaseOrderController.cs" />
    <Compile Include="Areas\Produce\ProduceAreaRegistration.cs" />
    <Compile Include="Areas\PUB\Controllers\StockController.cs" />
    <Compile Include="Areas\PUB\PUBAreaRegistration.cs" />
    <Compile Include="Areas\QM\Controllers\QM_PurchaseInspectionController.cs" />
    <Compile Include="Areas\QM\QMAreaRegistration.cs" />
    <Compile Include="Areas\RPT\Controllers\RPT_ItemMoveController.cs" />
    <Compile Include="Areas\RPT\RPTAreaRegistration.cs" />
    <Compile Include="Areas\Sale\Controllers\Sale_DeliveryController.cs" />
    <Compile Include="Areas\Sale\Controllers\Sale_ShippingPlanController.cs" />
    <Compile Include="Areas\Sale\SaleAreaRegistration.cs" />
    <Compile Include="Areas\SAP\Controllers\XZSAPController.cs" />
    <Compile Include="Areas\SAP\SAPAreaRegistration.cs" />
    <Compile Include="Areas\SD\Controllers\SD_DeliveryScanController.cs" />
    <Compile Include="Areas\SD\Controllers\SD_ReturnScanController.cs" />
    <Compile Include="Areas\SD\Controllers\SD_ShippingPlanController.cs" />
    <Compile Include="Areas\SD\SDAreaRegistration.cs" />
    <Compile Include="Areas\SD\Controllers\SD_ConsignmentNoteController.cs" />
    <Compile Include="Areas\Sys\Controllers\Sys_ApiLogConfigController.cs" />
    <Compile Include="Areas\Sys\Controllers\Sys_AppVersionController.cs" />
    <Compile Include="Areas\Sys\Controllers\Sys_DbBackupConfigController.cs" />
    <Compile Include="Areas\Sys\Controllers\Sys_DbBackupController.cs" />
    <Compile Include="Areas\Sys\Controllers\Sys_LogController.cs" />
    <Compile Include="Areas\Sys\Controllers\Sys_MailController.cs" />
    <Compile Include="Areas\Sys\Controllers\Sys_MailServerConfigController.cs" />
    <Compile Include="Areas\Sys\Controllers\Sys_MessageController.cs" />
    <Compile Include="Areas\Sys\Controllers\Sys_MessageNotifySettingController.cs" />
    <Compile Include="Areas\Sys\Controllers\Sys_MessageTypeController.cs" />
    <Compile Include="Areas\Sys\Controllers\Sys_OrganizationController.cs" />
    <Compile Include="Areas\Sys\Controllers\Sys_ResourceController.cs" />
    <Compile Include="Areas\Sys\Controllers\Sys_RoleController.cs" />
    <Compile Include="Areas\Sys\Controllers\Sys_RoleResourceController.cs" />
    <Compile Include="Areas\Sys\Controllers\Sys_SwithConfigController.cs" />
    <Compile Include="Areas\Sys\Controllers\Sys_DictionaryController.cs" />
    <Compile Include="Areas\Sys\Controllers\Sys_UserController.cs" />
    <Compile Include="Areas\Sys\Controllers\Sys_UserMessageController.cs" />
    <Compile Include="Areas\Sys\Controllers\Sys_UserRoleController.cs" />
    <Compile Include="Areas\Sys\Controllers\Sys_UserSapAccountController.cs" />
    <Compile Include="Areas\Sys\SysAreaRegistration.cs" />
    <Compile Include="Controllers\ApiBaseController.cs" />
    <Compile Include="Controllers\HealthCheckController.cs" />
    <Compile Include="Controllers\HomeController.cs" />
    <Compile Include="Controllers\ValuesController.cs" />
    <Compile Include="Filter\HZAuthorizeFilterAttribute.cs" />
    <Compile Include="Filter\APIExceptionFilterAttribute.cs" />
    <Compile Include="Filter\ApiLoggingFilterAttribute.cs" />
    <Compile Include="Filter\ManagementCacheFilterAttribute.cs" />
    <Compile Include="Filter\ValidateActionFilterAttribute.cs" />
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Areas\SRM\Controllers\XZSRMController.cs" />
    <Compile Include="Areas\SRM\SRMAreaRegistration.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Areas\HelpPage\HelpPage.css" />
    <Content Include="Areas\Produce\Views\web.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Areas\Sale\Views\web.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Content\bootstrap-theme.css" />
    <Content Include="Content\bootstrap-theme.min.css" />
    <Content Include="Content\bootstrap.css" />
    <Content Include="Content\bootstrap.min.css" />
    <Content Include="favicon.ico" />
    <Content Include="fonts\glyphicons-halflings-regular.svg" />
    <Content Include="Global.asax" />
    <Content Include="Oracle.DataAccess.Common.Configuration.Section.xsd" />
    <Content Include="Oracle.ManagedDataAccess.Client.Configuration.Section.xsd" />
    <Content Include="PDAUpdate\FWDWMS_PDA%28v1.0.2%29.apk" />
    <Content Include="Scripts\bootstrap.js" />
    <Content Include="Scripts\bootstrap.min.js" />
    <Content Include="Areas\HelpPage\Views\Web.config" />
    <Content Include="Areas\HelpPage\Views\Shared\_Layout.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\ResourceModel.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\Index.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\TextSample.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\SimpleTypeModelDescription.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\Samples.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\Parameters.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\ModelDescriptionLink.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\KeyValuePairModelDescription.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\InvalidSample.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\ImageSample.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\HelpPageApiModel.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\EnumTypeModelDescription.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\DictionaryModelDescription.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\ComplexTypeModelDescription.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\CollectionModelDescription.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\ApiGroup.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\Api.cshtml" />
    <Content Include="Areas\Sys\Views\web.config" />
    <Content Include="Areas\MD\Views\web.config" />
    <Content Include="Areas\MM\Views\web.config" />
    <Content Include="Areas\PO\Views\web.config" />
    <Content Include="Areas\PP\Views\web.config" />
    <Content Include="Areas\SD\Views\web.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Areas\RPT\Views\web.config" />
    <Content Include="Areas\PUB\Views\web.config" />
    <Content Include="Areas\KB\Views\web.config" />
    <Content Include="Areas\QM\Views\web.config" />
    <Content Include="Areas\SAP\Views\web.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Config\log4net.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="LabelTemplate\仓库盘点表.repx" />
    <Content Include="LabelTemplate\供应商送货单.repx" />
    <Content Include="LabelTemplate\供应商采购标签.repx" />
    <Content Include="LabelTemplate\供应商采购质检标签.repx" />
    <Content Include="LabelTemplate\包装箱标签.repx" />
    <Content Include="LabelTemplate\包装箱标签1.repx" />
    <Content Include="LabelTemplate\包装箱标签2.repx" />
    <Content Include="LabelTemplate\客户标签.repx" />
    <Content Include="LabelTemplate\库位标签.repx" />
    <Content Include="LabelTemplate\物料标签.repx" />
    <Content Include="LabelTemplate\生产不良物料登记单.repx" />
    <Content Include="LabelTemplate\生产不良自制品登记单.repx" />
    <Content Include="LabelTemplate\生产备料波次单.repx" />
    <Content Include="LabelTemplate\生产自制品标签.repx" />
    <Content Include="LabelTemplate\生产退料标签.repx" />
    <Content Include="LabelTemplate\销售交货波次单.repx" />
    <Content Include="LabelTemplate\销售装箱单.repx" />
    <Content Include="LabelTemplate\销售退货标签.repx" />
    <None Include="Properties\PublishProfiles\FolderProfile.pubxml" />
    <None Include="Scripts\jquery-3.3.1.intellisense.js" />
    <Content Include="Scripts\jquery-3.3.1.js" />
    <Content Include="Scripts\jquery-3.3.1.min.js" />
    <Content Include="Scripts\jquery-3.3.1.slim.js" />
    <Content Include="Scripts\jquery-3.3.1.slim.min.js" />
    <Content Include="Scripts\modernizr-2.8.3.js" />
    <Content Include="UploadFiles\LabelTemplate\销售送货单(雷登).repx" />
    <Content Include="Web.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Areas\HelpPage\Views\_ViewStart.cshtml" />
    <Content Include="Content\Site.css" />
    <Content Include="Views\Web.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Views\_ViewStart.cshtml" />
    <Content Include="Views\Home\Index.cshtml" />
    <Content Include="Views\Shared\Error.cshtml" />
    <Content Include="Views\Shared\_Layout.cshtml" />
    <Content Include="Areas\SRM\Views\web.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="UploadFiles\LabelTemplate\库位标签.repx" />
    <Content Include="UploadFiles\LabelTemplate\机座身份卡.repx" />
    <Content Include="UploadFiles\LabelTemplate\超额领料单.repx" />
    <Content Include="UploadFiles\LabelTemplate\采购退货单.repx" />
    <Content Include="UploadFiles\LabelTemplate\委外发料单.repx" />
    <Content Include="UploadFiles\LabelTemplate\委外领料申请单.repx" />
    <Content Include="UploadFiles\LabelTemplate\完工入库标签.repx" />
    <Content Include="UploadFiles\LabelTemplate\物料标签.repx" />
    <Content Include="UploadFiles\LabelTemplate\转子身份卡.repx" />
    <Content Include="UploadFiles\LabelTemplate\销售发运计划.repx" />
    <Content Include="UploadFiles\LabelTemplate\销售送货单.repx" />
    <Content Include="UploadFiles\LabelTemplate\调拨申请.repx" />
    <Content Include="UploadFiles\LabelTemplate\部门领料申请单.repx" />
    <Content Include="UploadFiles\LabelTemplate\物料标签%28物料%29.repx" />
    <Content Include="UploadFiles\LabelTemplate\委外退料单.repx" />
    <Content Include="UploadFiles\LabelTemplate\借出单.repx" />
    <Content Include="Nlog.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Nlog.Debug.config">
      <DependentUpon>Nlog.config</DependentUpon>
    </Content>
    <Content Include="Nlog.Release.config">
      <DependentUpon>Nlog.config</DependentUpon>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Areas\MD\Views\Shared\" />
    <Folder Include="Areas\MM\Models\" />
    <Folder Include="Areas\MM\Views\Shared\" />
    <Folder Include="Areas\PO\Models\" />
    <Folder Include="Areas\PO\Views\Shared\" />
    <Folder Include="Areas\PP\Views\Shared\" />
    <Folder Include="Areas\Produce\Views\Shared\" />
    <Folder Include="Areas\PUB\Models\" />
    <Folder Include="Areas\PUB\Views\Shared\" />
    <Folder Include="Areas\QM\Models\" />
    <Folder Include="Areas\QM\Views\Shared\" />
    <Folder Include="Areas\RPT\Models\" />
    <Folder Include="Areas\RPT\Views\RPT_FTT_View\" />
    <Folder Include="Areas\RPT\Views\Shared\" />
    <Folder Include="Areas\Sale\Views\Shared\" />
    <Folder Include="Areas\SAP\Models\" />
    <Folder Include="Areas\SAP\Views\Shared\" />
    <Folder Include="Areas\SD\Models\" />
    <Folder Include="Areas\SD\Views\Shared\" />
    <Folder Include="Areas\SRM\Models\" />
    <Folder Include="Areas\SRM\Views\Shared\" />
    <Folder Include="Areas\Sys\Models\" />
    <Folder Include="Areas\Sys\Views\Shared\" />
    <Folder Include="PrintTempPath\" />
    <Folder Include="UploadFiles\PP_OverReceive\" />
    <Folder Include="UploadFiles\PP_ReturnScanApplication\" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="fonts\glyphicons-halflings-regular.woff2" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="fonts\glyphicons-halflings-regular.woff" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="fonts\glyphicons-halflings-regular.ttf" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="fonts\glyphicons-halflings-regular.eot" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Content\bootstrap.min.css.map" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Content\bootstrap.css.map" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Content\bootstrap-theme.min.css.map" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Content\bootstrap-theme.css.map" />
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config">
      <SubType>Designer</SubType>
    </None>
    <Content Include="Scripts\jquery-3.3.1.slim.min.map" />
    <Content Include="Scripts\jquery-3.3.1.min.map" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\HZ.Core\AOS.Core.csproj">
      <Project>{62D9F685-5537-494E-8D51-4DAF877F8AF1}</Project>
      <Name>AOS.Core</Name>
    </ProjectReference>
    <ProjectReference Include="..\HZ.WMS.Application\AOS.WMS.Application.csproj">
      <Project>{28DBE3A0-F19A-49E4-9CFE-6F8177CB17BF}</Project>
      <Name>AOS.WMS.Application</Name>
    </ProjectReference>
    <ProjectReference Include="..\HZ.WMS.BydDataAdapter\AOS.WMS.SAPData.csproj">
      <Project>{ed97ed39-1a86-4c93-831b-aca2e4c5005a}</Project>
      <Name>AOS.WMS.SAPData</Name>
    </ProjectReference>
    <ProjectReference Include="..\HZ.WMS.Entity\AOS.WMS.Entity.csproj">
      <Project>{A56C0618-C820-42E7-89B5-E5DA01C72729}</Project>
      <Name>AOS.WMS.Entity</Name>
    </ProjectReference>
    <ProjectReference Include="..\HZ.WMS.WebJob\AOS.WMS.WebJob.csproj">
      <Project>{533772A9-34FE-41E5-BE23-74C0480B2895}</Project>
      <Name>AOS.WMS.WebJob</Name>
    </ProjectReference>
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <Target Name="MvcBuildViews" AfterTargets="AfterBuild" Condition="'$(MvcBuildViews)'=='true'">
    <AspNetCompiler VirtualPath="temp" PhysicalPath="$(WebProjectOutputDir)" />
  </Target>
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>54550</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl></IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target> -->
</Project>