using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.VW
{
    public class PROC_RPT_BarCodeRetrospect : BaseEntity
    {
        /// <summary>
        /// 业务类型
        /// </summary>
        public string docType { get; set; }
        /// <summary> 
        /// 扫描单号
        /// </summary> 
        public string DocNum { get; set; }
        /// <summary> 
        /// 订单号
        /// </summary> 
        public string BaseNum { get; set; }
        /// <summary> 
        /// 供应商/客户编号
        /// </summary> 
        public string CustomerCode { get; set; }
        /// <summary> 
        /// 供应商/客户名称
        /// </summary> 
        public string CustomerName { get; set; }
        /// <summary> 
        /// 物料编号
        /// </summary> 
        public string ItemCode { get; set; }
        /// <summary> 
        /// 物料名称
        /// </summary> 
        public string ItemName { get; set; }
        /// <summary> 
        /// 包装箱码
        /// </summary> 
        public string BoxBarCode { get; set; }
        /// <summary> 
        /// 条码
        /// </summary> 
        public string BarCode { get; set; }
        /// <summary> 
        /// 批次
        /// </summary> 
        public string BatchNum { get; set; }
        /// <summary> 
        /// 数量
        /// </summary> 
        public decimal? Qty { get; set; }
        /// <summary> 
        /// 仓库编号
        /// </summary> 
        public string WhsCode { get; set; }
        /// <summary> 
        /// 仓库名称
        /// </summary> 
        public string WhsName { get; set; }
        /// <summary> 
        /// 区域编号
        /// </summary> 
        public string RegionCode { get; set; }
        /// <summary> 
        /// 区域名称
        /// </summary> 
        public string RegionName { get; set; }
        /// <summary> 
        /// 库位编号
        /// </summary> 
        public string BinLocationCode { get; set; }
        /// <summary> 
        /// 库位名称
        /// </summary> 
        public string BinLocationName { get; set; }

    }
}
