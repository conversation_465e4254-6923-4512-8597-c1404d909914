using System.ComponentModel;

namespace HZ.WMS.Entity.SD
{
    /// <summary>
    /// 配送清单项
    /// </summary>
    public class DeliveryListItem
    {
        /// <summary>
        /// 序号
        /// </summary>
        [Description("序号")]
        public int 序号 { get; set; }

        /// <summary>
        /// 物料编码
        /// </summary>
        [Description("物料编码")]
        public string 物料编码 { get; set; }

        /// <summary>
        /// 物料描述
        /// </summary>
        [Description("物料描述")]
        public string 物料描述 { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量")]
        public string 数量 { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [Description("单位")]
        public string 单位 { get; set; }

        /// <summary>
        /// 工作中心编码
        /// </summary>
        [Description("工作中心编码")]
        public string 工作中心编码 { get; set; }

        /// <summary>
        /// 工作中心名称
        /// </summary>
        [Description("工作中心名称")]
        public string 工作中心名称 { get; set; }

        /// <summary>
        /// 站点编码
        /// </summary>
        [Description("站点编码")]
        public string 站点编码 { get; set; }

        /// <summary>
        /// 站点名称
        /// </summary>
        [Description("站点名称")]
        public string 站点名称 { get; set; }

        /// <summary>
        /// 排产类型
        /// </summary>
        [Description("排产类型")]
        public string 排产类型 { get; set; }
    }
}
