using HZ.Core.Logging;
using Quartz;
using Quartz.Impl;
using Quartz.Simpl;
using Quartz.Xml;
using System;
using System.Threading.Tasks;

namespace HZ.WMS.WebJob
{
    public class JobManager
    {
        public static async Task RunJob()
        {
            try
            {
                XMLSchedulingDataProcessor processor = new XMLSchedulingDataProcessor(new SimpleTypeLoadHelper());
                IScheduler scheduler = await StdSchedulerFactory.GetDefaultScheduler();
                await processor.ProcessFileAndScheduleJobs("~/bin/jobs.xml", scheduler);
                await scheduler.Start();
                await Task.Delay(TimeSpan.FromSeconds(10));
                //await scheduler.Shutdown();
            }
            catch (System.Exception ex)
            {
                LogHelper.Instance.LogError(ex.ToString());
            }
        }
    }
}
