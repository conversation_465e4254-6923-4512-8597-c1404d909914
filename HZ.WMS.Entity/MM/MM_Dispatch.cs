using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.MM
{
    /// <summary>
    /// 委外发料
    /// </summary>
    [SugarTable("MM_Dispatch")]
    public class MM_Dispatch : BaseEntity
    {
        /// <summary> 
        /// 主键ID
        /// </summary> 
        [SugarColumn(IsPrimaryKey = true)]
        [Description("主键ID")]
        public string OutsourcingDispatchID { get; set; }

        /// <summary>
        /// 委外领料单明细ID
        /// </summary>
        [Description("委外领料单明细ID")]
        public string SubcontractingApplicationDetailID { get; set; }

        /// <summary> 
        /// 领料申请单单号
        /// </summary> 
        [Description("领料申请单单号")]
        public string SubcontractingApplicationNum { get; set; }

        /// <summary> 
        /// 领料申请单行号
        /// </summary> 
        [Description("领料申请单行号")]
        public int? SubcontractingApplicationLine { get; set; }

        /// <summary> 
        /// wms单号
        /// </summary> 
        [Description("wms单号")]
        public string DocNum { get; set; }

        /// <summary> 
        /// wms行号
        /// </summary> 
        [Description("wms行号")]
        public int? Line { get; set; }

        /// <summary> 
        /// 委外发料编号
        /// </summary> 
        [Description("编号")]
        public string BaseEntry { get; set; }

        /// <summary> 
        /// 委外发料单号
        /// </summary> 
        [Description("单号")]
        public string BaseNum { get; set; }

        /// <summary> 
        /// 行号
        /// </summary> 
        [Description("行号")]
        public int? BaseLine { get; set; }

        /// <summary> 
        /// 条码
        /// </summary> 
        [Description("条码")]
        public string BarCode { get; set; }

        /// <summary> 
        /// 批次
        /// </summary> 
        [Description("批次")]
        public string BatchNum { get; set; }

        /// <summary> 
        /// 采购单号
        /// </summary> 
        [Description("采购单号")]
        public string PurchaseNum { get; set; }

        /// <summary> 
        /// 采购单行号
        /// </summary> 
        [Description("采购单行号")]
        public string PurchaseLine { get; set; }

        /// <summary> 
        /// 物料编号
        /// </summary> 
        [Description("物料编号")]
        public string ItemCode { get; set; }

        /// <summary> 
        /// 物料名称
        /// </summary> 
        [Description("物料名称")]
        public string ItemName { get; set; }

        /// <summary> 
        /// 供应商编号
        /// </summary> 
        [Description("供应商编号")]
        public string SupplierCode { get; set; }

        /// <summary> 
        /// 供应商名称
        /// </summary> 
        [Description("供应商名称")]
        public string SupplierName { get; set; }

        /// <summary> 
        /// 委外发料数量
        /// </summary> 
        [Description("委外发料数量")]
        public decimal? OutsourcingDispatchQty { get; set; }

        /// <summary> 
        /// 单位
        /// </summary> 
        [Description("单位")]
        public string Unit { get; set; }

        /// <summary> 
        /// 仓库编号
        /// </summary> 
        [Description("仓库编号")]
        public string WhsCode { get; set; }

        /// <summary> 
        /// 仓库名称
        /// </summary> 
        [Description("仓库名称")]
        public string WhsName { get; set; }

        /// <summary> 
        /// 区域编号
        /// </summary> 
        [Description("区域编号")]
        public string RegionCode { get; set; }

        /// <summary> 
        /// 区域名称
        /// </summary> 
        [Description("区域名称")]
        public string RegionName { get; set; }

        /// <summary> 
        /// 库位编号
        /// </summary> 
        [Description("库位编号")]
        public string BinLocationCode { get; set; }

        /// <summary> 
        /// 库位名称
        /// </summary> 
        [Description("库位名称")]
        public string BinLocationName { get; set; }

        /// <summary> 
        /// 手动过账时间
        /// </summary> 
        [Description("手动过账时间")]
        public DateTime? ManualPostTime { get; set; }

        /// <summary> 
        /// 是否过账 1:已过帐 0：未过账
        /// </summary> 
        [Description("是否过账")]
        public bool IsPosted { get; set; }

        /// <summary> 
        /// 过账人
        /// </summary> 
        [Description("过账人")]
        public string PostUser { get; set; }

        /// <summary> 
        /// 过账时间
        /// </summary> 
        [Description("过账时间")]
        public DateTime? PostTime { get; set; }

        /// <summary> 
        /// sap生成单号
        /// </summary> 
        [Description("sap生成单号")]
        public string SapDocNum { get; set; }

        /// <summary> 
        /// sap生成行号
        /// </summary> 
        [Description("sap生成行号")]
        public int? SapLine { get; set; }

        /// <summary> 
        /// 公司代码
        /// </summary> 
        [Description("公司代码")]
        public string CompanyCode { get; set; }

        /// <summary> 
        /// 工厂代码
        /// </summary> 
        [Description("工厂代码")]
        public string FactoryCode { get; set; }

        /// <summary> 
        /// 
        /// 
        /// </summary> 
        [Description("移动类型")]
        public string MovementType { get; set; }

        /// <summary> 
        /// 特殊库存
        /// </summary> 
        [Description("特殊库存")]
        public string SpecialInventory { get; set; }

        /// <summary>
        /// SAP过账标示
        /// </summary>
        [Description("SAP过账标示")]
        public string SAPmark { get; set; }

        /// <summary>
        /// SAP过账消息
        /// </summary>
        [Description("SAP过账消息")]
        public string SAPmessage { get; set; }

        /// <summary>
        /// 产品编号
        /// </summary>
        [Description("产品编号")]
        public string MatnrCode { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        [Description("产品名称")]
        public string MatnrName { get; set; }

        /// <summary>
        /// 产品数量
        /// </summary>
        [Description("产品数量")]
        public decimal? MatnrQty { get; set; }

    }
}
