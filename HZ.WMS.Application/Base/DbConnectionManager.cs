using System;
using System.Collections.Concurrent;
using System.Configuration;
using HZ.Core.Security;
using SqlSugar;

namespace HZ.WMS.Application
{
    /// <summary>
    /// 数据库连接池管理器
    /// </summary>
    public static class DbConnectionManager
    {
        private static readonly ConcurrentDictionary<string, SqlSugarClient> _connectionPool =
            new ConcurrentDictionary<string, SqlSugarClient>();

        /// <summary>
        /// 获取数据库连接
        /// </summary>
        /// <param name="dbKey">数据库连接键</param>
        /// <returns></returns>
        public static SqlSugarClient GetConnection(string dbKey)
        {
            return _connectionPool.GetOrAdd(dbKey, key => CreateConnection(key));
        }

        /// <summary>
        /// 创建数据库连接
        /// </summary>
        /// <param name="dbKey"></param>
        /// <returns></returns>
        private static SqlSugarClient CreateConnection(string dbKey)
        {
            // 使用优化的连接字符串
            var connectionString = ConnectionStringBuilder.BuildOptimizedConnectionString(dbKey);
            var dbType = GetDbType(dbKey);

            var config = new ConnectionConfig()
            {
                ConnectionString = connectionString,
                DbType = dbType,
                IsAutoCloseConnection = true,
                InitKeyType = InitKeyType.Attribute,
                // 连接池配置优化
                MoreSettings = new ConnMoreSettings
                {
                    IsAutoRemoveDataCache = true,
                    IsAutoToUpper = false,
                    // 启用连接池优化
                    IsWithNoLockQuery = true,
                    // 设置缓存时间
                    DefaultCacheDurationInSeconds = 300
                }
            };

            var client = new SqlSugarClient(config, db =>
            {
                // AOP配置
                db.Aop.OnLogExecuted = (sql, pars) =>
                {
                    // 日志记录逻辑
                    var isQuery = IsQuerySql(sql);
                    var sqlTime = db.Ado.SqlExecutionTime;

                    if (!isQuery)
                    {
                        var nativeSql = UtilMethods.GetNativeSql(sql, pars);
                        HZ.Core.Logging.LogHelper.Instance.LogInfo($"数据操作记录 - 用时：{sqlTime.Milliseconds} ms\nSQL：{nativeSql}");
                    }
                    else if (sqlTime.Seconds > 10)
                    {
                        var nativeSql = UtilMethods.GetNativeSql(sql, pars);
                        HZ.Core.Logging.LogHelper.Instance.LogDebug($"慢查询记录 - 用时：{sqlTime.Milliseconds} ms\nSQL：{nativeSql}");
                    }
                };

                // 连接异常处理
                db.Aop.OnError = (exp) =>
                {
                    // 如果是连接池相关异常，记录详细信息
                    if (exp.Message.Contains("timeout") || exp.Message.Contains("pool"))
                    {
                        // HZ.Core.Logging.LogHelper.Instance.LogConnectionPoolError(dbKey, "数据库操作", exp);
                    }
                    else
                    {
                        // HZ.Core.Logging.LogHelper.Instance.LogError("数据库操作异常", exp);
                    }
                };

                // 设置超时时间 - 减少到5分钟
                db.Ado.CommandTimeOut = 300;
            });

            return client;
        }

        /// <summary>
        /// 获取连接字符串
        /// </summary>
        /// <param name="dbKey"></param>
        /// <returns></returns>
        private static string GetConnectionString(string dbKey)
        {
            if (dbKey == "DbConnectionForOracle")
            {
                return ConfigurationManager.ConnectionStrings[dbKey].ConnectionString;
            }
            else
            {
                return DES.Decrypt(ConfigurationManager.ConnectionStrings[dbKey].ConnectionString);
            }
        }

        /// <summary>
        /// 获取数据库类型
        /// </summary>
        /// <param name="dbKey"></param>
        /// <returns></returns>
        private static DbType GetDbType(string dbKey)
        {
            return dbKey == "DbConnectionForOracle" ? DbType.Oracle : DbType.SqlServer;
        }

        /// <summary>
        /// 判断是否为查询SQL
        /// </summary>
        /// <param name="sql"></param>
        /// <returns></returns>
        private static bool IsQuerySql(string sql)
        {
            string result = sql.Trim();
            string[] queryKeywords = { "SELECT", "SHOW", "DESCRIBE", "EXPLAIN" };

            foreach (var keyword in queryKeywords)
            {
                if (result.StartsWith(keyword, StringComparison.OrdinalIgnoreCase))
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// 释放所有连接
        /// </summary>
        public static void DisposeAll()
        {
            foreach (var connection in _connectionPool.Values)
            {
                connection?.Dispose();
            }
            _connectionPool.Clear();
        }

        /// <summary>
        /// 清理空闲连接
        /// </summary>
        public static void ClearIdleConnections()
        {
            // 这里可以添加清理空闲连接的逻辑
            // SqlSugar的连接池会自动管理，但我们可以记录日志
            HZ.Core.Logging.LogHelper.Instance.LogInfo("执行连接池清理操作");
        }

        /// <summary>
        /// 获取连接池状态
        /// </summary>
        /// <returns></returns>
        public static string GetConnectionPoolStatus()
        {
            return $"当前连接池数量: {_connectionPool.Count}";
        }
    }
}
