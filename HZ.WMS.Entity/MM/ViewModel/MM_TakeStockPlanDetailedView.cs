//Chloe框架
using SqlSugar;


namespace HZ.WMS.Entity.MM.ViewModel
{
    [SugarTable("MM_TakeStockPlanDetailed")]
    public class MM_TakeStockPlanDetailedView : BaseEntity
    {
        /// <summary> 
        /// 计划ID
        /// </summary> 
        public string PlanID { get; set; }
        /// <summary>
        /// 计划单号
        /// </summary>
        public string DocNum { get; set; }
        /// <summary>
        /// 区域编号
        /// </summary>
        public string RegionCode { get; set; }
        /// <summary>
        /// 区域名称
        /// </summary>
        public string RegionName { get; set; }
        /// <summary>
        /// 库位编号
        /// </summary>
        public string BinLocationCode { get; set; }
        /// <summary>
        /// 库位名称
        /// </summary>
        public string BinLocationName { get; set; }
        /// <summary>
        /// 物料编号
        /// </summary>
        public string ItemCode { get; set; }
        /// <summary>
        /// 物料名称
        /// </summary>
        public string ItemName { get; set; }
        /// <summary>
        /// 责任人
        /// </summary>
        public string PUser { get; set; }
        /// <summary>
        /// 计划状态
        /// </summary>
        public int Status { get; set; }
    }
}


