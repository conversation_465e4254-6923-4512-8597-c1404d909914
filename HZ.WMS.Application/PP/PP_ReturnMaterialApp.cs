using SqlSugar;
using HZ.WMS.Application.MD;
using HZ.WMS.Application.SAP;
using HZ.WMS.Application.Sys;
using HZ.WMS.Entity.PP;
using HZ.WMS.Entity.PP.Dto;
using HZ.WMS.Entity.SAP;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Application.PP
{
    /// <summary>
    /// 车间退料主表
    /// </summary>
    public class PP_ReturnMaterialApp : BaseApp<PP_ReturnMaterial>
    {
        private PP_ReturnMaterialDetailApp detailApp = new PP_ReturnMaterialDetailApp();
        private Sys_SAPCompanyInfoApp sapApp = new Sys_SAPCompanyInfoApp();

        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public PP_ReturnMaterialApp() : base()
        {
        }

        #endregion

        #region 增加

        public bool Add(PP_ReturnMaterial returnMaterial,List<PP_ReturnMaterialDetail> materialDetails)
        {
            var mark = false;
            try
            {
                //this.DbContext.Ado.BeginTran();
                this.DbContext.Ado.BeginTran();
                base.Insert(returnMaterial);
                detailApp.Insert(materialDetails);
                this.DbContext.Ado.CommitTran();
                mark = true;
            }
            catch (Exception ex)
            {
                mark = false;
                this.DbContext.Ado.RollbackTran();
                throw ex;
            }
            return mark;
        }

        #endregion

        #region 编辑

        public bool Edit(PP_ReturnMaterial_Dto material_Dto, string user)
        {
            bool mark = false;
            try
            {
                var order = this.GetEntityByKey(material_Dto.ID);
                var entity = new PP_ReturnMaterial
                {
                    ID = material_Dto.ID,
                    DocNum = material_Dto.DocNum,
                    FileName = material_Dto.FileName,
                    FilePath = material_Dto.FilePath,
                    ExamineStatus = material_Dto.ExamineStatus,
                    WarehouseReviewer = material_Dto.WarehouseReviewer,
                    WarehouseTime = material_Dto.WarehouseTime,
                    Remark = material_Dto.Remark,
                    CUser = order?.CUser,
                    CTime = order?.CTime,
                    MUser = user,
                };

                var addList = new List<PP_ReturnMaterialDetail>();
                var updateList = new List<PP_ReturnMaterialDetail>();
                foreach (var item in material_Dto.detailed)
                {
                    if (string.IsNullOrEmpty(item.ID))
                    {
                        item.DocNum = material_Dto.DocNum;
                        item.MovementType = material_Dto.MovementType;
                        item.CUser = user;
                        addList.Add(item);
                    }
                    else
                    {
                        item.DocNum = material_Dto.DocNum;
                        item.MovementType = "311";
                        item.MUser = user;
                        updateList.Add(item);
                    }
                }
                var deleteList = detailApp.GetListByKeys(material_Dto.deletedetail);
                if (deleteList != null && deleteList.Count > 0)
                {
                    deleteList.ForEach(item =>
                    {
                        item.DUser = user;
                        item.IsDelete = true;
                    });
                    updateList.AddRange(deleteList);
                }

                this.DbContext.Ado.BeginTran();
                base.Update(entity);
                detailApp.Update(updateList);
                detailApp.Insert(addList);
                this.DbContext.Ado.CommitTran();
                mark = true;
            }
            catch (Exception ex)
            {
                mark = false;
                this.DbContext.Ado.RollbackTran();
                throw ex;
            }
            return mark;
        }

        #endregion

        #region 删除

        public bool Delete(string user, List<PP_ReturnMaterial> returnMaterials, List<PP_ReturnMaterialDetail> materialDetails)
        {
            var mark = false;
            try
            {
                //this.DbContext.Ado.BeginTran();
                this.DbContext.Ado.BeginTran();
                base.Delete(returnMaterials, user);
                detailApp.Delete(materialDetails, user);
                this.DbContext.Ado.CommitTran();
                mark = true;
            }
            catch (Exception ex)
            {
                mark = false;
                this.DbContext.Ado.RollbackTran();
                throw ex;
            }
            return mark;
        }

        #endregion

        #region 退料过账

        /// <summary>
        /// 过账
        /// </summary>
        /// <param name="entities"></param>
        /// <param name="opUser"></param>
        /// <param name="error_message"></param>
        /// <returns></returns>
        public bool DoPost(List<PP_ReturnMaterial> entities, string opUser, out string error_message)
        {
            error_message = "过账成功";
            try
            {
                //SAP过账
                foreach (var entity in entities)
                {
                    int Line = 0;
                    List<ZFGWMS005> zFGWMs = new List<ZFGWMS005>();
                    //var ManualPostTime = entity.ManualPostTime ?? DateTime.Now;
                    var docNum = entity.DocNum;
                    var detailList = detailApp.GetList(w => w.DocNum == entity.DocNum).ToList();
                    foreach (var item in detailList)
                    {

                        Line++;
                        item.Line = Line;

                        zFGWMs.Add(new ZFGWMS005
                        {
                            ZNUM = Line,
                            MATNR = item.ComponentCode,
                            WERKS = item.FactoryCode ?? "2002",
                            BWART = "311",
                            MENGE = (decimal)item.DemandQty,
                            MEINS = item.ComponentUnit,
                            LGORT = item.OutWhsCode,
                            UMLGO = item.InWhsCode,
                            SOBKZ = item.SpecialInventory ??"",
                            KDAUF = item.SalesOrderNo ?? "",
                            KDPOS = item.SalesOrderLineNo==null ? 0: (int)item.SalesOrderLineNo,
                            BWTAR = item.AssessmentType ?? "",
                            SGTXT = item.Remark ?? "",
                        });
                    }

                    var isPosted = false;
                    DateTime date = DateTime.Now;
                    //查询Sap账期时间
                    DateTime ManualPostTime = new SAPApp().GetSAPpostTime(Convert.ToDateTime(entity.ManualPostTime ?? DateTime.Now));
                    var result = sapApp.ZFGWMS005("001", docNum, docNum, ManualPostTime, zFGWMs, out isPosted, out error_message);

                    if (result != null && result.Count > 0)
                    {
                        List<PP_ReturnMaterialDetail> querydetails = new List<PP_ReturnMaterialDetail>();
                        foreach (var sap in result)
                        {
                            var querydetail = detailList.FirstOrDefault(x => x.DocNum == sap.DocNum && x.Line == sap.line);
                            if (querydetail != null)
                            {
                                querydetail.IsPosted = true;
                                querydetail.PostUser = opUser;
                                querydetail.PostTime = date;
                                querydetail.ManualPostTime = ManualPostTime;
                                querydetail.MUser = opUser;
                                querydetail.SapDocNum = sap.sapDocNum;
                                querydetail.SapLine = sap.sapline;
                                querydetails.Add(querydetail);

                                #region 更新库存
                                try
                                {
                                    //调接口减本地库存
                                    if (!string.IsNullOrEmpty(querydetail.SpecialInventory) && querydetail.SpecialInventory.ToUpper() == "E")
                                    {
                                        var mark = new MD_StockApp().StockOut(querydetail.ComponentCode, querydetail.OutBinCode, querydetail.SalesOrderNo, Decimal.ToInt32(querydetail.SalesOrderLineNo ?? 0), querydetail.DemandQty, opUser, out error_message);
                                        //if (!mark) return mark;
                                    }
                                    else
                                    {
                                        var mark = new MD_StockApp().StockOut(querydetail.ComponentCode, querydetail.OutBinCode, querydetail.DemandQty, opUser, out error_message);
                                        //if (!mark) return mark;
                                    }
                                    //加库存
                                    var flg = new MD_StockApp().StockIn(
                                        new Entity.MD.MD_Stock
                                        {
                                            ItemCode = querydetail.ComponentCode,
                                            ItemName = querydetail.MaterialName,
                                            Qty = querydetail.DemandQty,
                                            Unit = querydetail.ComponentUnit,
                                            RegionCode = querydetail.InRegionCode,
                                            RegionName = querydetail.InRegionName,
                                            BinLocationCode = querydetail.InBinCode,
                                            BinLocationName = querydetail.InBinName,
                                            WhsCode = querydetail.InWhsCode,
                                            WhsName = querydetail.InWhsName,
                                            SaleNum = querydetail.SalesOrderNo,
                                            SaleLine = Decimal.ToInt32(querydetail.SalesOrderLineNo ?? 0),
                                            AssessType = querydetail.AssessmentType,
                                            SpecialStock = querydetail.SpecialInventory,
                                        }, opUser, out error_message);
                                    //if (!flg) return flg;
                                }
                                catch (Exception)
                                {
                                }
                                #endregion
                            }
                        }

                        entity.IsPosted = true;
                        entity.PostUser = opUser;
                        entity.PostTime = date;
                        entity.MUser = opUser;
                        entity.MTime = date;
                        entity.ManualPostTime = ManualPostTime;

                        this.DbContext.Ado.BeginTran();
                        Update(entity);
                        detailApp.Update(querydetails);
                        this.DbContext.Ado.CommitTran();
                    }
                    else
                    {
                        error_message = "单号：" + docNum + "，过账失败，失败原因：" + error_message;
                        return isPosted;
                    }
                }
            }
            catch (Exception ex)
            {
                //this.DbContext.Ado.RollbackTran();
                error_message = ex.InnerException?.Message ?? ex.Message;
                return false;
            }
            return true;
        }

        #endregion
    }
}
