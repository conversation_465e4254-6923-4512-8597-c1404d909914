using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.PP
{
    /// <summary>
    /// 分配序列号
    /// </summary>
    public class PP_AssignSerialNo_View : BaseEntity
    {

        /// <summary>
        /// 序号
        /// </summary>
        [Description("序号")]
        public int? SequenceNo { get; set; }
        /// <summary>
        /// 出厂编号 = 开始日期 + 4位流水号
        /// </summary>
        [Description("出厂编号")]
        public string SerialNo { get; set; }
        /// <summary>
        /// 生产订单
        /// </summary>
        [Description("生产订单")]
        public string ProductionOrderNo { get; set; }
        /// <summary>
        /// 客户订单号
        /// </summary>
        [Description("客户订单号")]
        public string OrderNo { get; set; }
        /// <summary>
        /// 发货单位
        /// </summary>
        [Description("发货单位")]
        public string Shippers { get; set; }
        /// <summary>
        /// 交货时间
        /// </summary>
        [Description("交货时间")]
        public DateTime? DeliveryTime { get; set; }
        /// <summary>
        /// 产品型号
        /// </summary>
        [Description("产品型号")]
        public string ProductModel { get; set; }
        /// <summary>
        /// 开始日期(装配日期)
        /// </summary>
        [Description("装配日期")]
        public DateTime? StartTime { get; set; }
        /// <summary>
        /// 工作中心
        /// </summary>
        [Description("工作中心")]
        public string ProductionLineCode { get; set; }
        /// <summary>
        /// 线体描述
        /// </summary>
        [Description("装配线")]
        public string ProductionLineDes { get; set; }
        /// <summary>
        /// 生产批次
        /// </summary>
        [Description("生产批次")]
        public string ProductionBatch { get; set; }
        /// <summary>
        /// 销售订单
        /// </summary>
        [Description("销售订单号")]
        public string SalesOrderNo { get; set; }
        /// <summary>
        /// 销售订单行项目
        /// </summary>
        [Description("销售订单行项目")]
        public decimal? SalesOrderLineNo { get; set; }
        /// <summary>
        /// 合同号
        /// </summary>
        [Description("合同号")]
        public string ContractNo { get; set; }
        /// <summary>
        /// 物料编码
        /// </summary>
        [Description("产品件号")]
        public string MaterialNo { get; set; }
        /// <summary>
        /// 物料描述
        /// </summary>
        [Description("产品描述")]
        public string MaterialName { get; set; }
        /// <summary>
        /// 非标接收编号
        /// </summary>
        [Description("非标接收编号")]
        public string ReceiptNo { get; set; }
        /// <summary>
        /// 订单类型
        /// </summary>
        [Description("订单类型")]
        public string OrderType { get; set; }
        /// <summary>
        ///  订单状态
        /// </summary>
        [Description("订单状态")]
        public string OrderStatus { get; set; }
        /// <summary>
        /// 工厂
        /// </summary>
        [Description("工厂")]
        public string FactoryCode { get; set; }
        /// <summary>
        /// 生产调度员,101是主机，组件
        /// </summary>
        [Description("生产调度员")]
        public string ProductionScheduler { get; set; }
        /// <summary>
        ///  收货库存地点
        /// </summary>
        [Description("库存地点")]
        public string ReceivingLocation { get; set; }
        /// <summary>
        /// 通知状态,1:已通知,0:未通知
        /// </summary>
        [Description("通知状态")]
        public int? IsNoticed { get; set; }
        ///// <summary>
        ///// 备注
        ///// </summary>
        //[Description("备注")]
        //public string Remark { get; set; }
        ///// <summary>
        ///// 插入记录的用户Id
        ///// </summary>
        //[Description("创建用户")]
        //public string CUser { get; set; }
        ///// <summary>
        ///// 插入记录的服务器时间
        ///// </summary>
        //[Description("创建时间")]
        //public DateTime? CTime { get; set; } = DateTime.Now;
        /// <summary>
        /// 组件标识:1定子，2转子
        /// </summary>
        public int? IsComponent { get; set; }
        /// <summary>
        ///  EAP出场编号
        /// </summary>
        [Description("EAP出厂编号")]
        public string EapSerialNo { get; set; }
    }
}
