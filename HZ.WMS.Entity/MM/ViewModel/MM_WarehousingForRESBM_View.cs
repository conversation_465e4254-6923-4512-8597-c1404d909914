using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.MM.ViewModel
{
    /// <summary>
    /// 委外入库查询采购组件信息
    /// </summary>
    public class MM_WarehousingForRESBM_View
    {
        /// <summary>
        /// 采购订单号
        /// </summary>
        [Description("采购订单号")]
        public string BaseNum { get; set; }

        /// <summary>
        /// 采购订单行号
        /// </summary>
        [Description("采购订单行号")]
        public decimal? BaseLine { get; set; }

        /// <summary>
        /// 相关需求的编号
        /// </summary>
        [Description("相关需求的编号")]
        public decimal? RSNUM { get; set; }

        /// <summary>
        /// 相关需求的项目编号
        /// </summary>
        [Description("相关需求的项目编号")]
        public decimal? RSPOS { get; set; }

        /// <summary>
        /// 工厂
        /// </summary>
        [Description("工厂")]
        public string WERKS { get; set; }

        /// <summary>
        /// 物料编号
        /// </summary>
        [Description("物料编号")]
        public string ItemCode { get; set; }

        /// <summary>
        /// 物料名称
        /// </summary>
        [Description("物料名称")]
        public string ItemName { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量")]
        public decimal? Qty { get; set; }

        /// <summary>
        /// 采购订单明细数量
        /// </summary>
        [Description("采购订单明细数量")]
        public decimal? EKPOQty { get; set; }

        /// <summary>
        /// 比例
        /// </summary>
        [Description("比例")]
        public decimal? Proportion { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [Description("单位")]
        public string Unit { get; set; }

        /// <summary>
        /// 供应商编号
        /// </summary>
        [Description("供应商编号")]
        public string SupplierCode { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        [Description("供应商名称")]
        public string SupplierName { get; set; }

        /// <summary>
        /// 借方/贷方标识 H:贷方 S:借方
        /// </summary>
        [Description("借方/贷方标识")]
        public string SHKZG { get; set; }

    }
}
