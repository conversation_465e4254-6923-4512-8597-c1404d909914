using System;
using System.ComponentModel;
using SqlSugar;
//Chloe框架


namespace HZ.WMS.Entity.MM
{
    /// <summary>
    /// 物料标签
    /// </summary>
    [SugarTable("MM_BarCode")]
    public class MM_BarCode : BaseEntity
    {
        /// <summary> 
        /// 条码ID
        /// </summary> 
        [SugarColumn(IsPrimaryKey = true)]
        [Description("条码ID")]
        public string BarID { get; set; }

        /// <summary>
        /// 插入记录的服务器时间
        /// </summary>
        [Description("日期")]
        public new DateTime? CTime { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Description("装配线号")]
        public new string Remark { get; set; }

        /// <summary> 
        /// 合同编号
        /// </summary> 
        [Description("合同号")]
        public string BaseNum { get; set; }

        /// <summary> 
        /// 物料编号
        /// </summary> 
        [Description("产品件号")]
        public string ItemCode { get; set; }

        /// <summary> 
        /// 条码
        /// </summary> 
        [Description("出厂编号")]
        public string BarCode { get; set; }

        /// <summary> 
        /// 部件代码
        /// </summary> 
        [Description("部件代码")]
        public string PartCode { get; set; }

        /// <summary> 
        /// 批次
        /// </summary> 
        [Description("批次")]
        public string BatchNum { get; set; }
     
        /// <summary> 
        /// 物料名称
        /// </summary> 
        [Description("物料名称")]
        public string ItemName { get; set; }

        /// <summary> 
        /// 销售订单行号
        /// </summary> 
        [Description("销售订单行号")]
        public int? BaseLine { get; set; }

        /// <summary> 
        /// 数量
        /// </summary> 
        [Description("数量")]
        public decimal? Qty { get; set; }

        /// <summary> 
        /// 库存单位
        /// </summary> 
        [Description("库存单位")]
        public string Unit { get; set; }

        /// <summary> 
        /// 打印数量
        /// </summary> 
        [Description("打印数量")]
        public int? printQty { get; set; }

        /// <summary> 
        /// 仓库
        /// </summary> 
        [Description("仓库")]
        public string PrintTemplate { get; set; }

    }
}


