using System;
using System.Collections.Generic;
using System.Linq;
using SqlSugar;
using HZ.WMS.Entity.QM;
using HZ.WMS.Entity.SRM;
using HZ.WMS.Application.Sys;
using HZ.WMS.Entity.SRM.ViewModel;
using HZ.WMS.Entity;
using HZ.WMS.Entity.QM.ViewModel;
using HZ.Core.Http;
using HZ.WMS.Application.PO;

namespace HZ.WMS.Application.QM
{
    /// <summary>
    /// 采购入库检验
    /// </summary>
    public class QM_PurchaseInspectionApp : BaseApp<QM_PurchaseInspection>
    {
        #region 初始化

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public QM_PurchaseInspectionApp() : base(){}

        BaseApp<BaseEntity> _baseApp = new BaseApp<BaseEntity>();
        
        #endregion

        #region PC

        #region 删除

        /// <summary>
        /// 1、删除校验
        /// </summary>
        /// <param name="entities">集合</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        private bool CheckDelete(List<QM_PurchaseInspection> entities, out string error_message)
        {
            PO_PurchaseReceiptApp PurchaseReceiptApp = new PO_PurchaseReceiptApp();
            bool isPass = true;
            error_message = "";
            foreach (var t in entities)
            {
                //if (x.IsPosted == true)
                //{
                //    error_message = "ui.Message.PODeletePostingWarning"; // "已过账数据不允许删除!";
                //    isPass = false;
                //    break;
                //}
               var list= PurchaseReceiptApp.GetList(x => x.InspectionNum == t.InspectionNum && x.InspectionLine == t.InspectionLine)?.ToList();
                if (list.Count > 0 && list != null)
                {
                    error_message = "已采购入库不允许删除"; 
                    isPass = false;
                    break;
                }

            }

            return isPass;
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="ids">主键数组</param>
        /// <param name="user">当前用户</param>
        /// <param name="error_message">错误消息</param>
        public bool Deletes(string[] ids, string user, out string error_message)
        {
            bool bDeleted = true;
            var entities = GetListByKeys(ids);
            if (CheckDelete(entities, out error_message))
            {
                try
                {

                    var list = new List<P_InspectionDetail>();
                    foreach (var x in entities) //可用，以后需要再拿回来
                    {
                        var query = DbContextForSRM.Queryable<P_InspectionDetail>().Where(t => t.InspectionNo == x.InspectionNum && t.OrderNo == x.BaseNum
                                                                                             && t.OrderLine == x.BaseLine && t.ItemCode == x.ItemCode
                                                                                             && t.IsDelete == false).ToList().FirstOrDefault();
                        if (query != null)
                        {
                            query.QualifiedQty -= x.QualifiedQty;
                            query.UnQualifiedQty -= x.UnqualifiedQty;
                            list.Add(query);
                        }
                    }

                    DbContextForSRM.Ado.BeginTran();
                    DbContext.Ado.BeginTran();

                    if (Delete(entities, user) > 0)//UpdateInspectionForSRM(inspectiondetail);
                    {
                        UpdateInspectionForSRM(list);
                    }
                    else
                        bDeleted = false;

                    DbContextForSRM.Ado.CommitTran();//提交事务
                    DbContext.Ado.CommitTran();//提交事务
                }
                catch (Exception ex)
                {
                    if (DbContext.Ado.IsAnyTran())
                        DbContext.Ado.RollbackTran();//失败回滚
                    if (DbContextForSRM.Ado.IsAnyTran())
                        DbContextForSRM.Ado.RollbackTran();//失败回滚

                    bDeleted = false;
                    error_message = ex.Message;
                }
            }
            else
            {
                bDeleted = false;
            }
            return bDeleted;
        }

        #endregion

     
        #region 回写SRM检验单

        /// <summary>
        /// 回写SRM检验单
        /// </summary>
        /// <param name="entities">集合</param>
        /// <param name="user">当前用户</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool DoPost(List<QM_PurchaseInspection> entities, string user, out string error_message)
        {
            error_message = "";
            int Line = 0;
            var date = DateTime.Now;
            try
            {
                var list = new List<P_InspectionDetail>();
                foreach (var x in entities) //可用，以后需要再拿回来
                {
                    var query = DbContextForSRM.Queryable<P_InspectionDetail>().Where(t => t.InspectionNo == x.InspectionNum && t.OrderNo == x.BaseNum 
                                                                                         && t.OrderLine == x.BaseLine && t.ItemCode == x.ItemCode 
                                                                                         && t.IsDelete==false).ToList().FirstOrDefault();
                    if (query != null)
                    {
                        query.QualityRasult = x.InspectionItem;
                        if (query.QualifiedQty==null)
                        {
                            query.QualifiedQty = 0;
                        }
                        if (query.UnQualifiedQty==null)
                        {
                            query.UnQualifiedQty = 0;
                        }
                        query.QualifiedQty += x.QualifiedQty;
                        query.UnQualifiedQty = query.InspectionQty- query.QualifiedQty;
                        query.MUser = user;
                        query.MTime = date;
                        query.QualityUser = user;
                        query.QualityDate = date;
                        query.QualityRemark = x.Remark;
                        //query.Status = "2";
                        list.Add(query);
                        x.Line = Line++;
                        x.IsPosted = true;
                        x.PostUser = user;
                        x.PostTime = date;
                    }
                }

                DbContextForSRM.Ado.BeginTran();
                DbContext.Ado.BeginTran();

                if (UpdateInspectionForSRM(list) >0)//UpdateInspectionForSRM(inspectiondetail);
                {
                    Update(entities);
                }

                DbContextForSRM.Ado.CommitTran();//提交事务
                DbContext.Ado.CommitTran();//提交事务

                if (string.IsNullOrEmpty(error_message))
                {
                    error_message = "回写成功";
                }
                return true;
            }
            catch (Exception ex)
            {
                if (DbContext.Ado.IsAnyTran())
                {
                    DbContext.Ado.RollbackTran();//失败回滚
                }
                if (DbContextForSRM.Ado.IsAnyTran())
                {
                    DbContextForSRM.Ado.RollbackTran();//失败回滚
                }
                error_message = ex.Message.ToString();
                return false;
            }
        }

        /// <summary>
        /// 多个实体更新
        /// </summary>
        /// <param name="entities"></param>
        /// <returns>返回更新行数</returns>
        public int UpdateInspectionForSRM(List<P_InspectionDetail> entities)
        {
            var iCount = 0;
            try
            {
                foreach (P_InspectionDetail entity in entities)
                {
                    DbContextForSRM.Updateable(entity).ExecuteCommand();
                    iCount++;
                }
            }
            catch (Exception ex)
            {
                iCount = 0;
                throw new Exception(ex.Message);
            }
            return iCount;
        }

        /// <summary>
        /// 单个实体更新
        /// </summary>
        /// <param name="entities"></param>
        /// <returns>返回更新行数</returns>
        public int UpdateInspectionForSRM(P_InspectionDetail entities)
        {
            var iCount = 0;
            try
            {
                DbContextForSRM.Updateable(entities).ExecuteCommand();
                iCount++;
            }
            catch (Exception ex)
            {
                iCount = 0;
                throw new Exception(ex.Message);
            }
            return iCount;
        }

        #endregion

        #endregion

        #region Mobile

        #region 查询SRM报检单信息

        /// <summary>
        /// 校验 
        /// </summary>
        /// <param name="InspectionNo">报检单号</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool CheckDate(string InspectionNo, out string error_message)
        {
            error_message = "";
            var Inspection = DbContextForSRM.Queryable<P_InspectionDetail>().Where(x => x.InspectionNo == InspectionNo && x.IsDelete==false && x.OrderType !="Z004").ToList().FirstOrDefault();
            if (Inspection == null)
            {
                error_message = "未查询到报检单号[" + InspectionNo + "]的信息";
                return false;
            }
            return true;
        }

        /// <summary>
        /// 查询SRM报检单信息
        /// </summary>
        /// <param name="InspectionNo">参数</param>
        /// <returns></returns>
        public List<SRM_P_InspectionDetail_View> GetSRMInspectionInfo(string InspectionNo, out string error_message)
        {
            //Pagination page = new Pagination();
            //page.Sort = page.Sort.Replace("ItemName", "asc").Replace("OrderNo", "asc");
            error_message = "";
            var query = DbContext.Queryable<SRM_P_InspectionDetail_View>().Where(x => x.InspectionNo == InspectionNo).
              OrderBy(t => t.ItemName).OrderBy(r => r.ItemCode).OrderBy(r => r.OrderNo).OrderBy(r => r.Remark).ToList();
            if (query == null || query.Count <= 0)
            {
                error_message = "报检单号[" + InspectionNo + "]已检验完成" ;
            }
            return query;
        }

        /// <summary>
        /// 校验报检单号、物料号是否已经在采购检验中提交过了-废弃
        /// </summary>
        /// <param name="InspectionNo">报检单号</param>
        /// <param name="ItemCode">物料编号</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        //public bool CheckPurInsDate(string InspectionNo,string ItemCode, out string error_message)
        //{
        //    error_message = "";
        //    var purins = GetList(x => x.InspectionNum == InspectionNo && x.ItemCode == ItemCode).ToList().FirstOrDefault();
        //    if (purins != null)
        //    {
        //        error_message = "报检单号["+InspectionNo+"]物料号["+ItemCode+"]已存在采购检验记录！"; // "扫描的报检单号已存在!";
        //        return false;
        //    }
        //    return true;
        //}

        #endregion

        #region 保存

        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="list">集合</param>
        /// <param name="user">当前用户</param>
        /// <param name="error_message">错误消息</param>
        /// <param name="type">类型 1:提交失败 2：过账失败 </param>
        public bool Save(List<SRM_P_InspectionDetail_View> list, string user, out string error_message, out string type)
        {
            error_message = "";
            type = "";

            foreach (var y in list)
            {
                //根据报检单号、行号、采购单号、采购行号、物料查询报检单信息
                var query2 = DbContext.Queryable<SRM_P_InspectionDetail_View>().Where(x => x.InspectionNo == y.InspectionNo && x.InspectionLine == y.InspectionLine
                                                                                    && x.ItemCode == y.ItemCode && x.OrderNo == y.OrderNo
                                                                                    && x.OrderLine == y.OrderLine).ToList().FirstOrDefault();
                if (y.InspectionQty > query2.InspectionQty)//如果界面的检验数量大于报检单的总数量提示
                {
                    error_message = "报检单号[" + y.InspectionNo + "],物料[" + y.ItemCode + "]检验数量大于报检单数量";
                    type = "1";
                    return false;
                }
            }

            try
            {
                string DocNum = _baseApp.GetNewDocNum(DocType.QM, DocFixedNumDef.QM_PurchaseInspection);
                DateTime date = DateTime.Now;
                var purlist = new List<QM_PurchaseInspection>();
                //插入子表信息 podetailed
                foreach (var x in list)
                {
                    purlist.Add(new QM_PurchaseInspection
                    {
                        DocNum=DocNum,
                        InspectionNum=x.InspectionNo,
                        InspectionLine=x.InspectionLine,
                        CUser=user,
                        CTime=date,
                        IsDelete=false,
                        IsPosted=false,
                        BaseNum=x.OrderNo,
                        BaseLine=Convert.ToInt32(x.OrderLine),
                        BaseType=x.OrderType,
                        SupplierCode=x.SupplyCode,
                        SupplierName=x.SupplierName,
                        ItemCode=x.ItemCode,
                        ItemName=x.ItemName,
                        Batch=x.BatchNum,
                        SalesOrderNum=x.SaleNo,
                        SalesOrderLine=Convert.ToInt32(x.SaleLineNo),
                        InspectionItem=x.InspectionItem,
                        InspectionQty=x.InspectionQty,
                        QualifiedQty=x.QualifiedQty,
                        UnqualifiedQty=x.UnQualifiedQty,
                        DeliveryDate=x.DeliveryDate,
                        WhsCode=x.WhsCode,
                        WhsName=x.WhsAddress,
                        Unit=x.Unit,
                        CompanyCode = x.CompanyCode,
                        FactoryCode = x.FactoryCode
                    });
                }

                DbContext.Ado.BeginTran();

                //批量插入
                this.Insert(purlist);

                DbContext.Ado.CommitTran();

                 Sys_SwithConfigApp _switchApp = new Sys_SwithConfigApp();
                //是否自动过账判定
                if (_switchApp.IsQMPurchaseInspectionAutoPost)
                {
                    string postMag = "";
                    bool bpost= DoPost(purlist, user, out postMag);
                    if (!bpost)
                    {
                        error_message = "提交成功"+postMag;
                        type = "2";
                        return false;
                    }
                    else
                    {
                        error_message = "提交并"+postMag;
                    }
                }
                if (string.IsNullOrEmpty(error_message))
                    error_message = "提交成功";
                return true;
            }
            catch (Exception ex)
            {
                if(DbContext.Ado.IsAnyTran())
                    DbContext.Ado.RollbackTran();
                error_message = ex.Message;
                type = "1";
                return false;
            }
        }

        #endregion

        #endregion

    }

}