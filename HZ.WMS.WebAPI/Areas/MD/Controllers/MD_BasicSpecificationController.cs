using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;
using HZ.Core.Http;
using HZ.Core.Office;
using HZ.WMS.Application.MD;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.MD.Import;
using HZ.WMS.WebAPI.Controllers;

namespace HZ.WMS.WebAPI.Areas.MD.Controllers
{
    /// <summary>
    /// 基础规格表
    /// </summary>
    public class MD_BasicSpecificationController : ApiBaseController
    {
        private MD_BasicSpecificationApp _app = new MD_BasicSpecificationApp();

        #region 查询分页列表

        /// <summary>
        /// 查询分页列表 
        /// </summary>
        /// <param name="page">分页</param>
        /// <param name="req">查询参数</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]MD_BasicSpecificationListReq req)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetPageList(page, x => (string.IsNullOrEmpty(req.SeriesModel)
                    || x.SeriesModel.Contains(req.SeriesModel))
                    && (string.IsNullOrEmpty(req.EnergyEfficiencyModel)
                    || x.EnergyEfficiencyModel.Contains(req.EnergyEfficiencyModel))
                    && (string.IsNullOrEmpty(req.ForvordaPartNo)
                    || x.ForvordaPartNo.Contains(req.ForvordaPartNo))
                    && (string.IsNullOrEmpty(req.SAPPartNo)
                    || x.SAPPartNo.Contains(req.SAPPartNo))
                    && (string.IsNullOrEmpty(req.Model)
                    || x.Model.Contains(req.Model))
                    && (string.IsNullOrEmpty(req.SAPProductModel)
                    || x.SAPProductModel.Contains(req.SAPProductModel))
                    && (string.IsNullOrEmpty(req.RatedPower)
                    || x.RatedPower.Contains(req.RatedPower))
                    && (string.IsNullOrEmpty(req.PackingSize)
                    || x.PackingSize.Contains(req.PackingSize))
                    && (string.IsNullOrEmpty(req.PackingNetWeight)
                    || x.PackingNetWeight.Contains(req.PackingNetWeight))
                    && (string.IsNullOrEmpty(req.PackingGrossWeight)
                    || x.PackingGrossWeight.Contains(req.PackingGrossWeight)));
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取详情
        /// <summary>
        /// 获取详情
        /// </summary>
        /// <param name="ID">记录ID</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetEntity([FromUri]string ID)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.GetEntityByKey(ID);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 添加

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="entity">实体</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Insert([FromBody]MD_BasicSpecification entity)
        {
            var result = new ResponseData();
            try
            {
                entity.CUser = GetCurrentUser().LoginAccount;
                entity.CTime = DateTime.Now;
                
                result.Data = _app.Insert(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 修改

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="entity">实体</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Update([FromBody]MD_BasicSpecification entity)
        {
            var result = new ResponseData();
            try
            {
                entity.MUser = GetCurrentUser().LoginAccount;
                entity.MTime = DateTime.Now;
                
                result.Data = _app.Update(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除单条记录
        /// </summary>
        /// <param name="ID">记录ID</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult DeleteSingle([FromBody]string ID)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.DeleteByKey(ID, GetCurrentUser().LoginAccount);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 批量删除
        /// </summary>
        /// <param name="ids">记录ID数组</param>
        /// <returns></returns>
        [HttpDelete]
        public IHttpActionResult Delete([FromBody]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.DeleteByKeys(ids, GetCurrentUser().LoginAccount);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 导出模板

        /// <summary>
        /// 导出模板
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelModel()
        {
            var result = new ResponseData();
            try
            {
                var columns = ExcelService.FetchDefaultColumnList<MD_BasicSpecification>();
                string modelName = "基本规格参数-导入模板";
                //排除不需要的列名称 字符串
                string[] ignoreField = new string[]
                {
                    "Id", "IsDelete", "CUser", "CTime",
                    "MUser", "MTime", "DUser", "DTime"
                };
                var itemsData = new List<MD_BasicSpecification>() { };
                var ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<MD_BasicSpecification> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }

                return ExportToExcelFile<MD_BasicSpecification>(itemsData, columns,
                    GetCurrentUser().UserName + "_" + modelName);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        /// <summary>
        /// 导出数据
        /// </summary>
        /// <param name="req">查询参数</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] MD_BasicSpecificationListReq req)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetList(x => (string.IsNullOrEmpty(req.SeriesModel)
                    || x.SeriesModel.Contains(req.SeriesModel))
                    && (string.IsNullOrEmpty(req.EnergyEfficiencyModel)
                    || x.EnergyEfficiencyModel.Contains(req.EnergyEfficiencyModel))
                    && (string.IsNullOrEmpty(req.ForvordaPartNo)
                    || x.ForvordaPartNo.Contains(req.ForvordaPartNo))
                    && (string.IsNullOrEmpty(req.SAPPartNo)
                    || x.SAPPartNo.Contains(req.SAPPartNo))
                    && (string.IsNullOrEmpty(req.Model)
                    || x.Model.Contains(req.Model))
                    && (string.IsNullOrEmpty(req.SAPProductModel)
                    || x.SAPProductModel.Contains(req.SAPProductModel))
                    && (string.IsNullOrEmpty(req.RatedPower)
                    || x.RatedPower.Contains(req.RatedPower))
                    && (string.IsNullOrEmpty(req.PackingSize)
                    || x.PackingSize.Contains(req.PackingSize))
                    && (string.IsNullOrEmpty(req.PackingNetWeight)
                    || x.PackingNetWeight.Contains(req.PackingNetWeight))
                    && (string.IsNullOrEmpty(req.PackingGrossWeight)
                    || x.PackingGrossWeight.Contains(req.PackingGrossWeight))).ToList();
                List<ExcelColumn<MD_BasicSpecification>> columns = ExcelService.FetchDefaultColumnList<MD_BasicSpecification>();
                string[] ignoreField = new string[] { "IsDelete", "DTime", "DUser", "MUser", "MTime" };

                List<ExcelColumn<MD_BasicSpecification>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<MD_BasicSpecification> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }

                return ExportToExcelFile<MD_BasicSpecification>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #region 导入

        /// <summary>
        /// 导入
        /// </summary>
        /// <param name="entitys">接收的导入集合</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult ImportExcelToData([FromBody]List<MD_BasicSpecificationImport> entitys)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                if (_app.ImportExcelToBaseData(entitys, GetCurrentUser().LoginAccount, out error_message))
                {
                    result.Data = true;
                    result.Code = (int)WMSStatusCode.Success;
                }
                else
                {
                    result.Data = false;
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
            return Json(result);
        }

        #endregion
    }
}
