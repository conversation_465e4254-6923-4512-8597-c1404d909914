using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Runtime.Remoting;
using System.Threading;
using System.Web;
using System.Web.Http;
using DevExpress.DataAccess.Sql;
using DevExpress.XtraReports.UI;
using HZ.Core.Http;
using HZ.Core.Office;
using HZ.WMS.Application;
using HZ.WMS.Application.SAP;
using HZ.WMS.Application.SD;
using HZ.WMS.Entity.SD;
using HZ.WMS.Entity.SD.Import;
using HZ.WMS.Entity.SD.Parameters;
using HZ.WMS.Entity.Sys;
using HZ.WMS.WebAPI.Controllers;

namespace HZ.WMS.WebAPI.Areas.SD.Controllers
{
    /// <summary>
    /// 发运计划
    /// </summary>
    public class SD_ShippingPlanController : ApiBaseController
    {
        #region 初始化

        private static readonly ConcurrentDictionary<string, object> resourceLocks = new ConcurrentDictionary<string, object>();

        private SD_ShippingPlanApp _app = new SD_ShippingPlanApp();
        private SD_ShippingPlanDetailApp _detailapp = new SD_ShippingPlanDetailApp();
        private SAPApp xzsap_app = new SAPApp();

        #endregion

        #region 查询分页列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <param name="dateValue"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]string keyword, [FromUri] int? PlanStatus, [FromUri] DateTime[] dateValue, 
            [FromUri]string Customer, [FromUri]string CustomerOrderNum, [FromUri]string CONT, [FromUri]string PSStatus, [FromUri] int? IsDeliveryImport)
        {
            var result = new ResponseData();
            try
            {
                //var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = dateValue[0];
                DateTime toTime = dateValue[1];

                if (string.IsNullOrEmpty(Customer) && string.IsNullOrEmpty(CustomerOrderNum) && string.IsNullOrEmpty(CONT) && IsDeliveryImport == null)
                {
                    var itemsData = _app.GetPageList(page, t => (string.IsNullOrEmpty(keyword)
                       || t.DocNum.Contains(keyword) || t.SalesOrderType.Contains(keyword)
                       || t.CUser.Contains(keyword)
                       ) && (PlanStatus == null || t.ShippingPlanStatus == PlanStatus)
                       && (string.IsNullOrEmpty(PSStatus) || t.PSStatus == PSStatus)
                       && (t.DeliveryDate >= fromTime && t.DeliveryDate <= toTime)
                       ).ToList().OrderBy(t => t.ShippingPlanStatus);

                    result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                }
                else
                {
                    var itemsData = _detailapp.GetList(t => (string.IsNullOrEmpty(keyword)|| t.DocNum.Contains(keyword)|| t.CUser.Contains(keyword) || t.SalesOrderType.Contains(keyword)) 
                        && (string.IsNullOrEmpty(Customer) || t.CustomerCode.Contains(Customer) || t.CustomerName.Contains(Customer))
                        && (string.IsNullOrEmpty(CustomerOrderNum) || t.CustomerOrderNum.Contains(CustomerOrderNum) || t.SalesOrderNumber.Contains(CustomerOrderNum))
                        && (string.IsNullOrEmpty(CONT) || t.CONT.Contains(CONT))
                        && (IsDeliveryImport == null || t.IsDeliveryImport == IsDeliveryImport)
                        && (t.DeliveryDate >= fromTime && t.DeliveryDate <= toTime)
                        ).ToList();

                    var docNums = itemsData.Select(x => x.DocNum).Distinct().ToArray();
                    var itemsData1 = _app.GetPageList(page, t => docNums.Contains(t.DocNum) && (PlanStatus == null || t.ShippingPlanStatus == PlanStatus)
                    && (string.IsNullOrEmpty(PSStatus) || t.PSStatus == PSStatus) && (t.DeliveryDate >= fromTime && t.DeliveryDate <= toTime)
                     ).ToList().OrderBy(t => t.ShippingPlanStatus);

                    result.Data = new ResponsePageData { total = page.Total, items = itemsData1 };
                }
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 查询明细分页列表

        /// <summary>
        /// 查询明细分页列表
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageDetailList([FromUri]Pagination page, [FromUri]string keyword)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _detailapp.GetPageList(page, t => (string.IsNullOrEmpty(keyword)
                        || t.DocNum.Contains(keyword)
                        || t.ItemCode.Contains(keyword) 
                        || t.CustomerCode.Contains(keyword)
                        || t.ItemName.Contains(keyword) || t.WhsCode.Contains(keyword)
                        || t.WhsName.Contains(keyword) 
                      )).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 编辑根据单号查询明细信息

        /// <summary>
        /// 编辑根据单号查询明细信息
        /// </summary>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetList([FromUri]string keyword)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _detailapp.GetList(t => (string.IsNullOrEmpty(keyword)
                        || t.DocNum.Contains(keyword)
                        || t.ItemCode.Contains(keyword) || t.CustomerCode.Contains(keyword)
                        || t.CustomerName.Contains(keyword)
                        || t.ItemName.Contains(keyword) || t.WhsCode.Contains(keyword)
                        || t.WhsName.Contains(keyword) 
                      )).ToList();
                    result.Data = itemsData;//new ResponsePageData { total = page.Total, items = itemsData };
                    result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="DocNums">根据单号进行删除</param>
        /// <returns></returns>
        [HttpDelete]
        //[AllowAnonymous]
        public IHttpActionResult Delete([FromBody]string[] DocNums)
        {
            //[FromBody]string[] ids
            var result = new ResponseData();
            try
            {
                string error_message = "";
                //GetCurrentUser().LoginAccount
                bool bDelete = _app.Deletes(DocNums, GetCurrentUser().LoginAccount, out error_message);
                if (!bDelete || !string.IsNullOrEmpty(error_message))
                {
                    result.Code = (int)WMSStatusCode.UnHandledException;
                    result.Message = error_message;
                }

            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 完成

        /// <summary>
        /// 完成
        /// </summary>
        /// <param name="DocNums">手动完成</param>
        /// <returns></returns>
        [HttpGet]
        //[AllowAnonymous]
        public IHttpActionResult Finish([FromUri]string[] DocNums)
        {
            //[FromBody]string[] ids
            var result = new ResponseData();
            try
            {
                string error_message = "";
                //GetCurrentUser().LoginAccount
                bool bFinish = _app.Finish(DocNums, GetCurrentUser().LoginAccount, out error_message);
                if (!bFinish || !string.IsNullOrEmpty(error_message))
                {
                    result.Code = (int)WMSStatusCode.UnHandledException;
                    result.Message = error_message;
                }

            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 导出

        /// <summary>
        /// 导出
        /// </summary>
        /// <param name="keyword"></param>
        /// <param name="dateValue"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] string keyword, [FromUri] int? PlanStatus, [FromUri] DateTime[] dateValue,
            [FromUri] string ExportType,[FromUri]string Customer, [FromUri]string CustomerOrderNum, [FromUri]string CONT, [FromUri]string PSStatus)
        {
            var result = new ResponseData();
            try
            {
                string modelName = "";
                //var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = dateValue[0];
                DateTime toTime = dateValue[1];
                var itemsData = _app.GetExportInfo(keyword, PlanStatus, fromTime, toTime, Customer, CustomerOrderNum, CONT, PSStatus);
                var columns = ExcelService.FetchDefaultColumnList<SD_ShippingPlanDetail>();
                string[] ignoreField = new string[]
                {
                    "ShippingPlanDetailID","Line","SalesType","SalesOrganization",
                    "CreationDate","Time","VoucherDate","DistributionChannels",
                    "ProductGroup","BSTNK","Project","ProjectCategory","PRCTR",
                    "VSTEL","VGBEL","VGPOS","IsDelete","MUser","MTime","DUser",
                    "DTime","Unit","WhsCode","WhsName","DeliveryUser","DeliveryUserName","IsDeliveryImport"
                };
                var ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (var userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }

                if (ExportType == "导出")
                {
                    modelName = "发运计划-导出";
                    return ExportToExcelFile<SD_ShippingPlanDetail>(itemsData, columns);
                }
                else if (ExportType == "物流供应商")
                {
                    modelName = "发运计划-按物流供应商导出";
                    return ExportToExcelFileForShippingPlan(itemsData, columns, modelName, "物流供应商");
                }
                else//结算地址
                {
                    modelName = "发运计划-按结算地址导出";
                    return ExportToExcelFileForShippingPlan(itemsData, columns, modelName, "结算地址");
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        /// <summary>
        /// 按照物流供应商导出
        /// </summary>
        /// <param name="keyword"></param>
        /// <param name="dateValue"></param>
        /// <returns></returns>
        //[HttpGet]
        //public IHttpActionResult ExportToExcelFileForSupplier([FromUri] string keyword, [FromUri] DateTime[] dateValue)
        //{
        //    var result = new ResponseData();
        //    try
        //    {
        //        string modelName = "发运计划-按物流供应商导出";
        //        var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
        //        DateTime fromTime = querDateTimes[0];
        //        DateTime toTime = querDateTimes[1];
        //        var itemsData = _app.GetExportInfo(keyword, fromTime,toTime);
        //        //var itemsData = _detailapp.GetList().Where(t => string.IsNullOrEmpty(keyword)
        //        //        || t.DocNum.Contains(keyword)
        //        //        || t.ItemCode.Contains(keyword) || t.CustomerCode.Contains(keyword)
        //        //        || t.CustomerName.Contains(keyword) || t.ItemName.Contains(keyword)
        //        //        || t.WhsCode.Contains(keyword) || t.WhsName.Contains(keyword)
        //        //        || t.CUser.Contains(keyword)
        //        //        ).Where(x => x.CTime >= fromTime && x.CTime <= toTime && !x.IsDelete).ToList();
        //        //var itemsData = _app.GetList().ToList();
        //        var columns = ExcelService.FetchDefaultColumnList<SD_ShippingPlanDetail>();
        //        string[] ignoreField = new string[]
        //        {
        //            "ShippingPlanDetailID","Line","SalesType","SalesOrganization",
        //            "CreationDate","Time","VoucherDate","DistributionChannels",
        //            "ProductGroup","BSTNK","Project","ProjectCategory","PRCTR",
        //            "VSTEL","VGBEL","VGPOS","IsDelete","MUser","MTime","DUser",
        //            "DTime","Unit","WhsCode","WhsName"
        //        };
        //        var ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
        //        foreach (var userColumn in ignoreFieldList)
        //        {
        //            columns.Remove(userColumn);
        //        }
        //        //columns.ForEach((column) =>
        //        //{
        //        //    if (column.ColumnName == "IsPosted")
        //        //    {
        //        //        column.Formattor = ExcelExportFormatter.IsOrNoFormatter;
        //        //    }
        //        //});
        //        //string[] SupplierName = itemsData.Select(a => a.SupplierName).Distinct().ToArray();
        //        //foreach (var x in SupplierCode)
        //        //{
        //        return ExportToExcelFileForShippingPlan(itemsData, columns, modelName,"物流供应商");
        //        //}
        //        //return Json(result);
        //    }
        //    catch (Exception ex)
        //    {
        //        result.Code = (int)WMSStatusCode.UnHandledException;
        //        result.Message = ex.InnerException?.Message ?? ex.Message;
        //        return Json(result);
        //    }
        //}

        /// <summary>
        /// 按照结算地址导出
        /// </summary>
        /// <param name="keyword"></param>
        /// <param name="dateValue"></param>
        /// <returns></returns>
        //[HttpGet]
        //public IHttpActionResult ExportToExcelFileForSetAdd([FromUri] string keyword, [FromUri] DateTime[] dateValue)
        //{
        //    var result = new ResponseData();
        //    try
        //    {
        //        string modelName = "发运计划-按结算地址导出";
        //        var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
        //        DateTime fromTime = querDateTimes[0];
        //        DateTime toTime = querDateTimes[1];
        //        var itemsData = _app.GetExportInfo(keyword, fromTime, toTime);
        //        //var itemsData = _detailapp.GetList().Where(t => string.IsNullOrEmpty(keyword)
        //        //        || t.DocNum.Contains(keyword)
        //        //        || t.ItemCode.Contains(keyword) || t.CustomerCode.Contains(keyword)
        //        //        || t.CustomerName.Contains(keyword) || t.ItemName.Contains(keyword)
        //        //        || t.WhsCode.Contains(keyword) || t.WhsName.Contains(keyword)
        //        //        || t.CUser.Contains(keyword)
        //        //        ).Where(x => x.CTime >= fromTime && x.CTime <= toTime && !x.IsDelete).ToList();
        //        //var itemsData = _app.GetList().ToList();
        //        var columns = ExcelService.FetchDefaultColumnList<SD_ShippingPlanDetail>();
        //        string[] ignoreField = new string[]
        //        {
        //            "ShippingPlanDetailID","Line","SalesType","SalesOrganization",
        //            "CreationDate","Time","VoucherDate","DistributionChannels",
        //            "ProductGroup","BSTNK","Project","ProjectCategory","PRCTR",
        //            "VSTEL","VGBEL","VGPOS","IsDelete","MUser","MTime","DUser",
        //            "DTime","Unit","WhsCode","WhsName"
        //        };
        //        var ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
        //        foreach (var userColumn in ignoreFieldList)
        //        {
        //            columns.Remove(userColumn);
        //        }
        //        //columns.ForEach((column) =>
        //        //{
        //        //    if (column.ColumnName == "IsPosted")
        //        //    {
        //        //        column.Formattor = ExcelExportFormatter.IsOrNoFormatter;
        //        //    }
        //        //});
        //        //string[] SupplierName = itemsData.Select(a => a.SupplierName).Distinct().ToArray();
        //        //foreach (var x in SupplierCode)
        //        //{
        //        return ExportToExcelFileForShippingPlan(itemsData, columns, modelName, "结算地址");
        //        //}
        //        //return Json(result);
        //    }
        //    catch (Exception ex)
        //    {
        //        result.Code = (int)WMSStatusCode.UnHandledException;
        //        result.Message = ex.InnerException?.Message ?? ex.Message;
        //        return Json(result);
        //    }
        //}

        #endregion

        #region 打印

        /// <summary>
        /// 打印
        /// </summary>
        /// <param name="docNums">单号数组</param>
        /// <param name="templateCode">模板</param>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IHttpActionResult Print([FromUri]string[] docNums,[FromUri] string PrintType)
        {
            var result = new ResponseData();
            string templateCode = "销售发运计划.repx";
            if(PrintType== "物流供应商")
                templateCode = "销售发运计划(物流).repx";
            if (docNums == null || docNums.Length == 0 || string.IsNullOrEmpty(templateCode))
            {
                result.Code = (int)WMSStatusCode.Failed;
            }
            else
            {
                //MD_LabelTempleteApp _labelTemplateApp = new MD_LabelTempleteApp();
                //MD_LabelTemplete template = _labelTemplateApp.GetFirstEntity(x => x.Remark == templateCode);
                //if (template == null)
                //{
                //    result.Code = (int)WMSStatusCode.Failed;
                //}
                //else
                //{
                XtraReport report =
                    XtraReport.FromFile(HttpContext.Current.Server.MapPath("~/") + ConfigurationManager.AppSettings["LabelTemplateUploadPath"]?.ToString() + templateCode, true);
                SqlDataSource dataSource = report.DataSource as SqlDataSource;
                //switch (templateCode)
                //{
                //    case "PP_BarCodeReturn":
                //    case "PP_BarCode":
                //    case "MM_BarCode":
                QueryParameter parameter = new QueryParameter()
                {
                    Name = "docNum",
                    Type = typeof(List<string>),
                    Value = docNums.ToList()
                };

                dataSource.Queries[0].Parameters[0] = parameter;
                //dataSource.Fill();
                return base.PrintToPDF(report);

                //default:
                //    break;


            }
            return Json(result);
        }

        #endregion

        #region 保存

        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="Parameters">参数汇总</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Save([FromBody]SD_ShippingPlanParameters Parameters)
        {

            var result = new ResponseData();
            try
            {
                string error_message = "";
                Sys_User currentUser = GetCurrentUser();
                bool bSubmit = _app.Save(Parameters.DocNum, Parameters.Remark, Parameters.detaileds, Parameters.DeliveryDate, currentUser.LoginAccount, out error_message);

                if (!bSubmit || !string.IsNullOrEmpty(error_message))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }

        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="Parameters">参数汇总</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Update([FromBody]SD_ShippingPlanParameters Parameters)
        {

            var result = new ResponseData();
            try
            {
                string error_message = "";
                Sys_User currentUser = GetCurrentUser();
                bool bSubmit = _app.Update(Parameters.DocNum, Parameters.Remark, Parameters.deleteDetailArray, Parameters.detaileds, currentUser.LoginAccount, out error_message);

                if (!bSubmit || !string.IsNullOrEmpty(error_message))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }

        #endregion

        #region 获取系统生成的单号

        /// <summary>
        /// 获取系统生成的单号
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IHttpActionResult GetDocNum()
        {
            var result = new ResponseData();
            try
            {
                result.Data = base.GenerateDocNum(DocType.SD, DocFixedNumDef.SD_ShippingPlan);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 上传SRM

        /// <summary>
        /// 上传SRM
        /// </summary>
        /// <param name="entities">集合</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult UploadSRM([FromBody]List<SD_ShippingPlan> entities)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                Sys_User currentUser = GetCurrentUser();
                bool bSubmit = _app.UploadSRM(entities, currentUser.LoginAccount, out error_message);
                if (!bSubmit || !string.IsNullOrEmpty(error_message))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }

        #endregion

        #region 导入

        /// <summary>
        /// 导入
        /// </summary>
        /// <param name="entitys">接收的导入集合</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult ImportExcelToData([FromBody]List<SD_ShippingPlanImport> entitys)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                if (_app.ImprotExcelToBaseData(entitys, GetCurrentUser().LoginAccount,out error_message))
                {
                    result.Data = true;
                    result.Code = (int)WMSStatusCode.Success;
                }
                else
                {
                    result.Data = false;
                    result.Code = (int)WMSStatusCode.Failed;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
            return Json(result);
        }

        #endregion

        #region 导入模板

        /// <summary>
        /// 导入模板
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelModel()
        {
            var result = new ResponseData();
            try
            {
                var columns = ExcelService.FetchDefaultColumnList<SD_ShippingPlanDetail>();
                string modelName = "销售发运计划-导入模板";
                //排除不需要的列名称 字符串
                string[] ignoreField = new string[]
                {
                    "ShippingPlanDetailID", "IsDelete", "CUser", "CTime", "MUser", "MTime", "DUser", "DTime",
                    "DocNum", "Line", "CreationDate", "Time", "BSTNK", "Project", "ProjectCategory",
                    "SalesType","SalesOrganization", "VoucherDate", "DistributionChannels", "ProductGroup",
                    "BatchNum", "Unit", "WhsCode", "WhsName","PRCTR", "VSTEL", "VGBEL", "VGPOS","SettlementAdd",
                    "SupplierCode","SupplierName"
                };
                var itemsData = new List<SD_ShippingPlanDetail>()
                {
                    new SD_ShippingPlanDetail(){ Remark = modelName}
                };
                var ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<SD_ShippingPlanDetail> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                //需要格式转换的字段
                //columns.ForEach((column) =>
                //{
                //    if (column.ColumnName == "CTime")
                //    {
                //        column.Formattor = ExcelExportFormatter.DateTimeFormatter;
                //    }
                //});
                return ExportToExcelFile<SD_ShippingPlanDetail>(itemsData, columns, GetCurrentUser().UserName + "_" + modelName);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #region 查询没有发货的销售发运计划信息

        /// <summary>
        /// 查询销售发运计划信息
        /// </summary>
        /// <param name="DocNum">计划单号</param>
        /// <returns></returns>
        [HttpGet]
        //[AllowAnonymous]
        public IHttpActionResult GetNoDeliveryShippingPlan([FromUri] string  DeliveryUser)
        {
            string error_message;
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetNoDeliveryShippingPlan(DeliveryUser);
                if (itemsData != null && itemsData.Count > 0)
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Data = itemsData;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = "";
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region OMS同步WMS发运计划保存

        /// <summary>
        /// OMS同步WMS发运计划保存
        /// </summary>
        /// <param name="ShiPingList">参数汇总</param>
        /// <returns></returns>
        [HttpPost]
        //[AllowAnonymous]
        public IHttpActionResult SaveForOMS([FromBody] List<SD_ShippingForOMS> ShiPingList)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                Sys_User currentUser = GetCurrentUser();

                List<string> salesOrderNumbers = ShiPingList.Select(w => w.SalesOrderNumber).Distinct().ToList();
                List<object> lockedResources = new List<object>();

                try
                {
                    // 批量锁定资源
                    foreach (string resource in salesOrderNumbers)
                    {
                        var resourceLock = LockResource(resource);
                        if (resourceLock != null)
                        {
                            lockedResources.Add(resourceLock);
                        }
                        else
                        {
                            // 处理无法获取锁的情况
                            throw new ServerException("无法获取锁");
                        }
                    }

                    // 在批量锁定之后，执行需要锁定资源的操作
                    bool bSubmit = _app.SaveForOMS(ShiPingList, currentUser.LoginAccount, out error_message);
                    if (!bSubmit)
                    {
                        result.Code = (int)WMSStatusCode.Failed;
                        result.Message = error_message;
                    }
                    else
                    {
                        result.Code = (int)WMSStatusCode.Success;
                        result.Message = error_message;
                    }
                }
                finally
                {
                    // 解锁资源
                    foreach (object resourceLock in lockedResources)
                    {
                        UnlockResource(resourceLock);
                    }
                }

                // 在资源使用完成后，释放不再需要的锁对象
                foreach (string resource in salesOrderNumbers)
                {
                    ReleaseResourceLock(resource);
                }
                
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }

        private static object LockResource(string resourceName)
        {
            object resourceLock = resourceLocks.GetOrAdd(resourceName, key => new object());

            bool lockAcquired = false;
            lock (resourceLock)
            {
                while (!lockAcquired)
                {
                    Monitor.TryEnter(resourceLock, TimeSpan.FromMilliseconds(100), ref lockAcquired);
                    if (!lockAcquired)
                    {
                        // 无法获取锁，等待
                        Monitor.Wait(resourceLock);
                    }
                }
                Console.WriteLine($"Locking resource: {resourceName}");
            }

            return resourceLock;
        }

        private static void UnlockResource(object resourceLock)
        {
            if (resourceLock != null)
            {
                Console.WriteLine($"Unlocking resource: {resourceLock.GetHashCode()}");
                Monitor.Exit(resourceLock);
            }
        }

        private static void ReleaseResourceLock(string resourceName)
        {
            resourceLocks.TryRemove(resourceName, out _);
        }

        #endregion

    }
}