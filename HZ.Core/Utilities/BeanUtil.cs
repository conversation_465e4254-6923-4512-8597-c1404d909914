using System;
using System.Reflection;

namespace AOS.Core.Utilities
{
    public class BeanUtil
    {
        public static T Copy<T>(object source) where T : new()
        {
            if (source == null)
            {
                throw new ArgumentNullException(nameof(source));
            }

            T target = new T();
            Type targetType = typeof(T);
            Type sourceType = source.GetType();

            foreach (PropertyInfo sourceProperty in sourceType.GetProperties())
            {
                PropertyInfo targetProperty = targetType.GetProperty(sourceProperty.Name, sourceProperty.PropertyType);

                // 确保源属性和目标属性具有相同的类型
                if (targetProperty != null && targetProperty.PropertyType == sourceProperty.PropertyType && targetProperty.CanWrite && sourceProperty.CanRead)
                {
                    object value = sourceProperty.GetValue(source, null);
                    targetProperty.SetValue(target, value, null);
                }
            }

            return target;
        }
    }
}