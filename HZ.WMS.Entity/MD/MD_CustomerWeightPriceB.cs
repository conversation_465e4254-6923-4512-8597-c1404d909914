using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;
using System.ComponentModel;

namespace HZ.WMS.Entity.MD
{
    /// <summary>
    /// 销售供应商按车计价-B 
    /// </summary>
    public class MD_CustomerWeightPriceB : BaseEntity
    {
        /// <summary>
        /// ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Description("ID")]
        public string PriceID { get; set; }

        /// <summary>
        /// 供应商编号
        /// </summary>
        [Description("供应商编号")]
        public string SupplierCode { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        [Description("供应商")]
        public string SupplierName { get; set; }

        /// <summary>
        /// 结算地址
        /// </summary>
        [Description("结算地址")]
        public string SettlementAdd { get; set; }

        /// <summary>
        /// 重量从（吨）
        /// </summary>
        [Description("重量从（吨）")]
        public decimal? WeightFrom { get; set; }

        /// <summary>
        /// 重量至（吨）
        /// </summary>
        [Description("重量至（吨）")]
        public decimal? WeightTo { get; set; }

        /// <summary>
        /// 价格
        /// </summary>
        [Description("价格")]
        public decimal? Price { get; set; }

    }
}
