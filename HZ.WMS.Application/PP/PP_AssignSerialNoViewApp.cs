using SqlSugar;
using HZ.Core.Http;
using HZ.WMS.Entity.EAP;
using HZ.WMS.Entity.MM;
using HZ.WMS.Entity.PP;
using HZ.WMS.Entity.PP.ViewModel;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DbType = System.Data.DbType;

namespace HZ.WMS.Application.PP
{
    /// <summary>
    /// 序列号查询
    /// </summary>
    public class PP_AssignSerialNoViewApp : BaseApp<PP_AssignSerialNo_View>
    {
        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public PP_AssignSerialNoViewApp() : base()
        {
        }

        #region 获取EAP出厂编号

        public List<PP_AssignSerialNo_View> HandleEapSerialNo(IEnumerable<PP_AssignSerialNo_View> assignSerialItems)
        {
            if (assignSerialItems != null)
            {
                var assignSerialList = assignSerialItems.ToList();

                var pageNo = assignSerialList.Count() / 500;
                if (assignSerialList.Count() % 500 > 0)
                {
                    pageNo = pageNo + 1;
                }

                List<SGEAP_CUS_Order> cusOrderList = new List<SGEAP_CUS_Order>();

                for (int i = 0; i < pageNo; i++)
                {
                    int startNo = i * 500;
                    int endNo = (i + 1) * 500;
                    if (i == pageNo - 1)
                    {
                        endNo = assignSerialList.Count;
                    }
                    List<string> contractNoList = new List<string>();
                    List<string> orderNoList = new List<string>();
                    for (int j = startNo; j < endNo; j++)
                    {
                        contractNoList.Add(assignSerialList[j].ContractNo);
                        orderNoList.Add(assignSerialList[j].OrderNo);
                    }

                    cusOrderList.AddRange(DbContextForEAP.Queryable<SGEAP_CUS_Order>().Where(t =>
                        contractNoList.Contains(t.ContractNo) && orderNoList.Contains(t.OrderNo))?.ToList());
                }

                Dictionary<string, string> dic = new Dictionary<string, string>();
                foreach (SGEAP_CUS_Order cusOrder in cusOrderList)
                {
                    if (!dic.ContainsKey(cusOrder.ContractNo + cusOrder.OrderNo))
                    {
                        dic.Add(cusOrder.ContractNo + cusOrder.OrderNo, cusOrder.SerialNo);
                    }
                }

                foreach (PP_AssignSerialNo_View assignSerialNo in assignSerialList)
                {
                    if (dic.ContainsKey(assignSerialNo.ContractNo + assignSerialNo.OrderNo))
                    {
                        assignSerialNo.EapSerialNo = dic[assignSerialNo.ContractNo + assignSerialNo.OrderNo];
                    }
                }
                return assignSerialList;
            }

            return new List<PP_AssignSerialNo_View>();
        }

        #endregion


        public IEnumerable<PP_AssignSerialNo_View> GetSerialNoList(Pagination page, DateTime? startTime,
            DateTime? endTime, DateTime? startCreateTime, DateTime? endCreateTime, string keyword,
            string SalesOrderNo, string ContractNo, string OrderNo, string SalesOrderLineNo,
            string ProductionLineDes, string ProductionScheduler)
        {
            SugarParameter returnValue = new SugarParameter("@returnValue", 0, true);
            //查询SAP所有数据
            SugarParameter[] param =
            {
                new SugarParameter("@PageNumber", page.PageNumber),
                new SugarParameter("@PageSize", page.PageSize),
                new SugarParameter("@beginDate", startTime),
                new SugarParameter("@endDate", endTime),
                new SugarParameter("@beginCreateDate", startCreateTime),
                new SugarParameter("@endCreateDate", endCreateTime),
                new SugarParameter("@keyword", keyword),
                new SugarParameter("@SalesOrderNo", SalesOrderNo),
                new SugarParameter("@ContractNo", ContractNo),
                new SugarParameter("@OrderNo", OrderNo),
                new SugarParameter("@SalesOrderLineNo", SalesOrderLineNo),
                new SugarParameter("@ProductionLineDes", ProductionLineDes),
                new SugarParameter("@ProductionScheduler", ProductionScheduler),
                returnValue
            };
            var query = DbContext
                .Ado.UseStoredProcedure().SqlQuery<PP_AssignSerialNo_View>("P_Jerry_AssignSerialNo_20230531", param)
                ?.ToList();
            page.Total = Convert.ToInt32(returnValue.Value);
            return query;
        }


        public IEnumerable<PP_AssignSerialNo_View> GetSerialNoList(DateTime? startTime, DateTime? endTime,
            DateTime? startCreateTime, DateTime? endCreateTime, string keyword,
            string SalesOrderNo, string ContractNo, string OrderNo, string SalesOrderLineNo,
            string ProductionLineDes, string ProductionScheduler)
        {
            SugarParameter returnValue = new SugarParameter("@returnValue", 0, DbType.UInt32,ParameterDirection.Output);
            //查询SAP所有数据
            SugarParameter[] param =
            {
                new SugarParameter("@beginDate", startTime),
                new SugarParameter("@endDate", endTime),
                new SugarParameter("@beginCreateDate", startCreateTime),
                new SugarParameter("@endCreateDate", endCreateTime),
                new SugarParameter("@keyword", keyword),
                new SugarParameter("@SalesOrderNo", SalesOrderNo),
                new SugarParameter("@ContractNo", ContractNo),
                new SugarParameter("@OrderNo", OrderNo),
                new SugarParameter("@SalesOrderLineNo", SalesOrderLineNo),
                new SugarParameter("@ProductionLineDes", ProductionLineDes),
                new SugarParameter("@ProductionScheduler", ProductionScheduler),
                returnValue
            };
            var query = DbContext
                .Ado.UseStoredProcedure().SqlQuery<PP_AssignSerialNo_View>("P_Jerry_AssignSerialNo_20230531", param)
                ?.ToList();
            return query;
        }

        #endregion
    }
}