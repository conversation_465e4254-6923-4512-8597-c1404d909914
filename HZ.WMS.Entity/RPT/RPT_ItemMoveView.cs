using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.RPT
{
    /// <summary>
    /// 物料移动记录
    /// </summary>
    public class RPT_ItemMoveView : BaseEntity
    {

        /// <summary> 
        /// 单据类型
        /// </summary> 
        [Description("单据类型")]
        public string DocType { get; set; }
        /// <summary> 
        /// 单号
        /// </summary> 
        [Description("单号")]
        public string DocNum { get; set; }

        /// <summary> 
        /// 订单号
        /// </summary> 
        [Description("订单号")]
        public string BaseNum { get; set; }

        /// <summary> 
        /// 订单行号
        /// </summary> 
        [Description("订单行号")]
        public int? BaseLine { get; set; }


        /// <summary> 
        /// 供应商编号
        /// </summary> 
        [Description("客户编号")]
        public string SupplierCode { get; set; }

        /// <summary> 
        /// 供应商名称
        /// </summary> 
        [Description("客户名称")]
        public string SupplierName { get; set; }



        /// <summary> 
        /// 物料编号
        /// </summary> 
        [Description("物料编号")]
        public string ItemCode { get; set; }

        /// <summary> 
        /// 物料名称
        /// </summary> 
        [Description("物料名称")]
        public string ItemName { get; set; }

    

        /// <summary> 
        /// 数量
        /// </summary> 
        [Description("数量")]
        public decimal? Qty { get; set; }


        /// <summary> 
        /// 库存单位
        /// </summary> 
        [Description("库存单位")]
        public string Unit { get; set; }

        /// <summary> 
        /// 仓库编号
        /// </summary> 
        [Description("仓库编号")]
        public string WhsCode { get; set; }

        /// <summary> 
        /// 仓库名称
        /// </summary> 
        [Description("仓库")]
        public string WhsName { get; set; }



        /// <summary>
        /// 移动类型
        /// </summary>
        [Description("移动类型")]
        public string MovementType { get; set; }

        /// <summary>
        /// 移动类型名称
        /// </summary>
        [Description("移动类型名称")]
        public string MovementTypeName { get; set; }

        /// <summary> 
        /// 成本中心
        /// </summary> 
        [Description("成本中心")]
        public string CostCenter { get; set; }

        /// <summary> 
        /// 成本中心名称
        /// </summary> 
        [Description("成本中心名称")]
        public string CostCenterName { get; set; }

        /// <summary> 
        /// 总账科目
        /// </summary> 
        [Description("总账科目")]
        public string LedgerType { get; set; }

        /// <summary> 
        /// 总账科目名称
        /// </summary> 
        [Description("总账科目名称")]
        public string LedgerTypeName { get; set; }


        /// <summary> 
        /// 过账人
        /// </summary> 
        [Description("过账人")]
        public string PostUser { get; set; }

        /// <summary> 
        /// 过账时间
        /// </summary> 
        [Description("过账时间")]
        public DateTime? PostTime { get; set; }

        /// <summary>
        /// SAP物料凭证单号
        /// </summary>
        [Description("SAP物料凭证单号")]
        public string SapDocNum { get; set; }

        /// <summary>
        /// SAP物料凭证行号
        /// </summary>
        [Description("SAP物料凭证行号")]
        public int? SapLine { get; set; }

     

    }
}
