using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.PP
{
    /// <summary>
    /// 生产物料配送单主表
    /// </summary>
    public class PP_MaterialDistribution : BaseEntity
    {
        /// <summary>
        /// 主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string ID { get; set; }
        /// <summary>
        /// 配送单号
        /// </summary>
        [Description("配送单号")]
        public string DeliveryOrderNo { get; set; }
        /// <summary>
        /// 线体编码
        /// </summary>
        [Description("线体编码")]
        public string ProductionLineCode { get; set; }
        /// <summary>
        /// 线体描述
        /// </summary>
        [Description("线体描述")]
        public string ProductionLineDes { get; set; }
        /// <summary>
        /// 员工号
        /// </summary>
        [Description("员工号")]
        public string EmployeeNumber { get; set; }
        /// <summary>
        /// 员工姓名
        /// </summary>
        [Description("员工姓名")]
        public string EmployeeName { get; set; }
        /// <summary>
        /// 开始日期(装配日期)
        /// </summary>
        [Description("装配日期")]
        public DateTime? StartTime { get; set; }
        /// <summary>
        /// 是否过账
        /// </summary>
        [Description("是否过账")]
        public bool? IsDoPost { get; set; }
        /// <summary>
        /// 手动过账时间
        /// </summary>
        [Description("手动过账时间")]
        public DateTime? ManualPostTime { get; set; }
        /// <summary>
        /// 过账时间
        /// </summary>
        [Description("过账时间")]
        public DateTime? DoPostTime { get; set; }
        /// <summary>
        /// 过账人
        /// </summary>
        [Description("过账人")]
        public string DoPostMan { get; set; }
    }
}
