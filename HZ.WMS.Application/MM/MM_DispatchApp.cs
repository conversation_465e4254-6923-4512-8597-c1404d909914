using SqlSugar;
using HZ.WMS.Application.Sys;
using HZ.WMS.Entity;
using HZ.WMS.Entity.MM;
using HZ.WMS.Entity.SAP;
using HZ.WMS.Entity.Sys;
using System;
using System.Collections.Generic;
using System.Linq;
using HZ.WMS.Application.MD;
using System.Text;
using System.Threading.Tasks;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.MM.ViewModel;
using System.Linq.Expressions;
using HZ.WMS.Entity.MM.Parameters;
using HZ.WMS.Application.SAP;

namespace HZ.WMS.Application.MM
{
    /// <summary>
    /// 委外发料方法层
    /// </summary>
    public class MM_DispatchApp : BaseApp<MM_Dispatch>
    {
        #region 初始化

         BaseApp<BaseEntity> _baseApp = new BaseApp<BaseEntity>();
         MM_PickingApplyApp _pickingApp = new MM_PickingApplyApp();

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public MM_DispatchApp() : base(){ }

        #endregion

        #region PC
     
        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="ids">删除数组</param>
        /// <param name="opUser">当前用户</param>
        /// <param name="error_message">错误消息</param>
        public bool Deletes(string[] ids, string opUser, out string error_message)
        {
            bool bDeleted = true;
            var entities = GetListByKeys(ids);
            if (CheckDelete(entities, out error_message))
            {
                MM_PickingApplyApp _pickingApp = new MM_PickingApplyApp();
                DateTime date = DateTime.Now;
                var pickings = new List<MM_PickingApply>();
                try
                {
                    //发料后又删除需要同步更新委外领料申请单的状态
                    foreach (var x in entities)
                    {
                        var y = DbContext.Queryable<MM_DispatchForPickingApply_View>().Where(t => t.DocNum == x.SubcontractingApplicationNum && t.ItemCode==x.ItemCode
                                                                                           && t.SupplierCode==x.SupplierCode).ToList().FirstOrDefault();
                        if (y == null)//不存在，已发料完成，点击删除后更新状态为未开始
                        {
                            var z = _pickingApp.GetList(t => t.DocNum == x.SubcontractingApplicationNum).ToList().FirstOrDefault();
                            z.Status = 0;
                            z.MUser = opUser;
                            z.MTime = date;
                            pickings.Add(z);
                        }
                        else
                        {
                            //累计发料数量-本次发料数量 如果等于零，说明未发料，更新状态为未开始
                            if (y.OutsourcingDispatchQty - x.OutsourcingDispatchQty == 0)
                            {
                                var z = _pickingApp.GetList(t => t.DocNum == x.SubcontractingApplicationNum).ToList().FirstOrDefault();
                                z.Status = 0;
                                z.MUser = opUser;
                                z.MTime = date;
                                pickings.Add(z);
                            }
                            else//累计发料数量-本次发料数量 如果大于零，说明发料了，更新状态为进行中
                            {
                                var z = _pickingApp.GetList(t => t.DocNum == x.SubcontractingApplicationNum).ToList().FirstOrDefault();
                                z.Status = 1;
                                z.MUser = opUser;
                                z.MTime = date;
                                pickings.Add(z);
                            }
                        }
                    }

                    DbContext.Ado.BeginTran();
                    _pickingApp.DbContext = this.DbContext;

                    Delete(entities, opUser);
                    _pickingApp.Update(pickings);

                    DbContext.Ado.CommitTran();//提交事务
                }
                catch (Exception ex)
                {
                    bDeleted = false;
                    error_message = ex.Message;

                    if(DbContext.Ado.IsAnyTran())
                        DbContext.Ado.RollbackTran();//失败回滚
                }
            }
            else
            {
                bDeleted = false;
            }
            return bDeleted;
        }

        /// <summary>
        /// 删除校验
        /// 
        /// </summary>
        /// <param name="entities">集合</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        private bool CheckDelete(List<MM_Dispatch> entities, out string error_message)
        {
            bool isPass = true;
            error_message = "";
            foreach (var po in entities)
            {
                if (po.IsPosted == true)
                {
                    error_message = "ui.Message.PODeletePostingWarning"; // "已过账数据不允许删除!";
                    isPass = false;
                    break;
                }
           }
           return isPass;
        }

        #endregion

        #region 过账

        /// <summary>
        /// 过账
        /// </summary>
        /// <param name="entities">委外发料集合</param>
        /// <param name="opUser">当前过账用户</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool DoPost(List<MM_Dispatch> entities, string opUser, out string error_message)
        {
            error_message = "";
            MD_StockApp _stockApp = new MD_StockApp();
            Sys_SAPCompanyInfoApp _SAPCompanyInfoApp = new Sys_SAPCompanyInfoApp();
            try
            {
                //多条数据后根据单号进行分组
                var mainInfoList = entities.GroupBy(g => new
                {
                    g.DocNum
                }).Select(q => new
                {
                    DocNum = q.Key.DocNum
                });

                //按单号分组
                foreach (var mainInfo in mainInfoList)
                {
                    var list = new List<ZFGWMS016>();
                    int Line = 0;
                    var conditionList = entities.Where(x => x.DocNum == mainInfo.DocNum)?.ToList();
                    conditionList.ForEach(x =>
                    {
                        Line = Line + 1;
                        x.Line = Line;
                        list.Add(new ZFGWMS016
                        {
                            ZNUM = Line,
                            MATNR = x.ItemCode,
                            WERKS = x.FactoryCode ?? "2002",//eq.FactoryCode
                            BWART = x.MovementType ?? "541",//541：发料 542：退料
                            MENGE = x.OutsourcingDispatchQty,
                            MEINS = x.Unit,
                            LGORT = x.WhsCode,
                            SOBKZ = x.SpecialInventory,
                            LIFNR = x.SupplierCode,
                            SGTXT = x.Remark
                        });
                    });

                    bool ispost = false;
                    DateTime time = DateTime.Now;
                    //purchaseReceipt.CompanyCode 公司代码
                    //查询Sap账期时间
                    DateTime ManualPostTime = new SAPApp().GetSAPpostTime(Convert.ToDateTime(conditionList[0].ManualPostTime));
                    List<SAPRETURN> saplist = _SAPCompanyInfoApp.ZFGWMS016("001", mainInfo.DocNum, "", ManualPostTime, list, out ispost, out error_message);
                    if (saplist != null && saplist.Count > 0)
                    {
                        //List<MM_OutsourcingDispatch> queryList = new List<MM_OutsourcingDispatch>();
                        foreach (var sap in saplist)
                        {
                            var query = conditionList.Where(x => x.DocNum == mainInfo.DocNum&&x.Line== sap.line).ToList().FirstOrDefault();
                            if (query != null)
                            {
                                query.IsPosted = true;
                                query.PostUser = opUser;
                                query.PostTime = time;
                                query.MUser = opUser;
                                query.MTime = time;
                                query.SapDocNum = sap.sapDocNum;
                                query.SapLine = sap.sapline;
                                query.ManualPostTime = ManualPostTime;
                                query.SAPmark = "S";

                                if (Update(query) > 0)//更新成功后，更新库存
                                {
                                    string errorMsg = string.Empty;
                                    if (!_stockApp.StockOut(query.ItemCode, query.BinLocationCode, query.OutsourcingDispatchQty, opUser, out errorMsg))
                                    {
                                        error_message = errorMsg;
                                        //return false;
                                    }
                                    else
                                    {
                                        // 插入收货记录成功才能更新库存
                                        string stockMsg = string.Empty;
                                        var stock = new MD_Stock();
                                        stock.ItemCode = query.ItemCode;
                                        stock.ItemName = query.ItemName;
                                        stock.SupplierCode = query.SupplierCode;
                                        stock.SupplierName = query.SupplierName;
                                        stock.Qty = query.OutsourcingDispatchQty;
                                        stock.Unit = query.Unit;
                                        stock.SpecialStock = "O";
                                        if (!_stockApp.StockInSupplier(stock, opUser, out stockMsg))
                                        {
                                            error_message = stockMsg;
                                        }
                                    }
                                }
                            }
                        }

                        //更新
                        //Update(queryList);

                    }
                    else
                    {
                        var query = conditionList.Where(x => x.DocNum == mainInfo.DocNum).ToList();
                        foreach (var x in query)
                        {
                            x.SAPmark = "E";
                            x.SAPmessage = error_message;
                        }
                        Update(query);

                        error_message = "单号：" + mainInfo.DocNum + "，过账失败，失败原因：" + error_message;
                        return ispost;
                    }
                }
                if (string.IsNullOrEmpty(error_message))
                {
                    error_message = "过账成功";
                }
                return true;
            }
            catch (Exception ex)
            {
                error_message = ex.Message;
                return false;
            }
        }

        #endregion

        #region 冲销过账

        /// <summary>
        /// 冲销过账
        /// </summary>
        /// <param name="entities">主信息</param>
        /// <param name="entitieDetails">明细信息</param>
        /// <param name="opUser">当前过账用户</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool PassPost(List<MM_Dispatch> conditionList1, string opUser, out string error_message)
        {
            error_message = "";

            foreach (var x in conditionList1)
            {
                if (x.IsPosted == false)
                {
                    error_message = "信息未过帐，不允许冲销过账";
                    return false;
                }

            }

            Sys_SAPCompanyInfoApp _SAPCompanyInfoApp = new Sys_SAPCompanyInfoApp();
            MD_StockApp _stockApp = new MD_StockApp();

            try
            {

                //多条数据后根据单号进行分组
                var mainInfoList = conditionList1.GroupBy(g => new
                {
                    g.SapDocNum
                }).Select(q => new
                {
                    SapDocNum = q.Key.SapDocNum
                });

                //按订单号分组
                foreach (var mainInfo in mainInfoList)
                {
                    int Line = 0;

                    List<string> list = new List<string>();
                    var conditionList = conditionList1.Where(x => x.SapDocNum == mainInfo.SapDocNum)?.ToList();
                    conditionList.ForEach(x =>
                    {
                        list.Add(x.SapLine.ToString());

                    });

                    bool ispost = false;
                    DateTime time = DateTime.Now;
                    DateTime? ManualPostTime1 = conditionList1.Where(x => x.SapDocNum == mainInfo.SapDocNum)?.ToList()[0].ManualPostTime;
                    //查询Sap账期时间
                    DateTime ManualPostTime = new SAPApp().GetSAPpostTime(Convert.ToDateTime(ManualPostTime1));
                    //purchaseReceipt.CompanyCode 公司代码
                    var saplist = _SAPCompanyInfoApp.ZFGWMS021(conditionList[0].CompanyCode ?? "001", mainInfo.SapDocNum, list, ManualPostTime, out ispost, out error_message);
                    if (string.IsNullOrEmpty(error_message))
                    {
                        foreach (var query1 in conditionList)
                        {

                            query1.IsDelete = true;
                            query1.DUser = opUser;
                            query1.DTime = time;
                            query1.Remark = query1.Remark + "已冲销";

                            if (Update(query1) > 0)//更新成功后更新库存
                            {
                                #region 更新库存
                                string errorMsg = string.Empty;
                                if (!_stockApp.StockOut(query1.ItemCode, query1.SupplierCode, "O", query1.OutsourcingDispatchQty, opUser, out errorMsg))
                                {
                                    error_message = errorMsg;
                                    //return false;
                                }
                                else
                                {
                                    //插入普通库存信息
                                    string InstockMsg = string.Empty;
                                    MD_Stock stock = new MD_Stock();
                                    stock.BarCode = query1.BarCode ?? "";
                                    stock.BatchNum = "";
                                    stock.WhsCode = query1.WhsCode;
                                    stock.WhsName = query1.WhsName;
                                    stock.RegionCode = query1.RegionCode;
                                    stock.RegionName = query1.RegionName;
                                    stock.BinLocationCode = query1.BinLocationCode;
                                    stock.BinLocationName = query1.BinLocationName;
                                    //stock.BoqueryBarCode = querydetail.BoxBarCode;
                                    stock.ItemCode = query1.ItemCode.Trim();
                                    stock.ItemName = query1.ItemName.Trim();
                                    stock.ItmsGrpCode = "";
                                    stock.ItmsGrpName = "";
                                    //stock.PTime = querydetail.PTime;
                                    stock.Qty = query1.OutsourcingDispatchQty;
                                    stock.SaleNum = "";
                                    stock.SaleLine = 0;
                                    stock.SpecialStock = "";
                                    stock.Unit = query1.Unit;
                                    stock.AssessType =  "";

                                    if (!_stockApp.StockIn(stock, opUser, out InstockMsg))
                                    {
                                        error_message = InstockMsg;
                                        //return false;
                                    }
                                }
                                #endregion

                                //queryList.Add(query);
                            }
                        }

                      
                    }
                    else
                    {

                        error_message = "单号：" + conditionList[0].DocNum + "，冲销过账失败，失败原因：" + error_message;
                        return false;
                    }
                }
                if (string.IsNullOrEmpty(error_message))
                {
                    error_message = "冲销过账成功";
                }
                return true;
            }
            catch (Exception ex)
            {
                error_message = ex.Message;
                return false;
            }
        }

        #endregion

        #endregion

        #region Mobile

        #region 查询委外领料申请单信息

        /// <summary>
        /// 校验
        /// </summary>
        /// <param name="DocNum">单号</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool CheckDate(string DocNum, out string error_message)
        {
            error_message = "";
            var query = _pickingApp.GetList(x=> x.DocNum == DocNum).ToList().FirstOrDefault();
            if (query == null)
            {
                error_message = "领料申请单号["+DocNum+"]无效，请检查数据！"; 
                return false;
            }
            return true;
        }

        /// <summary>
        /// 查询委外领料申请单关联委外发料信息
        /// </summary>
        /// <param name="DocNum">领料申请单号</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public List<MM_DispatchForPickingApply_View> GetDispatchForPickingApplyInfo(string DocNum, out string error_message)
        {
            error_message = "";
            var query = DbContext.Queryable<MM_DispatchForPickingApply_View>().Where(x => x.DocNum == DocNum).ToList();
            if (query == null || query.Count <= 0)
            {
                error_message = "领料申请单号[" + DocNum + "],下的所有物料已完成发料";
            }
            return query;
        }

        #endregion

        #region 保存

        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="Parameters">委外发料集合</param>
        /// <param name="user">用户信息</param>
        /// <param name="error_message">错误消息返回</param>
        /// <param name="type">类型 1:提交失败 2：过账失败 </param>
        public bool Save(MM_DispatchParameters Parameters, string user, out string error_message, out string type)
        {
            error_message = "提交成功";
            type = "";
            MD_StockApp _stockApp = new MD_StockApp();
            MM_PickingApplyApp _pickingApp = new MM_PickingApplyApp();
            //MM_pic
            try
            {
                //foreach (var x in Parameters.entities)
                //{
                //    //校验出库库存
                //    string errorMsg = string.Empty;
                //    if (!_stockApp.ValidateStockOut(x.ItemCode, x.BinLocationCode, x.Qty, out errorMsg, null, null))
                //    {
                //        error_message = errorMsg;
                //        type = "1";
                //        return false;
                //    }
                //}

                DateTime date = DateTime.Now;
                string DocNum = _baseApp.GetNewDocNum(DocType.MM, DocFixedNumDef.MM_Dispatch);
                var querys = new List<MM_Dispatch>();

                Parameters.entities.ForEach(x=>
                {
                    querys.Add(new MM_Dispatch
                    {
                        MovementType = "541", //"541：发料 542：退料"
                        ManualPostTime =Parameters.ManualPostTime,
                        DocNum = DocNum,
                        IsDelete = false,
                        IsPosted = false,
                        CUser = user,
                        CTime = date,
                        WhsCode = x.WhsCode,
                        WhsName = x.WhsName,
                        RegionCode = x.RegionCode,
                        RegionName = x.RegionName,
                        BinLocationCode= x.BinLocationCode,
                        BinLocationName= x.BinLocationName,
                        CompanyCode="001",
                        FactoryCode="2002",
                        SubcontractingApplicationDetailID= x.DetailID,
                        SubcontractingApplicationNum= x.DocNum,
                        ItemCode= x.ItemCode,
                        ItemName= x.ItemName,
                        SupplierCode= x.SupplierCode,
                        SupplierName= x.SupplierName,
                        OutsourcingDispatchQty= x.Qty,
                        Unit= x.Unit,
                        MatnrCode=x.MatnrCode,
                        MatnrName=x.MatnrName,
                        MatnrQty=x.MatnrQty
                    });

                 

                });

                DbContext.Ado.BeginTran();

                //批量插入
                this.Insert(querys);

                DbContext.Ado.CommitTran();

                var pickings = new List<MM_PickingApply>();
                foreach (var x in Parameters.entities)
                {
                    var y = DbContext.Queryable<MM_DispatchForPickingApply_View>().Where(t => t.DocNum == x.DocNum).ToList();
                    if (y == null || y.Count <= 0)//不存在，已发料完成，更新申请单的状态为已完成
                    {
                        var z = _pickingApp.GetList(t => t.DocNum == x.DocNum).ToList().FirstOrDefault();
                        z.Status = 2;
                        z.MUser = user;
                        z.MTime = date;
                        pickings.Add(z);
                    }
                    else//更新状态为进行中
                    {
                        var z = _pickingApp.GetList(t => t.DocNum == x.DocNum).ToList().FirstOrDefault();
                        z.Status = 1;
                        z.MUser = user;
                        z.MTime = date;
                        pickings.Add(z);
                    }
                }
                _pickingApp.Update(pickings);

                //是否自动过账判定
                Sys_SwithConfigApp _switchApp = new Sys_SwithConfigApp();
                if (_switchApp.IsMMDispatchAutoPost)
                {
                    string postMsg = "";
                    bool bpost= DoPost(querys, user, out postMsg);
                    if (!bpost)
                    {
                        error_message = postMsg;
                        type = "2";
                        return false;
                    }
                    else
                    {
                        error_message = "提交并" + postMsg;
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                if(DbContext.Ado.IsAnyTran())
                    DbContext.Ado.RollbackTran();

                error_message = ex.Message;
                type = "1";
                return false;
            }
        }

        #endregion

        #endregion

    }
}
