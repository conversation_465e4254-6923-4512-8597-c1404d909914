using System;
using System.Collections.Generic;

using HZ.Core.Security;
using HZ.WMS.Entity.Sys;
using HZ.Core.Http;
using SqlSugar;

namespace HZ.WMS.Application.Sys
{
    /// <summary>
    /// 没有out_message消息的函数，意味着只在最前端做未处理异常捕捉
    /// </summary>

    public class Sys_MailApp : BaseApp<Sys_Mail>
    {
		#region 默认构造函数
        
        /// <summary>
        /// 默认构造函数
        /// </summary>
        public Sys_MailApp() : base()
        {
        }

        
        

        #endregion

        #region 获取分页列表

        /// <summary>
        /// 
        /// </summary>
        /// <param name="page"></param>
        /// <param name="fromTime"></param>
        /// <param name="toTime"></param>
        /// <param name="keyword"></param>
        /// <param name="currentUser"></param>
        /// <returns></returns>
        public List<Sys_Mail> GetPageList(Pagination page,DateTime fromTime,DateTime toTime,string keyword="",string currentUser="")
        {
            // 无关键字查询所有数据
            return base.GetPageList(page, t => 
                (string.IsNullOrEmpty(keyword) || t.MailSubject.Contains(keyword) || t.MailBody.Contains(keyword))
                 && ( string.IsNullOrEmpty(currentUser) || t.UserID == currentUser)
                 && (t.CTime >= fromTime && t.CTime <= toTime)
            );
            
        }

        #endregion

    }
}

