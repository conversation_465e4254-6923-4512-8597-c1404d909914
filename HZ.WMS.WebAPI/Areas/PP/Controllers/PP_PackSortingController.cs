using HZ.Core.Http;
using HZ.Core.Office;
using HZ.WMS.Application;
using HZ.WMS.Application.MD;
using HZ.WMS.Application.PP;
using HZ.WMS.Entity.PP;
using HZ.WMS.Entity.PP.Dto;
using HZ.WMS.Entity.Sys;
using HZ.WMS.WebAPI.Controllers;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web;
using System.Web.Http;

namespace HZ.WMS.WebAPI.Areas.PP.Controllers
{
    /// <summary>
    /// 装箱材料分拣
    /// </summary>
    public class PP_PackSortingController : ApiBaseController
    {
        private PP_PackSortingApp _app = new PP_PackSortingApp();
        private PP_PackSortingDetailApp detailApp = new PP_PackSortingDetailApp();
        private PP_ProductionOrderApp orderApp = new PP_ProductionOrderApp();
        private MD_ItemApp itemApp = new MD_ItemApp();

        #region 查询分页列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <param name="dateValue"></param>
        /// <param name="ProductionLineDes"></param>
        /// <param name="ProductionScheduler"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]string keyword, [FromUri]string ProductionLineDes, [FromUri]string ProductionScheduler, [FromUri]DateTime[] dateValue)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];

                var itemsData = _app.GetPageList(page,
                    t => (string.IsNullOrEmpty(keyword)
                        || t.SerialNo.Contains(keyword)
                        || t.ProductionOrderNo.Contains(keyword)
                        || t.PackSortingNo.Contains(keyword)
                        || t.MaterialNo.Contains(keyword)
                        || t.MaterialName.Contains(keyword)
                        || t.ContractNo.Contains(keyword)
                        || t.SalesOrderNo.Contains(keyword)
                        || t.Shippers.Contains(keyword))
                        && (string.IsNullOrEmpty(ProductionLineDes) || t.ProductionLineCode.Contains(ProductionLineDes) || t.ProductionLineDes.Contains(ProductionLineDes))
                        && (string.IsNullOrEmpty(ProductionScheduler) || t.ProductionScheduler.Contains(ProductionScheduler))
                    && (t.StartTime >= fromTime && t.StartTime < toTime)).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 查询子表分页列表

        /// <summary>
        /// 查询子表分页列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetDetailPageList([FromUri]Pagination page, [FromUri]string PackSortingNo)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = detailApp.GetPageList(page, w => PackSortingNo == w.PackSortingNo).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 查询子表列表

        /// <summary>
        /// 查询子表列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetDetailList([FromUri]string PackSortingNo)
        {
            var result = new ResponseData();
            try
            {
                result.Data = detailApp.GetList(w => PackSortingNo == w.PackSortingNo).ToList();
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取详情

        /// <summary>
        /// 获取详情
        /// </summary>
        /// <param name="ID"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetEntity([FromUri]string ID)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.GetEntityByKey(ID);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取系统生成的单号

        /// <summary>
        /// 获取系统生成的单号
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetDocNum()
        {
            var result = new ResponseData();
            try
            {
                result.Data = base.GenerateDocNum(DocType.PP, DocFixedNumDef.PP_PackSorting);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 根据序列号查询生产订单

        /// <summary>
        /// 根据序列号查询生产订单
        /// </summary>
        /// <param name="serialNo"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetOrderBySerialNo([FromUri]string serialNo)
        {
            var result = new ResponseData();
            try
            {
                var order = orderApp.GetFirstEntity(w => w.SerialNo == serialNo);
                var material = new List<PP_PackSortingDetail>();
                if (order != null)
                {
                    //主表订单数量
                    var parent = orderApp.GetSapOrderList(w => w.ProductionOrderNo == order.ProductionOrderNo).ToList().FirstOrDefault();
                    material = detailApp.GetSapOrderDetailList(order.ProductionOrderNo).ToList();
                    if (order.OrderQty < parent?.OrderQty)
                    {
                        foreach (var item in material)
                        {
                            item.DemandQty = item.DemandQty / parent.OrderQty * order.OrderQty;//=>子表需求数量/主表订单数量*主表实际投料数量
                        }
                    }
                }
                result.Data = new { Order = order, Materials = material };
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 添加

        private bool CheckQty(PP_PackSorting_Dto dto,out string error_message)
        {
            error_message = "";
            if (!string.IsNullOrEmpty(dto.SerialNo))
            {
                var pack = _app.GetFirstEntity(w => w.SerialNo == dto.SerialNo);
                if (pack != null)
                {
                    error_message = $"该订单已经分拣，请勿重复！";
                    return false;
                }
            }
            else
            {
                var orderQty = orderApp.GetSapOrderList(w => w.ProductionOrderNo == dto.ProductionOrderNo)?.Sum(s => s.OrderQty);
                var packQty = _app.GetListDistinct(w => w.ProductionOrderNo == dto.ProductionOrderNo)?.Sum(s => s.OrderQty);
                if (dto.OrderQty > (orderQty - packQty))
                {
                    error_message = $"分拣数量大于订单剩余数量，请检查！";
                    return false;
                }
            }

            var list = dto.DetailList.Where(w => w.IsConfirm != true);
            foreach (var item in list)
            {
                var con = itemApp.GetFirstEntity(f => f.ItemCode == item.ComponentCode)?.IsConfirm;
                if (con == true)
                {
                    error_message = $"物料{item.ComponentCode}需要进行确认！";
                    return false;
                }
            }
            return true;
        }

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Add([FromBody]PP_PackSorting_Dto entity)
        {
            var result = new ResponseData();
            try
            {
                string error_message = string.Empty;
                if (!CheckQty(entity,out error_message))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                    return Json(result);
                }

                Sys_User currLoginUser = GetCurrentUser();
                if (!_app.Add(entity,currLoginUser.LoginAccount))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Update([FromBody]PP_PackSorting_Dto entity)
        {
            var result = new ResponseData();
            try
            {
                string error_message = string.Empty;
                if (!CheckQty(entity, out error_message))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                    return Json(result);
                }

                Sys_User user = GetCurrentUser();
                if (!_app.Edit(entity, user.LoginAccount))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除（包括批量）

        /// <summary>
        /// 删除
        /// </summary>
        /// <returns></returns>
        [HttpDelete]
        public IHttpActionResult Delete([FromBody]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.DeleteByKeys(ids, GetCurrentUser().LoginAccount);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 导出

        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri]string keyword,[FromUri]DateTime[] dateValue, [FromUri]string ProductionLineDes, [FromUri]string ProductionScheduler)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];

                var list = _app.GetList(
                    t => (string.IsNullOrEmpty(keyword)
                        || t.SerialNo.Contains(keyword)
                        || t.ProductionOrderNo.Contains(keyword)
                        || t.PackSortingNo.Contains(keyword)
                        || t.MaterialNo.Contains(keyword)
                        || t.MaterialName.Contains(keyword)
                        || t.ContractNo.Contains(keyword)
                        || t.SalesOrderNo.Contains(keyword)
                        || t.Shippers.Contains(keyword))
                        && (string.IsNullOrEmpty(ProductionLineDes) || t.ProductionLineCode.Contains(ProductionLineDes) || t.ProductionLineDes.Contains(ProductionLineDes))
                        && (string.IsNullOrEmpty(ProductionScheduler) || t.ProductionScheduler.Contains(ProductionScheduler))
                    && (t.StartTime >= fromTime && t.StartTime < toTime))
                    .Select(s => s.PackSortingNo)
                    .ToList();

                var itemsData = detailApp.GetList(t => list.Contains(t.PackSortingNo)).ToList();

                List<ExcelColumn<PP_PackSortingDetail>> columns = ExcelService.FetchDefaultColumnList<PP_PackSortingDetail>();
                string[] ignoreField = new string[]
                {
                    "ID"
                    ,"AssessmentCategory"
                    ,"AssessmentType"
                    ,"SpecialInventory"
                    ,"IsBackflush"
                    ,"IsConfirm"
                    ,"IsDelete"
                    ,"MUser"
                    ,"MTime"
                    ,"DUser"
                    ,"DTime"
                };
                List<ExcelColumn<PP_PackSortingDetail>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<PP_PackSortingDetail> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                //columns.ForEach((column) =>
                //{
                //    if (column.ColumnName == "IsPosted")
                //    {
                //        column.Formattor = ExcelExportFormatter.IsOrNoFormatter;
                //    }
                //});

                return ExportToExcelFile<PP_PackSortingDetail>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #region 打印

        /// <summary>
        /// 打印
        /// </summary>
        /// <param name="docNums">工单号</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult Print([FromUri]string[] docNums)
        {
            var result = new ResponseData();
            try
            {
                //var docStr = string.Join(",", docNums);
                //DevExpress.XtraReports.UI.XtraReport report = DevExpress.XtraReports.UI.XtraReport.FromFile(HttpContext.Current.Server.MapPath("~/") + ConfigurationManager.AppSettings["LabelTemplateUploadPath"]?.ToString() + "转子身份卡.repx", true);
                //DevExpress.DataAccess.Sql.SqlDataSource dataSource = report.DataSource as DevExpress.DataAccess.Sql.SqlDataSource;
                //DevExpress.DataAccess.Sql.QueryParameter parameter = new DevExpress.DataAccess.Sql.QueryParameter()
                //{
                //    Name = "@docNums",
                //    Type = typeof(string),
                //    Value = docStr
                //};
                //dataSource.Queries[0].Parameters[0] = parameter;
                //return base.PrintToPDF(report);
                result.Code = (int)WMSStatusCode.Failed;
                result.Message = "未提供打印模板！";
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion
    }
}
