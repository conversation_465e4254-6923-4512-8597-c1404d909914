using System.ComponentModel;

namespace HZ.WMS.Entity.Produce
{
    public class AufnrComp
    {
        [Description("消息类型")] public string ETYPE { get; set; }

        [Description("消息文本")] public string EMSG { get; set; }

        [Description("DATA_IN")] public Table001078[] DataIn { get; set; }

        [Description("DATA_OUT")] public Table001078[] DataOut { get; set; }

        public class Table001078
        {
            [Description("工单号")] public string AUFNR { get; set; }

            [Description("物料编码")] public string MATNR { get; set; }

            [Description("消息类型")] public string ETYPE { get; set; }

            [Description("消息文本")] public string EMSG { get; set; }
        }
    }
}