using HZ.Core.Http;
using HZ.Core.Office;
using HZ.WMS.Application;
using HZ.WMS.Application.PP;
using HZ.WMS.Application.Sys;
using HZ.WMS.Entity.PP;
using HZ.WMS.Entity.PP.Dto;
using HZ.WMS.Entity.Sys;
using HZ.WMS.WebAPI.Controllers;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;

namespace HZ.WMS.WebAPI.Areas.PP.Controllers
{
    /// <summary>
    /// 生产退料
    /// </summary>
    public class PP_ProductionReturnController : ApiBaseController
    {
        private PP_ProductionFeedingApp _app = new PP_ProductionFeedingApp();
        private PP_ProductionFeedingDetailApp detailApp = new PP_ProductionFeedingDetailApp();
        private PP_ProductionOrderApp orderApp = new PP_ProductionOrderApp();
        private PP_ProductionReportApp reportApp = new PP_ProductionReportApp();

        #region 查询

        /// <summary>
        /// 获取主表分页列表
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <param name="dateValue"></param>
        /// <param name="isPosted"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]string keyword, [FromUri] DateTime[] dateValue, [FromUri]bool? isPosted)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];

                var itemsData = _app.GetPageList(page,
                    t => (string.IsNullOrEmpty(keyword)
                        || t.SerialNo.Contains(keyword)
                        || t.ProductionFeedingNo.Contains(keyword)
                        || t.ProductionOrderNo.Contains(keyword)
                        || t.MaterialNo.Contains(keyword)
                        || t.MaterialName.Contains(keyword)
                        || t.ContractNo.Contains(keyword)
                        || t.SalesOrderNo.Contains(keyword)
                        || t.Shippers.Contains(keyword))
                        && (t.CTime >= fromTime && t.CTime < toTime)
                        && (isPosted == null || t.IsPosted == isPosted)
                        && t.MovementType == "262")
                    .ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 获取退料列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetDetailList([FromUri]Pagination page, [FromUri]string keyword, [FromUri]string ComponentCode, [FromUri] DateTime[] dateValue)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];

                var itemsData = detailApp.GetDetailSource(page, keyword, ComponentCode, fromTime, toTime);
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 增加

        private bool CheckQty(PP_ProductionFeeding_Dto productionFeeding)
        {
            var mark = true;
            foreach (var item in productionFeeding.DetailList)
            {
                //已投数量
                var feedSum = detailApp.GetListDistinct(w => w.ProductionOrderNo == item.ProductionOrderNo && w.ComponentCode == item.ComponentCode && w.ComponentLineNo == item.ComponentLineNo && w.MovementType == "261").Sum(s => s.DemandQty);
                if (productionFeeding.MovementType == "261")//投料
                {
                    //订单总需求量
                    var orderSum = orderApp.GetSapOrderDetailList(w => w.ProductionOrderNo == item.ProductionOrderNo && w.ComponentCode == item.ComponentCode && w.ComponentLineNo == item.ComponentLineNo && string.IsNullOrEmpty(w.IsDeleteSAP)).Sum(s => s.DemandQty);
                    mark = feedSum + item.DemandQty <= orderSum;
                    if (!mark) break;
                }
                else//退料
                {
                    //已退数量
                    var returnSum = detailApp.GetListDistinct(w => w.ProductionOrderNo == item.ProductionOrderNo && w.ComponentCode == item.ComponentCode && w.ComponentLineNo == item.ComponentLineNo && w.MovementType == "262").Sum(s => s.DemandQty);
                    mark = returnSum + item.DemandQty <= feedSum;
                    if (!mark) break;
                }
            }
            return mark;
        }

        [HttpPost]
        public IHttpActionResult Add([FromBody]PP_ProductionFeeding_Dto productionFeeding)
        {
            var result = new ResponseData();
            try
            {
                if (!CheckQty(productionFeeding))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "请核实本次投料/退料数量";
                    return Json(result);
                }

                Sys_User user = GetCurrentUser();
                var group = productionFeeding.DetailList.GroupBy(g => g.ProductionOrderNo);
                foreach (var entity in group)
                {
                    var ProductionFeedingNo = base.GenerateDocNum(DocType.PP, DocFixedNumDef.PP_ProductionFeeding);
                    var order = orderApp.GetSapOrderList(w => w.ProductionOrderNo == entity.Key).ToList().FirstOrDefault();
                    var feeding = new PP_ProductionFeeding
                    {
                        ScanningCode = "",
                        SerialNo = "",
                        ProductionFeedingNo = ProductionFeedingNo,
                        ProductionOrderNo = entity.Key,
                        FactoryCode = order?.FactoryCode,
                        MaterialNo = order?.MaterialNo,
                        MaterialName = order?.MaterialName,
                        MovementType = productionFeeding.MovementType,
                        OrderType = order?.OrderType,
                        OrderQty = order?.OrderQty ?? 0,
                        Unit = order?.Unit,
                        ContractNo = order?.ContractNo,
                        SalesOrderNo = order?.SalesOrderNo,
                        Shippers = order?.Shippers,
                        ProductionLineCode = order?.ProductionLineCode,
                        ProductionLineDes = order?.ProductionLineDes,
                        ProductionScheduler = order?.ProductionScheduler,
                        DeliveryTime = order?.DeliveryTime,
                        StartTime = order?.StartTime,
                        ManualPostTime = productionFeeding.ManualPostTime,
                        Remark = productionFeeding.Remark,
                        CUser = user.LoginAccount,
                    };
                    var detailList = productionFeeding.DetailList;
                    detailList.ForEach(item =>
                    {
                        item.ProductionFeedingNo = ProductionFeedingNo;
                        item.MovementType = productionFeeding.MovementType;
                        item.ManualPostTime = productionFeeding.ManualPostTime;
                        item.CUser = user.LoginAccount;
                    });

                    if (!_app.Add(feeding, detailList))
                    {
                        result.Code = (int)WMSStatusCode.Failed;
                    }
                    else
                    {
                        //是否自动过账判定
                        if (new Sys_SwithConfigApp().IsPPProductionReturnAutoPost)
                        {
                            string error_message = "";
                            bool bSubmit = _app.DoPost(new List<PP_ProductionFeeding> { feeding }, user.LoginAccount, out error_message);

                            if (!bSubmit)
                            {
                                result.Message = "保存成功；过账失败，失败原因：" + error_message;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 导出

        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri]Pagination page, [FromUri]string keyword, [FromUri] DateTime[] dateValue, [FromUri]bool? isPosted)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];

                var list = _app.GetList(
                    t => (string.IsNullOrEmpty(keyword)
                        || t.SerialNo.Contains(keyword)
                        || t.ProductionFeedingNo.Contains(keyword)
                        || t.ProductionOrderNo.Contains(keyword)
                        || t.MaterialNo.Contains(keyword)
                        || t.MaterialName.Contains(keyword)
                        || t.ContractNo.Contains(keyword)
                        || t.SalesOrderNo.Contains(keyword)
                        || t.Shippers.Contains(keyword))
                        && (t.CTime >= fromTime && t.CTime < toTime)
                        && (isPosted == null || t.IsPosted == isPosted)
                        && t.MovementType == "262")
                    .Select(s => s.ProductionFeedingNo)
                    .ToList();

                var itemsData = detailApp.GetList(t => list.Contains(t.ProductionFeedingNo)).ToList();

                List<ExcelColumn<PP_ProductionFeedingDetail>> columns = ExcelService.FetchDefaultColumnList<PP_ProductionFeedingDetail>();
                string[] ignoreField = new string[]
                {
                    "ID"
                    ,"MovementType"
                    ,"IsPosted"
                    ,"PostUser"
                    ,"PostTime"
                    ,"ManualPostTime"
                    ,"SapDocNum"
                    ,"SapLine"
                    ,"IsDelete"
                    ,"MUser"
                    ,"MTime"
                    ,"DUser"
                    ,"DTime"
                };
                List<ExcelColumn<PP_ProductionFeedingDetail>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<PP_ProductionFeedingDetail> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                //columns.ForEach((column) =>
                //{
                //    if (column.ColumnName == "IsPosted")
                //    {
                //        column.Formattor = ExcelExportFormatter.IsOrNoFormatter;
                //    }
                //});

                return ExportToExcelFile<PP_ProductionFeedingDetail>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #region 导入

        [HttpPost]
        public IHttpActionResult ImportExcelToData([FromBody]DataTable entitys)
        {
            var result = new ResponseData();
            try
            {
                if (entitys == null || entitys.Rows.Count < 1)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "数据不能为空！";
                    return Json(result);
                }

                Sys_User user = GetCurrentUser();
                List<PP_ProductionFeedingDetail> feedingDetails = new List<PP_ProductionFeedingDetail>();
                foreach (DataRow item in entitys.Rows)
                {
                    feedingDetails.Add(new PP_ProductionFeedingDetail
                    {
                        ProductionOrderNo = item[0].ToString(),
                        ReservedNo = item[1].ToString(),
                        ComponentLineNo = Convert.ToDecimal(item[2] ?? 0),
                        ComponentCode = item[3].ToString(),
                        MaterialName = item[4].ToString(),
                        DemandQty = Convert.ToDecimal(item[5] ?? 0),
                        ComponentUnit = item[6].ToString(),
                        FactoryCode = item[7].ToString(),
                        DeliverLocation = item[8].ToString(),
                        SalesOrderNo = item[9].ToString(),
                        SalesOrderLineNo = Convert.ToDecimal(item[10]?.ToString() == "" ? 0 : item[10]),
                        AssessmentCategory = item[11].ToString(),
                        AssessmentType = item[12].ToString(),
                        SpecialInventory = item[13].ToString(),
                        ManualPostTime = Convert.ToDateTime(item[14] ?? DateTime.Now),
                        Remark = item[15].ToString(),
                        MovementType = "262",
                        IsBackflush = "",
                        CUser = user.LoginAccount,
                    });
                }
                if (feedingDetails.Any(
                    a => string.IsNullOrEmpty(a.ProductionOrderNo)
                    || string.IsNullOrEmpty(a.ReservedNo)
                    || string.IsNullOrEmpty(a.ComponentCode)
                    || string.IsNullOrEmpty(a.DeliverLocation)
                    || a.DemandQty == 0
                    ))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = $"请检查数据完善性！";
                    return Json(result);
                }
                //分组生成主表
                var feedList = new List<PP_ProductionFeeding>();
                var group = feedingDetails.GroupBy(g => g.ProductionOrderNo);
                foreach (var item in group)
                {
                    var order = orderApp.GetFirstEntity(f => f.ProductionOrderNo == item.Key);
                    if (order == null)
                    {
                        order = reportApp.GetOrderNoSerialNoByNo(item.Key.ToString())?.ToList().FirstOrDefault();
                        //order = reportApp.GetOrderNoSerialNo().FirstOrDefault(f => f.ProductionOrderNo == item.Key);
                    }
                    if (order == null)
                    {
                        result.Code = (int)WMSStatusCode.Failed;
                        result.Message = $"{item.Key}工单无效！";
                        return Json(result);
                    }
                    var feeding = new PP_ProductionFeeding
                    {
                        //SerialNo = "",
                        ProductionFeedingNo = base.GenerateDocNum(DocType.PP, DocFixedNumDef.PP_ProductionFeeding),
                        ProductionOrderNo = item.Key,
                        FactoryCode = order?.FactoryCode,
                        MaterialNo = order?.MaterialNo,
                        MaterialName = order?.MaterialName,
                        MovementType = "262",
                        OrderType = order?.OrderType,
                        OrderQty = order?.OrderQty ?? 0,
                        Unit = order?.Unit,
                        ContractNo = order?.ContractNo,
                        SalesOrderNo = order?.SalesOrderNo,
                        Shippers = order?.Shippers,
                        ProductionLineCode = order?.ProductionLineCode,
                        ProductionLineDes = order?.ProductionLineDes,
                        ProductionScheduler = order?.ProductionScheduler,
                        DeliveryTime = order?.DeliveryTime,
                        StartTime = order?.StartTime,
                        ManualPostTime = item.ToList().FirstOrDefault().ManualPostTime ?? DateTime.Now,
                        Remark = order?.Remark,
                        CUser = user.LoginAccount,
                    };
                    feedList.Add(feeding);

                    foreach (var entity in item)
                    {
                        //判断物料是否有效，物料数量是否正确
                        var detail = orderApp.GetSapOrderDetailList(w => w.ProductionOrderNo == entity.ProductionOrderNo && w.ComponentCode == entity.ComponentCode && w.ReservedNo.ToString() == entity.ReservedNo).ToList().FirstOrDefault();
                        if (detail == null)
                        {
                            result.Code = (int)WMSStatusCode.Failed;
                            result.Message = $"生产订单{entity.ProductionOrderNo}的物料{entity.ComponentCode}不存在SAP中间库！";
                            return Json(result);
                        }

                        //校验物料数量
                        //已投数量
                        var feedSum = detailApp.GetListDistinct(w => w.ProductionOrderNo == entity.ProductionOrderNo && w.ComponentCode == entity.ComponentCode && w.ComponentLineNo == entity.ComponentLineNo && w.MovementType == "261").Sum(s => s.DemandQty);
                        //已退数量
                        var returnSum = detailApp.GetListDistinct(w => w.ProductionOrderNo == entity.ProductionOrderNo && w.ComponentCode == entity.ComponentCode && w.ComponentLineNo == entity.ComponentLineNo && w.MovementType == "262").Sum(s => s.DemandQty);
                        if (returnSum + entity.DemandQty > feedSum)
                        {
                            result.Code = (int)WMSStatusCode.Failed;
                            result.Message = $"生产订单{entity.ProductionOrderNo}的物料{entity.ComponentCode},退料总数不能超过投料数量";
                            return Json(result);
                        }
                        entity.ProductionFeedingNo = feeding?.ProductionFeedingNo;
                    }
                }

                if (string.IsNullOrEmpty(result.Message))
                {
                    _app.InsertWithTran(feedList);
                    detailApp.InsertWithTran(feedingDetails);
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 下载模板

        [HttpGet]
        public IHttpActionResult DownExcelModel()
        {
            var result = new ResponseData();
            try
            {
                string modelName = "工单退料-导入模板";
                var itemsData = new List<PP_ProductionFeedingDetail>();
                List<ExcelColumn<PP_ProductionFeedingDetail>> columns = ExcelService.FetchDefaultColumnList<PP_ProductionFeedingDetail>();
                string[] ignoreField = new string[]
                {
                    "ID"
                    ,"ScanningCode"
                    ,"ProductionFeedingNo"
                    ,"FeedingLineNo"
                    ,"MovementType"
                    ,"IsBackflush"
                    ,"IsPosted"
                    ,"PostUser"
                    ,"PostTime"
                    ,"SapDocNum"
                    ,"SapLine"
                    ,"IsDelete"
                    ,"CUser"
                    ,"CTime"
                    ,"MUser"
                    ,"MTime"
                    ,"DUser"
                    ,"DTime"
                };
                List<ExcelColumn<PP_ProductionFeedingDetail>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<PP_ProductionFeedingDetail> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                //columns.ForEach((column) =>
                //{
                //    if (column.ColumnName == "IsPosted")
                //    {
                //        column.Formattor = ExcelExportFormatter.IsOrNoFormatter;
                //    }
                //});

                return ExportToExcelFile<PP_ProductionFeedingDetail>(itemsData, columns, $"{modelName}_{GetCurrentUser().UserName}");
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion
    }
}
