using SqlSugar;
using System;
using System.Collections.Generic;
using HZ.WMS.Application.Sys;
using HZ.WMS.Entity.SD;
using System.Linq;
using HZ.WMS.Entity;
using HZ.WMS.Application.MD;
using HZ.WMS.Entity.SAP;
using System.Data;
using HZ.WMS.Application.SAP;
using HZ.WMS.Entity.SD.ViewModel;
using static HZ.WMS.Application.SD.SD_ShippingPlanApp;
using HZ.Core.Http;
using System.Linq.Expressions;
using HZ.WMS.Entity.MD;
using System.Text;
using AOS.OMS.Entity.Sale;
using HZ.WMS.Entity.EAP;
using HZ.WMS.Entity.Sys;

namespace HZ.WMS.Application.SD
{
    /// <summary>
    /// 销售交货
    /// 没有out_message消息的函数，意味着只在最前端做未处理异常捕捉
    /// </summary>
    public class SD_DeliveryScanApp : BaseApp<SD_DeliveryScan>
    {
        #region 初始化

        BaseApp<BaseEntity> _baseApp = new BaseApp<BaseEntity>();
        SD_ShippingPlanApp _planapp = new SD_ShippingPlanApp();
        SD_ShippingPlanDetailApp _plandetailapp = new SD_ShippingPlanDetailApp();
        MD_StockApp _stockapp = new MD_StockApp();


        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public SD_DeliveryScanApp() : base()
        {
        }

        #endregion

        #endregion

        #region 获取EAP出厂编号

        public List<SD_DeliveryScan> HandleEapSerialNo(IEnumerable<SD_DeliveryScan> assignSerialItems)
        {
            if (assignSerialItems != null)
            {
                var assignSerialList = assignSerialItems.ToList();

                var pageNo = assignSerialList.Count() / 500;
                if (assignSerialList.Count() % 500 > 0)
                {
                    pageNo = pageNo + 1;
                }

                List<SGEAP_CUS_Order> cusOrderList = new List<SGEAP_CUS_Order>();

                for (int i = 0; i < pageNo; i++)
                {
                    int startNo = i * 500;
                    int endNo = (i + 1) * 500;
                    if (i == pageNo - 1)
                    {
                        endNo = assignSerialList.Count;
                    }

                    List<string> contractNoList = new List<string>();
                    List<string> orderNoList = new List<string>();
                    for (int j = startNo; j < endNo; j++)
                    {
                        contractNoList.Add(assignSerialList[j].CONT);
                        orderNoList.Add(assignSerialList[j].CustomerOrderNum);
                    }

                    cusOrderList.AddRange(DbContextForEAP.Queryable<SGEAP_CUS_Order>().Where(t =>
                        contractNoList.Contains(t.ContractNo) && orderNoList.Contains(t.OrderNo)).ToList());
                }

                Dictionary<string, string> dic = new Dictionary<string, string>();
                foreach (SGEAP_CUS_Order cusOrder in cusOrderList)
                {
                    if (!dic.ContainsKey(cusOrder.ContractNo + cusOrder.OrderNo))
                    {
                        dic.Add(cusOrder.ContractNo + cusOrder.OrderNo, cusOrder.SerialNo);
                    }
                }

                foreach (SD_DeliveryScan assignSerialNo in assignSerialList)
                {
                    if (dic.ContainsKey(assignSerialNo.CONT + assignSerialNo.CustomerOrderNum))
                    {
                        assignSerialNo.EapSerialNo = dic[assignSerialNo.CONT + assignSerialNo.CustomerOrderNum];
                    }
                }

                return assignSerialList;
            }

            return new List<SD_DeliveryScan>();
        }

        #endregion

        #region 获取价格

        public List<SD_DeliveryScan> HandlePrice(List<SD_DeliveryScan> assignSerialItems)
        {
            if (assignSerialItems != null)
            {
                var assignSerialList = assignSerialItems.ToList();

                var pageNo = assignSerialList.Count() / 500;
                if (assignSerialList.Count() % 500 > 0)
                {
                    pageNo = pageNo + 1;
                }

                List<SD_Sale_OrderDetails> cusOrderList = new List<SD_Sale_OrderDetails>();

                for (int i = 0; i < pageNo; i++)
                {
                    int startNo = i * 500;
                    int endNo = (i + 1) * 500;
                    if (i == pageNo - 1)
                    {
                        endNo = assignSerialList.Count;
                    }
                    List<string> contractNoList = new List<string>();
                    List<string> orderNoList = new List<string>();
                    for (int j = startNo; j < endNo; j++)
                    {
                        contractNoList.Add(assignSerialList[j].CONT);
                        orderNoList.Add(assignSerialList[j].CustomerOrderNum);
                    }

                    var detailList = DbContextForOMS.Queryable<SD_Sale_OrderDetails>().Where(t => contractNoList.Contains(t.ContractNum) && orderNoList.Contains(t.CustomerOrderNum))?.ToList();
                    if (detailList != null && detailList.Count > 0)
                    {
                        cusOrderList.AddRange(detailList);
                    }
                }

                Dictionary<string, decimal> dic = new Dictionary<string, decimal>();
                Dictionary<string, string> projectDescDic = new Dictionary<string, string>();
                foreach (SD_Sale_OrderDetails cusOrder in cusOrderList)
                {
                    if (!dic.ContainsKey(cusOrder.ContractNum + cusOrder.CustomerOrderNum))
                    {
                        dic.Add(cusOrder.ContractNum + cusOrder.CustomerOrderNum, cusOrder.Price.HasValue ? cusOrder.Price.Value : new decimal(0));
                        projectDescDic.Add(cusOrder.ContractNum + cusOrder.CustomerOrderNum, cusOrder.ProjectDisc);
                    }
                }

                foreach (SD_DeliveryScan assignSerialNo in assignSerialList)
                {
                    if (dic.ContainsKey(assignSerialNo.CONT + assignSerialNo.CustomerOrderNum))
                    {
                        assignSerialNo.Price = dic[assignSerialNo.CONT + assignSerialNo.CustomerOrderNum];
                    }
                    if (projectDescDic.ContainsKey(assignSerialNo.CONT + assignSerialNo.CustomerOrderNum))
                    {
                        assignSerialNo.Project = projectDescDic[assignSerialNo.CONT + assignSerialNo.CustomerOrderNum];
                    }
                }
                return assignSerialList;
            }

            return new List<SD_DeliveryScan>();
        }

        #endregion

        #region 获取姓名

        public List<SD_DeliveryScan> HandleUserName(List<SD_DeliveryScan> itemsData)
        {
            if (itemsData != null)
            {
                var assignSerialList = itemsData.ToList();

                var pageNo = assignSerialList.Count() / 500;
                if (assignSerialList.Count() % 500 > 0)
                {
                    pageNo = pageNo + 1;
                }

                List<Sys_User> userList = new List<Sys_User>();

                for (int i = 0; i < pageNo; i++)
                {
                    int startNo = i * 500;
                    int endNo = (i + 1) * 500;
                    if (i == pageNo - 1)
                    {
                        endNo = assignSerialList.Count;
                    }

                    List<string> userIds = new List<string>();
                    for (int j = startNo; j < endNo; j++)
                    {
                        userIds.Add(assignSerialList[j].CUser);
                    }

                    var detailList = DbContext.Queryable<Sys_User>().Where(t => userIds.Contains(t.LoginAccount))?.ToList();
                    if (detailList != null && detailList.Count > 0)
                    {
                        userList.AddRange(detailList);
                    }
                }

                var userMap = userList.ToDictionary(t => t.LoginAccount, t => t.UserName);
                foreach (SD_DeliveryScan assignSerialNo in assignSerialList)
                {
                    if (userMap.ContainsKey(assignSerialNo.CUser))
                    {
                        assignSerialNo.CUserName = userMap[assignSerialNo.CUser];
                    }
                }

                return assignSerialList;
            }

            return new List<SD_DeliveryScan>();
        }

        #endregion

        #region 处理联系人手机号

        public List<SD_DeliveryScan> HandlePhone(List<SD_DeliveryScan> itemsData)
        {
            if (itemsData != null)
            {
                var assignSerialList = itemsData.ToList();

                var pageNo = assignSerialList.Count() / 500;
                if (assignSerialList.Count() % 500 > 0)
                {
                    pageNo = pageNo + 1;
                }

                List<SD_ShippingPlanDetail> cusOrderList = new List<SD_ShippingPlanDetail>();

                for (int i = 0; i < pageNo; i++)
                {
                    int startNo = i * 500;
                    int endNo = (i + 1) * 500;
                    if (i == pageNo - 1)
                    {
                        endNo = assignSerialList.Count;
                    }

                    List<string> contractNoList = new List<string>();
                    List<string> orderNoList = new List<string>();
                    for (int j = startNo; j < endNo; j++)
                    {
                        contractNoList.Add(assignSerialList[j].CONT);
                        orderNoList.Add(assignSerialList[j].CustomerOrderNum);
                    }

                    var detailList = DbContext.Queryable<SD_ShippingPlanDetail>().Where(t =>
                        contractNoList.Contains(t.CONT) && orderNoList.Contains(t.CustomerOrderNum))?.ToList();
                    if (detailList != null && detailList.Count > 0)
                    {
                        cusOrderList.AddRange(detailList);
                    }
                }

                Dictionary<string, SD_ShippingPlanDetail> dic = new Dictionary<string, SD_ShippingPlanDetail>();
                foreach (SD_ShippingPlanDetail cusOrder in cusOrderList)
                {
                    if (!dic.ContainsKey(cusOrder.CONT + cusOrder.CustomerOrderNum))
                    {
                        dic.Add(cusOrder.CONT + cusOrder.CustomerOrderNum, cusOrder);
                    }
                }

                foreach (SD_DeliveryScan assignSerialNo in assignSerialList)
                {
                    if (dic.ContainsKey(assignSerialNo.CONT + assignSerialNo.CustomerOrderNum))
                    {
                        assignSerialNo.Telephone = dic[assignSerialNo.CONT + assignSerialNo.CustomerOrderNum].Telephone;
                        assignSerialNo.Contact = dic[assignSerialNo.CONT + assignSerialNo.CustomerOrderNum].Contact;
                        assignSerialNo.Project = dic[assignSerialNo.CONT + assignSerialNo.CustomerOrderNum].Project;
                        assignSerialNo.CustomerAdd = string.IsNullOrEmpty(assignSerialNo.CustomerAdd)
                            ? dic[assignSerialNo.CONT + assignSerialNo.CustomerOrderNum].CustomerAdd
                            : assignSerialNo.CustomerAdd;
                    }
                }

                return assignSerialList;
            }

            return new List<SD_DeliveryScan>();
        }

        #endregion

        #region PC

        /// <summary>
        /// 查询销售发运计划信息
        /// </summary>
        /// <param name="DocNum">计划单号</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public List<SD_DeliveryMain_View> GetSDDeliveryMainView(Pagination page)
        {
            var query = DbContext.Queryable<SD_DeliveryMain_View>()
                .OrderBy("DocNum desc");
            return query.ToPageList(page.PageNumber, page.PageSize);
        }

        /// <summary>
        /// 查询销售发运计划信息
        /// </summary>
        /// <param name="DocNum">计划单号</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public List<SD_DeliveryMain_View> GetSDDeliveryMainViewForSQL(Pagination page, string strCodes)
        {
            var list = new List<SD_DeliveryMain_View>();
            //string sql = @"SELECT *FROM dbo.SD_DeliveryMain_View WHERE DocNum IN(SELECT * FROM dbo.Fun_SplitStr(@lines,',')) ";
            string sql = @"SELECT * FROM dbo.SD_DeliveryMain_View A 
                            LEFT JOIN (SELECT * FROM dbo.Fun_SplitStr(@lines,',')) b ON b.code=a.DocNum
                            WHERE b.code IS NOT NULL ";
            SugarParameter[] param =
            {
                new SugarParameter("@lines",strCodes)
            };
            var itemPageData = new List<SD_DeliveryMain_View>();
            var itemsData = DbContext.Ado.SqlQuery<SD_DeliveryMain_View>(sql, param).ToList();
            int total = itemsData.Count();
            page.Total = total;
            if (page.PageSize > total)
            {
                itemPageData = itemsData.Skip(page.PageSize * (page.PageNumber - 1)).Take(total).ToList();
            }
            else
            {
                itemPageData = itemsData.Skip(page.PageSize * (page.PageNumber - 1)).Take(page.PageSize).ToList();
            }

            return itemPageData;
        }

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="opUser"></param>
        /// <param name="error_message"></param>
        public bool Delete(string[] DocNums, string opUser, out string error_message)
        {
            error_message = "";
            bool bDeleted = true;
            //List<SD_DeliveryScan> entities = GetListByKeys(ids);
            var entities = GetList(x => DocNums.Contains(x.DocNum)).ToList();
            if (entities.Any(x => x.IsPosted == true))
            {
                error_message = "已过帐信息不允许删除";
                return false;
            }

            try
            {
                DbContext.Ado.BeginTran();

                Delete(entities);

                DbContext.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                bDeleted = false;
                error_message = ex.Message;
                DbContext.Ado.RollbackTran();
            }

            return bDeleted;
        }

        #region 删除校验

        /// <summary>
        /// 删除校验
        /// </summary>
        /// <param name="entities"></param>
        /// <param name="error_message"></param>
        /// <returns></returns>
        private bool CheckDelete(List<SD_DeliveryScan> entities, out string error_message)
        {
            bool isPass = true;
            error_message = "";
            //foreach (SD_DeliveryScan sd in entities)
            //{
            //    if (sd.IsDelivery == true)
            //    {
            //        error_message = "已确认装箱数据不允许删除!";
            //        isPass = false;
            //        break;
            //    }
            //}

            return isPass;
        }

        #endregion

        #endregion

        #region 过账

        /// <summary>
        /// 过账
        /// 销售发货过账：
        ///    根据销售订单生成一个大的发货任务，根据WMS波次发货任务，分批确认
        ///    本过账只针对一个销售订单
        /// </summary>
        /// <param name="entities">页面箱子列表</param>
        /// <param name="user">过账用户</param>
        /// <param name="error_message"></param>
        /// <returns></returns>
        public bool DoPost(List<SD_DeliveryScan> entities, string user, out string error_message)
        {
            //if (entities.Any(x => x.IsPosted == true))
            //{
            //    error_message = "已过帐信息不允许重复过账";
            //    return false;
            //}
            MD_StockApp _stockApp = new MD_StockApp();
            Sys_SAPCompanyInfoApp _SAPCompanyInfoApp = new Sys_SAPCompanyInfoApp();
            error_message = "";
            try
            {
                //多条数据后根据单号进行分组
                var mainInfoList = entities.GroupBy(g => new
                {
                    g.DocNum,
                    g.BaseNum
                }).Select(q => new
                {
                    DocNum = q.Key.DocNum,
                    BaseNum = q.Key.BaseNum
                });

                DateTime time = DateTime.Now;
                //按单号分组
                foreach (var mainInfo in mainInfoList)
                {
                    ZFGWMS001 zfgwms = new ZFGWMS001();
                    List<ZFGWMS001> zfgwmsList = new List<ZFGWMS001>();

                    int Line = 0;
                    List<SD_DeliveryScan> conditionList = entities
                        .Where(x => x.DocNum == mainInfo.DocNum && x.BaseNum == mainInfo.BaseNum)?.ToList();

                    foreach (SD_DeliveryScan condition in conditionList)
                    {
                        List<ZFGWMS001_1> zfgwms_1List = new List<ZFGWMS001_1>();
                        condition.Line = Line += 1;
                        //eq.Line = Line;
                        ZFGWMS001_1 zfgwms_1 = new ZFGWMS001_1();

                        DateTime ManualPostTime =
                            new SAPApp().GetSAPpostTime(Convert.ToDateTime(condition.ManualPostTime));

                        zfgwms.VBELN = condition.BaseNum;
                        zfgwms.LFDAT = Convert.ToDateTime(Convert.ToDateTime(ManualPostTime).ToString("yyyy-MM-dd"));
                        zfgwms.WAUHR = Convert.ToDateTime(Convert.ToDateTime(ManualPostTime).ToString("HH:mm:ss")); //待定

                        zfgwms_1.LGORT = condition.WhsCode;
                        zfgwms_1.LFIMG = condition.DeliveryScanQty;
                        zfgwms_1.VGPOS = condition.BaseLine;

                        zfgwms_1.ZJHBS = condition.DocNum + "-" + condition.Line.ToString();
                        zfgwms_1.ZEREMARK = condition.Remark;
                        zfgwms_1List.Add(zfgwms_1);

                        condition.SapLine = condition.SapLine ?? 0 + 1; //记录过账次数

                        bool ispost = false;
                        string rtnErrMsg = "";
                        //purchaseReceipt.CompanyCode 公司代码
                        //查询Sap账期时间

                        List<SAPRETURN> saplist = _SAPCompanyInfoApp.ZFGWMS001("001", zfgwms, ManualPostTime,
                            zfgwms_1List, out ispost, out error_message);
                        if (saplist != null && saplist.Count > 0)
                        {
                            //List<SD_DeliveryScan> queryList = new List<SD_DeliveryScan>();
                            foreach (SAPRETURN sap in saplist)
                            {
                                //var querys = conditionList.Where(x => x.DocNum == mainInfo.DocNum && x.BaseNum == sap.basenum).ToList();
                                //foreach (var query in querys)
                                //{
                                condition.IsPosted = true;
                                condition.PostUser = user;
                                condition.PostTime = time;
                                condition.MUser = user;
                                condition.MTime = time;
                                condition.SapDocNum = sap.sapDocNum;
                                condition.ManualPostTime = ManualPostTime;
                                condition.SAPmark = "S";
                                condition.SAPmessage = "";

                                Update(condition);

                                //if (Update(condition) > 0)
                                //{
                                //待完善
                                //string stockMsg = string.Empty;

                                ////出库
                                //if (!_stockApp.StockOutBySale(condition.BarCode, condition.ItemCode, condition.BaseNum, Convert.ToInt32(condition.BaseLine), condition.BinLocationCode, condition.DeliveryScanQty, user, out stockMsg))
                                //{
                                //    //error_message = stockMsg;
                                //    //return false;
                                //}
                                //}
                                //}
                            }
                        }
                        else
                        {
                            //List<SD_DeliveryScan> listpo = conditionList.Where(x => x.DocNum == mainInfo.DocNum && x.BaseNum == mainInfo.BaseNum).ToList();
                            //foreach (SD_DeliveryScan info in listpo)
                            //{
                            //    info.SAPmark = "E";
                            //    info.SAPmessage = error_message;
                            //}
                            condition.SAPmark = "E";
                            condition.SAPmessage = error_message;
                            condition.MUser = user;
                            condition.MTime = DateTime.Now;
                            Update(condition);


                            //error_message = "单号：" + mainInfo.DocNum + "，过账失败，失败原因：" + error_message;
                            //return ispost;
                        }
                    }
                }

                if (string.IsNullOrEmpty(error_message))
                    error_message = "过账成功";
                return true;
            }
            catch (Exception ex)
            {
                error_message = ex.Message;
                return false;
            }
        }

        #endregion

        /// <summary>
        /// 取消过账
        /// </summary>
        /// <param name="entities">采购收货集合</param>
        /// <param name="opUser">当前过账用户</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool PassPost(string[] DocNums, string opUser, out string error_message)
        {
            error_message = "";

            MD_StockApp _stockApp = new MD_StockApp();
            Sys_SAPCompanyInfoApp _SAPCompanyInfoApp = new Sys_SAPCompanyInfoApp();
            var entities = GetList(x => DocNums.Contains(x.DocNum) && x.IsPosted == true).ToList();

            //多条数据后根据单号进行分组
            var mainInfoList = entities.GroupBy(g => new
            {
                g.SapDocNum
            }).Select(q => new
            {
                SapDocNum = q.Key.SapDocNum
            });

            //按订单号分组
            foreach (var mainInfo in mainInfoList)
            {
                var conditionList = entities.Where(x => x.SapDocNum == mainInfo.SapDocNum)?.ToList();

                try
                {
                    bool ispost = false;
                    DateTime time = DateTime.Now;

                    //查询Sap账期时间
                    DateTime ManualPostTime =
                        new SAPApp().GetSAPpostTime(Convert.ToDateTime(conditionList[0].ManualPostTime));
                    //purchaseReceipt.CompanyCode 公司代码
                    var saplist = _SAPCompanyInfoApp.ZFGWMS025("001", mainInfo.SapDocNum, ManualPostTime, out ispost,
                        out error_message);
                    if (string.IsNullOrEmpty(error_message))
                    {
                        foreach (var query in conditionList)
                        {
                            query.IsDelete = true;
                            query.DUser = opUser;
                            query.DTime = time;
                            query.SAPmark = "已冲销";

                            //待完善，完善内容 事务，库存缺少销售订单信息。
                            if (Update(query) > 0) //更新成功后更新库存
                            {
                                //更新库存
                            }
                        }

                        //DbContext.Session.CommitTransaction();
                    }
                    else
                    {
                        error_message = "单号[" + conditionList[0].DocNum + "],冲销过账失败，失败原因：" + error_message;
                        return false;
                    }
                }
                catch (Exception ex)
                {
                    error_message = "单号[" + conditionList[0].DocNum + "],冲销过账失败，失败原因：" + ex.Message;
                    return false;
                }
            }

            if (string.IsNullOrEmpty(error_message))
            {
                error_message = "冲销过账成功";
            }

            return true;
        }


        #region 导入

        /// <summary>
        /// 导入
        /// </summary>
        /// <param name="excelList">接收的集合</param>
        /// <param name="opUser">当前用户</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool ImportExcelToBaseData(List<SD_DeliveryScanImport> excelList, string opUser, out string error_message)
        {
            error_message = "";
            if (!ValidateCheck(excelList))
            {
                return false;
            }
            var flag = true;
            DateTime date = DateTime.Now;
            SD_ShippingPlanDetailApp _detailApp = new SD_ShippingPlanDetailApp();
            List<SD_DeliveryScan> Addlist = new List<SD_DeliveryScan>();

            List<SD_DeliveryScan> Updatelist = new List<SD_DeliveryScan>();
            List<SD_ShippingPlanDetail> updateShippingPlanDetails = new List<SD_ShippingPlanDetail>();
            //物料
            var ItemCodes = excelList.Select(x => x.物料编号.Trim()).Distinct().ToArray();
            var itemcodes = DbContextForSAP.Queryable<XZ_SAP_MARC>().Where(x => ItemCodes.Contains(x.MATNR))?.ToList();

            //仓库编码
            var WhsCodes = excelList.Select(x => x.仓库编号).Distinct().ToArray();
            var whscodes = new MD_BinLocationApp().GetList(x => WhsCodes.Contains(x.WhsCode))?.ToList();
            var list = excelList.GroupBy(g => new
            {
                g.销售订单号,
                g.销售单行号
            }).Select(q => new
            {
                销售订单号 = q.Key.销售订单号,
                销售单行号 = q.Key.销售单行号,
                交货数量 = q.Sum(s => s.交货数量)
            }).ToList();
            foreach (var x in list)
            {
                if (string.IsNullOrEmpty(x.销售订单号))
                {
                    error_message = "销售订单信息不能为空";
                    return false;
                }

                if (x.交货数量 == null)
                {
                    error_message = "销售数量不能为空";
                    return false;
                }

                if (x.交货数量 <= 0)
                {
                    error_message = "销售数量必须大于1";
                    return false;
                }
                var query = DbContext.Queryable<SD_ShippingPlanForVBAK_View>()
                    .Where(t => t.VBELN == x.销售订单号 && t.POSNR == x.销售单行号)?.ToList();
                if (query != null && query.Count > 0)
                {
                    if (x.交货数量 > query[0].KWMENG)
                    {
                        error_message = "销售订.单号【" + x.销售订单号 + "】行号【" + x.销售单行号 + "】,交货数量【" + x.交货数量 + "】不能超过订单数量【" +
                                        query[0].KWMENG + "】!";
                        return false;
                    }
                }
            }

            try
            {
                //暂时只进行插入操作，有需要在做更新操作
                foreach (var x in excelList)
                {
                    SD_ShippingPlanDetail plan = _detailApp.GetList(j => j.SalesOrderNumber == x.销售订单号 && j.SalesLine == x.销售单行号)?.ToList().FirstOrDefault();

                    if (plan == null)
                    {
                        error_message = "销售订单【" + x.销售订单号 + "-" + x.销售单行号 + "】找不到对应的发运计划";
                        return false;
                    }

                    plan.IsDeliveryImport = 1;
                    updateShippingPlanDetails.Add(plan);

                    if (plan.SalesOrderType.Contains("主机"))
                    {
                        //主机订单
                        SD_DeliveryScan info1 = GetList(j => j.BaseNum == x.销售订单号 && j.BaseLine == x.销售单行号)?.ToList().FirstOrDefault();
                        if (info1 == null)
                        {
                            SD_DeliveryScan info = new SD_DeliveryScan();
                            string DocNum = _baseApp.GetNewDocNum(DocType.SD, DocFixedNumDef.SD_DeliveryScan);
                            info.DocNum = DocNum;
                            info.IsDelete = false;
                            info.IsPosted = false;
                            info.CUser = opUser;
                            info.CTime = date;
                            info.CompanyCode = "001";
                            info.FactoryCode = "2002";


                            info.BaseEntry = x.销售订单号;
                            info.BaseNum = x.销售订单号;
                            info.BaseLine = x.销售单行号;
                            //info.BaseType = x.销售订单类型;
                            info.BaseType = plan.SalesOrderType;

                            info.BarCode = x.序列号;
                            info.BatchNum = x.批次;
                            info.ItemCode = x.物料编号.Trim();
                            var item = itemcodes.Where(v => v.MATNR == info.ItemCode)?.ToList().FirstOrDefault();
                            if (item == null)
                            {
                                error_message = "物料编号【" + info.ItemCode + "】无效!";
                                return false;
                            }

                            info.ItemName = item == null ? "" : item.MAKTX;
                            info.Unit = item == null ? "" : item.MEINS;
                            info.DeliveryScanQty = x.交货数量;
                            info.DeliveryDate = x.交货日期;
                            info.WhsCode = x.仓库编号;
                            var outwhs = whscodes.Where(v => v.WhsCode == x.仓库编号)?.ToList().FirstOrDefault();
                            info.WhsName = outwhs == null ? "" : outwhs.WhsName;
                            info.RegionCode = outwhs == null ? "" : outwhs.RegionCode;
                            info.RegionName = outwhs == null ? "" : outwhs.RegionName;
                            info.BinLocationCode = outwhs == null ? "" : outwhs.BinLocationCode;
                            info.BinLocationName = outwhs == null ? "" : outwhs.BinLocationName;

                            info.CONT = x.合同单号;
                            info.CustomerOrderNum = x.客户订单号;
                            info.CustomerName = x.客户名称;
                            info.CustomerAdd = x.客户地址;
                            info.CustomerCode = x.客户编号;

                            info.SupplierName = x.物流供应商;
                            info.SupplierCode = x.物流供应商编号;
                            info.SettlementAdd = x.结算地址;
                            info.ManualPostTime = x.过账时间 == null ? DateTime.Now : x.过账时间;

                            info.SalesOrganization = x.销售组织;
                            info.ProjectCategory = x.项目类别;
                            info.Project = x.项目名称;
                            info.Remark = x.备注;


                            Addlist.Add(info);
                        }
                        else
                        {
                            if (info1.IsPosted == false)
                            {
                                info1.MUser = opUser;
                                info1.MTime = date;
                                info1.BaseNum = x.销售订单号;
                                info1.BaseLine = x.销售单行号;
                                //info1.BaseType = x.销售订单类型;
                                info1.BaseType = plan.SalesOrderType;
                                info1.BarCode = x.序列号;
                                info1.BatchNum = x.批次;
                                info1.ItemCode = x.物料编号.Trim();
                                var item = itemcodes.Where(v => v.MATNR == info1.ItemCode)?.ToList().FirstOrDefault();
                                if (item == null)
                                {
                                    error_message = "物料编号【" + info1.ItemCode + "】无效!";
                                    return false;
                                }
                                info1.ItemName = item == null ? "" : item.MAKTX;
                                info1.Unit = item == null ? "" : item.MEINS;
                                info1.DeliveryScanQty = x.交货数量;
                                info1.DeliveryDate = x.交货日期;
                                info1.WhsCode = x.仓库编号;
                                var outwhs = whscodes.Where(v => v.WhsCode == x.仓库编号)?.ToList().FirstOrDefault();
                                info1.WhsName = outwhs == null ? "" : outwhs.WhsName;
                                info1.RegionCode = outwhs == null ? "" : outwhs.RegionCode;
                                info1.RegionName = outwhs == null ? "" : outwhs.RegionName;
                                info1.BinLocationCode = outwhs == null ? "" : outwhs.BinLocationCode;
                                info1.BinLocationName = outwhs == null ? "" : outwhs.BinLocationName;

                                info1.CONT = x.合同单号;
                                info1.CustomerOrderNum = x.客户订单号;
                                info1.CustomerName = x.客户名称;
                                info1.CustomerAdd = x.客户地址;
                                info1.CustomerCode = x.客户编号;

                                info1.SupplierName = x.物流供应商;
                                info1.SupplierCode = x.物流供应商编号;
                                info1.SettlementAdd = x.结算地址;
                                info1.ManualPostTime = x.过账时间 == null ? DateTime.Now : x.过账时间;

                                info1.SalesOrganization = x.销售组织;
                                info1.ProjectCategory = x.项目类别;
                                info1.Project = x.项目名称;
                                info1.Remark = x.备注;
                                Updatelist.Add(info1);
                            }
                        }
                    }
                    else
                    {
                        //部件订单
                        SD_DeliveryScan info = new SD_DeliveryScan();
                        string DocNum = _baseApp.GetNewDocNum(DocType.SD, DocFixedNumDef.SD_DeliveryScan);
                        info.DocNum = DocNum;
                        info.IsDelete = false;
                        info.IsPosted = false;
                        info.CUser = opUser;
                        info.CTime = date;
                        info.CompanyCode = "001";
                        info.FactoryCode = "2002";

                        info.BaseEntry = x.销售订单号;
                        info.BaseNum = x.销售订单号;
                        info.BaseLine = x.销售单行号;
                        //info.BaseType = x.销售订单类型;
                        info.BaseType = plan.SalesOrderType;

                        info.BarCode = x.序列号;
                        info.BatchNum = x.批次;
                        info.ItemCode = x.物料编号.Trim();
                        var item = itemcodes.Where(v => v.MATNR == info.ItemCode)?.ToList().FirstOrDefault();
                        if (item == null)
                        {
                            error_message = "物料编号【" + info.ItemCode + "】无效!";
                            return false;
                        }

                        info.ItemName = item == null ? "" : item.MAKTX;
                        info.Unit = item == null ? "" : item.MEINS;
                        info.DeliveryScanQty = x.交货数量;
                        info.DeliveryDate = x.交货日期;
                        info.WhsCode = x.仓库编号;
                        var outwhs = whscodes.Where(v => v.WhsCode == x.仓库编号)?.ToList().FirstOrDefault();
                        info.WhsName = outwhs == null ? "" : outwhs.WhsName;
                        info.RegionCode = outwhs == null ? "" : outwhs.RegionCode;
                        info.RegionName = outwhs == null ? "" : outwhs.RegionName;
                        info.BinLocationCode = outwhs == null ? "" : outwhs.BinLocationCode;
                        info.BinLocationName = outwhs == null ? "" : outwhs.BinLocationName;

                        info.CONT = x.合同单号;
                        info.CustomerOrderNum = x.客户订单号;
                        info.CustomerName = x.客户名称;
                        info.CustomerAdd = x.客户地址;
                        info.CustomerCode = x.客户编号;

                        info.SupplierName = x.物流供应商;
                        info.SupplierCode = x.物流供应商编号;
                        info.SettlementAdd = x.结算地址;
                        info.ManualPostTime = x.过账时间 == null ? DateTime.Now : x.过账时间;

                        info.SalesOrganization = x.销售组织;
                        info.ProjectCategory = x.项目类别;
                        info.Project = x.项目名称;
                        info.Remark = x.备注;
                        Addlist.Add(info);
                    }
                }
                _plandetailapp.DbContext = DbContext;
                DbContext.Ado.BeginTran();
                if (Addlist.Count > 0)
                    this.Insert(Addlist);
                if (Updatelist.Count > 0)
                    this.Update(Updatelist);
                if (updateShippingPlanDetails.Count > 0)
                    _plandetailapp.Update(updateShippingPlanDetails);
                this.DbContext.Ado.CommitTran();
                int conut = Addlist.Count + Updatelist.Count;
                error_message = "导入【" + excelList.Count + "】条，成功导入【" + conut + "】条";
            }
            catch (Exception ex)
            {
                if (this.DbContext.Ado.IsAnyTran())
                    this.DbContext.Ado.RollbackTran();
                throw ex;
            }
            return flag;
        }

        /// <summary>
        /// 导入校验规则
        /// </summary>
        /// <param name="excelList">导入的集合</param>
        /// <returns></returns>
        public bool ValidateCheck(List<SD_DeliveryScanImport> excelList)
        {
            if (excelList == null || excelList.Count <= 0)
            {
                throw new Exception("没有可以导入的数据");
            }
            SAPApp _SAPApp = new SAPApp();
            MD_BinLocationApp _binApp = new MD_BinLocationApp();
            var flag = true;
            //校验空值
            //校验导入数据 是否符合导入条件 用数据校验 空值/是否存在
            _SAPApp.IsHaveItem(excelList.Select(x => x.物料编号.Trim()).Distinct().ToArray()); //物料信息
            _binApp.IsHaveWhsCode(excelList.Select(x => x.仓库编号.Trim()).Distinct().ToArray()); //仓库
            return flag;
        }

        #endregion

        #endregion

        #region Mobile

        /// <summary>
        /// 查询交运计划单号是否有效
        /// </summary>
        /// <param name="DocNum">计划单号</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool GetDocNumForShippingPlan(string DocNum, out string error_message)
        {
            error_message = "";
            var query = _planapp.GetList(x => x.DocNum == DocNum).ToList().FirstOrDefault();
            if (query == null)
            {
                error_message = "计划单号[" + DocNum + "]无效，请检查信息";
                return false;
            }
            else
            {
                if (query.ShippingPlanStatus == 3)
                {
                    error_message = "计划单号[" + DocNum + "]已完成发运，请选择其他信息进行操作";
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// 查询销售发运计划信息
        /// </summary>
        /// <param name="DocNum">计划单号</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public List<SD_DeliveryScan_View> GetShippingPlanForMobile(string DocNum, out string error_message)
        {
            var view = new List<SD_DeliveryScan_View>();
            error_message = "";
            var query = _planapp.GetList(x => x.DocNum == DocNum).ToList().FirstOrDefault();
            if (query == null)
            {
                error_message = "计划单号[" + DocNum + "]无效，请检查信息";
                return null;
            }
            else
            {
                if (query.ShippingPlanStatus == 3)
                {
                    error_message = "计划单号[" + DocNum + "]已完成发运，请选择其他信息进行操作";
                    return null;
                }

                //查询销售发货计划
                string planMsg = "";
                var itemsData1 = _planapp.GetShippingPlanDetail(DocNum, out planMsg);
                if (itemsData1 != null && itemsData1.Count > 0)
                {
                    view = itemsData1;
                }
                else
                {
                    error_message = planMsg;
                    return null;
                }
            }

            return view;
        }


        #region 获取SAP主机生产订单，带序列号

        public List<MD_Stock> GetOrderListWithSerialNo(string BarCode)
        {
            StringBuilder strBuilder = new StringBuilder();
            strBuilder.AppendLine(
                @"		select SerialNo BarCode,ProductionBatch BatchNum,MaterialNo ItemCode, MaterialName ItemName,
		                         OrderQty Qty,Unit,SalesOrderNo SaleNum,CAST(SalesOrderLineNo as int) SaleLine,
		                         ReceivingLocation WhsCode,b.[WhsName],b.[RegionCode],b.[RegionName],b.BinLocationName,b.BinLocationCode,
		                         '' Remark from PP_ProductionOrder a
		                         left join [dbo].[MD_BinLocation] b on a.ReceivingLocation=b.WhsCode
		                        where a.IsDelete=0 ");

            //根据关键字过滤
            if (!string.IsNullOrEmpty(BarCode))
            {
                strBuilder.AppendLine($"and a.SerialNo = '{BarCode}' ");
            }

            var list = DbContext.Ado.SqlQuery<MD_Stock>(strBuilder.ToString()).ToList();
            return list;
        }

        #endregion

        #region 保存

        /// <summary>
        /// 校验
        /// </summary>
        /// <param name="keyword">参数</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool CheckDate(string keyword, out string error_message)
        {
            error_message = "";
            var query = DbContext.Queryable<SD_ShippingPlanDetail>()
                   .Where(x => !x.IsDelete)   //排除已经逻辑删除的记录
                   .Where(x => (string.IsNullOrEmpty(keyword) || x.SalesOrderNumber == keyword)).ToList();
            if (query == null && query.Count <= 0)
            {
                error_message = "扫描/录入的销售订单号为无效单据，请检查数据！";
                return false;
            }

            //校验扫描的单号在SRM库中是否为有效单据
            //string sql = @" SELECT *FROM XZSRM.dbo.P_InspectionDetail where InspectionNo=@InspectionNo and ItemCode=@ItemCode and IsDelete=0 ";
            //var Inspection = DbContext.SqlQuery<SRM_InspectionDetail>(sql, SugarParameter.Create("@InspectionNo", keyword),SugarParameter.Create("@ItemCode",ItemCode)).ToList().FirstOrDefault();
            //if (Inspection == null)
            //{
            //    //ui.Message.TheScannedInspectionOrderNumberInvalid
            //    error_message = "扫描/录入的报检单号/物料编号是无效单据，请检查数据！"; // "扫描的报检单号无效!";
            //    return false;
            //}

            //校验扫描的报检单号在WMS库中是否已存在记录
            //QM_PurchaseInspection PurchaseInspection = GetList(x => (string.IsNullOrEmpty(keyword) || x.InspectionNum == keyword) &&
            //                                                  string.IsNullOrEmpty(ItemCode) || x.ItemCode == ItemCode).ToList().FirstOrDefault();
            //if (PurchaseInspection != null)
            //{
            //    //ui.Message.TheScannedInspectionOrderNumberAlreadyExists
            //    error_message = "扫描/录入的报检单号/物料编号已存在记录，请检查数据！"; // "扫描的报检单号已存在!";
            //    return false;
            //}

            //string PurchaseInspectionSql = @" SELECT *FROM XZWMS.dbo.QM_PurchaseInspection where InspectionNum=@InspectionNum and ItemCode=@ItemCode and IsDelete=0 ";
            //var PurchaseInspection = DbContext.SqlQuery<QM_PurchaseInspection>(PurchaseInspectionSql, SugarParameter.Create("@InspectionNum", keyword),SugarParameter.Create("ItemCode",ItemCode)).ToList().FirstOrDefault();
            //if (PurchaseInspection != null)
            //{
            //    //ui.Message.TheScannedInspectionOrderNumberAlreadyExists
            //    error_message = "扫描/录入的报检单号/物料编号已存在记录，请检查数据！"; // "扫描的报检单号已存在!";
            //    return false;
            //}

            return true;

            //return error_message;
        }

        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="entities">销售交货集合</param>
        /// <param name="ManualPostTime">手动过账时间</param>
        /// <param name="user">用户信息</param>
        /// <param name="error_message">错误消息返回</param>
        /// <param name="type">类型 1:提交失败 2：过账失败</param>
        public bool Save(List<SD_DeliveryScan> entities, DateTime ManualPostTime, string user, out string error_message,
            out string type)
        {
            error_message = "提交成功";
            type = "";
            MD_StockApp _stockApp = new MD_StockApp();
            try
            {
                //库存校验
                //foreach (var x in entities)
                //{
                //    //序列号
                //    if (!string.IsNullOrEmpty(x.BarCode))
                //    {
                //        string barcodeMsg = "";
                //        if (!_stockApp.ValidateStockOut(x.BarCode, x.BinLocationCode, x.DeliveryScanQty, out barcodeMsg, null))
                //        {
                //            error_message = barcodeMsg;
                //            type = "1";
                //            return false;
                //        }
                //    }
                //    else if (!string.IsNullOrEmpty(x.BaseNum.Trim()) && !string.IsNullOrEmpty(x.ItemCode))
                //    {
                //        string basenumMsg = "";
                //        if (!_stockApp.ValidateStockOut(x.ItemCode, x.BinLocationCode, x.BaseNum, (int)x.BaseLine, x.DeliveryScanQty, out basenumMsg, null))
                //        {
                //            error_message = basenumMsg;
                //            type = "1";
                //            return false;
                //        }
                //    }
                //    else
                //    {
                //        string itemMsg = "";
                //        if (!_stockApp.ValidateStockOut(x.ItemCode, x.BinLocationCode, x.DeliveryScanQty, out itemMsg, null,null))
                //        {
                //            error_message = itemMsg;
                //            type = "1";
                //            return false;
                //        }
                //    }
                //}

                Sys_DictionaryApp Sys_DictionaryApp = new Sys_DictionaryApp();

                DateTime date = DateTime.Now;
                //插入表信息 
                var mainList = entities.GroupBy(g => new
                {
                    g.CustomerCode
                }).Select(q => new
                {
                    CustomerCode = q.Key.CustomerCode
                });
                //按客户分组
                foreach (var mainInfo in mainList)
                {
                    //查询是否特殊客户，如果是特殊客户要按销售订单分组，如果不是按地址分组。
                    if (Sys_DictionaryApp.GetList(x => x.TypeCode == "SD002" && x.EnumValue == mainInfo.CustomerCode)
                        .Any())
                    {
                        var mainInfoList = entities.Where(x => x.CustomerCode == mainInfo.CustomerCode).GroupBy(g => new
                        {
                            g.BaseNum
                        }).Select(q => new
                        {
                            BaseNum = q.Key.BaseNum
                        });
                        foreach (var mod in mainInfoList)
                        {
                            string DocNum = _baseApp.GetNewDocNum(DocType.SD, DocFixedNumDef.SD_DeliveryScan);
                            List<SD_DeliveryScan> conditionList =
                                entities.Where(x => x.BaseNum == mod.BaseNum)?.ToList();

                            foreach (var info in conditionList)
                            {
                                //x.MovementType = "541"; //"541：发料 542：退料"
                                info.ManualPostTime = ManualPostTime;
                                info.DocNum = DocNum;
                                info.IsDelete = false;
                                info.IsPosted = false;
                                info.CUser = user;
                                info.CTime = date;
                                info.CompanyCode = "001";
                                info.FactoryCode = "2002";
                                info.BaseEntry = info.BaseNum; //按销售订单分组的送货单标记，查询会用到
                                //校验序列号不允许重复扫描
                                //List<SD_DeliveryScan> list = GetList(y => y.BarCode == info.BarCode.Trim())?.ToList();
                                //if (list!=null && list.Count > 0)
                                //{
                                //    error_message = info.BarCode + "序列号已扫描,不允许重复扫描";

                                //    type = "1";
                                //    return false;
                                //}
                            }
                        }
                    }
                    else
                    {
                        var mainList1 = entities.Where(x => x.CustomerCode == mainInfo.CustomerCode).GroupBy(g => new
                        {
                            g.CustomerAdd,
                            g.BatchNum
                        }).Select(q => new
                        {
                            CustomerAdd = q.Key.CustomerAdd,
                            BatchNum = q.Key.BatchNum
                        });
                        //按客户地址/批次分组
                        foreach (var mainInfo1 in mainList1)
                        {
                            List<SD_DeliveryScan> conditionList = entities.Where(x =>
                                x.CustomerCode == mainInfo.CustomerCode && x.CustomerAdd == mainInfo1.CustomerAdd &&
                                x.BatchNum == mainInfo1.BatchNum)?.ToList();
                            string DocNum = _baseApp.GetNewDocNum(DocType.SD, DocFixedNumDef.SD_DeliveryScan);
                            foreach (var info in conditionList)
                            {
                                info.ManualPostTime = ManualPostTime;
                                info.DocNum = DocNum;
                                info.IsDelete = false;
                                info.IsPosted = false;
                                info.CUser = user;
                                info.CTime = date;
                                info.CompanyCode = "001";
                                info.FactoryCode = "2002";
                            }
                        }
                    }
                }

                DbContext.Ado.BeginTran();

                //批量插入
                Insert(entities);

                DbContext.Ado.CommitTran();

                //更新发运计划状态
                base.SqlQuery("PROC_UpdateShippingPlanStatus", CommandType.StoredProcedure);

                //Sys_SwithConfigApp _switchApp = new Sys_SwithConfigApp();
                ////是否自动过账判定
                //if (_switchApp.IsSDDeliveryScanAutoPost)
                //{
                //    string postMsg = "";
                //    bool bpost = DoPost(entities, user, out postMsg);
                //    if (!bpost)
                //    {
                //        error_message = postMsg;
                //        type = "2";
                //        return false;
                //    }
                //    else
                //    {
                //        error_message = "提交并" + postMsg;
                //    }
                //}

                return true;
            }
            catch (Exception ex)
            {

                DbContext.Ado.RollbackTran();
                error_message = ex.Message;
                type = "1";
                return false;
            }
        }

        #endregion

        #endregion
    }
}