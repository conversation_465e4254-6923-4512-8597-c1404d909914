using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.PP
{
    /// <summary>
    /// 超额领料子表
    /// </summary>
    public class PP_OverReceiveDetail : BaseEntity
    {
        /// <summary>
        /// 主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string ID { get; set; }
        /// <summary>
        /// 超额领料单号
        /// </summary>
        [Description("超额领料单号")]
        public string OverReceiveNo { get; set; }
        /// <summary>
        /// 超额领料行号
        /// </summary>
        [Description("超额领料行号")]
        public int? OverReceiveLineNo { get; set; }
        /// <summary>
        /// 生产订单
        /// </summary>
        [Description("生产订单")]
        public string ProductionOrderNo { get; set; }
        /// <summary>
        /// 出厂编号 = 开始日期 + 4位流水号
        /// </summary>
        [Description("出厂编号")]
        public string SerialNo { get; set; }
        /// <summary>
        /// 组件行项目号
        /// </summary>
        [Description("组件行项目号")]
        public decimal ComponentLineNo { get; set; }
        /// <summary>
        /// 组件编码
        /// </summary>
        [Description("组件编码")]
        public string ComponentCode { get; set; }
        /// <summary>
        /// 物料描述
        /// </summary>
        [Description("物料描述")]
        public string MaterialName { get; set; }
        /// <summary>
        /// 需求数量
        /// </summary>
        [Description("需求数量")]
        public decimal DemandQty { get; set; }
        /// <summary>
        /// 组件单位
        /// </summary>
        [Description("组件单位")]
        public string ComponentUnit { get; set; }
        /// <summary>
        /// 移动类型，工废：261，如果是料废311
        /// </summary>
        [Description("移动类型")]
        public string MovementType { get; set; }
        /// <summary>
        /// 工厂
        /// </summary>
        [Description("工厂")]
        public string FactoryCode { get; set; }
        /// <summary>
        /// 转出仓库
        /// </summary>
        [Description("转出仓库")]
        public string OutWarehouse { get; set; }
        /// <summary>
        /// 发料库存地
        /// </summary>
        [Description("发料库存地")]
        public string DeliverLocation { get; set; }
        /// <summary>
        /// 线体编码
        /// </summary>
        public string ProductionLineCode { get; set; }
        /// <summary>
        /// 线体描述
        /// </summary>
        [Description("装配线")]
        public string ProductionLineDes { get; set; }
        /// <summary>
        /// 合同号
        /// </summary>
        [Description("合同号")]
        public string ContractNo { get; set; }
        /// <summary>
        /// 物料组
        /// </summary>
        public string MaterialGroupCode { get; set; }
        /// <summary>
        /// 物料组描述
        /// </summary>
        [Description("物料组")]
        public string MaterialGroupDes { get; set; }
        /// <summary>
        /// 销售订单
        /// </summary>
        [Description("客户订单号")]
        public string SalesOrderNo { get; set; }
        /// <summary>
        /// 销售订单行项目
        /// </summary>
        [Description("销售订单行项目")]
        public decimal? SalesOrderLineNo { get; set; }
        /// <summary>
        /// 特殊库存标识
        /// </summary>
        [Description("特殊库存标识")]
        public string SpecialInventory { get; set; }
        /// <summary>
        /// 评估类别，为B时评估类型为01自制02外购
        /// </summary>
        [Description("评估类别")]
        public string AssessmentCategory { get; set; }
        /// <summary>
        /// 评估类型，01自制02外购
        /// </summary>
        [Description("评估类型")]
        public string AssessmentType { get; set; }
        /// <summary>
        /// 是否过账
        /// </summary>
        [Description("是否过账")]
        public bool IsPosted { get; set; } = false;
        /// <summary>
        /// 过账人
        /// </summary>
        [Description("过账人")]
        public string PostUser { get; set; }
        /// <summary>
        /// 过账时间
        /// </summary>
        [Description("过账时间")]
        public DateTime? PostTime { get; set; }
        /// <summary>
        /// 手动过账时间
        /// </summary>
        [Description("手动过账时间")]
        public DateTime? ManualPostTime { get; set; }
        /// <summary> 
        /// sap生成单号
        /// </summary> 
        [Description("sap生成单号")]
        public string SapDocNum { get; set; }
        /// <summary> 
        /// sap生成行号
        /// </summary> 
        [Description("sap生成行号")]
        public int? SapLine { get; set; }
    }
}
