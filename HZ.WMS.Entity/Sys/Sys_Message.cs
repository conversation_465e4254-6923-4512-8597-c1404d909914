using System;
using System.ComponentModel;
using SqlSugar;
//Chloe框架


namespace HZ.WMS.Entity.Sys
{
	/// <summary>
    /// 
    /// </summary>
    [SugarTable("Sys_Message")]
    public class Sys_Message:BaseEntity
    {
                /// <summary>
        /// 消息ID
        /// </summary>
        [Description("消息ID")]
        [SugarColumn(IsPrimaryKey = true)]
        public string MessageID {get;set;}
        /// <summary>
        /// 消息分类ID
        /// </summary>
        [Description("消息分类ID")]
        public string MessageTypeID {get;set;}
        /// <summary>
        /// 标题
        /// </summary>
        [Description("标题")]
        public string MessageTitle {get;set;}
        /// <summary>
        /// 消息正文
        /// </summary>
        [Description("消息正文")]
        public string MessageBody {get;set;}
        /// <summary>
        /// 发布者
        /// </summary>
        [Description("发布者")]
        public string MessagePublisher {get;set;}
        /// <summary>
        /// 发布时间
        /// </summary>
        [Description("发布时间")]
        public DateTime? MessagePublishTime {get;set;}

    }
}

