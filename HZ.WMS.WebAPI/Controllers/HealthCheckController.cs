using System;
using System.Threading.Tasks;
using System.Web.Http;
using HZ.Core.Http;
using HZ.WMS.Application;
using HZ.WMS.WebAPI.Controllers;

namespace HZ.WMS.WebAPI.Controllers
{
    /// <summary>
    /// 健康检查控制器
    /// </summary>
    public class HealthCheckController : ApiBaseController
    {
        /// <summary>
        /// 检查数据库连接池状态
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<IHttpActionResult> CheckDatabaseConnections()
        {
            var result = new ResponseData();
            try
            {
                var connectionStatus = await HZ.WMS.Application.SimpleConnectionMonitor.CheckAllConnectionsAsync();
                result.Data = connectionStatus;
                result.Code = (int)WMSStatusCode.Success;
                result.Message = "数据库连接检查完成";
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 获取连接池状态
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetConnectionPoolStatus()
        {
            var result = new ResponseData();
            try
            {
                var status = DatabaseConfig.GetConnectionPoolStatus();
                result.Data = status;
                result.Code = (int)WMSStatusCode.Success;
                result.Message = "获取连接池状态成功";
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 清理空闲连接
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult ClearIdleConnections()
        {
            var result = new ResponseData();
            try
            {
                DatabaseConfig.ClearIdleConnections();
                result.Code = (int)WMSStatusCode.Success;
                result.Message = "清理空闲连接成功";
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 检查特定数据库连接
        /// </summary>
        /// <param name="dbKey">数据库连接键</param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IHttpActionResult> CheckSpecificConnection(string dbKey)
        {
            var result = new ResponseData();
            try
            {
                var connectionString = ConnectionStringBuilder.BuildOptimizedConnectionString(dbKey);
                var isHealthy = await DbConnectionMonitor.IsConnectionPoolHealthyAsync(connectionString);
                var usage = await DbConnectionMonitor.GetConnectionPoolUsageAsync(connectionString);
                
                result.Data = new 
                { 
                    DbKey = dbKey,
                    IsHealthy = isHealthy,
                    UsagePercentage = usage
                };
                result.Code = (int)WMSStatusCode.Success;
                result.Message = $"数据库 {dbKey} 连接检查完成";
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 系统健康检查
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<IHttpActionResult> SystemHealth()
        {
            var result = new ResponseData();
            try
            {
                var connectionStatus = await HZ.WMS.Application.SimpleConnectionMonitor.CheckAllConnectionsAsync();
                var allHealthy = true;
                var unhealthyConnections = new System.Collections.Generic.List<string>();

                foreach (var status in connectionStatus)
                {
                    if (!status.IsHealthy)
                    {
                        allHealthy = false;
                        unhealthyConnections.Add(status.ConnectionKey);
                    }
                }

                result.Data = new
                {
                    SystemHealthy = allHealthy,
                    ConnectionPoolStatus = DatabaseConfig.GetConnectionPoolStatus(),
                    UnhealthyConnections = unhealthyConnections,
                    CheckTime = DateTime.Now,
                    ConnectionDetails = connectionStatus
                };

                result.Code = allHealthy ? (int)WMSStatusCode.Success : (int)WMSStatusCode.Failed;
                result.Message = allHealthy ? "系统健康" : $"系统异常，{unhealthyConnections.Count} 个数据库连接不健康";
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 手动清理连接池
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult ManualCleanupConnectionPool()
        {
            var result = new ResponseData();
            try
            {
                HZ.WMS.Application.ConnectionPoolCleanupService.ManualCleanup();
                result.Code = (int)WMSStatusCode.Success;
                result.Message = "连接池清理操作已执行";
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 获取连接池清理服务状态
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetCleanupServiceStatus()
        {
            var result = new ResponseData();
            try
            {
                result.Data = new
                {
                    IsRunning = HZ.WMS.Application.ConnectionPoolCleanupService.IsRunning(),
                    CheckTime = DateTime.Now
                };
                result.Code = (int)WMSStatusCode.Success;
                result.Message = "获取清理服务状态成功";
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.Message;
            }
            return Json(result);
        }
    }
}
