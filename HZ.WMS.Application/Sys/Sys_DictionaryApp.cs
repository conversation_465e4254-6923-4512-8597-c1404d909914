using HZ.Core.Http;
using HZ.WMS.Entity.Sys;
using System.Collections.Generic;
using System.Linq;

namespace HZ.WMS.Application.Sys
{
    /// <summary>
    /// 没有out_message消息的函数，意味着只在最前端做未处理异常捕捉
    /// </summary>

    public class Sys_DictionaryApp : BaseApp<Sys_Dictionary>
    {
        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public Sys_DictionaryApp() : base()
        {
        }




        #endregion

        #region 获取分页列表

        /// <summary>
        /// 
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <returns></returns>
        public List<Sys_Dictionary> GetPageList(Pagination page,string keyword)
        {
            return base.GetPageList(page, t =>
                string.IsNullOrEmpty(keyword) 
                || t.TypeCode.Contains(keyword)
                || t.TypeDisc.Contains(keyword)
                
            );
        }

        #endregion

        #region 添加

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public Sys_Dictionary Insert(Sys_Dictionary entity)
        {
            return base.Insert(entity);
        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public int Delete(Sys_Dictionary entity)
        {
            return base.Delete(entity);
        }

        #endregion

        #region 批量删除

        /// <summary>
        /// 批量删除
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public int DeleteByIDS(object[] ids, string deleteUser)
        {
            return base.DeleteByKeys(ids, deleteUser);
        }

        #endregion

        #region 更新

        /// <summary>
        /// 更新，只能修改备注，是否已读，已读时间，修改时间，修改人Id这几个字段
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public int Update(Sys_Dictionary entity)
        {
            return base.Update(entity);
        }

        #endregion

        /// <summary>
        /// 获取字典项
        /// </summary>
        /// <param name="enumKey"></param>
        /// <param name="typeCode"></param>
        /// <returns></returns>
        public Sys_Dictionary GetEntity(int enumKey, string typeCode)
        {
            return base.GetList(x => x.EnumKey == enumKey && x.TypeCode == typeCode).ToList().FirstOrDefault();
        }

        /// <summary>
        /// 获取字典项集合
        /// </summary>
        /// <param name="typeCode"></param>
        /// <returns></returns>
        public List<Sys_Dictionary> GetEntity(string typeCode)
        {
            return base.GetList(x => x.TypeCode == typeCode).ToList();
        }




    }
}

