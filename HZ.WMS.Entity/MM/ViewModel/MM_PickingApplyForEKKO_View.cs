using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.MM.ViewModel
{
    /// <summary>
    /// 委外领料申请单关联采购订单视图
    /// </summary>
    public class MM_PickingApplyForEKKO_View
    {
        /// <summary>
        /// 采购订单类型
        /// </summary>
        [Description("采购订单类型")]
        public string BSART { get; set; }

        /// <summary>
        /// 采购订单类型描述
        /// </summary>
        [Description("采购订单类型描述")]
        public string BATXT { get; set; }

        /// <summary>
        /// 采购订单编号
        /// </summary>
        [Description("采购订单编号")]
        public string EBELN { get; set; }

        /// <summary>
        /// 公司代码
        /// </summary>
        [Description("公司代码")]
        public string BUKRS { get; set; }

        /// <summary>
        /// 采购组织
        /// </summary>
        [Description("采购组织")]
        public string EKORG { get; set; }

        /// <summary>
        /// 采购组
        /// </summary>
        [Description("采购组")]
        public string EKGRP { get; set; }

        /// <summary>
        /// 创建日期
        /// </summary>
        [Description("创建日期")]
        public DateTime? AEDAT { get; set; }

        /// <summary>
        /// 创建者
        /// </summary>
        [Description("创建者")]
        public string ERNAM { get; set; }

        /// <summary>
        /// 供应商编号
        /// </summary>
        [Description("供应商编号")]
        public string LIFNR { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        [Description("供应商名称")]
        public string NAME1 { get; set; }

        /// <summary>
        /// 付款条件编码
        /// </summary>
        [Description("付款条件编码")]
        public string ZTERM { get; set; }

        /// <summary>
        /// OA流程单号
        /// </summary>
        [Description("OA流程单号")]
        public string ZNUMBER { get; set; }

        /// <summary>
        /// 状态码
        /// </summary>
        [Description("状态码")]
        public string ZZSTACODE { get; set; }

        /// <summary>
        /// 订单行号
        /// </summary>
        [Description("订单行号")]
        public decimal? EBELP { get; set; }

        /// <summary>
        /// 项目类别
        /// </summary>
        [Description("项目类别")]
        public string PSTYP { get; set; }

        /// <summary>
        /// 科目分配类别
        /// </summary>
        [Description("科目分配类别")]
        public string KNTTP { get; set; }

        /// <summary>
        /// 物料编码
        /// </summary>
        [Description("物料编码")]
        public string MATNR { get; set; }

        /// <summary>
        /// 物料名称
        /// </summary>
        [Description("物料名称")]
        public string TXZ01 { get; set; }

        /// <summary>
        /// 工厂代码
        /// </summary>
        [Description("工厂代码")]
        public string WERKS { get; set; }

        /// <summary>
        /// 库存地点代码
        /// </summary>
        /// 
        [Description("库存地点代码")]
        public string LGORT { get; set; }

        /// <summary>
        /// 库存地点描述
        /// </summary>
        [Description("库存地点描述")]
        public string LGOBE { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量")]
        public decimal? MENGE { get; set; }

        /// <summary>
        /// 订单单位
        /// </summary>
        [Description("订单单位")]
        public string MEINS { get; set; }

        /// <summary>
        /// 价格单位
        /// </summary>
        [Description("价格单位")]
        public decimal? PEINH { get; set; }

        /// <summary>
        /// 退货标记
        /// </summary>
        [Description("退货标记")]
        public string RETPO { get; set; }

        /// <summary>
        /// 交货日期
        /// </summary>
        [Description("交货日期")]
        public DateTime? EINDT { get; set; }

        /// <summary>
        /// 删除标记
        /// </summary>
        [Description("删除标记")]
        public string LOEKZ { get; set; }

        /// <summary>
        /// 不含税单价
        /// </summary>
        [Description("不含税单价")]
        public decimal? NETPR { get; set; }

        /// <summary>
        /// 不含税总价
        /// </summary>
        [Description("不含税总价")]
        public decimal? BRTWR { get; set; }

        /// <summary>
        /// 税码
        /// </summary>
        [Description("税码")]
        public string MWSKZ { get; set; }

        /// <summary>
        /// 税码描述
        /// </summary>
        [Description("税码描述")]
        public string TEXT1 { get; set; }

        /// <summary>
        /// 物料组
        /// </summary>
        [Description("物料组")]
        public string MATKL { get; set; }

        /// <summary>
        /// 评估类别
        /// </summary>
        [Description("评估类别")]
        public string BWTTY { get; set; }

        /// <summary>
        /// 评估类型
        /// </summary>
        [Description("评估类型")]
        public string BWTAR { get; set; }

        /// <summary>
        /// 总账科目
        /// </summary>
        [Description("总账科目")]
        public string SAKTO { get; set; }

        /// <summary>
        /// 资产号
        /// </summary>
        [Description("资产号")]
        public string ANLN1 { get; set; }

        /// <summary>
        /// 订单号
        /// </summary>
        [Description("订单号")]
        public string AUFNR { get; set; }

        /// <summary>
        /// 销售订单号
        /// </summary>
        [Description("销售订单号")]
        public string VBELN { get; set; }

        /// <summary>
        /// 销售订单行号
        /// </summary>
        [Description("销售订单行号")]
        public decimal? VBELP { get; set; }

        /// <summary>
        /// 供应商源
        /// </summary>
        [Description("供应商源")]
        public string EMLIF { get; set; }
    }
}
