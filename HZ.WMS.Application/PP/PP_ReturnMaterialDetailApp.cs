using SqlSugar;
using HZ.Core.Http;
using HZ.WMS.Entity.PP;
using HZ.WMS.Entity.PP.ViewModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Application.PP
{
    /// <summary>
    /// 车间退料子表
    /// </summary>
    public class PP_ReturnMaterialDetailApp : BaseApp<PP_ReturnMaterialDetail>
    {
        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public PP_ReturnMaterialDetailApp() : base()
        {
        }

        #endregion

        #region 获取SAP生产订单

        public List<PP_ReturnMaterial_View> GetSapPageList(Pagination page, Expression<Func<PP_ReturnMaterial_View, bool>> condition = null)
        {
            //查询SAP所有数据
            var query = DbContext.Queryable<PP_ReturnMaterial_View>().Where(condition);
            int total = query.Count();
            page.Total = total;
            if (page.PageSize > total)
            {
                return query.ToPageList(page.PageNumber, total);
            }
            return query.ToPageList(page.PageNumber, page.PageSize);
        }

        #endregion
    }
}
