using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.SAP
{
    public class ZFGWMS010
    {
        /// <summary>
        /// 领料单行号
        /// </summary>
        public int ZNUM { get; set; }
        /// <summary>
        /// 物料编码
        /// </summary>
        public string MATNR { get; set; }
        /// <summary>
        /// 工厂
        /// </summary>
        public string WERKS { get; set; }
        /// <summary>
        /// 移动类型
        /// </summary>
        public string BWART { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        public decimal? MENGE { get; set; }
        /// <summary>
        /// 单位
        /// </summary>
        public string MEINS { get; set; }
        /// <summary>
        /// 库存地点
        /// </summary>
        public string LGORT { get; set; }

        /// <summary>
        /// 成本中心 
        /// </summary>
        public string KOSTL { get; set; }

        /// <summary>
        /// 总账科目 移动类型等于Z25/Z26时，必输
        /// </summary>
        public string SAKTO { get; set; }

        /// <summary>
        /// 特殊库存
        /// </summary>
        public string SOBKZ { get; set; }
        /// <summary>
        /// 销售订单
        /// </summary>
        public string KDAUF { get; set; }
        /// <summary>
        /// 销售订单项目
        /// </summary>
        public int? KDPOS { get; set; }
        /// <summary>
        /// 评估类型
        /// </summary>
        public string BWTAR { get; set; }

        /// <summary>
        /// 生产订单号 
        /// </summary>
        public string AUFNR { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string SGTXT { get; set; }
    }
}
