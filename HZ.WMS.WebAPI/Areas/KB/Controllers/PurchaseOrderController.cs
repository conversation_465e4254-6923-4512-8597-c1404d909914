using HZ.Core.Http;
using HZ.WMS.Entity.Sys;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using HZ.WMS.Application.KB;

namespace HZ.WMS.WebAPI.Areas.KB.Controllers
{
    /// <summary>
    /// 采购模块看板
    /// 1、采购收货看板
    /// </summary>
    public class PurchaseOrderController : ApiController
    {
        private PurchaseOrderBoardApp _poApp = new PurchaseOrderBoardApp();

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IHttpActionResult GetUserInfo()
        {
            var result = new ResponseData();
            try
            {
                //验证用户名和密码
                result.Data = _poApp.GetPurchaseDeliveryData();
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
    }
}
