using System;
using System.ComponentModel;
using HZ.Core.Utilities;
using HZ.WMS.Entity;

namespace AOS.WMS.Entity.CableProduce
{
    public class Cable_ProductionOrderReq : BaseEntity
    {
        
        /// <summary>
        /// 销售单号
        /// </summary>
        [Description("销售单号")]
        public string[] ids { get; set; }

        /// <summary>
        /// 销售行号
        /// </summary>
        [Description("销售行号")]
        public DateTime assembleDate { get; set; }

    }
}