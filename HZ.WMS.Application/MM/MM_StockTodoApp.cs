using System;
using System.Collections.Generic;
using AOS.WMS.Entity.Sys;
using HZ.Core.Http;
using HZ.WMS.Application;
using HZ.WMS.Entity.SAP;

namespace AOS.WMS.Application.Store
{
    /// <summary>
    /// 没有out_message消息的函数，意味着只在最前端做未处理异常捕捉
    /// </summary>

    public class MM_StockTodoApp : BaseApp<MM_StockTodo>
    {
		#region 默认构造函数
        
        /// <summary>
        /// 默认构造函数
        /// </summary>
        public MM_StockTodoApp() : base()
        {
        }

        #endregion


        public ResponseData Warehousing(MM_StockTodoDetail mmStockTodoDetail, string currentUser)
        {
            var result = new ResponseData();
            try
            {
                DbContext.Ado.BeginTran();
                List<MM_StockTodo> list;
                if (mmStockTodoDetail.OperateType == 1)
                {
                    // 中间表校验编码是否存在
                    var part = DbContextForSAP.Queryable<XZ_SAP_MARC>().Where(t => t.MATNR == mmStockTodoDetail.ItemCode).ToList();
                    if (part.Count == 0)
                    {
                        result.Code = (int)WMSStatusCode.Failed;
                        result.Message = "物料不存在,请前往SAP维护后再试";
                        return result;
                    }
                    list = GetList(t =>
                            t.ItemCode == mmStockTodoDetail.ItemCode)
                        .ToList();
                    if (list.Count == 0)
                    {
                        // 添加库存
                        MM_StockTodo mmStockTodo = new MM_StockTodo();
                        mmStockTodo.ID = Guid.NewGuid().ToString();
                        mmStockTodo.ItemCode = part[0].MATNR;
                        mmStockTodo.ItemName = part[0].MAKTX;
                        mmStockTodo.Unit = part[0].MEINS;
                        mmStockTodo.StoreNo = mmStockTodoDetail.OperateNo;
                        DbContext.Insertable(mmStockTodo).ExecuteCommand();
                        list.Add(mmStockTodo);
                    }
                    else
                    {
                        // 更新库存
                        var storeSupplier = list[0];
                        storeSupplier.StoreNo += mmStockTodoDetail.OperateNo;
                        DbContext.Updateable(storeSupplier).ExecuteCommand();
                    }
                }
                else
                {
                    // 出库
                    list = GetList(t =>
                            t.ItemCode == mmStockTodoDetail.ItemCode)
                        .ToList();
                    if (list.Count == 0)
                    {
                        result.Code = (int)WMSStatusCode.Failed;
                        result.Message = "物料不存在";
                        return result;
                    }
                    else
                    {
                        // 更新库存
                        var storeSupplier = list[0];
                        if (storeSupplier.StoreNo < mmStockTodoDetail.OperateNo)
                        {
                            result.Code = (int)WMSStatusCode.Failed;
                            result.Message = "库存不足";
                            return result;
                        }
                        storeSupplier.StoreNo -= mmStockTodoDetail.OperateNo;
                        DbContext.Updateable(storeSupplier).ExecuteCommand();
                    }
                }
                // 操作记录
                mmStockTodoDetail.ID = Guid.NewGuid().ToString();
                mmStockTodoDetail.OperateType = mmStockTodoDetail.OperateType;
                mmStockTodoDetail.OperateTime = DateTime.Now;
                mmStockTodoDetail.Operator = currentUser;
                mmStockTodoDetail.PID = list[0].ID;
                mmStockTodoDetail.ItemName = list[0].ItemName;
                DbContext.Insertable(mmStockTodoDetail).ExecuteCommand();
                DbContext.Ado.CommitTran();
                result.Code = (int)WMSStatusCode.Success;
                result.Message = "操作成功";
                return result;
            }
            catch (Exception e)
            {
                if (DbContext.Ado.IsAnyTran())
                    DbContext.Ado.RollbackTran();
                result.Code = (int)WMSStatusCode.Failed;
                result.Message = e.Message;
                return result;
            }
        }
    }
}

