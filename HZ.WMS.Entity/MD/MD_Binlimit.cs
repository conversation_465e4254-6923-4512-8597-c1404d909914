using System.ComponentModel;
using SqlSugar;
//Chloe框架


namespace HZ.WMS.Entity.MD
{
	/// <summary>
    /// 库位限制
    /// </summary>
    
    public class MD_BinLimit:BaseEntity
    {
        /// <summary>
        /// 权限ID
        /// </summary>
        [Description("权限ID")]
        [SugarColumn(IsPrimaryKey = true)]
        public string limitID {get;set;}
        /// <summary>
        /// 菜单编号
        /// </summary>
        [Description("菜单编号")]
        public string MenuCode {get;set;}
        /// <summary>
        /// 菜单名称
        /// </summary>
        [Description("菜单名称")]
        public string MenuName {get;set;}
        /// <summary>
        /// 区域编号
        /// </summary>
        [Description("区域编号")]
        public string RegionCode {get;set;}
        /// <summary>
        /// 区域名称
        /// </summary>
        [Description("区域名称")]
        public string RegionName {get;set;}
        /// <summary>
        /// 库位编号
        /// </summary>
        [Description("库位编号")]
        public string BinLocationCode {get;set;}
        /// <summary>
        /// 库位名称
        /// </summary>
        [Description("库位名称")]
        public string BinLocationName {get;set;}

    }
}

