using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;
using HZ.Core.Http;
using HZ.Core.Office;
using HZ.WMS.Application.MD;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.MD.Import;
using HZ.WMS.Entity.Sys;
using HZ.WMS.WebAPI.Controllers;

namespace HZ.WMS.WebAPI.Areas.MD.Controllers
{
    /// <summary>
    /// 报工站点物料
    /// </summary>
    public class MD_ReportStationMaterialController : ApiBaseController
    {
        private MD_ReportStationMaterialApp _app = new MD_ReportStationMaterialApp();

        #region 查询分页列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="page">分页参数</param>
        /// <param name="keyword">关键字</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]string keyword)
        {
            var result = new ResponseData();
            try
            {
                var query = _app.GetList();
                if (!string.IsNullOrEmpty(keyword))
                {
                    query = query.Where(t => t.WorkCenterName.Contains(keyword) || 
                                            t.WorkCenterCode.Contains(keyword) || 
                                            t.ProcessShortText.Contains(keyword) || 
                                            t.StationCode.Contains(keyword) || 
                                            t.StationName.Contains(keyword) || 
                                            t.MaterialCode.Contains(keyword) || 
                                            t.MaterialDesc.Contains(keyword));
                }
                
                var count = query.Count();
                page.Total = count;
                var data = query.OrderBy(t => t.WorkCenterCode).Skip((page.PageNumber - 1) * page.PageSize).Take(page.PageSize).ToList();
                
                result.Data = new ResponsePageData { total = count, items = data };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 添加

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="entity">实体</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Add([FromBody]MD_ReportStationMaterial entity)
        {
            var result = new ResponseData();
            try
            {
                // 验证工作中心站点是否存在
                string error;
                if (!_app.ValidateWorkCenterStationInfo(entity.WorkCenterCode, entity.StationCode, out error))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error;
                    return Json(result);
                }
                
                Sys_User currLoginUser = GetCurrentUser();
                entity.Id = Guid.NewGuid().ToString();
                entity.IsDelete = false;
                entity.CUser = currLoginUser.LoginAccount;
                entity.MUser = currLoginUser.LoginAccount;
                entity.CTime = DateTime.Now;
                entity.MTime = DateTime.Now;
                
                // 保存站点物料
                result.Data = _app.Insert(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="entity">实体</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Update([FromBody]MD_ReportStationMaterial entity)
        {
            var result = new ResponseData();
            try
            {
                // 验证工作中心站点是否存在
                string error;
                if (!_app.ValidateWorkCenterStationInfo(entity.WorkCenterCode, entity.StationCode, out error))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error;
                    return Json(result);
                }
                
                var currentUser = GetCurrentUser().LoginAccount;
                entity.MUser = currentUser;
                entity.MTime = DateTime.Now;
                
                // 更新站点物料
                result.Data = _app.Update(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除（包括批量）

        /// <summary>
        /// 删除
        /// </summary>
        /// <returns></returns>
        [HttpDelete]
        public IHttpActionResult Delete([FromBody]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.DeleteByKeys(ids, GetCurrentUser().LoginAccount);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 导出

        /// <summary>
        /// 导出
        /// </summary>
        /// <param name="keyword">关键字</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] string keyword)
        {
            var result = new ResponseData();
            try
            {
                var query = _app.GetList();
                if (!string.IsNullOrEmpty(keyword))
                {
                    query = query.Where(t => t.WorkCenterName.Contains(keyword) || 
                                            t.WorkCenterCode.Contains(keyword) || 
                                            t.ProcessShortText.Contains(keyword) || 
                                            t.StationCode.Contains(keyword) || 
                                            t.StationName.Contains(keyword) || 
                                            t.MaterialCode.Contains(keyword) || 
                                            t.MaterialDesc.Contains(keyword));
                }
                
                var itemsData = query.OrderBy(t => t.WorkCenterCode).ToList();
                List<ExcelColumn<MD_ReportStationMaterial>> columns = ExcelService.FetchDefaultColumnList<MD_ReportStationMaterial>();
                string[] ignoreField = new string[] { "IsDelete", "DTime", "DUser" };
                var ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<MD_ReportStationMaterial> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }

                return ExportToExcelFile<MD_ReportStationMaterial>(itemsData, columns,
                    GetCurrentUser().UserName + "_报工站点物料");
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #region 导入

        /// <summary>
        /// 导入
        /// </summary>
        /// <param name="entitys">接收的导入集合</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult ImportExcelToData([FromBody]List<MD_ReportStationMaterialImport> entitys)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                if (_app.ImportExcelToBaseData(entitys, GetCurrentUser().LoginAccount, out error_message))
                {
                    result.Data = true;
                    result.Code = (int)WMSStatusCode.Success;
                }
                else
                {
                    result.Data = false;
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 导出模板

        /// <summary>
        /// 导出模板
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelModel()
        {
            var result = new ResponseData();
            try
            {
                List<MD_ReportStationMaterialImport> modelList = new List<MD_ReportStationMaterialImport>();
                modelList.Add(new MD_ReportStationMaterialImport
                {
                    WorkCenterName = "工作中心示例",
                    WorkCenterCode = "WC001",
                    ProcessNo = 10,
                    ProcessShortText = "工序示例",
                    StationCode = "ST001",
                    StationName = "站点示例",
                    MaterialCode = "MAT001",
                    MaterialDesc = "物料示例",
                    Quantity = 10.00m,
                    Unit = "PCS",
                    Remark = "备注示例"
                });

                var columns = ExcelService.FetchDefaultColumnList<MD_ReportStationMaterialImport>();
                return ExportToExcelFile<MD_ReportStationMaterialImport>(modelList, columns, "报工站点物料模板");
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion
    }
} 