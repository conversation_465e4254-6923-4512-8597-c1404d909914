using System.ComponentModel;

namespace HZ.WMS.Entity.Produce
{
    
    /// <summary>
    /// 编辑计划订单
    /// </summary>
    public class EditPlanOrder : BaseEntity
    {
        [Description("消息类型")]
        public string ETYPE { get; set; }

        [Description("消息文本")]
        public string EMSG { get; set; }

        [Description("DATA_IN")]
        public Table001086[] DataIn { get; set; }

        public class Table001086
        {
            
            [Description("工厂")]
            public string WERKS { get; set; }

            [Description("销售订单号")]
            public string VBELN { get; set; }

            [Description("销售订单号")]
            public string POSNR { get; set; }

            [Description("计划开始日期")]
            public string PSTTR { get; set; }

            [Description("生产版本")]
            public string VERID { get; set; }
            
        }
    }
    
} 