using HZ.WMS.Entity;
using HZ.Core.Http;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Linq;
using System.Text;
using System.Reflection;
using SqlSugar;

namespace HZ.WMS.Application
{

    /// <summary>
    /// Ado.net 并不支持并行事务，所以批量操作都单独重载一个带有事务参数的方法
    /// 外层添加事务的情况下，事务内处理时请不要使用带有Tran结尾的方法
    ///  where T : BaseEntity, new()
    /// 
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class BaseApp<T> : ContentBase where T : BaseEntity, new()
    {
        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public BaseApp() {}

        #endregion
        
        #region 更改链接数据库

        /// <summary>
        /// 更改链接数据库
        /// </summary>
        public BaseApp(string connect) : base(connect){}

        #endregion
        
        #region 添加(修正完毕)

        #region  单个实体添加

        /// <summary>
        /// 添加单实体，返回成功插入的实体
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public T Insert(T entity)
        {
            if (string.IsNullOrEmpty(entity.GetPrimaryKeyValue()?.ToString()))
            {
                entity.SetDefaultValueToPrimaryKey();
            }

            //获取实体自定义特性字段
            Type objType = entity.GetType();
            //取属性上的自定义特性
            foreach (PropertyInfo propInfo in objType.GetProperties())
            {
                object[] objAttrs = propInfo.GetCustomAttributes(typeof(UniqueCodeAttribute), true);
                if (objAttrs.Length > 0)
                {
                    UniqueCodeAttribute attr = objAttrs[0] as UniqueCodeAttribute;
                    if (attr != null)
                    {
                        //自定义特性唯一性校验
                        T existT = GetFirstEntityByFieldValue(propInfo.Name, propInfo.GetValue(entity));
                        if (existT != null)
                        {
                            //编号重复，请输入新的正确编号
                            throw new Exception("Common.ExistedCode");
                        }
                    }
                }
            }

            return DbContext.Insertable(entity).ExecuteReturnEntity();
        }

        #endregion

        #region 批量插入

        /// <summary>
        /// 批量插入，不带有事务控制
        /// </summary>
        /// <param name="entities"></param>
        /// <returns></returns>
        public int Insert(List<T> entities)
        {
            if (entities == null || entities.Count == 0) return 0;
            try
            {
                entities.ForEach(t =>
                {
                    if (string.IsNullOrEmpty(t.GetPrimaryKeyValue()?.ToString()))
                    {
                        t.SetDefaultValueToPrimaryKey();
                    }
                });

                DbContext.Insertable(entities).ExecuteCommand();
                return entities.Count;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        #endregion

        #region 批量插入带事务
        /// <summary>
        /// 批量插入带有事务处理 - 安全版本
        /// </summary>
        /// <param name="entities"></param>
        /// <returns></returns>
        public int InsertWithTran(List<T> entities)
        {
            return ExecuteTransactionWithRetry(db =>
            {
                int iCount = 0;

                // 使用批量插入提高性能
                if (entities.Count > 100)
                {
                    // 大批量数据使用SqlBulkCopy
                    foreach (T entity in entities)
                    {
                        if (string.IsNullOrEmpty(entity.GetPrimaryKeyValue()?.ToString()))
                        {
                            entity.SetDefaultValueToPrimaryKey();
                        }
                    }
                    iCount = db.Insertable(entities).ExecuteCommand();
                }
                else
                {
                    // 小批量数据逐条插入
                    foreach (T entity in entities)
                    {
                        if (string.IsNullOrEmpty(entity.GetPrimaryKeyValue()?.ToString()))
                        {
                            entity.SetDefaultValueToPrimaryKey();
                        }
                        db.Insertable<T>(entity).ExecuteCommand();
                        iCount++;
                    }
                }

                return iCount;
            });
        }

        /// <summary>
        /// 安全执行事务操作
        /// </summary>
        /// <typeparam name="TResult">返回类型</typeparam>
        /// <param name="operation">事务操作函数</param>
        /// <param name="maxRetries">最大重试次数</param>
        /// <returns></returns>
        private TResult ExecuteTransactionWithRetry<TResult>(Func<SqlSugarClient, TResult> operation, int maxRetries = 3)
        {
            Exception lastException = null;

            for (int i = 0; i < maxRetries; i++)
            {
                SqlSugarClient client = null;
                try
                {
                    // 使用SqlSugarClientFactory获取连接，确保连接池管理
                    client = SqlSugarClientFactory.GetClient("DbConnection");

                    // 确保连接状态正常
                    EnsureConnectionState(client);

                    try
                    {
                        client.Ado.BeginTran();
                        var result = operation(client);
                        client.Ado.CommitTran();
                        return result;
                    }
                    catch
                    {
                        if (client.Ado.IsAnyTran())
                        {
                            client.Ado.RollbackTran();
                        }
                        throw;
                    }
                }
                catch (Exception ex) when (IsConnectionRelatedError(ex))
                {
                    lastException = ex;
                    HZ.Core.Logging.LogHelper.Instance.LogError($"事务执行错误，第 {i + 1} 次重试: {ex.Message}");

                    if (i < maxRetries - 1)
                    {
                        // 等待一段时间后重试
                        System.Threading.Thread.Sleep(1000 * (i + 1));
                    }
                }
                finally
                {
                    // 确保连接被正确释放
                    try
                    {
                        if (client != null && client.Ado.Connection != null)
                        {
                            if (client.Ado.Connection.State == System.Data.ConnectionState.Open)
                            {
                                client.Ado.Connection.Close();
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        HZ.Core.Logging.LogHelper.Instance.LogWarn($"关闭数据库连接时发生异常: {ex.Message}");
                    }
                }
            }

            throw new Exception($"事务操作失败，已重试 {maxRetries} 次", lastException);
        }

        /// <summary>
        /// 确保连接状态正常
        /// </summary>
        /// <param name="client"></param>
        private void EnsureConnectionState(SqlSugarClient client)
        {
            try
            {
                // 检查连接状态
                if (client.Ado.Connection.State != System.Data.ConnectionState.Open)
                {
                    client.Ado.Connection.Open();
                }

                // 执行简单查询测试连接
                client.Ado.GetString("SELECT 1");
            }
            catch (Exception ex)
            {
                HZ.Core.Logging.LogHelper.Instance.LogError($"连接状态检查失败: {ex.Message}");

                // 尝试重新打开连接
                try
                {
                    if (client.Ado.Connection.State != System.Data.ConnectionState.Closed)
                    {
                        client.Ado.Connection.Close();
                    }
                    client.Ado.Connection.Open();
                }
                catch (Exception reopenEx)
                {
                    throw new Exception("无法重新建立数据库连接", reopenEx);
                }
            }
        }

        /// <summary>
        /// 判断是否为连接相关错误
        /// </summary>
        /// <param name="ex"></param>
        /// <returns></returns>
        private bool IsConnectionRelatedError(Exception ex)
        {
            var message = ex.Message.ToLower();
            return message.Contains("executereader") ||
                   message.Contains("connection") ||
                   message.Contains("timeout") ||
                   message.Contains("pool") ||
                   message.Contains("已打开且可用") ||
                   message.Contains("当前状态") ||
                   message.Contains("网络相关") ||
                   message.Contains("传输级错误");
        }

        #endregion

        #region 大数据批量插入

        /// <summary>
        /// 批量快速添加，不带有事务控制
        /// </summary>
        /// <param name="entities">The entities.</param>
        public void BulkInsert(List<T> entities)
        {
            base.BulkInsert(entities);
        }

        #endregion

        #endregion

        #region 更新(完毕)

        /// <summary>
        /// 单个实体更新
        /// </summary>
        /// <param name="entity"></param>
        public int Update(T entity)
        {
            entity.MTime = DateTime.Now;
            return this.DbContext.Updateable(entity).ExecuteCommand();
        }

        /// <summary>
        /// 多个实体更新
        /// </summary>
        /// <param name="entities"></param>
        /// <returns>返回更新行数</returns>
        public int Update(List<T> entities)
        {
            var iCount = 0;
            try
            {
                foreach (T entity in entities)
                {
                    //this.DbContext.Update(entity);
                    this.Update(entity);
                    iCount++;
                }
            }
            catch (Exception ex)
            {
                iCount = 0;
                throw new Exception(ex.Message);
            }
            return iCount;
        }

        // internal object GetPageList<SysUser>(Pagination page, Expression<Func<SysUser, bool>> condition)
        // {
        //     throw new NotImplementedException();
        // }

        /// <summary>
        /// 多个实体更新
        /// </summary>
        /// <param name="entities"></param>
        /// <returns></returns>
        public int UpdateWithTran(List<T> entities)
        {
            var iCount = 0;
            try
            {
                this.DbContext.Ado.BeginTran();
                foreach (T entity in entities)
                {
                    //this.DbContext.Update(entity);
                    this.Update(entity);
                    iCount++;
                }
                this.DbContext.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                iCount = 0;
                this.DbContext.Ado.RollbackTran();
                throw new Exception(ex.Message);
            }
            return iCount;
        }

        /// <summary>
        /// 根据条件单个实体更新
        /// <para>如：Update(entity,u =>u.id==1);</para>
        /// </summary>
        /// <param name="entity"></param>
        /// <param name="condition"></param>
        /// <returns></returns>
        public int Update(T entity, Expression<Func<T, bool>> condition)
        {
            entity.MTime = DateTime.Now;
            return this.DbContext.Updateable(entity).Where(condition).ExecuteCommand();
        }

        /// <summary>
        /// 实现按需要只更新部分更新
        /// <para>如：Update(u =>u.Id==1,u =>new User{Name="ok"});</para>
        /// </summary>
        /// <param name="where">The where.</param>
        /// <param name="entity">The entity.</param>
        public int Update(Expression<Func<T, bool>> where, Expression<Func<T, T>> entity)
        {
            return DbContext.Updateable<T>(entity).Where(where).ExecuteCommand();
        }

        #endregion

        #region 逻辑删除(完毕)

        /// <summary>
        /// 逻辑删除
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public int Delete(T obj)
        {
            // DbContext.TrackEntity(obj);
            obj.IsDelete = true;
            obj.DTime = DateTime.Now;
            return DbContext.Updateable<T>(obj).ExecuteCommand();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="entities"></param>
        /// <returns></returns>
        public int Delete(List<T> entities)
        {
            int iCount = 0;
            try
            {
                entities.ForEach(delegate (T t)
                {
                    // DbContext.TrackEntity(t);
                    t.IsDelete = true;
                    t.DTime = DateTime.Now;
                    DbContext.Updateable(t).ExecuteCommand();
                    iCount++;
                });
            }
            catch (Exception ex)
            {
                iCount = 0;
                throw new Exception(ex.Message);
            }
            return iCount;
        }

        /// <summary>
        /// 删除多个实体
        /// </summary>
        /// <param name="entities"></param>
        /// <returns></returns>
        public int DeleteWithTran(List<T> entities)
        {
            int iCount = 0;
            try
            {
                this.DbContext.Ado.BeginTran();
                entities.ForEach(delegate (T t)
                {
                    // DbContext.TrackEntity(t);
                    t.IsDelete = true;
                    t.DTime = DateTime.Now;
                    DbContext.Updateable(t).ExecuteCommand();
                    iCount++;
                });
                this.DbContext.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                iCount = 0;
                this.DbContext.Ado.RollbackTran();
                throw new Exception(ex.Message);
            }
            return iCount;
        }

        /// <summary>
        /// 删除多个实体
        /// </summary>
        /// <param name="entities">待实体列表</param>
        /// <param name="deleteUser">删除者唯一账号</param>
        /// <returns></returns>
        public int Delete(List<T> entities, string deleteUser)
        {
            int iCount = 0;
            try
            {
                entities.ForEach(delegate (T t)
                {
                    // DbContext.TrackEntity(t);
                    t.IsDelete = true;
                    t.DTime = DateTime.Now;
                    t.DUser = deleteUser;
                    DbContext.Updateable(t).ExecuteCommand();
                    iCount++;
                });

            }
            catch (Exception ex)
            {
                iCount = 0;
                throw new Exception(ex.Message);
            }
            return iCount;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="entities"></param>
        /// <returns></returns>
        public int DeleteWithTran(List<T> entities, string deleteUser)
        {
            int iCount = 0;
            try
            {
                this.DbContext.Ado.BeginTran();
                entities.ForEach(delegate (T t)
                {
                    // DbContext.TrackEntity(t);
                    t.IsDelete = true;
                    t.DTime = DateTime.Now;
                    t.DUser = deleteUser;
                    DbContext.Updateable(t).ExecuteCommand();
                    iCount++;
                });
                this.DbContext.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                iCount = 0;
                this.DbContext.Ado.RollbackTran();
                throw new Exception(ex.Message);
            }
            return iCount;
        }

        /// <summary>
        /// 逻辑删除
        /// </summary>
        /// <param name="key"></param>
        /// <param name="deleteUser"></param>
        /// <returns></returns>
        public int DeleteByKey(object key, string deleteUser)
        {
            try
            {
                T obj = DbContext.Queryable<T>().InSingle(key);
                // DbContext.TrackEntity(obj);

                obj.IsDelete = true;
                obj.DUser = deleteUser;
                obj.DTime = DateTime.Now;

                return DbContext.Updateable(obj).ExecuteCommand();
            }
            catch(Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// 根据主键数组逻辑删除（必须是主键，且单一主键）
        /// </summary>
        /// <param name="keys">主键数组</param>
        /// <param name="deleteUser">删除者账号</param>
        /// <returns></returns>
        public int DeleteByKeys(object[] keys, string deleteUser)
        {
            int iCount = 0;
            try
            {
                foreach (object key in keys)
                {
                    DeleteByKey(key, deleteUser);
                    iCount++;
                }
            }
            catch (Exception ex)
            {
                iCount = 0;
                throw new Exception(ex.Message);
            }

            return iCount;
        }

        /// <summary>
        /// 逻辑删除
        /// </summary>
        /// <param name="keys">主键数组</param>
        /// <param name="deleteUser">删除者账号</param>
        /// <returns></returns>
        public int DeleteByKeysWithTran(object[] keys, string deleteUser)
        {
            int iCount = 0;
            try
            {
                this.DbContext.Ado.BeginTran();
                foreach (object key in keys)
                {
                    DeleteByKey(key, deleteUser);
                    iCount++;
                }
                this.DbContext.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                iCount = 0;
                this.DbContext.Ado.RollbackTran();
                throw new Exception(ex.Message);
            }

            return iCount;
        }

        /// <summary>
        /// 根据条件删除
        /// </summary>
        /// <param name="where">删除条件</param>
        /// <param name="deleteUser">删除者账号</param>
        /// <returns></returns>
        public int Delete(Expression<Func<T, bool>> where, string deleteUser)
        {
            return DbContext.Updateable(new T() { IsDelete = true, DTime = DateTime.Now, DUser = deleteUser }).Where(where).ExecuteCommand();
        }



        #endregion

        #region 物理删除(完毕)

        /// <summary>
        /// 
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public int HardDelete(T entity)
        {
            return this.DbContext.Deleteable(entity).ExecuteCommand();
        }

        /// <summary>
        /// 物理删除
        /// </summary>
        /// <param name="entities">实体列表</param>
        /// <returns></returns>
        public int HardDelete(List<T> entities)
        {
            int iCount = 0;
            try
            {
                entities.ForEach(delegate (T entity)
                {
                    this.DbContext.Deleteable(entity).ExecuteCommand();
                    iCount++;
                });
            }
            catch (Exception ex)
            {
                iCount = 0;
                throw new Exception(ex.Message);
            }
            return iCount;
        }

        /// <summary>
        /// 物理删除
        /// </summary>
        /// <param name="entities">实体列表</param>
        /// <returns></returns>
        public int HardDeleteWithTran(List<T> entities)
        {
            int iCount = 0;
            try
            {
                this.DbContext.Ado.BeginTran();
                entities.ForEach(delegate (T entity)
                {
                    this.DbContext.Deleteable(entity).ExecuteCommand();
                    iCount++;
                });
                this.DbContext.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                iCount = 0;
                this.DbContext.Ado.RollbackTran();
                throw new Exception(ex.Message);
            }
            return iCount;
        }

        /// <summary>
        /// 根据主键物理删除
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        public int HardDeleteByKey(T obj)
        {
            return this.DbContext.Deleteable<T>(obj).ExecuteCommand();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="objs"></param>
        /// <returns></returns>
        public int HardDeleteByKeys(T[] objs)
        {
            int iCount = 0;
            try
            {
                foreach (T obj in objs)
                {
                    HardDeleteByKey(obj);
                    iCount++;
                }
            }
            catch (Exception ex)
            {
                iCount = 0;
                throw new Exception(ex.Message);
            }
            return iCount;
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="keys"></param>
        /// <returns></returns>
        public int HardDeleteByKeysWithTran(T[] objs)
        {
            int iCount = 0;
            try
            {
                this.DbContext.Ado.BeginTran();
                foreach (T obj in objs)
                {
                    HardDeleteByKey(obj);
                    iCount++;
                }

                this.DbContext.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                iCount = 0;
                this.DbContext.Ado.RollbackTran();
                throw new Exception(ex.Message);
            }
            return iCount;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="where"></param>
        /// <returns></returns>
        public int HardDelete(Expression<Func<T, bool>> where)
        {
            return this.DbContext.Deleteable<T>().Where(where).ExecuteCommand();
        }

        #endregion

        #region 获取单个实体(完毕)

        /// <summary>
        /// 逻辑删除
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        public T GetEntityByKey(object key)
        {
            T entity = DbContext.Queryable<T>().InSingle(key);
            return entity==null || entity.IsDelete ? null : entity;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="where"></param>
        /// <returns></returns>
        public T GetFirstEntity(Expression<Func<T, bool>> where)
        {
            return this.DbContext.Queryable<T>().Where(where).Where(t => !t.IsDelete).ToList().FirstOrDefault();
        }

        #endregion

        #region 是否存在

        /// <summary>
        /// 是否存在
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        public bool Any(Expression<Func<T, bool>> condition)
        {
            var query = DbContext.Queryable<T>()
                    .Where(x => !x.IsDelete)   //排除已经逻辑删除的记录
                    .Any(condition);
            return query;
        }


        #endregion

        #region 获取列表

        /// <summary>
        /// 按条件查询，默认按创建时间排序
        /// </summary>
        /// <param name="condition"></param>
        /// <param name="orderBy"></param>
        /// <returns></returns>
        public ISugarQueryable<T> GetList(Expression<Func<T, bool>> condition,string orderBy = null)
        {
            if (string.IsNullOrEmpty(orderBy))
            {
                orderBy = "CTime desc";
            }
            else
            {
                orderBy = orderBy.Replace("ascending", "asc").Replace("descending", "desc");
            }
            var query = DbContext.Queryable<T>()
                    .Where(x => !x.IsDelete)   //排除已经逻辑删除的记录
                    .Where(condition)
                    .OrderBy(orderBy);

            return query;
        }

        /// <summary>
        /// 查询 去重 sqlSugar orderBy与distinct不能连用
        /// </summary>
        /// <param name="condition"></param>
        /// <param name="orderBy"></param>
        /// <returns></returns>
        public ISugarQueryable<T> GetListDistinct(Expression<Func<T, bool>> condition)
        {
            var query = DbContext.Queryable<T>()
                .Where(x => !x.IsDelete) //排除已经逻辑删除的记录
                .Where(condition)
                .Distinct();
            return query;
        }

        /// <summary>
        /// 用于聚合查询的方法，不包含排序，避免与聚合函数冲突
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        public ISugarQueryable<T> GetListForAggregation(Expression<Func<T, bool>> condition)
        {
            var query = DbContext.Queryable<T>()
                .Where(x => !x.IsDelete) //排除已经逻辑删除的记录
                .Where(condition);
            return query;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public ISugarQueryable<T> GetList()
        {
            return DbContext.Queryable<T>()
                    .Where(x => !x.IsDelete);   //排除已经逻辑删除的记录
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="keys"></param>
        /// <returns></returns>
        public List<T> GetListByKeys(object[] keys)
        {
            List<T> list = new List<T>();
            foreach (object key in keys)
            {
                T item = GetEntityByKey(key);
                if (item != null)
                {
                    list.Add(item);
                }

            }
            //return DbContext.Queryable<T>(t=>keys.Contains(t.))

            return list;
        }


        #endregion

        #region 获取分页列表

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="TKey"></typeparam>
        /// <param name="page"></param>
        /// <param name="condition"></param>
        /// <param name="lockType"></param>
        /// <returns></returns>
        public List<T> GetPageList(Pagination page, Expression<Func<T, bool>> condition ,string lockType)
        {
            if (string.IsNullOrEmpty(page.Sort))
            {
                page.Sort = "CTime desc";
            }
            else
            {
                page.Sort = page.Sort.Replace("ascending", "asc").Replace("descending", "desc");
            }

            var query = DbContext.Queryable<T>()
                .With(lockType)
                .Where(x => !x.IsDelete)
                .Where(condition)
                .OrderBy(page.Sort);

            int total = query.Count();
            page.Total = total;
            if (page.PageSize > total)
            {
                return query.ToPageList(page.PageNumber, total);
            }
            return query.ToPageList(page.PageNumber, page.PageSize);
        }

        /// <summary>
        /// 获取分页数据，默认按照创建时间降序
        /// </summary>
        /// <param name="page"></param>
        /// <param name="condition"></param>
        /// <returns></returns>
        public List<T> GetPageList(Pagination page, Expression<Func<T, bool>> condition)
        {
            if (string.IsNullOrEmpty(page.Sort))
            {
                page.Sort = "CTime desc";
            }
            else
            {
                page.Sort = page.Sort.Replace("ascending", "asc").Replace("descending", "desc");
            }

            var query = DbContext.Queryable<T>()
                .Where(x => !x.IsDelete)
                .Where(condition);

            int total = query.Count();

            query = query.OrderBy(page.Sort);
            page.Total = total;
            if (page.PageSize > total)
            {
                return query.ToPageList(page.PageNumber, total);
            }
            return query.ToPageList(page.PageNumber, page.PageSize);
        }
        
        /// <summary>
        /// 获取分页数据，默认按照创建时间降序
        /// </summary>
        /// <param name="page"></param>
        /// <param name="condition"></param>
        /// <returns></returns>
        public List<T> GetPageList(Pagination page, Expression<Func<T, bool>> condition, SqlSugarClient dbContext)
        {
            if (string.IsNullOrEmpty(page.Sort))
            {
                page.Sort = "CTime desc";
            }
            else
            {
                page.Sort = page.Sort.Replace("ascending", "asc").Replace("descending", "desc");
            }

            var query = dbContext.Queryable<T>()
                .Where(x => !x.IsDelete)
                .Where(condition)
                .OrderBy(page.Sort);

            int total = query.Count();
            page.Total = total;
            if (page.PageSize > total)
            {
                return query.ToPageList(page.PageNumber, total);
            }
            return query.ToPageList(page.PageNumber, page.PageSize);
        }

        /// <summary>
        /// 获取分页数据，默认按照创建时间降序
        /// </summary>
        /// <param name="page"></param>
        /// <returns></returns>
        public List<T> GetPageList(Pagination page)
        {
            if (string.IsNullOrEmpty(page.Sort))
            {
                page.Sort = "CTime desc";
            }
            else
            {
                page.Sort = page.Sort.Replace("ascending", "asc").Replace("descending", "desc");
            }
            var query = DbContext.Queryable<T>()
                .Where(x => !x.IsDelete)
                .OrderBy(page.Sort);

            int total = query.Count();
            page.Total = total;
            if (page.PageSize > total)
            {
                return query.ToPageList(page.PageNumber, total);
            }
            return query.ToPageList(page.PageNumber, page.PageSize);
        }




        #endregion

        #region 获取列表包含已删除数据

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public ISugarQueryable<T> GetAllList()
        {
            return DbContext.Queryable<T>();
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        public ISugarQueryable<T> GetAllList(Expression<Func<T, bool>> condition)
        {
            var query = DbContext.Queryable<T>()
                    .Where(condition);
            return query;
        }



        #endregion

        #region 获取分页列表（包含删除数据）

        /// <summary>
        /// 
        /// </summary>
        /// <param name="page"></param>
        /// <param name="condition"></param>
        /// <returns></returns>
        public List<T> GetAllPageList(Pagination page, Expression<Func<T, bool>> condition)
        {
            if (string.IsNullOrEmpty(page.Sort))
            {
                page.Sort = "CTime desc";
            }
            else
            {
                page.Sort = page.Sort.Replace("ascending", "asc").Replace("descending", "desc");
            }
            var query = DbContext.Queryable<T>()
                .Where(condition)
                .OrderBy(page.Sort);

            int total = query.Count();
            page.Total = total;
            if (page.PageSize > total)
            {
                return query.ToPageList(page.PageNumber, total);
            }
            return query.ToPageList(page.PageNumber, page.PageSize);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="page"></param>
        /// <returns></returns>
        public List<T> GetAllPageList(Pagination page)
        {
            if (string.IsNullOrEmpty(page.Sort))
            {
                page.Sort = "CTime desc";
            }
            else
            {
                page.Sort = page.Sort.Replace("ascending", "asc").Replace("descending", "desc");
            }
            var query = DbContext.Queryable<T>()
                .OrderBy(page.Sort);

            int total = query.Count();
            page.Total = total;
            if (page.PageSize > total)
            {
                return query.ToPageList(page.PageNumber, total);
            }
            return query.ToPageList(page.PageNumber, page.PageSize);
        }

        #endregion

        #region 存储过程

        /// <summary>
        /// 执行存储过程返回实体列表
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="procName"></param>
        /// <param name="cmdType"></param>
        /// <param name="parameters"></param>
        /// <returns></returns>
        public object SqlQuery(string procName, System.Data.CommandType cmdType, params SugarParameter[] parameters)
        {
            var paramArray = parameters?.ToArray() ?? new SugarParameter[0];
            return this.DbContext.Ado.UseStoredProcedure().SqlQuery<object>(procName, paramArray).ToList();
        }





        #endregion

        #region 通过指定字段和值，作为查询条件，获取实体

        /// <summary>
        /// 通过指定字段和值，作为查询条件，获取实体
        /// </summary>
        /// <param name="field"></param>
        /// <param name="value"></param>
        /// <returns></returns>
        public T GetFirstEntityByFieldValue(string field, object value)
        {
            return DbContext.Ado.SqlQuery<T>("select * from " + typeof(T).Name + " where IsDelete=0 and " + field + "=@value", new List<SugarParameter>()
            {
                new SugarParameter("@value", value) //执行sql语句
            }).FirstOrDefault();
        }

        #endregion

        #region 通过指定字段和值，作为查询条件，获取实体

        /// <summary>
        /// 通过指定字段和值，作为查询条件，获取实体
        /// </summary>
        /// <param name="fields"></param>
        /// <param name="values"></param>
        /// <returns></returns>
        public T GetFirstEntityByFieldsValues(string[] fields, object[] values)
        {
            List<SugarParameter> pList = new List<SugarParameter>();
            StringBuilder sb = new StringBuilder("select * from ");
            sb.Append(typeof(T).Name).Append(" where IsDelete=0 ");
            for (int i = 0; i < fields.Length; i++)
            {
                string field = fields[i];
                sb.Append(" and ").Append(field).Append("=@").Append(field);
                pList.Add(new SugarParameter("@" + field, values[i]));
            }

            return this.DbContext.Ado.SqlQuery<T>(sb.ToString(), pList).FirstOrDefault();
        }

        #endregion

        #region 返回DataTable扩展
        /// <summary>
        /// 返回DataTable扩展
        /// </summary>
        /// <param name="cmdText"></param>
        /// <param name="objParams"></param>
        /// <returns></returns>
        // public DataTable ExecuteDataTable(string cmdText,object objParams)
        // {
        //     return DbContext.Ado.GetDataTable(cmdText, objParams);
        // }


        #endregion

        #region SQL执行查询，分页方法

        /// <summary>
        /// 
        /// </summary>
        /// <param name="page"></param>
        /// <param name="strSql"></param>
        /// <returns></returns>
        public IEnumerable<T> GetDataTableByPage(Pagination page, string strSql)
        {
            //DataTable dt = new DataTable();

            StringBuilder sbSql = new StringBuilder();
            sbSql.Append("P_QueryByPage ");
            //SqlParameter returnValue = new SqlParameter("@returnValue", SqlDbType.Int);
            //returnValue.Direction = ParameterDirection.ReturnValue;

            SugarParameter returnValue = new SugarParameter("@returnValue", 0, true);
            SugarParameter[] parameters =
            {
                new SugarParameter("@page", page.PageNumber),
                new SugarParameter("@pagesize", page.PageSize),
                new SugarParameter("@Sql",strSql),
                new SugarParameter("@Sort", page.Sort),
                new SugarParameter("@OrderField", ""),
                returnValue
            };
            //SqlParameter[] parameters = {
            //    new SqlParameter("@Sql", SqlDbType.VarChar,8000),
            //    new SqlParameter("@Sort", SqlDbType.VarChar,300),
            //    new SqlParameter("@page", SqlDbType.Int),
            //    new SqlParameter("@pagesize", SqlDbType.Int),
            //    new SqlParameter("@OrderField", SqlDbType.VarChar,500),returnValue};
            //parameters[0].Value = strSql;
            //parameters[1].Value = strSort;
            //parameters[2].Value = nCurrentPage;
            //parameters[3].Value = nPageSize;
            //parameters[4].Value = orderField;

            //dt = DbContext.Ado.GetDataTable(sbSql.ToString(), CommandType.StoredProcedure, parameters);
            var res = DbContext.Ado.UseStoredProcedure().SqlQuery<T>(sbSql.ToString(), parameters);
            page.Total = Convert.ToInt32(returnValue.Value);
            //page.Total = Convert.ToInt32(returnValue.Value);
            return res;

            //dt = dbHandler.GetDataTable(sbSql.ToString(), true, parameters);
            //nTotalRows = int.Parse(returnValue.Value.ToString());
        }

        #endregion
    }
}