using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.MM
{
    /// <summary>
    /// 委外入库明细表
    /// </summary>
    [SugarTable("MM_WarehousingDetail")]
    public class MM_WarehousingDetail : BaseEntity
    {
        /// <summary> 
        /// ID
        /// </summary> 
        [SugarColumn(IsPrimaryKey = true)]
        [Description("ID")]
        public string DetailID { get; set; }

        /// <summary> 
        /// 单号
        /// </summary> 
        [Description("单号")]
        public string DocNum { get; set; }

        /// <summary> 
        /// 行号
        /// </summary> 
        [Description("行号")]
        public int? Line { get; set; }

        /// <summary> 
        /// 采购订单号
        /// </summary> 
        [Description("采购订单号")]
        public string BaseNum { get; set; }

        /// <summary> 
        /// 采购订单行号
        /// </summary> 
        [Description("采购订单行号")]
        public int? BaseLine { get; set; }

        /// <summary> 
        /// 组件编号
        /// </summary> 
        [Description("组件编号")]
        public string ItemCode { get; set; }

        /// <summary> 
        /// 组件名称
        /// </summary> 
        [Description("组件名称")]
        public string ItemName { get; set; }

        /// <summary> 
        /// 供应商编号
        /// </summary> 
        [Description("供应商编号")]
        public string SupplierCode { get; set; }

        /// <summary> 
        /// 供应商名称
        /// </summary> 
        [Description("供应商名称")]
        public string SupplierName { get; set; }

        /// <summary> 
        /// 数量
        /// </summary> 
        [Description("数量")]
        public decimal? Qty { get; set; }

        /// <summary> 
        /// 单位
        /// </summary> 
        [Description("单位")]
        public string Unit { get; set; }
        
        /// <summary> 
        /// 是否过账 1：已过账，0：未过账
        /// </summary> 
        [Description("是否过账")]
        public bool IsPosted { get; set; }

        /// <summary> 
        /// 过账人
        /// </summary> 
        [Description("过账人")]
        public string PostUser { get; set; }

        /// <summary> 
        /// 过账时间
        /// </summary> 
        [Description("过账时间")]
        public DateTime? PostTime { get; set; }

        /// <summary> 
        /// 工厂代码
        /// </summary> 
        [Description("工厂代码")]
        public string FactoryCode { get; set; }

        /// <summary> 
        /// SAP物料凭证单号
        /// </summary> 
        [Description("SAP物料凭证单号")]
        public string SapDocNum { get; set; }

        /// <summary> 
        /// SAP物料凭证行号
        /// </summary> 
        [Description("SAP物料凭证行号")]
        public int? SapLine { get; set; }

        /// <summary>
        /// 相关需求的编号
        /// </summary>
        [Description("相关需求的编号")]
        public decimal? RSNUM { get; set; }

        /// <summary>
        /// 相关需求的项目编号
        /// </summary>
        [Description("相关需求的项目编号")]
        public decimal? RSPOS { get; set; }

        /// <summary>
        /// 借方/贷方标识 H：减少库存 S：添加库存 为S必须为负数
        /// </summary>
        [Description("借方/贷方标识")]
        public string SHKZG { get; set; }
        



    }
}
