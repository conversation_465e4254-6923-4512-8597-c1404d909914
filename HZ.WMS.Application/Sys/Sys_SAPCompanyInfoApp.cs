using System;
using System.Collections.Generic;
using System.Linq;
using HZ.Core.Logging;
using HZ.WMS.Application.SAP;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.PP.ViewModel;
using HZ.WMS.Entity.SAP;
using HZ.WMS.Entity.Sys;
using SAP.Middleware.Connector;

namespace HZ.WMS.Application.Sys
{
    /// <summary>
    /// 没有out_message消息的函数，意味着只在最前端做未处理异常捕捉
    /// </summary>

    public class Sys_SAPCompanyInfoApp : BaseApp<Sys_SAPCompanyInfo>
    {
        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public Sys_SAPCompanyInfoApp() : base()
        {
        }




        #endregion

        /// <summary>
        /// sap链接
        /// </summary>
        /// <param name="companycode">公司代码</param>
        /// <returns></returns>
        public RfcConfigParameters GetSAPLink(string companycode)
        {
            //连接 sap
            RfcConfigParameters rfc = new RfcConfigParameters();

            ////测试：
            //rfc.Add(RfcConfigParameters.Name, "FED");//mycon
            //rfc.Add(RfcConfigParameters.AppServerHost, "**********");//IP address
            //rfc.Add(RfcConfigParameters.Client, "200");
            //rfc.Add(RfcConfigParameters.User, "sys-wms");//username
            //rfc.Add(RfcConfigParameters.Password, "123456");//password
            //rfc.Add(RfcConfigParameters.SystemNumber, "10");//00
            Sys_SAPCompanyInfo info = base.GetList(x => x.CompanyCode == companycode).ToList().FirstOrDefault();
            if (info == null)
                return null;

            rfc.Add(RfcConfigParameters.Name, info.SAPName); //mycon
            rfc.Add(RfcConfigParameters.AppServerHost, info.SAPAppServerHost); //IP address
            rfc.Add(RfcConfigParameters.Client, info.SAPClient);
            rfc.Add(RfcConfigParameters.User, info.SAPUser); //username
            rfc.Add(RfcConfigParameters.Password, info.SAPPassword); //password
            rfc.Add(RfcConfigParameters.SystemNumber, info.SAPSystemNumber);
            rfc.Add(RfcConfigParameters.Language, "ZH");
            return rfc;
        }

        /// <summary>
        /// ZFGWMS001销售发货SAP系统接口
        /// </summary>
        /// <param name="CompanyCode"></param>
        /// <param name="lst"></param>
        /// <param name="rtnErrMsg"></param>
        /// <returns></returns>
        public List<SAPRETURN> ZFGWMS001(string CompanyCode, ZFGWMS001 info, DateTime PostTime, List<ZFGWMS001_1> lst,
            out bool isPosted, out string rtnErrMsg)
        {
            isPosted = false;
            RfcDestination rfcdest = RfcDestinationManager.GetDestination(GetSAPLink(CompanyCode)); //初始化SAP连接配置
            //RFC调用函数
            RfcRepository rfcrep = rfcdest.Repository; //连接SAP
            IRfcFunction myfun = null; //初始化RFC函数调用方法
            myfun = rfcrep.CreateFunction("ZFGWMS001"); //RFC函数名
            //组织函数的参数

            IRfcStructure HEAD = myfun.GetStructure("I_HEAD");
            HEAD.SetValue("VBELN", info.VBELN);
            HEAD.SetValue("LFDAT", info.LFDAT);
            HEAD.SetValue("WAUHR", info.WAUHR); //待定

            IRfcTable zh = myfun.GetTable("IT_ITEM");
            foreach (var input in lst)
            {
                zh.Insert();
                zh.CurrentRow.SetValue("LGORT", input.LGORT);
                zh.CurrentRow.SetValue("LFIMG", input.LFIMG);
                zh.CurrentRow.SetValue("VGPOS", input.VGPOS);
                zh.CurrentRow.SetValue("ZJHBS", input.ZJHBS);
                zh.CurrentRow.SetValue("ZREMARK", input.ZEREMARK);
            }


            myfun.Invoke(rfcdest); //调用函数


            List<SAPRETURN> list = new List<SAPRETURN>();
            IRfcStructure ter = myfun.GetStructure("E_RETURN");
            //IRfcTable ter = myfun.GetTable("E_RETURN");

            string DELIV_NUMB = ter.GetValue("DELIV_NUMB").ToString(); //交货
            string ZTYPE = ter.GetValue("ZTYPE").ToString(); //消息类型
            string ZMESSAGE = ter.GetValue("ZMESSAGE").ToString(); //消息文本


            if (ZTYPE == "S")
            {
                SAPRETURN mod = new SAPRETURN();
                //mod.DocNum = XBLNR;
                mod.basenum = info.VBELN;
                //mod.baseline = Convert.ToInt32(EBELP);
                mod.sapDocNum = DELIV_NUMB;
                //mod.sapline = Convert.ToInt32(ZEILE);
                list.Add(mod);
            }
            else
            {
                try
                {
                    string E_FLG = myfun.GetValue("E_FLG").ToString(); //标识

                    //B：创建成功，过账失败
                    if (E_FLG == "B")
                    {
                        //调用交货删除接口
                        ZFGWMS027(CompanyCode, DELIV_NUMB, out isPosted, out rtnErrMsg);

                    }
                }
                catch (Exception)
                {
                }

                isPosted = false;
                rtnErrMsg = ZMESSAGE;
                return null;
            }

            isPosted = true;
            rtnErrMsg = "";
            return list;

        }

        /// <summary>
        /// ZFGWMS027WMS交货单删除接口
        /// </summary>
        /// <param name="CompanyCode"></param>
        /// <param name="lst"></param>
        /// <param name="rtnErrMsg"></param>
        /// <returns></returns>
        public List<SAPRETURN> ZFGWMS027(string CompanyCode, string IV_VBELN, out bool isPosted, out string rtnErrMsg)
        {
            isPosted = false;
            RfcDestination rfcdest = RfcDestinationManager.GetDestination(GetSAPLink(CompanyCode)); //初始化SAP连接配置
            //RFC调用函数
            RfcRepository rfcrep = rfcdest.Repository; //连接SAP
            IRfcFunction myfun = null; //初始化RFC函数调用方法
            myfun = rfcrep.CreateFunction("ZFGWMS027"); //RFC函数名
            //组织函数的参数

            myfun.SetValue("IV_VBELN", IV_VBELN);

            myfun.Invoke(rfcdest); //调用函数


            List<SAPRETURN> list = new List<SAPRETURN>();
            IRfcStructure ter = myfun.GetStructure("E_RETURN");
            //IRfcTable ter = myfun.GetTable("E_RETURN");

            string VBELN = ter.GetValue("VBELN").ToString(); //交货
            string ZTYPE = ter.GetValue("ZTYPE").ToString(); //消息类型
            string ZMESSAGE = ter.GetValue("ZMESSAGE").ToString(); //消息文本

            if (ZTYPE == "S")
            {
                isPosted = true;
                rtnErrMsg = "";
                return list;
            }
            else
            {
                rtnErrMsg = ZMESSAGE;
                return null;
            }

        }

        /// <summary>
        /// ZFGWMS002WMS采购收货同步、委外入库同步 SAP系统接口
        /// </summary>
        /// <param name="CompanyCode">公司代码</param>
        /// <param name="Docnum">领料单号</param>
        /// <param name="BKTXT">凭证抬头文本</param>
        /// <param name="PostTime">过账时间</param>
        /// <param name="lst">主表</param>
        /// <param name="list1">组件</param>
        /// <param name="isPosted"></param>
        /// <param name="rtnErrMsg">错误消息</param>
        /// <returns></returns>
        public List<SAPRETURN> ZFGWMS002(string CompanyCode, string Docnum, string BKTXT, DateTime PostTime,
            List<ZFGWMS002> lst, List<ZFGWMS002_1> list1, out bool isPosted, out string rtnErrMsg)
        {
            isPosted = false;
            RfcDestination rfcdest = RfcDestinationManager.GetDestination(GetSAPLink(CompanyCode)); //初始化SAP连接配置
            //RFC调用函数
            RfcRepository rfcrep = rfcdest.Repository; //连接SAP
            IRfcFunction myfun = null; //初始化RFC函数调用方法
            myfun = rfcrep.CreateFunction("ZFGWMS002"); //RFC函数名
            //组织函数的参数

            IRfcStructure HEAD = myfun.GetStructure("I_HEAD");
            HEAD.SetValue("BLDAT", DateTime.Now.ToString("yyyyMMdd")); //凭证日期
            HEAD.SetValue("BUDAT", PostTime.ToString("yyyyMMdd")); //过账日期
            HEAD.SetValue("XBLNR", Docnum); //领料单号
            HEAD.SetValue("BKTXT", BKTXT); //凭证抬头文本
            //object aa=HEAD.GetValue("BLDAT");

            IRfcTable zh = myfun.GetTable("IT_ITEM");
            foreach (var input in lst)
            {
                //zh.CurrentRow.GetValue("EBELN");
                //zh.GetValue("EBELN");
                zh.Insert();
                zh.CurrentRow.SetValue("ZNUM", input.ZNUM);
                zh.CurrentRow.SetValue("EBELN", input.EBELN);
                zh.CurrentRow.SetValue("EBELP", input.EBELP);
                zh.CurrentRow.SetValue("MATNR", input.MATNR);
                zh.CurrentRow.SetValue("WERKS", input.WERKS);
                zh.CurrentRow.SetValue("BWART", input.BWART);
                zh.CurrentRow.SetValue("MENGE", input.MENGE);
                zh.CurrentRow.SetValue("MEINS", input.MEINS);
                zh.CurrentRow.SetValue("LGORT", input.LGORT);
                zh.CurrentRow.SetValue("SOBKZ", input.SOBKZ);
                zh.CurrentRow.SetValue("KDAUF", input.KDAUF);
                zh.CurrentRow.SetValue("KDPOS", input.KDPOS);
                zh.CurrentRow.SetValue("BWTAR", input.BWTAR);
            }

            //组件
            if (list1.Count > 0)
            {
                IRfcTable zh1 = myfun.GetTable("IT_ITEM1");
                foreach (var input in list1)
                {
                    zh1.Insert();
                    zh1.CurrentRow.SetValue("EBELN", input.EBELN);
                    zh1.CurrentRow.SetValue("EBELP", input.EBELP);
                    zh1.CurrentRow.SetValue("RSNUM", input.RSNUM);
                    zh1.CurrentRow.SetValue("RSPOS", input.RSPOS);
                    zh1.CurrentRow.SetValue("MATNR", input.MATNR);
                    zh1.CurrentRow.SetValue("WERKS", input.WERKS);
                    //zh1.CurrentRow.SetValue("BWART", input.BWART);
                    zh1.CurrentRow.SetValue("BDMNG", input.BDMNG);
                    zh1.CurrentRow.SetValue("MEINS", input.MEINS);
                    zh1.CurrentRow.SetValue("LIFNR", input.LIFNR);
                    zh1.CurrentRow.SetValue("BWTAR", input.BWTAR);
                    zh1.CurrentRow.SetValue("SGTXT", input.SGTXT);
                    zh1.CurrentRow.SetValue("LGORT", input.LGORT);

                }
            }

            myfun.Invoke(rfcdest); //调用函数


            List<SAPRETURN> list = new List<SAPRETURN>();
            IRfcTable ter = myfun.GetTable("ET_RETURN");
            foreach (var info in ter)
            {
                string XBLNR = info.GetValue("XBLNR").ToString(); //领料单号
                string MBLNR = info.GetValue("MBLNR").ToString(); //物料凭证编号 SAP生成单号
                string ZEILE = info.GetValue("ZEILE").ToString(); //物料凭证中的项目 SAP生成行号
                string EBELN = info.GetValue("EBELN").ToString(); //采购凭证编号
                string EBELP = info.GetValue("EBELP").ToString(); //采购凭证的项目编号
                string ZTYPE = info.GetValue("ZTYPE").ToString(); //消息类型
                string ZMESSAGE = info.GetValue("ZMESSAGE").ToString(); //消息文本

                if (ZTYPE == "S")
                {
                    SAPRETURN mod = new SAPRETURN();
                    mod.DocNum = XBLNR;
                    mod.basenum = EBELN;
                    mod.baseline = Convert.ToInt32(EBELP);
                    mod.sapDocNum = MBLNR;
                    mod.sapline = Convert.ToInt32(ZEILE);
                    list.Add(mod);
                }
                else
                {

                    rtnErrMsg = ZMESSAGE;
                    isPosted = false;
                    return null;
                }
            }

            isPosted = true;
            rtnErrMsg = "";
            return list;

        }

        /// <summary>
        /// ZFGWMS003配送出库同步SAP系统接口
        /// </summary>
        /// <param name="CompanyCode"></param>
        /// <param name="lst"></param>
        /// <param name="rtnErrMsg"></param>
        /// <returns></returns>
        public List<SAPRETURN> ZFGWMS003(string CompanyCode, string donum, string BKTXT, DateTime PostTime,
            List<ZFGWMS003> lst, out bool isPosted, out string rtnErrMsg)
        {

            isPosted = false;

            RfcDestination rfcdest = RfcDestinationManager.GetDestination(GetSAPLink(CompanyCode)); //初始化SAP连接配置
            //RFC调用函数
            RfcRepository rfcrep = rfcdest.Repository; //连接SAP
            IRfcFunction myfun = null; //初始化RFC函数调用方法
            myfun = rfcrep.CreateFunction("ZFGWMS003"); //RFC函数名
            //组织函数的参数

            IRfcStructure HEAD = myfun.GetStructure("I_HEAD");
            HEAD.SetValue("BLDAT", DateTime.Now.ToString("yyyyMMdd")); //凭证日期
            HEAD.SetValue("BUDAT", PostTime.ToString("yyyyMMdd")); //过账日期
            HEAD.SetValue("XBLNR", donum); //领料单号
            HEAD.SetValue("BKTXT", BKTXT); //领料单号


            IRfcTable zh = myfun.GetTable("IT_ITEM");
            foreach (var input in lst)
            {
                zh.Insert();
                zh.CurrentRow.SetValue("ZNUM", input.ZNUM);
                zh.CurrentRow.SetValue("MATNR", input.MATNR);
                zh.CurrentRow.SetValue("WERKS", input.WERKS);
                zh.CurrentRow.SetValue("BWART", input.BWART);
                zh.CurrentRow.SetValue("MENGE", input.MENGE);
                zh.CurrentRow.SetValue("MEINS", input.MEINS);
                zh.CurrentRow.SetValue("LGORT", input.LGORT);
                zh.CurrentRow.SetValue("UMLGO", input.UMLGO);
                zh.CurrentRow.SetValue("SOBKZ", input.SOBKZ);
                zh.CurrentRow.SetValue("KDAUF", input.KDAUF);
                zh.CurrentRow.SetValue("KDPOS", input.KDPOS);
                zh.CurrentRow.SetValue("BWTAR", input.BWTAR);
            }

            myfun.Invoke(rfcdest); //调用函数

            List<SAPRETURN> list = new List<SAPRETURN>();
            IRfcTable ter = myfun.GetTable("ET_RETURN");
            foreach (var info in ter)
            {
                string XBLNR = info.GetValue("XBLNR").ToString(); //wms单号
                string MBLNR = info.GetValue("MBLNR").ToString(); //物料凭证编号 SAP生成单号
                string ZEILE = info.GetValue("ZEILE").ToString(); //物料凭证中的项目 SAP生成行号
                string ZNUM = info.GetValue("ZNUM").ToString(); //行号
                string ZTYPE = info.GetValue("ZTYPE").ToString(); //消息类型
                string ZMESSAGE = info.GetValue("ZMESSAGE").ToString(); //消息文本

                if (ZTYPE == "S")
                {
                    SAPRETURN mod = new SAPRETURN();
                    mod.DocNum = XBLNR;
                    mod.line = Convert.ToInt32(ZNUM);
                    mod.sapDocNum = MBLNR;
                    mod.sapline = Convert.ToInt32(ZEILE);
                    list.Add(mod);
                }
                else
                {
                    rtnErrMsg = ZMESSAGE;
                    return null;
                }
            }

            isPosted = true;
            rtnErrMsg = "";
            return list;
        }

        /// <summary>
        /// ZFGWMS004超额领料同步SAP系统接口
        /// </summary>
        /// <param name="CompanyCode"></param>
        /// <param name="lst"></param>
        /// <param name="rtnErrMsg"></param>
        /// <returns></returns>
        public List<SAPRETURN> ZFGWMS004(string CompanyCode, string donum, string TYPE, string BKTXT, DateTime PostTime,
            List<ZFGWMS004> lst, out bool isPosted, out string rtnErrMsg)
        {

            isPosted = false;

            RfcDestination rfcdest = RfcDestinationManager.GetDestination(GetSAPLink(CompanyCode)); //初始化SAP连接配置
            //RFC调用函数
            RfcRepository rfcrep = rfcdest.Repository; //连接SAP
            IRfcFunction myfun = null; //初始化RFC函数调用方法
            myfun = rfcrep.CreateFunction("ZFGWMS004"); //RFC函数名
            //组织函数的参数

            IRfcStructure HEAD = myfun.GetStructure("I_HEAD");
            HEAD.SetValue("BLDAT", DateTime.Now.ToString("yyyyMMdd")); //凭证日期
            HEAD.SetValue("BUDAT", PostTime.ToString("yyyyMMdd")); //过账日期
            HEAD.SetValue("TYPE", TYPE); //1、工费  2、料废
            HEAD.SetValue("XBLNR", donum); //领料单号
            HEAD.SetValue("BKTXT", BKTXT); //领料单号

            IRfcTable zh = myfun.GetTable("IT_ITEM");
            foreach (var input in lst)
            {
                zh.Insert();
                zh.CurrentRow.SetValue("ZNUM", input.ZNUM);
                zh.CurrentRow.SetValue("AUFNR", input.AUFNR);
                zh.CurrentRow.SetValue("MATNR", input.MATNR);
                zh.CurrentRow.SetValue("WERKS", input.WERKS);
                zh.CurrentRow.SetValue("BWART", input.BWART);
                zh.CurrentRow.SetValue("MENGE", input.MENGE);
                zh.CurrentRow.SetValue("MEINS", input.MEINS);
                zh.CurrentRow.SetValue("LGORT", input.LGORT);
                zh.CurrentRow.SetValue("UMLGO", input.UMLGO);
                zh.CurrentRow.SetValue("SOBKZ", input.SOBKZ);
                zh.CurrentRow.SetValue("KDAUF", input.KDAUF);
                zh.CurrentRow.SetValue("KDPOS", input.KDPOS);
                zh.CurrentRow.SetValue("BWTAR", input.BWTAR);
                zh.CurrentRow.SetValue("SGTXT", input.SGTXT);
            }

            myfun.Invoke(rfcdest); //调用函数

            List<SAPRETURN> list = new List<SAPRETURN>();
            IRfcTable ter = myfun.GetTable("ET_RETURN");
            foreach (var info in ter)
            {
                string XBLNR = info.GetValue("XBLNR").ToString(); //wms单号
                string MBLNR = info.GetValue("MBLNR").ToString(); //物料凭证编号 SAP生成单号
                string ZEILE = info.GetValue("ZEILE").ToString(); //物料凭证中的项目 SAP生成行号
                string ZNUM = info.GetValue("ZNUM").ToString(); //行号
                string ZTYPE = info.GetValue("ZTYPE").ToString(); //消息类型
                string ZMESSAGE = info.GetValue("ZMESSAGE").ToString(); //消息文本

                if (ZTYPE == "S")
                {
                    SAPRETURN mod = new SAPRETURN();
                    mod.DocNum = XBLNR;
                    mod.line = Convert.ToInt32(ZNUM);
                    mod.sapDocNum = MBLNR;
                    mod.sapline = Convert.ToInt32(ZEILE);
                    list.Add(mod);
                }
                else
                {
                    rtnErrMsg = ZMESSAGE;
                    return null;
                }
            }

            isPosted = true;
            rtnErrMsg = "";
            return list;
        }

        /// <summary>
        /// ZFGWMS005车间退料同步SAP系统接口
        /// </summary>
        /// <param name="CompanyCode"></param>
        /// <param name="lst"></param>
        /// <param name="rtnErrMsg"></param>
        /// <returns></returns>
        public List<SAPRETURN> ZFGWMS005(string CompanyCode, string donum, string BKTXT, DateTime PostTime,
            List<ZFGWMS005> lst, out bool isPosted, out string rtnErrMsg)
        {

            isPosted = false;

            RfcDestination rfcdest = RfcDestinationManager.GetDestination(GetSAPLink(CompanyCode)); //初始化SAP连接配置
            //RFC调用函数
            RfcRepository rfcrep = rfcdest.Repository; //连接SAP
            IRfcFunction myfun = null; //初始化RFC函数调用方法
            myfun = rfcrep.CreateFunction("ZFGWMS005"); //RFC函数名
            //组织函数的参数

            IRfcStructure HEAD = myfun.GetStructure("I_HEAD");
            HEAD.SetValue("BLDAT", DateTime.Now.ToString("yyyyMMdd")); //凭证日期
            HEAD.SetValue("BUDAT", PostTime.ToString("yyyyMMdd")); //过账日期
            HEAD.SetValue("XBLNR", donum); //领料单号
            HEAD.SetValue("BKTXT", BKTXT); //领料单号


            IRfcTable zh = myfun.GetTable("IT_ITEM");
            foreach (var input in lst)
            {
                zh.Insert();
                zh.CurrentRow.SetValue("ZNUM", input.ZNUM);
                zh.CurrentRow.SetValue("MATNR", input.MATNR);
                zh.CurrentRow.SetValue("WERKS", input.WERKS);
                zh.CurrentRow.SetValue("BWART", input.BWART);
                zh.CurrentRow.SetValue("MENGE", input.MENGE);
                zh.CurrentRow.SetValue("MEINS", input.MEINS);
                zh.CurrentRow.SetValue("LGORT", input.LGORT);
                zh.CurrentRow.SetValue("UMLGO", input.UMLGO);
                zh.CurrentRow.SetValue("SOBKZ", input.SOBKZ);
                zh.CurrentRow.SetValue("KDAUF", input.KDAUF);
                zh.CurrentRow.SetValue("KDPOS", input.KDPOS);
                zh.CurrentRow.SetValue("BWTAR", input.BWTAR);
                zh.CurrentRow.SetValue("SGTXT", input.SGTXT);
            }

            myfun.Invoke(rfcdest); //调用函数

            List<SAPRETURN> list = new List<SAPRETURN>();
            IRfcTable ter = myfun.GetTable("ET_RETURN");
            foreach (var info in ter)
            {
                string XBLNR = info.GetValue("XBLNR").ToString(); //wms单号
                string MBLNR = info.GetValue("MBLNR").ToString(); //物料凭证编号 SAP生成单号
                string ZEILE = info.GetValue("ZEILE").ToString(); //物料凭证中的项目 SAP生成行号
                string ZNUM = info.GetValue("ZNUM").ToString(); //行号
                string ZTYPE = info.GetValue("ZTYPE").ToString(); //消息类型
                string ZMESSAGE = info.GetValue("ZMESSAGE").ToString(); //消息文本

                if (ZTYPE == "S")
                {
                    SAPRETURN mod = new SAPRETURN();
                    mod.DocNum = XBLNR;
                    mod.line = Convert.ToInt32(ZNUM);
                    mod.sapDocNum = MBLNR;
                    mod.sapline = Convert.ToInt32(ZEILE);
                    list.Add(mod);
                }
                else
                {
                    rtnErrMsg = ZMESSAGE;
                    return null;
                }
            }

            isPosted = true;
            rtnErrMsg = "";
            return list;
        }

        /// <summary>
        /// ZFGWMS006供应商退货单同步SAP系统接口
        /// </summary>
        /// <param name="CompanyCode"></param>
        /// <param name="DocNum">单号</param>
        /// <param name="PostTime">过账时间</param>
        /// <param name="isPosted">是否过账</param>
        /// <param name="lst">集合</param>
        /// <param name="rtnErrMsg">错误消息</param>
        /// <returns></returns>
        public List<SAPRETURN> ZFGWMS006(string CompanyCode, string DocNum, DateTime PostTime, List<ZFGWMS006> lst,
            out bool isPosted, out string rtnErrMsg)
        {
            LogHelper.Instance.LogDebug("开始：ZFGWMS006供应商退货单同步SAP系统接口：" + DateTime.Now.ToString());
            isPosted = false;

            RfcDestination rfcdest = RfcDestinationManager.GetDestination(GetSAPLink(CompanyCode)); //初始化SAP连接配置
            //RFC调用函数
            RfcRepository rfcrep = rfcdest.Repository; //连接SAP
            IRfcFunction myfun = null; //初始化RFC函数调用方法
            myfun = rfcrep.CreateFunction("ZFGWMS006"); //RFC函数名
            //组织函数的参数

            IRfcStructure HEAD = myfun.GetStructure("I_HEAD");
            HEAD.SetValue("BLDAT", DateTime.Now.ToString("yyyyMMdd")); //凭证日期
            HEAD.SetValue("BUDAT", PostTime.ToString("yyyyMMdd")); //过账日期
            HEAD.SetValue("XBLNR", DocNum); //退货单号



            IRfcTable zh = myfun.GetTable("IT_ITEM");
            foreach (var input in lst)
            {
                zh.Insert();
                zh.CurrentRow.SetValue("EBELN", input.EBELN);
                zh.CurrentRow.SetValue("EBELP", input.EBELP);
                zh.CurrentRow.SetValue("MATNR", input.MATNR);
                zh.CurrentRow.SetValue("WERKS", input.WERKS);
                zh.CurrentRow.SetValue("BWART", input.BWART);
                zh.CurrentRow.SetValue("MENGE", input.MENGE);
                zh.CurrentRow.SetValue("MEINS", input.MEINS);
                zh.CurrentRow.SetValue("LGORT", input.LGORT);
                zh.CurrentRow.SetValue("SOBKZ", input.SOBKZ);
                zh.CurrentRow.SetValue("KDAUF", input.KDAUF);
                zh.CurrentRow.SetValue("KDPOS", input.KDPOS);
                zh.CurrentRow.SetValue("BWTAR", input.BWTAR);
                zh.CurrentRow.SetValue("SGTXT", input.SGTXT);
            }

            myfun.Invoke(rfcdest); //调用函数

            List<SAPRETURN> list = new List<SAPRETURN>();
            IRfcTable ter = myfun.GetTable("ET_RETURN");
            foreach (var info in ter)
            {
                string XBLNR = info.GetValue("XBLNR").ToString(); //wms单号
                string MBLNR = info.GetValue("MBLNR").ToString(); //物料凭证编号 SAP生成单号
                string ZEILE = info.GetValue("ZEILE").ToString(); //物料凭证中的项目 SAP生成行号
                string EBELN = info.GetValue("EBELN").ToString(); //退货采购订单号
                string EBELP = info.GetValue("EBELP").ToString(); //采购凭证的项目编号
                string ZTYPE = info.GetValue("ZTYPE").ToString(); //消息类型
                string ZMESSAGE = info.GetValue("ZMESSAGE").ToString(); //消息文本

                if (ZTYPE == "S")
                {
                    SAPRETURN mod = new SAPRETURN();
                    mod.DocNum = XBLNR;
                    mod.basenum = EBELN;
                    mod.baseline = Convert.ToInt32(EBELP);
                    mod.sapDocNum = MBLNR;
                    mod.sapline = Convert.ToInt32(ZEILE);
                    list.Add(mod);
                }
                else
                {
                    rtnErrMsg = ZMESSAGE;
                    //LogHelper.Instance.LogDebug("ZFGWMS006供应商退货单同步SAP系统接口：" + DateTime.Now.ToString(),"");
                    return null;
                }
            }

            isPosted = true;
            rtnErrMsg = "";
            return list;
        }

        /// <summary>
        /// ZFGWMS008领料出库信息同步SAP系统接口
        /// </summary>
        /// <param name="CompanyCode"></param>
        /// <param name="lst"></param>
        /// <param name="rtnErrMsg"></param>
        /// <returns></returns>
        public List<SAPRETURN> ZFGWMS008(string CompanyCode, string donum, string BKTXT, DateTime PostTime,
            List<ZFGWMS008> lst, out bool isPosted, out string rtnErrMsg)
        {

            isPosted = false;
            try
            {
                RfcDestination rfcdest = RfcDestinationManager.GetDestination(GetSAPLink(CompanyCode)); //初始化SAP连接配置
                //RFC调用函数
                RfcRepository rfcrep = rfcdest.Repository; //连接SAP
                IRfcFunction myfun = null; //初始化RFC函数调用方法
                myfun = rfcrep.CreateFunction("ZFGWMS008"); //RFC函数名
                //组织函数的参数

                IRfcStructure HEAD = myfun.GetStructure("I_HEAD");
                HEAD.SetValue("BLDAT", DateTime.Now.ToString("yyyyMMdd")); //凭证日期
                HEAD.SetValue("BUDAT", PostTime.ToString("yyyyMMdd")); //过账日期
                HEAD.SetValue("XBLNR", donum); //领料单号
                HEAD.SetValue("BKTXT", BKTXT); //领料单号


                IRfcTable zh = myfun.GetTable("IT_ITEM");
                foreach (var input in lst)
                {
                    zh.Insert();
                    zh.CurrentRow.SetValue("ZNUM", input.ZNUM);
                    zh.CurrentRow.SetValue("MATNR", input.MATNR);
                    zh.CurrentRow.SetValue("WERKS", input.WERKS);
                    zh.CurrentRow.SetValue("BWART", input.BWART);
                    zh.CurrentRow.SetValue("MENGE", input.MENGE);
                    zh.CurrentRow.SetValue("MEINS", input.MEINS);
                    zh.CurrentRow.SetValue("LGORT", input.LGORT);
                    zh.CurrentRow.SetValue("KOSTL", input.KOSTL);
                    zh.CurrentRow.SetValue("SAKTO", input.SAKTO);
                    zh.CurrentRow.SetValue("AUFNR", input.AUFNR);
                    zh.CurrentRow.SetValue("ANLN1", input.ANLN1);

                    zh.CurrentRow.SetValue("SOBKZ", input.SOBKZ);
                    zh.CurrentRow.SetValue("KDAUF", input.KDAUF);
                    zh.CurrentRow.SetValue("KDPOS", input.KDPOS);
                    zh.CurrentRow.SetValue("BWTAR", input.BWTAR);
                    zh.CurrentRow.SetValue("SGTXT", input.SGTXT);
                }

                myfun.Invoke(rfcdest); //调用函数

                List<SAPRETURN> list = new List<SAPRETURN>();
                IRfcTable ter = myfun.GetTable("ET_RETURN");
                foreach (var info in ter)
                {
                    string XBLNR = info.GetValue("XBLNR").ToString(); //wms单号
                    string MBLNR = info.GetValue("MBLNR").ToString(); //物料凭证编号 SAP生成单号
                    string ZEILE = info.GetValue("ZEILE").ToString(); //物料凭证中的项目 SAP生成行号
                    string ZNUM = info.GetValue("ZNUM").ToString(); //行号
                    string ZTYPE = info.GetValue("ZTYPE").ToString(); //消息类型
                    string ZMESSAGE = info.GetValue("ZMESSAGE").ToString(); //消息文本

                    if (ZTYPE == "S")
                    {
                        SAPRETURN mod = new SAPRETURN();
                        mod.DocNum = XBLNR;
                        mod.line = Convert.ToInt32(ZNUM);
                        mod.sapDocNum = MBLNR;
                        mod.sapline = Convert.ToInt32(ZEILE);
                        list.Add(mod);
                    }
                    else
                    {
                        rtnErrMsg = ZMESSAGE;
                        return null;
                    }
                }

                isPosted = true;
                rtnErrMsg = "";
                return list;
            }
            catch (Exception ex)
            {
                rtnErrMsg = ex.Message;
                return null;
            }
        }

        /// <summary>
        /// ZFGWMS009报废出库同步SAP系统接口
        /// </summary>
        /// <param name="CompanyCode"></param>
        /// <param name="lst"></param>
        /// <param name="rtnErrMsg"></param>
        /// <returns></returns>
        public List<SAPRETURN> ZFGWMS009(string CompanyCode, string donum, string BKTXT, DateTime PostTime,
            List<ZFGWMS009> lst, out bool isPosted, out string rtnErrMsg)
        {

            isPosted = false;

            RfcDestination rfcdest = RfcDestinationManager.GetDestination(GetSAPLink(CompanyCode)); //初始化SAP连接配置
            //RFC调用函数
            RfcRepository rfcrep = rfcdest.Repository; //连接SAP
            IRfcFunction myfun = null; //初始化RFC函数调用方法
            myfun = rfcrep.CreateFunction("ZFGWMS009"); //RFC函数名
            //组织函数的参数

            IRfcStructure HEAD = myfun.GetStructure("I_HEAD");
            HEAD.SetValue("BLDAT", DateTime.Now.ToString("yyyyMMdd")); //凭证日期
            HEAD.SetValue("BUDAT", PostTime.ToString("yyyyMMdd")); //过账日期
            HEAD.SetValue("XBLNR", donum); //领料单号
            HEAD.SetValue("BKTXT", BKTXT); //领料单号


            IRfcTable zh = myfun.GetTable("IT_ITEM");
            foreach (var input in lst)
            {
                zh.Insert();
                zh.CurrentRow.SetValue("ZNUM", input.ZNUM);
                zh.CurrentRow.SetValue("MATNR", input.MATNR);
                zh.CurrentRow.SetValue("WERKS", input.WERKS);
                zh.CurrentRow.SetValue("BWART", input.BWART);
                zh.CurrentRow.SetValue("MENGE", input.MENGE);
                zh.CurrentRow.SetValue("MEINS", input.MEINS);
                zh.CurrentRow.SetValue("LGORT", input.LGORT);
                zh.CurrentRow.SetValue("KOSTL", input.KOSTL);


                zh.CurrentRow.SetValue("SOBKZ", input.SOBKZ);
                zh.CurrentRow.SetValue("KDAUF", input.KDAUF);
                zh.CurrentRow.SetValue("KDPOS", input.KDPOS);
                zh.CurrentRow.SetValue("LIFNR", input.LIFNR);
                zh.CurrentRow.SetValue("BWTAR", input.BWTAR);
                zh.CurrentRow.SetValue("SGTXT", input.SGTXT);
            }

            myfun.Invoke(rfcdest); //调用函数

            List<SAPRETURN> list = new List<SAPRETURN>();
            IRfcTable ter = myfun.GetTable("ET_RETURN");
            foreach (var info in ter)
            {
                string XBLNR = info.GetValue("XBLNR").ToString(); //wms单号
                string MBLNR = info.GetValue("MBLNR").ToString(); //物料凭证编号 SAP生成单号
                string ZEILE = info.GetValue("ZEILE").ToString(); //物料凭证中的项目 SAP生成行号
                string ZNUM = info.GetValue("ZNUM").ToString(); //行号
                string ZTYPE = info.GetValue("ZTYPE").ToString(); //消息类型
                string ZMESSAGE = info.GetValue("ZMESSAGE").ToString(); //消息文本

                if (ZTYPE == "S")
                {
                    SAPRETURN mod = new SAPRETURN();
                    mod.DocNum = XBLNR;
                    mod.line = Convert.ToInt32(ZNUM);
                    mod.sapDocNum = MBLNR;
                    mod.sapline = Convert.ToInt32(ZEILE);
                    list.Add(mod);
                }
                else
                {
                    rtnErrMsg = ZMESSAGE;
                    return null;
                }
            }

            isPosted = true;
            rtnErrMsg = "";
            return list;
        }

        /// <summary>
        /// ZFGWMS010其他入库同步SAP系统接口
        /// </summary>
        /// <param name="CompanyCode"></param>
        /// <param name="lst"></param>
        /// <param name="rtnErrMsg"></param>
        /// <returns></returns>
        public List<SAPRETURN> ZFGWMS010(string CompanyCode, string docnum, string BKTXT, DateTime PostTime,
            List<ZFGWMS010> lst, out bool isPosted, out string rtnErrMsg)
        {
            isPosted = false;

            RfcDestination rfcdest = RfcDestinationManager.GetDestination(GetSAPLink(CompanyCode)); //初始化SAP连接配置
            //RFC调用函数
            RfcRepository rfcrep = rfcdest.Repository; //连接SAP
            IRfcFunction myfun = null; //初始化RFC函数调用方法
            myfun = rfcrep.CreateFunction("ZFGWMS010"); //RFC函数名
            //组织函数的参数

            IRfcStructure HEAD = myfun.GetStructure("I_HEAD");
            HEAD.SetValue("BLDAT", DateTime.Now.ToString("yyyyMMdd")); //凭证日期
            HEAD.SetValue("BUDAT", PostTime.ToString("yyyyMMdd")); //过账日期
            HEAD.SetValue("XBLNR", docnum); //领料单号
            HEAD.SetValue("BKTXT", BKTXT); //凭证抬头文本


            IRfcTable zh = myfun.GetTable("IT_ITEM");
            foreach (var input in lst)
            {
                zh.Insert();
                zh.CurrentRow.SetValue("ZNUM", input.ZNUM);
                zh.CurrentRow.SetValue("MATNR", input.MATNR);
                zh.CurrentRow.SetValue("WERKS", input.WERKS);
                zh.CurrentRow.SetValue("BWART", input.BWART);
                zh.CurrentRow.SetValue("MENGE", input.MENGE);
                zh.CurrentRow.SetValue("MEINS", input.MEINS);
                zh.CurrentRow.SetValue("LGORT", input.LGORT);
                zh.CurrentRow.SetValue("KOSTL", input.KOSTL);
                zh.CurrentRow.SetValue("SAKTO", input.SAKTO);
                zh.CurrentRow.SetValue("SOBKZ", input.SOBKZ);
                zh.CurrentRow.SetValue("KDAUF", input.KDAUF);
                zh.CurrentRow.SetValue("KDPOS", input.KDPOS);
                zh.CurrentRow.SetValue("BWTAR", input.BWTAR);
                zh.CurrentRow.SetValue("AUFNR", input.AUFNR);
                zh.CurrentRow.SetValue("SGTXT", input.SGTXT);

            }

            myfun.Invoke(rfcdest); //调用函数

            List<SAPRETURN> list = new List<SAPRETURN>();
            IRfcTable ter = myfun.GetTable("ET_RETURN");
            foreach (var info in ter)
            {
                string XBLNR = info.GetValue("XBLNR").ToString(); //领料单号
                string MBLNR = info.GetValue("MBLNR").ToString(); //物料凭证编号
                string ZEILE = info.GetValue("ZEILE").ToString(); //物料凭证中的项目
                string ZNUM = info.GetValue("ZNUM").ToString(); //配送单行号
                string ZTYPE = info.GetValue("ZTYPE").ToString(); //消息类型
                string ZMESSAGE = info.GetValue("ZMESSAGE").ToString(); //消息文本


                if (ZTYPE == "S")
                {
                    SAPRETURN mod = new SAPRETURN();
                    mod.DocNum = XBLNR;
                    mod.line = Convert.ToInt32(ZNUM);
                    mod.sapDocNum = MBLNR;
                    mod.sapline = Convert.ToInt32(ZEILE);
                    list.Add(mod);
                }
                else
                {
                    rtnErrMsg = ZMESSAGE;
                    return null;
                }
            }

            isPosted = true;
            rtnErrMsg = "";
            return list;
        }

        /// <summary>
        /// ZFGWMS011借出单同步SAP系统接口
        /// </summary>
        /// <param name="CompanyCode">公司代码</param>
        /// <param name="DocNum">单号</param>
        /// <param name="BKTXT">凭证抬头文本</param>
        /// <param name="PostTime">过账日期</param>
        /// <param name="lst"></param>
        /// <param name="isPosted">是否过账</param>
        /// <param name="rtnErrMsg">错误消息</param>
        /// <returns></returns>
        public List<SAPRETURN> ZFGWMS011(string CompanyCode, string DocNum, string BKTXT, DateTime PostTime,
            List<ZFGWMS011> lst, out bool isPosted, out string rtnErrMsg)
        {
            isPosted = false;

            RfcDestination rfcdest = RfcDestinationManager.GetDestination(GetSAPLink(CompanyCode)); //初始化SAP连接配置
            //RFC调用函数
            RfcRepository rfcrep = rfcdest.Repository; //连接SAP
            IRfcFunction myfun = null; //初始化RFC函数调用方法
            myfun = rfcrep.CreateFunction("ZFGWMS011"); //RFC函数名
            //组织函数的参数

            IRfcStructure HEAD = myfun.GetStructure("I_HEAD");
            HEAD.SetValue("BLDAT", DateTime.Now.ToString("yyyyMMdd")); //凭证日期
            HEAD.SetValue("BUDAT", PostTime.ToString("yyyyMMdd")); //过账日期
            HEAD.SetValue("XBLNR", DocNum); //领料单号
            HEAD.SetValue("BKTXT", BKTXT); //凭证抬头文本

            IRfcTable zh = myfun.GetTable("IT_ITEM");
            foreach (var input in lst)
            {
                zh.Insert();
                zh.CurrentRow.SetValue("ZNUM", input.ZNUM);
                zh.CurrentRow.SetValue("MATNR", input.MATNR);
                zh.CurrentRow.SetValue("WERKS", input.WERKS);
                zh.CurrentRow.SetValue("BWART", input.BWART);
                zh.CurrentRow.SetValue("MENGE", input.MENGE);
                zh.CurrentRow.SetValue("MEINS", input.MEINS);
                zh.CurrentRow.SetValue("LGORT", input.LGORT);
                zh.CurrentRow.SetValue("KOSTL", input.KOSTL);
                zh.CurrentRow.SetValue("SAKTO", input.SAKTO);
                zh.CurrentRow.SetValue("SGTXT", input.SGTXT);
                zh.CurrentRow.SetValue("BWTAR", input.BWTAR);
            }

            myfun.Invoke(rfcdest); //调用函数

            List<SAPRETURN> list = new List<SAPRETURN>();
            IRfcTable ter = myfun.GetTable("ET_RETURN");
            foreach (var info in ter)
            {
                string XBLNR = info.GetValue("XBLNR").ToString(); //领料单号
                string MBLNR = info.GetValue("MBLNR").ToString(); //物料凭证编号
                string ZEILE = info.GetValue("ZEILE").ToString(); //物料凭证中的项目
                string ZNUM = info.GetValue("ZNUM").ToString(); //配送单行号
                string ZTYPE = info.GetValue("ZTYPE").ToString(); //消息类型
                string ZMESSAGE = info.GetValue("ZMESSAGE").ToString(); //消息文本

                if (ZTYPE == "S")
                {
                    SAPRETURN mod = new SAPRETURN();
                    mod.DocNum = XBLNR;
                    mod.line = Convert.ToInt32(ZNUM);
                    mod.sapDocNum = MBLNR;
                    mod.sapline = Convert.ToInt32(ZEILE);
                    list.Add(mod);
                }
                else
                {
                    rtnErrMsg = ZMESSAGE;
                    return null;
                }
            }

            isPosted = true;
            rtnErrMsg = "";
            return list;
        }

        /// <summary>
        /// ZFGWMS011借出单同步SAP系统接口-销售库存
        /// </summary>
        /// <param name="CompanyCode">公司代码</param>
        /// <param name="DocNum">单号</param>
        /// <param name="BKTXT">凭证抬头文本</param>
        /// <param name="PostTime">过账日期</param>
        /// <param name="lst"></param>
        /// <param name="isPosted">是否过账</param>
        /// <param name="rtnErrMsg">错误消息</param>
        /// <returns></returns>
        public List<SAPRETURN> ZFGWMS026(string CompanyCode, string DocNum, string BKTXT, DateTime PostTime,
            List<ZFGWMS011> lst, out bool isPosted, out string rtnErrMsg)
        {
            isPosted = false;

            RfcDestination rfcdest = RfcDestinationManager.GetDestination(GetSAPLink(CompanyCode)); //初始化SAP连接配置
            //RFC调用函数
            RfcRepository rfcrep = rfcdest.Repository; //连接SAP
            IRfcFunction myfun = null; //初始化RFC函数调用方法
            myfun = rfcrep.CreateFunction("ZFGWMS026"); //RFC函数名
            //组织函数的参数

            IRfcStructure HEAD = myfun.GetStructure("I_HEAD");
            HEAD.SetValue("BLDAT", DateTime.Now.ToString("yyyyMMdd")); //凭证日期
            HEAD.SetValue("BUDAT", PostTime.ToString("yyyyMMdd")); //过账日期
            HEAD.SetValue("XBLNR", DocNum); //领料单号
            HEAD.SetValue("BKTXT", BKTXT); //凭证抬头文本

            IRfcTable zh = myfun.GetTable("IT_ITEM");
            foreach (var input in lst)
            {
                zh.Insert();
                //zh.CurrentRow.SetValue("ZNUM", input.ZNUM);
                zh.CurrentRow.SetValue("MATNR", input.MATNR);
                zh.CurrentRow.SetValue("WERKS", input.WERKS);
                zh.CurrentRow.SetValue("BWART", input.BWART);
                zh.CurrentRow.SetValue("MENGE", input.MENGE);
                zh.CurrentRow.SetValue("MEINS", input.MEINS);
                zh.CurrentRow.SetValue("LGORT", input.LGORT);
                zh.CurrentRow.SetValue("KOSTL", input.KOSTL);
                zh.CurrentRow.SetValue("SAKTO", input.SAKTO);
                zh.CurrentRow.SetValue("SGTXT", input.SGTXT);
                //zh.CurrentRow.SetValue("BWTAR", input.BWTAR);
                zh.CurrentRow.SetValue("KDAUF", input.KDAUF);
                zh.CurrentRow.SetValue("KDPOS", input.KDPOS);
            }

            myfun.Invoke(rfcdest); //调用函数

            List<SAPRETURN> list = new List<SAPRETURN>();
            IRfcTable ter = myfun.GetTable("ET_RETURN");
            foreach (var info in ter)
            {
                string MBLNR = info.GetValue("MBLNR").ToString(); //物料凭证编号
                string ZEILE = info.GetValue("ZEILE").ToString(); //物料凭证中的项目

                string ZTYPE = info.GetValue("ZTYPE").ToString(); //消息类型
                string ZMESSAGE = info.GetValue("ZMESSAGE").ToString(); //消息文本

                //string XBLNR = info.GetValue("XBLNR").ToString();//领料单号
                //string ZNUM = info.GetValue("ZNUM").ToString();//配送单行号

                string XBLNR = DocNum;

                if (ZTYPE == "S")
                {

                    SAPRETURN mod = new SAPRETURN();
                    mod.DocNum = XBLNR;
                    //mod.line = Convert.ToInt32(ZNUM);
                    mod.sapDocNum = MBLNR;
                    mod.sapline = Convert.ToInt32(ZEILE);
                    list.Add(mod);

                    foreach (var input in lst)
                    {
                        //关闭销售订单
                        ZFGOMS003(CompanyCode, input.KDAUF, input.KDPOS, out isPosted, out rtnErrMsg);
                    }
                }
                else
                {
                    rtnErrMsg = ZMESSAGE;
                    return null;
                }
            }

            isPosted = true;
            rtnErrMsg = "";
            return list;
        }

        /// <summary>
        /// 取消销售订单
        /// </summary>
        /// <param name="companyCode"></param>
        /// <param name="I_ITEM"></param>
        /// <param name="isPosted"></param>
        /// <param name="rstMessage"></param>
        /// <returns></returns>
        public bool ZFGOMS003(string CompanyCode, string VBELN, int POSNR, out bool isPosted, out string rstMessage)
        {

            try
            {
                isPosted = false;
                rstMessage = string.Empty;
                RfcDestination rfcdest = RfcDestinationManager.GetDestination(GetSAPLink(CompanyCode)); //初始化SAP连接配置
                //RFC调用函数
                RfcRepository rfcrep = rfcdest.Repository; //连接SAP
                IRfcFunction myfun = null; //初始化RFC函数调用方法
                myfun = rfcrep.CreateFunction("ZFGOMS003"); //RFC函数名

                IRfcTable INFO = myfun.GetTable("IT_ITEM");
                INFO.Insert();
                INFO.CurrentRow.SetValue("VBELN", VBELN); //销售凭证(销售订单号) 当STAT为A时，该字段非必输；当STAT为DC时，该字段必输
                INFO.CurrentRow.SetValue("POSNR", POSNR); //销售分录凭证号

                myfun.Invoke(rfcdest); //调用函数

                IRfcStructure ter = myfun.GetStructure("E_RETURN");
                string ZTYPE = ter.GetValue("ZTYPE").ToString(); //消息类型
                string ZMESSAGE = ter.GetValue("ZMESSAGE").ToString(); //消息文本
                if (ZTYPE == "S")
                {
                    isPosted = true;
                    return true;
                }
                else
                {
                    rstMessage = string.Format("关闭销售订单失败,错误号:{0},错误消息:{1}", ZTYPE, ZMESSAGE);
                    return isPosted;
                }
            }
            catch (Exception ex)
            {
                isPosted = false;
                rstMessage = ex.Message;
                return isPosted;
            }
        }

        /// <summary>
        /// ZFGWMS012库存调整同步SAP系统接口
        /// </summary>
        /// <param name="CompanyCode"></param>
        /// <param name="lst"></param>
        /// <param name="rtnErrMsg"></param>
        /// <returns></returns>
        public List<SAPRETURN> ZFGWMS012(string CompanyCode, string docnum, string BKTXT, DateTime PostTime,
            List<ZFGWMS012> lst, out bool isPosted, out string rtnErrMsg)
        {
            isPosted = false;

            RfcDestination rfcdest = RfcDestinationManager.GetDestination(GetSAPLink(CompanyCode)); //初始化SAP连接配置
            //RFC调用函数
            RfcRepository rfcrep = rfcdest.Repository; //连接SAP
            IRfcFunction myfun = null; //初始化RFC函数调用方法
            myfun = rfcrep.CreateFunction("ZFGWMS012"); //RFC函数名
            //组织函数的参数

            IRfcStructure HEAD = myfun.GetStructure("I_HEAD");
            HEAD.SetValue("BLDAT", DateTime.Now.ToString("yyyyMMdd")); //凭证日期
            HEAD.SetValue("BUDAT", PostTime.ToString("yyyyMMdd")); //过账日期
            HEAD.SetValue("XBLNR", docnum); //领料单号
            HEAD.SetValue("BKTXT", BKTXT); //凭证抬头文本


            IRfcTable zh = myfun.GetTable("IT_ITEM");
            foreach (var input in lst)
            {
                zh.Insert();
                zh.CurrentRow.SetValue("ZNUM", input.ZNUM);
                zh.CurrentRow.SetValue("MATNR", input.MATNR);
                zh.CurrentRow.SetValue("WERKS", input.WERKS);
                zh.CurrentRow.SetValue("BWART", input.BWART);
                zh.CurrentRow.SetValue("MENGE", input.MENGE);
                zh.CurrentRow.SetValue("MEINS", input.MEINS);
                zh.CurrentRow.SetValue("LGORT", input.LGORT);
                zh.CurrentRow.SetValue("KOSTL", input.KOSTL);
                zh.CurrentRow.SetValue("SGTXT", input.SGTXT);

            }

            myfun.Invoke(rfcdest); //调用函数

            List<SAPRETURN> list = new List<SAPRETURN>();
            IRfcTable ter = myfun.GetTable("ET_RETURN");
            foreach (var info in ter)
            {
                string XBLNR = info.GetValue("XBLNR").ToString(); //领料单号
                string MBLNR = info.GetValue("MBLNR").ToString(); //物料凭证编号
                string ZEILE = info.GetValue("ZEILE").ToString(); //物料凭证中的项目
                string ZNUM = info.GetValue("ZNUM").ToString(); //配送单行号
                string ZTYPE = info.GetValue("ZTYPE").ToString(); //消息类型
                string ZMESSAGE = info.GetValue("ZMESSAGE").ToString(); //消息文本


                if (ZTYPE == "S")
                {
                    SAPRETURN mod = new SAPRETURN();
                    mod.DocNum = XBLNR;
                    mod.line = Convert.ToInt32(ZNUM);
                    mod.sapDocNum = MBLNR;
                    mod.sapline = Convert.ToInt32(ZEILE);
                    list.Add(mod);
                }
                else
                {
                    rtnErrMsg = ZMESSAGE;
                    return null;
                }
            }

            isPosted = true;
            rtnErrMsg = "";
            return list;
        }

        /// <summary>
        /// ZFGWMS013盘点差异同步SAP系统接口
        /// </summary>
        /// <param name="CompanyCode"></param>
        /// <param name="lst"></param>
        /// <param name="rtnErrMsg"></param>
        /// <returns></returns>
        public List<SAPRETURN> ZFGWMS013(string CompanyCode, string docnum, string BKTXT, DateTime PostTime,
            List<ZFGWMS013> lst, out bool isPosted, out string rtnErrMsg)
        {
            isPosted = false;

            RfcDestination rfcdest = RfcDestinationManager.GetDestination(GetSAPLink(CompanyCode)); //初始化SAP连接配置
            //RFC调用函数
            RfcRepository rfcrep = rfcdest.Repository; //连接SAP
            IRfcFunction myfun = null; //初始化RFC函数调用方法
            myfun = rfcrep.CreateFunction("ZFGWMS013"); //RFC函数名
            //组织函数的参数s

            IRfcStructure HEAD = myfun.GetStructure("I_HEAD");
            HEAD.SetValue("BLDAT", DateTime.Now.ToString("yyyyMMdd")); //凭证日期
            //HEAD.SetValue("BLDAT", DateTime.Now.AddMonths(-1).ToString("yyyyMMdd"));//凭证日期
            HEAD.SetValue("BUDAT", PostTime.ToString("yyyyMMdd")); //过账日期
            HEAD.SetValue("XBLNR", docnum); //领料单号
            HEAD.SetValue("BKTXT", BKTXT); //凭证抬头文本


            IRfcTable zh = myfun.GetTable("IT_ITEM");
            foreach (var input in lst)
            {
                zh.Insert();
                zh.CurrentRow.SetValue("ZNUM", input.ZNUM);
                zh.CurrentRow.SetValue("MATNR", input.MATNR);
                zh.CurrentRow.SetValue("WERKS", input.WERKS);
                zh.CurrentRow.SetValue("MENGE", input.MENGE);
                zh.CurrentRow.SetValue("MEINS", input.MEINS);
                zh.CurrentRow.SetValue("LGORT", input.LGORT);
                zh.CurrentRow.SetValue("SOBKZ", input.SOBKZ);
                zh.CurrentRow.SetValue("KDAUF", input.KDAUF);
                zh.CurrentRow.SetValue("KDPOS", input.KDPOS);
                zh.CurrentRow.SetValue("BWTAR", input.BWTAR);

            }

            myfun.Invoke(rfcdest); //调用函数

            List<SAPRETURN> list = new List<SAPRETURN>();
            IRfcTable ter = myfun.GetTable("ET_RETURN");
            foreach (var info in ter)
            {
                string XBLNR = info.GetValue("XBLNR").ToString(); //领料单号
                string MBLNR = info.GetValue("MBLNR").ToString(); //物料凭证编号
                string ZEILE = info.GetValue("ZEILE").ToString(); //物料凭证中的项目
                string ZNUM = info.GetValue("ZNUM").ToString(); //配送单行号
                string ZTYPE = info.GetValue("ZTYPE").ToString(); //消息类型
                string ZMESSAGE = info.GetValue("ZMESSAGE").ToString(); //消息文本


                if (ZTYPE == "S")
                {
                    SAPRETURN mod = new SAPRETURN();
                    mod.DocNum = XBLNR;
                    mod.line = Convert.ToInt32(ZNUM);
                    mod.sapDocNum = MBLNR;
                    mod.sapline = Convert.ToInt32(ZEILE);
                    list.Add(mod);
                }
                else
                {
                    rtnErrMsg = ZMESSAGE;
                    return null;
                }
            }

            isPosted = true;
            rtnErrMsg = "";
            return list;
        }

        /// <summary>
        /// ZFGWMS014库存调拨同步SAP系统接口（一步）
        /// </summary>
        /// <param name="CompanyCode"></param>
        /// <param name="lst"></param>
        /// <param name="rtnErrMsg"></param>
        /// <returns></returns>
        public List<SAPRETURN> ZFGWMS014(string CompanyCode, string docnum, string BKTXT, DateTime PostTime,
            List<ZFGWMS014> lst, out bool isPosted, out string rtnErrMsg)
        {
            isPosted = false;

            RfcDestination rfcdest = RfcDestinationManager.GetDestination(GetSAPLink(CompanyCode)); //初始化SAP连接配置
            //RFC调用函数
            RfcRepository rfcrep = rfcdest.Repository; //连接SAP
            IRfcFunction myfun = null; //初始化RFC函数调用方法
            myfun = rfcrep.CreateFunction("ZFGWMS014"); //RFC函数名
            //组织函数的参数

            IRfcStructure HEAD = myfun.GetStructure("I_HEAD");
            HEAD.SetValue("BLDAT", DateTime.Now.ToString("yyyyMMdd")); //凭证日期
            HEAD.SetValue("BUDAT", PostTime.ToString("yyyyMMdd")); //过账日期
            HEAD.SetValue("XBLNR", docnum); //领料单号
            HEAD.SetValue("BKTXT", BKTXT); //凭证抬头文本


            IRfcTable zh = myfun.GetTable("IT_ITEM");
            foreach (var input in lst)
            {
                zh.Insert();
                zh.CurrentRow.SetValue("ZNUM", input.ZNUM);
                zh.CurrentRow.SetValue("MATNR", input.MATNR);
                zh.CurrentRow.SetValue("WERKS", input.WERKS);
                zh.CurrentRow.SetValue("BWART", input.BWART);
                zh.CurrentRow.SetValue("MENGE", input.MENGE);
                zh.CurrentRow.SetValue("MEINS", input.MEINS);
                zh.CurrentRow.SetValue("LGORT", input.LGORT);
                zh.CurrentRow.SetValue("UMLGO", input.UMLGO);
                zh.CurrentRow.SetValue("SOBKZ", input.SOBKZ);
                zh.CurrentRow.SetValue("KDAUF", input.KDAUF);
                zh.CurrentRow.SetValue("KDPOS", input.KDPOS);
                zh.CurrentRow.SetValue("BWTAR", input.BWTAR);
                zh.CurrentRow.SetValue("SGTXT", input.SGTXT);

            }

            myfun.Invoke(rfcdest); //调用函数

            List<SAPRETURN> list = new List<SAPRETURN>();
            IRfcTable ter = myfun.GetTable("ET_RETURN");
            foreach (var info in ter)
            {
                string XBLNR = info.GetValue("XBLNR").ToString(); //领料单号
                string MBLNR = info.GetValue("MBLNR").ToString(); //物料凭证编号
                string ZEILE = info.GetValue("ZEILE").ToString(); //物料凭证中的项目
                string ZNUM = info.GetValue("ZNUM").ToString(); //配送单行号
                string ZTYPE = info.GetValue("ZTYPE").ToString(); //消息类型
                string ZMESSAGE = info.GetValue("ZMESSAGE").ToString(); //消息文本


                if (ZTYPE == "S")
                {
                    SAPRETURN mod = new SAPRETURN();
                    mod.DocNum = XBLNR;
                    mod.line = Convert.ToInt32(ZNUM);
                    mod.sapDocNum = MBLNR;
                    mod.sapline = Convert.ToInt32(ZEILE);
                    list.Add(mod);
                }
                else
                {
                    rtnErrMsg = ZMESSAGE;
                    return null;
                }
            }

            isPosted = true;
            rtnErrMsg = "";
            return list;
        }

        /// <summary>
        /// ZFGWMS015库存调拨同步SAP系统接口（二步）
        /// </summary>
        /// <param name="CompanyCode"></param>
        /// <param name="lst"></param>
        /// <param name="rtnErrMsg"></param>
        /// <returns></returns>
        public List<SAPRETURN> ZFGWMS015(string CompanyCode, string docnum, string BKTXT, DateTime PostTime,
            List<ZFGWMS015> lst, out bool isPosted, out string rtnErrMsg)
        {
            isPosted = false;

            RfcDestination rfcdest = RfcDestinationManager.GetDestination(GetSAPLink(CompanyCode)); //初始化SAP连接配置
            //RFC调用函数
            RfcRepository rfcrep = rfcdest.Repository; //连接SAP
            IRfcFunction myfun = null; //初始化RFC函数调用方法
            myfun = rfcrep.CreateFunction("ZFGWMS015"); //RFC函数名
            //组织函数的参数

            IRfcStructure HEAD = myfun.GetStructure("I_HEAD");
            HEAD.SetValue("BLDAT", DateTime.Now.ToString("yyyyMMdd")); //凭证日期
            HEAD.SetValue("BUDAT", PostTime.ToString("yyyyMMdd")); //过账日期
            HEAD.SetValue("XBLNR", docnum); //领料单号
            HEAD.SetValue("BKTXT", BKTXT); //凭证抬头文本


            IRfcTable zh = myfun.GetTable("IT_ITEM");
            foreach (var input in lst)
            {
                zh.Insert();
                zh.CurrentRow.SetValue("ZNUM", input.ZNUM);
                zh.CurrentRow.SetValue("MATNR", input.MATNR);
                zh.CurrentRow.SetValue("WERKS", input.WERKS);
                zh.CurrentRow.SetValue("BWART", input.BWART);
                zh.CurrentRow.SetValue("MENGE", input.MENGE);
                zh.CurrentRow.SetValue("MEINS", input.MEINS);
                zh.CurrentRow.SetValue("LGORT", input.LGORT);
                zh.CurrentRow.SetValue("UMLGO", input.UMLGO);
                zh.CurrentRow.SetValue("SOBKZ", input.SOBKZ);
                zh.CurrentRow.SetValue("KDAUF", input.KDAUF);
                zh.CurrentRow.SetValue("KDPOS", input.KDPOS);
                zh.CurrentRow.SetValue("BWTAR", input.BWTAR);
                zh.CurrentRow.SetValue("SGTXT", input.SGTXT);

            }

            myfun.Invoke(rfcdest); //调用函数

            List<SAPRETURN> list = new List<SAPRETURN>();
            IRfcTable ter = myfun.GetTable("ET_RETURN");
            foreach (var info in ter)
            {
                string XBLNR = info.GetValue("XBLNR").ToString(); //领料单号
                string MBLNR = info.GetValue("MBLNR").ToString(); //物料凭证编号
                string ZEILE = info.GetValue("ZEILE").ToString(); //物料凭证中的项目
                string ZNUM = info.GetValue("ZNUM").ToString(); //配送单行号
                string ZTYPE = info.GetValue("ZTYPE").ToString(); //消息类型
                string ZMESSAGE = info.GetValue("ZMESSAGE").ToString(); //消息文本


                if (ZTYPE == "S")
                {
                    SAPRETURN mod = new SAPRETURN();
                    mod.DocNum = XBLNR;
                    mod.line = Convert.ToInt32(ZNUM);
                    mod.sapDocNum = MBLNR;
                    mod.sapline = Convert.ToInt32(ZEILE);
                    list.Add(mod);
                }
                else
                {
                    rtnErrMsg = ZMESSAGE;
                    return null;
                }
            }

            isPosted = true;
            rtnErrMsg = "";
            return list;
        }

        /// <summary>
        /// ZFGWMS016委外发退料同步SAP系统接口
        /// </summary>
        /// <param name="CompanyCode"></param>
        /// <param name="lst"></param>
        /// <param name="rtnErrMsg"></param>
        /// <returns></returns>
        public List<SAPRETURN> ZFGWMS016(string CompanyCode, string docnum, string BKTXT, DateTime PostTime,
            List<ZFGWMS016> lst, out bool isPosted, out string rtnErrMsg)
        {
            isPosted = false;

            RfcDestination rfcdest = RfcDestinationManager.GetDestination(GetSAPLink(CompanyCode)); //初始化SAP连接配置
            //RFC调用函数
            RfcRepository rfcrep = rfcdest.Repository; //连接SAP
            IRfcFunction myfun = null; //初始化RFC函数调用方法
            myfun = rfcrep.CreateFunction("ZFGWMS016"); //RFC函数名
            //组织函数的参数

            IRfcStructure HEAD = myfun.GetStructure("I_HEAD");
            HEAD.SetValue("BLDAT", DateTime.Now.ToString("yyyyMMdd")); //凭证日期
            HEAD.SetValue("BUDAT", PostTime.ToString("yyyyMMdd")); //过账日期
            HEAD.SetValue("XBLNR", docnum); //领料单号
            HEAD.SetValue("BKTXT", BKTXT); //凭证抬头文本


            IRfcTable zh = myfun.GetTable("IT_ITEM");
            foreach (var input in lst)
            {
                zh.Insert();
                zh.CurrentRow.SetValue("ZNUM", input.ZNUM);
                zh.CurrentRow.SetValue("MATNR", input.MATNR);
                zh.CurrentRow.SetValue("WERKS", input.WERKS);
                zh.CurrentRow.SetValue("BWART", input.BWART);
                zh.CurrentRow.SetValue("MENGE", input.MENGE);
                zh.CurrentRow.SetValue("MEINS", input.MEINS);
                zh.CurrentRow.SetValue("LGORT", input.LGORT);
                zh.CurrentRow.SetValue("SOBKZ", input.SOBKZ);
                zh.CurrentRow.SetValue("LIFNR", input.LIFNR);
                zh.CurrentRow.SetValue("SGTXT", input.SGTXT);

            }

            myfun.Invoke(rfcdest); //调用函数

            List<SAPRETURN> list = new List<SAPRETURN>();
            IRfcTable ter = myfun.GetTable("ET_RETURN");
            foreach (var info in ter)
            {
                string XBLNR = info.GetValue("XBLNR").ToString(); //领料单号
                string MBLNR = info.GetValue("MBLNR").ToString(); //物料凭证编号
                string ZEILE = info.GetValue("ZEILE").ToString(); //物料凭证中的项目
                string ZNUM = info.GetValue("ZNUM").ToString(); //配送单行号
                string ZTYPE = info.GetValue("ZTYPE").ToString(); //消息类型
                string ZMESSAGE = info.GetValue("ZMESSAGE").ToString(); //消息文本


                if (ZTYPE == "S")
                {
                    SAPRETURN mod = new SAPRETURN();
                    mod.DocNum = XBLNR;
                    mod.line = Convert.ToInt32(ZNUM);
                    mod.sapDocNum = MBLNR;
                    mod.sapline = Convert.ToInt32(ZEILE);
                    list.Add(mod);
                }
                else
                {
                    rtnErrMsg = ZMESSAGE;
                    return null;
                }
            }

            isPosted = true;
            rtnErrMsg = "";
            return list;
        }


        /// <summary>
        /// ZFGWMS017生产报工接口SAP系统接口
        /// </summary>
        /// <param name="CompanyCode"></param>
        /// <param name="ACTID">I:报工  U:冲销报工 默认I</param>
        /// <param name="AUFNR">生产订单号</param>
        /// <param name="LTXA1">WMS单号 </param>
        /// <param name="GMNGA">按订单计量单位已确认的产量 (报工合格数量)</param>
        /// <param name="XMNGA">确认的废品数量总计(报工不合格数量)</param>
        /// <param name="PostTime">过账日期</param>
        /// <param name="isPosted"></param>
        /// <param name="rtnErrMsg"></param>
        /// <returns></returns>
        public List<SAPRETURN> ZFGWMS017(string CompanyCode, string ACTID, string AUFNR, string LTXA1, decimal GMNGA,
            decimal XMNGA, DateTime PostTime, out bool isPosted, out string rtnErrMsg)
        {
            isPosted = false;

            RfcDestination rfcdest = RfcDestinationManager.GetDestination(GetSAPLink(CompanyCode)); //初始化SAP连接配置
            //RFC调用函数
            RfcRepository rfcrep = rfcdest.Repository; //连接SAP
            IRfcFunction myfun = null; //初始化RFC函数调用方法
            myfun = rfcrep.CreateFunction("ZFGWMS017"); //RFC函数名
            //组织函数的参数

            IRfcStructure HEAD = myfun.GetStructure("I_HEAD");
            HEAD.SetValue("ACTID", ACTID); //I:报工  U:冲销报工
            HEAD.SetValue("AUFNR", AUFNR); //生产订单号
            HEAD.SetValue("LTXA1", LTXA1); //WMS单号
            HEAD.SetValue("GMNGA", GMNGA); //报工合格数量
            HEAD.SetValue("XMNGA", XMNGA); //报工不合格数量
            HEAD.SetValue("BUDAT", PostTime.ToString("yyyyMMdd")); //过账日期


            myfun.Invoke(rfcdest); //调用函数

            List<SAPRETURN> list = new List<SAPRETURN>();
            IRfcStructure ter = myfun.GetStructure("E_RETURN");

            string AUFNR1 = ter.GetValue("AUFNR").ToString(); //领料单号
            string RUECK = ter.GetValue("RUECK").ToString(); //操作完成的确认编号 
            string RMZHL = ter.GetValue("RMZHL").ToString(); //确认计数器 
            string ZTYPE = ter.GetValue("ZTYPE").ToString(); //消息类型
            string ZMESSAGE = ter.GetValue("ZMESSAGE").ToString(); //消息文本
            string STOKZ = ter.GetValue("STOKZ").ToString(); //凭证已被冲销


            if (ZTYPE == "S")
            {
                SAPRETURN mod = new SAPRETURN();
                mod.DocNum = AUFNR;
                mod.sapDocNum = RUECK;
                list.Add(mod);
            }
            else
            {
                rtnErrMsg = ZMESSAGE;
                return null;
            }


            isPosted = true;
            rtnErrMsg = "";
            return list;
        }



        /// <summary>
        /// ZFGWMS018生产订单投料、退料同步SAP系统接口
        /// </summary>
        /// <param name="CompanyCode"></param>
        /// <param name="lst"></param>
        /// <param name="rtnErrMsg"></param>
        /// <returns></returns>
        public List<SAPRETURN> ZFGWMS018(string CompanyCode, string docnum, string BKTXT, DateTime PostTime,
            List<ZFGWMS018> lst, out bool isPosted, out string rtnErrMsg)
        {
            isPosted = false;

            RfcDestination rfcdest = RfcDestinationManager.GetDestination(GetSAPLink(CompanyCode)); //初始化SAP连接配置
            //RFC调用函数
            RfcRepository rfcrep = rfcdest.Repository; //连接SAP
            IRfcFunction myfun = null; //初始化RFC函数调用方法
            myfun = rfcrep.CreateFunction("ZFGWMS018"); //RFC函数名
            //组织函数的参数

            IRfcStructure HEAD = myfun.GetStructure("I_HEAD");
            HEAD.SetValue("BLDAT", DateTime.Now.ToString("yyyyMMdd")); //凭证日期
            HEAD.SetValue("BUDAT", PostTime.ToString("yyyyMMdd")); //过账日期
            HEAD.SetValue("XBLNR", docnum); //领料单号
            HEAD.SetValue("BKTXT", BKTXT); //凭证抬头文本


            IRfcTable zh = myfun.GetTable("IT_ITEM");
            foreach (var input in lst)
            {
                zh.Insert();
                zh.CurrentRow.SetValue("ZNUM", input.ZNUM);
                zh.CurrentRow.SetValue("RSNUM", input.RSNUM);
                zh.CurrentRow.SetValue("RSPOS", input.RSPOS);
                zh.CurrentRow.SetValue("MATNR", input.MATNR);
                zh.CurrentRow.SetValue("WERKS", input.WERKS);
                zh.CurrentRow.SetValue("BWART", input.BWART);
                zh.CurrentRow.SetValue("MENGE", input.MENGE);
                zh.CurrentRow.SetValue("MEINS", input.MEINS);
                zh.CurrentRow.SetValue("LGORT", input.LGORT);
                zh.CurrentRow.SetValue("SOBKZ", input.SOBKZ);
                zh.CurrentRow.SetValue("UMLGO", input.UMLGO);
                zh.CurrentRow.SetValue("SGTXT", input.SGTXT);
                zh.CurrentRow.SetValue("BWTAR", input.BWTAR);
                zh.CurrentRow.SetValue("KDAUF", input.KDAUF);
                zh.CurrentRow.SetValue("KDPOS", input.KDPOS);
            }

            myfun.Invoke(rfcdest); //调用函数

            List<SAPRETURN> list = new List<SAPRETURN>();
            IRfcTable ter = myfun.GetTable("ET_RETURN");
            foreach (var info in ter)
            {
                string XBLNR = info.GetValue("XBLNR").ToString(); //领料单号
                string MBLNR = info.GetValue("MBLNR").ToString(); //物料凭证编号
                string ZEILE = info.GetValue("ZEILE").ToString(); //物料凭证中的项目
                string ZNUM = info.GetValue("ZNUM").ToString(); //配送单行号
                string ZTYPE = info.GetValue("ZTYPE").ToString(); //消息类型
                string ZMESSAGE = info.GetValue("ZMESSAGE").ToString(); //消息文本


                if (ZTYPE == "S")
                {
                    SAPRETURN mod = new SAPRETURN();
                    mod.DocNum = XBLNR;
                    mod.line = Convert.ToInt32(ZNUM);
                    mod.sapDocNum = MBLNR;
                    mod.sapline = Convert.ToInt32(ZEILE);
                    list.Add(mod);
                }
                else
                {
                    rtnErrMsg = ZMESSAGE;
                    return null;
                }
            }

            isPosted = true;
            rtnErrMsg = "";
            return list;
        }

        /// <summary>
        /// 获取生产订单状态
        /// </summary>
        /// <param name="CompanyCode">公司代码</param>
        /// <param name="PP_orderList">生产订单号</param>
        /// <returns></returns>
        public Dictionary<string, string> ZFGWMS019(string CompanyCode, List<string> PP_orderList)
        {
            if (PP_orderList == null || PP_orderList.Count() == 0)
            {
                return new Dictionary<string, string>();
            }

            var _PP_orderList = PP_orderList.Distinct(); //去重
            RfcDestination rfcdest = RfcDestinationManager.GetDestination(GetSAPLink(CompanyCode)); //初始化SAP连接配置
            //RFC调用函数
            RfcRepository rfcrep = rfcdest.Repository; //连接SAP
            IRfcFunction myfun = null; //初始化RFC函数调用方法
            myfun = rfcrep.CreateFunction("ZFGWMS019"); //RFC函数名
            //组织函数的参数

            IRfcTable zh = myfun.GetTable("IT_ITEM");
            foreach (var AUFNR in _PP_orderList)
            {
                zh.Insert();
                zh.CurrentRow.SetValue("AUFNR", AUFNR); //订单号
            }

            myfun.Invoke(rfcdest); //调用函数

            List<SAPRETURN> list = new List<SAPRETURN>();
            Dictionary<string, string> res = new Dictionary<string, string>();
            IRfcTable ter = myfun.GetTable("IT_ITEM");
            foreach (var info in ter)
            {
                string AUFNR = info.GetValue("AUFNR").ToString(); //订单号
                string STATUS = info.GetValue("STATUS").ToString(); //订单状态 1不允许 0允许
                res.Add(AUFNR, STATUS);
            }

            return res;
        }



        /// <summary>
        /// ZFGWMS021取消物料凭证同步SAP系统接口
        /// </summary>
        /// <param name="CompanyCode"></param>
        /// <param name="ACTID">单号</param>
        /// <param name="AUFNR">行号</param>
        /// <param name="PostTime">过账日期</param>
        /// <param name="isPosted"></param>
        /// <param name="rtnErrMsg"></param>
        /// <returns></returns>
        public List<SAPRETURN> ZFGWMS021(string CompanyCode, string MBLNR, List<string> line, DateTime PostTime,
            out bool isPosted, out string rtnErrMsg)
        {
            isPosted = false;

            RfcDestination rfcdest = RfcDestinationManager.GetDestination(GetSAPLink(CompanyCode)); //初始化SAP连接配置
            //RFC调用函数
            RfcRepository rfcrep = rfcdest.Repository; //连接SAP
            IRfcFunction myfun = null; //初始化RFC函数调用方法
            myfun = rfcrep.CreateFunction("ZFGWMS021"); //RFC函数名
            //组织函数的参数

            IRfcStructure HEAD = myfun.GetStructure("I_HEAD");
            HEAD.SetValue("MBLNR", MBLNR); //物料凭证编号
            HEAD.SetValue("BLDAT", DateTime.Now.ToString("yyyyMMdd")); //凭证中的凭证日期
            HEAD.SetValue("BUDAT", PostTime.ToString("yyyyMMdd")); //凭证中的过账日期
            HEAD.SetValue("BKTXT", ""); //凭证抬头文本

            IRfcTable zh = myfun.GetTable("IT_ITEM");
            foreach (var input in line)
            {
                zh.Insert();
                zh.CurrentRow.SetValue("ZEILE", input);
            }

            myfun.Invoke(rfcdest); //调用函数

            List<SAPRETURN> list = new List<SAPRETURN>();
            IRfcTable ter = myfun.GetTable("ET_RETURN");
            foreach (var info in ter)
            {
                string ZTYPE = info.GetValue("ZTYPE").ToString(); //消息类型
                string ZMESSAGE = info.GetValue("ZMESSAGE").ToString(); //消息文本

                if (ZTYPE != "S")
                {
                    rtnErrMsg = ZMESSAGE;
                    return null;
                }

            }

            isPosted = true;
            rtnErrMsg = "";
            return list;
        }



        /// <summary>
        /// 获取有效工单
        /// </summary>
        /// <param name="CompanyCode"></param>
        /// <param name="PP_orderList"></param>
        /// <returns></returns>
        public IEnumerable<string> GetSapValidOrder(string CompanyCode, List<string> PP_orderList)
        {
            return this.ZFGWMS019(CompanyCode, PP_orderList)?.Where(w => w.Value == "0")?.Select(s => s.Key);
        }



        /// <summary>
        /// ZFGWMS024WMS查询SAP库存接口
        /// </summary>
        /// <param name="CompanyCode"></param>
        /// <param name="lst"></param>
        /// <param name="rtnErrMsg"></param>
        /// <returns></returns>
        public List<MD_Stock> ZFGWMS024(string CompanyCode, string WERKS, string MATNR, string LGORT, string SOBKZ,
            string LIFNR, out string rtnErrMsg)
        {


            RfcDestination rfcdest = RfcDestinationManager.GetDestination(GetSAPLink(CompanyCode)); //初始化SAP连接配置
            //RFC调用函数
            RfcRepository rfcrep = rfcdest.Repository; //连接SAP
            IRfcFunction myfun = null; //初始化RFC函数调用方法
            myfun = rfcrep.CreateFunction("ZFGWMS024"); //RFC函数名
            //组织函数的参数

            IRfcStructure HEAD = myfun.GetStructure("I_HEAD");
            HEAD.SetValue("WERKS", WERKS);
            HEAD.SetValue("MATNR", MATNR);
            HEAD.SetValue("LGORT", LGORT);
            HEAD.SetValue("SOBKZ", SOBKZ);
            HEAD.SetValue("LIFNR", LIFNR);

            myfun.Invoke(rfcdest); //调用函数

            List<MD_Stock> list = new List<MD_Stock>();
            IRfcTable ter = myfun.GetTable("ET_RETURN");
            SAPApp xzsap_app = new SAPApp();
            var whs = xzsap_app.GetXZ_SAP_T001L().ToList();
            foreach (var info in ter)
            {

                MD_Stock mod = new MD_Stock();

                mod.ItemCode = info.GetValue("MATNR").ToString(); //物料编码
                mod.ItemName = info.GetValue("MAKTX").ToString(); //物料描述
                mod.Qty = Convert.ToDecimal(info.GetValue("MENGE")); //数量
                mod.Unit = info.GetValue("MEINS").ToString(); //单位
                mod.WhsCode = info.GetValue("LGORT").ToString(); //库存地点
                var wh = whs.Where(v => v.LGORT == mod.WhsCode)?.ToList().FirstOrDefault();
                if (wh != null)
                    mod.WhsName = wh.LGOBE;
                mod.SpecialStock = info.GetValue("SOBKZ").ToString(); //特殊库存
                mod.SaleNum = info.GetValue("VBELN").ToString(); //销售订单
                mod.SaleLine = Convert.ToInt32(info.GetValue("POSNR")); //销售订单项目
                mod.AssessType = info.GetValue("BWTAR").ToString(); //评估类型
                mod.SupplierCode = info.GetValue("LIFNR").ToString(); //供应商
                mod.BatchNum = info.GetValue("CHARG").ToString(); //批号


                list.Add(mod);

            }

            rtnErrMsg = "";
            return list;
        }


        /// <summary>
        /// ZFGWMS021取消物料凭证同步SAP系统接口
        /// </summary>
        /// <param name="CompanyCode"></param>
        /// <param name="ACTID">单号</param>
        /// <param name="AUFNR">行号</param>
        /// <param name="PostTime">过账日期</param>
        /// <param name="isPosted"></param>
        /// <param name="rtnErrMsg"></param>
        /// <returns></returns>
        public List<SAPRETURN> ZFGWMS025(string CompanyCode, string MBLNR, DateTime PostTime, out bool isPosted,
            out string rtnErrMsg)
        {
            isPosted = false;

            RfcDestination rfcdest = RfcDestinationManager.GetDestination(GetSAPLink(CompanyCode)); //初始化SAP连接配置
            //RFC调用函数
            RfcRepository rfcrep = rfcdest.Repository; //连接SAP
            IRfcFunction myfun = null; //初始化RFC函数调用方法
            myfun = rfcrep.CreateFunction("ZFGWMS025"); //RFC函数名
            //组织函数的参数

            IRfcStructure HEAD = myfun.GetStructure("I_HEAD");
            HEAD.SetValue("VBELN", MBLNR); //交货
            HEAD.SetValue("BUDAT", PostTime.ToString("yyyyMMdd")); //冲销日期

            myfun.Invoke(rfcdest); //调用函数

            List<SAPRETURN> list = new List<SAPRETURN>();
            IRfcStructure ter = myfun.GetStructure("E_RETURN");
            //IRfcTable ter = myfun.GetTable("E_RETURN");


            string ZTYPE = ter.GetValue("ZTYPE").ToString(); //消息类型
            string ZMESSAGE = ter.GetValue("ZMESSAGE").ToString(); //消息文本
            string E_FLG = myfun.GetValue("E_FLG").ToString(); //标识
            if (ZTYPE == "S")
            {
                //SAPRETURN mod = new SAPRETURN();
                ////mod.DocNum = XBLNR;
                //mod.basenum = info.VBELN;
                ////mod.baseline = Convert.ToInt32(EBELP);
                //mod.sapDocNum = DELIV_NUMB;
                ////mod.sapline = Convert.ToInt32(ZEILE);
                //list.Add(mod);
                if (E_FLG == "A")
                {
                    //调用交货删除接口
                    ZFGWMS027(CompanyCode, MBLNR, out isPosted, out rtnErrMsg);
                }
            }
            else
            {

                rtnErrMsg = ZMESSAGE;
                return null;
            }

            isPosted = true;
            rtnErrMsg = "";
            return list;
        }


        /// <summary>
        /// ZFGWMS024WMS查询SAP库存接口
        /// </summary>
        /// <param name="CompanyCode"></param>
        /// <param name="lst"></param>
        /// <param name="rtnErrMsg"></param>
        /// <returns></returns>
        public List<PP_ReturnMaterial_View> ZFGWMS024_PP(string CompanyCode, string WERKS, string MATNR, string LGORT,
            string SOBKZ, string LIFNR, out string rtnErrMsg)
        {


            RfcDestination rfcdest = RfcDestinationManager.GetDestination(GetSAPLink(CompanyCode)); //初始化SAP连接配置
            //RFC调用函数
            RfcRepository rfcrep = rfcdest.Repository; //连接SAP
            IRfcFunction myfun = null; //初始化RFC函数调用方法
            myfun = rfcrep.CreateFunction("ZFGWMS024"); //RFC函数名
            //组织函数的参数

            IRfcStructure HEAD = myfun.GetStructure("I_HEAD");
            HEAD.SetValue("WERKS", WERKS);
            HEAD.SetValue("MATNR", MATNR);
            HEAD.SetValue("LGORT", LGORT);
            HEAD.SetValue("SOBKZ", SOBKZ);
            HEAD.SetValue("LIFNR", LIFNR);

            myfun.Invoke(rfcdest); //调用函数

            List<PP_ReturnMaterial_View> list = new List<PP_ReturnMaterial_View>();
            IRfcTable ter = myfun.GetTable("ET_RETURN");
            SAPApp xzsap_app = new SAPApp();
            var whs = xzsap_app.GetXZ_SAP_T001L().ToList();
            foreach (var info in ter)
            {

                PP_ReturnMaterial_View mod = new PP_ReturnMaterial_View();

                mod.ComponentCode = info.GetValue("MATNR").ToString(); //物料编码
                mod.MaterialName = info.GetValue("MAKTX").ToString(); //物料描述
                mod.DemandQty = Convert.ToDecimal(info.GetValue("MENGE")); //数量
                mod.ComponentUnit = info.GetValue("MEINS").ToString(); //单位
                mod.OutWhsCode = info.GetValue("LGORT").ToString(); //库存地点
                var wh = whs.Where(v => v.LGORT == mod.OutWhsCode)?.ToList().FirstOrDefault();
                if (wh != null)
                    mod.OutWhsName = wh.LGOBE;
                mod.AssessmentCategory = string.IsNullOrEmpty(info.GetValue("BWTAR").ToString()) ? "" : "B"; //评估类型
                list.Add(mod);

            }

            rtnErrMsg = "";
            return list;
        }

        #region 创建计划订单

        /// <summary>
        /// 创建计划订单
        /// </summary>
        /// <param name="companyCode">公司代码</param>
        /// <param name="order">订单信息</param>
        /// <param name="isPosted">返回的布尔值</param>
        /// <param name="rstMessage">返回的消息</param>
        /// <returns></returns>
        public ZFITFG001018_RES ZFITFG001018(string companyCode, ZFITFG001018 param, out bool isPosted,
            out string rstMessage)
        {
            try
            {
                isPosted = false;
                rstMessage = string.Empty;
                RfcDestination rfcdest = RfcDestinationManager.GetDestination(GetSAPLink(companyCode)); //初始化SAP连接配置
                //RFC调用函数
                RfcRepository rfcrep = rfcdest.Repository; //连接SAP
                IRfcFunction myfun = null; //初始化RFC函数调用方法
                myfun = rfcrep.CreateFunction("ZFIT_FG001_018"); //RFC函数名
                var HEAD = myfun.GetStructure("HEAD_DATA");
                HEAD.SetValue("MATERIAL", param.MATERIAL);
                HEAD.SetValue("PROD_PLANT", param.PROD_PLANT);
                HEAD.SetValue("PLAN_PLANT", param.PLAN_PLANT);
                HEAD.SetValue("TOTAL_PLORD_QTY", param.TOTAL_PLORD_QTY);
                HEAD.SetValue("ORDER_START_DATE", param.ORDER_FIN_DATE);
                HEAD.SetValue("ORDER_FIN_DATE", param.ORDER_FIN_DATE);
                HEAD.SetValue("VERSION", param.VERSION);
                HEAD.SetValue("BASE_UOM", param.BASE_UOM);
                HEAD.SetValue("SALES_ORD", param.SALES_ORD);
                HEAD.SetValue("S_ORD_ITEM", param.S_ORD_ITEM);
                HEAD.SetValue("MANUAL_COMPONENT", param.MANUAL_COMPONENT);
                HEAD.SetValue("FIRMING_IND", param.FIRMING_IND);
                HEAD.SetValue("PLDORD_PROFILE", param.PLDORD_PROFILE);
                HEAD.SetValue("ACCTASSCAT", param.ACCTASSCAT);

                //订单明细
                IRfcTable zh = myfun.GetTable("DATA_IN");
                foreach (var detail in param.itmes)
                {
                    zh.Insert();
                    zh.CurrentRow.SetValue("PLANT", detail.PLANT); //销售凭证项目 当STAT为AC时，该字段必输
                    zh.CurrentRow.SetValue("MATERIAL", detail.MATERIAL); //物料编号 当STAT为A时，该字段必输
                    zh.CurrentRow.SetValue("ENTRY_QTY",
                        detail.ENTRY_QTY); //销售凭证项目类别 (除了退货：有价格：NCCU 没有价格：NCCF) 当STAT为A时，该字段必输
                    //zh.CurrentRow.SetValue("KBETR", detail.KBETR);//条件金额或百分比（单价） 当STAT为A时，该字段必输
                    zh.CurrentRow.SetValue("ENTRY_UOM", detail.ENTRY_UOM); //条件金额或百分比（单价） 当STAT为A时，该字段必输

                }

                myfun.Invoke(rfcdest); //调用函数

                IRfcStructure ter = myfun.GetStructure("DATA_OUT");
                string PLNUM = ter.GetValue("PLNUM").ToString(); //销售凭证
                string PTYPE = ter.GetValue("PTYPE").ToString(); //消息类型
                string PMSG = ter.GetValue("PMSG").ToString(); //消息文本
                string MATERIAL = param.MATERIAL;
                string SALES_ORD = param.SALES_ORD;
                string S_ORD_ITEM = param.S_ORD_ITEM;
                ZFITFG001018_RES mod = new ZFITFG001018_RES();
                if (!string.IsNullOrEmpty(PLNUM) && PTYPE == "S")
                {
                    isPosted = true;
                    mod.PTYPE = PTYPE;
                    mod.PMSG = PMSG;
                    mod.PLNUM = PLNUM;
                    mod.MATERIAL = MATERIAL;
                    mod.SALES_ORD = SALES_ORD;
                    mod.S_ORD_ITEM = S_ORD_ITEM;
                }
                else
                {
                    rstMessage = string.Format("错误号:{0},错误消息:{1}", PTYPE, PMSG);
                    isPosted = false;
                    return null;
                }

                return mod;
            }
            catch (Exception ex)
            {
                isPosted = false;
                rstMessage = ex.Message;
                return null;
            }
        }

        #endregion

        #region 计划订单转生产订单

        /// <summary>
        /// 销售订单
        /// </summary>
        /// <param name="companyCode">公司代码</param>
        /// <param name="order">订单信息</param>
        /// <param name="isPosted">返回的布尔值</param>
        /// <param name="rstMessage">返回的消息</param>
        /// <returns></returns>
        public List<ZFITFG001019_RES> ZFITFG001019(string companyCode, List<ZFITFG001019> reqs, out bool isPosted,
            out string rstMessage)
        {
            try
            {
                isPosted = false;
                rstMessage = string.Empty;
                RfcDestination rfcdest = RfcDestinationManager.GetDestination(GetSAPLink(companyCode)); //初始化SAP连接配置
                //RFC调用函数
                RfcRepository rfcrep = rfcdest.Repository; //连接SAP
                IRfcFunction myfun = null; //初始化RFC函数调用方法
                myfun = rfcrep.CreateFunction("ZFIT_FG001_019"); //RFC函数名
                var HEAD = myfun.GetTable("DATA_IN");
                foreach (var param in reqs)
                {
                    HEAD.Append();
                    HEAD.SetValue("PLNUM", param.PLNUM);
                    HEAD.SetValue("AUART", param.AUART);
                }

                myfun.Invoke(rfcdest); //调用函数

                var res = myfun.GetTable("DATA_IN");

                List<ZFITFG001019_RES> list = new List<ZFITFG001019_RES>();

                isPosted = true;
                for (var i = res.Count - 1; i >= 0; i--)
                {
                    ZFITFG001019_RES mod = new ZFITFG001019_RES();
                    mod.PLNUM = res[i].GetValue("PLNUM").ToString();
                    mod.AUFNR = res[i].GetValue("AUFNR").ToString();
                    mod.PTYPE = res[i].GetValue("PTYPE").ToString();
                    mod.PMSG = res[i].GetValue("PMSG").ToString();
                    mod.MATERIAL = reqs[i].MATERIAL;
                    mod.SALES_ORD = reqs[i].SALES_ORD;
                    mod.S_ORD_ITEM = reqs[i].S_ORD_ITEM;
                    mod.PLANSTATUS = reqs[i].PLANSTATUS;
                    mod.PLAMMSG = reqs[i].PLAMMSG;
                    list.Add(mod);
                }

                return list;
            }
            catch (Exception ex)
            {
                isPosted = false;
                rstMessage = ex.Message;
                return null;
            }
        }

        #endregion

        #region 计划订单转生产订单

        /// <summary>
        /// 销售订单
        /// </summary>
        /// <param name="companyCode">公司代码</param>
        /// <param name="order">订单信息</param>
        /// <param name="isPosted">返回的布尔值</param>
        /// <param name="rstMessage">返回的消息</param>
        /// <returns></returns>
        public ZFITFG001011_RES ZFITFG001011(string companyCode, List<string> reqs, out bool isPosted,
            out string rstMessage)
        {
            try
            {
                isPosted = false;
                rstMessage = string.Empty;
                RfcDestination rfcdest = RfcDestinationManager.GetDestination(GetSAPLink(companyCode)); //初始化SAP连接配置
                //RFC调用函数
                RfcRepository rfcrep = rfcdest.Repository; //连接SAP
                IRfcFunction myfun = rfcrep.CreateFunction("ZFIT_FG001_011"); //RFC函数名
                var ins = myfun.GetTable("DATA_IN");

                foreach (var dataIn in reqs)
                {
                    ins.Append();
                    ins.SetValue("ORDER_NUMBER", dataIn);
                }

                myfun.Invoke(rfcdest); //调用函数

                ZFITFG001011_RES res = new ZFITFG001011_RES();
                IRfcStructure ter = myfun.GetStructure("DATA_OUT");

                string EMSG = ter.GetValue("EMSG").ToString(); //释放消息
                string ETYPE = ter.GetValue("ETYPE").ToString(); //释放状态

                if (!string.IsNullOrEmpty(EMSG) && ETYPE == "S")
                {
                    isPosted = true;
                    res.EMSG = EMSG;
                    res.ETYPE = ETYPE;
                }
                else
                {
                    rstMessage = string.Format("错误号:{0},错误消息:{1}", ETYPE, EMSG);
                    isPosted = false;
                    return null;
                }

                return res;
            }
            catch (Exception ex)
            {
                isPosted = false;
                rstMessage = ex.Message;
                return null;
            }
        }

        #endregion

        #region 报工

        /// <summary>
        /// 销售订单
        /// </summary>
        /// <param name="companyCode">公司代码</param>
        /// <param name="order">订单信息</param>
        /// <param name="isPosted">返回的布尔值</param>
        /// <param name="rstMessage">返回的消息</param>
        /// <returns></returns>
        public ZFGWMS017_RES ZFGWMS017(string companyCode, ZFGWMS017 param, out bool isPosted, out string rstMessage)
        {
            try
            {
                isPosted = false;
                rstMessage = string.Empty;
                RfcDestination rfcdest = RfcDestinationManager.GetDestination(GetSAPLink(companyCode)); //初始化SAP连接配置
                //RFC调用函数
                RfcRepository rfcrep = rfcdest.Repository; //连接SAP
                IRfcFunction myfun = rfcrep.CreateFunction("ZFGWMS017"); //RFC函数名
                var HEAD = myfun.GetStructure("I_HEAD");
                HEAD.SetValue("ACTID", "I");
                HEAD.SetValue("AUFNR", param.AUFNR);
                HEAD.SetValue("BUDAT", param.BUDAT);
                HEAD.SetValue("LTXA1", param.LTXA1);
                HEAD.SetValue("GMNGA", param.GMNGA);
                HEAD.SetValue("XMNGA", param.XMNGA);

                myfun.Invoke(rfcdest); //调用函数

                ZFGWMS017_RES mod = new ZFGWMS017_RES();
                IRfcStructure ter = myfun.GetStructure("E_RETURN");

                string ZMESSAGE = ter.GetValue("ZMESSAGE").ToString(); //释放消息
                string ZTYPE = ter.GetValue("ZTYPE").ToString(); //释放状态


                if (!string.IsNullOrEmpty(ZMESSAGE) && ZTYPE == "S")
                {
                    isPosted = true;
                    mod.PNUM = param.AUFNR;
                    mod.ZTYPE = ZTYPE;
                    mod.ZMESSAGE = ZMESSAGE;
                }
                else
                {
                    rstMessage = string.Format("错误号:{0},错误消息:{1}", ZTYPE, ZMESSAGE);
                    isPosted = false;
                    return null;
                }

                return mod;
            }
            catch (Exception ex)
            {
                isPosted = false;
                rstMessage = ex.Message;
                return null;
            }
        }

        #endregion

        #region 取消报工

        /// <summary>
        /// 销售订单
        /// </summary>
        /// <param name="companyCode">公司代码</param>
        /// <param name="order">订单信息</param>
        /// <param name="isPosted">返回的布尔值</param>
        /// <param name="rstMessage">返回的消息</param>
        /// <returns></returns>
        public void ZFITFG001021(string companyCode, string aufnr, out bool isPosted, out string rstMessage)
        {
            try
            {
                isPosted = false;
                rstMessage = string.Empty;
                RfcDestination rfcdest = RfcDestinationManager.GetDestination(GetSAPLink(companyCode)); //初始化SAP连接配置
                //RFC调用函数
                RfcRepository rfcrep = rfcdest.Repository; //连接SAP
                IRfcFunction myfun = null; //初始化RFC函数调用方法
                IRfcFunction myfun2 = null; //初始化RFC函数调用方法
                myfun = rfcrep.CreateFunction("ZFIT_FG001_021"); //RFC函数名
                myfun.SetValue("AUFNR", aufnr);

                myfun.Invoke(rfcdest);
                
                isPosted = true;

                // ZFGWMS017_RES mod = new ZFGWMS017_RES();
                // IRfcStructure ter = myfun.GetStructure("DATA_OUT");
                //
                // string ZMESSAGE = ter.GetValue("ZMESSAGE").ToString();//释放消息
                // string ZTYPE = ter.GetValue("ZTYPE").ToString();//释放状态
                //
                //
                // if (!string.IsNullOrEmpty(ZMESSAGE) && ZTYPE=="S")
                // {
                //     mod.PNUM = param.AUFNR;
                //     mod.ZTYPE = ZTYPE;
                //     mod.ZMESSAGE = ZMESSAGE;
                // }
                // else
                // {
                //     rstMessage = string.Format("错误号:{0},错误消息:{1}", ZTYPE, ZMESSAGE);
                //     isPosted = false;
                //     return ;
                // }
                return;
            }
            catch (Exception ex)
            {
                isPosted = false;
                rstMessage = ex.Message;
                return;
            }
        }

        #endregion
    }
}

