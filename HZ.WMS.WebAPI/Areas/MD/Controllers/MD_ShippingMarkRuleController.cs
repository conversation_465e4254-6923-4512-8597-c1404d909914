using HZ.Core.Office;
using HZ.WMS.Application;
using HZ.WMS.Application.MD;
using HZ.WMS.Entity;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.MD.Import;
using HZ.WMS.Entity.MD.DTO;
using HZ.WMS.WebAPI.Controllers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;
using HZ.Core.Http;

namespace HZ.WMS.WebAPI.Areas.MD.Controllers
{
    /// <summary>
    /// 唛头规则
    /// </summary>
    public class MD_ShippingMarkRuleController : ApiBaseController
    {
        private readonly MD_ShippingMarkRuleApp _app = new MD_ShippingMarkRuleApp();

        #region 获取列表

        /// <summary>
        /// 获取列表
        /// </summary>
        /// <param name="keyword">关键字</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetList([FromUri] string keyword)
        {
            var result = new ResponseData();
            try
            {
                var list = _app.GetListWithTemplateName(keyword);

                result.Data = list;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取分页列表

        /// <summary>
        /// 获取分页列表
        /// </summary>
        /// <param name="pagination">分页参数</param>
        /// <param name="keyword">关键字</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri] Pagination pagination, [FromUri] string keyword)
        {
            var result = new ResponseData();
            try
            {
                var list = _app.GetPageListWithTemplateName(pagination, keyword);

                result.Data = new ResponsePageData { total = pagination.Total, items = list };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取单个实体

        /// <summary>
        /// 获取单个实体
        /// </summary>
        /// <param name="id">ID</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetEntity([FromUri] string id)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.GetEntityByKey(id);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 添加或更新

        /// <summary>
        /// 添加或更新
        /// </summary>
        /// <param name="entity">实体</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult UpdateOrInsert([FromBody] MD_ShippingMarkRule entity)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.UpdateOrInsert(entity, GetCurrentUser().LoginAccount);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="ids">ID数组</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Delete([FromBody] string[] ids)
        {
            var result = new ResponseData();
            try
            {
                if (ids == null || ids.Length == 0)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "请选择要删除的记录";
                    return Json(result);
                }

                var currentUser = GetCurrentUser().LoginAccount;
                var entities = _app.GetList(t => ids.Contains(t.Id)).ToList();
                
                foreach (var entity in entities)
                {
                    entity.IsDelete = true;
                    entity.DUser = currentUser;
                    entity.DTime = DateTime.Now;
                }

                result.Data = _app.Update(entities) > 0;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 导出

        /// <summary>
        /// 导出
        /// </summary>
        /// <param name="keyword">关键字</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] string keyword)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetList(t => string.IsNullOrEmpty(keyword)
                    || t.DeliveryCompany.Contains(keyword)
                    || t.ShippingMarkPrintMethod.Contains(keyword)
                    || t.ShippingMarkEncapsulationMethod.Contains(keyword)
                    || t.ShippingMarkPaper.Contains(keyword)
                    || t.ShippingMarkPaperOption.Contains(keyword)
                    || t.Brush.Contains(keyword)
                    || t.IsExport.Contains(keyword)
                    || t.PrintDirection.Contains(keyword)
                    || t.Remark.Contains(keyword)).ToList();

                var columns = ExcelService.FetchDefaultColumnList<MD_ShippingMarkRule>();
                string[] ignoreField = new string[]
                {
                    "Id", "IsDelete", "CUser", "MUser", "MTime", "DUser", "DTime"
                };

                var ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (var userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }

                return ExportToExcelFile<MD_ShippingMarkRule>(itemsData, columns, GetCurrentUser().UserName + "_唛头规则");
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #region 导入模板

        /// <summary>
        /// 导出导入模板
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelModel()
        {
            var result = new ResponseData();
            try
            {
                var columns = ExcelService.FetchDefaultColumnList<MD_ShippingMarkRuleImport>();
                string modelName = "唛头规则-导入模板";
                var itemsData = new List<MD_ShippingMarkRuleImport>()
                {
                    new MD_ShippingMarkRuleImport(){}
                };

                return ExportToExcelFile<MD_ShippingMarkRuleImport>(itemsData, columns, GetCurrentUser().UserName + "_" + modelName);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #region 导入

        /// <summary>
        /// 导入
        /// </summary>
        /// <param name="entitys">接收的导入集合</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult ImportExcelToData([FromBody] List<MD_ShippingMarkRuleImport> entitys)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                if (_app.ImportExcelToBaseData(entitys, GetCurrentUser().LoginAccount, out error_message))
                {
                    result.Data = true;
                    result.Code = (int)WMSStatusCode.Success;
                }
                else
                {
                    result.Data = false;
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
            return Json(result);
        }

        #endregion
    }
}
