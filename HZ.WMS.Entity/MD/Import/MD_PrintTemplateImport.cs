using System.ComponentModel;

namespace HZ.WMS.Entity.MD.Import
{
    /// <summary>
    /// 打印模板导入
    /// </summary>
    public class MD_PrintTemplateImport
    {
        /// <summary>
        /// 模板名称
        /// </summary>
        [Description("模板名称")]
        public string TemplateName { get; set; }

        /// <summary>
        /// 模板json
        /// </summary>
        [Description("模板json")]
        public string TemplateJson { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        [Description("是否启用")]
        public bool Enable { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Description("备注")]
        public string Remark { get; set; }

        /// <summary>
        /// 模板参数Id
        /// </summary>
        [Description("模板参数Id")]
        public string TemplateParameterId { get; set; }
    }
}
