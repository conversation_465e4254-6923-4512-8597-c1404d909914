using SqlSugar;
using System;
using System.Collections.Generic;

using HZ.Core.Security;
using HZ.WMS.Entity.Sys;
using HZ.Core.Http;
using System.Threading.Tasks;
using System.IO;

namespace HZ.WMS.Application.Sys
{

    /// <summary>
    /// 使用限制：
    /// 1. 支持压缩备份，有参数可以控制
    /// 2. 压缩和还原的备份文件路径都只能是数据库所在服务器的本地路径
    /// 3. 如果数据库正在使用中，无法还原
    /// 4. 另外，不支持在数据库存在的情况下用该库的备份文件直接进行还原，需要先删除原有库
    /// 5. 还原创建的数据库名称为参数中指定的库名，并非原始库名
    /// 6. 只支持完整数据库备份和完整数据库恢复
    /// </summary>
    public class Sys_DbBackupApp : BaseApp<Sys_DbBackup>
    {
        private Sys_DbBackupConfigApp _dbBackupConfigApp = new Sys_DbBackupConfigApp();
        //private readonly string backupSqlFormat = "BACKUP DATABASE [{0}] TO DISK='{1}' WITH {2},NAME='{3}',DESCRIPTION ='{4}';";
        //private readonly string restoreSqlFormat = "RESTORE DATABASE [{0}] FROM DISK='{1}'";
        // Microsoft SQL Server Express (64-bit) 不支持压缩选项，暂时去掉压缩可选项

        // {0}:数据库名称 {1}:备份文件名称(全路径)
        private readonly string backupSqlFormat = "BACKUP DATABASE [{0}] TO DISK = N'{1}' WITH NOFORMAT, NOINIT, NAME = N'master-完整 数据库 备份', SKIP, NOREWIND, NOUNLOAD, STATS = 10";
        // {0}:复原后数据库名称 {1}:备份文件名称(全路径) {2}:数据库原始名称 {3}:复原后数据库文件名(全路径mdf) {4}:复原后数据库日志文件名称(全路径)
        private readonly string restoreSqlFormat = "RESTORE DATABASE [{0}] FROM DISK = N'{1}' WITH FILE = 1, MOVE N'{2}' TO N'{3}', MOVE N'{2}_log' TO N'{4}', NOUNLOAD, STATS = 5";
        private readonly string defaultBackupDBExtensionFileName = ".bak";

        //BACKUP DATABASE [HuaZhiWms] TO DISK = N'C:\HuaZhiWMS\DbBackup\HuaZhiWms_20190903002.bak' WITH NOFORMAT, NOINIT, NAME = N'master-完整 数据库 备份', SKIP, NOREWIND, NOUNLOAD, STATS = 10
        //RESTORE DATABASE [HuaZhiWms_20190903002] FROM DISK = N'C:\HuaZhiWMS\DbBackup\HuaZhiWms_20190903002.bak' WITH FILE = 1, MOVE N'HuaZhiWms' TO N'C:\Program Files\Microsoft SQL Server\MSSQL12.MSSQLSERVER\MSSQL\DATA\HuaZhiWms_20190903002.mdf', MOVE N'HuaZhiWms_log' TO N'C:\Program Files\Microsoft SQL Server\MSSQL12.MSSQLSERVER\MSSQL\DATA\HuaZhiWms_20190903002_log.ldf', NOUNLOAD, STATS = 5

        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public Sys_DbBackupApp() : base()
        {
        }




        #endregion

        #region 数据库备份

        /// <summary>
        /// 添加（备份数据库操作）
        /// </summary>
        /// <param name="backupUser"></param>
        /// <returns></returns>
        public Task BackupDB(Sys_User backupUser)
        {
            return Task.Run(() =>
            {
                try
                {
                    Sys_DbBackupConfig dbBackupConfig = _dbBackupConfigApp.GetDbBackupConfig(); 
                    // 如果不带\ 默认追加
                    string backupFilePath = dbBackupConfig.BackupFilePath.Substring(dbBackupConfig.BackupFilePath.Length - 1, 1) == "\\" ? dbBackupConfig.BackupFilePath : dbBackupConfig.BackupFilePath + "\\";
                    string backupName = dbBackupConfig.DbName + "_" + DateTime.Now.ToString("yyyyMMddHHmmss") + defaultBackupDBExtensionFileName;
                    // 去除压缩选项支持
                    //string backupSql = string.Format(backupSqlFormat, dbBackupConfig.DbName, dbBackupConfig.BackupFilePath,
                    //    dbBackupConfig.IsCompressed ? "COMPRESSION " : "NO_COMPRESSION ", backupName, dbBackupConfig.Remark);
                    string backupSql = string.Format(backupSqlFormat, dbBackupConfig.DbName, backupFilePath + backupName);
                    base.DbContext.Ado.ExecuteCommand(backupSql);

                    Sys_DbBackup entity = new Sys_DbBackup();
                    entity.DbName = dbBackupConfig.DbName;
                    entity.DbServer = dbBackupConfig.DbServer;
                    entity.IsCompressed = dbBackupConfig.IsCompressed;
                    entity.BackupName = backupName;
                    entity.BackupFilePath = dbBackupConfig.BackupFilePath;
                    entity.CTime = DateTime.Now;
                    entity.CUser = backupUser.LoginAccount;

                    base.Insert(entity);
                }
                catch(Exception ex)
                {
                    throw ex;

                }
                
            });
        }

        #endregion

        #region 数据库还原(只允许一个一个还原)

        /// <summary>
        /// 
        /// </summary>
        /// <param name="backupDb"></param>
        /// <returns></returns>
        public Task RestoreDB(Sys_DbBackup backupDb)
        {
            Sys_DbBackupConfig config = _dbBackupConfigApp.GetDbBackupConfig();
            return Task.Run(() =>
            {
                //
                string restoreDbName = backupDb.BackupName.Replace(".bak", "").Trim().ToString();
                string backupDbFile = backupDb.BackupFilePath + backupDb.BackupName;
                string oldDbName = backupDb.DbName;
                string restoreMdfFile = config.RestoreFilePath+ restoreDbName + ".mdf";
                string restoreLdfFile = config.RestoreFilePath + restoreDbName + "_log.ldf";

                string restoreSql = string.Format(restoreSqlFormat, restoreDbName, backupDbFile,oldDbName,restoreMdfFile,restoreLdfFile);
                base.DbContext.Ado.ExecuteCommand(restoreSql);
            });
        }

        #endregion

        #region 删除数据库备份
        /// <summary>
        /// 删除数据库备份
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="deleteUser"></param>
        public void DeleteDbBackup(string[] ids,string deleteUser)
        {
            try
            {
                base.DbContext.Ado.BeginTran();
                List<Sys_DbBackup> dbBackupList = GetListByKeys(ids);
                dbBackupList.ForEach((item) => {

                    File.Delete(item.BackupFilePath + item.BackupName);
                    item.DUser = deleteUser;
                    base.Delete(item);
                });
                base.DbContext.Ado.CommitTran();
            }
            catch(Exception ex)
            {
                base.DbContext.Ado.RollbackTran();
                throw new Exception(ex.Message);
            }
        }

        #endregion

    }
}

