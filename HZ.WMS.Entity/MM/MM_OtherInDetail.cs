using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.MM
{
    /// <summary>
    /// 其他入库明细
    /// </summary>
    [SugarTable("MM_OtherInDetail")]
    public class MM_OtherInDetail : BaseEntity
    {
        /// <summary>
        /// 主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Description("ID")]
        public string DepReqDetailedID { get;set;}

        /// <summary>
        /// 入库单号
        /// </summary>
        [Description("入库单号")]
        public string DocNum { get; set; }

        /// <summary>
        /// 入库单行号
        /// </summary>
        [Description("入库单行号")]
        public int? Line { get; set; }

        /// <summary>
        /// 物料编码
        /// </summary>
        [Description("物料编码")]
        public string ItemCode { get; set; }

        /// <summary>
        /// 物料描述
        /// </summary>
        [Description("物料描述")]
        public string ItemName { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量")]
        public decimal? Qty { get; set; }

        /// <summary> 
        /// 单位
        /// </summary> 
        [Description("单位")]
        public string Unit { get; set; }

        /// <summary> 
        /// 仓库编号
        /// </summary> 
        [Description("仓库编号")]
        public string WhsCode { get; set; }

        /// <summary> 
        /// 仓库名称
        /// </summary> 
        [Description("仓库")]
        public string WhsName { get; set; }

        /// <summary> 
        /// 区域编号
        /// </summary> 
        [Description("区域编号")]
        public string RegionCode { get; set; }

        /// <summary> 
        /// 区域名称
        /// </summary> 
        [Description("区域")]
        public string RegionName { get; set; }

        /// <summary> 
        /// 库位编号
        /// </summary> 
        [Description("库位编号")]
        public string BinLocationCode { get; set; }

        /// <summary> 
        /// 库位名称
        /// </summary> 
        [Description("库位")]
        public string BinLocationName { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        [Description("状态")]
        public int? Status { get; set; }

        /// <summary>
        /// 移动类型
        /// </summary>
        [Description("移动类型")]
        public string MovementType { get; set; }

        /// <summary>
        /// 移动类型名称
        /// </summary>
        [Description("移动类型名称")]
        public string MovementTypeName { get; set; }

        /// <summary> 
        /// 成本中心
        /// </summary> 
        [Description("成本中心")]
        public string CostCenter { get; set; }

        /// <summary> 
        /// 成本中心名称
        /// </summary> 
        [Description("成本中心名称")]
        public string CostCenterName { get; set; }

        /// <summary> 
        /// 总账科目
        /// </summary> 
        [Description("总账科目")]
        public string LedgerType { get; set; }

        /// <summary> 
        /// 总账科目名称
        /// </summary> 
        [Description("总账科目名称")]
        public string LedgerTypeName { get; set; }

        /// <summary> 
        /// 生产订单号
        /// </summary> 
        [Description("生产订单号")]
        public string OrderNum { get; set; }

        /// <summary> 
        /// 特殊库存
        /// </summary> 
        [Description("特殊库存")]
        public string SpecialStock { get; set; }

        /// <summary> 
        /// 销售订单
        /// </summary> 
        [Description("销售订单")]
        public string SaleNum { get; set; }

        /// <summary> 
        /// 销售订单项目
        /// </summary> 
        [Description("销售订单项目")]
        public int? SaleLine { get; set; }

        /// <summary> 
        /// 评估类型
        /// </summary> 
        [Description("评估类型")]
        public string AssessType { get; set; }

        /// <summary> 
        /// 是否过账 1:已过帐 0：未过账
        /// </summary> 
        [Description("是否过账")]
        public bool? IsPosted { get; set; }

        /// <summary> 
        /// 过账人
        /// </summary> 
        [Description("过账人")]
        public string PostUser { get; set; }

        /// <summary> 
        /// 过账时间
        /// </summary> 
        [Description("过账时间")]
        public DateTime? PostTime { get; set; }

        /// <summary> 
        /// sap生成单号
        /// </summary> 
        [Description("sap生成单号")]
        public string SapDocNum { get; set; }

        /// <summary> 
        /// sap生成行号
        /// </summary> 
        [Description("sap生成行号")]
        public int? SapLine { get; set; }

        /// <summary>
        /// 公司代码
        /// </summary>
        [Description("公司代码")]
        public string CompanyCode { get; set; }

        /// <summary>
        /// 工厂代码
        /// </summary>
        [Description("工厂代码")]
        public string FactoryCode { get; set; }
    }
}
