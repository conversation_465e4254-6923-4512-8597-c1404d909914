using SqlSugar;
using System.ComponentModel;

namespace HZ.WMS.Entity.MD
{
    /// <summary>
    /// 打印参数模板
    /// </summary>
    [SugarTable("MD_PrintTemplateParameter")]
    public class MD_PrintTemplateParameter : BaseEntity
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Description("主键ID")]
        public string Id { get; set; }

        /// <summary>
        /// 模板名称
        /// </summary>
        [Description("模板名称")]
        public string TemplateName { get; set; }

        /// <summary>
        /// 是否启用 0否 1启用
        /// </summary>
        [Description("是否启用")]
        public bool? Enable { get; set; }

        /// <summary>
        /// 参数json
        /// </summary>
        [Description("参数json")]
        public string ParameterJson { get; set; }

        /// <summary>
        /// 模板key
        /// </summary>
        [Description("模板key")]
        public string TemplateKey { get; set; }
    }
}
