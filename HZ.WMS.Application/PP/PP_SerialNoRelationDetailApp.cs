using SqlSugar;
using HZ.Core.Http;
using HZ.WMS.Entity.PP;
using HZ.WMS.Entity.PP.ViewModel;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Application.PP
{
    /// <summary>
    /// 生产投料明细表
    /// </summary>
    public class PP_SerialNoRelationDetailApp : BaseApp<PP_SerialNoRelationDetail>
    {
        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public PP_SerialNoRelationDetailApp() : base()
        {
        }

        #endregion

        #region 获取SAP中间库数据

        public ISugarQueryable<PP_FeedingDetail_View> GetSapOrderDetailList(Expression<Func<PP_FeedingDetail_View, bool>> condition = null)
        {
            return DbContext.Queryable<PP_FeedingDetail_View>().Where(condition);
        }

        #endregion

    }
}
