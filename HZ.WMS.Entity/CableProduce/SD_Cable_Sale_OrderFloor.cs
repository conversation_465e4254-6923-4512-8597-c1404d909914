using System.ComponentModel;
using HZ.WMS.Entity;
using SqlSugar;

namespace AOS.OMS.Entity.Sale
{
    /// <summary>
    /// 订单类
    /// </summary>
    public class SD_Cable_Sale_OrderFloor : BaseEntity
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Description("主键ID")]
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }
        
        /// <summary>
        /// 订单号
        /// </summary>
        [Description("订单号")]
        public string OrderNum { get; set; }
        
        /// <summary>
        /// 父主键ID
        /// </summary>
        [Description("父主键ID")]
        public string Pid { get; set; }

        /// <summary>
        /// 层号
        /// </summary>
        [Description("层号")]
        public string FloorNum { get; set; }

        /// <summary>
        /// 高度
        /// </summary>
        [Description("高度")]
        public decimal? Height { get; set; }

        /// <summary>
        /// 序号
        /// </summary>
        [Description("序号")]
        public string SerialNo { get; set; }
        
    }
}
