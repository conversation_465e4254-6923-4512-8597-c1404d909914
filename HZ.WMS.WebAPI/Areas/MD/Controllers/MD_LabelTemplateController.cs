using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Web;
using System.Web.Http;
using HZ.Core.Http;
using HZ.Core.Office;
using HZ.WMS.Application;
using HZ.WMS.Application.MD;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.Sys;
using HZ.WMS.WebAPI.Controllers;

namespace HZ.WMS.WebAPI.Areas.MD.Controllers
{
    /// <summary>
    /// 标签模板
    /// </summary>
    public class MD_LabelTemplateController : ApiBaseController
    {
        private MD_LabelTempleteApp _app = new MD_LabelTempleteApp();

        #region 查询分页列表

        /// <summary>
        /// 查询分页列表 
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]string keyword, [FromUri]int templeteType)
        {
            var result = new ResponseData();
            try
            {
                page.Sort = "TempleteType asc,IsEnable asc";
                var itemsData = _app.GetPageList(page, x => (string.IsNullOrEmpty(keyword)
                || x.TempleteDesc.Contains(keyword)
                || x.TempleteFile.Contains(keyword)) && (templeteType == 0 || x.TempleteType == templeteType))?.ToList();

                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 查询所有模板

        /// <summary>
        /// 查询所有检验等级
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetList()
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.GetList().ToList();
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 查询模板

        /// <summary>
        /// 查询所有检验等级
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetTemplateList([FromUri]int TemplateType)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.GetList(x => x.TempleteType == TemplateType).ToList();
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Update([FromBody]MD_LabelTemplete entity)
        {
            var result = new ResponseData();
            try
            {
                entity.MUser = GetCurrentUser().LoginAccount;
                entity.MTime = DateTime.Now;
                result.Data = _app.Update(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 添加
        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Add([FromBody]MD_LabelTemplete entity)
        {
            var result = new ResponseData();
            try
            {
                Sys_User currLoginUser = GetCurrentUser();
                entity.CUser = currLoginUser.LoginAccount;
                entity.MUser = currLoginUser.LoginAccount;
                entity.CTime = DateTime.Now;
                entity.MTime = DateTime.Now;
                result.Data = _app.Insert(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除（包括批量）

        /// <summary>
        /// 删除
        /// </summary>
        /// <returns></returns>
        [HttpDelete]
        public IHttpActionResult Delete([FromBody]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                //数据伪删除，文件不做物理删除
                //List<MD_LabelTemplete> templates = _app.GetList(x => ids.Contains(x.TempleteID)).ToList();
                //foreach(MD_LabelTemplete temp in templates)
                //{
                //    System.IO.File.Delete(HttpContext.Current.Server.MapPath("~/") + temp.TempleteFile);
                //}
                result.Data = _app.DeleteByKeys(ids, GetCurrentUser().LoginAccount);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 启用模板

        /// <summary>
        /// 启用模板
        /// </summary>
        /// <param name="TemplateID"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult EnableLabelTemplate([FromUri]string TemplateID)
        {
            var result = new ResponseData();
            try
            {
                MD_LabelTemplete entity = _app.GetFirstEntity(x => x.TempleteID == TemplateID);
                entity.MUser = GetCurrentUser().LoginAccount;
                entity.MTime = DateTime.Now;
                entity.IsEnable = true;
                result.Data = _app.Update(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 禁用模板

        /// <summary>
        /// 禁用模板
        /// </summary>
        /// <param name="TemplateID"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult DisableLabelTemplate([FromUri]string TemplateID)
        {
            var result = new ResponseData();
            try
            {
                MD_LabelTemplete entity = _app.GetFirstEntity(x => x.TempleteID == TemplateID);
                entity.MUser = GetCurrentUser().LoginAccount;
                entity.MTime = DateTime.Now;
                entity.IsEnable = false;
                result.Data = _app.Update(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 上传模板文件

        /// <summary>
        /// 上传模板文件
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        //[AllowAnonymous]
        public IHttpActionResult UploadLabelTemplate()
        {
            var result = new ResponseData();
            try
            {
                //获取参数信息
                HttpContextBase context = (HttpContextBase)Request.Properties["MS_HttpContext"];
                HttpRequestBase request = context.Request;      //定义传统request对象
                string tempelateId = Guid.NewGuid().ToString();
                string templateDesc = request.Params["TempleteDesc"]?.ToString();
                string templateType = request.Params["TempleteType"]?.ToString();

                string remark = request.Params["Remark"]?.ToString();
                bool isEnable = bool.Parse(request.Params["IsEnable"]?.ToString());
                string templateFile = request.Params["TempleteFile"]?.ToString();

                MD_LabelTemplete labelTemplate = new MD_LabelTemplete();

                Sys_User loginUser = GetCurrentUser();
                //保存文件"UploadFiles\\LabelTemplate\\"
                string fileName = templateFile;//String.Format(tempelateId + ".repx");
                string filePath = HttpContext.Current.Server.MapPath("~/") + ConfigurationManager.AppSettings["LabelTemplateUploadPath"]?.ToString() + fileName;

                labelTemplate.TempleteID = tempelateId;
                labelTemplate.TempleteDesc = templateDesc;
                labelTemplate.TempleteFile = fileName;
                labelTemplate.TempleteType =int.Parse(templateType);

                labelTemplate.Remark = remark;
                labelTemplate.IsEnable = isEnable;
                labelTemplate.CUser = loginUser.LoginAccount;
                labelTemplate.MUser = loginUser.LoginAccount;



                request.Files[0].SaveAs(filePath);
                _app.Insert(labelTemplate);

                result.Data = new { FileName = fileName };
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            //返回结果

            return Json(result);

        }

        #endregion

        #region PDF打印

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        //[HttpGet]
        //[AllowAnonymous]
        //public HttpResponseMessage PringPdf()
        //{

        //    string exportFile = "";
        //    FileStream stream = File.Open(@"C:\Software\ReportTemplate.pdf", FileMode.Open);//初始化文件流
        //    byte[] array = new byte[stream.Length];//初始化字节数组，用来暂存读取到的字节
        //    stream.Read(array, 0, array.Length);//读取流中数据，写入到字节数组中

        //    if (string.IsNullOrEmpty(exportFile))
        //    {
        //        exportFile = "admin" + "_" + DateTime.Now.ToString("yyyyMMddHHmmssfff"); //到毫秒
        //    }
        //    if (stream == null)
        //    {
        //        return new HttpResponseMessage(HttpStatusCode.NoContent);
        //    }

        //    HttpResponseMessage result = new HttpResponseMessage(HttpStatusCode.OK);

        //    result.Content = new StreamContent(stream, (int)stream.Length);
        //    //http://tool.oschina.net/commons
        //    //result.Content.Headers.ContentType = new MediaTypeHeaderValue("application/octet-stream");        //告诉浏览器输出内容为流 
        //    result.Content.Headers.ContentType = new MediaTypeHeaderValue("application/pdf");                 //pdf文件
        //    //result.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.ms-excel");        // Excel97 - 2003(xls)
        //    //result.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");      // Excel 2007(xlsx)

        //    result.Content.Headers.ContentDisposition = new ContentDispositionHeaderValue("inline");      //attachment此报文头content-disposition， 对报文体进行描述， 规定了接收端的显示处理行为。 attachment 和 inline， 分别表示保存 还是 直接显示。
        //    result.Content.Headers.ContentDisposition.FileName = System.Web.HttpUtility.UrlEncode("test");    // HttpUtility.UrlEncode(exportFile);        // 中文名注意乱码问题处理
        //    result.Content.Headers.ContentLength = stream.Length;
        //    return result;
        //}


        #endregion

        #region 导出

        /// <summary>
        /// 
        /// </summary>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] string keyword)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetList().Where(t => string.IsNullOrEmpty(keyword) || t.TempleteDesc.Contains(keyword)).ToList();
                //var itemsData = _app.GetList().ToList();
                List<ExcelColumn<MD_LabelTemplete>> columns = ExcelService.FetchDefaultColumnList<MD_LabelTemplete>();
                string[] ignoreField = new string[] { "IsDelete", "DTime", "DUser" };

                List<ExcelColumn<MD_LabelTemplete>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<MD_LabelTemplete> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }

                columns.ForEach((column) =>
                {
                    if (column.ColumnName == "IsEnable")
                    {
                        column.Formattor = ExcelExportFormatter.IsEnableFormatter;
                    }
                });

                return ExportToExcelFile<MD_LabelTemplete>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }
        #endregion

    }
}
