//Chloe框架
using SqlSugar;


namespace HZ.WMS.Entity.MD
{
    /// <summary>
    /// 库位主数据
    /// </summary>
    public class MD_BinLocation : BaseEntity
    {
        /// <summary> 
        /// 库位ID
        /// </summary> 
        [SugarColumn(IsPrimaryKey = true)]
        public string BinID { get; set; }
        /// <summary>
        /// 库位编号
        /// </summary>
        [UniqueCode]
        public string BinLocationCode { get; set; }
        /// <summary>
        /// 库位名称
        /// </summary>
        public string BinLocationName { get; set; }
        /// <summary>
        /// 所属区域编号
        /// </summary>
        public string RegionCode { get; set; }
        /// <summary>
        /// 所属区域名称
        /// </summary>
        public string RegionName { get; set; }
        /// <summary>
        /// 所属仓库编号
        /// </summary>
        public string WhsCode { get; set; }
        /// <summary>
        /// 所属仓库名称
        /// </summary>
        public string WhsName { get; set; }


    }
}


