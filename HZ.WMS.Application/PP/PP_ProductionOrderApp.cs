using SqlSugar;
using HZ.Core.Http;
using HZ.WMS.Application.MD;
using HZ.WMS.Application.Sys;
using HZ.WMS.Entity;
using HZ.WMS.Entity.EAP;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.PP;
using HZ.WMS.Entity.PP.ViewModel;
using HZ.WMS.Entity.SRM;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Linq.Expressions;
using System.Text;

namespace HZ.WMS.Application.PP
{
    /// <summary>
    /// 生产订单主表
    /// </summary>
    public class PP_ProductionOrderApp : BaseApp<PP_ProductionOrder>
    {
        private BaseApp<BaseEntity> _baseApp = new BaseApp<BaseEntity>();
        private Sys_DictionaryApp dictionaryApp = new Sys_DictionaryApp();
        private MD_PartCodeApp codeApp = new MD_PartCodeApp();
        private CC_SafepartCustomerApp cscApp = new CC_SafepartCustomerApp();//220331-cc添加
        private CC_SafepartRuleApp csrApp = new CC_SafepartRuleApp();//220331-cc添加

        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public PP_ProductionOrderApp() : base()
        {
        }

        #endregion

        #region 同步SAP数据

        #region 获取启用序列号的生产订单

        /// <summary>
        /// 获取启用序列号的生产订单
        /// </summary>
        /// <returns></returns>
        public List<PP_ProductionOrder> GetOrderWithSerialNo(DateTime fromTime, DateTime toTime)
        {
            return DbContext.Ado.SqlQuery<PP_ProductionOrder>(@"select distinct 
                                                            b.ZORD_OUTNO as SerialNo,
															a.FEVOR as ProductionScheduler,
															a.AUFNR1 as HostProductionOrderNo,
                                                            a.AUFNR as ProductionOrderNo,
                                                            a.WERKS as FactoryCode,
                                                            e.ARBPL as ProductionLineCode,
															i.KTEXT as ProductionLineDes,--装配线
                                                            a.MATNR as MaterialNo,
                                                            case
                                                            when c.longtxt is not null and c.longtxt <> '' then c.longtxt
                                                            else c.MAKTX end as MaterialName,
                                                            case
									                        when CHARINDEX('&',REVERSE(c.MAKTX)) > 0 then (select right (c.MAKTX,CHARINDEX('&',REVERSE(c.MAKTX)) - 1))
                                                            else c.MAKTX end ProductModel,		--产品型号
                                                            a.DAUAT as OrderType,
                                                            a.GAMNG as OrderQty,
                                                            a.GMEIN as Unit,
															CAST(CAST(a.CY_SEQNR AS bigint) AS nvarchar) as ProductionBatch,
                                                            a.LGORT as ReceivingLocation,
                                                            a.TXT04 as OrderStatus,
															b.ZORD_CONT as ContractNo,--合同号
                                                            a.KDAUF as SalesOrderNo,
                                                            a.KDPOS as SalesOrderLineNo,
															b.CMTD_DELIV_DATE as DeliveryTime,--交货时间
                                                            a.GSTRP as StartTime,
                                                            a.GLTRP as EndTime,
															f.KUNNR as Customer,
                                                            h.NAME1 as Shippers,
															a.BWTAR as AssessmentType,
															case when a.Status = 0 and a.XLOEK <> 'X' then CAST(0 as bit)
															else CAST(1 as bit) end as IsDelete
                                                            from XZ_SAP.dbo.XZ_SAP_AFKO a
                                                            left join XZ_SAP.dbo.XZ_SAP_VBAP b on b.VBELN = a.KDAUF and b.POSNR = a.KDPOS and b.MATNR = a.MATNR and b.Status = 0
															left join XZ_SAP.dbo.XZ_SAP_MARC c on c.WERKS = a.WERKS and c.MATNR = a.MATNR and c.Status = 0 and c.LVORM = '' and c.LVORA = ''
															inner join XZ_WMS.dbo.MD_Item d on d.ItemCode = a.MATNR and d.IsDelete = 0 and d.StockManWay = 2
															left join XZ_SAP.dbo.XZ_SAP_AFVC e on e.AUFNR = a.AUFNR and e.VORNR = (select min(VORNR) VORNR from XZ_SAP.dbo.XZ_SAP_AFVC where AUFNR = a.AUFNR) and e.Status = 0 
															left join XZ_SAP.dbo.XZ_SAP_VBAK f on f.Status = 0 and f.VBELN = a.KDAUF
															left join XZ_SAP.dbo.XZ_SAP_KNA1 h on h.KUNNR = f.KUNNR and h.Status = 0-- and h.WAERS = a.WERKS
															left join XZ_SAP.dbo.XZ_SAP_CRHD i on i.WERKS = a.WERKS and i.ARBPL = e.ARBPL
                                                            where a.Status = 0 and a.XLOEK <> 'X' and a.GSTRP >= '" + fromTime + "' and a.GSTRP < '" + toTime + "' order by a.AUFNR ").ToList();
        }

        #endregion

        #region 生产部件代码

        /// <summary>
        /// 生产部件代码
        /// </summary>
        private void SetCode(PP_ProductionOrder order)
        {
            #region 220331-cc添加
            var cscRow = cscApp.GetFirstEntity(r => r.ccode == order.Customer && !r.IsDelete);
            if (cscRow == null)
            {
                return;
            }
            else
            {
                var csrRow = csrApp.GetFirstEntity(r => r.id == cscRow.ruleid && !r.IsDelete);
                if (csrRow == null)
                {
                    return;
                }
                else
                {
                    if (DateTime.Now.Year.ToString() != csrRow.codey)
                    {
                        csrRow.codey = DateTime.Now.Year.ToString();
                        csrRow.maxnum = "00000";
                    }
                    char[] maxNumArray = csrRow.maxnum.ToCharArray();
                    csrRow.maxnum = GetNewNum(maxNumArray);
                    var newNum = csrRow.code1 + csrRow.codey + csrRow.code2 + csrRow.maxnum;
                    switch (order.ProductionLineCode)
                    {
                        case "1A-XSQZ1":
                            order.PartCode = this.GetFullNum("F310" + newNum);
                            csrApp.Update(csrRow);
                            break;
                        case "1A-AQQZ1":
                            order.PartCode = this.GetFullNum("F320" + newNum);
                            csrApp.Update(csrRow);
                            break;
                        case "1A-HCQZ1":
                            order.PartCode = this.GetFullNum("F330" + newNum);
                            csrApp.Update(csrRow);
                            break;
                        default:
                            //主机切换后打开-220331-cc注释
                            //order.PartCode = this.GetFullNum("B370" + newNum);
                            //order.UplinkCode = this.GetFullNum("F350" + newNum);
                            //order.DownCode = this.GetFullNum("F380" + newNum);
                            //csrApp.Update(csrRow);
                            break;
                    }
                }
            }
            
            #endregion

            #region 220331-cc注释
            //string Serial = "";
            //var cusCode = codeApp.GetFirstEntity(w => w.SAPCode == order.Customer);
            //if (cusCode == null) return;
            //if (DateTime.Now.Year.ToString() != cusCode.OldYear)
            //{
            //    //如果进入下一年，重置流水号
            //    cusCode.OldSerial = cusCode.NewSerial;
            //    cusCode.OldYear = cusCode.NewYear;
            //    cusCode.NewSerial = 0;
            //    cusCode.NewYear = DateTime.Now.Year.ToString();
            //}
            //if (cusCode.OldYear == order.StartTime.Value.Year.ToString())
            //{
            //    cusCode.OldSerial++;
            //    Serial = this.GetSerial(cusCode.OldSerial, cusCode.CustomCode);
            //}
            //else
            //{
            //    cusCode.NewSerial++;
            //    Serial = this.GetSerial(cusCode.NewSerial, cusCode.CustomCode);
            //}
            //codeApp.Update(cusCode);//更新流水号

            //switch (order.ProductionLineCode)
            //{
            //    case "1A-XSQZ1":
            //        order.PartCode = this.GetCode(4, Serial, cusCode);
            //        break;
            //    case "1A-AQQZ1":
            //        order.PartCode = this.GetCode(5, Serial, cusCode);
            //        break;
            //    case "1A-HCQZ1":
            //        order.PartCode = this.GetCode(6, Serial, cusCode);
            //        break;
            //    default:
            //        order.PartCode = this.GetCode(1, Serial, cusCode);
            //        order.UplinkCode = this.GetCode(2, Serial, cusCode);
            //        order.DownCode = this.GetCode(3, Serial, cusCode);
            //        break;
            //}
            #endregion
        }

        ///十进制与三十三进制对应的数组
        private List<char> charList =new List<char>{
            '0','1','2','3','4','5','6','7','8','9',
            'A','B','C','D','E','F','G','H','J','K',
            'L','M','N','P','R','S','T','U','V','W',
            'X','Y','Z'
         };

        private string GetNewNum(char[] oldKey)
        {
            var newKey = "";
            List<int> charIndex = new List<int>();
            for(int i = 0; i < 5; i++)
            {
                charIndex.Add(charList.IndexOf(oldKey[i]));
            }
            charIndex[4] = charIndex[4] + 1;
            for (int i = 4; i >= 0; i--)
            {
                if (charIndex[i] > 32)
                {
                    charIndex[i] = charIndex[i] -33;
                    if (i != 0)
                    {
                        charIndex[i - 1] = charIndex[i - 1] + 1;
                    }
                    else
                    {
                        return "编码溢出";
                    }
                }
            }
            for(int i = 0; i < 5; i++)
            {
                newKey = newKey + charList[charIndex[i]].ToString();
            }
            return newKey;
        }
        private string GetFullNum(string oldCode)
        {
            var newCode = "";
            if (oldCode.Length == 19)
            {
                char[] charArray = oldCode.ToCharArray();
                var codeSum = 0;
                for(int i = 0; i < charArray.Length; i++)
                {
                    codeSum = codeSum + (i + 1) * charList.IndexOf(charArray[i]);
                }
                var yuNum = codeSum % 103;
                newCode = oldCode + yuNum.ToString().Last().ToString();
            }
            else
            {
                return "编码长度错误";
            }
            return newCode;
        }

        #region 220331-cc注释
        ///// <summary>
        ///// 最后拼接
        ///// </summary>
        ///// <param name="typeCode"></param>
        ///// <param name="serial"></param>
        ///// <param name="cusCode"></param>
        ///// <returns></returns>
        //private string GetCode(int typeCode, string serial, MD_PartCode cusCode)
        //{
        //    //部件码-客户代码-年份-8位流水号 用户可指定-检验值
        //    var temp = dictionaryApp.GetEntity(typeCode, "PP001")?.EnumValue + cusCode.CustomerCode + cusCode.OldYear + serial;
        //    return temp + this.GetCheckValue(temp);
        //}

        ///// <summary>
        ///// 获取8位流水号
        ///// </summary>
        ///// <returns></returns>
        //private string GetSerial(int serial, string cus)
        //{
        //    string res = string.Empty;
        //    if (string.IsNullOrEmpty(cus))
        //    {
        //        res = string.Format("{0:d8}", this.Get33(serial));
        //    }
        //    else//客户指定代码
        //    {
        //        var code = this.Get33(serial).PadLeft(8 - cus.Length, '0');
        //        res = cus + code;
        //    }
        //    return res;
        //}

        ///// <summary>
        ///// 十进制转三十三进制
        ///// </summary>
        ///// <param name="serial"></param>
        ///// <returns></returns>
        //private string Get33(int serial)
        //{
        //    int i;
        //    string res = "";
        //    while (serial >= 33)
        //    {
        //        i = serial % 33;//求余数
        //        res = charList[i].ToString() + res;//把得到的余数转为三十三进制数（例如“11”转“b”）
        //        serial /= 33;//求商再赋值给它自己（方便下个循环再除）
        //    }
        //    if (serial < 33)
        //        res = charList[serial].ToString() + res;
        //    return res;
        //}

        ///// <summary>
        ///// 获取校验码
        ///// </summary>
        ///// <param name="value"></param>
        ///// <returns></returns>
        //private string GetCheckValue(string value)
        //{
        //    int i = 1;
        //    int sum = 0;
        //    foreach (var item in value)
        //    {
        //        int tmp = charList.IndexOf(item);
        //        sum = sum + tmp * i;
        //        i++;
        //    }
        //    int remainder = sum % 103;
        //    return remainder.ToString().Last().ToString();
        //}
        #endregion

        #endregion

        /// <summary>
        /// 同步SAP数据
        /// </summary>
        /// <returns></returns>
        public bool UpdateDataBySAP(string Operator, DateTime fromTime, DateTime toTime)
        {
            //查询中间库数据
            var sapAllList = GetOrderWithSerialNo(fromTime, toTime);
            if (sapAllList == null || sapAllList.Count < 1) return true;
            //SAP存在数据
            var sapList = sapAllList.Where(w => !w.IsDelete).ToList();
            var sapNoList = sapList.Select(s => s.ProductionOrderNo).ToList();
            //SAP删除数据
            var sapDelList = sapAllList.Where(w => w.IsDelete).Select(s => s.ProductionOrderNo).ToList();
            ////获取生产订单状态
            //var statusList = new Sys_SAPCompanyInfoApp().ZFGWMS019("001", sapNoList);
            //var tempDel = statusList.Where(w => w.Value == "1").Select(s => s.Key);
            //sapList.RemoveAll(w => tempDel.Contains(w.ProductionOrderNo));
            //sapDelList.AddRange(tempDel);
            //SAP更新数据会删掉旧记录，因此需要去重
            sapDelList.RemoveAll(r => sapNoList.Contains(r));
            //本地库删除数据
            var delList = new List<PP_ProductionOrder>();
            //如果大于1000则分批
            if (sapDelList.Count > 1000)
            {
                for (int i = 0; i < sapDelList.Count; i += 1000)
                {
                    var tempList = GetList(w => sapDelList.Skip(i).Take(1000).Contains(w.ProductionOrderNo) && !w.IsDelete).ToList();
                    delList.AddRange(tempList);
                }
            }
            else
            {
                delList = GetList(w => sapDelList.Contains(w.ProductionOrderNo) && !w.IsDelete).ToList();
            }
            //var delList = GetList(w => sapDelList.Contains(w.ProductionOrderNo) && !w.IsDelete).ToList();
            delList.ForEach(entity =>
            {
                entity.DUser = Operator;
                entity.IsDelete = true;
            });
            //本地库更新数据
            var updateList = new List<PP_ProductionOrder>();
            //如果大于1000则分批
            if(sapNoList.Count > 1000)
            {
                for (int i = 0; i < sapNoList.Count; i+= 1000)
                {
                    var tempList = GetList(w => sapNoList.Skip(i).Take(1000).Contains(w.ProductionOrderNo)).ToList();
                    updateList.AddRange(tempList);
                }
            }
            else
            {
                updateList = GetList(w => sapNoList.Contains(w.ProductionOrderNo)).ToList();
            }
            updateList.ForEach(entity =>
            {
                var item = sapList.FirstOrDefault(f => f.ProductionOrderNo == entity.ProductionOrderNo);
                if(item != null)
                {
                    entity.FactoryCode = item.FactoryCode;
                    entity.ProductionLineCode = item.ProductionLineCode;
                    entity.ProductionLineDes = item.ProductionLineDes;
                    entity.ProductionScheduler = item.ProductionScheduler;
                    entity.MaterialNo = item.MaterialNo;
                    entity.MaterialName = item.MaterialName;
                    entity.ProductModel = item.ProductModel;
                    entity.OrderType = item.OrderType;
                    entity.OrderQty = item.OrderQty;
                    entity.Unit = item.Unit;
                    entity.ProductionBatch = item.ProductionBatch;
                    entity.ReceivingLocation = item.ReceivingLocation;
                    entity.OrderStatus = item.OrderStatus;
                    entity.AssessmentType = item.AssessmentType;
                    entity.ContractNo = item.ContractNo;
                    entity.SalesOrderNo = item.SalesOrderNo;
                    entity.SalesOrderLineNo = item.SalesOrderLineNo;
                    entity.DeliveryTime = item.DeliveryTime;
                    entity.StartTime = item.StartTime;
                    entity.EndTime = item.EndTime;
                    entity.Customer = item.Customer;
                    entity.Shippers = item.Shippers;
                    entity.MUser = Operator;
                    entity.IsDelete = false;
                }
            });
            updateList.AddRange(delList);
            //本地库新增数据
            var addList = sapList.Where(w => !(updateList.Select(s => s.ProductionOrderNo)).Contains(w.ProductionOrderNo)).ToList();
            //根据装配时间分组
            var groupList = addList.GroupBy(g => g.StartTime);
            foreach (var group in groupList)
            {
                var Serial = this.GetListForAggregation(w => w.StartTime.Value.Date == group.Key.Value.Date)
                    .Max(m => m.Serial) ?? 0;
                //主机生产订单
                var hostList = group.Where(w => w.ProductionScheduler == "101");
                foreach (var item in hostList)
                {
                    //生产部件代码
                    this.SetCode(item);
                    //生成主机序列号
                    item.CUser = Operator;
                    if (string.IsNullOrEmpty(item.SerialNo) || string.IsNullOrEmpty(item.SerialNo.Trim()))
                    {
                        Serial++;
                        item.Serial = Serial;
                        item.SerialNo = "Y" + (item.StartTime ?? DateTime.Now).ToString("yyyyMMdd") + string.Format("{0:d4}", item.Serial);
                        //item.SerialNo = (item.StartTime ?? DateTime.Now).ToString("yyyyMMdd") + string.Format("{0:d4}", item.Serial);
                    }
                    //启用序列号的订单数量都是1！
                    var componentList = addList.Where(w => w.HostProductionOrderNo == item.ProductionOrderNo);
                    int num = 0;
                    //转子为-1X
                    var rList = componentList.Where(w => w.ProductionScheduler == "103");
                    num = 10;
                    foreach (var entity in rList)
                    {
                        num++;
                        entity.Serial = num;
                        entity.CUser = Operator;
                        if (string.IsNullOrEmpty(entity.SerialNo) || string.IsNullOrEmpty(entity.SerialNo.Trim()))
                        {
                            entity.SerialNo = item.SerialNo + "-" + entity.Serial;
                        }
                    }
                    //定子为-2X
                    var sList = componentList.Where(w => w.ProductionScheduler == "102");
                    num = 20;
                    foreach (var entity in sList)
                    {
                        num++;
                        entity.Serial = num;
                        entity.CUser = Operator;
                        if (string.IsNullOrEmpty(entity.SerialNo) || string.IsNullOrEmpty(entity.SerialNo.Trim()))
                        {
                            entity.SerialNo = item.SerialNo + "-" + entity.Serial;
                        }
                    }
                }

                //没有主机生产订单且未分配序列号的订单
                var _addList = group.Where(
                    w => (string.IsNullOrEmpty(w.SerialNo) || string.IsNullOrEmpty(w.SerialNo.Trim())) 
                    && (string.IsNullOrEmpty(w.HostProductionOrderNo) || string.IsNullOrEmpty(w.HostProductionOrderNo.Trim()))
                    ).ToList();
                if (_addList != null && _addList.Count > 0)
                {
                    _addList.ForEach(item =>
                    {
                        for (int i = 0; i < item.OrderQty; i++)
                        {
                            Serial++;
                            var tempSerial = "Y" + (item.StartTime ?? DateTime.Now).ToString("yyyyMMdd") + string.Format("{0:d4}", Serial);
                            //var tempSerial = (item.StartTime ?? DateTime.Now).ToString("yyyyMMdd") + string.Format("{0:d4}", Serial);
                            if (item.ProductionScheduler == "102")
                            {
                                tempSerial = "D" + tempSerial;
                            }
                            if (item.ProductionScheduler == "103")
                            {
                                tempSerial = "Z" + tempSerial;
                            }
                            var host = new PP_ProductionOrder
                            {
                                SerialNo = tempSerial,
                                ProductionOrderNo = item.ProductionOrderNo,
                                HostProductionOrderNo = item.HostProductionOrderNo,
                                FactoryCode = item.FactoryCode,
                                ProductionLineCode = item.ProductionLineCode,
                                ProductionLineDes = item.ProductionLineDes,
                                ProductionScheduler = item.ProductionScheduler,
                                MaterialNo = item.MaterialNo,
                                MaterialName = item.MaterialName,
                                ProductModel = item.ProductModel,
                                OrderType = item.OrderType,
                                OrderQty = 1,//订单数量
                                Unit = item.Unit,
                                ProductionBatch = item.ProductionBatch,
                                ReceivingLocation = item.ReceivingLocation,
                                OrderStatus = item.OrderStatus,
                                AssessmentType = item.AssessmentType,
                                ContractNo = item.ContractNo,
                                SalesOrderNo = item.SalesOrderNo,
                                SalesOrderLineNo = item.SalesOrderLineNo,
                                StartTime = item.StartTime,
                                EndTime = item.EndTime,
                                DeliveryTime = item.DeliveryTime,
                                Customer = item.Customer,
                                Shippers = item.Shippers,
                                Serial = Serial,
                                CUser = Operator,
                            };
                            //生产部件代码
                            this.SetCode(host);
                            addList.Add(host);
                        }
                    });
                    addList.RemoveAll(w => _addList.Contains(w));
                }

                //有主机生产订单且未分配序列号的订单
                var addListWithHost = group.Where(
                    w => (string.IsNullOrEmpty(w.SerialNo) || string.IsNullOrEmpty(w.SerialNo.Trim()))
                    && (!string.IsNullOrEmpty(w.HostProductionOrderNo) || !string.IsNullOrEmpty(w.HostProductionOrderNo.Trim()))
                    ).ToList();
                //根据主机生产订单分组
                var _group = addListWithHost.GroupBy(g => g.HostProductionOrderNo);
                foreach (var item in _group)
                {
                    //如果有主机生产订单，且找不到主机序列号，则不插入WMS序列号数据库
                    var _host = this.GetFirstEntity(w => w.ProductionOrderNo == item.Key);
                    if(_host == null)
                    {
                        addList.RemoveAll(w => item.Contains(w));
                    }
                    else
                    {
                        //转子为-1X
                        var rList = item.Where(w => w.ProductionScheduler == "103");
                        var rSerial = this.GetListForAggregation(w => w.HostProductionOrderNo == item.Key && w.ProductionScheduler == "103")
                            .Max(m => m.Serial) ?? 10;
                        foreach (var it in rList)
                        {
                            rSerial++;
                            it.CUser = Operator;
                            it.Serial = rSerial;
                            it.SerialNo = _host.SerialNo + "-" + it.Serial;
                        }
                        //定子为-2X
                        var sList = item.Where(w => w.ProductionScheduler == "102");
                        var sSerial = this.GetListForAggregation(w => w.HostProductionOrderNo == item.Key && w.ProductionScheduler == "102")
                            .Max(m => m.Serial) ?? 20;
                        foreach (var it in sList)
                        {
                            sSerial++;
                            it.CUser = Operator;
                            it.Serial = sSerial;
                            it.SerialNo = _host.SerialNo + "-" + it.Serial;
                        }
                    }
                }
            }

            addList.ForEach(item => 
            {
                if (string.IsNullOrEmpty(item.CUser))
                {
                    item.CUser = Operator;
                }
            });

            //事务处理
            var iCount = false;
            try
            {
                //this.DbContext.Ado.BeginTran();
                this.DbContext.Ado.BeginTran();
                Update(updateList);
                Insert(addList);
                this.DbContext.Ado.CommitTran();
                iCount = true;
            }
            catch (Exception ex)
            {
                iCount = false;
                this.DbContext.Ado.RollbackTran();
                throw ex;
            }

            return iCount;
        }

        #endregion

        #region 获取SAP主机生产订单，带序列号

        public IEnumerable<PP_AssignSerialNo_View> GetOrderListWithSerialNo(Pagination page, string keyword,string ProductionLineDes, string ProductionScheduler, DateTime fromTime, DateTime toTime)
        {
            StringBuilder strBuilder = new StringBuilder();
            strBuilder.AppendLine(@"select distinct 
                                    ISNULL(g.SerialNo,b.ZORD_OUTNO) SerialNo,--出厂编号
                                    a.AUFNR as ProductionOrderNo,--生产订单
                                    j.CustomerOrderNum as OrderNo,--客户订单号
                                    a.FEVOR as ProductionScheduler,--生产调度员
                                    --h.NAME1 as Shippers,--发货单位
									j.CustomerName as Shippers,
                                    j.DevliyerDate as DeliveryTime,--交货时间
									case
									when CHARINDEX('&',REVERSE(c.MAKTX)) > 0 then (select right (c.MAKTX,CHARINDEX('&',REVERSE(c.MAKTX)) - 1))
                                    else c.MAKTX end ProductModel,		--产品型号
                                    a.GSTRP as StartTime,--装配日期
									case 
									when CHARINDEX('-',REVERSE(i.KTEXT)) > 0 then (select right (i.KTEXT,CHARINDEX('-',REVERSE(i.KTEXT)) - 1))
                                    else i.KTEXT end as ProductionLineDes,--装配线
                                    CAST(CAST(a.CY_SEQNR AS bigint) AS nvarchar) as ProductionBatch,--生产批次
                                    a.KDAUF as SalesOrderNo,
                                    a.KDPOS as SalesOrderLineNo,
                                    b.ZORD_CONT as ContractNo,--合同号
                                    a.MATNR as MaterialNo,--产品件号
                                    c.MAKTX as MaterialName,--产品描述
                                    j.ReceiveNumber ReceiptNo,--非标接收编号
                                    a.DAUAT as OrderType,--订单类型
                                    a.TXT04 as OrderStatus,--订单状态
                                    a.WERKS as FactoryCode,--工厂
                                    a.LGORT as ReceivingLocation,--库存地点
                                    g.IsNoticed,--通知状态
                                    g.Remark,--备注
                                    g.CUser,--创建用户
                                    g.CTime,--创建时间
                                    case when c.MATKL = 'ZJ11' then 1 when c.MATKL = 'ZJ12' then 2 else 0 end IsComponent--组件标识:1定子，2转子
                                    from XZ_SAP.dbo.XZ_SAP_AFKO a--SAP生产订单
                                    --SAP销售订单行表
                                    left join XZ_SAP.dbo.XZ_SAP_VBAP b on b.VBELN = a.KDAUF and b.POSNR = a.KDPOS and b.Status = 0
                                    --SAP物料主数据
                                    left join XZ_SAP.dbo.XZ_SAP_MARC c on c.WERKS = a.WERKS and c.MATNR = a.MATNR and c.Status = 0 and c.LVORM = '' and c.LVORA = ''
                                    --WMS物料数据表
                                    inner join XZ_WMS.dbo.MD_Item d on d.ItemCode = a.MATNR and d.IsDelete = 0 and d.StockManWay = 2
                                    --SAP生产工艺路线
                                    left join XZ_SAP.dbo.XZ_SAP_AFVC e on e.AUFNR = a.AUFNR and e.VORNR = (select min(VORNR) VORNR from XZ_SAP.dbo.XZ_SAP_AFVC where AUFNR = a.AUFNR) and e.Status = 0 
                                    --SAP销售订单主表
                                    --left join XZ_SAP.dbo.XZ_SAP_VBAK f on f.Status = 0 and f.VBELN = a.KDAUF
                                    --WMS生产订单
                                    left join XZ_WMS.dbo.PP_ProductionOrder g on g.ProductionOrderNo = a.AUFNR and g.IsDelete = 0
                                    --SAP客户主数据
                                    --left join XZ_SAP.dbo.XZ_SAP_KNA1 h on h.KUNNR = f.KUNNR and h.Status = 0-- and h.WAERS = a.WERKS
                                    --SAP工作中心
                                    left join XZ_SAP.dbo.XZ_SAP_CRHD i on i.WERKS = a.WERKS and i.ARBPL = e.ARBPL
                                    --关联OMS销售生产视图
                                    --left join XZ_OMS.dbo.oms_v_SalesAndProductionInfo j on j.SAP_NO = a.KDAUF and j.POSNR = a.KDPOS
                                    left join
                                    (
                                    select SAP_NO,POSNR,CustomerOrderNum,CustomerName,DevliyerDate,ReceiveNumber  from XZ_OMS.dbo.oms_v_SalesAndProductionInfo
                                    UNION
                                    select SAP_NO,POSNR,CustomerOrderNum,CustomerName,DevliyerDate,ReceiveNumber  from XZ_OMS.dbo.oms_v_PartsAndSecurityInfo
                                    ) j on j.SAP_NO = a.KDAUF and j.POSNR = a.KDPOS
                                    where a.Status = 0 and a.XLOEK <> 'X'  ");
            //根据时间过滤
            if (fromTime != null)
            {
                strBuilder.AppendLine("and a.GSTRP >= '" + fromTime + "' and a.GSTRP < '" + toTime + "' ");
            }
            //根据关键字过滤
            if (!string.IsNullOrEmpty(keyword))
            {
                strBuilder.AppendLine($"and (SerialNo like '%{keyword}%' or a.AUFNR like '%{keyword}%' or a.MATNR like '%{keyword}%' or c.MAKTX like '%{keyword}%' or j.CustomerName like '%{keyword}%' or j.CustomerOrderNum like '%{keyword}%' or b.ZORD_CONT like '%{keyword}%' or a.KDAUF like '%{keyword}%' or a.KDPOS like '%{keyword}%'  ) ");
            }
            //根据工作中心过滤
            if (!string.IsNullOrEmpty(ProductionLineDes))
            {
                strBuilder.AppendLine($"and (e.ARBPL like '%{ProductionLineDes}%' or i.KTEXT like '%{ProductionLineDes}%' ) ");
            }
            //根据生产调度员过滤
            if (!string.IsNullOrEmpty(ProductionScheduler))
            {
                strBuilder.AppendLine($"and a.FEVOR = '{ProductionScheduler}' ");
            }
            //排序
            strBuilder.AppendLine("order by a.AUFNR");
            var list = DbContext.Ado.SqlQuery<PP_AssignSerialNo_View>(strBuilder.ToString()).ToList();
            ////过滤无效订单
            //var statusList = new Sys_SAPCompanyInfoApp().ZFGWMS019("001", list.Select(s => s.ProductionOrderNo).ToList());
            //var tempDel = statusList.Where(w => w.Value == "1").Select(s => s.Key);
            //list.RemoveAll(w => tempDel.Contains(w.ProductionOrderNo));
            if (page == null)
            {
                return list;
            }
            else
            {
                page.Total = list.Count();
                //return list.Skip(page.PageSize * (page.PageNumber - 1)).Take(page.PageSize);
                if (page.PageSize > page.Total)
                {
                    return list.Skip(page.PageSize * (page.PageNumber - 1)).Take(page.Total);
                }
                return list.Skip(page.PageSize * (page.PageNumber - 1)).Take(page.PageSize);
            }
        }

        #endregion

        #region 获取车间执行排产

        public IEnumerable<PP_AssignSerialNo_View> GetPageScheduling(Pagination page, string keyword, string ProductionLineCode,string ProductionScheduler, string OrderNo,string ContractNo,
            string SalesOrderNo, string SalesOrderLineNo,DateTime startTime,DateTime endTime,DateTime createStartTime,DateTime createEndTime)
        {
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.AppendLine(@"select distinct  
                                        a.SerialNo,
                                        a.ProductionOrderNo,
                                        j.CustomerOrderNum as OrderNo,--客户订单号
										--h.NAME1 as Shippers,--发货单位
									    j.CustomerName as Shippers,
										j.DevliyerDate as DeliveryTime,--交货时间
										case
										when CHARINDEX('&',c.MAKTX) < 1 then c.MAKTX
										else c.MAKTX end ProductModel,		--产品型号
										a.StartTime,
										i.KTEXT as ProductionLineDes,--装配线
										a.ProductionBatch,
										a.SalesOrderNo,
										d.ZORD_CONT as ContractNo,--合同号
										a.MaterialNo,
                                        a.MaterialName,
										'' ReceiptNo,--非标接收编号
										a.OrderType,
										a.OrderStatus,
										a.FactoryCode,
										a.ProductionScheduler,
										a.ReceivingLocation,
										a.IsNoticed,--通知状态
										a.Remark,--备注
										a.CUser,--创建用户
										a.CTime,--创建时间
                                        case when c.MATKL = 'ZJ11' then 1 when c.MATKL = 'ZJ12' then 2 else 0 end IsComponent,
                                        a.HostProductionOrderNo,
                                        a.ProductionLineCode,
                                        a.OrderQty,
                                        a.Unit,
                                        a.SalesOrderLineNo,
                                        a.EndTime,
                                        a.EapSerialNo,
                                        a.IsDelete
                                        from XZ_WMS.dbo.PP_ProductionOrder a
                                        inner join XZ_SAP.dbo.XZ_SAP_AFKO b on b.AUFNR = a.ProductionOrderNo and b.Status = 0 and b.XLOEK <> 'X'
                                        left join XZ_SAP.dbo.XZ_SAP_MARC c on c.WERKS = a.FactoryCode and c.MATNR = a.MaterialNo and c.Status = 0 and c.LVORM = '' and c.LVORA = ''
                                        --left join XZ_SAP.dbo.XZ_SAP_VBAK f on f.Status = 0 and f.VBELN = a.SalesOrderNo
										--left join XZ_SAP.dbo.XZ_SAP_KNA1 h on h.KUNNR = f.KUNNR and h.Status = 0-- and h.WAERS = a.WERKS
										left join XZ_SAP.dbo.XZ_SAP_VBAP d on d.VBELN = a.SalesOrderNo and d.POSNR = a.SalesOrderLineNo and d.Status = 0
										left join XZ_SAP.dbo.XZ_SAP_AFVC e on e.AUFNR = a.ProductionOrderNo and e.VORNR = (select min(VORNR) VORNR from XZ_SAP.dbo.XZ_SAP_AFVC where AUFNR = a.ProductionOrderNo) and e.Status = 0 
										left join XZ_SAP.dbo.XZ_SAP_CRHD i on i.WERKS = a.FactoryCode and i.ARBPL = e.ARBPL 
                                        --关联OMS销售生产视图
                                        --left join XZ_OMS.dbo.oms_v_SalesAndProductionInfo j on j.SAP_NO = a.SalesOrderNo and j.POSNR = a.SalesOrderLineNo
                                        left join
                                        (
                                        select CustomerOrderNum,CustomerName,DevliyerDate,SAP_NO,POSNR from XZ_OMS.dbo.oms_v_SalesAndProductionInfo
                                        UNION
                                        select CustomerOrderNum,CustomerName,DevliyerDate,SAP_NO,POSNR from XZ_OMS.dbo.oms_v_PartsAndSecurityInfo
                                        ) j on j.SAP_NO = a.SalesOrderNo and j.POSNR = a.SalesOrderLineNo");
            stringBuilder.AppendLine(@"where a.IsDelete = 0 and a.ProductionScheduler in ('101','201') ");
            //根据时间过滤
            if (startTime != null && endTime != null)
            {
                stringBuilder.AppendLine($"and a.StartTime >= '{startTime}' and a.StartTime < '{endTime}' ");
            }
            //根据创建时间过滤
            if (createStartTime != null && createEndTime != null)
            {
                stringBuilder.AppendLine($"and a.CTime >= '{createStartTime}' and a.CTime < '{createEndTime}' ");
            }
            //根据关键字过滤
            if (!string.IsNullOrEmpty(keyword))
            {
                stringBuilder.AppendLine($"and (a.SerialNo like '%{keyword}%' or a.ProductionOrderNo like '%{keyword}%' or a.MaterialNo like '%{keyword}%' or a.MaterialName like '%{keyword}%' or j.CustomerName like '%{keyword}%') ");
            }
            //根据客户订单号
            if (!string.IsNullOrEmpty(OrderNo))
            {
                stringBuilder.AppendLine($"and j.CustomerOrderNum like '%{OrderNo}%'  ");
            }
            //根据合同号
            if (!string.IsNullOrEmpty(ContractNo))
            {
                stringBuilder.AppendLine($"and d.ZORD_CONT like '%{ContractNo}%'  ");
            }
            //根据工作中心过滤
            if (!string.IsNullOrEmpty(ProductionLineCode))
            {
                stringBuilder.AppendLine($"and (a.ProductionLineCode like '%{ProductionLineCode}%' or i.KTEXT like '%{ProductionLineCode}%' ) ");
            }
            //根据生产调度员过滤
            if(!string.IsNullOrEmpty(ProductionScheduler))
            {
                stringBuilder.AppendLine($"and a.ProductionScheduler = '{ProductionScheduler}' ");
            }
            //根据销售订单
            if (!string.IsNullOrEmpty(SalesOrderNo))
            {
                stringBuilder.AppendLine($"and a.SalesOrderNo = '{SalesOrderNo}' ");
            }
            //根据销售订单
            if (!string.IsNullOrEmpty(SalesOrderLineNo))
            {
                stringBuilder.AppendLine($"and a.SalesOrderLineNo = '{SalesOrderLineNo}' ");
            }
            //排序
            stringBuilder.AppendLine("order by a.ProductionOrderNo asc ");
            var list = DbContext.Ado.SqlQuery<PP_AssignSerialNo_View>(stringBuilder.ToString()).ToList();
            ////过滤无效订单
            //var statusList = new Sys_SAPCompanyInfoApp().ZFGWMS019("001", list.Select(s => s.ProductionOrderNo).ToList());
            //var tempDel = statusList.Where(w => w.Value == "1").Select(s => s.Key);
            //list.RemoveAll(w => tempDel.Contains(w.ProductionOrderNo));
            if (page == null)
            {
                return list;
            }
            else
            {
                page.Total = list.Count();
                //return list.Skip(page.PageSize * (page.PageNumber - 1)).Take(page.PageSize);
                if (page.PageSize > page.Total)
                {
                    return list.Skip(page.PageSize * (page.PageNumber - 1)).Take(page.Total);
                }
                return list.Skip(page.PageSize * (page.PageNumber - 1)).Take(page.PageSize);
            }
        }

        #endregion

        #region 导出序列号分配

        /// <summary>
        /// 导出主机排产单
        /// </summary>
        /// <param name="page"></param>
        /// <param name="condition"></param>
        /// <returns></returns>
        public IEnumerable<PP_ExportAssignSerialNo_View> ExportAssignSerialNo(Pagination page, Expression<Func<PP_ExportAssignSerialNo_View, bool>> condition = null)
        {
            //查询SAP所有数据
            var query = DbContext.Queryable<PP_ExportAssignSerialNo_View>()
                .Where(condition).OrderBy("ProductionOrderNo asc,SerialNo asc").ToList();

            int i = 1;
            foreach (var item in query)
            {
                item.SequenceNo = i++;
            }
            if (page == null)
            {
                return query;
            }
            else
            {
                page.Total = query.Count();
                if (page.PageSize > page.Total)
                {
                    return query.Skip(page.PageSize * (page.PageNumber - 1)).Take(page.Total);
                }
                return query.Skip(page.PageSize * (page.PageNumber - 1)).Take(page.PageSize);
            }
        }

        #endregion

        #region 获取EAP出厂编号

        public List<PP_ExportHostScheduling_View> HandleEapSerialNo(List<PP_ExportHostScheduling_View> assignSerialList)
        {
            if (assignSerialList != null)
            {
                var pageNo = assignSerialList.Count() / 500;
                if (assignSerialList.Count() % 500 > 0)
                {
                    pageNo = pageNo + 1;
                }

                List<SGEAP_CUS_Order> cusOrderList = new List<SGEAP_CUS_Order>();

                for (int i = 0; i < pageNo; i++)
                {
                    int startNo = i * 500;
                    int endNo = (i + 1) * 500;
                    if (i == pageNo - 1)
                    {
                        endNo = assignSerialList.Count;
                    }
                    List<string> contractNoList = new List<string>();
                    List<string> orderNoList = new List<string>();

                    for (int j = startNo; j < endNo; j++)
                    {
                        assignSerialList[j].EapSerialNo = "";
                        if (assignSerialList[j].ContractNo != null)
                        {
                            contractNoList.Add(assignSerialList[j].ContractNo);
                        }
                        orderNoList.Add(assignSerialList[j].OrderNo);
                    }
                    cusOrderList.AddRange(DbContextForEAP.Queryable<SGEAP_CUS_Order>().Where(t => contractNoList.Contains(t.ContractNo) && orderNoList.Contains(t.OrderNo))?.ToList());
                }


                Dictionary<string, string> dic = new Dictionary<string, string>();
                foreach (SGEAP_CUS_Order cusOrder in cusOrderList)
                {

                    if (!dic.ContainsKey(cusOrder.ContractNo + cusOrder.OrderNo))
                    {
                        dic.Add(cusOrder.ContractNo + cusOrder.OrderNo, cusOrder.SerialNo);
                    }
                }

                foreach (PP_ExportHostScheduling_View assignSerialNo in assignSerialList)
                {
                    if (dic.ContainsKey(assignSerialNo.ContractNo + assignSerialNo.OrderNo))
                    {
                        assignSerialNo.EapSerialNo = dic[assignSerialNo.ContractNo + assignSerialNo.OrderNo];
                    }
                }

            }

            return assignSerialList;
        }

        #endregion

        #region 导出主机排产单

        /// <summary>
        /// 导出主机排产单
        /// </summary>
        /// <param name="page"></param>
        /// <param name="condition"></param>
        /// <returns></returns>
        public IEnumerable<PP_ExportHostScheduling_View> ExportHostScheduling(Pagination page, Expression<Func<PP_ExportHostScheduling_View, bool>> condition = null)
        {
            //查询SAP所有数据
            var query = DbContext.Queryable<PP_ExportHostScheduling_View>()
                .Where(condition).OrderBy("ProductionOrderNo asc, SerialNo asc").ToList();

            int i = 1;
            foreach (var item in query)
            {
                item.SequenceNo = i++;
            }
            if (page == null)
            {
                return query;
            }
            else
            {
                page.Total = query.Count();
                if (page.PageSize > page.Total)
                {
                    return query.Skip(page.PageSize * (page.PageNumber - 1)).Take(page.Total);
                }
                return query.Skip(page.PageSize * (page.PageNumber - 1)).Take(page.PageSize);
            }
        }

        #endregion

        #region 导出主机总装线/巨通线/蒂森线

        /// <summary>
        /// 导出主机总装线/巨通线/蒂森线
        /// </summary>
        /// <param name="page"></param>
        /// <param name="condition"></param>
        /// <returns></returns>
        public IEnumerable<PP_ExportHostLine_View> ExportHostLine(Pagination page,DateTime? startTime,DateTime? endTime, Func<PP_ExportHostLine_View, bool> condition = null)
        {
            //查询SAP所有数据
            SugarParameter[] param =
                {
                    new SugarParameter("@SDATE",startTime),
                    new SugarParameter("@EDATE",endTime),
                };
            var query = DbContext.Ado.UseStoredProcedure().SqlQuery<PP_ExportHostLine_View>("PP_ExportHostLine_Proc", param)
                .Where(condition)?.ToList();
            if (page == null)
            {
                return query;
            }
            else
            {
                page.Total = query.Count();
                if (page.PageSize > page.Total)
                {
                    return query.Skip(page.PageSize * (page.PageNumber - 1)).Take(page.Total);
                }
                return query.Skip(page.PageSize * (page.PageNumber - 1)).Take(page.PageSize);
            }
        }

        #endregion

        #region 导出DT线

        /// <summary>
        /// 导出DT线
        /// </summary>
        /// <param name="page"></param>
        /// <param name="condition"></param>
        /// <returns></returns>
        public IEnumerable<PP_ExportDTLine_View> ExportDTLine(Pagination page, DateTime? startTime, DateTime? endTime, Func<PP_ExportDTLine_View, bool> condition = null)
        {
            //查询SAP所有数据
            SugarParameter[] param =
                {
                    new SugarParameter("@SDATE",startTime),
                    new SugarParameter("@EDATE",endTime),
                };
            var query = DbContext.Ado.UseStoredProcedure().SqlQuery<PP_ExportDTLine_View>("PP_ExportDTLine_Proc",  param)
                .Where(condition)?.ToList();
            if (page == null)
            {
                return query;
            }
            else
            {
                page.Total = query.Count();
                if (page.PageSize > page.Total)
                {
                    return query.Skip(page.PageSize * (page.PageNumber - 1)).Take(page.Total);
                }
                return query.Skip(page.PageSize * (page.PageNumber - 1)).Take(page.PageSize);
            }
        }

        #endregion

        #region 导出试制返修线

        /// <summary>
        /// 导出试制返修线
        /// </summary>
        /// <param name="page"></param>
        /// <param name="condition"></param>
        /// <returns></returns>
        public IEnumerable<PP_ExportRepairLine_View> ExportRepairLine(Pagination page, DateTime? startTime, DateTime? endTime, Func<PP_ExportRepairLine_View, bool> condition = null)
        {
            //查询SAP所有数据
            SugarParameter[] param =
                {
                    new SugarParameter("@SDATE",startTime),
                    new SugarParameter("@EDATE",endTime),
                };
            var query = DbContext.Ado.UseStoredProcedure().SqlQuery<PP_ExportRepairLine_View>("PP_ExportRepairLine_Proc", param)
                .Where(condition)?.ToList();
            if (page == null)
            {
                return query;
            }
            else
            {
                page.Total = query.Count();
                if (page.PageSize > page.Total)
                {
                    return query.Skip(page.PageSize * (page.PageNumber - 1)).Take(page.Total);
                }
                return query.Skip(page.PageSize * (page.PageNumber - 1)).Take(page.PageSize);
            }
        }

        #endregion

        #region 获取完工入库标签字段

        public DataTable GetStorageLabel(List<string> SerialNo)
        {
            string sql = @"select a.SerialNo,a.PartCode,i.ZORD_CONT AS ContractNo
                            from XZ_WMS.dbo.PP_ProductionOrder a 
                            inner join XZ_SAP.dbo.XZ_SAP_AFKO b on b.AUFNR = a.ProductionOrderNo and b.Status = 0 and b.XLOEK <> 'X'
                            LEFT JOIN XZ_SAP.dbo.XZ_SAP_VBAP AS i ON i.Status = 0 AND i.VBELN = b.KDAUF AND i.POSNR = b.KDPOS 
                            where a.IsDelete = 0 and b.XLOEK <> 'X' and a.SerialNo in ('" + string.Join("','", SerialNo) + "')"
                            + "order by a.SerialNo";
            return this.DbContext.Ado.GetDataTable(sql);
        }

        #endregion

        #region 获取SAP生产订单主表

        public ISugarQueryable<PP_ProductionOrder_View> GetSapOrderList(Expression<Func<PP_ProductionOrder_View, bool>> condition = null)
        {
            return DbContext.Queryable<PP_ProductionOrder_View>().Where(condition);
        }

        #endregion

        #region 获取SAP生产订单子表

        public ISugarQueryable<PP_ProductionOrderDetail_View> GetSapOrderDetailList(Expression<Func<PP_ProductionOrderDetail_View, bool>> condition = null)
        {
            return DbContext.Queryable<PP_ProductionOrderDetail_View>().Where(condition);
        }

        #endregion

        #region 废弃

        //    /// <summary>
        //    /// 从接口查询生产订单信息,根据创建时间和最后修改时间筛选
        //    /// </summary>
        //    /// <param name="productionOrderID"></param>
        //    /// <param name="fromCreatedTime"></param>
        //    /// <param name="fromLastChangeTime"></param>
        //    /// <param name="productionLine"></param>
        //    /// <param name="productionOrderStatus"></param>
        //    /// <returns></returns>
        //    public List<PP_ProductionOrder> GetViewList(string[] productionOrderStatus, string productionLine, string productionOrderID, DateTime? fromCreatedTime, DateTime? fromLastChangeTime)
        //    {
        //        List<PP_ProductionOrder> result = new List<PP_ProductionOrder>();

        //        //var rs = ProductionOrderService.GetProductionOrderOverviews(productionOrderStatus, productionLine, productionOrderID, fromCreatedTime, null, fromLastChangeTime, null);
        //        //if (rs == null)
        //        //{
        //        //    return null;
        //        //}
        //        //foreach (var item in rs)
        //        //{
        //        //    for (int i = 0; i < item.ProductionOrderMaterialOutput.Length; i++)
        //        //    {
        //        //        var entity = new PP_ProductionOrder();
        //        //        entity.BaseNum = item.ProductionOrderID;
        //        //        //entity.CUser = item.SystemAdministrativeData?.LastChangeIdentityUUID;
        //        //        entity.ItemCode = item.ProductionOrderMaterialOutput[i].ProductionOrderOutputProductID;
        //        //        entity.ItemName = item.ProductionOrderMaterialOutput[i].ProductionOrderOutputProductDesc.Value;
        //        //        //entity.ItmsGrpCode = item.ProductionOrderMaterialOutput[i].;
        //        //        //entity.ItmsGrpName = item.ProductionOrderID;
        //        //        entity.ProductionOrderFullfilledQuantity = item.ProductionOrderMaterialOutput[i].ProductionOrderFullfilledQuantity.Value;
        //        //        // + " " + BaseMeasureUnitHelper.Instance.GetDescriptionByCode(item.ProductionOrderMaterialOutput[i].ProductionOrderFullfilledQuantity.UnitCode);
        //        //        entity.ProductionOrderOpenQuantity = item.ProductionOrderMaterialOutput[i].ProductionOrderOpenQuantity.Value;
        //        //        // + " " + BaseMeasureUnitHelper.Instance.GetDescriptionByCode(item.ProductionOrderMaterialOutput[i].ProductionOrderOpenQuantity.UnitCode);
        //        //        entity.ProductionOrderPlannedQuantity = item.ProductionOrderMaterialOutput[i].ProductionOrderPlannedQuantity.Value;
        //        //        //+ " " + BaseMeasureUnitHelper.Instance.GetDescriptionByCode(item.ProductionOrderMaterialOutput[i].ProductionOrderPlannedQuantity.UnitCode);
        //        //        entity.ProductionOrderPlannedQuantityUnit = item.ProductionOrderMaterialOutput[i].ProductionOrderPlannedQuantity.UnitCode;

        //        //        entity.ProductionOrderPriority = item.ProductionOrderPriority;
        //        //        entity.ProductionOrderReleaseDate = item.ProductionOrderReleaseDate;
        //        //        entity.ProductionOrderSiteID = item.ProductionOrderMaterialOutput[i].ProductionOrderSiteID;
        //        //        entity.ProductionOrderStatus = item.ProductionOrderStatus;
        //        //        entity.RequestedEndDate = item.ProductionOrderRequestedEndDate;
        //        //        entity.MaterialOutputUUID = item.ProductionOrderMaterialOutput[i].MaterialOutputUUID;
        //        //        entity.ProductionOrderUUID = item.ProductionOrderUUID;
        //        //        entity.PlannedStartDate = item.ProductionOrderStartDate;
        //        //        entity.ProductionLine = item.ProductionLine;
        //        //        entity.CreatedDate = item.SystemAdministrativeData?.CreationDateTime;
        //        //        entity.ModifiedDate = item.SystemAdministrativeData?.LastChangeDateTime;
        //        //        entity.CreateUserID = item.SystemAdministrativeData?.CreationIdentityUUID;
        //        //        result.Add(entity);
        //        //    }
        //        //}
        //        return result;
        //    }


        //    /// <summary>
        //    /// 从接口查询生产订单信息,分页
        //    /// (重点)查到结果后,再按计划开始时间升序排列
        //    /// </summary>
        //    /// <param name="pageSize"></param>
        //    /// <param name="firstID"></param>
        //    /// <param name="productionOrderID"></param>
        //    /// <param name="keyword"></param>
        //    /// <param name="productionOrderStatus"></param>
        //    /// <param name="productionLine"></param>
        //    /// <returns></returns>
        //    public List<PP_ProductionOrder> GetViewPage(int pageSize, string firstID, string productionOrderStatus, string productionLine, string productionOrderID, string keyword = null)
        //    {
        //        List<PP_ProductionOrder> result = new List<PP_ProductionOrder>();

        //        //var sapProductionOrderList = ProductionOrderService.GetProductionOrderPage(pageSize, firstID, productionOrderStatus, productionLine, productionOrderID, keyword);
        //        //if (sapProductionOrderList == null)
        //        //{
        //        //    return null;
        //        //}

        //        //sapProductionOrderList.ForEach(x =>
        //        //{
        //        //    for (int i = 0; i < x.ProductionOrderMaterialOutput.Length; i++)
        //        //    {
        //        //        var itemView = new PP_ProductionOrder
        //        //        {
        //        //            RequestedEndDate = x.ProductionOrderRequestedEndDate,
        //        //            ProductionOrderSiteID = x.ProductionOrderMaterialOutput[i].ProductionOrderSiteID,
        //        //            ProductionOrderStatus = x.ProductionOrderStatus,
        //        //            ProductionOrderPriority = x.ProductionOrderPriority,
        //        //            BaseNum = x.ProductionOrderID,
        //        //            ItemCode = x.ProductionOrderMaterialOutput[i].ProductionOrderOutputProductID,
        //        //            ItemName = x.ProductionOrderMaterialOutput[i].ProductionOrderOutputProductDesc.Value,
        //        //            ProductionOrderOpenQuantity = x.ProductionOrderMaterialOutput[i].ProductionOrderOpenQuantity.Value,// + " " + BaseMeasureUnitHelper.Instance.GetDescriptionByCode(x.ProductionOrderMaterialOutput[i].ProductionOrderOpenQuantity.UnitCode),
        //        //            ProductionOrderReleaseDate = x.ProductionOrderReleaseDate,
        //        //            ProductionOrderPlannedQuantity = x.ProductionOrderMaterialOutput[i].ProductionOrderPlannedQuantity.Value,// + " " + BaseMeasureUnitHelper.Instance.GetDescriptionByCode(x.ProductionOrderMaterialOutput[i].ProductionOrderPlannedQuantity.UnitCode),
        //        //            ProductionOrderPlannedQuantityUnit = x.ProductionOrderMaterialOutput[i].ProductionOrderPlannedQuantity.UnitCode,
        //        //            ProductionOrderFullfilledQuantity = x.ProductionOrderMaterialOutput[i].ProductionOrderFullfilledQuantity.Value,// + " " + BaseMeasureUnitHelper.Instance.GetDescriptionByCode(x.ProductionOrderMaterialOutput[i].ProductionOrderFullfilledQuantity.UnitCode),
        //        //            CUser = x.SystemAdministrativeData?.CreationIdentityUUID,
        //        //            ProductionLine = x.ProductionLine,
        //        //            MaterialOutputUUID = x.ProductionOrderMaterialOutput[i].MaterialOutputUUID,
        //        //            ProductionOrderUUID = x.ProductionOrderUUID,
        //        //            PlannedStartDate = x.ProductionOrderStartDate,
        //        //        };

        //        //        result.Add(itemView);
        //        //    }
        //        //});

        //        return result?.OrderBy(x => x.PlannedStartDate)?.ToList();
        //    }

        //    /// <summary>
        //    /// 从接口查询生产订单详细信息
        //    /// </summary>
        //    /// <param name="productionOrderID">生产订单编号</param>
        //    /// <param name="allFromSAP">是否从接口获取基础数据</param>
        //    /// <param name="productionLine"></param>
        //    /// <returns></returns>
        //    public List<PP_ProductionOrderDetail> GetDetailListSAP(string productionOrderID, bool allFromSAP = false, string productionLine = null)
        //    {
        //        List<PP_ProductionOrderDetail> result = new List<PP_ProductionOrderDetail>();

        //        //var productionLots = ProductionLotISIService.GetProductionLot(productionOrderID);
        //        DateTime ctime = DateTime.Now;
        //        //foreach (var productionLot in productionLots)
        //        //{
        //        //    //获取用料
        //        //    var groups = productionLot.ConfirmationGroup.Where(x => x.ProductionTask.Where(y => y.OperationTypeCode == "1").Count() > 0).ToList();
        //        //    foreach (var grp in groups)
        //        //    {
        //        //        foreach (var materialInput in grp.MaterialInput)
        //        //        {
        //        //            PP_ProductionOrderDetail PP_ProductionOrderDetail = new PP_ProductionOrderDetail();
        //        //            PP_ProductionOrderDetail.BaseNum = productionOrderID;
        //        //            PP_ProductionOrderDetail.CTime = ctime;
        //        //            PP_ProductionOrderDetail.CUser = "SYS";
        //        //            PP_ProductionOrderDetail.DetailID = Guid.NewGuid().ToString();

        //        //            PP_ProductionOrderDetail.IsDelete = false;
        //        //            PP_ProductionOrderDetail.ItemCode = materialInput.ProductID;
        //        //            PP_ProductionOrderDetail.LineItemGroupID = materialInput.LineItemGroupID;
        //        //            PP_ProductionOrderDetail.ProductionLine = productionLine;

        //        //            //已领料数量
        //        //            PP_ProductionOrderDetail.FulfilledQuantity = materialInput.TotalConfirmedQuantity.Value;
        //        //            PP_ProductionOrderDetail.FulfilledQuantityUnit = materialInput.TotalConfirmedQuantity.UnitCode;

        //        //            //未领料数量
        //        //            var openQty = materialInput.PlannedQuantity.Value - materialInput.TotalConfirmedQuantity.Value;
        //        //            PP_ProductionOrderDetail.OpenQuantity = openQty > 0 ? openQty : 0;
        //        //            PP_ProductionOrderDetail.OpenQuantityUnit = materialInput.PlannedQuantity.UnitCode;

        //        //            //计划数量(转换后)
        //        //            string stockUnitCode = new MD_ItemApp().GetList(x => x.ItemCode == materialInput.ProductID).ToList().FirstOrDefault()?.Unit;
        //        //            //生产BOM单位转成库存单位
        //        //            decimal conversionRate = new MD_UnitCodeConversionApp().GetConvertRate(materialInput.PlannedQuantity.UnitCode, stockUnitCode);

        //        //            PP_ProductionOrderDetail.PlanQuantity = materialInput.PlannedQuantity.Value * conversionRate;
        //        //            PP_ProductionOrderDetail.PlanQuantityUnit = stockUnitCode;

        //        //            PP_ProductionOrderDetail.ConversionRate = conversionRate;


        //        //            ////计划数量(未转换)
        //        //            PP_ProductionOrderDetail.OrderDetailQty = materialInput.PlannedQuantity.Value;
        //        //            PP_ProductionOrderDetail.OrderDetailUnit = materialInput.PlannedQuantity.UnitCode;

        //        //            //确认方法代码
        //        //            PP_ProductionOrderDetail.ConfirmationMethodCode = materialInput.ConfirmationMethodCode;

        //        //            //生产任务ID
        //        //            PP_ProductionOrderDetail.ProductTaskID = groups.ToList().FirstOrDefault()?.ProductionTask.ToList().FirstOrDefault()?.ProductionTaskID;

        //        //            result.Add(PP_ProductionOrderDetail);
        //        //        }
        //        //    }
        //        //}

        //        var materialIDs = result.Select(x => x.ItemCode)?.Distinct()?.ToArray();
        //        MD_ItemApp MD_ItemApp = new MD_ItemApp();
        //        var mdItemList = MD_ItemApp.GetAllList(x => materialIDs.Contains(x.ItemCode) && !x.IsDelete);

        //        foreach (var input in result)
        //        {
        //            var item = mdItemList.Where(x => x.ItemCode == input.ItemCode).ToList().FirstOrDefault();
        //            input.ItemName = item?.ItemName;
        //            //detail.ItmsGrpCode = item.ItmsGrpCode;
        //            //detail.ItmsGrpName = item.ItmsGrpName;
        //            if (item?.StockingQty > 0)
        //            {
        //                input.StockingQty = Math.Min(input.OpenQuantity, item.StockingQty.Value);
        //            }
        //            else
        //            {
        //                input.StockingQty = input.OpenQuantity;
        //            }
        //        }

        //        if (allFromSAP)
        //        {//从SAP接口补充产品类别信息
        //            //var materials = MaterialService.GetMaterials(string.Empty, materialIDs);
        //            //var categoryIDs = materials?.Select(x => x.ProductCategoryID)?.Distinct()?.ToArray();
        //            //var categories = ProductCategoryService.GetProductCategoriesByID(categoryIDs);

        //            //result.ForEach(x =>
        //            //{
        //            //    var material = materials.Where(y => y.InternalID == x.ItemCode).ToList().FirstOrDefault();
        //            //    if (material != null)
        //            //    {
        //            //        x.ItmsGrpCode = material.ProductCategoryID;
        //            //        var category = categories.Where(y => y.InternalID == x.ItmsGrpCode).ToList().FirstOrDefault();
        //            //        if (category != null)
        //            //        {
        //            //            x.ItmsGrpName = ((Dictionary<string, string>)category.Description).ToList().FirstOrDefault().Value + "";
        //            //        }
        //            //    }
        //            //});
        //        }
        //        else
        //        {//从wms本地获取产品类别信息
        //            var materials = MD_ItemApp.GetAllList(x => materialIDs.Contains(x.ItemCode) && !x.IsDelete);
        //            result.ForEach(x =>
        //            {
        //                var material = materials.Where(y => y.ItemCode == x.ItemCode).ToList().FirstOrDefault();
        //                if (material != null)
        //                {
        //                    x.ItmsGrpCode = material.ItmsGrpCode;
        //                    x.ItmsGrpName = material.ItmsGrpName;
        //                }
        //            });

        //        }

        //        return result;
        //    }


        //    /// <summary>
        //    /// 获取生产订单状态列表JSON
        //    /// </summary>
        //    /// <returns></returns>
        //    public List<object> GetStatusList()
        //    {
        //        return ProductionOrderStatusHelper.Instance.GetList();
        //    }

        //    /// <summary>
        //    /// 生产订单数据采集
        //    /// </summary>
        //    /// <param name="productionLine"></param>
        //    /// <param name="productionOrderID"></param>
        //    /// <param name="productionOrderStatus"></param>
        //    /// <param name="createdDateFrom"></param>
        //    /// <param name="createdDateTo"></param>
        //    /// <param name="modifiedDateFrom"></param>
        //    /// <param name="modifiedDateTo"></param>
        //    /// <returns></returns>
        //    public Task GrabData(string productionLine = null, string productionOrderID = null, string productionOrderStatus = null, DateTime? createdDateFrom = null, DateTime? createdDateTo = null, DateTime? modifiedDateFrom = null, DateTime? modifiedDateTo = null)
        //    {

        //        int iBeforeMinute = int.Parse(System.Configuration.ConfigurationManager.AppSettings["CrawOrderJobTimeBefore"]);

        //        modifiedDateTo = DateTime.Now;
        //        modifiedDateFrom = modifiedDateTo.Value.AddMinutes(-1 * iBeforeMinute);

        //        return Task.Run(() =>
        //        {
        //            LogHelper.Instance.LogDebug("生产订单数据抓取开始...");

        //            int result = 0;
        //            string[] productionOrderStatuses = null;
        //            if (!string.IsNullOrEmpty(productionOrderStatus))
        //            { productionOrderStatuses = new string[] { productionOrderStatus }; }

        //            //获取所有生产线
        //            Sys_DictionaryApp Sys_DictionaryApp = new Sys_DictionaryApp();
        //            PP_ProductionOrderApp PP_ProductionOrderApp = new PP_ProductionOrderApp();
        //            var productionLines = Sys_DictionaryApp.GetAllList(x => x.TypeCode == "PP005" && !x.IsDelete)?.Select(x => x.EnumValue)?.ToList()?.Distinct();

        //            if (productionLines != null)
        //            {
        //                //遍历生产线
        //                foreach (var line in productionLines)
        //                {
        //                    if (!string.IsNullOrEmpty(productionLine) && productionLine != line)
        //                    {
        //                        continue;
        //                    }

        //                    try
        //                    {
        //                        //todo: 获取上次抓取时间
        //                        //DateTime lastGrabTime = DateTime.Now;

        //                        //抓取生产线的所有订单
        //                        var productionOrders1 = PP_ProductionOrderApp.GetListSAP(productionOrderStatuses, new string[] { line }, null, createdDateFrom, createdDateTo, modifiedDateFrom, modifiedDateTo);

        //                        List<PP_ProductionOrder> productionOrders = new List<PP_ProductionOrder>();
        //                        if (productionOrders1?.Count > 0)
        //                        {
        //                            productionOrders.AddRange(productionOrders1);
        //                        }

        //                        if (productionOrders?.Count > 0)
        //                        {
        //                            foreach (var productionOrder in productionOrders1)
        //                            {//防止数据量过大,所以每次生成1个订单的数据并保存    

        //                                if (!string.IsNullOrEmpty(productionOrderID) && productionOrder.BaseNum != productionOrderID)
        //                                {
        //                                    continue;
        //                                }

        //                                LogHelper.Instance.LogDebug("开始抓取生产线[" + productionOrder.ProductionLine + "]生产订单[" + productionOrder.BaseNum + "]的订单概况...");

        //                                bool handlingDetailFlag = false;
        //                                var orderTmp = DbContext.Queryable<PP_ProductionOrder>().Where(x => x.BaseNum == productionOrder.BaseNum).ToList().FirstOrDefault();
        //                                //抓取详情
        //                                var orderDetailList = PP_ProductionOrderApp.GetDetailListSAP(productionOrder.BaseNum, false, productionOrder.ProductionLine);
        //                                string _ProductionTaskID = orderDetailList.ToList().FirstOrDefault()?.ProductTaskID;

        //                                if (orderTmp != null)
        //                                {//订单存在

        //                                    //如果订单的Sap最后更新时间不变，则不更新
        //                                    if (orderTmp?.ModifiedDate == productionOrder.ModifiedDate)
        //                                    {
        //                                        continue;
        //                                    }
        //                                    //更新订单
        //                                    handlingDetailFlag = DbContext.Update<PP_ProductionOrder>(x => x.BaseNum == productionOrder.BaseNum, x => new PP_ProductionOrder()
        //                                    {
        //                                        CreatedDate = productionOrder.CreatedDate,
        //                                        CreateUserID = productionOrder.CreateUserID,
        //                                        ItemCode = productionOrder.ItemCode,
        //                                        ItemName = productionOrder.ItemName,
        //                                        ItmsGrpCode = productionOrder.ItmsGrpCode,
        //                                        ItmsGrpName = productionOrder.ItmsGrpName,
        //                                        MaterialOutputUUID = productionOrder.MaterialOutputUUID,
        //                                        ModifiedDate = productionOrder.ModifiedDate,
        //                                        PlannedStartDate = productionOrder.PlannedStartDate,
        //                                        ProductionLine = productionOrder.ProductionLine,
        //                                        ProductionOrderFullfilledQuantity = productionOrder.ProductionOrderFullfilledQuantity,
        //                                        ProductionOrderOpenQuantity = productionOrder.ProductionOrderOpenQuantity,
        //                                        ProductionOrderPlannedQuantity = productionOrder.ProductionOrderPlannedQuantity,
        //                                        ProductionOrderPlannedQuantityUnit = productionOrder.ProductionOrderPlannedQuantityUnit,
        //                                        ProductionOrderPriority = productionOrder.ProductionOrderPriority,
        //                                        ProductionOrderReleaseDate = productionOrder.ProductionOrderReleaseDate,
        //                                        ProductionOrderSiteID = productionOrder.ProductionOrderSiteID,
        //                                        ProductionOrderStatus = productionOrder.ProductionOrderStatus,
        //                                        ProductionOrderUUID = productionOrder.ProductionOrderUUID,
        //                                        ProductionTaskID = _ProductionTaskID,
        //                                        RequestedEndDate = productionOrder.RequestedEndDate
        //                                    }) > 0;
        //                                }
        //                                else
        //                                {
        //                                    //插入
        //                                    productionOrder.ProductionTaskID = _ProductionTaskID;
        //                                    handlingDetailFlag = DbContext.Insert<PP_ProductionOrder>(productionOrder) != null;
        //                                }

        //                                //订单详情
        //                                if (handlingDetailFlag)
        //                                {
        //                                    LogHelper.Instance.LogDebug("开始抓取生产线[" + productionOrder.ProductionLine + "]生产订单[" + productionOrder.BaseNum + "]的详情...");
        //                                    try
        //                                    {
        //                                        DbContext.UseTransaction(() =>
        //                                        {
        //                                            //删除原有
        //                                            DbContext.Delete<PP_ProductionOrderDetail>(x => x.BaseNum == productionOrder.BaseNum);


        //                                            orderDetailList.ForEach(x =>
        //                                            {
        //                                                x.ProductionLine = productionOrder.ProductionLine;
        //                                            });

        //                                            //插入
        //                                            if (orderDetailList?.Count > 0)
        //                                            {
        //                                                DbContext.InsertRange<PP_ProductionOrderDetail>(orderDetailList);
        //                                            }
        //                                        });
        //                                    }
        //                                    catch (System.Exception ex)
        //                                    {
        //                                        if (DbContext.Ado.IsAnyTran())
        //                                            DbContext.Ado.RollbackTran();
        //                                    }
        //                                    LogHelper.Instance.LogDebug("完成抓取生产线[" + productionOrder.ProductionLine + "]生产订单[" + productionOrder.BaseNum + "]的详情...");
        //                                }
        //                                else
        //                                {
        //                                    LogHelper.Instance.LogDebug("抓取生产线[" + productionOrder.ProductionLine + "]生产订单[" + productionOrder.BaseNum + "]的详情时由于新增或者更新WMS中的订单概况信息失败而未能成功...");
        //                                }
        //                            }
        //                        }
        //                    }
        //                    catch (System.Exception ex)
        //                    {
        //                        LogHelper.Instance.LogError("生产线:[" + line + "]的订单数据抓取失败." + ex.Message);
        //                        continue;
        //                    }
        //                }
        //            }
        //            LogHelper.Instance.LogDebug("生产订单数据抓取完成...");
        //            return result;
        //        });
        //    }

        //    /// <summary>
        //    /// 获取生产订单详情
        //    /// </summary>
        //    /// <returns></returns>
        //    public ISugarQueryable<PP_ProductionOrderDetail> GetDetailList()
        //    {
        //        return DbContext.Queryable<PP_ProductionOrderDetail>();
        //    }

        //    /// <summary>
        //    /// 从接口查询生产订单信息,根据创建时间和最后修改时间筛选
        //    /// </summary>
        //    /// <param name="productionOrderIDs"></param>
        //    /// <param name="createdTimeFrom"></param>
        //    /// <param name="createdTimeTo"></param>
        //    /// <param name="modifiedDateFrom"></param>
        //    /// <param name="modifiedDateTo"></param>
        //    /// <param name="productionLines"></param>
        //    /// <param name="productionOrderStatus"></param>
        //    /// <returns></returns>
        //    public List<PP_ProductionOrder> GetListSAP(string[] productionOrderStatus, string[] productionLines, string[] productionOrderIDs, DateTime? createdTimeFrom = null, DateTime? createdTimeTo = null, DateTime? modifiedDateFrom = null, DateTime? modifiedDateTo = null)
        //    {
        //        List<PP_ProductionOrder> result = new List<PP_ProductionOrder>();

        //        //var rs = ProductionOrderService.GetProductionOrderOverviews(productionOrderStatus, productionLines, productionOrderIDs, createdTimeFrom, createdTimeTo, modifiedDateFrom, modifiedDateTo);
        //        //if (rs == null)
        //        //{
        //        //    return null;
        //        //}
        //        //if (rs.Count > 0)
        //        //{
        //        //    foreach (var item in rs)
        //        //    {

        //        //        for (int i = 0; i < item.ProductionOrderMaterialOutput.Length; i++)
        //        //        {
        //        //            if (item.ProductionOrderMaterialOutput[i].ProductionOrderSiteID == "SZ1")
        //        //            {
        //        //                var entity = new PP_ProductionOrder();
        //        //                entity.BaseNum = item.ProductionOrderID;
        //        //                //entity.CUser = item.SystemAdministrativeData?.LastChangeIdentityUUID;
        //        //                entity.ItemCode = item.ProductionOrderMaterialOutput[i].ProductionOrderOutputProductID;
        //        //                entity.ItemName = item.ProductionOrderMaterialOutput[i].ProductionOrderOutputProductDesc.Value;
        //        //                //entity.ItmsGrpCode = item.ProductionOrderMaterialOutput[i].;
        //        //                //entity.ItmsGrpName = item.ProductionOrderID;
        //        //                entity.ProductionOrderFullfilledQuantity = item.ProductionOrderMaterialOutput[i].ProductionOrderFullfilledQuantity.Value;
        //        //                // + " " + BaseMeasureUnitHelper.Instance.GetDescriptionByCode(item.ProductionOrderMaterialOutput[i].ProductionOrderFullfilledQuantity.UnitCode);
        //        //                entity.ProductionOrderOpenQuantity = item.ProductionOrderMaterialOutput[i].ProductionOrderOpenQuantity.Value;
        //        //                // + " " + BaseMeasureUnitHelper.Instance.GetDescriptionByCode(item.ProductionOrderMaterialOutput[i].ProductionOrderOpenQuantity.UnitCode);
        //        //                entity.ProductionOrderPlannedQuantity = item.ProductionOrderMaterialOutput[i].ProductionOrderPlannedQuantity.Value;
        //        //                //+ " " + BaseMeasureUnitHelper.Instance.GetDescriptionByCode(item.ProductionOrderMaterialOutput[i].ProductionOrderPlannedQuantity.UnitCode);
        //        //                entity.ProductionOrderPlannedQuantityUnit = item.ProductionOrderMaterialOutput[i].ProductionOrderPlannedQuantity.UnitCode;

        //        //                entity.ProductionOrderPriority = item.ProductionOrderPriority;
        //        //                entity.ProductionOrderReleaseDate = item.ProductionOrderReleaseDate;
        //        //                entity.ProductionOrderSiteID = item.ProductionOrderMaterialOutput[i].ProductionOrderSiteID;
        //        //                entity.ProductionOrderStatus = item.ProductionOrderStatus;
        //        //                entity.RequestedEndDate = item.ProductionOrderRequestedEndDate;
        //        //                entity.MaterialOutputUUID = item.ProductionOrderMaterialOutput[i].MaterialOutputUUID;
        //        //                entity.ProductionOrderUUID = item.ProductionOrderUUID;
        //        //                entity.PlannedStartDate = item.ProductionOrderStartDate;
        //        //                entity.ProductionLine = item.ProductionLine;
        //        //                entity.CreatedDate = item.SystemAdministrativeData?.CreationDateTime;
        //        //                entity.ModifiedDate = item.SystemAdministrativeData?.LastChangeDateTime;
        //        //                entity.CreateUserID = item.SystemAdministrativeData?.CreationIdentityUUID;
        //        //                result.Add(entity);

        //        //            }
        //        //        }
        //        //    }

        //        //    if (result?.Count > 0)
        //        //    {//补充产品类别信息
        //        //        var itemCodes = result.Select(x => x.ItemCode).ToArray();
        //        //        MD_ItemApp MD_ItemApp = new MD_ItemApp();
        //        //        var mdItems = MD_ItemApp.GetAllList(x => !x.IsDelete && itemCodes.Contains(x.ItemCode));
        //        //        result.ForEach(x =>
        //        //        {
        //        //            var mdItem = mdItems.Where(y => y.ItemCode == x.ItemCode).ToList().FirstOrDefault();
        //        //            x.ItmsGrpCode = mdItem?.ItmsGrpCode;
        //        //            x.ItmsGrpName = mdItem?.ItmsGrpName;
        //        //        });
        //        //    }
        //        //}
        //        return result;
        //    }

        //    /// <summary>
        //    /// 抓取数据到本地
        //    /// </summary>
        //    /// <param name="productionOrderStatus"></param>
        //    /// <param name="productionLines"></param>
        //    /// <param name="productionOrderIDs"></param>
        //    /// <param name="createdTimeFrom"></param>
        //    /// <param name="createdTimeTo"></param>
        //    /// <param name="modifiedDateFrom"></param>
        //    /// <param name="modifiedDateTo"></param>
        //    /// <param name="user"></param>
        //    /// <returns></returns>
        //    public Task GrabData(string[] productionOrderStatus, string[] productionLines, string[] productionOrderIDs, DateTime? createdTimeFrom = null, DateTime? createdTimeTo = null, DateTime? modifiedDateFrom = null, DateTime? modifiedDateTo = null, string user = null)
        //    {
        //        return Task.Run((Action)(() =>
        //        {
        //            var sapList = this.GetListSAP(productionOrderStatus, productionLines, productionOrderIDs, createdTimeFrom, createdTimeTo, modifiedDateFrom, modifiedDateTo);

        //            if (sapList?.Count > 0)
        //                if (productionOrderIDs?.Length > 0)
        //                {
        //                    productionOrderIDs = productionOrderIDs.Distinct().ToArray();
        //                }
        //                else
        //                {
        //                    productionOrderIDs = sapList.Select(x => x.BaseNum).Distinct().ToArray();
        //                }

        //            #region 订单概况
        //            //查找本地保存的生产订单信息
        //            var wmsList = this.GetAllList(x => productionOrderIDs.Contains(x.BaseNum) && !x.IsDelete)?.ToList();
        //            foreach (var sapItem in sapList)
        //            {
        //                try
        //                {
        //                    DbContext.UseTransaction((Action)(() =>
        //                    {
        //                        #region 订单概况
        //                        var wmsItem = wmsList?.Where(x => x.BaseNum == sapItem.BaseNum)?.ToList().FirstOrDefault();
        //                        var sapDetailItems = this.GetDetailListSAP(sapItem.BaseNum, false, sapItem.ProductionLine);
        //                        //本地存在相同订单号的单子更新，不存在的新增
        //                        if (wmsItem != null)
        //                        {

        //                            wmsItem.ItemCode = sapItem.ItemCode;
        //                            wmsItem.ItemName = sapItem.ItemName;
        //                            wmsItem.ItmsGrpCode = sapItem.ItmsGrpCode;
        //                            wmsItem.ItmsGrpName = sapItem.ItmsGrpName;
        //                            wmsItem.MaterialOutputUUID = sapItem.MaterialOutputUUID;
        //                            wmsItem.ModifiedDate = sapItem.ModifiedDate;
        //                            wmsItem.PlannedStartDate = sapItem.PlannedStartDate;
        //                            wmsItem.ProductionLine = sapItem.ProductionLine;
        //                            wmsItem.ProductionOrderFullfilledQuantity = sapItem.ProductionOrderFullfilledQuantity;
        //                            wmsItem.ProductionOrderOpenQuantity = sapItem.ProductionOrderPlannedQuantity;
        //                            wmsItem.ProductionOrderPlannedQuantityUnit = sapItem.ProductionOrderPlannedQuantityUnit;
        //                            wmsItem.ProductionOrderPriority = sapItem.ProductionOrderPriority;
        //                            wmsItem.ProductionOrderReleaseDate = sapItem.ProductionOrderReleaseDate;
        //                            wmsItem.ProductionOrderSiteID = sapItem.ProductionOrderSiteID;
        //                            wmsItem.ProductionOrderStatus = sapItem.ProductionOrderStatus;
        //                            wmsItem.ProductionOrderUUID = sapItem.ProductionOrderUUID;
        //                            wmsItem.RequestedEndDate = sapItem.RequestedEndDate;
        //                            wmsItem.MTime = DateTime.Now;
        //                            wmsItem.MUser = user;
        //                            wmsItem.ProductionTaskID = sapDetailItems.ToList().FirstOrDefault()?.ProductTaskID;
        //                            this.Update(wmsItem);
        //                        }
        //                        else
        //                        {
        //                            sapItem.ID = Guid.NewGuid().ToString();
        //                            sapItem.CTime = DateTime.Now;
        //                            sapItem.CUser = user;
        //                            sapItem.ProductionTaskID = sapDetailItems.ToList().FirstOrDefault()?.ProductTaskID;
        //                            this.Insert(sapItem);
        //                        }
        //                        #endregion

        //                        #region 订单详情
        //                        //var sapDetailItems = this.GetDetailListSAP(sapItem.BaseNum, false, sapItem.ProductionLine);
        //                        string[] bomItemCodeList = sapDetailItems.Select(x => x.ItemCode).ToArray();
        //                        List<MD_Item> bomItemList = new MD_ItemApp().GetList(x => bomItemCodeList.Contains(x.ItemCode)).ToList();
        //                        if (sapDetailItems?.Count > 0)
        //                        {
        //                            //更新详情的策略为，物理删除订单已有详情记录，重新插入
        //                            DbContext.Delete<PP_ProductionOrderDetail>(x => x.BaseNum == sapItem.BaseNum);
        //                            List<PP_ProductionOrderDetail> detailList = new List<PP_ProductionOrderDetail>();
        //                            foreach (var item in sapDetailItems)
        //                            {
        //                                PP_ProductionOrderDetail detail = new PP_ProductionOrderDetail();
        //                                string itemStockUnit = bomItemList.Where(x => x.ItemCode == item.ItemCode).ToList().FirstOrDefault()?.Unit;

        //                                detail.BaseNum = item.BaseNum;
        //                                detail.CTime = DateTime.Now;
        //                                detail.CUser = user;
        //                                detail.DetailID = Guid.NewGuid().ToString();
        //                                detail.FulfilledQuantity = item.FulfilledQuantity;
        //                                detail.FulfilledQuantityUnit = item.FulfilledQuantityUnit;
        //                                detail.IsDelete = false;
        //                                detail.ItemCode = item.ItemCode;
        //                                detail.ItemName = item.ItemName;
        //                                detail.ItmsGrpCode = item.ItmsGrpCode;
        //                                detail.ItmsGrpName = item.ItmsGrpName;
        //                                detail.OpenQuantity = item.OpenQuantity;
        //                                detail.OpenQuantityUnit = item.OpenQuantityUnit;
        //                                detail.PlanQuantity = item.PlanQuantity;
        //                                detail.PlanQuantityUnit = item.PlanQuantityUnit;
        //                                detail.ProductionLine = item.ProductionLine;
        //                                detail.StockingQty = item.StockingQty;
        //                                detail.LineItemGroupID = item.LineItemGroupID;
        //                                detail.ConfirmationMethodCode = item.ConfirmationMethodCode;
        //                                detail.ConversionRate = new MD_UnitCodeConversionApp().GetConvertRate(item.PlanQuantityUnit, itemStockUnit);

        //                                detailList.Add(detail);
        //                            }

        //                            if (detailList?.Count > 0)
        //                                DbContext.InsertRange<PP_ProductionOrderDetail>(detailList);
        //                        }

        //                        #endregion
        //                    }));
        //                }
        //                catch (System.Exception ex)
        //                {
        //                    if (DbContext.Ado.IsAnyTran())
        //                    {
        //                        DbContext.Ado.RollbackTran();
        //                    }
        //                    //throw ex;
        //                    //允许个别生产订单同步失败，不打断其他
        //                    continue;
        //                }
        //            }
        //            #endregion

        //        }));
        //    }
        //    #region 查询导出数据(视图)
        //    public List<PP_ProductionOrder_View> GetExectData(DateTime fromTime, DateTime toTime, string productionOrderID, string productionOrderStatus, string productionLine)
        //    {
        //        return this.DbContext.Queryable<PP_ProductionOrder_View>(x =>
        //               (string.IsNullOrEmpty(productionOrderID) || x.BaseNum == productionOrderID) &&
        //               (string.IsNullOrEmpty(productionOrderStatus) || x.ProductionOrderStatus == productionOrderStatus) &&
        //               (string.IsNullOrEmpty(productionLine) || x.ProductionLine == productionLine) &&
        //               x.CreatedDate >= fromTime && x.CreatedDate < toTime
        //               ).ToList();
        //    }
        //    #endregion

        //    #region 查询生产订单
        //    public List<PP_ProductionOrder> GetProductionOrderList(string productionOrderStatus, string productionLine, string productionOrderID)
        //    {
        //        PP_InScanApp _inApp = new PP_InScanApp();
        //        PP_BarCodeApp _barCodeApp = new PP_BarCodeApp();
        //        var productions = this.GetAllList(x =>
        //                 (string.IsNullOrEmpty(productionOrderStatus) || x.ProductionOrderStatus == productionOrderStatus) &&
        //                 (string.IsNullOrEmpty(productionLine) || x.ProductionLine == productionLine) &&
        //                 (string.IsNullOrEmpty(productionOrderID) || x.BaseNum == productionOrderID) &&
        //             !x.IsDelete)?.OrderByDesc(x => x.CreatedDate)?.ToList();
        //        var sumbList = _inApp.GetList(x => (string.IsNullOrEmpty(productionOrderID) || x.BaseNum == productionOrderID) && x.IsPosted == true)?.ToList()
        //            .GroupBy(x => new { x.BaseNum, x.Qty }).Select(x => new PP_InScan { BaseNum = x.Key.BaseNum, Qty = x.Sum(y => y.Qty) }).ToList();
        //        var finnallyList = _barCodeApp.GetList(x => (string.IsNullOrEmpty(productionOrderID) || x.BaseNum == productionOrderID))?.ToList()
        //            .GroupBy(x => new { x.BaseNum, x.Qty }).Select(x => new PP_BarCode { BaseNum = x.Key.BaseNum, Qty = x.Sum(y => y.Qty) }).ToList();
        //        productions.ForEach(production =>
        //        {
        //            var sumb = sumbList.Where(x => x.BaseNum == production.BaseNum)?.ToList().FirstOrDefault();
        //            var finnally = finnallyList.Where(x => x.BaseNum == production.BaseNum)?.ToList().FirstOrDefault();
        //            production.SubmittedQuantity = sumb == null ? 0 : sumb.Qty;
        //            production.ProductionOrderFullfilledQuantity = finnally == null ? 0 : finnally.Qty.Value;
        //        });
        //        return productions.Where(x => x.SubmittedQuantity == x.ProductionOrderFullfilledQuantity && x.SubmittedQuantity != 0 && x.ProductionOrderFullfilledQuantity != 0).Distinct().ToList();
        //    }
        //    #endregion

        #endregion

        #region 根据序号获取有效订单信息

        /// <summary>
        /// 获取启用序列号的生产订单
        /// </summary>
        /// <returns></returns>
        public List<PP_ProductionOrder> GetSerialNoEntity(string serialNo)
        {
            return DbContext.Ado.SqlQuery<PP_ProductionOrder>(@"select a.* from PP_ProductionOrder a
                    left join [XZ_SAP].[dbo].[XZ_SAP_AFKO] b on a.ProductionOrderNo=b.AUFNR and b.Status=0 and b.XLOEK <> 'X' 
                     where a.IsDelete=0 and a.SerialNo='" + serialNo + "' and isnull(b.AUFNR,'')<>'' ").ToList();
        }

        #endregion
    }
}
