using System;

namespace HZ.WMS.Entity.Produce.Req
{
    /// <summary>
    /// 生产报工扫描请求
    /// </summary>
    public class Produce_ReportScanReq
    {
        /// <summary>
        /// 生产订单号
        /// </summary>
        public string ProduceOrderNo { get; set; }

        /// <summary>
        /// 员工编号
        /// </summary>
        public string EmployeeNumber { get; set; }

        /// <summary>
        /// 员工名称
        /// </summary>
        public string EmployeeName { get; set; }

        /// <summary>
        /// 工序号
        /// </summary>
        public int? ProcessNo { get; set; }

        /// <summary>
        /// 报工数量
        /// </summary>
        public decimal? ReportQty { get; set; }

        /// <summary>
        /// 合格数量
        /// </summary>
        public decimal? QualifiedQty { get; set; }

        /// <summary>
        /// 不合格数量
        /// </summary>
        public decimal? UnqualifiedQty { get; set; }

        /// <summary>
        /// 不合格备注
        /// </summary>
        public string UnqualifiedRemarks { get; set; }
    }
} 