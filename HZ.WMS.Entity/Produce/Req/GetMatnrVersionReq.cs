using System.ComponentModel;

namespace AOS.OMS.Entity.Sale
{
    public class GetMatnrVersionReq
    {
        /// <summary>
        /// 工厂
        /// </summary>  
        [Description("工厂")]
        public string WERKS { get; set; }

        /// <summary>
        /// 物料编码
        /// </summary>  
        [Description("物料编码")]
        public string MATNR { get; set; }

        /// <summary>
        /// 生产版本
        /// </summary>  
        [Description("生产版本 0001")]
        public string VERID { get; set; }

        /// <summary>
        /// 查询日期
        /// </summary>  
        [Description("查询日期 8位")]
        public string ADATU { get; set; }
            
    }
}