using SqlSugar;
using HZ.WMS.Entity.MM;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Application.MM
{
    /// <summary>
    /// 物料标签扫描
    /// </summary>
    public class MM_BarCodeScanApp : BaseApp<MM_BarCodeScan>
    {
        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public MM_BarCodeScanApp() : base(){ }

        MM_BarCodeApp _barApp = new MM_BarCodeApp();

        #endregion

        #region 查询物料标签信息

        /// <summary>
        /// 校验
        /// </summary>
        /// <param name="BarCode">条码号</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool CheckDate(string BarCode, out string error_message)
        {
            error_message = "";
            var query = _barApp.GetList(x => x.BarCode == BarCode).ToList().FirstOrDefault();
            if (query == null)
            {
                error_message = "条码号[" + BarCode + "]无效，请检查数据！";
                return false;
            }
            return true;
        }

        /// <summary>
        /// 查询物料标签信息
        /// </summary>
        /// <param name="BarCode">条码号</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public MM_BarCode GetBarCodeInfo(string BarCode, out string error_message)
        {
            error_message = "";
            var query = _barApp.GetList(x => x.BarCode == BarCode).ToList().FirstOrDefault();
            if (query == null)
            {
                error_message = "条码号[" + BarCode + "]无效，请检查数据！";
            }
            return query;
        }

        #endregion

        #region 保存

        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="list"></param>
        /// <param name="user">用户信息</param>
        /// <param name="error_message">错误消息返回</param>
        public bool Save(List<MM_BarCodeScan> list, string user, out string error_message)
        {
            error_message = "";
            try
            {
                list.ForEach(x =>
                {
                    x.IsDelete = false;
                    x.CUser = user;
                    x.CTime = DateTime.Now;
                });

                DbContext.Ado.BeginTran();

                //批量插入
                this.Insert(list);

                DbContext.Ado.CommitTran();

                return true;
            }
            catch (Exception ex)
            {
                if (DbContext.Ado.IsAnyTran())
                    DbContext.Ado.RollbackTran();

                error_message = ex.Message;
                return false;
            }
        }

        #endregion
    }
}
