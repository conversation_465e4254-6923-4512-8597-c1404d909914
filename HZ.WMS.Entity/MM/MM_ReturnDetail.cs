using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.MM
{
    /// <summary>
    /// 委外领料申请明细表
    /// </summary>
    [SugarTable("MM_ReturnDetail")]
    public class MM_ReturnDetail : BaseEntity
    {
        /// <summary> 
        /// 明细ID
        /// </summary> 
        [SugarColumn(IsPrimaryKey = true)]
        [Description("明细ID")]
        public string OutsourcingReturnDetailID { get; set; }

        /// <summary> 
        /// wms单号
        /// </summary> 
        [Description("退货单号")]
        public string DocNum { get; set; }

        /// <summary> 
        /// wms行号
        /// </summary> 
        [Description("退货行号")]
        public int? Line { get; set; }

        /// <summary> 
        /// 批次
        /// </summary> 
        [Description("批次")]
        public string BatchNum { get; set; }

        /// <summary> 
        /// 物料编号
        /// </summary> 
        [Description("物料编号")]
        public string ItemCode { get; set; }

        /// <summary> 
        /// 物料名称
        /// </summary> 
        [Description("物料名称")]
        public string ItemName { get; set; }

        /// <summary> 
        /// 物料组编号
        /// </summary> 
        [Description("物料组编号")]
        public string ItmsGrpCode { get; set; }

        /// <summary> 
        /// 物料组名称
        /// </summary> 
        [Description("物料组名称")]
        public string ItmsGrpName { get; set; }

        /// <summary> 
        /// 供应商编号
        /// </summary> 
        [Description("供应商编号")]
        public string SupplierCode { get; set; }

        /// <summary> 
        /// 供应商名称
        /// </summary> 
        [Description("供应商名称")]
        public string SupplierName { get; set; }

        /// <summary> 
        /// 数量
        /// </summary> 
        [Description("数量")]
        public decimal? OutsourcingReturnQty { get; set; }

        /// <summary> 
        /// 单位
        /// </summary> 
        [Description("单位")]
        public string Unit { get; set; }

        /// <summary> 
        /// 仓库编号
        /// </summary> 
        [Description("仓库编号")]
        public string WhsCode { get; set; }

        /// <summary> 
        /// 仓库名称
        /// </summary> 
        [Description("仓库")]
        public string WhsName { get; set; }

        /// <summary> 
        /// 区域编号
        /// </summary> 
        [Description("区域编号")]
        public string RegionCode { get; set; }

        /// <summary> 
        /// 区域名称
        /// </summary> 
        [Description("区域")]
        public string RegionName { get; set; }

        /// <summary> 
        /// 库位编号
        /// </summary> 
        [Description("库位编号")]
        public string BinLocationCode { get; set; }

        /// <summary> 
        /// 库位名称
        /// </summary> 
        [Description("库位")]
        public string BinLocationName { get; set; }

        /// <summary> 
        /// 是否过账 1：已过账，0：未过账
        /// </summary> 
        [Description("是否过账")]
        public bool IsPosted { get; set; }

        /// <summary> 
        /// 过账人
        /// </summary> 
        [Description("过账人")]
        public string PostUser { get; set; }

        /// <summary> 
        /// 过账时间
        /// </summary> 
        [Description("过账时间")]
        public DateTime? PostTime { get; set; }

        /// <summary> 
        /// sap生成单号
        /// </summary> 
        [Description("sap物料凭证单号")]
        public string SapDocNum { get; set; }

        /// <summary> 
        /// sap生成行号
        /// </summary> 
        [Description("sap物料凭证行号")]
        public int? SapLine { get; set; }

        /// <summary> 
        /// 公司代码
        /// </summary> 
        [Description("公司代码")]
        public string CompanyCode { get; set; }

        /// <summary> 
        /// 工厂代码
        /// </summary> 
        [Description("工厂代码")]
        public string FactoryCode { get; set; }

        /// <summary> 
        /// 
        /// 
        /// </summary> 
        [Description("移动类型")]
        public string MovementType { get; set; }

        /// <summary> 
        /// 特殊库存
        /// </summary> 
        [Description("特殊库存")]
        public string SpecialInventory { get; set; }

    }
}
