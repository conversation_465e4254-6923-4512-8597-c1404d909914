using System;
using System.Linq;
using System.Web.Http;
using AOS.OMS.Entity.Sale;
using AOS.WMS.Entity.CableProduce;
using HZ.Core.Http;
using HZ.Core.Office;
using HZ.WMS.Application.PP;
using HZ.WMS.WebAPI.Controllers;

namespace HZ.WMS.WebAPI.Areas.PP.Controllers
{
    /// <summary>
    /// 工单管理
    /// </summary>
    public class WorkOrderController : ApiBaseController
    {
        private Cable_WorkOrderApp _app = new Cable_WorkOrderApp();

        #region 查询工单列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri] Pagination page,
            [FromUri] Cable_WorkOrderListReq productionOrderListReq)
        {
            var result = new ResponseData();
            try
            {
                page.Sort = "CTime desc";
                var itemsData = _app.GetPageList(page, productionOrderListReq);
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion
        
        #region 查询工单详情

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetInfo([FromUri] Cable_WorkOrderInfoReq productionOrderInfoReq)
        {
            var result = new ResponseData();
            try
            {
                // 校验合同号不能为空
                if (string.IsNullOrEmpty(productionOrderInfoReq.ContractNo))
                {
                    result.Message = "合同号不能为空";
                    result.Code = (int)WMSStatusCode.Failed;
                }
                result.Data = _app.GetList(t => t.ContractNo == productionOrderInfoReq.ContractNo).ToList().FirstOrDefault();
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion
        
        #region 工单确认扫描

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult ConfirmScan([FromBody] Cable_WorkOrderConfirmScan workOrderConfirmScan)
        {
            var result = new ResponseData();
            try
            {
                var entity = _app.GetEntityByKey(workOrderConfirmScan.Id);
                if (workOrderConfirmScan.Type == 1)
                {
                    entity.ScanStatus = 1;
                }
                else if (workOrderConfirmScan.Type == 2)
                {
                    entity.HoistwayStatus = 1;
                }
                _app.Update(entity);
                result.Message = "扫描成功";
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 打印标记更新

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult UpdatePrint([FromBody] Cable_PrintUpdate cablePrintUpdate)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.UpdatePrint(cablePrintUpdate);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 生成排序

        /// <summary>
        /// 生成排序
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult GenerateProductNo([FromBody] string[] ids)
        {
            var result = new ResponseData();
            try
            {
                _app.GenerateProductNo(ids);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 合同明细导出

        [HttpGet]
        public IHttpActionResult ContractDetailExport([FromUri] Cable_WorkOrderListReq orderInfoListReq,
            [FromUri] Pagination page)
        {
            var result = new ResponseData();
            try
            {
                page.PageSize = int.MaxValue;
                var itemsData = _app.GetPageList(page, orderInfoListReq);

                var columns = ExcelService.FetchDefaultColumnList<SD_Cable_Sale_OrderExport>();
                //排除不需要的列名称 字符串
                string[] ignoreField = new string[] { "Remark", "IsDelete", "MUser", "MTime", "DUser", "DTime", "ID" };
                var ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<SD_Cable_Sale_OrderExport> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }

                System.Collections.Generic.List<SD_Cable_Sale_OrderExport> cableSaleOrderExports =
                    new System.Collections.Generic.List<SD_Cable_Sale_OrderExport>();
                foreach (var item in itemsData)
                {
                    SD_Cable_Sale_OrderExport export = new SD_Cable_Sale_OrderExport();
                    if (item.SaleParameter != null)
                    {
                        // export.SequenceNo = item.SaleParameter.SequenceNo;
                        export.FloorStationDoor = item.SaleParameter.FloorNo + "/" + item.SaleParameter.StationStopNo +
                                                  "/" + item.SaleParameter.OpenNo;

                        export.EstimatedWeight = item.SaleParameter.EstimatedWeight;
                        export.BatchNo = item.SaleParameter.BatchNo;
                        export.Trapezium = item.SaleParameter.Trapezium;
                        export.MaterialCode = item.SaleParameter.MaterialCode;
                        export.Print =
                            (!string.IsNullOrEmpty(item.SaleParameter.PackageBoxStatus)
                                ? item.SaleParameter.PackageBoxStatus + "&"
                                : "") +
                            (!string.IsNullOrEmpty(item.SaleParameter.Print) ? item.SaleParameter.Print : "");
                    }

                    if (item.OrderDetailList != null && item.OrderDetailList.Count > 0)
                    {
                        foreach (var sdCableSaleOrderDetails in item.OrderDetailList)
                        {
                            if (sdCableSaleOrderDetails.Classify == "包装箱1")
                            {
                                export.WoodenBoxSize = sdCableSaleOrderDetails.SpecificationModel;
                            }

                            if (sdCableSaleOrderDetails.Classify == "包装箱2")
                            {
                                export.WoodenTraySize = sdCableSaleOrderDetails.SpecificationModel;
                            }
                        }
                    }

                    export.DeliveryTime = item.DeliveryDate;
                    export.ContractNo = item.ContractNo;
                    export.CustomerOrderNum = item.CustomerOrderNum;
                    export.SequenceNo = item.ProductionNo;
                    export.OrderTypeName = item.OrderTypeName;
                    export.CustomerName = item.CustomerName;
                    export.ElevatorType = item.ElevatorType;

                    export.PartCode = item.PartCode;
                    cableSaleOrderExports.Add(export);
                }

                return ExportToExcelFile(cableSaleOrderExports, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion
        
    }
}