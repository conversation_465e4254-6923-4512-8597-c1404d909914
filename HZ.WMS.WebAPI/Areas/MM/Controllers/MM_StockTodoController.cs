using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;
using AOS.WMS.Application.Store;
using AOS.WMS.Entity.Sys;
using HZ.Core.Http;
using HZ.Core.Office;
using HZ.WMS.WebAPI.Controllers;

namespace AOS.WMS.WebAPI.Areas.Store
{
    /// <summary>
    /// 供应商库存
    /// </summary>
    public class MM_StockTodoController : ApiBaseController
    {
        private MM_StockTodoApp _app = new MM_StockTodoApp();
        private MM_StockTodoDetailApp _appDetail = new MM_StockTodoDetailApp();

        #region 查询分页列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri] Pagination page, [FromUri] string itemCode)
        {
            var result = new ResponseData();
            try
            {
                page.Sort = "CTime";
                var itemData = _app.GetPageList(page, x =>
                    (string.IsNullOrEmpty(itemCode) || x.ItemCode.Contains(itemCode))).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 查询明细分页列表

        /// <summary>
        /// 查询明细分页列表
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetDetailPageList([FromUri] Pagination page,
            [FromUri] string itemCode)
        {
            var result = new ResponseData();
            try
            {
                page.Sort = "CTime";
                var itemData = _appDetail.GetPageList(page, x =>
                    (string.IsNullOrEmpty(itemCode) || x.ItemCode.Contains(itemCode))).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取件号列表

        /// <summary>
        /// 查询明细分页列表
        /// </summary>
        /// <param name="supplierCode"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPartList([FromUri] string SupplierCode)
        {
            var result = new ResponseData();
            try
            {
                var itemData = _app.GetList(x =>
                        string.IsNullOrEmpty(SupplierCode)).ToList()
                    .Select(t => t.ItemCode)
                    .Distinct().ToList();
                result.Data = itemData;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 入库

        /// <summary>
        /// 入库
        /// </summary>
        /// <param name="storeSupplier"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Warehousing([FromBody] MM_StockTodoDetail mmStockTodoDetail)
        {
            var result = new ResponseData();
            try
            {
                return Json(_app.Warehousing(mmStockTodoDetail, GetCurrentUser().LoginAccount));
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 创建

        /// <summary>
        /// 创建
        /// </summary>
        /// <param name="mmStockTodo"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Create([FromBody] MM_StockTodo mmStockTodo)
        {
            var result = new ResponseData();
            try
            {
                var list = _app.DbContext.Queryable<MM_StockTodo>().Where(t =>
                    t.ItemCode == mmStockTodo.ItemCode).ToList();
                if (list.Count > 0)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "物料不存在";
                    return Json(result);
                }
                mmStockTodo.ID = Guid.NewGuid().ToString();
                mmStockTodo.StoreNo = 0;
                _app.Insert(mmStockTodo);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 导出

        /// <summary>
        /// 导出
        /// </summary>
        /// <param name="PartNo"></param>
        /// <param name="SupplierCode"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] string itemCode)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetList(x => (string.IsNullOrEmpty(itemCode) || x.ItemCode.Contains(itemCode))).ToList();
                List<ExcelColumn<MM_StockTodo>> columns = ExcelService.FetchDefaultColumnList<MM_StockTodo>();
                string[] ignoreField = new string[] { };

                List<ExcelColumn<MM_StockTodo>> ignoreFieldList =
                    columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<MM_StockTodo> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }

                return ExportToExcelFile<MM_StockTodo>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="storeSupplier"></param>
        /// <returns></returns>
        [HttpDelete]
        public IHttpActionResult Delete([FromBody] String[] ids)
        {
            var result = new ResponseData();
            try
            {
                _app.DeleteByKeys(ids, GetCurrentUser().LoginAccount);
                var detailIds = _appDetail.GetList(t => ids.Contains(t.PID)).ToList().Select(t => t.ID).Distinct()
                    .ToArray();
                _appDetail.DeleteByKeys(detailIds, GetCurrentUser().LoginAccount);
                result.Data = "删除成功";
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion
        
    }
}