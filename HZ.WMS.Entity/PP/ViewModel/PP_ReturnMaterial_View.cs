using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.PP.ViewModel
{
    /// <summary>
    /// 车间退料
    /// </summary>
    public class PP_ReturnMaterial_View
    {
        /// <summary>
        /// 生产订单
        /// </summary>
        public string ProductionOrderNo { get; set; }
        /// <summary>
        /// 组件行项目号
        /// </summary>
        public decimal ComponentLineNo { get; set; }
        /// <summary> 
        /// 物料编号
        /// </summary> 
        public string ComponentCode { get; set; }
        /// <summary> 
        /// 物料描述
        /// </summary> 
        public string MaterialName { get; set; }
        /// <summary> 
        /// 需求数量
        /// </summary> 
        public decimal? DemandQty { get; set; }
        /// <summary> 
        /// 组件单位
        /// </summary> 
        public string ComponentUnit { get; set; }
        /// <summary> 
        /// 工厂代码
        /// </summary> 
        public string FactoryCode { get; set; }
        /// <summary> 
        /// 转出仓库编号
        /// </summary> 
        public string OutWhsCode { get; set; }
        /// <summary> 
        /// 转出仓库名称
        /// </summary> 
        public string OutWhsName { get; set; }
        /// <summary> 
        /// 转出区域编号
        /// </summary> 
        public string OutRegionCode { get; set; }
        /// <summary> 
        /// 转出区域名称
        /// </summary> 
        public string OutRegionName { get; set; }
        /// <summary> 
        /// 转出库位编号
        /// </summary> 
        public string OutBinCode { get; set; }
        /// <summary> 
        /// 转出库位名称
        /// </summary> 
        public string OutBinName { get; set; }
        /// <summary> 
        /// 转入仓库编号
        /// </summary> 
        public string InWhsCode { get; set; }
        /// <summary> 
        /// 转入仓库名称
        /// </summary> 
        public string InWhsName { get; set; }
        /// <summary> 
        /// 转入区域编号
        /// </summary> 
        public string InRegionCode { get; set; }
        /// <summary> 
        /// 转入区域名称
        /// </summary> 
        public string InRegionName { get; set; }
        /// <summary> 
        /// 转入库位编号
        /// </summary> 
        public string InBinCode { get; set; }
        /// <summary> 
        /// 转入库位名称
        /// </summary> 
        public string InBinName { get; set; }
        /// <summary>
        /// 销售订单
        /// </summary>
        public string SalesOrderNo { get; set; }
        /// <summary>
        /// 销售订单行项目
        /// </summary>
        public int? SalesOrderLineNo { get; set; }
        /// <summary>
        /// 特殊库存标识
        /// </summary>
        public string SpecialInventory { get; set; }
        /// <summary>
        /// 评估类别，为B时评估类型为01自制02外购
        /// </summary>
        public string AssessmentCategory { get; set; }
        /// <summary>
        /// 开始日期(装配日期)
        /// </summary>
        public DateTime? StartTime { get; set; }
        /// <summary>
        /// 主表物料编码
        /// </summary>
        public string MaterialNo { get; set; }
    }
}
