using System.Configuration;
using HZ.Core.Security;

namespace HZ.WMS.Application
{
    /// <summary>
    /// 
    /// </summary>
    public class DbConfig
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="dbKey"></param>
        /// <returns></returns>
        public static string GetDbConnectionString(string dbKey)
        {
            return DES.Decrypt(ConfigurationManager.ConnectionStrings[dbKey].ConnectionString);
        }

        /// <summary>
        /// EAP中间库连接字符串
        /// </summary>
        /// <param name="dbKeyForEAP"></param>
        /// <returns></returns>
        public static string GetDbConnectionStringForEAP(string dbKeyForEAP)
        {
            return DES.Decrypt(ConfigurationManager.ConnectionStrings[dbKeyForEAP].ConnectionString);
        }

        /// <summary>
        /// SAP中间库连接字符串
        /// </summary>
        /// <param name="dbKeyForSAP"></param>
        /// <returns></returns>
        public static string GetDbConnectionStringForSAP(string dbKeyForSAP)
        {
            return DES.Decrypt(ConfigurationManager.ConnectionStrings[dbKeyForSAP].ConnectionString);
        }

        /// <summary>
        /// SRM连接字符串
        /// </summary>
        /// <param name="dbKeyForSRM"></param>
        /// <returns></returns>
        public static string GetDbConnectionStringForSRM(string dbKeyForSRM)
        {
            return DES.Decrypt(ConfigurationManager.ConnectionStrings[dbKeyForSRM].ConnectionString);
        }

        /// <summary>
        /// OMS连接字符串
        /// </summary>
        /// <param name="dbKeyForOMS"></param>
        /// <returns></returns>
        public static string GetDbConnectionStringForOMS(string dbKeyForOMS)
        {
            return DES.Decrypt(ConfigurationManager.ConnectionStrings[dbKeyForOMS].ConnectionString);
        }

        /// <summary>
        /// Oracle数据库连接字符串
        /// </summary>
        /// <param name="dbKeyForOracle">Oracle数据库连接配置键名</param>
        /// <returns>Oracle连接字符串</returns>
        public static string GetDbConnectionStringForOracle(string dbKeyForOracle = "DbConnectionForOracle")
        {
            return ConfigurationManager.ConnectionStrings[dbKeyForOracle].ConnectionString;
        }
    }
     
}
