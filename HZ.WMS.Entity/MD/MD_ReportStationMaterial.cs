using SqlSugar;
using System.ComponentModel;

namespace HZ.WMS.Entity.MD
{
    /// <summary>
    /// 报工站点物料
    /// </summary>
    public class MD_ReportStationMaterial : BaseEntity
    {
        /// <summary>
        /// 主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Description("主键")]
        public string Id { get; set; }

        /// <summary>
        /// 工作中心名称
        /// </summary>
        [Description("工作中心名称")]
        public string WorkCenterName { get; set; }

        /// <summary>
        /// 工作中心编码
        /// </summary>
        [Description("工作中心编码")]
        public string WorkCenterCode { get; set; }

        /// <summary>
        /// 工序号
        /// </summary>
        [Description("工序号")]
        public int? ProcessNo { get; set; }

        /// <summary>
        /// 工序短文本
        /// </summary>
        [Description("工序短文本")]
        public string ProcessShortText { get; set; }

        /// <summary>
        /// 站点代码
        /// </summary>
        [Description("站点代码")]
        public string StationCode { get; set; }

        /// <summary>
        /// 站点名称
        /// </summary>
        [Description("站点名称")]
        public string StationName { get; set; }

        /// <summary>
        /// 物料编码
        /// </summary>
        [Description("物料编码")]
        public string MaterialCode { get; set; }

        /// <summary>
        /// 物料描述
        /// </summary>
        [Description("物料描述")]
        public string MaterialDesc { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量")]
        public decimal? Quantity { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [Description("单位")]
        public string Unit { get; set; }

        /// <summary>
        /// 站点序号
        /// </summary>
        [Description("站点序号")]
        public decimal? StationSort { get; set; }

        /// <summary>
        /// 是否多站点配送 1是  0否
        /// </summary>
        [Description("是否多站点配送 1是 0否")]
        public bool? IsMultistation { get; set; }
    }
} 