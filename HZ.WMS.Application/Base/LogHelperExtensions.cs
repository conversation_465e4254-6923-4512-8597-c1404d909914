using System;
using HZ.Core.Logging;

namespace HZ.WMS.Application
{
    /// <summary>
    /// LogHelper 扩展方法
    /// </summary>
    public static class LogHelperExtensions
    {
        /// <summary>
        /// 记录错误日志，包含异常详细信息
        /// </summary>
        /// <param name="logHelper">LogHelper实例</param>
        /// <param name="message">错误消息</param>
        /// <param name="exception">异常对象</param>
        public static void LogError(this LogHelper logHelper, string message, Exception exception)
        {
            var detailedMessage = $"{message}\n异常类型: {exception.GetType().Name}\n异常消息: {exception.Message}\n堆栈跟踪: {exception.StackTrace}";
            
            // 如果有内部异常，也记录
            if (exception.InnerException != null)
            {
                detailedMessage += $"\n内部异常: {exception.InnerException.Message}";
                if (!string.IsNullOrEmpty(exception.InnerException.StackTrace))
                {
                    detailedMessage += $"\n内部异常堆栈: {exception.InnerException.StackTrace}";
                }
            }
            
            logHelper.LogError(detailedMessage);
        }

        /// <summary>
        /// 记录连接池相关错误
        /// </summary>
        /// <param name="logHelper">LogHelper实例</param>
        /// <param name="dbKey">数据库连接键</param>
        /// <param name="operation">操作描述</param>
        /// <param name="exception">异常对象</param>
        public static void LogConnectionPoolError(this LogHelper logHelper, string dbKey, string operation, Exception exception)
        {
            var message = $"连接池错误 - 数据库: {dbKey}, 操作: {operation}";
            logHelper.LogError(message, exception);
        }

        /// <summary>
        /// 记录数据库操作错误
        /// </summary>
        /// <param name="logHelper">LogHelper实例</param>
        /// <param name="operation">操作描述</param>
        /// <param name="sql">SQL语句</param>
        /// <param name="exception">异常对象</param>
        public static void LogDatabaseError(this LogHelper logHelper, string operation, string sql, Exception exception)
        {
            var message = $"数据库操作错误 - 操作: {operation}\nSQL: {sql}";
            logHelper.LogError(message, exception);
        }

        /// <summary>
        /// 记录事务错误
        /// </summary>
        /// <param name="logHelper">LogHelper实例</param>
        /// <param name="operation">操作描述</param>
        /// <param name="entityCount">实体数量</param>
        /// <param name="exception">异常对象</param>
        public static void LogTransactionError(this LogHelper logHelper, string operation, int entityCount, Exception exception)
        {
            var message = $"事务操作错误 - 操作: {operation}, 实体数量: {entityCount}";
            logHelper.LogError(message, exception);
        }

        /// <summary>
        /// 记录配置错误
        /// </summary>
        /// <param name="logHelper">LogHelper实例</param>
        /// <param name="configKey">配置键</param>
        /// <param name="operation">操作描述</param>
        /// <param name="exception">异常对象</param>
        public static void LogConfigurationError(this LogHelper logHelper, string configKey, string operation, Exception exception)
        {
            var message = $"配置错误 - 配置键: {configKey}, 操作: {operation}";
            logHelper.LogError(message, exception);
        }
    }
}
