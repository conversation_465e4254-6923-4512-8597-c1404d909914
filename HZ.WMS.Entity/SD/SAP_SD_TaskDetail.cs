using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.SD
{
    public class SAP_SD_TaskDetail : BaseEntity
    {
        /// <summary> 
        /// 任务数据库uid
        /// </summary> 
        [SugarColumn(IsPrimaryKey = true)]
        [Description("任务数据库uid")]
        public string TaskUUID { get; set; }
        /// <summary>
        /// 任务ID
        /// </summary>
        [Description("任务ID")]
        public string SiteLogisticsLotMaterialInputUUID { get; set; }
        /// <summary>
        /// 物料编号
        /// </summary>
        [Description("物料编号")]
        public string ProductID { get; set; }
        /// <summary>
        /// 物料名称
        /// </summary>
        [Description("物料名称")]
        public string ProductDescription { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量")]
        public decimal? OpenQuantity { get; set; }
        /// <summary>
        /// 单位
        /// </summary>
        [Description("单位")]
        public string UnitCode { get; set; }
        /// <summary>
        /// 物料行号
        /// </summary>
        [Description("物料行号")]
        public string LineItemID { get; set; }
        /// <summary>
        /// 入库物料UUID
        /// </summary>
        [Description("入库物料UUID")]
        public string SiteLogisticsLotMaterialOutputUUID { get; set; }
        /// <summary>
        /// 相关单据编号（销售订单）
        /// </summary>
        [Description("相关单据编号")]
        public string ReferenceDocumentID { get; set; }
        /// <summary>
        /// 相关单据行号（销售订单行号）
        /// </summary>
        [Description("相关单据行号")]
        public string ReferenceDocumentLineID { get; set; }
        /// <summary>
        /// 任务ID
        /// </summary>
        [Description("任务ID")]
        public string SiteLogisticsTaskID { get; set; }

    }
}
