using System.Web.Mvc;

namespace HZ.WMS.WebAPI.Areas.PO
{
    public class QMAreaRegistration : AreaRegistration 
    {
        public override string AreaName 
        {
            get 
            {
                return "QM";
            }
        }

        public override void RegisterArea(AreaRegistrationContext context) 
        {
            context.MapRoute(
                "QM_default",
                "QM/{controller}/{action}/{id}",
                new { action = "Index", id = UrlParameter.Optional }
            );
        }
    }
}