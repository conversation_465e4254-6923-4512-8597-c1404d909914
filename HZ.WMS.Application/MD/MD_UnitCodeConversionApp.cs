using SqlSugar;
using System;
using System.Collections.Generic;

using HZ.Core.Security;
using HZ.WMS.Entity.MD;
using HZ.Core.Http;
using System.Linq;

namespace HZ.WMS.Application.MD
{
    /// <summary>
    /// 通用单位转换率
    /// </summary>
    public class MD_UnitCodeConversionApp : BaseApp<MD_UnitCodeConversion>
    {
        #region 获取单位转换率

        /// <summary>
        /// 单位转换率：以乘法的方式使用
        /// 单位转换表只存在两种情况： A->B 或者 A->B->C  
        /// 例如：LBR-->KGM 得到返回值：0.45359237 
        ///     意味 2 LBR = 2*0.45359237 KGM
        /// </summary>
        /// <param name="fromUnitCode"></param>
        /// <param name="toUnitCode"></param>
        /// <returns></returns>
        public decimal GetConvertRate(string fromUnitCode,string toUnitCode)
        {
            decimal converRate = 1;   //相同单位返回1;查询不到转换结果，则抛出异常
            if (fromUnitCode == toUnitCode) return converRate;

            List<MD_UnitCodeConversion> unitConvertList = GetList().ToList();

            if(unitConvertList.Any(x=>x.FromUnitCode==fromUnitCode && x.ToUnitCode == toUnitCode))
            {
                converRate = unitConvertList.Where(x => x.FromUnitCode == fromUnitCode && x.ToUnitCode == toUnitCode).FirstOrDefault().ConvertRate;
            }
            else
            {
                // fromUnit
                MD_UnitCodeConversion fromUnit = unitConvertList.Where(x => x.FromUnitCode == fromUnitCode).FirstOrDefault();

                // toUnit
                MD_UnitCodeConversion toUnit = unitConvertList.Where(x => x.FromUnitCode == toUnitCode).FirstOrDefault();

                if(fromUnit!=null && toUnit!=null && fromUnit.ToUnitCode == toUnit.ToUnitCode)
                {
                    converRate = fromUnit.ConvertRate / toUnit.ConvertRate;
                }
                else
                {
                    throw new Exception(string.Format("单位转换表MD_UnitCodeConversion中不存在单位{0}-->{1}的转换关系！", fromUnitCode, toUnit));
                }
            }

            return converRate;
        }


        #endregion
    }
}
