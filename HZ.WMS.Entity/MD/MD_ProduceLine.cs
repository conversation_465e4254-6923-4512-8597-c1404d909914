using SqlSugar;
using System.ComponentModel;

namespace HZ.WMS.Entity.MD
{
    /// <summary>
    /// 生产线对照
    /// </summary>
    [SugarTable("MD_ProduceLine")]
    public class MD_ProduceLine : BaseEntity
    {

        /// <summary>
        /// Id
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Description("Id")]
        public string Id { get; set; }

        /// <summary>
        /// 线体名称
        /// </summary>
        [Description("线体名称")]
        public string LineName { get; set; }

        /// <summary>
        /// Sap代码
        /// </summary>
        [Description("Sap代码")]
        public string SapCode { get; set; }

    }
}