/// <XRTypeInfo>
///   <AssemblyFullName>DevExpress.XtraReports.v16.1, Version=16.1.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</AssemblyFullName>
///   <AssemblyLocation>C:\Windows\Microsoft.Net\assembly\GAC_MSIL\DevExpress.XtraReports.v16.1\v4.0_16.1.5.0__b88d1754d700e49a\DevExpress.XtraReports.v16.1.dll</AssemblyLocation>
///   <TypeName>DevExpress.XtraReports.UI.XtraReport</TypeName>
///   <Localization>zh-CN</Localization>
///   <Version>16.1</Version>
///   <Resources>
///     <Resource Name="XtraReportSerialization.OverReceiveApply">
/// zsrvvgEAAACRAAAAbFN5c3RlbS5SZXNvdXJjZXMuUmVzb3VyY2VSZWFkZXIsIG1zY29ybGliLCBWZXJzaW9uPTQuMC4wLjAsIEN1bHR1cmU9bmV1dHJhbCwgUHVibGljS2V5VG9rZW49Yjc3YTVjNTYxOTM0ZTA4OSNTeXN0ZW0uUmVzb3VyY2VzLlJ1bnRpbWVSZXNvdXJjZVNldAIAAAABAAAAAAAAAFBBRFBBRFA4hdt+AAAAAA8BAABOcwBxAGwARABhAHQAYQBTAG8AdQByAGMAZQAxAC4AUgBlAHMAdQBsAHQAUwBjAGgAZQBtAGEAUwBlAHIAaQBhAGwAaQB6AGEAYgBsAGUAAAAAAAHMClBFUmhkR0ZUWlhRZ1RtRnRaVDBpYzNGc1JHRjBZVk52ZFhKalpURWlQanhXYVdWM0lFNWhiV1U5SWxCUVgxQnlhVzUwVDNabGNsSmxZMlZwZG1WQmNIQnNlVjlXYVdWM0lqNDhS
/// bWxsYkdRZ1RtRnRaVDBpVDNKa1pYSkRZWFJsWjI5eWVTSWdWSGx3WlQwaVUzUnlhVzVuSWlBdlBqeEdhV1ZzWkNCT1lXMWxQU0pQZG1WeVVtVmpaV2wyWlU1dklpQlVlWEJsUFNKVGRISnBibWNpSUM4K1BFWnBaV3hrSUU1aGJXVTlJa1poWTNSdmNubERiMlJsSWlCVWVYQmxQU0pUZEhKcGJtY2lJQzgrUEVacFpXeGtJRTVoYldVOUlsQnliMlIxWTNScGIyNU1hVzVsUTI5a1pTSWdWSGx3WlQwaVUzUnlhVzVuSWlBdlBqeEdhV1ZzWkNCT1lXMWxQU0pQZFhSVGRYQndiR2xsY2lJZ1ZIbHdaVDBpVTNSeWFXNW5JaUF2UGp4R2FXVnNaQ0JPWVcxbFBTSlNaVzFoY21zaUlGUjVjR1U5SWxOMGNtbHVaeUlnTHo0OFJtbGxiR1FnVG1GdFpUMGlSWGhoYldsdVpVUmxjeUln
/// Vkhsd1pUMGlVM1J5YVc1bklpQXZQanhHYVdWc1pDQk9ZVzFsUFNKRVpYQmhjblJ0Wlc1MElpQlVlWEJsUFNKVGRISnBibWNpSUM4K1BFWnBaV3hrSUU1aGJXVTlJa052YlhCdmJtVnVkRU52WkdVaUlGUjVjR1U5SWxOMGNtbHVaeUlnTHo0OFJtbGxiR1FnVG1GdFpUMGlUV0YwWlhKcFlXeE9ZVzFsSWlCVWVYQmxQU0pUZEhKcGJtY2lJQzgrUEVacFpXeGtJRTVoYldVOUlsTndaV05wWm1sallYUnBiMjRpSUZSNWNHVTlJbE4wY21sdVp5SWdMejQ4Um1sbGJHUWdUbUZ0WlQwaVEyOXRjRzl1Wlc1MFZXNXBkQ0lnVkhsd1pUMGlVM1J5YVc1bklpQXZQanhHYVdWc1pDQk9ZVzFsUFNKRVpXMWhibVJSZEhraUlGUjVjR1U5SWtSbFkybHRZV3dpSUM4K1BFWnBaV3hrSUU1
/// aGJXVTlJazkxZEZkaGNtVm9iM1Z6WlNJZ1ZIbHdaVDBpVTNSeWFXNW5JaUF2UGp4R2FXVnNaQ0JPWVcxbFBTSlBkWFJYWVhKbGFHOTFjMlZFWlhNaUlGUjVjR1U5SWxOMGNtbHVaeUlnTHo0OFJtbGxiR1FnVG1GdFpUMGlVSEp2WkhWamRHbHZiazl5WkdWeVRtOGlJRlI1Y0dVOUlsTjBjbWx1WnlJZ0x6NDhSbWxsYkdRZ1RtRnRaVDBpUTNKaFpuUkRiMlJsSWlCVWVYQmxQU0pUZEhKcGJtY2lJQzgrUEVacFpXeGtJRTVoYldVOUlrTnlZV1owUkdWeklpQlVlWEJsUFNKVGRISnBibWNpSUM4K1BFWnBaV3hrSUU1aGJXVTlJa0poZEdOb1RtOGlJRlI1Y0dVOUlsTjBjbWx1WnlJZ0x6NDhSbWxsYkdRZ1RtRnRaVDBpVUdsamEybHVaMFJsY3lJZ1ZIbHdaVDBpVTNSeWFX
/// NW5JaUF2UGp4R2FXVnNaQ0JPWVcxbFBTSkZlSEJ5T0NJZ1ZIbHdaVDBpVTNSeWFXNW5JaUF2UGp4R2FXVnNaQ0JPWVcxbFBTSkNZWFJqYUVSbGN5SWdWSGx3WlQwaVUzUnlhVzVuSWlBdlBqd3ZWbWxsZHo0OEwwUmhkR0ZUWlhRKw==</Resource>
///   </Resources>
/// </XRTypeInfo>
namespace XtraReportSerialization {
    
    public class OverReceiveApply : DevExpress.XtraReports.UI.XtraReport {
        private DevExpress.XtraReports.UI.XRTableCell tableCell13;
        private DevExpress.XtraReports.UI.XRTableCell tableCell30;
        private DevExpress.XtraReports.UI.XRTableCell tableCell42;
        private DevExpress.XtraReports.UI.XRTableCell tableCell29;
        private DevExpress.XtraReports.UI.XRTableCell tableCell25;
        private DevExpress.XtraReports.UI.XRTableCell tableCell8;
        private DevExpress.XtraReports.UI.XRTableCell tableCell49;
        private DevExpress.XtraReports.UI.XRTableCell tableCell35;
        private DevExpress.XtraReports.UI.XRTableCell tableCell45;
        private DevExpress.XtraReports.UI.XRTableRow tableRow4;
        private DevExpress.XtraReports.UI.XRTableCell tableCell19;
        private DevExpress.XtraReports.UI.XRTableCell tableCell20;
        private DevExpress.XtraReports.UI.XRTableCell tableCell21;
        private DevExpress.XtraReports.UI.XRTableCell tableCell22;
        private DevExpress.XtraReports.UI.XRTableCell tableCell23;
        private DevExpress.XtraReports.UI.XRTableCell tableCell24;
        private DevExpress.XtraReports.UI.XRTableCell tableCell3;
        private DevExpress.XtraReports.UI.XRTableRow tableRow11;
        private DevExpress.XtraReports.UI.XRTableCell tableCell48;
        private DevExpress.XtraReports.UI.XRTableCell tableCell46;
        private DevExpress.XtraReports.UI.XRBarCode barCode1;
        private DevExpress.XtraReports.UI.GroupFooterBand GroupFooter1;
        private DevExpress.XtraReports.UI.XRTable table4;
        private DevExpress.XtraReports.UI.XRTableRow tableRow6;
        private DevExpress.XtraReports.UI.XRTableCell tableCell33;
        private DevExpress.XtraReports.UI.XRTableRow tableRow7;
        private DevExpress.XtraReports.UI.XRTableCell tableCell32;
        private DevExpress.XtraReports.UI.XRTableCell tableCell34;
        private DevExpress.XtraReports.UI.XRTableCell tableCell27;
        private DevExpress.XtraReports.UI.XRTableRow tableRow1;
        private DevExpress.XtraReports.UI.XRTableCell tableCell4;
        private DevExpress.XtraReports.UI.XRTableCell tableCell5;
        private DevExpress.XtraReports.UI.XRTableCell tableCell6;
        private DevExpress.XtraReports.UI.XRTableCell tableCell7;
        private DevExpress.XtraReports.UI.XRTableCell tableCell10;
        private DevExpress.XtraReports.UI.XRTableCell tableCell1;
        private DevExpress.XtraReports.UI.XRTable table6;
        private DevExpress.XtraReports.UI.XRTableRow tableRow3;
        private DevExpress.XtraReports.UI.XRTableCell tableCell12;
        private DevExpress.XtraReports.UI.XRTableCell tableCell15;
        private DevExpress.XtraReports.UI.XRTableCell tableCell16;
        private DevExpress.XtraReports.UI.XRTableCell tableCell17;
        private DevExpress.XtraReports.UI.XRTableCell tableCell18;
        private DevExpress.XtraReports.UI.DetailBand Detail;
        private DevExpress.XtraReports.UI.XRTable table1;
        private DevExpress.XtraReports.UI.XRTableRow tableRow9;
        private DevExpress.XtraReports.UI.XRTableCell tableCell41;
        private DevExpress.XtraReports.UI.XRTableCell tableCell43;
        private DevExpress.XtraReports.UI.XRTableCell tableCell44;
        private DevExpress.XtraReports.UI.XRTableCell tableCell50;
        private DevExpress.XtraReports.UI.XRTableRow tableRow8;
        private DevExpress.XtraReports.UI.XRTableCell tableCell36;
        private DevExpress.XtraReports.UI.XRTableCell tableCell37;
        private DevExpress.XtraReports.UI.XRTableCell tableCell38;
        private DevExpress.XtraReports.UI.XRTableCell tableCell39;
        private DevExpress.XtraReports.UI.XRTableCell tableCell40;
        private DevExpress.XtraReports.UI.XRTableCell tableCell51;
        private DevExpress.XtraReports.UI.XRTableCell tableCell31;
        private DevExpress.XtraReports.UI.XRTableCell tableCell9;
        private DevExpress.XtraReports.UI.XRLabel label2;
        private DevExpress.XtraReports.UI.XRTable table2;
        private DevExpress.XtraReports.UI.XRTableRow tableRow2;
        private DevExpress.XtraReports.UI.XRTableCell tableCell2;
        private DevExpress.XtraReports.UI.XRTableCell tableCell11;
        private DevExpress.XtraReports.UI.XRTableCell tableCell14;
        private DevExpress.XtraReports.UI.TopMarginBand TopMargin;
        private DevExpress.XtraReports.UI.XRTableRow tableRow5;
        private DevExpress.XtraReports.UI.XRTableCell tableCell26;
        private DevExpress.XtraReports.UI.XRTableCell tableCell28;
        private DevExpress.XtraReports.UI.XRTable table3;
        private DevExpress.XtraReports.UI.XRLabel label4;
        private DevExpress.XtraReports.UI.BottomMarginBand BottomMargin;
        private DevExpress.XtraReports.UI.XRLabel label1;
        private DevExpress.XtraReports.UI.XRLabel label3;
        private DevExpress.XtraReports.UI.CalculatedField calculatedField1;
        private DevExpress.XtraReports.Parameters.Parameter docNums;
        private DevExpress.DataAccess.Sql.SqlDataSource sqlDataSource1;
        private DevExpress.XtraReports.UI.GroupHeaderBand GroupHeader2;
        private System.Resources.ResourceManager _resources;
        private string _resourceString;
        public OverReceiveApply() {
            this._resourceString = DevExpress.XtraReports.Serialization.XRResourceManager.GetResourceFor("XtraReportSerialization.OverReceiveApply");
            this.InitializeComponent();
        }
        private System.Resources.ResourceManager resources {
            get {
                if (_resources == null) {
                    this._resources = new DevExpress.XtraReports.Serialization.XRResourceManager(this._resourceString);
                }
                return this._resources;
            }
        }
        private void InitializeComponent() {
            DevExpress.XtraPrinting.BarCode.QRCodeGenerator qRCodeGenerator1 = new DevExpress.XtraPrinting.BarCode.QRCodeGenerator();
            DevExpress.XtraReports.UI.XRSummary summary1 = new DevExpress.XtraReports.UI.XRSummary();
            DevExpress.XtraReports.UI.XRSummary summary2 = new DevExpress.XtraReports.UI.XRSummary();
            DevExpress.DataAccess.ConnectionParameters.MsSqlConnectionParameters msSqlConnectionParameters1 = new DevExpress.DataAccess.ConnectionParameters.MsSqlConnectionParameters();
            DevExpress.DataAccess.Sql.SelectQuery selectQuery1 = new DevExpress.DataAccess.Sql.SelectQuery();
            DevExpress.DataAccess.Sql.AllColumns allColumns1 = new DevExpress.DataAccess.Sql.AllColumns();
            DevExpress.DataAccess.Sql.Table table5 = new DevExpress.DataAccess.Sql.Table();
            DevExpress.DataAccess.Sql.QueryParameter queryParameter1 = new DevExpress.DataAccess.Sql.QueryParameter();
            this.tableCell13 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell30 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell42 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell29 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell25 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell8 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell49 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell35 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell45 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableRow4 = new DevExpress.XtraReports.UI.XRTableRow();
            this.tableCell3 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableRow11 = new DevExpress.XtraReports.UI.XRTableRow();
            this.tableCell46 = new DevExpress.XtraReports.UI.XRTableCell();
            this.barCode1 = new DevExpress.XtraReports.UI.XRBarCode();
            this.GroupFooter1 = new DevExpress.XtraReports.UI.GroupFooterBand();
            this.tableCell19 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell27 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableRow1 = new DevExpress.XtraReports.UI.XRTableRow();
            this.tableCell1 = new DevExpress.XtraReports.UI.XRTableCell();
            this.table6 = new DevExpress.XtraReports.UI.XRTable();
            this.tableRow3 = new DevExpress.XtraReports.UI.XRTableRow();
            this.Detail = new DevExpress.XtraReports.UI.DetailBand();
            this.table4 = new DevExpress.XtraReports.UI.XRTable();
            this.tableCell24 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell37 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell5 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell39 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell18 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell7 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell20 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell33 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell31 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell9 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell43 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell48 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableRow9 = new DevExpress.XtraReports.UI.XRTableRow();
            this.tableCell36 = new DevExpress.XtraReports.UI.XRTableCell();
            this.label2 = new DevExpress.XtraReports.UI.XRLabel();
            this.tableCell32 = new DevExpress.XtraReports.UI.XRTableCell();
            this.table2 = new DevExpress.XtraReports.UI.XRTable();
            this.TopMargin = new DevExpress.XtraReports.UI.TopMarginBand();
            this.tableRow5 = new DevExpress.XtraReports.UI.XRTableRow();
            this.tableCell17 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell51 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell41 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableRow7 = new DevExpress.XtraReports.UI.XRTableRow();
            this.table3 = new DevExpress.XtraReports.UI.XRTable();
            this.table1 = new DevExpress.XtraReports.UI.XRTable();
            this.tableCell44 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell50 = new DevExpress.XtraReports.UI.XRTableCell();
            this.label4 = new DevExpress.XtraReports.UI.XRLabel();
            this.BottomMargin = new DevExpress.XtraReports.UI.BottomMarginBand();
            this.label1 = new DevExpress.XtraReports.UI.XRLabel();
            this.tableCell26 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell4 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableRow6 = new DevExpress.XtraReports.UI.XRTableRow();
            this.label3 = new DevExpress.XtraReports.UI.XRLabel();
            this.calculatedField1 = new DevExpress.XtraReports.UI.CalculatedField();
            this.tableCell6 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell22 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell28 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell23 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell12 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableRow2 = new DevExpress.XtraReports.UI.XRTableRow();
            this.tableCell16 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell34 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell15 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell38 = new DevExpress.XtraReports.UI.XRTableCell();
            this.docNums = new DevExpress.XtraReports.Parameters.Parameter();
            this.tableCell14 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell11 = new DevExpress.XtraReports.UI.XRTableCell();
            this.sqlDataSource1 = new DevExpress.DataAccess.Sql.SqlDataSource();
            this.tableCell40 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell10 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableRow8 = new DevExpress.XtraReports.UI.XRTableRow();
            this.GroupHeader2 = new DevExpress.XtraReports.UI.GroupHeaderBand();
            this.tableCell21 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell2 = new DevExpress.XtraReports.UI.XRTableCell();
            ((System.ComponentModel.ISupportInitialize)(this.table6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.table4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.table2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.table3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.table1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // tableCell13
            // 
            this.tableCell13.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "calculatedField1", "{0:yyyy-MM-dd}")});
            this.tableCell13.Dpi = 100F;
            this.tableCell13.Name = "tableCell13";
            this.tableCell13.StylePriority.UseTextAlignment = false;
            this.tableCell13.Text = "tableCell13";
            this.tableCell13.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.tableCell13.Weight = 0.565626606263973D;
            // 
            // tableCell30
            // 
            this.tableCell30.Dpi = 100F;
            this.tableCell30.Multiline = true;
            this.tableCell30.Name = "tableCell30";
            this.tableCell30.StylePriority.UseTextAlignment = false;
            this.tableCell30.Text = "工单编号\r\n工    艺\r\n工艺名称\r\n";
            this.tableCell30.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell30.Weight = 0.45129419267525733D;
            // 
            // tableCell42
            // 
            this.tableCell42.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "PP_PrintOverReceiveApply_View.MaterialName")});
            this.tableCell42.Dpi = 100F;
            this.tableCell42.Name = "tableCell42";
            this.tableCell42.StylePriority.UseTextAlignment = false;
            this.tableCell42.Text = "tableCell42";
            this.tableCell42.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell42.Weight = 0.59205204296700631D;
            // 
            // tableCell29
            // 
            this.tableCell29.Dpi = 100F;
            this.tableCell29.Name = "tableCell29";
            this.tableCell29.StylePriority.UseTextAlignment = false;
            this.tableCell29.Text = "单位";
            this.tableCell29.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell29.Weight = 0.2350077955647282D;
            // 
            // tableCell25
            // 
            this.tableCell25.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell25.Dpi = 100F;
            this.tableCell25.Name = "tableCell25";
            this.tableCell25.StylePriority.UseBorders = false;
            this.tableCell25.StylePriority.UseTextAlignment = false;
            this.tableCell25.Text = "总领料数量";
            this.tableCell25.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.tableCell25.Weight = 0.23989483698434622D;
            // 
            // tableCell8
            // 
            this.tableCell8.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right)));
            this.tableCell8.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "PP_PrintOverReceiveApply_View.ProductionOrderNo")});
            this.tableCell8.Dpi = 100F;
            this.tableCell8.Multiline = true;
            this.tableCell8.Name = "tableCell8";
            this.tableCell8.StylePriority.UseBorders = false;
            this.tableCell8.StylePriority.UseTextAlignment = false;
            this.tableCell8.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell8.Weight = 0.46803746465992235D;
            // 
            // tableCell49
            // 
            this.tableCell49.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right)));
            this.tableCell49.Dpi = 100F;
            this.tableCell49.Name = "tableCell49";
            this.tableCell49.RowSpan = 3;
            this.tableCell49.StylePriority.UseBorders = false;
            this.tableCell49.StylePriority.UseTextAlignment = false;
            this.tableCell49.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell49.Weight = 0.34992178809468938D;
            // 
            // tableCell35
            // 
            this.tableCell35.Dpi = 100F;
            this.tableCell35.Name = "tableCell35";
            this.tableCell35.StylePriority.UseTextAlignment = false;
            this.tableCell35.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell35.Weight = 0.22244391808174957D;
            // 
            // tableCell45
            // 
            this.tableCell45.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "PP_PrintOverReceiveApply_View.CraftCode")});
            this.tableCell45.Dpi = 100F;
            this.tableCell45.Name = "tableCell45";
            this.tableCell45.StylePriority.UseTextAlignment = false;
            this.tableCell45.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell45.Weight = 0.46803746465992235D;
            // 
            // tableRow4
            // 
            this.tableRow4.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
                        this.tableCell19,
                        this.tableCell20,
                        this.tableCell21,
                        this.tableCell22,
                        this.tableCell23,
                        this.tableCell24});
            this.tableRow4.Dpi = 100F;
            this.tableRow4.Name = "tableRow4";
            this.tableRow4.Weight = 1D;
            // 
            // tableCell3
            // 
            this.tableCell3.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "PP_PrintOverReceiveApply_View.FactoryCode")});
            this.tableCell3.Dpi = 100F;
            this.tableCell3.Name = "tableCell3";
            this.tableCell3.StylePriority.UseTextAlignment = false;
            this.tableCell3.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.tableCell3.Weight = 0.57011569428202225D;
            // 
            // tableRow11
            // 
            this.tableRow11.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.tableRow11.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
                        this.tableCell48});
            this.tableRow11.Dpi = 100F;
            this.tableRow11.Name = "tableRow11";
            this.tableRow11.StylePriority.UseBorders = false;
            this.tableRow11.Weight = 1D;
            // 
            // tableCell46
            // 
            this.tableCell46.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "PP_PrintOverReceiveApply_View.PickingDes")});
            this.tableCell46.Dpi = 100F;
            this.tableCell46.Name = "tableCell46";
            this.tableCell46.StylePriority.UseTextAlignment = false;
            this.tableCell46.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell46.Weight = 0.45531009144238332D;
            // 
            // barCode1
            // 
            this.barCode1.Alignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.barCode1.AutoModule = true;
            this.barCode1.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.barCode1.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "PP_PrintOverReceiveApply_View.ComponentCode")});
            this.barCode1.Dpi = 100F;
            this.barCode1.LocationFloat = new DevExpress.Utils.PointFloat(669.8499F, 0F);
            this.barCode1.Name = "barCode1";
            this.barCode1.Padding = new DevExpress.XtraPrinting.PaddingInfo(10, 10, 0, 0, 100F);
            this.barCode1.ShowText = false;
            this.barCode1.SizeF = new System.Drawing.SizeF(91.94183F, 75.00001F);
            this.barCode1.StylePriority.UseBorders = false;
            this.barCode1.StylePriority.UseTextAlignment = false;
            this.barCode1.Symbology = qRCodeGenerator1;
            this.barCode1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // GroupFooter1
            // 
            this.GroupFooter1.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
                        this.table4});
            this.GroupFooter1.Dpi = 100F;
            this.GroupFooter1.HeightF = 50F;
            this.GroupFooter1.Name = "GroupFooter1";
            // 
            // tableCell19
            // 
            this.tableCell19.Dpi = 100F;
            this.tableCell19.Name = "tableCell19";
            this.tableCell19.StylePriority.UseTextAlignment = false;
            this.tableCell19.Text = "领料单号";
            this.tableCell19.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.tableCell19.Weight = 0.56562659657751879D;
            // 
            // tableCell27
            // 
            this.tableCell27.Dpi = 100F;
            this.tableCell27.Multiline = true;
            this.tableCell27.Name = "tableCell27";
            this.tableCell27.StylePriority.UseTextAlignment = false;
            this.tableCell27.Text = "领料数量\r\n仓    库\r\n仓库名称";
            this.tableCell27.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell27.Weight = 0.54028578701518226D;
            // 
            // tableRow1
            // 
            this.tableRow1.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
                        | DevExpress.XtraPrinting.BorderSide.Right)));
            this.tableRow1.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
                        this.tableCell4,
                        this.tableCell5,
                        this.tableCell6,
                        this.tableCell7,
                        this.tableCell8,
                        this.tableCell10,
                        this.tableCell49});
            this.tableRow1.Dpi = 100F;
            this.tableRow1.Name = "tableRow1";
            this.tableRow1.StylePriority.UseBorders = false;
            this.tableRow1.Weight = 1D;
            // 
            // tableCell1
            // 
            this.tableCell1.Dpi = 100F;
            this.tableCell1.Name = "tableCell1";
            this.tableCell1.StylePriority.UseTextAlignment = false;
            this.tableCell1.Text = "领料单别";
            this.tableCell1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.tableCell1.Weight = 0.56562659657751879D;
            // 
            // table6
            // 
            this.table6.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.table6.Dpi = 100F;
            this.table6.LocationFloat = new DevExpress.Utils.PointFloat(669.8498F, 172.5F);
            this.table6.Name = "table6";
            this.table6.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
                        this.tableRow11});
            this.table6.SizeF = new System.Drawing.SizeF(91.94196F, 75F);
            this.table6.StylePriority.UseBorders = false;
            // 
            // tableRow3
            // 
            this.tableRow3.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
                        this.tableCell12,
                        this.tableCell13,
                        this.tableCell15,
                        this.tableCell16,
                        this.tableCell17,
                        this.tableCell18});
            this.tableRow3.Dpi = 100F;
            this.tableRow3.Name = "tableRow3";
            this.tableRow3.Weight = 1D;
            // 
            // Detail
            // 
            this.Detail.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
                        this.table1,
                        this.barCode1});
            this.Detail.Dpi = 100F;
            this.Detail.HeightF = 75.00001F;
            this.Detail.Name = "Detail";
            // 
            // table4
            // 
            this.table4.Dpi = 100F;
            this.table4.LocationFloat = new DevExpress.Utils.PointFloat(0.6249905F, 0F);
            this.table4.Name = "table4";
            this.table4.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
                        this.tableRow6,
                        this.tableRow7});
            this.table4.SizeF = new System.Drawing.SizeF(761.1667F, 50F);
            // 
            // tableCell24
            // 
            this.tableCell24.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "PP_PrintOverReceiveApply_View.ExamineDes")});
            this.tableCell24.Dpi = 100F;
            this.tableCell24.Name = "tableCell24";
            this.tableCell24.StylePriority.UseTextAlignment = false;
            this.tableCell24.Text = "tableCell24";
            this.tableCell24.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.tableCell24.Weight = 0.56562667644586639D;
            // 
            // tableCell37
            // 
            this.tableCell37.Dpi = 100F;
            this.tableCell37.Name = "tableCell37";
            this.tableCell37.StylePriority.UseTextAlignment = false;
            this.tableCell37.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell37.Weight = 0.2437267234965757D;
            // 
            // tableCell5
            // 
            this.tableCell5.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right)));
            this.tableCell5.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "PP_PrintOverReceiveApply_View.ComponentCode")});
            this.tableCell5.Dpi = 100F;
            this.tableCell5.Name = "tableCell5";
            this.tableCell5.StylePriority.UseBorders = false;
            this.tableCell5.StylePriority.UseTextAlignment = false;
            this.tableCell5.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell5.Weight = 0.59205204296700631D;
            // 
            // tableCell39
            // 
            this.tableCell39.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "PP_PrintOverReceiveApply_View.CraftDes")});
            this.tableCell39.Dpi = 100F;
            this.tableCell39.Name = "tableCell39";
            this.tableCell39.StylePriority.UseTextAlignment = false;
            this.tableCell39.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell39.Weight = 0.46803746465992235D;
            // 
            // tableCell18
            // 
            this.tableCell18.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "PP_PrintOverReceiveApply_View.Department")});
            this.tableCell18.Dpi = 100F;
            this.tableCell18.Name = "tableCell18";
            this.tableCell18.StylePriority.UseTextAlignment = false;
            this.tableCell18.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.tableCell18.Weight = 0.56562667644586639D;
            // 
            // tableCell7
            // 
            this.tableCell7.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right)));
            this.tableCell7.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "PP_PrintOverReceiveApply_View.DemandQty")});
            this.tableCell7.Dpi = 100F;
            this.tableCell7.Name = "tableCell7";
            this.tableCell7.StylePriority.UseBorders = false;
            this.tableCell7.StylePriority.UseTextAlignment = false;
            this.tableCell7.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell7.Weight = 0.56033158279607942D;
            // 
            // tableCell20
            // 
            this.tableCell20.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "PP_PrintOverReceiveApply_View.OverReceiveNo")});
            this.tableCell20.Dpi = 100F;
            this.tableCell20.Name = "tableCell20";
            this.tableCell20.StylePriority.UseTextAlignment = false;
            this.tableCell20.Text = "tableCell20";
            this.tableCell20.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.tableCell20.Weight = 0.565626606263973D;
            // 
            // tableCell33
            // 
            this.tableCell33.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell33.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "PP_PrintOverReceiveApply_View.DemandQty")});
            this.tableCell33.Dpi = 100F;
            this.tableCell33.Name = "tableCell33";
            this.tableCell33.StylePriority.UseBorders = false;
            this.tableCell33.StylePriority.UseTextAlignment = false;
            summary1.Running = DevExpress.XtraReports.UI.SummaryRunning.Report;
            this.tableCell33.Summary = summary1;
            this.tableCell33.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.tableCell33.Weight = 1.7601051630156537D;
            // 
            // tableCell31
            // 
            this.tableCell31.Dpi = 100F;
            this.tableCell31.Name = "tableCell31";
            this.tableCell31.StylePriority.UseTextAlignment = false;
            this.tableCell31.Text = "序号";
            this.tableCell31.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell31.Weight = 0.21157011522477634D;
            // 
            // tableCell9
            // 
            this.tableCell9.Dpi = 100F;
            this.tableCell9.Name = "tableCell9";
            this.tableCell9.StylePriority.UseTextAlignment = false;
            this.tableCell9.Text = "工厂";
            this.tableCell9.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.tableCell9.Weight = 0.56562661832951944D;
            // 
            // tableCell43
            // 
            this.tableCell43.Dpi = 100F;
            this.tableCell43.Name = "tableCell43";
            this.tableCell43.StylePriority.UseTextAlignment = false;
            this.tableCell43.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell43.Weight = 0.2437267234965757D;
            // 
            // tableCell48
            // 
            this.tableCell48.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Right | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell48.Dpi = 100F;
            this.tableCell48.Multiline = true;
            this.tableCell48.Name = "tableCell48";
            this.tableCell48.StylePriority.UseBorders = false;
            this.tableCell48.StylePriority.UseTextAlignment = false;
            this.tableCell48.Text = "\r\n\r\n物料二维码";
            this.tableCell48.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopCenter;
            this.tableCell48.Weight = 1D;
            // 
            // tableRow9
            // 
            this.tableRow9.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right)));
            this.tableRow9.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
                        this.tableCell41,
                        this.tableCell42,
                        this.tableCell43,
                        this.tableCell44,
                        this.tableCell45,
                        this.tableCell46,
                        this.tableCell50});
            this.tableRow9.Dpi = 100F;
            this.tableRow9.Name = "tableRow9";
            this.tableRow9.StylePriority.UseBorders = false;
            this.tableRow9.Weight = 1D;
            // 
            // tableCell36
            // 
            this.tableCell36.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "PP_PrintOverReceiveApply_View.Specification")});
            this.tableCell36.Dpi = 100F;
            this.tableCell36.Name = "tableCell36";
            this.tableCell36.StylePriority.UseTextAlignment = false;
            this.tableCell36.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell36.Weight = 0.59205204296700631D;
            // 
            // label2
            // 
            this.label2.BorderWidth = 0F;
            this.label2.Dpi = 100F;
            this.label2.Font = new System.Drawing.Font("宋体", 16F);
            this.label2.LocationFloat = new DevExpress.Utils.PointFloat(0.2083302F, 47.29169F);
            this.label2.Multiline = true;
            this.label2.Name = "label2";
            this.label2.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.label2.SizeF = new System.Drawing.SizeF(761.5834F, 26.66667F);
            this.label2.StylePriority.UseBorderWidth = false;
            this.label2.StylePriority.UseFont = false;
            this.label2.StylePriority.UseTextAlignment = false;
            this.label2.Text = "主机超额领料单";
            this.label2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // tableCell32
            // 
            this.tableCell32.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell32.Dpi = 100F;
            this.tableCell32.Name = "tableCell32";
            this.tableCell32.StylePriority.UseBorders = false;
            this.tableCell32.StylePriority.UseTextAlignment = false;
            this.tableCell32.Text = "签字";
            this.tableCell32.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.tableCell32.Weight = 0.23989483698434622D;
            // 
            // table2
            // 
            this.table2.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
                        | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.table2.Dpi = 100F;
            this.table2.LocationFloat = new DevExpress.Utils.PointFloat(0.4166603F, 97.49998F);
            this.table2.Name = "table2";
            this.table2.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
                        this.tableRow2,
                        this.tableRow4,
                        this.tableRow3});
            this.table2.SizeF = new System.Drawing.SizeF(761.3751F, 74.99998F);
            this.table2.StylePriority.UseBorders = false;
            this.table2.StylePriority.UseTextAlignment = false;
            this.table2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // TopMargin
            // 
            this.TopMargin.Dpi = 100F;
            this.TopMargin.HeightF = 10F;
            this.TopMargin.Name = "TopMargin";
            this.TopMargin.Visible = false;
            // 
            // tableRow5
            // 
            this.tableRow5.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
                        this.tableCell31,
                        this.tableCell26,
                        this.tableCell29,
                        this.tableCell27,
                        this.tableCell30,
                        this.tableCell28});
            this.tableRow5.Dpi = 100F;
            this.tableRow5.Name = "tableRow5";
            this.tableRow5.Weight = 2.7916659545898437D;
            // 
            // tableCell17
            // 
            this.tableCell17.Dpi = 100F;
            this.tableCell17.Name = "tableCell17";
            this.tableCell17.StylePriority.UseTextAlignment = false;
            this.tableCell17.Text = "部门";
            this.tableCell17.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.tableCell17.Weight = 0.5656266331996973D;
            // 
            // tableCell51
            // 
            this.tableCell51.Dpi = 100F;
            this.tableCell51.Name = "tableCell51";
            this.tableCell51.RowSpan = 3;
            this.tableCell51.StylePriority.UseTextAlignment = false;
            this.tableCell51.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell51.Weight = 0.34992178809468938D;
            // 
            // tableCell41
            // 
            this.tableCell41.Dpi = 100F;
            this.tableCell41.Name = "tableCell41";
            this.tableCell41.StylePriority.UseTextAlignment = false;
            this.tableCell41.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell41.Weight = 0.22244391808174957D;
            // 
            // tableRow7
            // 
            this.tableRow7.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
                        this.tableCell32,
                        this.tableCell34});
            this.tableRow7.Dpi = 100F;
            this.tableRow7.Name = "tableRow7";
            this.tableRow7.Weight = 1D;
            // 
            // table3
            // 
            this.table3.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.table3.Dpi = 100F;
            this.table3.LocationFloat = new DevExpress.Utils.PointFloat(0.8333206F, 172.5F);
            this.table3.Name = "table3";
            this.table3.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
                        this.tableRow5});
            this.table3.SizeF = new System.Drawing.SizeF(669.0165F, 75F);
            this.table3.StylePriority.UseBorders = false;
            // 
            // table1
            // 
            this.table1.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.table1.Dpi = 100F;
            this.table1.LocationFloat = new DevExpress.Utils.PointFloat(0.2452393F, 0F);
            this.table1.Name = "table1";
            this.table1.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
                        this.tableRow1,
                        this.tableRow9,
                        this.tableRow8});
            this.table1.SizeF = new System.Drawing.SizeF(761.5464F, 75F);
            this.table1.StylePriority.UseBorders = false;
            // 
            // tableCell44
            // 
            this.tableCell44.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "PP_PrintOverReceiveApply_View.OutWarehouse")});
            this.tableCell44.Dpi = 100F;
            this.tableCell44.Name = "tableCell44";
            this.tableCell44.StylePriority.UseTextAlignment = false;
            this.tableCell44.Text = "tableCell44";
            this.tableCell44.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell44.Weight = 0.56033158279607942D;
            // 
            // tableCell50
            // 
            this.tableCell50.Dpi = 100F;
            this.tableCell50.Name = "tableCell50";
            this.tableCell50.RowSpan = 3;
            this.tableCell50.StylePriority.UseTextAlignment = false;
            this.tableCell50.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell50.Weight = 0.34992178809468938D;
            // 
            // label4
            // 
            this.label4.BorderWidth = 0F;
            this.label4.Dpi = 100F;
            this.label4.Font = new System.Drawing.Font("宋体", 20F);
            this.label4.LocationFloat = new DevExpress.Utils.PointFloat(0.4166603F, 10.00001F);
            this.label4.Multiline = true;
            this.label4.Name = "label4";
            this.label4.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.label4.SizeF = new System.Drawing.SizeF(761.5834F, 37.29168F);
            this.label4.StylePriority.UseBorderWidth = false;
            this.label4.StylePriority.UseFont = false;
            this.label4.StylePriority.UseTextAlignment = false;
            this.label4.Text = "浙江西子富沃德电机有限公司";
            this.label4.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // BottomMargin
            // 
            this.BottomMargin.Dpi = 100F;
            this.BottomMargin.HeightF = 0F;
            this.BottomMargin.Name = "BottomMargin";
            this.BottomMargin.Visible = false;
            // 
            // label1
            // 
            this.label1.Dpi = 100F;
            this.label1.LocationFloat = new DevExpress.Utils.PointFloat(0.2083302F, 73.95837F);
            this.label1.Name = "label1";
            this.label1.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.label1.SizeF = new System.Drawing.SizeF(91.58337F, 23.5416F);
            this.label1.StylePriority.UseTextAlignment = false;
            this.label1.Text = "制表日期:";
            this.label1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // tableCell26
            // 
            this.tableCell26.Dpi = 100F;
            this.tableCell26.Multiline = true;
            this.tableCell26.Name = "tableCell26";
            this.tableCell26.StylePriority.UseTextAlignment = false;
            this.tableCell26.Text = "材料品号\r\n品    名\r\n规    格";
            this.tableCell26.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell26.Weight = 0.57087175406547963D;
            // 
            // tableCell4
            // 
            this.tableCell4.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right)));
            this.tableCell4.Dpi = 100F;
            this.tableCell4.Multiline = true;
            this.tableCell4.Name = "tableCell4";
            this.tableCell4.StylePriority.UseBorders = false;
            this.tableCell4.StylePriority.UseTextAlignment = false;
            summary2.Func = DevExpress.XtraReports.UI.SummaryFunc.RecordNumber;
            summary2.Running = DevExpress.XtraReports.UI.SummaryRunning.Report;
            this.tableCell4.Summary = summary2;
            this.tableCell4.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell4.Weight = 0.22244391808174957D;
            // 
            // tableRow6
            // 
            this.tableRow6.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
                        this.tableCell25,
                        this.tableCell33});
            this.tableRow6.Dpi = 100F;
            this.tableRow6.Name = "tableRow6";
            this.tableRow6.Weight = 1D;
            // 
            // label3
            // 
            this.label3.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "calculatedField1", "{0:yyyy-MM-dd}")});
            this.label3.Dpi = 100F;
            this.label3.LocationFloat = new DevExpress.Utils.PointFloat(91.79171F, 73.95837F);
            this.label3.Name = "label3";
            this.label3.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.label3.SizeF = new System.Drawing.SizeF(138.5417F, 23F);
            this.label3.StylePriority.UseTextAlignment = false;
            this.label3.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // calculatedField1
            // 
            this.calculatedField1.Expression = "Now()";
            this.calculatedField1.Name = "calculatedField1";
            // 
            // tableCell6
            // 
            this.tableCell6.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right)));
            this.tableCell6.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "PP_PrintOverReceiveApply_View.ComponentUnit")});
            this.tableCell6.Dpi = 100F;
            this.tableCell6.Name = "tableCell6";
            this.tableCell6.StylePriority.UseBorders = false;
            this.tableCell6.StylePriority.UseTextAlignment = false;
            this.tableCell6.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell6.Weight = 0.2437267234965757D;
            // 
            // tableCell22
            // 
            this.tableCell22.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "PP_PrintOverReceiveApply_View.ProductionLineCode")});
            this.tableCell22.Dpi = 100F;
            this.tableCell22.Name = "tableCell22";
            this.tableCell22.StylePriority.UseTextAlignment = false;
            this.tableCell22.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.tableCell22.Weight = 0.57011569428202225D;
            // 
            // tableCell28
            // 
            this.tableCell28.Dpi = 100F;
            this.tableCell28.Multiline = true;
            this.tableCell28.Name = "tableCell28";
            this.tableCell28.StylePriority.UseTextAlignment = false;
            this.tableCell28.Text = "批    号\r\n领料说明\r\n备    注\r\n批号说明";
            this.tableCell28.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell28.Weight = 0.44054641735438338D;
            // 
            // tableCell23
            // 
            this.tableCell23.Dpi = 100F;
            this.tableCell23.Name = "tableCell23";
            this.tableCell23.StylePriority.UseTextAlignment = false;
            this.tableCell23.Text = "审核";
            this.tableCell23.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.tableCell23.Weight = 0.5656266331996973D;
            // 
            // tableCell12
            // 
            this.tableCell12.Dpi = 100F;
            this.tableCell12.Name = "tableCell12";
            this.tableCell12.StylePriority.UseTextAlignment = false;
            this.tableCell12.Text = "单据日期";
            this.tableCell12.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.tableCell12.Weight = 0.56562659657751879D;
            // 
            // tableRow2
            // 
            this.tableRow2.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
                        this.tableCell1,
                        this.tableCell2,
                        this.tableCell9,
                        this.tableCell3,
                        this.tableCell11,
                        this.tableCell14});
            this.tableRow2.Dpi = 100F;
            this.tableRow2.Name = "tableRow2";
            this.tableRow2.Weight = 1D;
            // 
            // tableCell16
            // 
            this.tableCell16.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "PP_PrintOverReceiveApply_View.OutSupplier")});
            this.tableCell16.Dpi = 100F;
            this.tableCell16.Name = "tableCell16";
            this.tableCell16.StylePriority.UseTextAlignment = false;
            this.tableCell16.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.tableCell16.Weight = 0.57011569428202225D;
            // 
            // tableCell34
            // 
            this.tableCell34.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell34.Dpi = 100F;
            this.tableCell34.Name = "tableCell34";
            this.tableCell34.StylePriority.UseBorders = false;
            this.tableCell34.StylePriority.UseTextAlignment = false;
            this.tableCell34.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.tableCell34.Weight = 1.7601051630156537D;
            // 
            // tableCell15
            // 
            this.tableCell15.Dpi = 100F;
            this.tableCell15.Name = "tableCell15";
            this.tableCell15.StylePriority.UseTextAlignment = false;
            this.tableCell15.Text = "委外供应商";
            this.tableCell15.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.tableCell15.Weight = 0.56562661832951944D;
            // 
            // tableCell38
            // 
            this.tableCell38.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "PP_PrintOverReceiveApply_View.OutWarehouseDes")});
            this.tableCell38.Dpi = 100F;
            this.tableCell38.Name = "tableCell38";
            this.tableCell38.StylePriority.UseTextAlignment = false;
            this.tableCell38.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell38.Weight = 0.56033158279607942D;
            // 
            // docNums
            // 
            this.docNums.MultiValue = true;
            this.docNums.Name = "docNums";
            this.docNums.ValueInfo = "PR201026004";
            // 
            // tableCell14
            // 
            this.tableCell14.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "PP_PrintOverReceiveApply_View.Remark")});
            this.tableCell14.Dpi = 100F;
            this.tableCell14.Name = "tableCell14";
            this.tableCell14.StylePriority.UseTextAlignment = false;
            this.tableCell14.Text = "tableCell14";
            this.tableCell14.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.tableCell14.Weight = 0.56562667644586639D;
            // 
            // tableCell11
            // 
            this.tableCell11.Dpi = 100F;
            this.tableCell11.Name = "tableCell11";
            this.tableCell11.StylePriority.UseTextAlignment = false;
            this.tableCell11.Text = "备注";
            this.tableCell11.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.tableCell11.Weight = 0.5656266331996973D;
            // 
            // sqlDataSource1
            // 
            this.sqlDataSource1.ConnectionName = "10.18.0.94_XZ_WMS_Connection";
            msSqlConnectionParameters1.AuthorizationType = DevExpress.DataAccess.ConnectionParameters.MsSqlAuthorizationType.SqlServer;
            msSqlConnectionParameters1.DatabaseName = "XZ_WMS";
            msSqlConnectionParameters1.Password = "Xzfwd@1234";
            msSqlConnectionParameters1.ServerName = "10.18.0.94";
            msSqlConnectionParameters1.UserName = "sa";
            this.sqlDataSource1.ConnectionParameters = msSqlConnectionParameters1;
            this.sqlDataSource1.Name = "sqlDataSource1";
            table5.MetaSerializable = "30|30|125|480";
            table5.Name = "PP_PrintOverReceiveApply_View";
            allColumns1.Table = table5;
            selectQuery1.Columns.Add(allColumns1);
            selectQuery1.FilterString = "[PP_PrintOverReceiveApply_View.OverReceiveNo] In (?docNums)";
            selectQuery1.Name = "PP_PrintOverReceiveApply_View";
            queryParameter1.Name = "docNums";
            queryParameter1.Type = typeof(DevExpress.DataAccess.Expression);
            queryParameter1.Value = new DevExpress.DataAccess.Expression("[Parameters.docNums]", typeof(string));
            selectQuery1.Parameters.Add(queryParameter1);
            selectQuery1.Tables.Add(table5);
            this.sqlDataSource1.Queries.AddRange(new DevExpress.DataAccess.Sql.SqlQuery[] {
                        selectQuery1});
            this.sqlDataSource1.ResultSchemaSerializable = resources.GetString("sqlDataSource1.ResultSchemaSerializable");
            // 
            // tableCell40
            // 
            this.tableCell40.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "PP_PrintOverReceiveApply_View.BatchDes")});
            this.tableCell40.Dpi = 100F;
            this.tableCell40.Name = "tableCell40";
            this.tableCell40.StylePriority.UseTextAlignment = false;
            this.tableCell40.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell40.Weight = 0.45531009144238332D;
            // 
            // tableCell10
            // 
            this.tableCell10.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right)));
            this.tableCell10.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "PP_PrintOverReceiveApply_View.BatchNo")});
            this.tableCell10.Dpi = 100F;
            this.tableCell10.Name = "tableCell10";
            this.tableCell10.StylePriority.UseBorders = false;
            this.tableCell10.StylePriority.UseTextAlignment = false;
            this.tableCell10.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell10.Weight = 0.45531009144238332D;
            // 
            // tableRow8
            // 
            this.tableRow8.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
                        this.tableCell35,
                        this.tableCell36,
                        this.tableCell37,
                        this.tableCell38,
                        this.tableCell39,
                        this.tableCell40,
                        this.tableCell51});
            this.tableRow8.Dpi = 100F;
            this.tableRow8.Name = "tableRow8";
            this.tableRow8.Weight = 1D;
            // 
            // GroupHeader2
            // 
            this.GroupHeader2.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
                        this.table6,
                        this.label3,
                        this.label2,
                        this.label1,
                        this.label4,
                        this.table2,
                        this.table3});
            this.GroupHeader2.Dpi = 100F;
            this.GroupHeader2.HeightF = 247.5F;
            this.GroupHeader2.Name = "GroupHeader2";
            // 
            // tableCell21
            // 
            this.tableCell21.Dpi = 100F;
            this.tableCell21.Name = "tableCell21";
            this.tableCell21.StylePriority.UseTextAlignment = false;
            this.tableCell21.Text = "工作中心";
            this.tableCell21.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.tableCell21.Weight = 0.56562661832951944D;
            // 
            // tableCell2
            // 
            this.tableCell2.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "PP_PrintOverReceiveApply_View.OrderCategory")});
            this.tableCell2.Dpi = 100F;
            this.tableCell2.Name = "tableCell2";
            this.tableCell2.StylePriority.UseTextAlignment = false;
            this.tableCell2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.tableCell2.Weight = 0.565626606263973D;
            // 
            // OverReceiveApply
            // 
            this.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
                        this.TopMargin,
                        this.Detail,
                        this.BottomMargin,
                        this.GroupHeader2,
                        this.GroupFooter1});
            this.CalculatedFields.AddRange(new DevExpress.XtraReports.UI.CalculatedField[] {
                        this.calculatedField1});
            this.ComponentStorage.AddRange(new System.ComponentModel.IComponent[] {
                        this.sqlDataSource1});
            this.DataMember = "PP_PrintOverReceiveApply_View";
            this.DataSource = this.sqlDataSource1;
            this.Dpi = 100F;
            this.Font = new System.Drawing.Font("宋体", 10.2F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Margins = new System.Drawing.Printing.Margins(30, 35, 10, 0);
            this.Name = "OverReceiveApply";
            this.PageHeight = 1169;
            this.PageWidth = 827;
            this.PaperKind = System.Drawing.Printing.PaperKind.A4;
            this.Parameters.AddRange(new DevExpress.XtraReports.Parameters.Parameter[] {
                        this.docNums});
            this.Version = "16.1";
            ((System.ComponentModel.ISupportInitialize)(this.table6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.table4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.table2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.table3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.table1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();
        }
    }
}
