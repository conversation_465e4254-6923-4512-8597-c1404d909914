using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Web;
using System.Web.Http;
using HZ.Core.Http;
using HZ.WMS.Application.MD;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.Sys;
using HZ.WMS.WebAPI.Controllers;
using HZ.Core.Office;
using HZ.WMS.Application;

namespace HZ.WMS.WebAPI.Areas.MD.Controllers
{
    /// <summary>
    /// 销售客户地址
    /// </summary>
    public class MD_CustomerAddController : ApiBaseController
    {
        private MD_CustomerAddApp _app = new MD_CustomerAddApp();

        #region 查询分页列表

        /// <summary>
        /// 查询分页列表 
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]string keyword)
        {
            var result = new ResponseData();
            try
            {
                page.Sort = "CustomerCode asc";
                var itemsData = _app.GetPageList(page, x => (string.IsNullOrEmpty(keyword)
                                                            || x.CustomerCode.Contains(keyword)
                                                            || x.CustomerName.Contains(keyword) 
                                                            || x.CustomerAdd.Contains(keyword)
                                                            || x.Contact.Contains(keyword)
                                                            || x.Telephone.Contains(keyword)
                                                            || x.SettlementAdd.Contains(keyword)
                                                            ))?.ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 查询所有信息

        /// <summary>
        /// 查询所有信息 
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetAllList()
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetList()?.ToList();
                result.Data = itemsData;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <returns></returns>
        [HttpDelete]
        public IHttpActionResult Delete([FromBody]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.DeleteByKeys(ids, GetCurrentUser().LoginAccount);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 添加
        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Add([FromBody]MD_CustomerAdd entity)
        {
            var result = new ResponseData();
            try
            {
                Sys_User currLoginUser = GetCurrentUser();
                entity.IsDelete = false ;
                //entity.IsDefault = true;
                entity.CUser = currLoginUser.LoginAccount;
                //entity.MUser = currLoginUser.LoginAccount;
                entity.CTime = DateTime.Now;
                //entity.MTime = DateTime.Now;
                result.Data = _app.Insert(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Update([FromBody]MD_CustomerAdd entity)
        {
            var result = new ResponseData();
            try
            {
                entity.MUser = GetCurrentUser().LoginAccount;
                entity.MTime = DateTime.Now;
                result.Data = _app.Update(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 导出

        /// <summary>
        /// 
        /// </summary>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] string keyword)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetList().Where(x => string.IsNullOrEmpty(keyword)
                                                           || x.CustomerCode.Contains(keyword)
                                                           || x.CustomerName.Contains(keyword)
                                                           || x.CustomerAdd.Contains(keyword)
                                                           || x.Contact.Contains(keyword)
                                                           || x.Telephone.Contains(keyword)
                                                           || x.SettlementAdd.Contains(keyword)
                                                        ).ToList();
                //var itemsData = _app.GetList().ToList();
                List<ExcelColumn<MD_CustomerAdd>> columns = ExcelService.FetchDefaultColumnList<MD_CustomerAdd>();
                string[] ignoreField = new string[] 
                {
                    "IsDelete", "DTime", "DUser","CustomerId","CustomerCode", "CTime", "CUser", "MTime", "MUser"
                };

                List<ExcelColumn<MD_CustomerAdd>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<MD_CustomerAdd> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }

                columns.ForEach((column) =>
                {
                    //if (column.ColumnName == "CTime" || column.ColumnName == "MTime")
                    //{
                    //    column.Formattor = ExcelExportFormatter.DateTimeFormatter;
                    //}
                    if (column.ColumnName == "IsDefault")
                    {
                        column.Formattor = ExcelExportFormatter.IsOrNoFormatterForBoolean;
                    }

                });

                return ExportToExcelFile(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }
        #endregion

    }
}
