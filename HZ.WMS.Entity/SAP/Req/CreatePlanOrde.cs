using System.ComponentModel;

namespace HZ.WMS.Entity.Produce.Req
{
    public class CreatePlanOrde
    {
        [Description("消息类型")] 
        public string ETYPE { get; set; }

        [Description("消息文本")] 
        public string EMSG { get; set; }

        public Table001058[] DataIn { get; set; }

        public Table001058[] DataOut { get; set; }

        public class Table001058
        {
            [Description("工厂")] 
            public string WERKS { get; set; }

            [Description("销售订单号")] 
            public string VBELN { get; set; }

            [Description("销售订单行")] 
            public string POSNR { get; set; }

            [Description("交货日期")] 
            public string LFDAT { get; set; }

            [Description("生产版本")] 
            public string VERID { get; set; }

            [Description("物料编码")] 
            public string MATNR { get; set; }

            [Description("计划单号")] 
            public string PLNUM { get; set; }

            [Description("状态")] 
            public string ETYPE { get; set; }

            [Description("消息")] 
            public string EMSG { get; set; }
        }
        
        
    }
}