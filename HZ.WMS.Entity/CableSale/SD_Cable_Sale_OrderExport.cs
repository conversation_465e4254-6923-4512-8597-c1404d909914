using System;
using System.Collections.Generic;
using System.ComponentModel;
using SqlSugar;

namespace AOS.OMS.Entity.Sale
{
    /// <summary>
    /// 订单类
    /// </summary>
    public class SD_Cable_Sale_OrderExport
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Description("序号")]
        [SugarColumn(IsPrimaryKey = true)]
        public string SequenceNo { get; set; }
        
        /// <summary>
        /// 发货时间
        /// </summary>
        [Description("发货时间")]
        public DateTime? DeliveryTime { get; set; }
        
        /// <summary>
        /// 合同号
        /// </summary>
        [Description("合同号")]
        public string ContractNo { get; set; }
        
        /// <summary>
        /// 物料编码
        /// </summary>
        [Description("物料编码")]
        public string MaterialCode { get; set; }
        
        /// <summary>
        /// 客户订单号
        /// </summary>
        [Description("客户订单号")]
        public string CustomerOrderNum { get; set; }
        
        /// <summary>
        /// 梯型
        /// </summary>
        [Description("梯型")]
        public string Trapezium { get; set; }
        
        /// <summary>
        /// 批号
        /// </summary>
        [Description("批号")]
        public string BatchNo { get; set; }
        
        /// <summary>
        /// 层站门
        /// </summary>
        [Description("层站门")]
        public string FloorStationDoor { get; set; }
        
        /// <summary>
        /// 订单类型
        /// </summary>
        [Description("订单类型")]
        public string OrderTypeName { get; set; }
        
        /// <summary>
        /// 客户名称
        /// </summary>
        [Description("客户名称")]
        public string CustomerName { get; set; }
        
        /// <summary>
        /// 木箱尺寸
        /// </summary>
        [Description("木箱尺寸")]
        public string WoodenBoxSize { get; set; }
        
        /// <summary>
        /// 木盘尺寸
        /// </summary>
        [Description("木盘尺寸")]
        public string WoodenTraySize { get; set; }
        
        /// <summary>
        /// 木箱刷字
        /// </summary>
        [Description("木箱刷字")]
        public string Print { get; set; }
        
        /// <summary>
        /// 预估重量
        /// </summary>
        [Description("预估重量")]
        public decimal? EstimatedWeight { get; set; }
        
        /// <summary>
        /// 主件号
        /// </summary>
        [Description("主件号")]
        public string PartCode { get; set; }
        
        /// <summary>
        /// 电梯类型
        /// </summary>
        [Description("电梯类型")]
        public string ElevatorType { get; set; }
        
        
    }
}
