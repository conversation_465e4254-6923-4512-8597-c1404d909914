using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.SAP
{
    /// <summary>
    ///SAP中间库-仓库
    /// </summary>
    [SugarTable("XZ_SAP_T001L")]
    public class XZ_SAP_T001L
    {
        /// <summary>
        /// 库存编码
        /// </summary>
        [Description("库存编码")]
        public string LGORT { get; set; }

        /// <summary>
        /// 库存名称
        /// </summary>
        [Description("库存名称")]
        public string LGOBE { get; set; }
    }
}
