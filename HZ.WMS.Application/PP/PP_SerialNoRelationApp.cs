using SqlSugar;
using HZ.WMS.Application.MD;
using HZ.WMS.Application.SAP;
using HZ.WMS.Application.Sys;
using HZ.WMS.Entity.PP;
using HZ.WMS.Entity.PP.Dto;
using HZ.WMS.Entity.SAP;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Application.PP
{
    /// <summary>
    /// 生产投料
    /// </summary>
    public class PP_SerialNoRelationApp : BaseApp<PP_SerialNoRelation>
    {
        private PP_SerialNoRelationDetailApp detailApp = new PP_SerialNoRelationDetailApp();
        private Sys_SAPCompanyInfoApp sapApp = new Sys_SAPCompanyInfoApp();

        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public PP_SerialNoRelationApp() : base()
        {
        }

        #endregion

        #region 增加

        public bool Add(PP_SerialNoRelation productionFeeding, List<PP_SerialNoRelationDetail> productionFeedingDetails)
        {
            var mark = false;
            try
            {
                //this.DbContext.Ado.BeginTran();
                this.DbContext.Ado.BeginTran();
                base.Insert(productionFeeding);
                detailApp.Insert(productionFeedingDetails);
                this.DbContext.Ado.CommitTran();
                mark = true;
            }
            catch (Exception ex)
            {
                mark = false;
                this.DbContext.Ado.RollbackTran();
                throw ex;
            }
            return mark;
        }

        #endregion

        #region 删除

        public bool Delete(string user, List<PP_SerialNoRelation> productionFeedings, List<PP_SerialNoRelationDetail> productionFeedingDetails)
        {
            var mark = false;
            try
            {
                //this.DbContext.Ado.BeginTran();
                this.DbContext.Ado.BeginTran();
                base.Delete(productionFeedings, user);
                detailApp.Delete(productionFeedingDetails, user);
                this.DbContext.Ado.CommitTran();
                mark = true;
            }
            catch (Exception ex)
            {
                mark = false;
                this.DbContext.Ado.RollbackTran();
                throw ex;
            }
            return mark;
        }

        #endregion

        #region 编辑

        public bool Edit(PP_SerialNoRelation_Dto productionFeeding, string user)
        {
            bool result = false;
            try
            {
                var entity = new PP_SerialNoRelation
                {
                    ID = productionFeeding.ID,
                    ScanningCode = productionFeeding.ScanningCode,
                    SerialNo = productionFeeding.SerialNo,
                    DocNum = productionFeeding.DocNum,
                    ProductionOrderNo = productionFeeding.ProductionOrderNo,
                    FactoryCode = productionFeeding.FactoryCode,
                    MaterialNo = productionFeeding.MaterialNo,
                    MaterialName = productionFeeding.MaterialName,
                    OrderType = productionFeeding.OrderType,
                    OrderQty = productionFeeding.OrderQty,
                    Unit = productionFeeding.Unit,
                    ContractNo = productionFeeding.ContractNo,
                    SalesOrderNo = productionFeeding.SalesOrderNo,
                    Shippers = productionFeeding.Shippers,
                    ProductionLineCode = productionFeeding.ProductionLineCode,
                    ProductionLineDes = productionFeeding.ProductionLineDes,
                    ProductionScheduler = productionFeeding.ProductionScheduler,
                    DeliveryTime = productionFeeding.DeliveryTime,
                    StartTime = productionFeeding.StartTime,
                    Remark = productionFeeding.Remark,
                    CUser = productionFeeding.CUser,
                    CTime = productionFeeding.CTime,
                    MUser = user,
                };

                var addList = new List<PP_SerialNoRelationDetail>();
                var updateList = new List<PP_SerialNoRelationDetail>();
                foreach (var item in productionFeeding.DetailList)
                {
                    item.DocNum = productionFeeding.DocNum;
                    if (string.IsNullOrEmpty(item.ID))
                    {
                        item.CUser = user;
                        addList.Add(item);
                    }
                    else
                    {
                        item.MUser = user;
                        updateList.Add(item);
                    }
                }
                var deleteList = detailApp.GetListByKeys(productionFeeding.DelDetailIds);
                if (deleteList != null && deleteList.Count > 0)
                {
                    deleteList.ForEach(item =>
                    {
                        item.DUser = user;
                        item.DTime = DateTime.Now;
                        item.IsDelete = true;
                    });
                    updateList.AddRange(deleteList);
                }

                //this.DbContext.Ado.BeginTran();
                this.DbContext.Ado.BeginTran();
                base.Update(entity);
                detailApp.Update(updateList);
                detailApp.Insert(addList);
                this.DbContext.Ado.CommitTran();
                result = true;
            }
            catch (Exception ex)
            {
                result = false;
                this.DbContext.Ado.RollbackTran();
                throw ex;
            }
            return result;
        }

        #endregion

        
    }
}
