using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.MM.ViewModel
{
    /// <summary>
    /// 委外领料申请单关联委外发料信息
    /// </summary>
    public class MM_DispatchForPickingApply_View
    {
        /// <summary> 
        /// 明细ID
        /// </summary> 
        [SugarColumn(IsPrimaryKey = true)]
        [Description("明细ID")]
        public string DetailID { get; set; }

        /// <summary> 
        /// 单号
        /// </summary> 
        [Description("单号")]
        public string DocNum { get; set; }

        /// <summary> 
        /// 领料申请单单号
        /// </summary> 
        [Description("领料申请单单号")]
        public string SubcontractingApplicationNum { get; set; }

        /// <summary> 
        /// 行号
        /// </summary> 
        [Description("行号")]
        public int? BaseLine { get; set; }

        /// <summary> 
        /// 采购订单
        /// </summary> 
        [Description("采购订单")]
        public string BaseNum { get; set; }

        /// <summary> 
        /// 物料编号
        /// </summary> 
        [Description("物料编号")]
        public string ItemCode { get; set; }

        /// <summary> 
        /// 物料名称
        /// </summary> 
        [Description("物料名称")]
        public string ItemName { get; set; }

        /// <summary> 
        /// 供应商编号
        /// </summary> 
        [Description("供应商编号")]
        public string SupplierCode { get; set; }

        /// <summary> 
        /// 供应商名称
        /// </summary> 
        [Description("供应商名称")]
        public string SupplierName { get; set; }

        /// <summary> 
        /// 数量
        /// </summary> 
        [Description("数量")]
        public decimal? Qty { get; set; }

        /// <summary> 
        /// 发料数量
        /// </summary> 
        [Description("发料数量")]
        public decimal? OutsourcingDispatchQty { get; set; }

        /// <summary> 
        /// 单位
        /// </summary> 
        [Description("单位")]
        public string Unit { get; set; }

        /// <summary> 
        /// 仓库编号
        /// </summary> 
        [Description("仓库编号")]
        public string WhsCode { get; set; }

        /// <summary> 
        /// 仓库名称
        /// </summary> 
        [Description("仓库")]
        public string WhsName { get; set; }

        /// <summary> 
        /// 区域编号
        /// </summary> 
        [Description("区域编号")]
        public string RegionCode { get; set; }

        /// <summary> 
        /// 区域名称
        /// </summary> 
        [Description("区域")]
        public string RegionName { get; set; }

        /// <summary> 
        /// 库位编号
        /// </summary> 
        [Description("库位编号")]
        public string BinLocationCode { get; set; }

        /// <summary> 
        /// 库位名称
        /// </summary> 
        [Description("库位")]
        public string BinLocationName { get; set; }

        /// <summary>
        /// 产品编号
        /// </summary>
        [Description("产品编号")]
        public string MatnrCode { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        [Description("产品名称")]
        public string MatnrName { get; set; }

        /// <summary>
        /// 产品数量
        /// </summary>
        [Description("产品数量")]
        public decimal? MatnrQty { get; set; }
    }
}
