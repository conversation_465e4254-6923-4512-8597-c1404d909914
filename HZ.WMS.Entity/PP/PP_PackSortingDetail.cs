using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.PP
{
    /// <summary>
    /// 装箱材料分拣明细表
    /// </summary>
    [SugarTable("PP_PackSortingDetail")]
    public class PP_PackSortingDetail : BaseEntity
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Description("主键ID")]
        [SugarColumn(IsPrimaryKey = true)]
        public string ID { get; set; }
        /// <summary>
        /// 扫描码
        /// </summary>
        [Description("扫描码")]
        public string ScanningCode { get; set; }
        /// <summary>
        /// 单号
        /// </summary>
        [Description("单号")]
        public string PackSortingNo { get; set; }
        /// <summary>
        /// 生产订单
        /// </summary>
        [Description("生产订单")]
        public string ProductionOrderNo { get; set; }
        /// <summary>
        /// 预留编号
        /// </summary>
        [Description("预留编号")]
        public string ReservedNo { get; set; }
        /// <summary>
        /// 预留行号
        /// </summary>
        [Description("预留行号")]
        public decimal? ComponentLineNo { get; set; }
        /// <summary>
        /// 组件编码
        /// </summary>
        [Description("组件编码")]
        public string ComponentCode { get; set; }
        /// <summary>
        /// 物料描述
        /// </summary>
        [Description("物料描述")]
        public string MaterialName { get; set; }
        /// <summary>
        /// 需求数量
        /// </summary>
        [Description("需求数量")]
        public decimal? DemandQty { get; set; }
        /// <summary>
        /// 组件单位
        /// </summary>
        [Description("组件单位")]
        public string ComponentUnit { get; set; }
        /// <summary>
        /// 工厂
        /// </summary>
        [Description("工厂")]
        public string FactoryCode { get; set; }
        /// <summary>
        /// 发料库存地
        /// </summary>
        [Description("发料库存地")]
        public string DeliverLocation { get; set; }
        /// <summary>
        /// 销售订单
        /// </summary>
        [Description("销售订单")]
        public string SalesOrderNo { get; set; }
        /// <summary>
        /// 销售订单行项目
        /// </summary>
        [Description("销售订单行项目")]
        public int? SalesOrderLineNo { get; set; }
        /// <summary>
        /// 评估类别，为B时评估类型为01自制02外购
        /// </summary>
        [Description("评估类别")]
        public string AssessmentCategory { get; set; }
        /// <summary>
        /// 评估类型，01自制02外购
        /// </summary>
        [Description("评估类型")]
        public string AssessmentType { get; set; }
        /// <summary>
        /// 特殊库存标识
        /// </summary>
        [Description("特殊库存标识")]
        public string SpecialInventory { get; set; }
        /// <summary>
        /// 是否是倒冲方式
        /// </summary>
        [Description("是否是倒冲方式")]
        public string IsBackflush { get; set; }
        /// <summary>
        /// 是否确认 0：否 1：是
        /// </summary>
        [Description("是否确认")]
        public bool? IsConfirm { get; set; }
    }
}
