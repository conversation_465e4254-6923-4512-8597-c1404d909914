using System;
using System.Collections.Generic;
using System.Linq;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.MD.Import;

namespace HZ.WMS.Application.MD
{
    /// <summary>
    /// 工作中心站点
    /// </summary>
    public class MD_WorkCenterStationApp : BaseApp<MD_WorkCenterStation>
    {
        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public MD_WorkCenterStationApp() : base(){}

        #endregion
        
        #region 导入

        /// <summary>
        /// 导入
        /// </summary>
        /// <param name="excelList">接收的集合</param>
        /// <param name="opUser">当前用户</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool ImportExcelToBaseData(List<MD_WorkCenterStationImport> excelList, string opUser, out string error_message)
        {
            error_message = "";
            var flag = true;
            DateTime date = DateTime.Now;
            var entityList = new List<MD_WorkCenterStation>();

            try
            {
                foreach (var item in excelList)
                {
                    if (string.IsNullOrEmpty(item.WorkCenterName))
                    {
                        error_message = "工作中心名称不能为空";
                        return false;
                    }

                    if (string.IsNullOrEmpty(item.WorkCenterCode))
                    {
                        error_message = "工作中心编码不能为空";
                        return false;
                    }

                    if (string.IsNullOrEmpty(item.StationCode))
                    {
                        error_message = "站点代码不能为空";
                        return false;
                    }

                    if (string.IsNullOrEmpty(item.StationName))
                    {
                        error_message = "站点名称不能为空";
                        return false;
                    }

                    var entity = new MD_WorkCenterStation
                    {
                        Id = Guid.NewGuid().ToString(),
                        WorkCenterName = item.WorkCenterName,
                        WorkCenterCode = item.WorkCenterCode,
                        StationCode = item.StationCode,
                        StationName = item.StationName,
                        Remark = item.Remark,
                        CUser = opUser,
                        CTime = date,
                        IsDelete = false
                    };

                    entityList.Add(entity);
                }

                if (entityList.Count > 0)
                {
                    Insert(entityList);
                }

                return flag;
            }
            catch (Exception ex)
            {
                error_message = ex.Message;
                return false;
            }
        }

        #endregion

        #region 验证方法

        /// <summary>
        /// 验证工作中心站点是否存在
        /// </summary>
        /// <param name="workCenterCode">工作中心编码</param>
        /// <param name="stationCode">站点代码</param>
        /// <returns>是否存在</returns>
        public bool ValidateWorkCenterStationExists(string workCenterCode, string stationCode)
        {
            return this.Any(x => x.WorkCenterCode == workCenterCode && x.StationCode == stationCode && !x.IsDelete);
        }

        /// <summary>
        /// 检查工作中心站点是否被报工站点引用
        /// </summary>
        /// <param name="id">工作中心站点ID</param>
        /// <returns>是否被引用</returns>
        public bool IsReferencedByReportStation(string id)
        {
            // 获取工作中心站点信息
            var workCenterStation = this.GetEntityByKey(id);
            if (workCenterStation == null)
            {
                return false;
            }
            
            // 验证该工作中心站点是否被报工站点引用
            var reportStationApp = new MD_ReportStationApp();
            return reportStationApp.Any(x => 
                x.WorkCenterCode == workCenterStation.WorkCenterCode && 
                x.StationCode == workCenterStation.StationCode && 
                !x.IsDelete);
        }
        
        /// <summary>
        /// 检查工作中心站点列表是否被报工站点引用
        /// </summary>
        /// <param name="ids">工作中心站点ID列表</param>
        /// <returns>被引用的工作中心站点列表</returns>
        public List<string> GetReferencedWorkCenterStations(string[] ids)
        {
            List<string> referencedIds = new List<string>();
            
            foreach (var id in ids)
            {
                if (IsReferencedByReportStation(id))
                {
                    referencedIds.Add(id);
                }
            }
            
            return referencedIds;
        }

        #endregion
    }
} 