using HZ.Core.Logging;
using HZ.Core.Http;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http.Controllers;
using System.Web.Http.Filters;
//using System.Web.Mvc;

using HZ.WMS.Application.Sys;
using HZ.WMS.Entity.Sys;
using System.IO;
using System.Text;
using System.Net.Http;
using HZ.Core.Security;
using System.Web;

namespace HZ.WMS.WebAPI.Filter
{
    /// <summary>
    /// 操作日志
    /// https://blog.csdn.net/xxj_jing/article/details/48806829
    /// </summary>
    public class ApiLoggingFilterAttribute : ActionFilterAttribute
    {
        Sys_LogApp _app = new Sys_LogApp();

        #region 记录请求内容
        /// <summary>
        /// 请求之前
        /// </summary>
        /// <param name="actionContext"></param>
        public override void OnActionExecuting(HttpActionContext actionContext)
        {

            try
            {
                actionContext.Request.Properties["requestTime"] = DateTime.Now;
                var method = actionContext.Request.Method;
                if (method == HttpMethod.Post || method == HttpMethod.Delete)
                {
                    var requestBody = GetRequestPostParms();
                    actionContext.Request.Properties["requestBody"] = requestBody;
                }
            }
            catch (Exception ex)
            {
                // 异常记录文件异常日志
                LogHelper.Instance.LogError("ActionBefore---StackTrace:" + ex.StackTrace.ToString());
            }

            // try
            // {
            //    base.OnActionExecuting(actionContext);
            //
            //     string postContent = GetRequestPostParms();
            //
            //     // 获取请求参数
            //     List<Sys_ApiLogConfig> listLogConfig = new Sys_ApiLogConfigApp().GetList().ToList();
            //     Sys_Log log = new Sys_Log();
            //     string logID = Guid.NewGuid().ToString().ToUpper();
            //     actionContext.Request.Headers.Add("RequestSessionID", logID);
            //     log.LogID = logID;
            //     log.ApiUrl = actionContext.Request.RequestUri.AbsolutePath;
            //     Sys_ApiLogConfig logConfig = listLogConfig.Where(t => t.ApiUrl == log.ApiUrl).ToList().FirstOrDefault();
            //     string logType = System.Configuration.ConfigurationManager.AppSettings["LogType"]??"NONE";
            //
            //     if (logType.ToUpper() == "ALL" || (logType.ToUpper()=="CONFIG" && logConfig != null ))
            //     {
            //         log.ApiModule = logConfig?.BelongModule;
            //         log.ApiDescription = logConfig?.ApiDescription;
            //         log.ApiType = logConfig?.ApiType;
            //
            //         log.RequestType = actionContext.Request.Method.ToString();  // 请求方法
            //         log.RequestParms = Newtonsoft.Json.JsonConvert.SerializeObject(actionContext.ActionArguments);  // 请求参数
            //         
            //
            //         IEnumerable<string> requestUsers = new List<string>();
            //         bool haveUser = actionContext.Request.Headers.TryGetValues("Uid", out requestUsers);
            //         if (haveUser)
            //         {
            //             // 账号信息
            //             log.OperateUser = requestUsers.ToList().FirstOrDefault();
            //         }
            //         else
            //         {
            //             // 不存在账号信息时，获取请求参数中的username
            //         }
            //         log.LogType = 1;    // 1: 业务日志 2：Debug日志
            //                             //获取IP地址(::1   本机): ((System.Web.HttpContextWrapper)actionContext.Request.Properties["MS_HttpContext"]).Request.UserHostAddress;
            //         log.ClientHost = actionContext.Request.Headers.Host;  // 获取Host主机名
            //         log.ClientIP = ((System.Web.HttpContextWrapper)actionContext.Request.Properties["MS_HttpContext"]).Request.UserHostAddress;  // 获取Host主机名
            //         if (actionContext.Request.Headers.UserAgent.Count >= 4)
            //         {
            //             log.ClientBrowser = actionContext.Request.Headers.UserAgent.ToList()[3]?.ToString();
            //             log.ClientOS = actionContext.Request.Headers.UserAgent.ToList()[1]?.ToString();
            //         }
            //         log.Remark = actionContext.Request.Headers.UserAgent?.ToString();
            //         log.CUser = "System";
            //         log.Result = "失败"; // 默认失败，如果执行输出时没有改写就表示失败
            //         new Sys_LogApp().Insert(log);
            //     }
            //
            // }
            // catch(Exception ex)
            // {
            //     // 异常记录文件异常日志
            //     LogHelper.Instance.LogError("ActionBefore---StackTrace:" + ex.StackTrace.ToString());
            // }


        }


        #endregion

        #region 获取响应数据并记录LOG

        /// <summary>
        /// 请求响应
        /// </summary>
        /// <param name="context"></param>

        public override void OnActionExecuted(System.Web.Http.Filters.HttpActionExecutedContext context)
        {
            try
            {

                base.OnActionExecuted(context);

                double executeMillisecond = DateTime.Now.Subtract((DateTime)(context.Request.Properties["requestTime"])).Milliseconds;
                var urlPath = context.Request.RequestUri.AbsolutePath;
                var method = context.Request.Method;
                var account = getCurrentAccount(context.Request);
                if (method == HttpMethod.Post || method == HttpMethod.Delete)
                {
                    var requestBody = context.Request.Properties["requestBody"];
                    var responseBody = GetResponseValues(context);
                    LogHelper.Instance.LogInfo("操作地址" + urlPath + " - 操作人：" + account + " - 执行时间：" + executeMillisecond + "ms 请求参数：" + requestBody + " 返回参数：" + responseBody);
                }

                // base.OnActionExecuted(context);
                //
                //
                //
                // IEnumerable<string> requestSessionIDS = new List<string>();
                // bool haveRequestSessionID = context.Request.Headers.TryGetValues("RequestSessionID", out requestSessionIDS);
                //  
                // if (haveRequestSessionID)
                // {
                //     //log.MTime - log.CTime = 执行时间
                //     var logID = requestSessionIDS.ToList().FirstOrDefault();
                //     Sys_Log log = new Sys_LogApp().GetEntityByKey(logID);
                //
                //     if (log != null)
                //     {
                //         log.MUser = "System";
                //         log.MTime = DateTime.Now;
                //
                //         string responseData = GetResponseValues(context); //Newtonsoft.Json.JsonConvert.SerializeObject(context.Response.RequestMessage);
                //         ResponseData resData = Newtonsoft.Json.JsonConvert.DeserializeObject<ResponseData>(responseData);
                //         if (resData.Code == 2000)
                //         {
                //             log.Result = "成功";
                //         }
                //         log.ResponseData = responseData;
                //         new Sys_LogApp().Update(log);
                //     }
                // }
            }
            catch(Exception ex)
            {
                LogHelper.Instance.LogError("ActionAfter---StackTrace:" + ex.StackTrace.ToString());
            }
            

            



        }


        #endregion

        /* 获取操作人 */
        private string getCurrentAccount(HttpRequestMessage request)
        {
            // var request = System.Web.HttpContext.Current.Request;
            var token = request.Headers.GetValues("X-Token");
            var account = "";
            if (token != null && token.Count() > 0)
            {
                account = TokenUtil.GetUid(token.ElementAtOrDefault(0));
            }
            return account;
        }

        #region 私有方法

        /// <summary>
        /// 读取action返回的result
        /// </summary>
        /// <param name="actionExecutedContext"></param>
        /// <returns></returns>
        private string GetResponseValues(HttpActionExecutedContext actionExecutedContext)
        {
            var stream = actionExecutedContext.Response.Content.ReadAsStreamAsync().Result;
            var encoding = Encoding.UTF8;
            /*
            这个StreamReader不能关闭，也不能dispose， 关了就傻逼了
            因为你关掉后，后面的管道  或拦截器就没办法读取了
            */
            var reader = new StreamReader(stream, encoding);
            var result = reader.ReadToEnd();
            /*
            这里也要注意：   stream.Position = 0; 
            当你读取完之后必须把stream的位置设为开始
            因为request和response读取完以后Position到最后一个位置，交给下一个方法处理的时候就会读不到内容了。
            */
            stream.Position = 0;
            return result;
        }

        /// <summary>
        /// 判断类和方法头上的特性是否要进行Action拦截
        /// </summary>
        /// <param name="actionContext"></param>
        /// <returns></returns>
        //private static bool SkipLogging(HttpActionContext actionContext)
        //{
        //    return actionContext.ActionDescriptor.GetCustomAttributes<NoLogAttribute>().Any() || actionContext.ActionDescriptor.ControllerDescriptor.GetCustomAttributes<NoLogAttribute>().Any();
        //}

        #endregion

        #region 获取POST参数

        public string GetRequestPostParms()
        {
            var request = HttpContext.Current.Request;
            request.GetBufferedInputStream();
            var resStream = request.InputStream;
            var len = (int)resStream.Length; //post数据长度
            var res = string.Empty;
            if (len != 0)
            {
                var inputByts = new byte[len]; //字节数据,用于存储post数据
                resStream.Read(inputByts, 0, len); //将post数据写入byte数组中s
                resStream.Close();
                res = Encoding.UTF8.GetString(inputByts); //转为UTF8编码
            }

            return res;
        }

        #endregion
    }
}