namespace HZ.WMS.Entity.SAP.Req
{
    public class CreatePlanOrderReq
    {
        
        /// <summary>
        /// 计划工厂
        /// </summary>
        public string PLWRK { get; set; }
        
        /// <summary>
        /// 销售订单号
        /// </summary>
        public string KDAUF { get; set; }
        
        /// <summary>
        /// 销售订单行
        /// </summary>
        public string KDPOS { get; set; }
        
        /// <summary>
        /// 物料编码
        /// </summary>
        public string MATNR { get; set; }
        
        /// <summary>
        /// 数量
        /// </summary>
        public string GSMNG { get; set; }
        
        /// <summary>
        /// 单位
        /// </summary>
        public string MEINS { get; set; }
        
        /// <summary>
        /// 开始日期
        /// </summary>
        public string PSTTR { get; set; }
        
        /// <summary>
        /// 结束日期
        /// </summary>
        public string PEDTR { get; set; }
        
        /// <summary>
        /// 生产版本
        /// </summary>
        public string VERID { get; set; }
        
        /// <summary>
        /// 计划订单号
        /// </summary>
        public string PLNUM { get; set; }
        
        /// <summary>
        /// BOM物料编码
        /// </summary>
        public string MATNR_BOM { get; set; }
        
        /// <summary>
        /// BOM物料数量
        /// </summary>
        public string GSMNG_BOM { get; set; }
        
        /// <summary>
        /// BOM物料单位
        /// </summary>
        public string MEINS_BOM { get; set; }
        
        /// <summary>
        /// 消息文本
        /// </summary>
        public string EMSG { get; set; }
        
        
    }
}