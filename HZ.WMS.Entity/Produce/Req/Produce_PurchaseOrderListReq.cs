using System;

namespace HZ.WMS.Entity.Produce.Req
{
    /// <summary>
    /// 采购订单列表查询请求
    /// </summary>
    public class Produce_PurchaseOrderListReq
    {
        /// <summary>
        /// 物料编码
        /// </summary>
        public string MaterialCode { get; set; }

        /// <summary>
        /// 父ID
        /// </summary>
        public string Pid { get; set; }

        /// <summary>
        /// Sap单号
        /// </summary>
        public string SapNo { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// 采购订单号
        /// </summary>
        public string PurchaseOrderNo { get; set; }

        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// 结束日期
        /// </summary>
        public DateTime? EndDate { get; set; }
    }
} 