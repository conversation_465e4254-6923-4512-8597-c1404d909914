using HZ.Core.Http;
using HZ.Core.Office;
using HZ.WMS.Application;
using HZ.WMS.Application.PO;
using HZ.WMS.Application.QM;
using HZ.WMS.Application.Sys;
using HZ.WMS.Entity.PO;
using HZ.WMS.Entity.PO.Parameters;
using HZ.WMS.Entity.Sys;
using HZ.WMS.WebAPI.Controllers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Http;

namespace HZ.WMS.WebAPI.Areas.PO.Controllers
{
    /// <summary>
    /// 采购入库
    /// </summary>
    public class PO_PurchaseReceiptController : ApiBaseController
    {
        #region 初始化

        /// <summary>
        /// 采购入库
        /// </summary>
        private PO_PurchaseReceiptApp _app = new PO_PurchaseReceiptApp();

        #endregion

        #region PC

        #region 查询分页列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="page">分页</param>
        /// <param name="keyword">参数</param>
        /// <param name="dateValue">日期区间</param>
        /// <param name="isPosted">是否过账  1：已过帐 0:未过账</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]string keyword, [FromUri]string Supplier, [FromUri]string InspectionNum, 
            [FromUri]string BaseNum, [FromUri]string Item ,[FromUri] DateTime[] dateValue, [FromUri] bool? isPosted, 
            [FromUri] DateTime[] PostdateValue, [FromUri] DateTime[] ManualPostdateValue)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];

                var querDateTimes1 = FormatProcessor.QueryDateTimesFormat(ManualPostdateValue);
                DateTime fromManualTime = querDateTimes1[0];
                DateTime toManualTime = querDateTimes1[1];
                if (ManualPostdateValue.Length == 0)
                {
                    toManualTime = toManualTime.AddYears(1);
                }


                var querDateTimes2 = FormatProcessor.QueryDateTimesFormat(PostdateValue);
                DateTime fromPostTime = querDateTimes2[0];
                DateTime toPostTime = querDateTimes2[1];

                var itemsData = new List<PO_PurchaseReceipt>();
                if (PostdateValue.Length > 0)
                {
                    page.Sort = "InspectionNum desc,BaseNum,BaseLine";
                    itemsData = _app.GetPageList(page, t => (string.IsNullOrEmpty(keyword)
                                                   || t.DocNum.Contains(keyword)
                                                   || t.WhsCode.Contains(keyword)
                                                   || t.BarCode.Contains(keyword) || t.BatchNum.Contains(keyword)
                                                   || t.Remark.Contains(keyword)
                                                   || t.PostUser.Contains(keyword) || t.CUser.Contains(keyword))
                                                   && (string.IsNullOrEmpty(Supplier) || t.SupplierCode.Contains(Supplier) || t.SupplierName.Contains(Supplier))
                                                   && (string.IsNullOrEmpty(InspectionNum) || t.InspectionNum.Contains(InspectionNum))
                                                   && (string.IsNullOrEmpty(Item) || t.ItemCode.Contains(Item) || t.ItemName.Contains(Item))
                                                   && (string.IsNullOrEmpty(BaseNum) || t.BaseNum.Contains(BaseNum))
                                                   && (t.CTime >= fromTime && t.CTime <= toTime)
                                                   && (t.ManualPostTime >= fromManualTime && t.ManualPostTime <= toManualTime)
                                                   && (t.PostTime >= fromPostTime && t.PostTime <= toPostTime)
                                                   && (isPosted == null || t.IsPosted == isPosted)).ToList();
                }
                else
                {
                    page.Sort = "InspectionNum desc,BaseNum,BaseLine";
                    itemsData = _app.GetPageList(page, t => (string.IsNullOrEmpty(keyword)
                                                   || t.DocNum.Contains(keyword)
                                                   || t.WhsCode.Contains(keyword)
                                                   || t.BarCode.Contains(keyword) || t.BatchNum.Contains(keyword)
                                                   || t.Remark.Contains(keyword)
                                                   || t.PostUser.Contains(keyword) || t.CUser.Contains(keyword))
                                                   && (string.IsNullOrEmpty(Supplier) || t.SupplierCode.Contains(Supplier) || t.SupplierName.Contains(Supplier))
                                                   && (string.IsNullOrEmpty(InspectionNum) || t.InspectionNum.Contains(InspectionNum))
                                                   && (string.IsNullOrEmpty(Item) || t.ItemCode.Contains(Item) || t.ItemName.Contains(Item))
                                                   && (string.IsNullOrEmpty(BaseNum) || t.BaseNum.Contains(BaseNum))
                                                   && (t.CTime >= fromTime && t.CTime <= toTime)
                                                   && (t.ManualPostTime >= fromManualTime && t.ManualPostTime <= toManualTime)
                                                   && (isPosted == null || t.IsPosted == isPosted)).ToList();
                }
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 导出

        /// <summary>
        /// 导出
        /// </summary>
        /// <param name="keyword">参数</param>
        /// <param name="dateValue">日期区间</param>
        /// <param name="isPosted">是否过账 1：已过帐 0：未过账</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] string keyword, [FromUri]string Supplier, [FromUri]string InspectionNum, 
            [FromUri]string Item, [FromUri]string BaseNum, [FromUri] DateTime[] dateValue, [FromUri] string isPosted, [FromUri]List<string> ids,
            [FromUri] DateTime[] PostdateValue, [FromUri] DateTime[] ManualPostdateValue)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = new List<PO_PurchaseReceipt>();
                if (ids == null || ids.Count < 1)
                {
                    var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                    DateTime fromTime = querDateTimes[0];
                    DateTime toTime = querDateTimes[1];

                    var querDateTimes1 = FormatProcessor.QueryDateTimesFormat(ManualPostdateValue);
                    DateTime fromManualTime = querDateTimes1[0];
                    DateTime toManualTime = querDateTimes1[1];
                    if (ManualPostdateValue.Length == 0)
                    {
                        toManualTime = toManualTime.AddYears(1);
                    }

                    var querDateTimes2 = FormatProcessor.QueryDateTimesFormat(PostdateValue);
                    DateTime fromPostTime = querDateTimes2[0];
                    DateTime toPostTime = querDateTimes2[1];
                    if (PostdateValue.Length > 0)
                    {
                        itemsData = _app.GetList(t => (string.IsNullOrEmpty(keyword)
                                               || t.DocNum.Contains(keyword)
                                               || t.WhsCode.Contains(keyword)
                                               || t.BarCode.Contains(keyword) || t.BatchNum.Contains(keyword)
                                               || t.Remark.Contains(keyword)
                                               || t.PostUser.Contains(keyword) || t.CUser.Contains(keyword))
                                               && (string.IsNullOrEmpty(Supplier) || t.SupplierCode.Contains(Supplier) || t.SupplierName.Contains(Supplier))
                                               && (string.IsNullOrEmpty(InspectionNum) || t.InspectionNum.Contains(InspectionNum))
                                               && (string.IsNullOrEmpty(Item) || t.ItemCode.Contains(Item) || t.ItemName.Contains(Item))
                                               && (string.IsNullOrEmpty(BaseNum) || t.BaseNum.Contains(BaseNum))
                                               && (t.CTime >= fromTime && t.CTime <= toTime)
                                               && (t.ManualPostTime >= fromManualTime && t.ManualPostTime <= toManualTime)
                                               &&  (t.PostTime >= fromPostTime && t.PostTime <= toPostTime)
               && (string.IsNullOrEmpty(isPosted) || t.IsPosted == bool.Parse(isPosted))
               ).ToList();
                    }
                    else
                    {
                        itemsData = _app.GetList(t => (string.IsNullOrEmpty(keyword)
                                                                       || t.DocNum.Contains(keyword)
                                                                       || t.WhsCode.Contains(keyword)
                                                                       || t.BarCode.Contains(keyword) || t.BatchNum.Contains(keyword)
                                                                       || t.Remark.Contains(keyword)
                                                                       || t.PostUser.Contains(keyword) || t.CUser.Contains(keyword))
                                                                       && (string.IsNullOrEmpty(Supplier) || t.SupplierCode.Contains(Supplier) || t.SupplierName.Contains(Supplier))
                                                                       && (string.IsNullOrEmpty(InspectionNum) || t.InspectionNum.Contains(InspectionNum))
                                                                       && (string.IsNullOrEmpty(Item) || t.ItemCode.Contains(Item) || t.ItemName.Contains(Item))
                                                                       && (string.IsNullOrEmpty(BaseNum) || t.BaseNum.Contains(BaseNum))
                                                                       && (t.CTime >= fromTime && t.CTime <= toTime)
                                                                       && (t.ManualPostTime >= fromManualTime && t.ManualPostTime <= toManualTime)
                                       && (string.IsNullOrEmpty(isPosted) || t.IsPosted == bool.Parse(isPosted))
                                       ).ToList();
                    }
                }
                else
                {
                    itemsData = _app.GetList(t => ids.Contains(t.PurchaseReceiptID)).ToList();
                }
                //var itemsData = _app.GetList().ToList();
                var columns = ExcelService.FetchDefaultColumnList<PO_PurchaseReceipt>();
                string[] ignoreField = new string[]
                {
                    "PurchaseReceiptID","Line","InspectionLine","BaseType","DeliveryDate","BarCode","BatchNum",
                    "CompanyCode", "FactoryCode", "MovementType", "SpecialInventory","SupplierBatch",
                    "SalesOrderNum", "SalesOrderLine", "EvaluationType", "BaseEntry", "PTime",
                    "WhsCode", "RegionCode", "BinLocationCode","ItmsGrpCode","ItmsGrpName",
                    "SapDocNum","SapLine",
                    "ProjectType","Organization","SAPmark","SAPmessage",
                    "Status", "IsDelete", "DTime", "DUser", "MUser","MTime"
                };

                var ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (var userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                columns.ForEach((column) =>
                {
                    if (column.ColumnName == "IsPosted")
                    {
                        column.Formattor = ExcelExportFormatter.IsOrNoFormatterForBoolean;
                    }
                });

                return ExportToExcelFile<PO_PurchaseReceipt>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #region 过账

        /// <summary>
        /// 过账
        /// </summary>
        /// <param name="entities">接收的集合</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult DoPost([FromBody]List<PO_PurchaseReceipt> entities)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                Sys_User currentUser = GetCurrentUser();


                var PurchaseReceiptIDs = entities.Select(x => x.PurchaseReceiptID).Distinct().ToArray();
                var itemsData1 = _app.GetList(t => PurchaseReceiptIDs.Contains(t.PurchaseReceiptID)&&t.IsPosted==false).ToList();
                bool bPost = _app.DoPost(itemsData1, currentUser.LoginAccount, out error_message);
                if (!bPost)//过账失败==2000，前台处理过账失败后重新刷新界面信息
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                    result.MessageParam = 2010;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2000;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }

        #endregion


        #region 冲销过账

        /// <summary>
        /// 冲销过账
        /// </summary>
        /// <param name="entities">接收的集合</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult PassPost([FromBody]List<PO_PurchaseReceipt> entities)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                Sys_User currentUser = GetCurrentUser();

                var PurchaseReceiptIDs = entities.Select(x => x.PurchaseReceiptID).Distinct().ToArray();
                var itemsData1 = _app.GetList(t => PurchaseReceiptIDs.Contains(t.PurchaseReceiptID) && t.IsPosted == true).ToList();

                bool bPost = _app.PassPost(itemsData1, currentUser.LoginAccount, out error_message);
                if (!bPost)//过账失败==2000，前台处理过账失败后重新刷新界面信息
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                    result.MessageParam = 2010;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2000;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }

        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="entities">接收的集合</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Update([FromBody]List<PO_PurchaseReceipt> entities)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                Sys_User currentUser = GetCurrentUser();

                if (_app.Update(entities)>0)
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2000;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                    result.MessageParam = 2010;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="ids">根据ID进行删除</param>
        /// <returns></returns>
        [HttpDelete]
        public IHttpActionResult Delete([FromBody]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                bool bDelete = _app.Deletes(ids, GetCurrentUser().LoginAccount, out error_message);
                if (!bDelete)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #endregion

        #region Mobile

        #region 查询采购检验关联采购收货信息

        /// <summary>
        /// 查询采购检验关联采购收货信息
        /// </summary>
        /// <param name="keyword">参数</param>
        /// <returns></returns>
        [HttpGet]
        //[AllowAnonymous]
        public IHttpActionResult GetInspectionInfoForMobile([FromUri] string keyword)
        {
            string error_message;
            var result = new ResponseData();
            if (_app.ValidationInspection(keyword, out error_message))//校验
            {
                try
                {
                    var itemsData = _app.GetInspectionForReceipt(keyword, out error_message);
                    if (itemsData !=null && itemsData.Count > 0 )
                    {
                        result.Data = itemsData;
                        result.Code = (int)WMSStatusCode.Success;
                    }
                    else
                    {
                        result.Code = (int)WMSStatusCode.Success;
                        result.Message = error_message;
                    }
                }
                catch (Exception ex)
                {
                    result.Code = (int)WMSStatusCode.UnHandledException;
                    result.Message = ex.InnerException?.Message ?? ex.Message;
                }
            }
            else
            {
                result.Code = (int)WMSStatusCode.Success;
                result.Message = error_message;
            }
            return Json(result);
        }

        #endregion

        #region 保存

        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="Parameters">参数类</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Save([FromBody]PO_PurchaseReceiptParameters Parameters)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                string type = ""; //类型 1:提交失败 2：过账失败
                Sys_User currentUser = GetCurrentUser();
                bool bSubmit = _app.Save(Parameters, currentUser.LoginAccount, out error_message,out type);
                if (!bSubmit && type == "1" )
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
                else if (!bSubmit && type == "2" )
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #endregion

    }
}