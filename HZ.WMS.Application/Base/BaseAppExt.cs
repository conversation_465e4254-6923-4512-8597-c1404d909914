using HZ.WMS.Entity;
using HZ.Core.Extensions;
using System.Collections.Generic;
using System.Linq;
using System;
using SqlSugar;
using SqlSugar;
using DbType = System.Data.DbType;
using System.Data;

namespace HZ.WMS.Application
{

    /// <summary>
    /// 扩展BaseApp基类，增加方法
    /// </summary>
    public static class BaseAppExt
    {

        #region 获取业务单号
        /// <summary>
        /// 调用存储过程，获取唯一业务单号
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="app"></param>
        /// <param name="docType"></param>
        /// <param name="fixedNum"></param>
        /// <returns></returns>
        public static string GetNewDocNum<T>(this BaseApp<T> app, string docType, string fixedNum) where T : BaseEntity, new()
        {
            var pm1 = new SugarParameter("@DocType", docType);
            var pm2 = new SugarParameter("@FixedNum", fixedNum);
            var outPm = new SugarParameter("@BarCode", null, true);
            app.DbContext.Ado.UseStoredProcedure().ExecuteCommand("Proc_WMS_GetNewNum",  pm1, pm2, outPm);
            return outPm.Value + "";
        }


        #endregion



        //#region 获取条码类型
        ///// <summary>
        ///// 
        ///// </summary>
        ///// <typeparam name="T"></typeparam>
        ///// <param name="barCode"></param>
        ///// <returns></returns>
        //public static string GetBarCodeType<T>(string barCode) where T : BaseEntity, new()
        //{
        //    if(IsWmsBarCode<T>(barCode))
        //    {
        //        return GetDocTypeByFixedNum<T>(barCode.ExtractLetterPart());
        //    }

        //    return "";
        //}

        //#endregion

        //#region 获取单据号类型
        ///// <summary>
        ///// 获取单据号的固定前缀
        ///// </summary>
        ///// <param name="docNum"></param>
        ///// <returns></returns>
        //public static string GetDocumentType(string docNum)
        //{
        //    return docNum.ExtractLetterPart(); ;
        //}

        //#endregion

        #region 判断是否正确条码

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="app"></param>
        /// <param name="inputCode"></param>
        /// <returns></returns>
        public static bool IsWmsBarCode<T>(this BaseApp<T> app, string inputCode) where T : BaseEntity, new()
        {
            List<string> barCodeFixedPart = app.GetFixedNumList<T>().Where(t => t.Length == 1).ToList();
            string fixedPart = inputCode.ExtractLetterPart();
            return fixedPart.Length == 1 && barCodeFixedPart.Contains(fixedPart) ? true : false;
        }


        #endregion

        #region 判断是否为正确的单据号

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="app"></param>
        /// <param name="inputCode"></param>
        /// <returns></returns>
        public static bool IsWmsDocument<T>(this BaseApp<T> app, string inputCode) where T : BaseEntity, new()
        {
            List<string> docFixedPart = app.GetFixedNumList<T>().Where(t => t.Length == 2).ToList();
            string fixedPart = inputCode.ExtractLetterPart();
            return fixedPart.Length == 1 && docFixedPart.Contains(fixedPart) ? true : false;
        }

        #endregion



        #region 私有辅助方法

        /// <summary>
        /// 获取配置表中固定前缀
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        private static List<string> GetFixedNumList<T>(this BaseApp<T> app) where T : BaseEntity, new()
        {
            // 直接执行Sql语句
            return app.DbContext.Ado.SqlQuery<string>("SELECT FixedNum FROM [dbo].[MD_NumRules]").ToList();
        }


        /// <summary>
        /// 获取配置表中业务类型
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        private static string GetDocTypeByFixedNum<T>(this BaseApp<T> app, string fixedNum) where T : BaseEntity, new()
        {
            // 直接执行Sql语句
            return app.DbContext.Ado.SqlQuery<string>("SELECT DocType FROM [dbo].[MD_NumRules] WHERE FixedNum =@FixedNum", new SugarParameter("@FixedNum", fixedNum)).FirstOrDefault();
        }

        #endregion

        #region 获取业务单号
        /// <summary>
        /// 批量条码/单号生成,生成规则同存储过程,存储过程生成数量为1,此方法可指定数量
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="app"></param>
        /// <param name="docType"></param>
        /// <param name="fixedNum"></param>
        /// <param name="count">生成条码数量</param>
        /// <returns></returns>
        public static string[] GetNewDocNumExt<T>(this BaseApp<T> app, string docType, string fixedNum, int count = 1) where T : BaseEntity, new()
        {
            string[] result = new string[count];
            //生成单号逻辑:固定+服务器日期+流水
            string sql = @" DECLARE @NN   INT;
                            DECLARE @FlowNum   INT;
                            DECLARE @date   NVARCHAR (20);
                            DECLARE @COUNT   INT;                            
                            SET @COUNT = 0;
                            SET @date = CONVERT (DATE, GETDATE (), 23);
                            SET @BARCODESTRING='';

                            WHILE (@COUNT < @BarCodeCount)
                               BEGIN
                                  IF EXISTS
                                        (SELECT 1
                                         FROM MD_NumRules
                                         WHERE     DocType = @DocType
                                               AND FixedNum = @FixedNum
                                               AND DocDate = @date)
                                     BEGIN
                                        SELECT @NN = NextNumber, @FlowNum = FlowNum
                                        FROM MD_NumRules
                                        WHERE     DocType = @DocType
                                              AND FixedNum = @FixedNum
                                              AND DocDate = @date;
                                        SET @BARCODESTRING=@BARCODESTRING+','+ @FixedNum
                                               + CONVERT (VARCHAR (100), GETDATE (), 112)
                                               + RIGHT (
                                                      REPLICATE ('0', @FlowNum)
                                                    + CAST (@NN AS VARCHAR (10)),
                                                    @FlowNum);
                                        --更新下一编号
                                        SET @NN = @NN + 1;
                                        UPDATE MD_NumRules
                                        SET NextNumber = @NN, MTime = GETDATE ()
                                        WHERE     DocType = @DocType
                                              AND FixedNum = @FixedNum
                                              AND DocDate = @date;
                                     END
                                  ELSE
                                     BEGIN
                                        SET @NN = 1;
                                        SELECT @FlowNum = FlowNum
                                        FROM MD_NumRules
                                        WHERE DocType = @DocType AND FixedNum = @FixedNum;
                                        SET @BARCODESTRING=@BARCODESTRING+','+ @FixedNum
                                               + CONVERT (VARCHAR (100), GETDATE (), 112)
                                               + RIGHT (
                                                      REPLICATE ('0', @FlowNum)
                                                    + CAST (@NN AS VARCHAR (10)),
                                                    @FlowNum);
                                        --更新下一编号
                                        SET @NN = @NN + 1;
                                        UPDATE MD_NumRules
                                        SET NextNumber = @NN, DocDate = @date, MTime = GETDATE ()
                                        WHERE DocType = @DocType AND FixedNum = @FixedNum;
                                     END;

                                  SET @COUNT = @COUNT + 1
                               END;
                               SELECT @BARCODESTRING;";
            SugarParameter barcodeString = new SugarParameter("@BARCODESTRING",null, DbType.String, ParameterDirection.Output, 1000);
            var list = app.DbContext.Ado.SqlQuery<string>(sql, new SugarParameter("@BarCodeCount", count), new SugarParameter("@FixedNum", fixedNum), new SugarParameter("@DocType", docType), barcodeString)?.ToArray();
            return barcodeString.Value?.ToString()?.TrimStart(',')?.Split(',');
        }
        #endregion

        #region 获取业务单号

        public static List<string> GetNewDocNums(this ContentBase app, string docType, string fixedNum, DateTime? date, int numCount, int flowNum)
        {
            List<string> barCodes = new List<string>();
            string currentDate = DateTime.Today.ToString("yyyy-MM-dd");
            if (date != null)
            {
                currentDate = date.Value.ToString("yyyy-MM-dd");
            }

            if (docType == "I")
            {
                barCodes = GenerateBarCodesForTypeI(app, numCount);
            }
            else
            {
                barCodes = GenerateBarCodesForOtherTypes(app, docType, fixedNum, currentDate, numCount, flowNum);
            }

            return barCodes;
        }

        private static List<string> GenerateBarCodesForTypeI(ContentBase app, int numCount)
        {
            var barCodes = new List<string>();
            var enumKey = app.DbContext.Queryable<int>("SELECT [EnumKey] FROM [Sys_Dictionary] WHERE [TypeCode] = 'I'")
                .ToList().FirstOrDefault();

            for (int i = 0; i < numCount; i++)
            {
                barCodes.Add(enumKey.ToString());
                enumKey++;
            }

            app.DbContext.Ado.ExecuteCommand(
                "UPDATE [Sys_Dictionary] SET [EnumKey] = @EnumKey WHERE [TypeCode] = 'I'",
                new SugarParameter("@EnumKey", enumKey));

            return barCodes;
        }

        private static List<string> GenerateBarCodesForOtherTypes(ContentBase app, string docType, string fixedNum,
            string currentDate, int numCount, int flowNum)
        {
            var barCodes = new List<string>();
            int nn;

            var record = app.DbContext.Ado.SqlQuery<dynamic>(
                    "SELECT * FROM [MD_NumRules] WHERE [DocType] = @DocType AND [FixedNum] = @FixedNum AND [DocDate] = @CurrentDate",
                    new SugarParameter("@DocType", docType),
                    new SugarParameter("@FixedNum", fixedNum),
                    new SugarParameter("@CurrentDate", currentDate))
                .FirstOrDefault();

            if (record != null)
            {
                nn = record.NextNumber;
                flowNum = record.FlowNum;
            }
            else
            {
                nn = 1; // 从1开始
                // 插入新纪录
                app.DbContext.Ado.ExecuteCommand(
                    "INSERT INTO dbo.MD_NumRules (RulesID, DocType, FixedNum, FlowNum, NextNumber, DocDate, CTime, MTime) VALUES (NEWID(), @DocType, @FixedNum, @FlowNum, @NextNumber, @DocDate, GETDATE(), GETDATE())",
                    new SugarParameter("@DocType", docType),
                    new SugarParameter("@FixedNum", fixedNum),
                    new SugarParameter("@FlowNum", flowNum),
                    new SugarParameter("@NextNumber", nn),
                    new SugarParameter("@DocDate", currentDate));
            }

            string datePart = DateTime.Now.ToString("yyMMdd");
            for (int i = 0; i < numCount; i++)
            {
                var num = nn.ToString().PadLeft(flowNum, '0');
                var barCode = $"{fixedNum}{datePart}{num}";
                barCodes.Add(barCode);
                nn++;
            }

            // 更新MD_NumRules表
            app.DbContext.Ado.ExecuteCommand(
                "UPDATE MD_NumRules SET NextNumber = @NextNumber, MTime = GETDATE() WHERE DocType = @DocType AND FixedNum = @FixedNum AND DocDate = @CurrentDate",
                new SugarParameter("@NextNumber", nn),
                new SugarParameter("@DocType", docType),
                new SugarParameter("@FixedNum", fixedNum),
                new SugarParameter("@CurrentDate", currentDate));

            return barCodes;
        }

        #endregion

    }

    /// <summary>
    /// 单据类型
    /// </summary>
    public class DocType
    {
        /// <summary>
        /// 采购模块
        /// </summary>
        public const string PO = "PO";

        /// <summary>
        /// 生产模块
        /// </summary>
        public const string PP = "PP";

        /// <summary>
        /// 仓库模块
        /// </summary>
        public const string MM = "MM";

        /// <summary>
        /// 销售模块
        /// </summary>
        public const string SD = "SD";

        /// <summary>
        /// 基础数据
        /// </summary>
        public const string MD = "MD";

        /// <summary>
        /// SAP模块
        /// </summary>
        public const string SAP = "SAP";

        /// <summary>
        /// 品质模块
        /// </summary>
        public const string QM = "QM";

        /// <summary>
        /// 仓库模块
        /// </summary>
        public const string POST_MM = "POST_MM";

        /// <summary>
        /// 销售模块
        /// </summary>
        public const string POST_SD = "POST_SD";

        /// <summary>
        /// 采购模块
        /// </summary>
        public const string POST_PO = "POST_PO";

        ///// <summary>
        ///// 生产模块
        ///// </summary>
        //public const string POST_PP = "POST_PP";

        /// <summary>
        /// 质量模块
        /// </summary>
        public const string POST_QM = "POST_QM";

        /// <summary>
        /// 电缆销售
        /// </summary>
        public const string CS = "CS";

    }

    /// <summary>
    /// 固定前缀
    /// </summary>
    public class DocFixedNumDef
    {
        #region 标签部分

        /// <summary>
        /// 采购标签
        /// </summary>
        public const string PO_BarCode = "M";

        /// <summary>
        /// 自制品标签
        /// </summary>
        public const string PP_BarCode = "P";


        /// <summary>
        /// 自制品标签
        /// </summary>
        public const string MM_BarCode = "O";
        #endregion

        /// <summary>
        /// 批次号
        /// </summary>
        public const string BatchNo = "B";

        #region 单据部分

        #region 基础数据

        /// <summary>
        /// 库存-序列号
        /// </summary>
        public const string MD_Stock = "MS";

        #endregion

        #region 采购

        /// <summary>
        /// 采购交货计划单
        /// </summary>
        public const string PO_DeliveryPlan = "DP";

        /// <summary>
        /// 采购送货单
        /// </summary>
        public const string PO_DeliveryNote = "DN";

        /// <summary>
        /// 采购报检扫描
        /// </summary>
        public const string PO_Inspection_Scan = "PN";

        /// <summary>
        /// 采购质检单
        /// </summary>
        public const string PO_Inspection = "PI";

        /// <summary>
        /// 采购上架扫描单
        /// </summary>
        public const string PO_ShelfScan = "PS";

        /// <summary>
        /// 采购退货扫描单
        /// </summary>
        public const string PO_ReturnScan = "PR";

        /// <summary>
        /// 采购退供质检单(退给供应商)
        /// </summary>
        public const string PO_InspectionScan = "RS";

        /// <summary>
        /// 采购退供区物料移动扫描单
        /// </summary>
        public const string PO_ITransferScan = "IT";

        /// <summary>
        /// 采购收货
        /// </summary>
        public const string PO_PurchaseReceipt = "PU";


        #endregion

        #region 生产

        ///// <summary>
        ///// 生产备货波次单
        ///// </summary>
        //public const string PP_StockingWave = "SW";

        ///// <summary>
        ///// 生产备货扫描单
        ///// </summary>
        //public const string PP_StockingScan = "PS";

        /// <summary>
        /// 生产退货单
        /// </summary>
        public const string PP_ReturnScan = "PR";

        /// <summary>
        /// 生产报工
        /// </summary>
        public const string PP_ProductionReport = "PI";

        /// <summary>
        /// 生产发料扫描单
        /// </summary>
        public const string PP_ProductionFeeding = "PF";

        /// <summary>
        /// 超领料申请单
        /// </summary>
        public const string PP_OverReceive = "PR";

        /// <summary>
        /// 生产物料配送单
        /// </summary>
        public const string PP_MaterialDistribution = "MD";

        /// <summary>
        /// 生产订单
        /// </summary>
        public const string PP_ProductionOrder = "PO";

        /// <summary>
        /// 生产退料申请
        /// </summary>
        public const string PP_ReturnScanApplication = "RA";

        /// <summary>
        /// 生产发料扫描单
        /// </summary>
        public const string PP_PackSorting = "PS";

        /// <summary>
        /// 生产序列号关联
        /// </summary>
        public const string PP_SerialNoRelation = "SR";

        #endregion

        #region 销售

        /// <summary>
        /// 销售交货扫描单
        /// </summary>
        public const string SD_DeliveryScan = "DS";

        /// <summary>
        /// 销售退货扫描单
        /// </summary>
        public const string SD_ReturnScan = "SR";

        /// <summary>
        /// 托运单
        /// </summary>
        public const string SD_ConsignmentNote = "SC";

        /// <summary>
        /// 交运计划
        /// </summary>
        public const string SD_ShippingPlan = "SH";

        /// <summary>
        /// 交运计划
        /// </summary>
        public const string SD_ShippingPlanXS = "XS";
        #endregion

        #region 仓库

        /// <summary>
        /// 物料转移扫描单
        /// </summary>
        public const string MM_TransferScan = "MT";

        /// <summary>
        /// 盘点计划单
        /// </summary>
        public const string MM_TakeStockPlan = "TS";
        
        /// <summary>
        /// 部门领料单
        /// </summary>
        public const string MM_DepRequisition = "MD";

        /// <summary>
        /// 委外领料申请单
        /// </summary>
        public const string MM_PickingApply = "MS";

        /// <summary>
        /// 委外发料
        /// </summary>
        public const string MM_Dispatch = "OD";

        /// <summary>
        /// 设备领料
        /// </summary>
        public const string MM_EquipmentPicking = "MQ";

        /// <summary>
        /// 委外退料
        /// </summary>
        public const string MM_Return = "OR";

        /// <summary>
        /// 委外发料
        /// </summary>
        public const string MM_Warehousing = "OW";
        
        /// <summary>
        /// 磁材委外发料
        /// </summary>
        public const string MM_MagnetsWarehousing = "MO";

        /// <summary>
        /// 调拨申请单
        /// </summary>
        public const string MM_RedeployApply = "MR";

        /// <summary>
        /// 借出单
        /// </summary>
        public const string MM_LendingOrder = "ML";

        /// <summary>
        /// 归还单
        /// </summary>
        public const string MM_LendingOrderReturn = "LR";

        /// <summary>
        /// 供应商返修
        /// </summary>
        public const string MM_SupplierRepair = "SR";


        /// <summary>
        /// 其他入库
        /// </summary>
        public const string MM_OtherIn = "OI";

        /// <summary>
        /// 报废申请
        /// </summary>
        public const string MM_ScrapApplication = "SA";

        #endregion

        #region 品质

        /// <summary>
        /// 采购检验
        /// </summary>
        public const string QM_PurchaseInspection = "QP";

        #endregion

        #endregion
        
        #region 电缆单据部分


        #region 销售

        /// <summary>
        /// 销售交货
        /// </summary>
        public const string CableDelivery = "CD";

        #endregion
        
        
        #region 生产

        /// <summary>
        /// 序列号
        /// </summary>
        public const string SerialNumber = "SN";

        #endregion

        #endregion

        #region SAP单据
        /// <summary>
        /// SAP采购收货通知单
        /// </summary>
        public const string PO_DeliveryNotification = "DN";

        /// <summary>
        /// 仓库模块
        /// </summary>
        public const string POST_MM = "W";

        /// <summary>
        /// 销售模块
        /// </summary>
        public const string POST_SD = "S";

        /// <summary>
        /// 采购模块
        /// </summary>
        public const string POST_PO = "B";

        /// <summary>
        /// 生产模块
        /// </summary>
        public const string POST_PP = "P";


        /// <summary>
        /// 质量模块
        /// </summary>
        public const string POST_QM = "Q";

        #endregion

    }
    /// <summary>
    /// 格式化处理器
    /// </summary>
    public static class FormatProcessor
    {
        /// <summary>
        /// 查询/导出 时间条件格式化
        /// </summary>
        /// <param name="dateTimes"></param>
        /// <param name="isDateTime"></param>
        /// <returns></returns>
        public static DateTime[] QueryDateTimesFormat(DateTime[] dateTimes, bool isDateTime = false)
        {
            DateTime fromTime = new DateTime(2020, 1, 1).Date;
            DateTime toTime = DateTime.Now.AddMonths(1).Date;
            if (dateTimes != null && dateTimes.Length >= 2)
            {
                fromTime = dateTimes[0].Date;
                toTime = dateTimes[1].AddDays(1).Date;
                if (isDateTime)
                {
                    fromTime = dateTimes[0];
                    toTime = dateTimes[1].AddDays(1);
                }
            }
            return new DateTime[] { fromTime, toTime };
        }

        /// <summary>
        /// 当天日期,时间条件格式化,把传入的时间转换为 当前00：00：00 - 23：59：59 而不是新增加一天
        /// </summary>
        /// <param name="dateTimes">日期区间</param>
        /// <param name="isDateTime"></param>
        /// <returns></returns>
        public static DateTime[] QueryDateTimesFormatTodayDate(DateTime[] dateTimes, bool isDateTime = false)
        {
            DateTime fromTime =new DateTime(2020, 1, 1).Date ;
            DateTime toTime = Convert.ToDateTime(DateTime.Now.AddDays(1).ToString("D").ToString()).AddSeconds(-1);
            if (dateTimes != null && dateTimes.Length >= 2)
            {
                fromTime = dateTimes[0].Date;
                toTime = Convert.ToDateTime(dateTimes[1].AddDays(1).ToString("D").ToString()).AddSeconds(-1);
                ;
                if (isDateTime)
                {
                    fromTime = dateTimes[0];
                    toTime = dateTimes[1];
                }
            }
            return new DateTime[] { fromTime, toTime };
        }

        /// <summary>
        /// 查询/导出 时间条件格式化
        /// </summary>
        /// <param name="dateTimes"></param>
        /// <param name="isDateTime"></param>
        /// <returns></returns>
        public static DateTime[] QueryDateTimesFormatAll(DateTime[] dateTimes, bool isDateTime = false)
        {
            DateTime fromTime = new DateTime(1900, 1, 1).Date;
            DateTime toTime = new DateTime(2999, 12, 31).Date;
            if (dateTimes != null && dateTimes.Length >= 2)
            {
                fromTime = dateTimes[0].Date;
                toTime = dateTimes[1].AddDays(1).Date;
                if (isDateTime)
                {
                    fromTime = dateTimes[0];
                    toTime = dateTimes[1].AddDays(1);
                }
            }
            return new DateTime[] { fromTime, toTime };
        }

    }
}
