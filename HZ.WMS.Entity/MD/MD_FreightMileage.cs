using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;
using System.ComponentModel;

namespace HZ.WMS.Entity.MD
{
    /// <summary>
    ///  销售运费里程
    /// </summary>
    public class MD_FreightMileage : BaseEntity
    {
        /// <summary>
        /// ID
        /// </summary>
         [SugarColumn(IsPrimaryKey = true)]
        [Description("ID")]
        public string RegionID  { get; set; }

        /// <summary>
        /// 省编号
        /// </summary>
        [Description("省编号")]
        public string Province  { get; set; }

        /// <summary>
        /// 省名称
        /// </summary>
        [Description("省")]
        public string ProvinceName { get; set; }

        /// <summary>
        /// 市编号
        /// </summary>
        [Description("市市编号")]
        public string City  { get; set; }

        /// <summary>
        /// 市名称
        /// </summary>
        [Description("市")]
        public string CityName { get; set; }

        /// <summary>
        /// 地区编号
        /// </summary>
        [Description("地区编号")]
        public string Region { get; set; }

        /// <summary>
        /// 地区名称
        /// </summary>
        [Description("地区")]
        public string RegionName { get; set; }

        /// <summary>
        /// 运费结算地址
        /// </summary>
        [Description("运费结算地址")]
        public string SettlementAdd  { get; set; }

        /// <summary>
        /// 零担时效(天）
        /// </summary>
        [Description("零担时效(天）")]
        public int Prescription  { get; set; }

        /// <summary>
        /// 供应商编号
        /// </summary>
        [Description("供应商编号")]
        public string SupplierCode  { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        [Description("供应商")]
        public string SupplierName  { get; set; }

        /// <summary>
        /// 结算里程
        /// </summary>
        [Description("结算里程")]
        public int? Mileage  { get; set; }

        /// <summary>
        /// 到达天数
        /// </summary>
        [Description("到达天数")]
        public int? Arrival  { get; set; }

        /// <summary>
        /// 回单天数
        /// </summary>
        [Description("回单天数")]
        public int? Receipt  { get; set; }

        /// <summary>
        /// 是否直发
        /// </summary>
        [Description("是否直发")]
        public bool IsZF{ get; set; }


        /// <summary>
        /// 发货人编号
        /// </summary>
        [Description("发货人编号")]
        public string UserCode { get; set; }


        /// <summary>
        /// 发货人名称
        /// </summary>
        [Description("发货人名称")]
        public string UserName { get; set; }

    }
}
