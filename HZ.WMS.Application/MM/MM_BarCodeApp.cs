using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using HZ.Core.Security;
using HZ.WMS.Entity.MM;
using HZ.Core.Http;
using HZ.Core.Helper;
using HZ.WMS.Entity.MD;
using HZ.WMS.Application.MD;
using HZ.WMS.Entity;
using HZ.WMS.Entity.MM.Import;
using HZ.WMS.Entity.SAP;

namespace HZ.WMS.Application.MM
{
    /// <summary>
    /// 物料标签
    /// 没有out_message消息的函数，意味着只在最前端做未处理异常捕捉
    /// </summary>
    public class MM_BarCodeApp : BaseApp<MM_BarCode>
    {
        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public MM_BarCodeApp() : base(){}

        #endregion

        #region 导入

        /// <summary>
        /// 导入
        /// </summary>
        /// <param name="excelList">导入信息</param>
        /// <param name="opUser">当前用户</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool ImprotExcelToBaseData(List<MM_BarCodeImport> excelList, string opUser, out string error_message)
        {
            error_message = "";
            var flag = true;
            DateTime date = DateTime.Now;
            BaseApp<BaseEntity> _baseApp = new BaseApp<BaseEntity>();
            var query = new List<MM_BarCode>();
            //暂时只进行插入操作，有需要在做更新操作
            foreach (var x in excelList)
            {
                //明细信息
                var z = new MM_BarCode();
                z.BarCode = x.出厂编号;
                z.BaseNum = x.合同号;
                z.CTime = x.日期;
                z.CUser = opUser;
                z.IsDelete = false;
                z.ItemCode = x.产品件号;
                var marc = DbContextForSAP.Queryable<XZ_SAP_MARC>().Where(y => y.MATNR == z.ItemCode).ToList().FirstOrDefault();
                if (marc != null)
                    z.ItemName = marc.MAKTX;
                z.PartCode = x.部件代码;
                z.Remark = x.装配线号;
                z.Qty = 1;
                query.Add(z);
            };
            Insert(query);
            return flag;
        }


        /// <summary>
        /// 导入物料号
        /// </summary>
        /// <param name="excelList">导入信息</param>
        /// <param name="opUser">当前用户</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool ImprotExcelToBaseDataItem(List<MM_BarCodeImport> excelList, string opUser, out string error_message)
        {
            error_message = "";
            var flag = true;
            DateTime date = DateTime.Now;
            BaseApp<BaseEntity> _baseApp = new BaseApp<BaseEntity>();
            var query = new List<MM_BarCode>();
            //暂时只进行插入操作，有需要在做更新操作
            foreach (var x in excelList)
            {
                //明细信息
                var z = new MM_BarCode();
                //z.BarCode = x.出厂编号;
                //z.BaseNum = x.合同号;
                z.CTime = date;
                z.CUser = opUser;
                z.IsDelete = false;
                z.ItemCode = x.产品件号;
                var marc = DbContextForSAP.Queryable<XZ_SAP_MARC>().Where(y => y.MATNR == z.ItemCode).ToList().FirstOrDefault();
                if (marc != null)
                    z.ItemName = marc.MAKTX;
                z.Unit = x.单位;
                z.Qty = x.数量;
                z.Remark = x.库位;
                z.PrintTemplate = x.仓库;
                query.Add(z);
            };
            Insert(query);
            return flag;
        }

        #endregion

    }
}

