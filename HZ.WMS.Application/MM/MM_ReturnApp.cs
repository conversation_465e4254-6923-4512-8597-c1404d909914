using SqlSugar;
using HZ.Core.Http;
using HZ.WMS.Application.MD;
using HZ.WMS.Application.SAP;
using HZ.WMS.Application.Sys;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.MM;
using HZ.WMS.Entity.MM.Parameters;
using HZ.WMS.Entity.SAP;
using HZ.WMS.Entity.SAP.View;
using HZ.WMS.Entity.Sys;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Application.MM
{
    /// <summary>
    /// 委外退料
    /// 没有out_message消息的函数，意味着只在最前端做未处理异常捕捉
    /// </summary>
    public class MM_ReturnApp : BaseApp<MM_Return>
    {
        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public MM_ReturnApp() : base(){}

        public class MM_ReturnDetailApp : BaseApp<MM_ReturnDetail> { }

        MM_ReturnDetailApp _detail_app = new MM_ReturnDetailApp();

        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="DocNums">单号数组</param>
        /// <param name="opUser">当前用户</param>
        /// <param name="error_message">错误消息</param>
        public bool Deletes(string[] DocNums, string opUser, out string error_message)
        {
            error_message = "";
            bool bDeleted = true;

            var mslist = new List<MM_Return>();
            var msDetailList = new List<MM_ReturnDetail>();
            foreach (string key in DocNums)
            {
                var item=GetList(x=>x.DocNum==key)?.ToList();
                mslist.AddRange(item);

                var itemdetail = _detail_app.GetList(x => x.DocNum==key)?.ToList();
                msDetailList.AddRange(itemdetail);
            }
            if (CheckDelete(mslist, out error_message)) //删除校验
            {
                try
                {
                    DbContext.Ado.BeginTran();
                    _detail_app.DbContext = this.DbContext;

                    //删除主表信息
                    Delete(mslist, opUser);

                    //删除明细表信息
                    _detail_app.Delete(msDetailList);

                    DbContext.Ado.CommitTran();//提交事务
                }
                catch (Exception ex)
                {
                    bDeleted = false;
                    error_message = ex.Message;

                    if(DbContext.Ado.IsAnyTran())
                        DbContext.Ado.RollbackTran();//失败回滚
                }
            }
            else
            {
                bDeleted = false;
            }
            return bDeleted;
        }

        /// <summary>
        /// 删除校验
        /// </summary>
        /// <param name="list">集合</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool CheckDelete(List<MM_Return> list, out string error_message)
        {
            bool isPass = true;
            error_message = "";
            ////校验所选择的单号是否已经发料
            //List<MM_OutsourcingDispatch> odList = new List<MM_OutsourcingDispatch>();
            //List<string> strList = new List<string>();
            //foreach (string key in DocNums)
            //{
            //    var query = od_app.GetList(x => (x.SubcontractingApplicationNum == key)).ToList();
            //    if (query.Count > 0)
            //    {
            //        strList.Add(key);
            //    }
            //    odList.AddRange(query);

            //}
            //strArray = strList.ToArray();//strArray=[str0,str1,str2]
            //if (odList.Count > 0)
            //{
            //    error_message = "所删除的信息中包含已发料的信息，请重新选择信息";
            //    isPass = false;
            //}

            foreach (var x in list)
            {
                if (x.IsPosted == true)
                {
                    error_message = "ui.Message.PODeletePostingWarning"; // "已过账数据不允许删除!";
                    isPass = false;
                    break;
                }
            }
            return isPass;
        }

        #endregion

        #region 过账

        /// <summary>
        /// 过账
        /// </summary>
        /// <param name="entities">主信息</param>
        /// <param name="entitiedetails">明细信息</param>
        /// <param name="opUser">当前用户</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool DoPost(List<MM_Return> entities,List<MM_ReturnDetail> entitiedetails, string opUser, out string error_message/*,out string messageType*/)
        {
            error_message = "";
            try
            {
                MD_StockApp _stockApp = new MD_StockApp();
                Sys_SAPCompanyInfoApp _SAPCompanyInfoApp = new Sys_SAPCompanyInfoApp();

                var mainInfoList = entities.GroupBy(g => new
                {
                    g.DocNum
                }).Select(q => new
                {
                    DocNum = q.Key.DocNum
                });

                //根据单号查询明细表的信息
                //foreach (MM_Return keyword in entities)
                //{
                //    var query = detail_app.GetList(x => (string.IsNullOrEmpty(keyword.DocNum) || x.DocNum == keyword.DocNum) && x.IsPosted == false).ToList();
                //    if (query != null && query.Count > 0)
                //    {
                //        entitiedetails.AddRange(query);
                //    }
                //}
               
                //按订单号分组
                foreach (var mainInfo in mainInfoList)
                {
                    var list = new List<ZFGWMS016>();
                    int Line = 0;
                    var detaList = new List<MM_ReturnDetail>();
                    if (entitiedetails != null && entitiedetails.Count > 0)//手持端添加信息后自动过账，不需要在查询一次
                    {
                        //查询明细信息
                        detaList = entitiedetails.Where(x => x.DocNum == mainInfo.DocNum && x.IsPosted == false)?.ToList();
                    }
                    else
                    {
                        detaList = _detail_app.GetList(x => x.DocNum == mainInfo.DocNum && x.IsPosted == false)?.ToList();
                    }
                    detaList.ForEach(x=>
                    {
                        x.Line = Line += 1;
                        list.Add(new ZFGWMS016
                        {
                            ZNUM = x.Line,
                            MATNR = x.ItemCode,
                            WERKS = x.FactoryCode ?? "2002",//eq.FactoryCode
                            BWART = x.MovementType ?? "542", //eq.MovementType;
                            MENGE = x.OutsourcingReturnQty,
                            MEINS = x.Unit,
                            LGORT = x.WhsCode,
                            SOBKZ = "",//必填 x.SpecialInventory
                            LIFNR = x.SupplierCode,//必填
                            SGTXT = x.Remark,
                        });
                    });

                    bool ispost = false;
                    //DateTime postdate = DateTime.Now;//过账时间 后续需要改逻辑
                    DateTime date = DateTime.Now;
                    //查询Sap账期时间
                    DateTime ManualPostTime = new SAPApp().GetSAPpostTime(Convert.ToDateTime(entities[0].ManualPostTime));
                    List<SAPRETURN> saplist = _SAPCompanyInfoApp.ZFGWMS016(detaList[0].CompanyCode ?? "001", mainInfo.DocNum, "",ManualPostTime , list, out ispost, out error_message);
                    if (saplist != null && saplist.Count > 0)
                    {
                        //List<MM_OutsourcingReturnDetail> querydetails = new List<MM_OutsourcingReturnDetail>();
                        foreach (var sap in saplist)
                        {
                            var querydetail = detaList.Where(x => x.DocNum == mainInfo.DocNum&&x.Line==sap.line).ToList().FirstOrDefault();
                            querydetail.IsPosted = true;
                            querydetail.PostUser = opUser;
                            querydetail.PostTime = date;
                            querydetail.MUser = opUser;
                            querydetail.MTime = date;
                            querydetail.SapDocNum = sap.sapDocNum;
                            querydetail.SapLine = sap.sapline;

                            if (_detail_app.Update(querydetail) > 0)
                            {
                                //供应商库存出库
                                string errorMsg = "";
                                if (!_stockApp.StockOut(querydetail.ItemCode, querydetail.SupplierCode, querydetail.SpecialInventory ?? "O", querydetail.OutsourcingReturnQty, opUser, out errorMsg))
                                {
                                    error_message = errorMsg;
                                    //messageType = "";
                                    //return false;
                                }
                                else
                                {
                                    //插入普通库存信息
                                    string stockMsg = string.Empty;
                                    MD_Stock stock = new MD_Stock();
                                    stock.BarCode = "";
                                    stock.BatchNum = "";
                                    stock.WhsCode = querydetail.WhsCode;
                                    stock.WhsName = querydetail.WhsName;
                                    stock.RegionCode = querydetail.RegionCode;
                                    stock.RegionName = querydetail.RegionName;
                                    stock.BinLocationCode = querydetail.BinLocationCode;
                                    stock.BinLocationName = querydetail.BinLocationName;
                                    //stock.BoqueryBarCode = querydetail.BoxBarCode;
                                    stock.ItemCode = querydetail.ItemCode.Trim();
                                    stock.ItemName = querydetail.ItemName.Trim();
                                    stock.ItmsGrpCode = querydetail.ItmsGrpCode;
                                    stock.ItmsGrpName = querydetail.ItmsGrpName;
                                    //stock.PTime = querydetail.PTime;
                                    stock.Qty = querydetail.OutsourcingReturnQty;
                                    stock.SaleNum = "";
                                    stock.SaleLine = 0;
                                    stock.Unit = querydetail.Unit;
                                    //stock.IsDelete = false;
                                    //user = query.CUser;
                                    if (!_stockApp.StockIn(stock, opUser, out stockMsg))
                                    {
                                        error_message = stockMsg;
                                        //messageType = "";
                                        //return false;
                                    }
                                }
                            }

                            //querydetails.Add(querydetail);
                        }

                        var query = entities.Where(x => x.DocNum == mainInfo.DocNum).ToList().FirstOrDefault();
                        query.IsPosted = true;
                        query.PostUser = opUser;
                        query.PostTime = date;
                        query.MUser = opUser;
                        query.MTime = date;
                        query.ManualPostTime = ManualPostTime;
                        query.SAPmark = "S";

                        //更新主表信息
                        Update(query);

                        //更新明细表信息
                        //detail_app.Update(querydetails);
                    }
                    else
                    {
                        var query = entities.Where(x => x.DocNum == mainInfo.DocNum).ToList();
                        foreach (var x in query)
                        {
                            x.SAPmark = "E";
                            x.SAPmessage = error_message;
                        }
                        //更新主信息
                        Update(query);

                        error_message = "单号：" + mainInfo.DocNum + "，过账失败，失败原因：" + error_message;
                        //messageType = "过账";
                        return ispost;
                    }
                }
                if (string.IsNullOrEmpty(error_message))
                {
                    error_message = "过账成功";
                }
                return true;
            }
            catch (Exception ex)
            {
                error_message = ex.Message;
                //messageType = "";
                return false;
            }
        }


        #endregion

        #region 冲销过账

        /// <summary>
        /// 冲销过账
        /// </summary>
        /// <param name="entities">主信息</param>
        /// <param name="entitieDetails">明细信息</param>
        /// <param name="opUser">当前过账用户</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool PassPost(List<MM_Return> entities, string opUser, out string error_message)
        {
            error_message = "";

            var DocNums = entities.Select(x => x.DocNum).Distinct().ToArray();
            var conditionList1 = _detail_app.GetList(x => DocNums.Contains(x.DocNum) && x.IsPosted == true)?.ToList();

            Sys_SAPCompanyInfoApp _SAPCompanyInfoApp = new Sys_SAPCompanyInfoApp();
            MD_StockApp _stockApp = new MD_StockApp();

            try
            {

                //多条数据后根据单号进行分组
                var mainInfoList = conditionList1.GroupBy(g => new
                {
                    g.SapDocNum
                }).Select(q => new
                {
                    SapDocNum = q.Key.SapDocNum
                });

                //按订单号分组
                foreach (var mainInfo in mainInfoList)
                {
                    int Line = 0;

                    List<string> list = new List<string>();
                    var conditionList = conditionList1.Where(x => x.SapDocNum == mainInfo.SapDocNum)?.ToList();
                    conditionList.ForEach(x =>
                    {
                        list.Add(x.SapLine.ToString());

                    });

                    bool ispost = false;
                    DateTime time = DateTime.Now;
                    DateTime? ManualPostTime1 = entities.Where(x => x.DocNum == conditionList[0].DocNum)?.ToList()[0].ManualPostTime;
                    //查询Sap账期时间
                    DateTime ManualPostTime = new SAPApp().GetSAPpostTime(Convert.ToDateTime(ManualPostTime1));
                    //purchaseReceipt.CompanyCode 公司代码
                    var saplist = _SAPCompanyInfoApp.ZFGWMS021(conditionList[0].CompanyCode ?? "001", mainInfo.SapDocNum, list, ManualPostTime, out ispost, out error_message);
                    if (string.IsNullOrEmpty(error_message))
                    {
                        foreach (var query1 in conditionList)
                        {

                            query1.IsDelete = true;
                            query1.DUser = opUser;
                            query1.DTime = time;
                            query1.Remark = query1.Remark + "已冲销";

                            if (_detail_app.Update(query1) > 0)//更新成功后更新库存
                            {
                                #region 更新库存
                                string errorMsg = string.Empty;
                                if (!_stockApp.StockOut(query1.ItemCode, query1.BinLocationCode, query1.OutsourcingReturnQty, opUser, out errorMsg))
                                {
                                    error_message = errorMsg;
                                    //return false;
                                }
                                else
                                {
                                    // 插入收货记录成功才能更新库存
                                    string stockMsg = string.Empty;
                                    var stock = new MD_Stock();
                                    stock.ItemCode = query1.ItemCode;
                                    stock.ItemName = query1.ItemName;
                                    stock.SupplierCode = query1.SupplierCode;
                                    stock.SupplierName = query1.SupplierName;
                                    stock.Qty = query1.OutsourcingReturnQty;
                                    stock.Unit = query1.Unit;
                                    stock.SpecialStock = "O";
                                    if (!_stockApp.StockInSupplier(stock, opUser, out stockMsg))
                                    {
                                        error_message = stockMsg;
                                    }
                                }
                                #endregion

                                //queryList.Add(query);
                            }
                        }

                        var query = entities.Where(x => x.DocNum == conditionList[0].DocNum).ToList().FirstOrDefault();
                        if (query != null)
                        {
                            query.IsDelete = true;
                            query.DUser = opUser;
                            query.DTime = time;
                            query.SAPmark = "已冲销";
                        }
                        //更新
                        Update(query);
                    }
                    else
                    {
                        //var query = entities.Where(x => x.DocNum == conditionList[0].DocNum).ToList().FirstOrDefault();
                        //if (query != null)
                        //{
                        //    query.SAPmark = "E";
                        //    query.SAPmessage = error_message;
                        //}
                        ////更新
                        //Update(query);

                        error_message = "单号：" + conditionList[0].DocNum + "，冲销过账失败，失败原因：" + error_message;
                        return false;
                    }
                }
                if (string.IsNullOrEmpty(error_message))
                {
                    error_message = "冲销过账成功";
                }
                return true;
            }
            catch (Exception ex)
            {
                error_message = ex.Message;
                return false;
            }
        }

        #endregion

        #region 保存

        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="Parameters">单据号</param>
        /// <param name="user">用户</param>
        /// <param name="error_message">错误信息</param>
        /// <param name="type">类型 1:提交失败 2：过账失败</param>
        /// <returns></returns>
        public bool Save(MM_ReturnParameters Parameters, string user, out string error_message, out string type)
        {
            error_message = "";
            type = "";
            MD_StockApp _stockApp = new MD_StockApp();
            try
            {
                #region 库存校验
                //库存注释
                //foreach (var x in Parameters.detailed)
                //{
                //    string stockMsg = "";
                //    bool bstock=_stockApp.ValidateStockOut(x.ItemCode,x.SupplierCode,"O",x.OutsourcingReturnQty,out stockMsg,null);
                //    if (!bstock)
                //    {
                //        error_message = stockMsg;
                //        type = "1";
                //        return false;
                //    }
                //}

                #endregion

                #region 属性初始化

                DateTime time = DateTime.Now;
                var querys = new List<MM_Return>();

                #endregion

                #region 逻辑处理

                querys.Add(new MM_Return()
                {
                    DocNum = Parameters.DocNum,
                    IsDelete = false,
                    CUser = user,
                    CTime = time,
                    Remark = Parameters.Remark,
                    ManualPostTime=Parameters.ManualPostTime
                });

                Parameters.detailed.ForEach(x=>
                {
                    x.MovementType = "542"; //"541：发料 542：退料"
                    x.SpecialInventory = "O";// O:供应商库存
                    x.CompanyCode = "001";
                    x.FactoryCode = "2002";
                    x.DocNum = Parameters.DocNum;
                    x.IsDelete = false;
                    x.CUser = user;
                    x.CTime = time;
                    //x.WhsCode = Parameters.WhsCode;
                    //x.WhsName = Parameters.WhsName;
                    //x.RegionCode = Parameters.RegionCode;
                    //x.RegionName = Parameters.RegionName;
                    //x.BinLocationCode = Parameters.BinLocationCode;
                    //x.BinLocationName = Parameters.BinLocationName;
                });

                //detailed.Where((x, i) => detailed.FindIndex(z => z.DocNum == x.DocNum) == i) 去除某一列重复值
                //detailed.Distinct() 去重

                #endregion

                #region 事务操作

                DbContext.Ado.BeginTran();
                _detail_app.DbContext = this.DbContext;

                //委外退料插入
                Insert(querys);

                //委外退料明细插入
                _detail_app.Insert(Parameters.detailed);

                DbContext.Ado.CommitTran();

                #endregion

                #region 是否自动过账

                //是否自动过账判定
                Sys_SwithConfigApp _switchApp = new Sys_SwithConfigApp();
                if (_switchApp.IsMMReturnAutoPost)
                {
                    string postMsg = "";
                    //string messageType = "";
                    bool bpost=DoPost(querys, Parameters.detailed, user, out postMsg/*,out messageType*/);
                    if (!bpost)
                    {
                        error_message = "提交成功"+postMsg;
                        type = "2";
                        return false;
                    }
                    else
                    {
                        error_message = "提交并" + postMsg;
                    }
                }

                #endregion

                if (string.IsNullOrEmpty(error_message))
                    error_message = "提交成功";
                return true;
            }
            catch (Exception ex)
            {
                if (DbContext.Ado.IsAnyTran())
                    DbContext.Ado.RollbackTran();

                error_message = ex.Message;
                type = "1";
                return false;
            }
        }

        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="Parameters">单据号</param>
        /// <param name="user">用户</param>
        /// <param name="error_message">错误信息</param>
        /// <param name="type">类型 1:提交失败 2：过账失败</param>
        /// <returns></returns>
        public bool Update(MM_ReturnParameters Parameters, string user, out string error_message, out string type)
        {
            error_message = "";
            type = "";
            MD_StockApp _stockApp = new MD_StockApp();
            Sys_SwithConfigApp _switchApp = new Sys_SwithConfigApp();

            try
            {
                #region 库存校验
                //库存注释
                //foreach (var x in Parameters.detailed)
                //{
                //    string stockMsg = "";
                //    bool bstock = _stockApp.ValidateStockOut(x.ItemCode, x.SupplierCode, "O", x.OutsourcingReturnQty, out stockMsg, null);
                //    if (!bstock)
                //    {
                //        error_message = stockMsg;
                //        type = "1";
                //        return false;
                //    }
                //}

                #endregion

                #region 属性初始化

                DateTime time = DateTime.Now;
                var AddmsDetailList = new List<MM_ReturnDetail>();
                var UpdatemsDetailList = new List<MM_ReturnDetail>();

                #endregion

                #region 逻辑处理

                //根据主键ID查询委外申请单明细信息
                var deleteArrays = _detail_app.GetListByKeys(Parameters.deletedetail);

                //根据申请单号查询委外申请单主表信息
                var query = GetList(x => x.DocNum == Parameters.DocNum).ToList();
               
                foreach (var x in Parameters.detailed)
                {
                    if (string.IsNullOrEmpty(x.OutsourcingReturnDetailID) && x.OutsourcingReturnDetailID == null)
                    {
                        x.DocNum = Parameters.DocNum;
                        x.IsDelete = false;
                        x.CUser = user;
                        x.CTime = time;
                        x.IsPosted = false;
                        x.CompanyCode = "001";
                        x.FactoryCode = "2002";
                        AddmsDetailList.Add(x);
                    }
                    else
                    {
                        x.MUser = user;
                        x.MTime = time;
                        UpdatemsDetailList.Add(x);
                    }
                }

                #endregion

                #region 事务操作

                DbContext.Ado.BeginTran();
                _detail_app.DbContext = this.DbContext;

                //删除明细信息
                if (deleteArrays.Count>0)
                {
                    _detail_app.Delete(deleteArrays, user);
                }
                //更新主信息
                if (query != null && query.Count > 0)
                {
                    query[0].Remark = Parameters.Remark;
                    query[0].MUser = user;
                    query[0].MTime = time;
                    base.Update(query);
                }
                //插入明细信息
                //this.Update(listms);
                if (AddmsDetailList.Count > 0)
                {
                    _detail_app.Insert(AddmsDetailList);
                }
                //更新明细信息
                if (UpdatemsDetailList.Count > 0)
                {
                    _detail_app.Update(UpdatemsDetailList);
                }

                DbContext.Ado.CommitTran();

                #endregion

                #region 是否自动过账

                //是否自动过账判定
                if (_switchApp.IsMMReturnAutoPost)
                {
                    string postMsg = "";
                    bool bpost= DoPost(query,Parameters.detailed, user, out postMsg);
                    if (!bpost)
                    {
                        error_message = "更新成功"+postMsg;
                        type = "2";
                        return false;
                    }
                    else
                    {
                        error_message = "更新并" + postMsg;
                    }
                }

                #endregion

                if (string.IsNullOrEmpty(error_message))
                    error_message = "更新成功";
                return true;
            }
            catch (Exception ex)
            {
                if(DbContext.Ado.IsAnyTran())
                    DbContext.Ado.RollbackTran();

                error_message = ex.Message;
                type = "1";
                return false;
            }
        }

        #endregion

        #region 获取供应商库存

        /// <summary>
        /// 获取供应商库存
        /// </summary>
        /// <param name="page">分页</param>
        /// <param name="condition">参数</param>
        /// <returns></returns>
        public List<MD_Stock> GetSupplierStock(Pagination page, Expression<Func<MD_Stock, bool>> condition)
        {
            if (string.IsNullOrEmpty(page.Sort))
            {
                page.Sort = "ItemCode";
            }
            var query = DbContext.Queryable<MD_Stock>()
                .Where(x=>x.IsDelete==false && x.SpecialStock== "O" && x.Qty>0)
                .Where(condition)
                .OrderBy(page.Sort);

            int total = query.Count();
            page.Total = total;
            if (page.PageSize > total)
            {
                return query.ToPageList(page.PageNumber, total);
            }
            return query.ToPageList(page.PageNumber, page.PageSize);
        }

        #endregion


        #region 获取子查询订单号列表

        /// <summary>
        /// 获取供应商库存
        /// </summary>
        /// <param name="keywords">供应编号</param>
        /// <param name="fromTime">开始时间</param>
        /// <param name="toTime">结束时间</param>
        /// <returns></returns>
        public List<string> GetDocNumByInfo(string keywords, DateTime fromTime, DateTime toTime)
        {
            string sql = @" SELECT DocNum FROM MM_ReturnDetail WHERE CTime >= @fromTime and CTime <= @toTime and SupplierCode = @keywords";
            return DbContext.Ado.SqlQuery<string>(sql, new SugarParameter[]{ new SugarParameter("@fromTime", fromTime),
                new SugarParameter("@toTime", toTime),new SugarParameter("@keywords", keywords)}).ToList();
        }

        #endregion

    }
}
