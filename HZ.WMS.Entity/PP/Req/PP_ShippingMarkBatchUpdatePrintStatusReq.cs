using System;
using System.Collections.Generic;
using System.ComponentModel;

namespace HZ.WMS.Entity.PP
{
    /// <summary>
    /// 唛头打印状态批量更新请求
    /// </summary>
    public class PP_ShippingMarkBatchUpdatePrintStatusReq
    {
        /// <summary>
        /// 唛头ID列表
        /// </summary>
        [Description("唛头ID列表")]
        public List<string> Ids { get; set; }

        /// <summary>
        /// 打印状态
        /// </summary>
        [Description("打印状态")]
        public bool PrintStatus { get; set; }

        /// <summary>
        /// 打印时间（可选，不传则自动设置为当前时间）
        /// </summary>
        [Description("打印时间")]
        public DateTime? PrintTime { get; set; }
    }
}
