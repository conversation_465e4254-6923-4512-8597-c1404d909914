using HZ.Core.Http;
using HZ.WMS.Application.SAP;
using HZ.WMS.WebAPI.Controllers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Http;
using HZ.WMS.Entity.SD;
using HZ.WMS.Entity.SAP.View;
using HZ.WMS.Application;

namespace HZ.WMS.WebAPI.Areas.SAP.Controllers
{
    /// <summary>
    /// SAP中间库（通用）
    /// </summary>
    public class XZSAPController : ApiBaseController
    {
        #region 初始化

        private SAPApp xzsap_app = new SAPApp();
        //[AllowAnonymous] //添加此属性，PostMan可以直接通过接口调试，而不通过前端界面

        #endregion

        #region 采购订单

        /// <summary>
        /// 查询采购订单(不区分类型)
        /// </summary>
        /// <param name="page">分页信息</param>
        /// <param name="keyword">参数</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetXZSAP_EKKO([FromUri]Pagination page, [FromUri]string keyword)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = xzsap_app.GetXZSAP_EKKO(page, keyword).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 查询采购订单类型Z006(退货采购订单)
        /// </summary>
        /// <param name="page">分页信息</param>
        /// <param name="keyword">参数</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetXZSAP_EKKOForBSART([FromUri]Pagination page, [FromUri]string keyword)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = xzsap_app.GetPO_ReturnScanForEKKO(page, keyword).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 条件查询采购订单组件-委外领料申请使用
        /// </summary>
        /// <param name="entities">集合</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult GetXZSAP_RESBM([FromBody]List<SAP_EKKO_EKPO_View> entities)
        {
            var result = new ResponseData();
            string error_message = "";
            try
            {
                var itemsData = xzsap_app.GetXZ_SAP_RESBM_View(entities,out error_message);
                if (itemsData != null && itemsData.Count > 0)
                {
                    result.Data = itemsData ;
                    result.Code = (int)WMSStatusCode.Success;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 分页条件查询采购订单关联委外领料申请单
        /// </summary>
        /// <param name="page">分页信息</param>
        /// <param name="keyword">参数</param>
        /// <param name="BaseNum">采购单号</param>
        /// <param name="Supplier">供应商</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetSAP_EKKOForSubcontractingApplication([FromUri]Pagination page, [FromUri]string keyword, [FromUri]string BaseNum, [FromUri]string Supplier)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = xzsap_app.GetSAP_EKKOForSubcontractingApplication(page,  x => (string.IsNullOrEmpty(keyword)
                                                                                      || x.EBELN.Contains(keyword) || x.BSART.Contains(keyword)
                                                                                      || x.BATXT.Contains(keyword) || x.BUKRS.Contains(keyword)
                                                                                      || x.EKORG.Contains(keyword) || x.EKGRP.Contains(keyword)
                                                                                      || x.LIFNR.Contains(keyword) || x.NAME1.Contains(keyword)
                                                                                      || x.ZTERM.Contains(keyword) || x.PSTYP.Contains(keyword)
                                                                                      || x.MATNR.Contains(keyword) || x.TXZ01.Contains(keyword)
                                                                                      || x.WERKS.Contains(keyword) || x.LGORT.Contains(keyword)
                                                                                      || x.MEINS.Contains(keyword) || x.MWSKZ.Contains(keyword)
                                                                                      || x.MATKL.Contains(keyword)) 
                                                                                      && (string.IsNullOrEmpty(BaseNum) || x.EBELN.Contains(BaseNum))
                                                                                      && (string.IsNullOrEmpty(Supplier) || x.LIFNR.Contains(Supplier) || x.NAME1.Contains(Supplier))
                                                                                      ).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 物料主数据

        /// <summary>
        /// 分页条件查询物料主数据
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IHttpActionResult GetXZSAP_MARC([FromUri]Pagination page, [FromUri]string keyword)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = xzsap_app.GetXZSAP_MARC(page, keyword).ToList();
                //return query.ToPageList(page.PageNumber, page.PageSize);
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 条件查询物料主数据
        /// </summary>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IHttpActionResult GetXZSAP_MARCForCondition([FromUri]string keyword)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = xzsap_app.GetXZSAP_MARC(keyword).ToList().FirstOrDefault();
                //return query.ToPageList(page.PageNumber, page.PageSize);
                result.Data =itemsData;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 查询物料主数据关联委外领料申请单
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IHttpActionResult GetSAP_MARCForSubcontractingApplication([FromUri]Pagination page, [FromUri]string keyword)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = xzsap_app.GetSAP_MARCForSubcontractingApplication(page, keyword).ToList();
                //return query.ToPageList(page.PageNumber, page.PageSize);
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 仓库

        /// <summary>
        /// 查询所有仓库
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous] //添加此属性，PostMan可以直接通过接口调试，而不通过前端界面
        public IHttpActionResult GetXZ_SAP_T001L()
        {
            var result = new ResponseData();
            try
            {
                var itemsData = xzsap_app.GetXZ_SAP_T001L().ToList();
                //var itemsData = xzsap_app.GetXZSAP_EKKO(page, keyword).ToList();
                //return query.ToPageList(page.PageNumber, page.PageSize);
                result.Data = itemsData;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 销售订单

        /// <summary>
        /// 销售发运计划关联SAP销售订单信息
        /// </summary>
        /// <param name="page">分页</param>
        /// <param name="keyword">参数</param>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous] //添加此属性，PostMan可以直接通过接口调试，而不通过前端界面
        public IHttpActionResult GetXZSAP_VBAK([FromUri]Pagination page, [FromUri]string keyword, [FromUri] DateTime[] dateValue)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormatAll(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];

                var itemsData = xzsap_app.GetXZSAP_VBAK(page, keyword, fromTime, toTime).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }


        #endregion

        #region 成本中心

        /// <summary>
        /// 查询所有成本中心
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous] //添加此属性，PostMan可以直接通过接口调试，而不通过前端界面
        public IHttpActionResult GetXZ_SAP_CSKS()
        {
            var result = new ResponseData();
            try
            {
                var itemsData = xzsap_app.GetXZ_SAP_CSKS().ToList();
                result.Data = itemsData;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 总账科目

        /// <summary>
        /// 查询所有总账科目基于成本中心
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous] //添加此属性，PostMan可以直接通过接口调试，而不通过前端界面
        public IHttpActionResult GetXZ_SAP_SKA1()
        {
            var result = new ResponseData();
            try
            {
                var itemsData = xzsap_app.GetXZ_SAP_SKA1().ToList();
                result.Data = itemsData;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 查询所有总账科目 不基于成本中心
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous] //添加此属性，PostMan可以直接通过接口调试，而不通过前端界面
        public IHttpActionResult GetXZ_SAP_SKA12()
        {
            var result = new ResponseData();
            try
            {
                var itemsData = xzsap_app.GetXZ_SAP_SKA12().ToList();
                result.Data = itemsData;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 客户主数据

        /// <summary>
        /// 查询所有客户主数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous] //添加此属性，PostMan可以直接通过接口调试，而不通过前端界面
        public IHttpActionResult GetXZ_SAP_KNA1()
        {
            var result = new ResponseData();
            try
            {
                var itemsData = xzsap_app.GetXZ_SAP_KNA1().ToList();
                result.Data = itemsData;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 员工客户信息

        /// <summary>
        /// 员工客户信息
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous] //添加此属性，PostMan可以直接通过接口调试，而不通过前端界面
        public IHttpActionResult GetXZ_SAP_KNA1ForEmp()
        {
            var result = new ResponseData();
            try
            {
                var itemsData = xzsap_app.GetXZ_SAP_KNA1ForEmp().ToList();
                result.Data = itemsData;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 内部订单

        /// <summary>
        /// 查询所有内部订单
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous] //添加此属性，PostMan可以直接通过接口调试，而不通过前端界面
        public IHttpActionResult GetSAPAUFK()
        {
            var result = new ResponseData();
            try
            {
                var itemsData = xzsap_app.GetSAPAUFK().ToList();
                result.Data = itemsData;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 资产卡片

        /// <summary>
        /// 查询所有资产卡片
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous] //添加此属性，PostMan可以直接通过接口调试，而不通过前端界面
        public IHttpActionResult GetSAPANLA()
        {
            var result = new ResponseData();
            try
            {
                var itemsData = xzsap_app.GetSAPANLA().ToList();
                result.Data = itemsData;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

    }
}