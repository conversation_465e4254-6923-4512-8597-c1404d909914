using HZ.Core.Http;
using HZ.Core.Office;
using HZ.WMS.Application;
using HZ.WMS.Application.MD;
using HZ.WMS.Application.PP;
using HZ.WMS.Application.Sys;
using HZ.WMS.Entity.PP;
using HZ.WMS.Entity.PP.Dto;
using HZ.WMS.Entity.PP.ViewModel;
using HZ.WMS.Entity.Sys;
using HZ.WMS.WebAPI.Controllers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;

namespace HZ.WMS.WebAPI.Areas.PP.Controllers
{
    /// <summary>
    /// 生产物料配送单
    /// </summary>
    public class PP_MaterialDistributionController : ApiBaseController
    {
        private PP_MaterialDistributionApp _app = new PP_MaterialDistributionApp();
        private PP_MaterialDistributionDetailApp detailApp = new PP_MaterialDistributionDetailApp();
        private MD_ProductionDistributionSettingApp disApp = new MD_ProductionDistributionSettingApp();
        private PP_DistributionDetailViewApp _appView = new PP_DistributionDetailViewApp();
        #region 获取数据

        /// <summary>
        /// 获取主表分页列表
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <param name="dateValue"></param>
        /// <param name="isPosted"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]string keyword, [FromUri] DateTime[] dateValue, [FromUri]bool? isPosted)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];

                page.Sort = "CTime desc, DeliveryOrderNo desc";
                var itemsData = _app.GetPageList(page, 
                    t => (string.IsNullOrEmpty(keyword)
                        || t.DeliveryOrderNo.Contains(keyword)
                        || t.EmployeeNumber.Contains(keyword)
                        || t.EmployeeName.Contains(keyword)
                        || t.DoPostMan.Contains(keyword)
                        || t.CUser.Contains(keyword)) 
                        && (t.StartTime >= fromTime && t.StartTime < toTime)
                        && (isPosted == null || t.IsDoPost == isPosted))
                    .ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 查询子表分页列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetDetailPageList([FromUri]Pagination page, [FromUri]string deliveryNo)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = detailApp.GetPageList(page, w => deliveryNo == w.DeliveryOrderNo).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 查询子表列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetDetailList([FromUri]string deliveryNo)
        {
            var result = new ResponseData();
            try
            {
                result.Data = detailApp.GetList(w => deliveryNo == w.DeliveryOrderNo).ToList();
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取SAP配送清单

        /// <summary>
        /// 获取SAP配送清单
        /// </summary>
        /// <param name="page"></param>
        /// <param name="ProductionOrderNo">员工号/员工姓名</param>
        /// <param name="MaterialNo">物料号/物料名称</param>
        /// <param name="ProductionLineCode">线体编号</param>
        /// <param name="ProductionLineDes">线体描述</param>
        /// <param name="IsEmpty">员工号是否为空</param>
        /// <param name="dateValue">装配日期区间</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetSapOrderList([FromUri]Pagination page, [FromUri]string keyword, [FromUri]string ProductionOrderNo, [FromUri]string MaterialNo, [FromUri]string ProductionLineCode, [FromUri]string ProductionLineDes, [FromUri]bool IsEmpty, [FromUri]DateTime[] dateValue)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];

                //查员工号为空的数据
                if(IsEmpty)
                {
                    var itemsData = _appView.GetPageList(page,
                    w =>
                    (string.IsNullOrEmpty(keyword)
                    || w.MaterialGroupCode.Contains(keyword)
                    || w.MaterialGroupDes.Contains(keyword)
                    || w.AssessmentCategory.Contains(keyword))
                    &&  string.IsNullOrEmpty(w.EmployeeNumber)
                    && (string.IsNullOrEmpty(MaterialNo) || w.ComponentCode.Contains(MaterialNo) || w.MaterialName.Contains(MaterialNo))
                    && (string.IsNullOrEmpty(ProductionLineCode) || w.ProductionLineCode.Contains(ProductionLineCode))
                    && (string.IsNullOrEmpty(ProductionLineDes) || w.ProductionLineDes.Contains(ProductionLineDes))
                    && (w.StartTime >= fromTime && w.StartTime < toTime)).ToList();
                    result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                }
                else
                {
                    var itemsData = _appView.GetPageList(page,
                    w => (string.IsNullOrEmpty(keyword)
                    || w.MaterialGroupCode.Contains(keyword)
                    || w.MaterialGroupDes.Contains(keyword)
                    || w.AssessmentCategory.Contains(keyword))
                    &&(string.IsNullOrEmpty(ProductionOrderNo) || w.EmployeeName.Contains(ProductionOrderNo) || w.EmployeeNumber.Contains(ProductionOrderNo))
                    && (string.IsNullOrEmpty(MaterialNo) || w.ComponentCode.Contains(MaterialNo) || w.MaterialName.Contains(MaterialNo))
                    && (string.IsNullOrEmpty(ProductionLineCode) || w.ProductionLineCode.Contains(ProductionLineCode))
                    && (string.IsNullOrEmpty(ProductionLineDes) || w.ProductionLineDes.Contains(ProductionLineDes))
                    && (w.StartTime >= fromTime && w.StartTime < toTime)).ToList();
                    result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取所有线体

        /// <summary>
        /// 获取所有线体
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetAllLine()
        {
            var result = new ResponseData();
            try
            {
                result.Data = disApp.GetHostLine();
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 增加

        [HttpPost]
        public IHttpActionResult Add([FromBody]PP_MaterialDistribution_Dto materialDistribution)
        {
            var result = new ResponseData();
            try
            {
                Sys_User user = GetCurrentUser();
                //根据生产线和配送人进行分组
                var groupList = materialDistribution.details.GroupBy(g => new { g.ProductionLineCode, g.EmployeeNumber, g.StartTime });
                List<PP_MaterialDistribution> materials = new List<PP_MaterialDistribution>();
                foreach (var item in groupList)
                {
                    string deliveryOrderNo = base.GenerateDocNum(DocType.PP, DocFixedNumDef.PP_MaterialDistribution);
                    materials.Add(new PP_MaterialDistribution
                    {
                        DeliveryOrderNo = deliveryOrderNo,
                        ProductionLineCode = item.Key.ProductionLineCode,
                        ProductionLineDes = item.ToList().FirstOrDefault().ProductionLineDes ?? "",
                        EmployeeNumber = item.Key.EmployeeNumber,
                        EmployeeName = item.ToList().FirstOrDefault().EmployeeName ?? "",
                        ManualPostTime = materialDistribution.ManualPostTime,
                        StartTime = item.Key.StartTime,
                        IsDoPost = false,
                        CUser = user.LoginAccount,
                });
                    
                    foreach (var detail in item)
                    {
                        detail.DeliveryOrderNo = deliveryOrderNo;
                        detail.StartTime = item.Key.StartTime;
                        detail.IsDoPost = false;
                        detail.ManualPostTime = materialDistribution.ManualPostTime;
                        detail.CUser = user.LoginAccount;
                    }
                }

                if (!_app.Add(materials, materialDistribution.details))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                }
                else
                {
                    //是否自动过账判定
                    if (new Sys_SwithConfigApp().IsPPDeliveryAutoPost)
                    {
                        string error_message = "";
                        bool bSubmit = _app.DoPost(materials, user.LoginAccount, out error_message);

                        if (!bSubmit)
                        {
                            result.Message = "保存成功；过账失败，失败原因：" + error_message;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 更新

        [HttpPost]
        public IHttpActionResult Update([FromBody]PP_MaterialDistribution_Dto disDto)
        {
            var result = new ResponseData();
            try
            {
                Sys_User user = GetCurrentUser();
                if (!_app.Edit(disDto, user.LoginAccount))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 删除

        [HttpDelete]
        public IHttpActionResult DeleteDelivery(List<string> deliveryNo)
        {
            var result = new ResponseData();
            try
            {
                Sys_User user = GetCurrentUser();
                var list = _app.GetList(w => deliveryNo.Contains(w.DeliveryOrderNo)).ToList();
                if(list.Any(w => w.IsDoPost == true))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "ui.Message.PODeletePostingWarning";
                }
                if (!_app.Delete(deliveryNo,user.LoginAccount))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        [HttpDelete]
        public IHttpActionResult DeleteDeliveryDetail(string[] detailIDs)
        {
            var result = new ResponseData();
            try
            {
                Sys_User user = GetCurrentUser();
                detailApp.DeleteByKeys(detailIDs,user.LoginAccount);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 打印

        #endregion

        #region 导出

        /// <summary>
        /// 导出
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri]string keyword, [FromUri] DateTime[] dateValue, [FromUri]bool? isPosted, [FromUri]List<string> DeliveryOrderNos)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = new List<PP_MaterialDistributionDetail>();
                if (DeliveryOrderNos == null || DeliveryOrderNos.Count < 1)
                {
                    var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                    DateTime fromTime = querDateTimes[0];
                    DateTime toTime = querDateTimes[1];

                    var list = _app.GetList(
                        t => (string.IsNullOrEmpty(keyword)
                            || t.DeliveryOrderNo.Contains(keyword)
                            || t.EmployeeNumber.Contains(keyword)
                            || t.EmployeeName.Contains(keyword)
                            || t.DoPostMan.Contains(keyword)
                            || t.CUser.Contains(keyword))
                            && (t.StartTime >= fromTime && t.StartTime < toTime)
                            && (isPosted == null || t.IsDoPost == isPosted))
                        .Select(s => s.DeliveryOrderNo)
                        .ToList();

                    itemsData = detailApp.GetList(t => list.Contains(t.DeliveryOrderNo)).ToList();
                }
                else
                {
                    itemsData = detailApp.GetList(t => DeliveryOrderNos.Contains(t.DeliveryOrderNo)).ToList();
                }

                //单位替换
                itemsData.ForEach(item =>
                {
                    switch (item.ComponentUnit.ToUpper())
                    {
                        case "KAR":
                            item.ComponentUnit = "CAR";
                            break;
                        case "PAK":
                            item.ComponentUnit = "PAC";
                            break;
                        case "ST":
                            item.ComponentUnit = "PC";
                            break;
                        default:
                            break;
                    }
                });
                List<ExcelColumn<PP_MaterialDistributionDetail>> columns = ExcelService.FetchDefaultColumnList<PP_MaterialDistributionDetail>();
                string[] ignoreField = new string[]
                {
                    "ID"
                    ,"DeliveryOrderNo"
                    ,"DeliveryLineNo"
                    ,"ProductionOrderNo"
                    ,"SerialNo"
                    ,"ContractNo"
                    ,"ComponentLineNo"
                    ,"Customer"
                    ,"ProductionBatch"
                    ,"ProductionSequencing"
                    ,"FactoryCode"
                    ,"SalesOrderNo"
                    ,"SalesOrderLineNo"
                    ,"EmployeeNumber"
                    //,"ProductionLineCode"
                   // ,"MaterialGroupCode"
                    ,"SpecialInventory"
                    ,"AssessmentCategory"
                    ,"AssessmentType"
                    ,"IsDoPost"
                    ,"ManualPostTime"
                    ,"DoPostTime"
                    ,"DoPostMan"
                    ,"SapDocNum"
                    ,"SapLine"
                    ,"IsDelete"
                    ,"MUser"
                    ,"MTime"
                    ,"DUser"
                    ,"DTime"
                };
                List<ExcelColumn<PP_MaterialDistributionDetail>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<PP_MaterialDistributionDetail> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                //columns.ForEach((column) =>
                //{
                //    if (column.ColumnName == "IsPosted")
                //    {
                //        column.Formattor = ExcelExportFormatter.IsOrNoFormatter;
                //    }
                //});

                return ExportToExcelFile<PP_MaterialDistributionDetail>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #region 导出SAP配送信息

        /// <summary>
        /// 导出SAP配送信息
        /// </summary>
        /// <param name="ProductionOrderNo">员工号/员工姓名</param>
        /// <param name="MaterialNo">物料号/物料名称</param>
        /// <param name="ProductionLineCode">线体编号</param>
        /// <param name="ProductionLineDes">线体描述</param>
        /// <param name="IsEmpty">员工号是否为空</param>
        /// <param name="dateValue">装配日期区间</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFileForSAP([FromUri]string keyword, [FromUri]string ProductionOrderNo, [FromUri]string MaterialNo, [FromUri]string ProductionLineCode, [FromUri]string ProductionLineDes, [FromUri]bool IsEmpty, [FromUri]DateTime[] dateValue)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];

                var itemsData = new List<PP_DistributionDetail_View>();
                //查员工号为空的数据
                if (IsEmpty)
                {
                    itemsData = _appView.GetList(
                    w =>(string.IsNullOrEmpty(keyword)
                    || w.MaterialGroupCode.Contains(keyword)
                    || w.MaterialGroupDes.Contains(keyword)
                    || w.AssessmentCategory.Contains(keyword))
                    && string.IsNullOrEmpty(w.EmployeeNumber)
                    && (string.IsNullOrEmpty(MaterialNo) || w.ComponentCode.Contains(MaterialNo) || w.MaterialName.Contains(MaterialNo))
                    && (string.IsNullOrEmpty(ProductionLineCode) || w.ProductionLineCode.Contains(ProductionLineCode))
                    && (string.IsNullOrEmpty(ProductionLineDes) || w.ProductionLineDes.Contains(ProductionLineDes))
                    && (w.StartTime >= fromTime && w.StartTime < toTime)).ToList();
                }
                else
                {
                    itemsData = _appView.GetList(
                    w => (string.IsNullOrEmpty(keyword)
                    || w.MaterialGroupCode.Contains(keyword)
                    || w.MaterialGroupDes.Contains(keyword)
                    || w.AssessmentCategory.Contains(keyword))
                    && (string.IsNullOrEmpty(ProductionOrderNo) || w.EmployeeName.Contains(ProductionOrderNo) || w.EmployeeNumber.Contains(ProductionOrderNo))
                    && (string.IsNullOrEmpty(MaterialNo) || w.ComponentCode.Contains(MaterialNo) || w.MaterialName.Contains(MaterialNo))
                    && (string.IsNullOrEmpty(ProductionLineCode) || w.ProductionLineCode.Contains(ProductionLineCode))
                    && (string.IsNullOrEmpty(ProductionLineDes) || w.ProductionLineDes.Contains(ProductionLineDes))
                    && (w.StartTime >= fromTime && w.StartTime < toTime)).ToList();
                }

                //单位替换
                itemsData.ForEach(item =>
                {
                    switch (item.ComponentUnit.ToUpper())
                    {
                        case "KAR":
                            item.ComponentUnit = "CAR";
                            break;
                        case "PAK":
                            item.ComponentUnit = "PAC";
                            break;
                        case "ST":
                            item.ComponentUnit = "PC";
                            break;
                        default:
                            break;
                    }
                });
                List<ExcelColumn<PP_DistributionDetail_View>> columns = ExcelService.FetchDefaultColumnList<PP_DistributionDetail_View>();
                string[] ignoreField = new string[]
                {
                    "AssessmentCategory"
                    //,"MaterialGroupCode"
                    //,"ProductionLineCode"
                    ,"EmployeeNumber"
                    ,"Remark"
                    ,"CUser"
                    ,"CTime"
                    ,"MUser"
                    ,"MTime"
                    ,"DUser"
                    ,"DTime"
                    ,"IsDelete"
                };
                List<ExcelColumn<PP_DistributionDetail_View>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<PP_DistributionDetail_View> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                //columns.ForEach((column) =>
                //{
                //    if (column.ColumnName == "IsPosted")
                //    {
                //        column.Formattor = ExcelExportFormatter.IsOrNoFormatter;
                //    }
                //});

                return ExportToExcelFile<PP_DistributionDetail_View>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #region 导出汇总表

        /// <summary>
        /// 导出汇总表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportSummary([FromUri]string keyword, [FromUri] DateTime[] dateValue, [FromUri]bool? isPosted)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];

                var listNo = _app.GetList(
                    t => (string.IsNullOrEmpty(keyword)
                        || t.DeliveryOrderNo.Contains(keyword)
                        || t.EmployeeNumber.Contains(keyword)
                        || t.EmployeeName.Contains(keyword)
                        || t.DoPostMan.Contains(keyword)
                        || t.CUser.Contains(keyword))
                        && (t.StartTime >= fromTime && t.StartTime < toTime)
                        && (isPosted == null || t.IsDoPost == isPosted))
                    .Select(s => s.DeliveryOrderNo)
                    .ToList();
                var itemsData = detailApp.GetList(t => listNo.Contains(t.DeliveryOrderNo)).ToList();
                var group = itemsData.GroupBy(g => new
                {
                    g.ProductionLineCode,
                    g.ProductionBatch,
                    g.ComponentCode,
                    g.EmployeeNumber,
                    g.DeliverLocation,
                    g.OutWarehouse,
                    g.MaterialGroupCode,
                });
                var list = new List<ExportSummary>();
                foreach (var item in group)
                {
                    list.Add(new ExportSummary
                    {
                        ProductionLineCode = item.Key.ProductionLineCode,
                        ProductionBatch = item.Key.ProductionBatch,
                        ComponentCode = item.Key.ComponentCode,
                        EmployeeNumber = item.Key.EmployeeNumber,
                        DemandQty = item.Sum(c => c.DemandQty),
                        ComponentUnit = item.FirstOrDefault(f => !string.IsNullOrEmpty(f.ComponentUnit))?.ComponentUnit,
                        EmployeeName = item.FirstOrDefault(f => !string.IsNullOrEmpty(f.EmployeeName))?.EmployeeName,
                        MaterialName = item.FirstOrDefault(f => !string.IsNullOrEmpty(f.MaterialName))?.MaterialName,
                        ProductionLineDes = item.FirstOrDefault(f => !string.IsNullOrEmpty(f.ProductionLineDes))?.ProductionLineDes,
                        OutWarehouse = item.Key.OutWarehouse,
                        DeliverLocation = item.Key.DeliverLocation,
                        MaterialGroupCode = item.Key.MaterialGroupCode,
                        MaterialGroupDes = item.FirstOrDefault(f => !string.IsNullOrEmpty(f.MaterialGroupDes))?.MaterialGroupDes,
                    });
                }
                List<ExcelColumn<ExportSummary>> columns = ExcelService.FetchDefaultColumnList<ExportSummary>();
                //string[] ignoreField = new string[]
                //{

                //};
                //List<ExcelColumn<ExportSummary>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                //foreach (ExcelColumn<ExportSummary> userColumn in ignoreFieldList)
                //{
                //    columns.Remove(userColumn);
                //}
                //columns.ForEach((column) =>
                //{
                //    if (column.ColumnName == "IsPosted")
                //    {
                //        column.Formattor = ExcelExportFormatter.IsOrNoFormatter;
                //    }
                //});

                return ExportToExcelFile<ExportSummary>(list, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #region 过账

        [HttpPost]
        public IHttpActionResult DoPost([FromBody]List<PP_MaterialDistribution> entities)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                Sys_User currentUser = GetCurrentUser();
                bool bSubmit = _app.DoPost(entities, currentUser.LoginAccount, out error_message);

                if (!bSubmit)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion
    }
}
