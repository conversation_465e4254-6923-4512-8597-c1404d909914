using System;
using System.Web.Http;
using AOS.WMS.Entity.CableProduce;
using HZ.Core.Http;
using HZ.WMS.Application.PP;
using HZ.WMS.WebAPI.Controllers;

namespace HZ.WMS.WebAPI.Areas.PP.Controllers
{
    /// <summary>
    /// 工单管理
    /// </summary>
    public class PartWorkOrderController : ApiBaseController
    {
        private Cable_PartWorkOrderApp _app = new Cable_PartWorkOrderApp();

        #region 查询工单列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri] Pagination page,
            [FromUri] Cable_PartWorkOrderListReq productionOrderListReq)
        {
            var result = new ResponseData();
            try
            {
                page.Sort = "CTime desc";
                var itemsData = _app.GetPageList(page, productionOrderListReq);
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion
    }
}