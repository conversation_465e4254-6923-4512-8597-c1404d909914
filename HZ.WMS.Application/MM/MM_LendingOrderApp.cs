using SqlSugar;
using HZ.Core.Http;
using HZ.WMS.Application.MD;
using HZ.WMS.Application.SAP;
using HZ.WMS.Application.Sys;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.MM;
using HZ.WMS.Entity.MM.Parameters;
using HZ.WMS.Entity.MM.ViewModel;
using HZ.WMS.Entity.SAP;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using SqlSugar;

namespace HZ.WMS.Application.MM
{
    /// <summary>
    /// 借出单
    /// </summary>
    public class MM_LendingOrderApp : BaseApp<MM_LendingOrder>
    {
        #region 初始化

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public MM_LendingOrderApp() : base(){ }

        MM_LendingOrderDetailApp _detailApp = new MM_LendingOrderDetailApp();

        #endregion

        #region 保存

        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="Parameters">参数类</param>
        /// <param name="user">用户</param>
        /// <param name="error_message">错误信息</param>
        /// <returns></returns>
        public bool Save(MM_LendingOrderParameters Parameters, string user, out string error_message)
        {
            error_message = "";
            try
            {
                #region 属性初始化

                DateTime time = DateTime.Now;
                var queryList = new List<MM_LendingOrder>();

                #endregion

                #region 逻辑处理

                //主表信息
                queryList.Add(new MM_LendingOrder
                {
                    ManualPostTime = Parameters.ManualPostTime,
                    DocNum = Parameters.DocNum,
                    IsPosted = false,
                    IsDelete = false,
                    CUser = user,
                    CTime = time,
                    Remark = Parameters.Remark,
                    Status = "0",
                    LendingType= Parameters.LendingType,
                    TradingPartners =Parameters.TradingPartners,
                    TradingPartnersName=Parameters.TradingPartnersName,
                    PartnersNum=Parameters.PartnersNum,
                    PartnersName=Parameters.PartnersName,
                    HandlenCode = Parameters.HandlenCode,
                    HandlenName = Parameters.HandlenName
                });

                //明细表信息
                Parameters.DetailedList.ForEach(x =>
                {
                    x.CompanyCode = "001";
                    x.FactoryCode = "2002";
                    x.IsPosted = false;
                    x.DocNum = Parameters.DocNum;
                    x.IsDelete = false;
                    x.CUser = user;
                    x.CTime = time;
                    //x.WhsCode = Parameters.WhsCode;
                    //x.WhsName = Parameters.WhsName;
                    //x.RegionCode = Parameters.RegionCode;
                    //x.RegionName = Parameters.RegionName;
                    //x.BinLocationCode = Parameters.BinLocationCode;
                    //x.BinLocationName = Parameters.BinLocationName;
                });

                #endregion

                #region 事务操作

                DbContext.Ado.BeginTran();
                _detailApp.DbContext = this.DbContext;

                //主信息插入
                Insert(queryList);

                //明细信息插入
                _detailApp.Insert(Parameters.DetailedList);

                DbContext.Ado.CommitTran();

                #endregion

                if (string.IsNullOrEmpty(error_message))
                {
                    error_message = "提交成功";
                }
                return true;
            }
            catch (Exception ex)
            {
                if (DbContext.Ado.IsAnyTran())
                    DbContext.Ado.RollbackTran();

                error_message = ex.Message;
                return false;
            }
        }

        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="Parameters">参数类</param>
        /// <param name="user">用户</param>
        /// <param name="error_message">错误信息</param>
        /// <returns></returns>
        public bool Update(MM_LendingOrderParameters Parameters, string user, out string error_message)
        {
            error_message = "";

            try
            {
                #region 属性初始化

                DateTime time = DateTime.Now;
                var AddmsDetailList = new List<MM_LendingOrderDetail>();
                var UpdatemsDetailList = new List<MM_LendingOrderDetail>();

                #endregion

                #region 逻辑处理

                //根据ID数组查询明细信息
                var entities = _detailApp.GetListByKeys(Parameters.deldetailArray);

                //根据单号查询主信息
                var query = GetList(x => x.DocNum == Parameters.DocNum)?.ToList();

                //插入子表信息 podetailed
                foreach (var x in Parameters.DetailedList)
                {
                    if (string.IsNullOrEmpty(x.DetailID))
                    {
                        x.DocNum = Parameters.DocNum;
                        x.IsDelete = false;
                        x.CUser = user;
                        x.CTime = time;
                        x.IsPosted = false;
                        x.CompanyCode = "001";
                        x.FactoryCode = "2002";
                        AddmsDetailList.Add(x);
                    }
                    else
                    {
                        x.MUser = user;
                        x.MTime = time;
                        UpdatemsDetailList.Add(x);
                    }
                }

                #endregion

                #region 事务操作

                DbContext.Ado.BeginTran();//开启事务
                _detailApp.DbContext = DbContext;

                //删除明细信息
                if (entities != null && entities.Count > 0)
                {
                    _detailApp.Delete(entities, user);
                }
                //更新主信息
                if (query != null && query.Count > 0)
                {
                    query[0].Remark = Parameters.Remark;
                    query[0].MUser = user;
                    query[0].MTime = time;
                    query[0].ManualPostTime = Parameters.ManualPostTime;
                    query[0].TradingPartners = Parameters.TradingPartners;
                    query[0].TradingPartnersName = Parameters.TradingPartnersName;
                    query[0].PartnersNum = Parameters.PartnersNum;
                    query[0].PartnersName = Parameters.PartnersName;
                    Update(query);
                }
                //插入明细信息
                if (AddmsDetailList != null && AddmsDetailList.Count > 0)
                {
                    _detailApp.Insert(AddmsDetailList);
                }
                //更新明细信息
                if (UpdatemsDetailList != null && UpdatemsDetailList.Count > 0)
                {
                    _detailApp.Update(UpdatemsDetailList);
                }

                DbContext.Ado.CommitTran();//提交事务

                #endregion

                if (string.IsNullOrEmpty(error_message))
                {
                    error_message = "更新成功";
                }
                return true;
            }
            catch (Exception ex)
            {
                if (DbContext.Ado.IsAnyTran())
                    DbContext.Ado.RollbackTran();//回滚事务

                error_message = ex.Message;
                return false;
            }
        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="DocNums">单号数组</param>
        /// <param name="opUser">当前用户</param>
        /// <param name="error_message">错误消息</param>
        public bool Delete(List<string> DocNums, string opUser, out string error_message)
        {
            error_message = "";
            bool bDeleted = true;
            //var list = new List<MM_DepRequisition>();
            //var detailList = new List<MM_DepReqDetailed>();
            //if (flag.Any(x => x.IsPosted == true))
            var flag = GetList(d => DocNums.Contains(d.DocNum)).ToList();
            foreach (var x in flag)
            {
                if (x.LendingType=="3")
                {
                    error_message = "OA审批通过后的借出单据不允许删除";
                    return false;
                }
                if (x.IsPosted == true)
                {
                    error_message = "已过帐数据不允许删除";
                    return false;
                }
            }
          
            var mark = _detailApp.GetList(d => DocNums.Contains(d.DocNum)).ToList();
            try
            {
                DbContext.Ado.BeginTran();
                _detailApp.DbContext = DbContext;

                //删除主表信息
                Delete(flag, opUser);

                //删除明细表信息
                _detailApp.Delete(mark, opUser);

                DbContext.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                bDeleted = false;
                error_message = ex.Message;
                if (DbContext.Ado.IsAnyTran())
                    DbContext.Ado.RollbackTran();
            }
            if (string.IsNullOrEmpty(error_message))
            {
                error_message = "删除成功";
            }
            return bDeleted;

        }

        #endregion

        #region 过账

        /// <summary>
        /// 过账
        /// </summary>
        /// <param name="entities">主信息</param>
        /// <param name="entitieDetails">明细信息</param>
        /// <param name="opUser">当前过账用户</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool DoPost(List<MM_LendingOrder> entities, List<MM_LendingOrderDetail> entitieDetails, string opUser, out string error_message)
        {
            error_message = "";

            foreach (var x in entities)
            {
                if (x.IsPosted == true)
                {
                    error_message = "信息已过帐，不允许重复过账";
                    return false;
                }
                else if (x.Status == "0")
                {
                    error_message = "单号[" + x.DocNum + "]还[未审核]，不允许过账";
                    return false;
                }
                else if (x.Status == "3")
                {
                    error_message = "单号[" + x.DocNum + "],[已取消]的信息，不允许过账";
                    return false;
                }

            }

            Sys_SAPCompanyInfoApp _SAPCompanyInfoApp = new Sys_SAPCompanyInfoApp();
            MD_StockApp _stockApp = new MD_StockApp();
            try
            {
                //多条数据后根据单号进行分组
                var mainInfoList = entities.GroupBy(g => new
                {
                    g.DocNum
                }).Select(q => new
                {
                    DocNum = q.Key.DocNum
                });

                //按单号分组
                foreach (var mainInfo in mainInfoList)
                {
                    bool isSale = false;
                    var list = new List<ZFGWMS011>();
                    int Line = 0;
                    var conditionList = new List<MM_LendingOrderDetail>();
                    MM_LendingOrder mod= GetList(x => x.DocNum == mainInfo.DocNum )?.ToList().FirstOrDefault();
                    if (entitieDetails != null && entitieDetails.Count > 0)//手持端添加信息后自动过账，不需要在查询一次
                    {
                        //查询明细信息
                        conditionList = entitieDetails.Where(x => x.DocNum == mainInfo.DocNum && x.IsPosted == false)?.ToList();
                    }
                    else//PC端过账
                    {
                        conditionList = _detailApp.GetList(x => x.DocNum == mainInfo.DocNum && x.IsPosted == false)?.ToList();
                    }
                    conditionList.ForEach(x =>
                    {
                        Line = Line + 1;
                        x.Line = Line;
                        var y = new ZFGWMS011();
                        y.ZNUM = Line; //condition.BaseLine;领料单行号
                        y.MATNR = x.ItemCode;//
                        y.WERKS = x.FactoryCode ?? "2002";//eq.FactoryCode
                        y.BWART = x.MovementType ?? "201";//移动类型()
                        y.MENGE = Convert.ToDecimal(x.Qty);
                        y.MEINS = x.Unit;
                        y.LGORT = x.WhsCode;
                        y.KOSTL = x.CostCenter;
                        y.SAKTO = x.LedgerType;
                        y.SGTXT = x.Remark;
                        y.BWTAR = x.AssessType;
                        if (!string.IsNullOrEmpty(x.SaleNum))
                        {
                            isSale = true;
                            y.KDAUF = x.SaleNum;
                            y.KDPOS =Convert.ToInt32(x.SaleLine);
                            y.BWART = "Z40";
                        }
                        list.Add(y);
                    });

                    bool ispost = false;
                    DateTime time = DateTime.Now;
                    //string rtnErrMsg = "";
                    //purchaseReceipt.CompanyCode 公司代码
                    //查询Sap账期时间
                    DateTime ManualPostTime = new SAPApp().GetSAPpostTime(Convert.ToDateTime(entities[0].ManualPostTime));
                    List<SAPRETURN> saplist = new List<SAPRETURN>();
                    if(!isSale) //非销售库存
                    saplist = _SAPCompanyInfoApp.ZFGWMS011(conditionList[0].CompanyCode ?? "001", mainInfo.DocNum, mod.Remark, ManualPostTime, list, out ispost, out error_message);
                    else
                        saplist = _SAPCompanyInfoApp.ZFGWMS026(conditionList[0].CompanyCode ?? "001", mainInfo.DocNum, mod.Remark, ManualPostTime, list, out ispost, out error_message);

                    if (saplist != null && saplist.Count > 0)
                    {
                        foreach (var sap in saplist)
                        {
                            //&& x.BaseNum == sap.basenum && x.BaseLine == sap.baseline
                            MM_LendingOrderDetail querydetail = new MM_LendingOrderDetail();
                            if (!isSale)
                                querydetail = conditionList.Where(x => x.DocNum == mainInfo.DocNum && x.Line == sap.line).ToList().FirstOrDefault();
                            else
                                querydetail = conditionList.Where(x => x.DocNum == mainInfo.DocNum ).ToList().FirstOrDefault();
                            if (querydetail != null)
                            {
                                querydetail.IsPosted = true;
                                querydetail.PostUser = opUser;
                                querydetail.PostTime = time;
                                querydetail.MUser = opUser;
                                querydetail.MTime = time;
                                querydetail.SapDocNum = sap.sapDocNum;
                                querydetail.SapLine = sap.sapline;

                                if (_detailApp.Update(querydetail) > 0)//更新成功后更新库存
                                {
                                    //评估类型为空
                                    if (string.IsNullOrEmpty(string.IsNullOrEmpty(querydetail.AssessType) ? "" : querydetail.AssessType.Trim()))
                                    {
                                        string stockMsg = string.Empty;
                                        //正常库存出库
                                        if (!_stockApp.StockOut(querydetail.ItemCode, querydetail.BinLocationCode, querydetail.Qty, opUser, out stockMsg))
                                        {
                                            error_message = stockMsg;
                                            //return false;
                                        }
                                    }
                                    else
                                    {
                                        string stockMsg = string.Empty;
                                        //评估类型库存出库
                                        if (!_stockApp.StockOutForAssmentType(querydetail.ItemCode, querydetail.BinLocationCode, querydetail.Qty,querydetail.AssessType, opUser, out stockMsg))
                                        {
                                            error_message = stockMsg;
                                            //return false;
                                        }
                                    }
                                }
                            }
                        }

                        var query = entities.Where(x => x.DocNum == mainInfo.DocNum).ToList().FirstOrDefault();
                        if (query != null)
                        {
                            query.IsPosted = true;
                            query.PostUser = opUser;
                            query.PostTime = time;
                            query.MUser = opUser;
                            query.MTime = time;
                            query.ManualPostTime = ManualPostTime;
                            query.SAPmark = "S";
                        }
                        //更新
                        Update(query);
                    }
                    else
                    {
                        var query = entities.Where(x => x.DocNum == mainInfo.DocNum).ToList().FirstOrDefault();
                        if (query != null)
                        {
                            query.SAPmark = "E";
                            query.SAPmessage = error_message;
                        }
                        //更新
                        Update(query);

                        error_message = "单号：" + mainInfo.DocNum + "，过账失败，失败原因：" + error_message;
                        return false;
                    }
                }
                if (string.IsNullOrEmpty(error_message))
                {
                    error_message = "过账成功";
                }
                return true;
            }
            catch (Exception ex)
            {
                error_message = ex.Message;
                return false;
            }
        }

        #endregion

        #region 审核

        /// <summary>
        /// 审核
        /// </summary>
        /// <param name="DocNums">单号数组</param>
        /// <param name="opUser">当前用户</param>
        /// <param name="error_message">错误消息</param>
        /// <param name="type">类型 1:提交失败 2：过账失败</param>
        public bool Audits(string[] DocNums, string opUser, out string error_message, out string type)
        {
            error_message = "";
            type = "";

            var flag = GetList(d => DocNums.Contains(d.DocNum)).ToList();
            foreach (var x in flag)
            {
                if (x.IsPosted == true)
                {
                    error_message = "已过帐的信息不允许重复审核";
                    type = "1";
                    return false;
                }
                if (x.Status == "2")
                {
                    error_message = "[已审核]的信息,请勿重复审核";
                    type = "1";
                    return false;
                }
                //else if (x.Status == "3")
                //{
                //    error_message = "[已取消]的信息，暂不支持再次审核";
                //    type = "1";
                //    return false;
                //}
            }

            #region 库存校验

            //foreach (var x in Parameters.detailed)
            //{
            //    string message = "";
            //    //库存校验
            //    bool result = _stockApp.ValidateStockOut(x.ItemCode, Parameters.BinLocationCode, x.SalesOrderNum, Convert.ToInt32(x.SalesOrderLine), x.ReturnScanQty, out message, null);
            //    bool result = _stockApp.ValidateStockOut(x.ItemCode, Parameters.BinLocationCode, x.ReturnScanQty, out message, null, null);
            //    if (!result)
            //    {
            //        error_message = message;
            //        type = "1";
            //        return false;
            //    }
            //}

            #endregion

            DateTime date= DateTime.Now;
            try
            {
                flag.ForEach(x =>
                {
                    x.Status = "2";
                    x.AuditUser = opUser;
                    x.AuditDate = date;
                    x.MUser = opUser;
                    x.MTime = date;
                });
                DbContext.Ado.BeginTran();//开启事务

                Update(flag);

                DbContext.Ado.CommitTran();//提交事务

                #region 是否自动过账

                Sys_SwithConfigApp _switchApp = new Sys_SwithConfigApp();
                if (_switchApp.IsLendingOrderAutoPost)
                {
                    string postMsg = "";
                    bool bpost = DoPost(flag, null, opUser, out postMsg);
                    if (!bpost)
                    {
                        error_message = "审核成功,"+postMsg;
                        type = "2";
                        return false;
                    }
                    else
                    {
                        error_message = "审核并" + postMsg;
                    }
                }

                #endregion

                if (string.IsNullOrEmpty(error_message))
                {
                    error_message = "审核成功";
                }
                return true;
            }
            catch (Exception ex)
            {
                if (DbContext.Ado.IsAnyTran())
                    DbContext.Ado.RollbackTran();//回滚事务
                error_message = ex.Message;
                type = "1";
                return false;
            }
        }

        #endregion

        #region 取消

        /// <summary>
        /// 取消
        /// </summary>
        /// <param name="DocNums">单号数组</param>
        /// <param name="opUser">当前用户</param>
        /// <param name="error_message">错误消息</param>
        public bool Rejects(string[] DocNums, string opUser, out string error_message)
        {
            error_message = "";

            var flag = GetList(d => DocNums.Contains(d.DocNum)).ToList();
            foreach (var x in flag)
            {
                if (x.IsPosted==true)
                {
                    error_message = "已过帐的信息不允许取消";
                    return false;
                }
                if (x.Status == "0")
                {
                    error_message = "[未审核]的信息，请先进行审核后才可进行取消操作";
                    return false;
                }
                else if (x.Status == "3")
                {
                    error_message = "[已取消]的信息,请勿重复取消";
                    return false;
                }
            };

            DateTime date = DateTime.Now;
            try
            {
                flag.ForEach(x =>
                {
                    x.Status = "3";
                    x.RejectUser = opUser;
                    x.RejectDate = date;
                    x.MUser = opUser;
                    x.MTime = date;
                });
                DbContext.Ado.BeginTran();//开启事务

                Update(flag);

                DbContext.Ado.CommitTran();//提交事务

                if (string.IsNullOrEmpty(error_message))
                {
                    error_message = "取消成功";
                }
                return true;
            }
            catch (Exception ex)
            {
                if (DbContext.Ado.IsAnyTran())
                    DbContext.Ado.RollbackTran();//失败回滚
                error_message = ex.Message;
                return false;
            }
        }

        #endregion

        #region 查询导出数据信息

        /// <summary>
        /// 查询导出数据信息
        /// </summary>
        /// <param name="page"></param>
        /// <param name="condition"></param>
        /// <returns></returns>
        public ISugarQueryable<MM_LendingOrderExport_View> GetLendingOrderExportView(Expression<Func<MM_LendingOrderExport_View, bool>> condition)
        {
            var query = DbContext.Queryable<MM_LendingOrderExport_View>()
                .Where(condition)
                .OrderBy("CTime desc");
            return query;
        }

        #endregion

        #region OA同步WMS借出单保存

        /// <summary>
        /// OA同步WMS借出单保存
        /// </summary>
        /// <param name="ShiPingOMS"></param>
        /// <param name="user">用户</param>
        /// <param name="error_message">错误信息</param>
        /// <returns></returns>
        public bool SaveForOA(List<MM_LendingOrderDetail> ShiPingOMS, string user, out string error_message)
        {
            error_message = "借出单接口调用成功";

            //var listlendWms = new List<MM_LendingOrderDetail>();
            //var x = new MM_LendingOrderDetail();
            //x.ItemCode = "test";//物料编码
            //x.ItemName = "测试";//物料名称
            //x.Qty = 1;//数量
            //x.WhsCode = "Y001";//借出仓库编码
            //x.ReturnDate = DateTime.Now;//归还日期
            //x.ReturnWhsCode = "X001";//归还仓库
            //listlendWms.Add(x);

            //MD_CustomerAddApp _CustomerAddApp = new MD_CustomerAddApp();
            //MD_FreightMileageApp _FreightMileageApp = new MD_FreightMileageApp();
            //MD_StockApp _stockApp = new MD_StockApp();
            try
            {
                //foreach (MM_RedeployApplyDetail x in ShiPingOMS)
                //{
                //    if (_detailApp.GetList(t => t.SalesOrderNumber == x.SalesOrderNumber && t.SalesLine == x.SalesLine).ToList().FirstOrDefault() != null)
                //    {
                //        error_message = "销售单号[" + x.SalesOrderNumber + "],销售行号[" + x.SalesLine + "]已存在发运计划信息，请勿重复生成";
                //        return false;
                //    }
                //}

                //detailed.Where((x, i) => detailed.FindIndex(z => z.DocNum == x.DocNum) == i) 去除某一列重复值
                //detailed.Distinct() 去重

                //string DocNum = _baseApp.GetNewDocNum(DocType.SD, DocFixedNumDef.SD_ShippingPlan);
                //DateTime date = DateTime.Now;
                //var query = new SD_ShippingPlan
                //{
                //    DocNum = DocNum,
                //    DeliveryDate = Convert.ToDateTime(Convert.ToDateTime(ShiPingOMS[0].DeliveryDate).ToString("yyyy-MM-dd")),
                //    IsDelete = false,
                //    CUser = user,
                //    CTime = date,
                //    Remark = "",
                //    ShippingPlanStatus = 0,
                //};

                //var querydetails = new List<SD_ShippingPlanDetail>();
                //foreach (SD_ShippingForOMS x in ShiPingOMS)
                //{
                //    var y = new SD_ShippingPlanDetail();
                //    y.DeliveryDate = x.PromisedDeliveryDate;
                //    y.DocNum = DocNum;
                //    y.IsDelete = false;
                //    y.CUser = user;
                //    y.CTime = date;
                //    y.SalesOrderNumber = x.SalesOrderNumber;
                //    y.SalesLine = x.SalesLine;
                //    y.SalesType = x.SalesType;
                //    y.SalesOrganization = x.SalesOrganization;
                //    y.VoucherDate = x.VoucherDate;
                //    y.CustomerCode = x.CustomerCode;
                //    y.CustomerName = x.CustomerName;
                //    y.CustomerAdd = x.CustomerAdd;
                //    y.BatchNum = x.BatchNum;
                //    y.ItemCode = x.ItemCode;
                //    y.ItemName = x.ItemName;
                //    y.ShippingPlanDetailQty = x.ShippingPlanDetailQty;
                //    y.CONT = x.CONT;

                //    ////根据客户地址查询结算地址等信息
                //    var CustomerAdd = _CustomerAddApp.GetList(t => t.CustomerAdd == y.CustomerAdd)?.ToList().FirstOrDefault();
                //    if (CustomerAdd != null)
                //    {
                //        y.SettlementAdd = CustomerAdd.SettlementAdd;
                //        y.Contact = CustomerAdd.Contact;
                //        y.Telephone = CustomerAdd.Telephone;
                //    }
                //    else
                //    {
                //        y.SettlementAdd = "";
                //        y.Contact = "";
                //        y.Telephone = "";
                //    }

                //    //根据结算地址查询物流供应商信息
                //    var Mileageinfo = _FreightMileageApp.GetList(t => t.SettlementAdd == y.SettlementAdd)?.ToList().FirstOrDefault();
                //    if (Mileageinfo != null)
                //    {
                //        y.SupplierCode = Mileageinfo.SupplierCode;
                //        y.SupplierName = Mileageinfo.SupplierName;
                //        y.DeliveryUser = Mileageinfo.UserCode;
                //        y.DeliveryUserName = Mileageinfo.UserName;
                //    }

                //    //获取出厂编号
                //    if (string.IsNullOrEmpty(string.IsNullOrEmpty(y.OUTNO) ? "" : y.OUTNO.Trim()))
                //    {
                //        //查询出厂编号信息 要抓库存数据！
                //        var barcode = _stockApp.GetList(t => t.SaleNum == y.SalesOrderNumber && t.SaleLine == y.SalesLine).ToList().FirstOrDefault();
                //        y.OUTNO = barcode != null ? barcode.BarCode : "";
                //    }
                //    var z = DbContext.Queryable<SAP_VBAK_View>().Where(t => t.VBELN == y.SalesOrderNumber && t.POSNR == y.SalesLine).ToList().FirstOrDefault();
                //    if (z != null)
                //    {
                //        y.CreationDate = z.ERDAT;
                //        y.DistributionChannels = z.VTWEG;
                //        y.ProductGroup = z.SPART;
                //        y.BSTNK = z.BSTNK;
                //        y.ProjectCategory = z.PSTYV;
                //        y.Unit = z.MEINS;
                //        y.WhsCode = z.LGORT;
                //        var whsname = DbContextForSAP.Queryable<XZ_SAP_T001L>().Where(t => t.LGORT == y.WhsCode).ToList().FirstOrDefault();
                //        y.WhsName = whsname != null ? whsname.LGOBE : "";
                //        y.PRCTR = z.PRCTR;
                //        y.VSTEL = z.VSTEL;
                //        y.VGBEL = z.VGBEL;
                //        y.VGPOS = z.VGPOS;
                //    }
                //    querydetails.Add(y);
                //}

                //DbContext.Ado.BeginTran();
                //_detailApp.DbContext = this.DbContext;
                ////交运计划信息插入
                //this.Insert(query);

                ////交运计划信息明细插入
                //_detailApp.Insert(querydetails);

                //DbContext.Ado.CommitTran();

                return true;
            }
            catch (Exception ex)
            {
                //if (DbContext.Ado.IsAnyTran())
                //    DbContext.Ado.RollbackTran();
                error_message = ex.Message;
                return false;
            }
        }

        #endregion

        /// <summary>
        /// 查询物料信息
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <returns></returns>
        public List<MM_LendingOrderItem_View> GetItem(Pagination page, string keyword,string Whs)
        {
            Expression<Func<MM_LendingOrderItem_View, bool>> condition = x => (string.IsNullOrEmpty(keyword)
                                                                  || x.ItemCode.Contains(keyword) || x.ItemName.Contains(keyword)
                                                                 ) && (string.IsNullOrEmpty(Whs)
                                                                  || x.WhsCode.Contains(keyword) || x.WhsName.Contains(keyword)
                                                                 );
            var query = DbContext.Queryable<MM_LendingOrderItem_View>()
                .Where(condition);
            int total = query.Count();
            page.Total = total;
            if (page.PageSize > total)
            {
                return query.ToPageList(page.PageNumber, total);
            }
            return query.ToPageList(page.PageNumber, page.PageSize);
        }


    }
}
