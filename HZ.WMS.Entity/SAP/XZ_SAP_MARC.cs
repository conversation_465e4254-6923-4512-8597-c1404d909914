using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.SAP
{
    /// <summary>
    ///SAP中间库-物料主数据
    /// </summary>
    [SugarTable("XZ_SAP_MARC")]
    public class XZ_SAP_MARC
    {
        /// <summary>
        /// 工厂
        /// </summary>
        [Description("工厂")]
        public string WERKS { get; set; }

        /// <summary>
        /// 物料编号
        /// </summary>
        [Description("物料编号")]
        public string MATNR { get; set; }

        /// <summary>
        /// 物料描述
        /// </summary>
        [Description("物料描述")]
        public string MAKTX { get; set; }

        /// <summary>
        /// 基本单位
        /// </summary>
        [Description("基本单位")]
        public string MEINS { get; set; }

        /// <summary>
        /// 物料组
        /// </summary>
        [Description("物料组")]
        public string MATKL { get; set; }

        /// <summary>
        /// 物料组描述
        /// </summary>
        [Description("物料组描述")]
        public string WGBEZ { get; set; }

        /// <summary>
        /// 物料类型
        /// </summary>
        [Description("物料类型")]
        public string MTART { get; set; }

        /// <summary>
        /// 物料类型描述
        /// </summary>
        [Description("物料类型描述")]
        public string MTBEZ { get; set; }

        /// <summary>
        /// 外部物料组
        /// </summary>
        [Description("外部物料组")]
        public string EXTWG { get; set; }

        /// <summary>
        /// 外部物料组描述
        /// </summary>
        [Description("外部物料组描述")]
        public string EWBEZ { get; set; }

        /// <summary>
        /// 采购单位
        /// </summary>
        [Description("采购单位")]
        public string BSTME { get; set; }

        /// <summary>
        /// 采购类型
        /// </summary>
        [Description("采购类型")]
        public string BESKZ { get; set; }

        /// <summary>
        /// 特殊采购类型
        /// </summary>
        [Description("特殊采购类型")]
        public string SOBSL { get; set; }

        /// <summary>
        /// 物料删除标识
        /// </summary>
        [Description("物料删除标识")]
        public string LVORM { get; set; }

        /// <summary>
        /// 物料删除标识
        /// </summary>
        [Description("物料删除标识")]
        public string LVORA { get; set; }

        /// <summary>
        /// 最小批量水平
        /// </summary>
        [Description("最小批量水平")]
        public string BSTMI { get; set; }

        /// <summary>
        /// 最大批量水平
        /// </summary>
        [Description("最大批量水平")]
        public string BSTMA { get; set; }

        /// <summary>
        /// 毛重
        /// </summary>
        [Description("毛重")]
        public decimal? BRGEW { get; set; }

        /// <summary>
        /// 净重
        /// </summary>
        [Description("净重")]
        public decimal? NTGEW { get; set; }

        /// <summary>
        /// 重量单位
        /// </summary>
        [Description("重量单位")]
        public string GEWEI { get; set; }

        /// <summary>
        /// 工厂描述
        /// </summary>
        [Description("工厂描述")]
        public string NAME1 { get; set; }

        /// <summary>
        /// 采购组描述
        /// </summary>
        [Description("采购组描述")]
        public string EKNAM { get; set; }

        /// <summary>
        /// 系列型号
        /// </summary>
        [Description("系列型号")]
        public string ZPRDGPC { get; set; }

        /// <summary>
        /// 系列型号描述
        /// </summary>
        [Description("系列型号描述")]
        public string ZPRDGPD { get; set; }

        /// <summary>
        /// 采购组
        /// </summary>
        [Description("采购组")]
        public string EKGRP { get; set; }

        /// <summary>
        /// 评估类型
        /// </summary>
        [Description("评估类型")]
        public string BWTAR { get; set; }

        /// <summary>
        /// 评估类别
        /// </summary>
        [Description("评估类别")]
        public string BWTTY { get; set; }

        /// <summary>
        /// 单位换算率分母
        /// </summary>
        [Description("单位换算率分母")]
        public string UMREN { get; set; }

        /// <summary>
        /// 单位换算率分子
        /// </summary>
        [Description("单位换算率分子")]
        public string UMREZ { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        [Description("状态")]
        public bool? Status { get; set; }

        /// <summary>
        /// 跨工厂物料状态
        /// </summary>
        [Description("跨工厂物料状态")]
        public string MSTAE { get; set; }
      

    }
}
