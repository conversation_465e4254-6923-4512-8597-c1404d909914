using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;

//
using HZ.Core.Security;
using HZ.WMS.Application.MD;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.Sys;
using HZ.Core.Http;
using HZ.WMS.WebAPI.Controllers;
using Newtonsoft.Json;
using HZ.Core.Vue;
using HZ.Core.Office;
using HZ.WMS.Application;

namespace HZ.WMS.WebAPI.Areas.MD.Controllers
{
    /// <summary>
    /// 仓库管理：法拉鼎只有一个仓库
    /// </summary>
    public class MD_WarehouseController : ApiBaseController
    {
        private MD_WarehouseApp _app = new MD_WarehouseApp();

        #region 查询分页列表

        /// <summary>
        /// 查询分页列表 
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]string keyword)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetPageList(page, x => string.IsNullOrEmpty(keyword)
                || x.WhsCode.Contains(keyword)
                || x.WhsName.Contains(keyword))?.ToList();

                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion


        #region 查询仓库

        /// <summary>
        /// 查询仓库清单
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetList()
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.GetList().ToList();
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 添加
        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Add([FromBody]MD_Warehouse entity)
        {
            var result = new ResponseData();
            try
            {
				Sys_User currLoginUser = GetCurrentUser();
                entity.CUser = currLoginUser.LoginAccount;
                entity.MUser = currLoginUser.LoginAccount;
				entity.CTime = DateTime.Now;
                entity.MTime = DateTime.Now;
                result.Data = _app.Insert(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取详情
        /// <summary>
        /// 获取详情
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetEntity([FromUri]string ID)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.GetEntityByKey(ID);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Update([FromBody]MD_Warehouse entity)
        {
            var result = new ResponseData();
            try
            {
                entity.MUser = GetCurrentUser().LoginAccount;
				entity.MTime = DateTime.Now;
                result.Data = _app.Update(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除（包括批量）

        /// <summary>
        /// 删除
        /// </summary>
        /// <returns></returns>
        [HttpDelete]
        public IHttpActionResult Delete([FromBody]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.DeleteByKeys(ids, GetCurrentUser().LoginAccount);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 查询默认仓库

        /// <summary>
        /// 查询仓库清单
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetDefaultWarehouse()
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.GetList().ToList().FirstOrDefault();
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 查询仓库下所有区域

        /// <summary>
        /// 查询仓库下所有区域
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetWarehouseRegion([FromUri]string whsCode)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.GetWarehouseRegion(whsCode);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 根据库位查询仓库

        /// <summary>
        /// 查询仓库清单
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetWarehouseByBinLocation([FromUri] string binLocationCode)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.GetWarehouseByBinLocationCode(binLocationCode);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 导出

        /// <summary>
        /// 
        /// </summary>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] string keyword)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetList().Where(t => string.IsNullOrEmpty(keyword) || t.WhsCode.Contains(keyword)|| t.WhsName.Contains(keyword)).ToList();
                //var itemsData = _app.GetList().ToList();
                List<ExcelColumn<MD_Warehouse>> columns = ExcelService.FetchDefaultColumnList<MD_Warehouse>();
                string[] ignoreField = new string[] { "IsDelete", "DTime", "DUser" };

                List<ExcelColumn<MD_Warehouse>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<MD_Warehouse> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }

                //columns.ForEach((column) =>
                //{
                //    if (column.ColumnName == "OrganizationID")
                //    {
                //        column.Formattor = ExcelExportFormatter.OrganizationFormatter;
                //    }

                //    if (column.ColumnName == "IsEnable")
                //    {
                //        column.Formattor = ExcelExportFormatter.IsEnableFormatter;
                //    }

                //    if (column.ColumnName == "Gender")
                //    {
                //        column.Formattor = ExcelExportFormatter.GenderFormatter;
                //    }
                //});

                return ExportToExcelFile<MD_Warehouse>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }

        }
        #endregion
    }
}

