using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using HZ.Core.Security;
using HZ.WMS.Entity.Sys;
using HZ.Core.Http;
using System.Threading.Tasks;

namespace HZ.WMS.Application.Sys
{

    /// <summary>
    ///
    /// </summary>
    public class Sys_DbBackupConfigApp : BaseApp<Sys_DbBackupConfig>
    {

        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public Sys_DbBackupConfigApp() : base()
        {
        }

        #endregion

        #region 获取数据库备份配置
        /// <summary>
        /// 获取数据库备份配置
        /// </summary>
        /// <returns></returns>
        public Sys_DbBackupConfig GetDbBackupConfig()
        {
            return GetList().ToList().FirstOrDefault();
        }

        #endregion

        #region 保存数据库备份配置
        /// <summary>
        /// 
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public int SaveDbBackupConfig(Sys_DbBackupConfig entity)
        {
            if (entity.BackupFilePath.Substring(entity.BackupFilePath.Length - 1, 1) != "\\")
            {
                entity.BackupFilePath = entity.BackupFilePath + "\\";
            }
            if (entity.RestoreFilePath.Substring(entity.RestoreFilePath.Length - 1, 1) != "\\")
            {
                entity.RestoreFilePath = entity.RestoreFilePath + "\\";
            }
            return base.Update(entity);
        }

        #endregion

    }
}

