/// <XRTypeInfo>
///   <AssemblyFullName>DevExpress.XtraReports.v16.1, Version=16.1.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</AssemblyFullName>
///   <AssemblyLocation>C:\WINDOWS\Microsoft.Net\assembly\GAC_MSIL\DevExpress.XtraReports.v16.1\v4.0_16.1.5.0__b88d1754d700e49a\DevExpress.XtraReports.v16.1.dll</AssemblyLocation>
///   <TypeName>DevExpress.XtraReports.UI.XtraReport</TypeName>
///   <Localization>zh-CN</Localization>
///   <Version>16.1</Version>
///   <Resources>
///     <Resource Name="XtraReportSerialization.XtraReport">
/// zsrvvgEAAACRAAAAbFN5c3RlbS5SZXNvdXJjZXMuUmVzb3VyY2VSZWFkZXIsIG1zY29ybGliLCBWZXJzaW9uPTQuMC4wLjAsIEN1bHR1cmU9bmV1dHJhbCwgUHVibGljS2V5VG9rZW49Yjc3YTVjNTYxOTM0ZTA4OSNTeXN0ZW0uUmVzb3VyY2VzLlJ1bnRpbWVSZXNvdXJjZVNldAIAAAABAAAAAAAAAFBBRFBBRFA4hdt+AAAAAA8BAABOcwBxAGwARABhAHQAYQBTAG8AdQByAGMAZQAxAC4AUgBlAHMAdQBsAHQAUwBjAGgAZQBtAGEAUwBlAHIAaQBhAGwAaQB6AGEAYgBsAGUAAAAAAAGoB1BFUmhkR0ZUWlhRZ1RtRnRaVDBpYzNGc1JHRjBZVk52ZFhKalpURWlQanhXYVdWM0lFNWhiV1U5SWxORVgwUmxiR2wyWlhKNVYyRjJaU0krUEVacFpXeGtJRTVoYldVOUlrUnZZ
/// MDUxYlNJZ1ZIbHdaVDBpVTNSeWFXNW5JaUF2UGp4R2FXVnNaQ0JPWVcxbFBTSkRkWE4wYjIxbGNrNWhiV1VpSUZSNWNHVTlJbE4wY21sdVp5SWdMejQ4Um1sbGJHUWdUbUZ0WlQwaVUyaHBjRlJwYldVaUlGUjVjR1U5SWtSaGRHVlVhVzFsSWlBdlBqeEdhV1ZzWkNCT1lXMWxQU0pRVlhObGNpSWdWSGx3WlQwaVUzUnlhVzVuSWlBdlBqd3ZWbWxsZHo0OFZtbGxkeUJPWVcxbFBTSlRSRjlFWld4cGRtVnllVmRoZG1WRVpYUmhhV3hsWkNJK1BFWnBaV3hrSUU1aGJXVTlJa0poYzJWT2RXMGlJRlI1Y0dVOUlsTjBjbWx1WnlJZ0x6NDhSbWxsYkdRZ1RtRnRaVDBpUW1GelpVeHBibVVpSUZSNWNHVTlJa2x1ZERNeUlpQXZQanhHYVdWc1pDQk9ZVzFsUFNKSmRHVnRRMjlr
/// WlNJZ1ZIbHdaVDBpVTNSeWFXNW5JaUF2UGp4R2FXVnNaQ0JPWVcxbFBTSkpkR1Z0VG1GdFpTSWdWSGx3WlQwaVUzUnlhVzVuSWlBdlBqeEdhV1ZzWkNCT1lXMWxQU0pSZEhraUlGUjVjR1U5SWtSbFkybHRZV3dpSUM4K1BFWnBaV3hrSUU1aGJXVTlJbFZ1YVhRaUlGUjVjR1U5SWxOMGNtbHVaeUlnTHo0OFJtbGxiR1FnVG1GdFpUMGlRbTk0WlhNaUlGUjVjR1U5SWtsdWRETXlJaUF2UGp4R2FXVnNaQ0JPWVcxbFBTSk9aWFJYWldsbmFIUWlJRlI1Y0dVOUlrUmxZMmx0WVd3aUlDOCtQRVpwWld4a0lFNWhiV1U5SWtkeWIzTnpWMlZwWjJoMElpQlVlWEJsUFNKRVpXTnBiV0ZzSWlBdlBqeEdhV1ZzWkNCT1lXMWxQU0pRWVd4c1pYUk9kVzBpSUZSNWNHVTlJa2x1ZERN
/// eUlpQXZQanhHYVdWc1pDQk9ZVzFsUFNKTlpYUnlaVk4xYlNJZ1ZIbHdaVDBpUkdWamFXMWhiQ0lnTHo0OEwxWnBaWGMrUEM5RVlYUmhVMlYwUGc9PQ==</Resource>
///   </Resources>
/// </XRTypeInfo>
namespace XtraReportSerialization {
    
    public class XtraReport : DevExpress.XtraReports.UI.XtraReport {
        private DevExpress.XtraReports.UI.PageFooterBand pageFooterBand1;
        private DevExpress.XtraReports.UI.XRLabel label9;
        private DevExpress.XtraReports.UI.XRTableCell tableCell3;
        private DevExpress.XtraReports.UI.XRTableCell tableCell21;
        private DevExpress.XtraReports.UI.TopMarginBand topMarginBand1;
        private DevExpress.XtraReports.UI.XRLabel label10;
        private DevExpress.XtraReports.UI.XRBarCode barCode1;
        private DevExpress.XtraReports.UI.XRLabel label33;
        private DevExpress.XtraReports.UI.XRLabel label32;
        private DevExpress.XtraReports.UI.XRLabel label31;
        private DevExpress.XtraReports.UI.XRLabel label30;
        private DevExpress.XtraReports.UI.XRLabel label29;
        private DevExpress.XtraReports.UI.XRLabel label28;
        private DevExpress.XtraReports.UI.XRLabel label27;
        private DevExpress.XtraReports.UI.XRLabel label26;
        private DevExpress.XtraReports.UI.XRLabel label25;
        private DevExpress.XtraReports.UI.XRLabel label24;
        private DevExpress.XtraReports.UI.XRLabel label22;
        private DevExpress.XtraReports.UI.XRTableCell tableCell6;
        private DevExpress.XtraReports.UI.XRTableCell tableCell14;
        private DevExpress.XtraReports.UI.XRTableCell tableCell2;
        private DevExpress.XtraReports.UI.XRTableRow tableRow2;
        private DevExpress.XtraReports.UI.XRTableCell tableCell12;
        private DevExpress.XtraReports.UI.XRTableCell tableCell13;
        private DevExpress.XtraReports.UI.XRTableCell tableCell15;
        private DevExpress.XtraReports.UI.XRTableCell tableCell16;
        private DevExpress.XtraReports.UI.XRTableCell tableCell18;
        private DevExpress.XtraReports.UI.XRTableCell tableCell19;
        private DevExpress.XtraReports.UI.XRTableCell tableCell20;
        private DevExpress.XtraReports.UI.XRTableCell tableCell22;
        private DevExpress.XtraReports.UI.XRTableCell tableCell17;
        private DevExpress.XtraReports.UI.XRControlStyle DataField;
        private DevExpress.XtraReports.UI.XRTableCell tableCell4;
        private DevExpress.XtraReports.UI.XRTableCell tableCell9;
        private DevExpress.XtraReports.UI.XRTableCell tableCell11;
        private DevExpress.XtraReports.UI.XRTable table1;
        private DevExpress.XtraReports.UI.XRTableRow tableRow1;
        private DevExpress.XtraReports.UI.XRTableCell tableCell1;
        private DevExpress.XtraReports.UI.XRTableCell tableCell5;
        private DevExpress.XtraReports.UI.XRTableCell tableCell7;
        private DevExpress.XtraReports.UI.XRTableCell tableCell8;
        private DevExpress.XtraReports.UI.XRTableCell tableCell10;
        private DevExpress.XtraReports.UI.XRControlStyle FieldCaption;
        private DevExpress.XtraReports.UI.DetailBand detailBand1;
        private DevExpress.XtraReports.UI.XRTable table2;
        private DevExpress.DataAccess.Sql.SqlDataSource sqlDataSource1;
        private DevExpress.XtraReports.UI.XRControlStyle Title;
        private DevExpress.XtraReports.UI.BottomMarginBand bottomMarginBand1;
        private DevExpress.XtraReports.UI.ReportHeaderBand reportHeaderBand1;
        private DevExpress.XtraReports.Parameters.Parameter DocNum;
        private DevExpress.XtraReports.UI.XRControlStyle PageInfo;
        private System.Resources.ResourceManager _resources;
        private string _resourceString;
        public XtraReport() {
            this._resourceString = DevExpress.XtraReports.Serialization.XRResourceManager.GetResourceFor("XtraReportSerialization.XtraReport");
            this.InitializeComponent();
        }
        private System.Resources.ResourceManager resources {
            get {
                if (_resources == null) {
                    this._resources = new DevExpress.XtraReports.Serialization.XRResourceManager(this._resourceString);
                }
                return this._resources;
            }
        }
        private void InitializeComponent() {
            DevExpress.DataAccess.ConnectionParameters.MsSqlConnectionParameters msSqlConnectionParameters1 = new DevExpress.DataAccess.ConnectionParameters.MsSqlConnectionParameters();
            DevExpress.DataAccess.Sql.SelectQuery selectQuery1 = new DevExpress.DataAccess.Sql.SelectQuery();
            DevExpress.DataAccess.Sql.Column column1 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression1 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Table table3 = new DevExpress.DataAccess.Sql.Table();
            DevExpress.DataAccess.Sql.Column column2 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression2 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column3 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression3 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column4 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression4 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.QueryParameter queryParameter1 = new DevExpress.DataAccess.Sql.QueryParameter();
            DevExpress.DataAccess.Sql.SelectQuery selectQuery2 = new DevExpress.DataAccess.Sql.SelectQuery();
            DevExpress.DataAccess.Sql.Column column5 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression5 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Table table4 = new DevExpress.DataAccess.Sql.Table();
            DevExpress.DataAccess.Sql.Column column6 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression6 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column7 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression7 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column8 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression8 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column9 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression9 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column10 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression10 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column11 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression11 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column12 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression12 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column13 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression13 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column14 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression14 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.Column column15 = new DevExpress.DataAccess.Sql.Column();
            DevExpress.DataAccess.Sql.ColumnExpression columnExpression15 = new DevExpress.DataAccess.Sql.ColumnExpression();
            DevExpress.DataAccess.Sql.QueryParameter queryParameter2 = new DevExpress.DataAccess.Sql.QueryParameter();
            DevExpress.XtraPrinting.BarCode.QRCodeGenerator qRCodeGenerator1 = new DevExpress.XtraPrinting.BarCode.QRCodeGenerator();
            this.pageFooterBand1 = new DevExpress.XtraReports.UI.PageFooterBand();
            this.tableCell3 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell21 = new DevExpress.XtraReports.UI.XRTableCell();
            this.topMarginBand1 = new DevExpress.XtraReports.UI.TopMarginBand();
            this.tableCell6 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell14 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell2 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableRow2 = new DevExpress.XtraReports.UI.XRTableRow();
            this.tableCell20 = new DevExpress.XtraReports.UI.XRTableCell();
            this.label25 = new DevExpress.XtraReports.UI.XRLabel();
            this.tableCell13 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell12 = new DevExpress.XtraReports.UI.XRTableCell();
            this.DataField = new DevExpress.XtraReports.UI.XRControlStyle();
            this.tableCell4 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell9 = new DevExpress.XtraReports.UI.XRTableCell();
            this.label24 = new DevExpress.XtraReports.UI.XRLabel();
            this.tableCell11 = new DevExpress.XtraReports.UI.XRTableCell();
            this.table1 = new DevExpress.XtraReports.UI.XRTable();
            this.label26 = new DevExpress.XtraReports.UI.XRLabel();
            this.tableRow1 = new DevExpress.XtraReports.UI.XRTableRow();
            this.label27 = new DevExpress.XtraReports.UI.XRLabel();
            this.tableCell7 = new DevExpress.XtraReports.UI.XRTableCell();
            this.label9 = new DevExpress.XtraReports.UI.XRLabel();
            this.label30 = new DevExpress.XtraReports.UI.XRLabel();
            this.FieldCaption = new DevExpress.XtraReports.UI.XRControlStyle();
            this.detailBand1 = new DevExpress.XtraReports.UI.DetailBand();
            this.tableCell1 = new DevExpress.XtraReports.UI.XRTableCell();
            this.label33 = new DevExpress.XtraReports.UI.XRLabel();
            this.sqlDataSource1 = new DevExpress.DataAccess.Sql.SqlDataSource();
            this.tableCell18 = new DevExpress.XtraReports.UI.XRTableCell();
            this.Title = new DevExpress.XtraReports.UI.XRControlStyle();
            this.tableCell22 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell16 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell19 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell8 = new DevExpress.XtraReports.UI.XRTableCell();
            this.bottomMarginBand1 = new DevExpress.XtraReports.UI.BottomMarginBand();
            this.tableCell17 = new DevExpress.XtraReports.UI.XRTableCell();
            this.reportHeaderBand1 = new DevExpress.XtraReports.UI.ReportHeaderBand();
            this.tableCell10 = new DevExpress.XtraReports.UI.XRTableCell();
            this.label32 = new DevExpress.XtraReports.UI.XRLabel();
            this.tableCell15 = new DevExpress.XtraReports.UI.XRTableCell();
            this.label22 = new DevExpress.XtraReports.UI.XRLabel();
            this.label29 = new DevExpress.XtraReports.UI.XRLabel();
            this.DocNum = new DevExpress.XtraReports.Parameters.Parameter();
            this.label28 = new DevExpress.XtraReports.UI.XRLabel();
            this.tableCell5 = new DevExpress.XtraReports.UI.XRTableCell();
            this.table2 = new DevExpress.XtraReports.UI.XRTable();
            this.label10 = new DevExpress.XtraReports.UI.XRLabel();
            this.PageInfo = new DevExpress.XtraReports.UI.XRControlStyle();
            this.label31 = new DevExpress.XtraReports.UI.XRLabel();
            this.barCode1 = new DevExpress.XtraReports.UI.XRBarCode();
            ((System.ComponentModel.ISupportInitialize)(this.table1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.table2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // pageFooterBand1
            // 
            this.pageFooterBand1.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
                        this.label9});
            this.pageFooterBand1.Dpi = 96F;
            this.pageFooterBand1.HeightF = 111.8F;
            this.pageFooterBand1.Name = "pageFooterBand1";
            this.pageFooterBand1.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 2, 96F);
            this.pageFooterBand1.StylePriority.UsePadding = false;
            // 
            // tableCell3
            // 
            this.tableCell3.Dpi = 96F;
            this.tableCell3.Multiline = true;
            this.tableCell3.Name = "tableCell3";
            this.tableCell3.Text = "物料代码\r\nItem Code";
            this.tableCell3.Weight = 1D;
            // 
            // tableCell21
            // 
            this.tableCell21.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "SD_DeliveryWaveDetailed.PalletNum")});
            this.tableCell21.Dpi = 96F;
            this.tableCell21.Name = "tableCell21";
            this.tableCell21.Weight = 1.138697693668304D;
            // 
            // topMarginBand1
            // 
            this.topMarginBand1.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
                        this.label10,
                        this.barCode1,
                        this.label33,
                        this.label32,
                        this.label31,
                        this.label30,
                        this.label29,
                        this.label28,
                        this.label27,
                        this.label26,
                        this.label25,
                        this.label24,
                        this.label22});
            this.topMarginBand1.Dpi = 96F;
            this.topMarginBand1.HeightF = 206F;
            this.topMarginBand1.Name = "topMarginBand1";
            // 
            // tableCell6
            // 
            this.tableCell6.Dpi = 96F;
            this.tableCell6.Multiline = true;
            this.tableCell6.Name = "tableCell6";
            this.tableCell6.Text = "单位\r\nUnit";
            this.tableCell6.Weight = 1D;
            // 
            // tableCell14
            // 
            this.tableCell14.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "SD_DeliveryWaveDetailed.ItemCode")});
            this.tableCell14.Dpi = 96F;
            this.tableCell14.Name = "tableCell14";
            this.tableCell14.Weight = 1.1386985148360374D;
            // 
            // tableCell2
            // 
            this.tableCell2.Dpi = 96F;
            this.tableCell2.Multiline = true;
            this.tableCell2.Name = "tableCell2";
            this.tableCell2.Text = "行号\r\nLine No.";
            this.tableCell2.Weight = 1D;
            // 
            // tableRow2
            // 
            this.tableRow2.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
                        this.tableCell12,
                        this.tableCell13,
                        this.tableCell14,
                        this.tableCell15,
                        this.tableCell16,
                        this.tableCell18,
                        this.tableCell19,
                        this.tableCell20,
                        this.tableCell22,
                        this.tableCell21,
                        this.tableCell17});
            this.tableRow2.Dpi = 96F;
            this.tableRow2.Name = "tableRow2";
            this.tableRow2.Weight = 1D;
            // 
            // tableCell20
            // 
            this.tableCell20.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "SD_DeliveryWaveDetailed.NetWeight")});
            this.tableCell20.Dpi = 96F;
            this.tableCell20.Name = "tableCell20";
            this.tableCell20.Weight = 1.1386985148360374D;
            // 
            // label25
            // 
            this.label25.Dpi = 96F;
            this.label25.Font = new System.Drawing.Font("宋体", 16F);
            this.label25.LocationFloat = new DevExpress.Utils.PointFloat(305.6F, 95.2F);
            this.label25.Name = "label25";
            this.label25.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 96F);
            this.label25.SizeF = new System.Drawing.SizeF(304.8F, 24F);
            this.label25.StylePriority.UseFont = false;
            this.label25.StylePriority.UseTextAlignment = false;
            this.label25.Text = "Sales WaveTask Report";
            this.label25.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // tableCell13
            // 
            this.tableCell13.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "SD_DeliveryWaveDetailed.BaseLine")});
            this.tableCell13.Dpi = 96F;
            this.tableCell13.Name = "tableCell13";
            this.tableCell13.Weight = 1.1386985148360376D;
            // 
            // tableCell12
            // 
            this.tableCell12.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "SD_DeliveryWaveDetailed.BaseNum")});
            this.tableCell12.Dpi = 96F;
            this.tableCell12.Name = "tableCell12";
            this.tableCell12.Weight = 1.1386985148360376D;
            // 
            // DataField
            // 
            this.DataField.BackColor = System.Drawing.Color.Transparent;
            this.DataField.BorderColor = System.Drawing.Color.Black;
            this.DataField.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.DataField.BorderWidth = 1F;
            this.DataField.Font = new System.Drawing.Font("Times New Roman", 10F);
            this.DataField.ForeColor = System.Drawing.Color.Black;
            this.DataField.Name = "DataField";
            this.DataField.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.DataField.TextAlignment = DevExpress.XtraPrinting.TextAlignment.BottomCenter;
            // 
            // tableCell4
            // 
            this.tableCell4.Dpi = 96F;
            this.tableCell4.Multiline = true;
            this.tableCell4.Name = "tableCell4";
            this.tableCell4.Text = "物料名称\r\nDescription";
            this.tableCell4.Weight = 1D;
            // 
            // tableCell9
            // 
            this.tableCell9.Dpi = 96F;
            this.tableCell9.Multiline = true;
            this.tableCell9.Name = "tableCell9";
            this.tableCell9.Text = "毛重\r\nGross Weight";
            this.tableCell9.Weight = 1D;
            // 
            // label24
            // 
            this.label24.Dpi = 96F;
            this.label24.Font = new System.Drawing.Font("宋体", 16F, System.Drawing.FontStyle.Bold);
            this.label24.LocationFloat = new DevExpress.Utils.PointFloat(305.6F, 68.8F);
            this.label24.Name = "label24";
            this.label24.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 96F);
            this.label24.SizeF = new System.Drawing.SizeF(304.8F, 26.4F);
            this.label24.StylePriority.UseFont = false;
            this.label24.StylePriority.UseTextAlignment = false;
            this.label24.Text = "销售波次任务单";
            this.label24.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // tableCell11
            // 
            this.tableCell11.Dpi = 96F;
            this.tableCell11.Multiline = true;
            this.tableCell11.Name = "tableCell11";
            this.tableCell11.Text = "总米数\r\nTotal Rice";
            this.tableCell11.Weight = 1D;
            // 
            // table1
            // 
            this.table1.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
                        | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.table1.Dpi = 96F;
            this.table1.Font = new System.Drawing.Font("宋体", 9.75F);
            this.table1.LocationFloat = new DevExpress.Utils.PointFloat(0.4000015F, 10F);
            this.table1.Name = "table1";
            this.table1.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
                        this.tableRow1});
            this.table1.SizeF = new System.Drawing.SizeF(930.6F, 32F);
            this.table1.StylePriority.UseBorders = false;
            this.table1.StylePriority.UseFont = false;
            this.table1.StylePriority.UseTextAlignment = false;
            this.table1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // label26
            // 
            this.label26.Dpi = 96F;
            this.label26.Font = new System.Drawing.Font("宋体", 9.75F);
            this.label26.LocationFloat = new DevExpress.Utils.PointFloat(711.8F, 114F);
            this.label26.Name = "label26";
            this.label26.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 2, 96F);
            this.label26.SizeF = new System.Drawing.SizeF(84F, 18.40001F);
            this.label26.StylePriority.UseFont = false;
            this.label26.StylePriority.UsePadding = false;
            this.label26.StylePriority.UseTextAlignment = false;
            this.label26.Text = "编号No";
            this.label26.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleJustify;
            // 
            // tableRow1
            // 
            this.tableRow1.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
                        this.tableCell1,
                        this.tableCell2,
                        this.tableCell3,
                        this.tableCell4,
                        this.tableCell5,
                        this.tableCell6,
                        this.tableCell7,
                        this.tableCell8,
                        this.tableCell9,
                        this.tableCell10,
                        this.tableCell11});
            this.tableRow1.Dpi = 96F;
            this.tableRow1.Name = "tableRow1";
            this.tableRow1.Weight = 1D;
            // 
            // label27
            // 
            this.label27.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "SD_DeliveryWave.DocNum")});
            this.label27.Dpi = 96F;
            this.label27.LocationFloat = new DevExpress.Utils.PointFloat(795.7999F, 114F);
            this.label27.Name = "label27";
            this.label27.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 2, 96F);
            this.label27.SizeF = new System.Drawing.SizeF(135.2F, 18.40001F);
            this.label27.StylePriority.UsePadding = false;
            this.label27.StylePriority.UseTextAlignment = false;
            this.label27.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleJustify;
            // 
            // tableCell7
            // 
            this.tableCell7.Dpi = 96F;
            this.tableCell7.Multiline = true;
            this.tableCell7.Name = "tableCell7";
            this.tableCell7.Text = "箱数\r\nBox Qty";
            this.tableCell7.Weight = 1D;
            // 
            // label9
            // 
            this.label9.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
                        | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.label9.Dpi = 96F;
            this.label9.Font = new System.Drawing.Font("宋体", 9.75F);
            this.label9.LocationFloat = new DevExpress.Utils.PointFloat(0F, 10F);
            this.label9.Multiline = true;
            this.label9.Name = "label9";
            this.label9.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 0, 96F);
            this.label9.SizeF = new System.Drawing.SizeF(930.9999F, 101.8F);
            this.label9.StylePriority.UseBorders = false;
            this.label9.StylePriority.UseFont = false;
            this.label9.StylePriority.UsePadding = false;
            this.label9.Text = "备注：Remark：\r\n\r\n";
            // 
            // label30
            // 
            this.label30.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
                        | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.label30.Dpi = 96F;
            this.label30.Font = new System.Drawing.Font("宋体", 9.75F);
            this.label30.LocationFloat = new DevExpress.Utils.PointFloat(459.2F, 132.4F);
            this.label30.Multiline = true;
            this.label30.Name = "label30";
            this.label30.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 2, 96F);
            this.label30.SizeF = new System.Drawing.SizeF(116F, 38.07999F);
            this.label30.StylePriority.UseBorders = false;
            this.label30.StylePriority.UseFont = false;
            this.label30.StylePriority.UsePadding = false;
            this.label30.StylePriority.UseTextAlignment = false;
            this.label30.Text = "责任人\r\nContact Person";
            this.label30.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleJustify;
            // 
            // FieldCaption
            // 
            this.FieldCaption.BackColor = System.Drawing.Color.Transparent;
            this.FieldCaption.BorderColor = System.Drawing.Color.Black;
            this.FieldCaption.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.FieldCaption.BorderWidth = 1F;
            this.FieldCaption.Font = new System.Drawing.Font("Arial", 10F, System.Drawing.FontStyle.Bold);
            this.FieldCaption.ForeColor = System.Drawing.Color.Maroon;
            this.FieldCaption.Name = "FieldCaption";
            // 
            // detailBand1
            // 
            this.detailBand1.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
                        this.table2});
            this.detailBand1.Dpi = 96F;
            this.detailBand1.HeightF = 33.6F;
            this.detailBand1.Name = "detailBand1";
            // 
            // tableCell1
            // 
            this.tableCell1.Dpi = 96F;
            this.tableCell1.Multiline = true;
            this.tableCell1.Name = "tableCell1";
            this.tableCell1.Text = "订单号\r\nPO No.";
            this.tableCell1.Weight = 0.99527532267672036D;
            // 
            // label33
            // 
            this.label33.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Top | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.label33.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "SD_DeliveryWave.PUser")});
            this.label33.Dpi = 96F;
            this.label33.Font = new System.Drawing.Font("宋体", 9.75F);
            this.label33.LocationFloat = new DevExpress.Utils.PointFloat(575.4F, 132.4F);
            this.label33.Multiline = true;
            this.label33.Name = "label33";
            this.label33.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 2, 96F);
            this.label33.SizeF = new System.Drawing.SizeF(136.4F, 38.07999F);
            this.label33.StylePriority.UseBorders = false;
            this.label33.StylePriority.UseFont = false;
            this.label33.StylePriority.UsePadding = false;
            this.label33.StylePriority.UseTextAlignment = false;
            this.label33.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleJustify;
            // 
            // sqlDataSource1
            // 
            this.sqlDataSource1.ConnectionName = "192.168.1.147_FaradyneWMS_Connection";
            msSqlConnectionParameters1.AuthorizationType = DevExpress.DataAccess.ConnectionParameters.MsSqlAuthorizationType.SqlServer;
            msSqlConnectionParameters1.DatabaseName = "FaradyneWMS";
            msSqlConnectionParameters1.ServerName = "192.168.1.147";
            this.sqlDataSource1.ConnectionParameters = msSqlConnectionParameters1;
            this.sqlDataSource1.Name = "sqlDataSource1";
            columnExpression1.ColumnName = "DocNum";
            table3.MetaSerializable = "30|30|125|480";
            table3.Name = "SD_DeliveryWave";
            columnExpression1.Table = table3;
            column1.Expression = columnExpression1;
            columnExpression2.ColumnName = "CustomerName";
            columnExpression2.Table = table3;
            column2.Expression = columnExpression2;
            columnExpression3.ColumnName = "ShipTime";
            columnExpression3.Table = table3;
            column3.Expression = columnExpression3;
            columnExpression4.ColumnName = "PUser";
            columnExpression4.Table = table3;
            column4.Expression = columnExpression4;
            selectQuery1.Columns.Add(column1);
            selectQuery1.Columns.Add(column2);
            selectQuery1.Columns.Add(column3);
            selectQuery1.Columns.Add(column4);
            selectQuery1.FilterString = "[SD_DeliveryWave.DocNum] = ?DocNum";
            selectQuery1.Name = "SD_DeliveryWave";
            queryParameter1.Name = "DocNum";
            queryParameter1.Type = typeof(DevExpress.DataAccess.Expression);
            queryParameter1.Value = new DevExpress.DataAccess.Expression("[Parameters.DocNum]", typeof(string));
            selectQuery1.Parameters.Add(queryParameter1);
            selectQuery1.Tables.Add(table3);
            columnExpression5.ColumnName = "BaseNum";
            table4.MetaSerializable = "30|30|125|580";
            table4.Name = "SD_DeliveryWaveDetailed";
            columnExpression5.Table = table4;
            column5.Expression = columnExpression5;
            columnExpression6.ColumnName = "BaseLine";
            columnExpression6.Table = table4;
            column6.Expression = columnExpression6;
            columnExpression7.ColumnName = "ItemCode";
            columnExpression7.Table = table4;
            column7.Expression = columnExpression7;
            columnExpression8.ColumnName = "ItemName";
            columnExpression8.Table = table4;
            column8.Expression = columnExpression8;
            columnExpression9.ColumnName = "Qty";
            columnExpression9.Table = table4;
            column9.Expression = columnExpression9;
            columnExpression10.ColumnName = "Unit";
            columnExpression10.Table = table4;
            column10.Expression = columnExpression10;
            columnExpression11.ColumnName = "Boxes";
            columnExpression11.Table = table4;
            column11.Expression = columnExpression11;
            columnExpression12.ColumnName = "NetWeight";
            columnExpression12.Table = table4;
            column12.Expression = columnExpression12;
            columnExpression13.ColumnName = "GrossWeight";
            columnExpression13.Table = table4;
            column13.Expression = columnExpression13;
            columnExpression14.ColumnName = "PalletNum";
            columnExpression14.Table = table4;
            column14.Expression = columnExpression14;
            columnExpression15.ColumnName = "MetreSum";
            columnExpression15.Table = table4;
            column15.Expression = columnExpression15;
            selectQuery2.Columns.Add(column5);
            selectQuery2.Columns.Add(column6);
            selectQuery2.Columns.Add(column7);
            selectQuery2.Columns.Add(column8);
            selectQuery2.Columns.Add(column9);
            selectQuery2.Columns.Add(column10);
            selectQuery2.Columns.Add(column11);
            selectQuery2.Columns.Add(column12);
            selectQuery2.Columns.Add(column13);
            selectQuery2.Columns.Add(column14);
            selectQuery2.Columns.Add(column15);
            selectQuery2.FilterString = "[SD_DeliveryWaveDetailed.DocNum] = ?DocNum";
            selectQuery2.Name = "SD_DeliveryWaveDetailed";
            queryParameter2.Name = "DocNum";
            queryParameter2.Type = typeof(DevExpress.DataAccess.Expression);
            queryParameter2.Value = new DevExpress.DataAccess.Expression("[Parameters.DocNum]", typeof(string));
            selectQuery2.Parameters.Add(queryParameter2);
            selectQuery2.Tables.Add(table4);
            this.sqlDataSource1.Queries.AddRange(new DevExpress.DataAccess.Sql.SqlQuery[] {
                        selectQuery1,
                        selectQuery2});
            this.sqlDataSource1.ResultSchemaSerializable = resources.GetString("sqlDataSource1.ResultSchemaSerializable");
            // 
            // tableCell18
            // 
            this.tableCell18.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "SD_DeliveryWaveDetailed.Unit")});
            this.tableCell18.Dpi = 96F;
            this.tableCell18.Name = "tableCell18";
            this.tableCell18.Weight = 1.1386981042521707D;
            // 
            // Title
            // 
            this.Title.BackColor = System.Drawing.Color.Transparent;
            this.Title.BorderColor = System.Drawing.Color.Black;
            this.Title.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.Title.BorderWidth = 1F;
            this.Title.Font = new System.Drawing.Font("Times New Roman", 20F, System.Drawing.FontStyle.Bold);
            this.Title.ForeColor = System.Drawing.Color.Maroon;
            this.Title.Name = "Title";
            // 
            // tableCell22
            // 
            this.tableCell22.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "SD_DeliveryWaveDetailed.GrossWeight")});
            this.tableCell22.Dpi = 96F;
            this.tableCell22.Name = "tableCell22";
            this.tableCell22.Weight = 1.1386985148360378D;
            // 
            // tableCell16
            // 
            this.tableCell16.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "SD_DeliveryWaveDetailed.Qty")});
            this.tableCell16.Dpi = 96F;
            this.tableCell16.Name = "tableCell16";
            this.tableCell16.Text = "tableCell16";
            this.tableCell16.Weight = 1.138698925419904D;
            // 
            // tableCell19
            // 
            this.tableCell19.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "SD_DeliveryWaveDetailed.Boxes")});
            this.tableCell19.Dpi = 96F;
            this.tableCell19.Name = "tableCell19";
            this.tableCell19.Weight = 1.1386985148360376D;
            // 
            // tableCell8
            // 
            this.tableCell8.Dpi = 96F;
            this.tableCell8.Multiline = true;
            this.tableCell8.Name = "tableCell8";
            this.tableCell8.Text = "净重\r\nNet Weight";
            this.tableCell8.Weight = 1D;
            // 
            // bottomMarginBand1
            // 
            this.bottomMarginBand1.Dpi = 96F;
            this.bottomMarginBand1.HeightF = 24F;
            this.bottomMarginBand1.Name = "bottomMarginBand1";
            // 
            // tableCell17
            // 
            this.tableCell17.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "SD_DeliveryWaveDetailed.MetreSum")});
            this.tableCell17.Dpi = 96F;
            this.tableCell17.Name = "tableCell17";
            this.tableCell17.Weight = 1.1387001571715043D;
            // 
            // reportHeaderBand1
            // 
            this.reportHeaderBand1.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
                        this.table1});
            this.reportHeaderBand1.Dpi = 96F;
            this.reportHeaderBand1.HeightF = 42F;
            this.reportHeaderBand1.Name = "reportHeaderBand1";
            // 
            // tableCell10
            // 
            this.tableCell10.Dpi = 96F;
            this.tableCell10.Multiline = true;
            this.tableCell10.Name = "tableCell10";
            this.tableCell10.Text = "每托数量\r\nTorr Qty";
            this.tableCell10.Weight = 1D;
            // 
            // label32
            // 
            this.label32.Borders = ((DevExpress.XtraPrinting.BorderSide)((DevExpress.XtraPrinting.BorderSide.Top | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.label32.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "SD_DeliveryWave.CustomerName")});
            this.label32.Dpi = 96F;
            this.label32.Font = new System.Drawing.Font("宋体", 9.75F);
            this.label32.LocationFloat = new DevExpress.Utils.PointFloat(96F, 132.4F);
            this.label32.Multiline = true;
            this.label32.Name = "label32";
            this.label32.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 2, 96F);
            this.label32.SizeF = new System.Drawing.SizeF(363F, 38.07999F);
            this.label32.StylePriority.UseBorders = false;
            this.label32.StylePriority.UseFont = false;
            this.label32.StylePriority.UsePadding = false;
            this.label32.StylePriority.UseTextAlignment = false;
            this.label32.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleJustify;
            // 
            // tableCell15
            // 
            this.tableCell15.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "SD_DeliveryWaveDetailed.ItemName")});
            this.tableCell15.Dpi = 96F;
            this.tableCell15.Name = "tableCell15";
            this.tableCell15.Weight = 1.1386985148360376D;
            // 
            // label22
            // 
            this.label22.BackColor = System.Drawing.Color.Transparent;
            this.label22.BorderDashStyle = DevExpress.XtraPrinting.BorderDashStyle.Double;
            this.label22.Dpi = 96F;
            this.label22.Font = new System.Drawing.Font("宋体", 18F, ((System.Drawing.FontStyle)((System.Drawing.FontStyle.Bold | System.Drawing.FontStyle.Underline))));
            this.label22.LocationFloat = new DevExpress.Utils.PointFloat(0F, 2.4F);
            this.label22.Multiline = true;
            this.label22.Name = "label22";
            this.label22.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 96F);
            this.label22.SizeF = new System.Drawing.SizeF(307.2F, 22.08001F);
            this.label22.StylePriority.UseBackColor = false;
            this.label22.StylePriority.UseBorderDashStyle = false;
            this.label22.StylePriority.UseFont = false;
            this.label22.StylePriority.UseTextAlignment = false;
            this.label22.Text = "苏州法拉鼎电机有限公司";
            this.label22.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // label29
            // 
            this.label29.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
                        | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.label29.Dpi = 96F;
            this.label29.Font = new System.Drawing.Font("宋体", 9.75F);
            this.label29.LocationFloat = new DevExpress.Utils.PointFloat(712.2F, 132.4F);
            this.label29.Multiline = true;
            this.label29.Name = "label29";
            this.label29.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 2, 96F);
            this.label29.SizeF = new System.Drawing.SizeF(102.2F, 38.08F);
            this.label29.StylePriority.UseBorders = false;
            this.label29.StylePriority.UseFont = false;
            this.label29.StylePriority.UsePadding = false;
            this.label29.StylePriority.UseTextAlignment = false;
            this.label29.Text = "备货日期\r\nDelivery Date";
            this.label29.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleJustify;
            // 
            // DocNum
            // 
            this.DocNum.Description = "波次任务单号";
            this.DocNum.Name = "DocNum";
            this.DocNum.ValueInfo = "2119111901";
            // 
            // label28
            // 
            this.label28.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Top | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.label28.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "SD_DeliveryWave.ShipTime")});
            this.label28.Dpi = 96F;
            this.label28.LocationFloat = new DevExpress.Utils.PointFloat(814.4F, 132.4F);
            this.label28.Name = "label28";
            this.label28.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 2, 96F);
            this.label28.SizeF = new System.Drawing.SizeF(116.8F, 38.07999F);
            this.label28.StylePriority.UseBorders = false;
            this.label28.StylePriority.UsePadding = false;
            this.label28.StylePriority.UseTextAlignment = false;
            this.label28.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleJustify;
            // 
            // tableCell5
            // 
            this.tableCell5.Dpi = 96F;
            this.tableCell5.Multiline = true;
            this.tableCell5.Name = "tableCell5";
            this.tableCell5.Text = "备货数量\r\nStock Qty";
            this.tableCell5.Weight = 1D;
            // 
            // table2
            // 
            this.table2.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.table2.Dpi = 96F;
            this.table2.Font = new System.Drawing.Font("宋体", 9.75F);
            this.table2.LocationFloat = new DevExpress.Utils.PointFloat(0F, 0F);
            this.table2.Name = "table2";
            this.table2.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
                        this.tableRow2});
            this.table2.SizeF = new System.Drawing.SizeF(930.9999F, 33.6F);
            this.table2.StylePriority.UseBorders = false;
            this.table2.StylePriority.UseFont = false;
            this.table2.StylePriority.UseTextAlignment = false;
            this.table2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // label10
            // 
            this.label10.Dpi = 96F;
            this.label10.Font = new System.Drawing.Font("宋体", 18F);
            this.label10.LocationFloat = new DevExpress.Utils.PointFloat(0F, 24.48001F);
            this.label10.Name = "label10";
            this.label10.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 96F);
            this.label10.SizeF = new System.Drawing.SizeF(389.6F, 22.08F);
            this.label10.StylePriority.UseFont = false;
            this.label10.StylePriority.UseTextAlignment = false;
            this.label10.Text = "Faradyne Motors(Suzhou)co.,Ltd";
            this.label10.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleJustify;
            // 
            // PageInfo
            // 
            this.PageInfo.BackColor = System.Drawing.Color.Transparent;
            this.PageInfo.BorderColor = System.Drawing.Color.Black;
            this.PageInfo.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.PageInfo.BorderWidth = 1F;
            this.PageInfo.Font = new System.Drawing.Font("Times New Roman", 10F, System.Drawing.FontStyle.Bold);
            this.PageInfo.ForeColor = System.Drawing.Color.Black;
            this.PageInfo.Name = "PageInfo";
            // 
            // label31
            // 
            this.label31.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
                        | DevExpress.XtraPrinting.BorderSide.Right) 
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.label31.Dpi = 96F;
            this.label31.Font = new System.Drawing.Font("宋体", 9.75F);
            this.label31.LocationFloat = new DevExpress.Utils.PointFloat(0F, 132.4F);
            this.label31.Multiline = true;
            this.label31.Name = "label31";
            this.label31.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 2, 96F);
            this.label31.SizeF = new System.Drawing.SizeF(96F, 38.07999F);
            this.label31.StylePriority.UseBorders = false;
            this.label31.StylePriority.UseFont = false;
            this.label31.StylePriority.UsePadding = false;
            this.label31.StylePriority.UseTextAlignment = false;
            this.label31.Text = "客户\r\nCustomer";
            this.label31.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleJustify;
            // 
            // barCode1
            // 
            this.barCode1.Alignment = DevExpress.XtraPrinting.TextAlignment.TopCenter;
            this.barCode1.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
                        new DevExpress.XtraReports.UI.XRBinding("Text", null, "SD_DeliveryWave.DocNum")});
            this.barCode1.Dpi = 96F;
            this.barCode1.Font = new System.Drawing.Font("宋体", 9.75F);
            this.barCode1.LocationFloat = new DevExpress.Utils.PointFloat(795.8F, 2.4F);
            this.barCode1.Module = 1.92F;
            this.barCode1.Name = "barCode1";
            this.barCode1.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 2, 2, 96F);
            this.barCode1.SizeF = new System.Drawing.SizeF(135.1999F, 92.8F);
            this.barCode1.StylePriority.UseFont = false;
            this.barCode1.StylePriority.UsePadding = false;
            this.barCode1.StylePriority.UseTextAlignment = false;
            qRCodeGenerator1.CompactionMode = DevExpress.XtraPrinting.BarCode.QRCodeCompactionMode.Byte;
            qRCodeGenerator1.Version = DevExpress.XtraPrinting.BarCode.QRCodeVersion.Version4;
            this.barCode1.Symbology = qRCodeGenerator1;
            this.barCode1.Text = "SD20191106008";
            this.barCode1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // XtraReport
            // 
            this.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
                        this.topMarginBand1,
                        this.detailBand1,
                        this.bottomMarginBand1,
                        this.pageFooterBand1,
                        this.reportHeaderBand1});
            this.ComponentStorage.AddRange(new System.ComponentModel.IComponent[] {
                        this.sqlDataSource1});
            this.DataMember = "SD_DeliveryWaveDetailed";
            this.DataSource = this.sqlDataSource1;
            this.Dpi = 96F;
            this.Landscape = true;
            this.Margins = new System.Drawing.Printing.Margins(83, 96, 206, 24);
            this.Name = "XtraReport";
            this.PageHeight = 794;
            this.PageWidth = 1123;
            this.PaperKind = System.Drawing.Printing.PaperKind.A4;
            this.Parameters.AddRange(new DevExpress.XtraReports.Parameters.Parameter[] {
                        this.DocNum});
            this.ReportUnit = DevExpress.XtraReports.UI.ReportUnit.Pixels;
            this.SnapGridSize = 12.5F;
            this.StyleSheet.AddRange(new DevExpress.XtraReports.UI.XRControlStyle[] {
                        this.Title,
                        this.FieldCaption,
                        this.PageInfo,
                        this.DataField});
            this.Version = "16.1";
            ((System.ComponentModel.ISupportInitialize)(this.table1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.table2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();
        }
    }
}
