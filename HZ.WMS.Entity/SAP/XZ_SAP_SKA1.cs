using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.SAP
{
    /// <summary>
    ///SAP中间库-总账科目
    /// </summary>
    [SugarTable("XZ_SAP_SKA1")]
    public class XZ_SAP_SKA1
    {
        /// <summary>
        /// 总账科目
        /// </summary>
        [Description("总账科目")]
        public string SAKNR { get; set; }

        /// <summary>
        /// 总账科目描述
        /// </summary>
        [Description("总账科目描述")]
        public string TXT50 { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        [Description("状态")]
        public bool? Status { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [Description("创建时间")]
        public DateTime? CDate { get; set; }
    }
}
