<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Antlr" version="*******" targetFramework="net45" />
  <package id="LinqKit.Core" version="1.2.7" targetFramework="net46" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="6.0.0" targetFramework="net462" />
  <package id="NPOI" version="2.5.6" targetFramework="net46" />
  <package id="Oracle.ManagedDataAccess" version="21.18.0" targetFramework="net462" />
  <package id="SharpZipLib" version="1.3.3" targetFramework="net462" />
  <package id="System.Buffers" version="4.6.0" targetFramework="net462" />
  <package id="System.Formats.Asn1" version="8.0.1" targetFramework="net462" />
  <package id="System.Memory" version="4.6.0" targetFramework="net462" />
  <package id="System.Numerics.Vectors" version="4.6.0" targetFramework="net462" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.1.0" targetFramework="net462" />
  <package id="System.Text.Encodings.Web" version="6.0.0" targetFramework="net462" />
  <package id="System.Text.Json" version="6.0.10" targetFramework="net462" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net462" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net462" />
  <package id="bootstrap" version="3.3.7" targetFramework="net45" />
  <package id="jQuery" version="3.3.1" targetFramework="net45" />
  <package id="Microsoft.AspNet.Cors" version="5.2.7" targetFramework="net45" />
  <package id="Microsoft.AspNet.Mvc" version="5.2.4" targetFramework="net45" />
  <package id="Microsoft.AspNet.Mvc.zh-Hans" version="5.2.4" targetFramework="net45" />
  <package id="Microsoft.AspNet.Razor" version="3.2.4" targetFramework="net45" />
  <package id="Microsoft.AspNet.Razor.zh-Hans" version="3.2.4" targetFramework="net45" />
  <package id="Microsoft.AspNet.Web.Optimization" version="1.1.3" targetFramework="net45" />
  <package id="Microsoft.AspNet.Web.Optimization.zh-Hans" version="1.1.3" targetFramework="net45" />
  <package id="Microsoft.AspNet.WebApi" version="5.2.4" targetFramework="net45" />
  <package id="Microsoft.AspNet.WebApi.Client" version="5.2.7" targetFramework="net45" />
  <package id="Microsoft.AspNet.WebApi.Client.zh-Hans" version="5.2.7" targetFramework="net45" />
  <package id="Microsoft.AspNet.WebApi.Core" version="5.2.7" targetFramework="net45" />
  <package id="Microsoft.AspNet.WebApi.Core.zh-Hans" version="5.2.7" targetFramework="net45" />
  <package id="Microsoft.AspNet.WebApi.Cors" version="5.2.7" targetFramework="net45" />
  <package id="Microsoft.AspNet.WebApi.HelpPage" version="5.2.4" targetFramework="net45" />
  <package id="Microsoft.AspNet.WebApi.WebHost" version="5.2.4" targetFramework="net45" />
  <package id="Microsoft.AspNet.WebApi.WebHost.zh-Hans" version="5.2.4" targetFramework="net45" />
  <package id="Microsoft.AspNet.WebPages" version="3.2.4" targetFramework="net45" />
  <package id="Microsoft.AspNet.WebPages.zh-Hans" version="3.2.4" targetFramework="net45" />
  <package id="Microsoft.Web.Infrastructure" version="*******" targetFramework="net45" />
  <package id="Modernizr" version="2.8.3" targetFramework="net45" />
  <package id="Newtonsoft.Json" version="11.0.1" targetFramework="net45" />
  <package id="NLog" version="5.2.7" targetFramework="net46" />
  <package id="SqlSugar" version="*********" targetFramework="net46" />
  <package id="Swashbuckle" version="5.6.0" targetFramework="net45" />
  <package id="Swashbuckle.Core" version="5.6.0" targetFramework="net45" />
  <package id="WebActivatorEx" version="2.0" targetFramework="net45" />
  <package id="WebGrease" version="1.6.0" targetFramework="net45" />
</packages>