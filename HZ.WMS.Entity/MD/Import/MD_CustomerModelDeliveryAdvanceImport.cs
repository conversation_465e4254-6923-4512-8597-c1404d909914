using System.ComponentModel;

namespace HZ.WMS.Entity.MD.Import
{
    /// <summary>
    /// 客户机型交期提前管理导入
    /// </summary>
    public class MD_CustomerModelDeliveryAdvanceImport
    {
        /// <summary>
        /// 客户名称
        /// </summary>
        [Description("客户名称")]
        public string CustomerName { get; set; }

        /// <summary>
        /// 客户代码
        /// </summary>
        [Description("客户代码")]
        public string CustomerCode { get; set; }

        /// <summary>
        /// 生产序号
        /// </summary>
        [Description("生产序号")]
        public string ProduceModel { get; set; }

        /// <summary>
        /// 提前天数
        /// </summary>
        [Description("提前天数")]
        public int AdvanceDays { get; set; }
    }
}
