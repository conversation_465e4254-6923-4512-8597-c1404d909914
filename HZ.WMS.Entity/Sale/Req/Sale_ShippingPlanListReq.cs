using System;
using System.Collections.Generic;
using System.ComponentModel;
using AOS.OMS.Entity.Sale;

namespace HZ.WMS.Entity.SD
{
    /// <summary>
    /// 发运计划
    /// </summary>
    public class Sale_ShippingPlanListReq : BaseEntity
    {
        
        /// <summary> 
        /// 通用前缀
        /// </summary> 
        [Description("通用前缀")]
        public string Keyword { get; set; }
        
        /// <summary> 
        /// 订单类型
        /// </summary> 
        [Description("订单类型")]
        public string OrderType { get; set; }
        
        /// <summary> 
        /// 客户订单号
        /// </summary> 
        [Description("客户订单号")]
        public string CustomerOrderNo { get; set; }
        
        /// <summary> 
        /// 合同号
        /// </summary> 
        [Description("合同号")]
        public string ContractNo { get; set; }
        
        /// <summary> 
        /// 物料编号
        /// </summary> 
        [Description("物料编号")]
        public string ItemCode { get; set; }

        /// <summary> 
        /// 物料名称
        /// </summary> 
        [Description("物料名称")]
        public string ItemName { get; set; }
        
        /// <summary> 
        /// 要求发运日期
        /// </summary> 
        [Description("要求发运日期")]
        public DateTime[] DeliveryDate { get; set; }
        
        /// <summary> 
        /// 创建时间
        /// </summary> 
        [Description("创建时间")]
        public DateTime[] CTime { get; set; }

        /// <summary>
        /// 是否交货导入
        /// </summary>
        [Description("是否交货导入")]
        public int? IsDeliveryImport { get; set; }

        /// <summary>
        /// 是否下载 0否 1是
        /// </summary>
        [Description("是否下载")]
        public int? IsDownload { get; set; }

        /// <summary>
        /// 状态 0-未发运 1-已同步 2-发运中 3-已完成
        /// </summary>
        [Description("状态")]
        public int? Status { get; set; }
        
        /// <summary> 
        /// 客户名称
        /// </summary> 
        [Description("客户名称")]
        public string CustomerName { get; set; }
        
        /// <summary>
        /// 树请求集合
        /// </summary>
        [Description("树请求集合")]
        public List<HostOrderTreeReq> TreeReqList { get; set; }

    }
}
