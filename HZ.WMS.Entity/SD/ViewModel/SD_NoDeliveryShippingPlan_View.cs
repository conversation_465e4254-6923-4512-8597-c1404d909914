using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.SD.ViewModel
{
    public  class SD_NoDeliveryShippingPlan_View: SD_ShippingPlanDetail
    {
        /// <summary>
        /// 单号
        /// </summary>
        [Description("单号")]
        public string ShippingPlanDocNum { get; set; }

        /// <summary>
        /// 发货数量
        /// </summary>
        [Description("发货数量")]
        public decimal? DeliveryScanQty { get; set; }

        /// <summary>
        /// 库位编号
        /// </summary>
        public string BinLocationCode { get; set; }

        /// <summary>
        /// 库位名称
        /// </summary>
        public string BinLocationName { get; set; }

        /// <summary>
        /// 所属区域编号
        /// </summary>
        public string RegionCode { get; set; }

        /// <summary>
        /// 所属区域名称
        /// </summary>
        public string RegionName { get; set; }

        /// <summary>
        /// 所属仓库编号
        /// </summary>
        public string StockWhsCode { get; set; }

        /// <summary>
        /// 所属仓库名称
        /// </summary>
        public string StockWhsName { get; set; }

    }
}
