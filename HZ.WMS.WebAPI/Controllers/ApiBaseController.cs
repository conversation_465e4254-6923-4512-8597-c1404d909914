using HZ.WMS.Entity.Sys;
using HZ.WMS.Entity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using HZ.WMS.Application.Sys;
using HZ.Core.Http;
using HZ.WMS.Application;
using HZ.WMS.Entity.MD;
using System.Configuration;
using System.Data;
using HZ.Core.Office;
using System.IO;
using System.Net.Http.Headers;
using DevExpress.XtraPrinting;
using System.Web;
using System.Data.SqlClient;
using HZ.Core.Security;
using HZ.WMS.Entity.SD;

namespace HZ.WMS.WebAPI.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    public class ApiBaseController : ApiController
    {
        private Sys_UserApp _app = new Sys_UserApp();
        private int CFG_ExportMaxRowsLimit = int.Parse(ConfigurationManager.AppSettings["ExportMaxRowsLimit"]);
        private string CFG_ExportExcelType = ConfigurationManager.AppSettings["ExportExcelType"];
        private string CFG_PrintPath = ConfigurationManager.AppSettings["PrintPath"];
        private string CFG_ApiVisualPathName = ConfigurationManager.AppSettings["ApiVisualPathName"];
        BaseApp<BaseEntity> _baseApp = new BaseApp<BaseEntity>();

        #region 获取当前用户


        /// <summary>
        /// 除了doLogin 之外都可以获取到
        /// </summary>
        /// <returns></returns>
        public Sys_User GetCurrentUser()
        {
            IEnumerable<string> userIDs = new List<string>();
            var canGetUserID = Request.Headers.TryGetValues("Uid", out userIDs);
            if (canGetUserID)
            {
                var account = userIDs.ToList().FirstOrDefault().ToString();
                return _app.GetUserByAccount(account);
            }
            else
            {
                throw new Exception("当前用户已登出或者登录已过期，请重新登录！");
            }
        }


        #endregion

        #region 获取AppID
        /// <summary>
        /// 获取当前请求的客户端ID
        /// </summary>
        /// <returns></returns>
        public string GetRequestClientAppID()
        {
            IEnumerable<string> appIDs = new List<string>();
            var hasAppID = Request.Headers.TryGetValues("AppID", out appIDs);

            if (hasAppID)
            {
                return appIDs.ToList().FirstOrDefault().ToString();
            }
            else
            {
                throw new Exception("当前请求中未包含AppID信息，请联系开发人员！");
            }

        }


        #endregion
             
        #region 获取系统生成的单号


        /// <summary>
        /// 获取系统生成的单号
        /// </summary>
        /// <param name="docType"></param>
        /// <param name="fixedNum"></param>
        /// <returns></returns>
        public string GenerateDocNum(string docType, string fixedNum)
        {
            return _baseApp.GetNewDocNum(docType, fixedNum);
        }


        #endregion

        #region 获取系统生成的批次号

        /// <summary>
        /// 获取系统生成的批次号
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetBatchNum()
        {
            var result = new ResponseData();
            try
            {
                result.Data = GenerateDocNum(MD_FixedNumDef.DocType, MD_FixedNumDef.BatchNum);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }


        #endregion

        #region 后端导出到Excel

        /// <summary>
        /// 
        /// </summary>
        /// <param name="dt"></param>
        /// <param name="exportFileName"></param>
        /// <returns></returns>
        public IHttpActionResult ExportToExcelFile(DataTable dt, string exportFileName = "")
        {
            ExcelUtil excelUtil = new ExcelUtil();
            MemoryStream stream = excelUtil.CreateExcel(dt);
            return ResponseMessage(ExportToExcel(stream, exportFileName));
        }

        public IHttpActionResult ExportToExcelFileByGroup<S,T>(Dictionary<S, List<T>> list, List<ExcelColumn<T>> columns, string exportFileName = "")
        {
            var result = new ResponseData();
            if (list.Count > CFG_ExportMaxRowsLimit)
            {
                result.Code = (int)WMSStatusCode.Failed;
                result.Message = "ui.Sys.Sys_User.UserName";
            }
            else
            {
                ExcelFileType fileType = ExcelFileType.xlsx;
                return ResponseMessage(ExportToExcel(ExcelService.ExportToExcelByGroup<S,T>(list, fileType, columns), exportFileName));
            }
            return Json(result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="list"></param>
        /// <param name="columns"></param>
        /// <param name="exportFileName"></param>
        /// <param name="sheetName"></param>
        /// <returns></returns>
        public IHttpActionResult ExportToExcelFile<T>(List<T> list, List<ExcelColumn<T>> columns, string exportFileName = "", string sheetName = "Sheet1")
        {
            var result = new ResponseData();
            if (list.Count > CFG_ExportMaxRowsLimit)
            {
                result.Code = (int)WMSStatusCode.Failed;
                result.Message = "ui.Sys.Sys_User.UserName";
            }
            else
            {
                ExcelFileType fileType = ExcelFileType.xlsx;
                //switch (CFG_ExportExcelType)
                //{
                //    case "xls":
                //        fileType = ExcelFileType.xls;
                //        break;
                //    case "xlsx":
                //        fileType = ExcelFileType.xlsx;
                //        break;
                //}

                return ResponseMessage(ExportToExcel(ExcelService.ExportToExcel<T>(list, fileType, sheetName, columns), exportFileName));
            }

            return Json(result);
        }

        private HttpResponseMessage ExportToExcel(MemoryStream stream, string exportFile = "")
        {
            if (string.IsNullOrEmpty(exportFile))
            {
                exportFile = GetCurrentUser().UserName + "_" + DateTime.Now.ToString("yyyyMMddHHmmssfff"); //到毫秒
            }
            if (stream == null)
            {
                return new HttpResponseMessage(HttpStatusCode.NoContent);
            }

            HttpResponseMessage result = new HttpResponseMessage(HttpStatusCode.OK);

            result.Content = new StreamContent(stream, (int)stream.Length);
            result.Content.Headers.ContentType = new MediaTypeHeaderValue("application/octet-stream");
            //result.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.ms-excel");      // Excel97 - 2003(xls)
            //result.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");      // Excel 2007(xlsx)

            //result.Content.Headers.ContentDisposition = new ContentDispositionHeaderValue("attachment");    //content-disposition
            result.Content.Headers.ContentDisposition = new ContentDispositionHeaderValue("attachment");
            result.Content.Headers.ContentDisposition.FileName = System.Web.HttpUtility.UrlEncode(exportFile);    // HttpUtility.UrlEncode(exportFile);        // 中文名注意乱码问题处理
            result.Content.Headers.ContentLength = stream.Length;
            return result;
        }


        #endregion

        #region 发运计划导出

        /// <summary>
        /// 发运计划导出
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="list"></param>
        /// <param name="columns"></param>
        /// <param name="exportFileName"></param>
        /// <param name="sheetName"></param>
        /// <returns></returns>
        public IHttpActionResult ExportToExcelFileForShippingPlan(List<SD_ShippingPlanDetail> list, List<ExcelColumn<SD_ShippingPlanDetail>> columns, string exportFileName, string sheetName)
        {
            var result = new ResponseData();
            if (list.Count > CFG_ExportMaxRowsLimit)
            {
                result.Code = (int)WMSStatusCode.Failed;
                result.Message = "ui.Sys.Sys_User.UserName";
            }
            else
            {
                ExcelFileType fileType = ExcelFileType.xls;
                switch (CFG_ExportExcelType)
                {
                    case "xls":
                        fileType = ExcelFileType.xls;
                        break;
                    case "xlsx":
                        fileType = ExcelFileType.xlsx;
                        break;
                }
                return ResponseMessage(ExportToExcel(ExcelService.ExportToExcelForShippingPlan(list, fileType, sheetName, columns), exportFileName));
            }

            return Json(result);

            //if (sheetName == "物流供应商")
            //{
            //    var group = itemsData.GroupBy(g => g.SupplierCode);

            //    var dic = group.ToDictionary(w => $"{w.Key.ToString()}", w => w.ToList());
            //    return ResponseMessage(ExportToExcel(ExcelService.ExportToExcelByGroup<string, SD_ShippingPlanDetail>(dic, ExcelFileType.xlsx, columns), exportFileName));
            //}
            //else 
            //{
            //    var group = itemsData.GroupBy(g => g.SettlementAdd);
            //    var dic = group.ToDictionary(w => $"{w.Key.ToString()}", w => w.ToList());
            //    return ResponseMessage(ExportToExcel(ExcelService.ExportToExcelByGroup<string, SD_ShippingPlanDetail>(dic, ExcelFileType.xlsx, columns), exportFileName));

            //}

        }

        #endregion

        #region 后端导出PDF

        /// <summary>
        /// 后端导出PDF
        /// </summary>
        /// <param name="rpt"></param>
        /// <param name="pdfFilePath"></param>
        /// <returns></returns>
        public IHttpActionResult PrintToPDF(DevExpress.XtraReports.UI.XtraReport rpt,string pdfFilePath="")
        {
            var result = new ResponseData();
            string printUrl = "/";
            try
            {
                //动态重设数据源
                SqlConnectionStringBuilder scb = new SqlConnectionStringBuilder(DES.Decrypt(ConfigurationManager.ConnectionStrings["DbConnection"].ToString()));
                String strDB = scb.InitialCatalog;
                String strUser = scb.UserID;
                String strPwd = scb.Password;

                DevExpress.DataAccess.Sql.SqlDataSource dataSource = rpt.DataSource as DevExpress.DataAccess.Sql.SqlDataSource;

                if (dataSource != null)
                {
                    DevExpress.DataAccess.ConnectionParameters.MsSqlConnectionParameters connectionParam = dataSource.ConnectionParameters as DevExpress.DataAccess.ConnectionParameters.MsSqlConnectionParameters;
                    connectionParam.AuthorizationType = DevExpress.DataAccess.ConnectionParameters.MsSqlAuthorizationType.SqlServer;
                    connectionParam.DatabaseName = scb.InitialCatalog;
                    connectionParam.ServerName = scb.DataSource;
                    connectionParam.Password = scb.Password;
                    connectionParam.UserName = scb.UserID;

                    dataSource.ConnectionParameters = connectionParam;
                    //超时
                    dataSource.ConnectionOptions.CommandTimeout = 5000;
                    //dataSource.ConnectionOptions.DbCommandTimeout = 5000;
                    //重新执行数据源查询
                    dataSource.Fill();
                }

                
                //
                string printTemplateFile = DateTime.Now.ToString("yyyyMMddHHmmssfff") + ".pdf";
                if (string.IsNullOrEmpty(pdfFilePath))
                {
                    pdfFilePath = HttpContext.Current.Server.MapPath("~/") + CFG_PrintPath + printTemplateFile;
                }

                PdfExportOptions pdfOptions = rpt.ExportOptions.Pdf;
                // 设置导出选项
                pdfOptions.Compressed = true;
                pdfOptions.ImageQuality = PdfJpegImageQuality.High;
                rpt.ExportToPdf(pdfFilePath);

                //if (!string.IsNullOrEmpty(CFG_ApiVisualPathName))
                //{
                //    printUrl = Request.RequestUri.ToString().Replace(Request.RequestUri.PathAndQuery, "") + "/" + CFG_ApiVisualPathName + "/";
                //}
                //else
                //{
                //    printUrl = Request.RequestUri.ToString().Replace(Request.RequestUri.PathAndQuery, "") + "/";
                //}

                //if (!string.IsNullOrEmpty(CFG_ApiVisualPathName))
                //{
                //    printUrl = "/" + CFG_ApiVisualPathName + "/";
                //}


                HZ.Core.Log.LogUtil.WriteLog(Request.RequestUri.AbsoluteUri);
                HZ.Core.Log.LogUtil.WriteLog(Request.RequestUri.LocalPath);

                string webFilePath = printUrl + CFG_PrintPath + printTemplateFile;

                result.Data = new { PrintedPDF= webFilePath.Replace("\\","/") };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
            
        }

        #endregion

        #region 后端导出PDF

        /// <summary>
        /// 后端导出PDF
        /// </summary>
        /// <param name="rpt"></param>
        /// <param name="pdfFilePath"></param>
        /// <returns></returns>
        public IHttpActionResult PrintToShowPreview(DevExpress.XtraReports.UI.XtraReport rpt, string pdfFilePath = "")
        {
            var result = new ResponseData();
            string printUrl = "";
            try
            {
                //动态重设数据源
                SqlConnectionStringBuilder scb = new SqlConnectionStringBuilder(DES.Decrypt(ConfigurationManager.ConnectionStrings["DbConnection"].ToString()));
                String strDB = scb.InitialCatalog;
                String strUser = scb.UserID;
                String strPwd = scb.Password;

                DevExpress.DataAccess.Sql.SqlDataSource dataSource = rpt.DataSource as DevExpress.DataAccess.Sql.SqlDataSource;

                if (dataSource != null)
                {
                    DevExpress.DataAccess.ConnectionParameters.MsSqlConnectionParameters connectionParam = dataSource.ConnectionParameters as DevExpress.DataAccess.ConnectionParameters.MsSqlConnectionParameters;
                    connectionParam.AuthorizationType = DevExpress.DataAccess.ConnectionParameters.MsSqlAuthorizationType.SqlServer;
                    connectionParam.DatabaseName = scb.InitialCatalog;
                    connectionParam.ServerName = scb.DataSource;
                    connectionParam.Password = scb.Password;
                    connectionParam.UserName = scb.UserID;

                    dataSource.ConnectionParameters = connectionParam;
                    //重新执行数据源查询
                    dataSource.Fill();
                }


                //
                string printTemplateFile = DateTime.Now.ToString("yyyyMMddHHmmssfff") + ".pdf";
                pdfFilePath = HttpContext.Current.Server.MapPath("~/") + ConfigurationManager.AppSettings["LabelTemplateUploadPath"]?.ToString() + "物料标签.repx";
                rpt.LoadLayout(pdfFilePath);
                rpt.DataSource = dataSource;

                if (!string.IsNullOrEmpty(CFG_ApiVisualPathName))
                {
                    printUrl = "/" + CFG_ApiVisualPathName + "/";
                }


                HZ.Core.Log.LogUtil.WriteLog(Request.RequestUri.AbsoluteUri);
                HZ.Core.Log.LogUtil.WriteLog(Request.RequestUri.LocalPath);

                string webFilePath = printUrl + CFG_PrintPath + printTemplateFile;

                result.Data = new { PrintedPDF = webFilePath.Replace("\\", "/") };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }

        #endregion

        #region 后端导出PDF

        /// <summary>
        /// 后端导出PDF
        /// </summary>
        /// <param name="rpt"></param>
        /// <param name="pdfFilePath"></param>
        /// <returns></returns>
        public string GetPrintPDFPath(DevExpress.XtraReports.UI.XtraReport rpt, string pdfFilePath = "")
        {
            string printUrl = "";
            //动态重设数据源
            SqlConnectionStringBuilder scb = new SqlConnectionStringBuilder(DES.Decrypt(ConfigurationManager.ConnectionStrings["DbConnection"].ToString()));
            String strDB = scb.InitialCatalog;
            String strUser = scb.UserID;
            String strPwd = scb.Password;

            DevExpress.DataAccess.Sql.SqlDataSource dataSource = rpt.DataSource as DevExpress.DataAccess.Sql.SqlDataSource;

            if (dataSource != null)
            {
                DevExpress.DataAccess.ConnectionParameters.MsSqlConnectionParameters connectionParam = dataSource.ConnectionParameters as DevExpress.DataAccess.ConnectionParameters.MsSqlConnectionParameters;
                connectionParam.AuthorizationType = DevExpress.DataAccess.ConnectionParameters.MsSqlAuthorizationType.SqlServer;
                connectionParam.DatabaseName = scb.InitialCatalog;
                connectionParam.ServerName = scb.DataSource;
                connectionParam.Password = scb.Password;
                connectionParam.UserName = scb.UserID;
                dataSource.ConnectionParameters = connectionParam;
                //重新执行数据源查询
                dataSource.Fill();
            }
            //
            string printTemplateFile = DateTime.Now.ToString("yyyyMMddHHmmssfff") + ".pdf";
            if (string.IsNullOrEmpty(pdfFilePath))
            {
                pdfFilePath = HttpContext.Current.Server.MapPath("~/") + CFG_PrintPath + printTemplateFile;
            }
            PdfExportOptions pdfOptions = rpt.ExportOptions.Pdf;
            // 设置导出选项
            pdfOptions.Compressed = true;
            pdfOptions.ImageQuality = PdfJpegImageQuality.High;
            rpt.ExportToPdf(pdfFilePath);
            //if (!string.IsNullOrEmpty(CFG_ApiVisualPathName))
            //{
            //    printUrl = "/" + CFG_ApiVisualPathName + "/";
            //}

            HZ.Core.Log.LogUtil.WriteLog(Request.RequestUri.AbsoluteUri);
            HZ.Core.Log.LogUtil.WriteLog(Request.RequestUri.LocalPath);

            string webFilePath = printUrl + CFG_PrintPath + printTemplateFile;
            return webFilePath.Replace("\\", "/");
        }

        #endregion

    }
}
