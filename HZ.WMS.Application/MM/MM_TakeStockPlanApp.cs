using SqlSugar;
using HZ.Core.Http;
using HZ.WMS.Application.Sys;
using HZ.WMS.Entity.MM;
using HZ.WMS.Entity.MM.ViewModel;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;

namespace HZ.WMS.Application.MM
{
    /// <summary>
    /// 没有out_message消息的函数，意味着只在最前端做未处理异常捕捉
    /// </summary>

    public class MM_TakeStockPlanApp : BaseApp<MM_TakeStockPlan>
    {

        private MM_TakeStockScanApp MM_TakeStockScanApp = new MM_TakeStockScanApp();
        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public MM_TakeStockPlanApp() : base()
        {
        }




        #endregion

        #region 获取列表

        /// <summary>
        /// 获取列表
        /// </summary>
        /// <returns></returns>
        public List<MM_TakeStockPlan> GetList()
        {
            return base.GetList().ToList();
        }

        #endregion

        #region 获取分页列表

        /// <summary>
        /// 分页查询
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyWard"></param>
        /// <returns></returns>
        public List<MM_TakeStockPlan> GetPageList(Pagination page)
        {
            // 无关键字查询所有数据
            var query = base.GetPageList(page);
            return query.ToList();
        }

        #endregion

        #region 添加

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public MM_TakeStockPlan Insert(MM_TakeStockPlan entity)
        {
            return base.Insert(entity);
        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public int Delete(MM_TakeStockPlan entity)
        {
            return base.Delete(entity);
        }

        #endregion

        #region 批量删除

        /// <summary>
        /// 批量删除
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public int DeleteByIDS(object[] ids, string deleteUser)
        {
            return base.DeleteByKeys(ids, deleteUser);
        }

        #endregion

        #region 更新

        /// <summary>
        /// 更新，只能修改备注，是否已读，已读时间，修改时间，修改人Id这几个字段
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public int Update(MM_TakeStockPlan entity)
        {
            return base.Update(entity);
        }

        #endregion



        /// <summary>
        /// 主从表级联删除，事务
        /// </summary>
        /// <param name="parentIDs"></param>
        /// <param name="userAccount"></param>
        /// <returns></returns>
        public int DeleteParentAndChildren(string[] parentIDs, string userAccount)
        {
            int result = 0;
            try
            {
                if (parentIDs != null && parentIDs.Length > 0)
                {
                    DbContext.Ado.BeginTran();

                    var query = base.GetList(p => parentIDs.Contains(p.PlanID));
                    var parentDocNumList = query.Select(p => p.DocNum).ToList();
                    var detailApp = new MM_TakeStockPlanDetailedApp();
                    detailApp.DbContext = this.DbContext;
                    var childrenIDList = detailApp.GetList(c => parentDocNumList.Contains(c.DocNum)).Select(c => c.PlanID).ToList();

                    detailApp.DeleteByKeys(childrenIDList.ToArray(), userAccount);
                    result = base.DeleteByKeys(parentIDs, userAccount);

                    DbContext.Ado.CommitTran();
                }
            }
            catch (System.Exception ex)
            {
                if (this.DbContext.Ado.IsAnyTran())
                    this.DbContext.RollbackTran();
                throw ex;
            }
            return result;
        }

        /// <summary>
        /// 完成计划
        /// </summary>
        /// <param name="planID"></param>
        /// <param name="userAccount"></param>
        /// <returns></returns>
        public bool FinishPlan(string planID, string userAccount)
        {
            bool result = false;
            try
            {

                DateTime time = DateTime.Now;
                DbContext.Ado.BeginTran();
                MM_TakeStockScanApp.DbContext = this.DbContext;
                var plan = GetEntityByKey(planID);

                if (plan.Status != 2)
                {
                    throw new Exception("Common.error", new Exception("ui.MM.TakeStockPlan.planStatusError"));
                }

                //生成盘点差异
                var detailList = MM_TakeStockScanApp.GetList(x => x.DocNum == plan.DocNum && !x.IsDelete).ToList();
                if (detailList?.Count > 0)
                {
                    detailList.ForEach(x =>
                    {
                        x.DiffQty = x.ScanQty - x.StockQty; //正数盘盈,负数盘亏
                        x.MTime = time;
                        x.MUser = userAccount;
                        if (x.PTime == null)
                            x.PTime = time;
                    });
                    MM_TakeStockScanApp.Update(detailList);
                }


                //修改计划状态
                plan.MTime = time;
                plan.MUser = userAccount;
                plan.Status = 3;
                result = this.Update(plan) > 0;

                ////仓库解锁 2019-12-31注释 
                //this.ReleaseStockLock(userAccount);
                DbContext.Ado.CommitTran();
            }
            catch (System.Exception ex)
            {
                if (this.DbContext.Ado.IsAnyTran())
                    this.DbContext.RollbackTran();
                throw ex;
            }
            return result;
        }

        /// <summary>
        /// 复核计划
        /// </summary>
        /// <param name="data"></param>
        /// <param name="userAccount"></param>
        /// <returns></returns>
        public bool ReviewPlan(List<MM_TakeStockScan> data, string userAccount)
        {
            bool result = false;
            try
            {
                DbContext.Ado.BeginTran();
                MM_TakeStockScanApp.DbContext = this.DbContext;

                var docNums = data.Select(x => x.DocNum).ToList();
                //检查1
                var ckIsConfirm = MM_TakeStockScanApp.GetList(x => docNums.Contains(x.DocNum) && x.IsConfirm)?.Distinct()?.ToList();
                if (ckIsConfirm != null && ckIsConfirm.Count > 0)
                {
                    throw new Exception("Common.error", new Exception("ui.MM.TakeStockPlan.CheckIsConfirm"));
                }
                //检查2
                var planList = this.GetList(x => docNums.Contains(x.DocNum))?.Distinct()?.ToList();
                if (planList == null)
                {
                    throw new Exception("Common.error", new Exception("ui.MM.TakeStockPlan.planNotFound"));
                }
                else
                {
                    planList.ForEach(x =>
                    {
                        x.Status = 2;
                        x.MTime = DateTime.Now;
                        x.MUser = userAccount;
                    });
                    result = this.Update(planList) > 0;
                }
                DbContext.Ado.CommitTran();
            }
            catch (System.Exception ex)
            {
                if (this.DbContext.Ado.IsAnyTran())
                    this.DbContext.RollbackTran();
                throw ex;
            }
            return result;
        }
        /// <summary>
        /// 根据单号获取实体
        /// </summary>
        /// <returns></returns>
        public MM_TakeStockPlan GetEntityByDocNum(string docNum)
        {
            var model = GetFirstEntityByFieldValue("DocNum", docNum);
            if (model == null)
            {
                throw new Exception("Common.error", new Exception("Common.barcodeIsUnusable"));
            }
            switch (model.Status)
            {
                case 1:
                    throw new Exception("Common.error", new Exception("MM.MM_TakeStockScan.planIsNotStart"));

                case 3:
                    throw new Exception("Common.error", new Exception("MM.MM_TakeStockScan.planIsComplete"));

            }
            return model;
        }

        /// <summary>
        /// 确认计划
        /// </summary>
        /// <param name="data"></param>
        /// <param name="userAccount"></param>
        /// <returns></returns>
        public bool ConfirmPlan(List<MM_TakeStockScan> data, string userAccount)
        {
            bool result = false;
            try
            {
                //DbContext.Ado.BeginTran();
                //MM_TakeStockScanApp.DbContext = this.DbContext;
                //校验1
                if (data == null)
                {
                    throw new Exception("Common.error", new Exception("ui.MM.TakeStockPlan.planScanRecordsNotFound"));
                }
                else//不为空，校验本表状态
                {
                    data.ForEach(datas =>
                    {
                        if (datas.IsConfirm == true)//状态为是，不可以再次确认差异
                        {
                            throw new Exception("Common.error", new Exception("ui.MM.TakeStockPlan.TakeStockError"));
                        }
                    });
                }
                ////校验2
                //if (!this.LockStock(userAccount))
                //{
                //    throw new Exception("Common.error", new Exception("ui.MM.TakeStockPlan.lockStockFailed"));
                //}


                //准备确认扫描表状态
                data.ForEach(x =>
                {
                    x.IsConfirm = true;
                    x.MTime = DateTime.Now;
                    x.MUser = userAccount;
                });

                ////生成差异数据
                //MM_TakeStockScanApp.MakeInOutStockData(data, userAccount);


                ////根据差异查出所属计划，判断计划数据状态
                //var docNums = data.Select(x => x.DocNum).ToList();
                //var planList = this.GetList(x => docNums.Contains(x.DocNum) && !x.IsDelete)?.Distinct()?.ToList();
                //if (planList == null)
                //{
                //    throw new Exception("Common.error", new Exception("ui.MM.TakeStockPlan.planNotFound"));
                //}
                //else
                //{
                //    planList.ForEach(plan =>
                //    {
                //        if (plan.Status == 3)//状态为完成，可以确认差异
                //        {

                //        }
                //        else
                //        {
                //            throw new Exception("Common.error", new Exception("ui.MM.TakeStockPlan.planStatusError"));

                //        }
                //    });
                //}


                //if (MM_TakeStockScanApp.Update(data) > 0)
                //{
                //    planList.ForEach(plan =>
                //    {
                //        if (plan.Status != 3)
                //        {
                //            throw new Exception("Common.error", new Exception("ui.MM.TakeStockPlan.planStatusError"));
                //        }

                //        //修改计划状态
                //        plan.MTime = DateTime.Now;
                //        plan.MUser = userAccount;
                //        plan.Status = 2;    //开始
                //        result = this.Update(plan) > 0;
                //    });
                //}


                //DbContext.Ado.CommitTran();
            }
            catch (System.Exception ex)
            {
                if (this.DbContext.Ado.IsAnyTran())
                    this.DbContext.RollbackTran();
                throw ex;
            }
            return result;
        }

        /// <summary>
        /// 仓库解锁 //库存盘点锁库跟自动过账无关
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        public bool ReleaseStockLock(string user)
        {
            bool result = false;
            //查找是否有已开始的盘点计划，如果有，则不允许解锁
            int count = this.GetList(x => x.Status == 2).Count();
            if (count == 0)
            {
                Sys_SwithConfigApp Sys_SwithConfigApp = new Sys_SwithConfigApp();
                result = Sys_SwithConfigApp.Update(x => x.ConfigCode == "MM_AP_000", x => new Entity.Sys.Sys_SwithConfig()
                {
                    SwitchValue = false,
                    MTime = DateTime.Now,
                    MUser = user
                }) > 0;
            }

            return result;
        }

        /// <summary>
        /// 仓库锁定 //库存盘点锁库跟自动过账无关
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        public bool LockStock(string user)
        {
            bool result = false;
            Sys_SwithConfigApp Sys_SwithConfigApp = new Sys_SwithConfigApp();
            result = Sys_SwithConfigApp.Update(x => x.ConfigCode == "MM_AP_000", x => new Entity.Sys.Sys_SwithConfig()
            {
                SwitchValue = true,
                MTime = DateTime.Now,
                MUser = user
            }) > 0;

            return result;
        }

        /// <summary>
        /// 获取已开始的盘点计划
        /// </summary>
        /// <param name="itemCode">物料编码</param>
        /// <param name="binLocationCode">库位编码</param>
        /// <param name="regionCode">区域编码</param>
        /// <returns></returns>
        public List<MM_TakeStockPlan> GetStartedTakeStockPlanList(string itemCode = null, string binLocationCode = null, string regionCode = null, SqlSugarClient tranDbContext = null)
        {
            List<MM_TakeStockPlan> result = null;
            try
            {
                if (tranDbContext != null)
                    MM_TakeStockScanApp.DbContext = tranDbContext;
                //条件必须三选一
                int count = 0;
                if (!string.IsNullOrEmpty(itemCode))
                { count++; }
                if (!string.IsNullOrEmpty(binLocationCode))
                { count++; }
                if (!string.IsNullOrEmpty(regionCode))
                { count++; }
                if (count > 0)
                {
                    //MM_TakeStockPlanDetailedApp MM_TakeStockPlanDetailedApp = new MM_TakeStockPlanDetailedApp();

                    //检查是否存在符合查询条件的开始中的盘点计划 关联盘点主表和盘点账面量表
                    var list = MM_TakeStockScanApp
                     .GetAllList(x =>
                         (string.IsNullOrEmpty(itemCode) || x.ItemCode == itemCode) &&
                         (string.IsNullOrEmpty(binLocationCode) || x.BinLocationCode == binLocationCode) &&
                         (string.IsNullOrEmpty(regionCode) || x.RegionCode == regionCode) &&
                         !x.IsDelete)?
                     .InnerJoin<MM_TakeStockPlan>((x, y) => x.DocNum == y.DocNum && y.Status == 2)?
                     .Select((x, y) => y)?
                     .ToList();
                    result = list;
                }
            }
            catch (System.Exception ex)
            {

            }

            return result;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public List<MM_TakeStockPlan_View> GetListView(string keyword, DateTime startDate, DateTime endDate, int? state)
        {
            var result = this.DbContext.Queryable<MM_TakeStockPlan_View>()
                .Where(x =>
                    (string.IsNullOrEmpty(keyword)
                     || x.DocNum.Contains(keyword)
                     || x.PUser.Contains(keyword)))
                .Where(x => x.CTime >= startDate && x.CTime <= endDate)
             .Where(x => state == 0 || x.Status == state);
            return result.ToList();
        }
    }
}

