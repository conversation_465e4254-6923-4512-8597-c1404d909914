using System.ComponentModel;

namespace AOS.OMS.Entity.Sale
{
    public class SaleBomReq
    {
        /// <summary>
        /// 工厂
        /// </summary>  
        [Description("工厂")]
        public string WERKS { get; set; }

        /// <summary>
        /// 销售单号
        /// </summary>  
        [Description("销售单号")]
        public string VBELN { get; set; }

        /// <summary>
        /// 销售行号
        /// </summary>  
        [Description("销售行号")]
        public string POSNR { get; set; }
        
    }
}