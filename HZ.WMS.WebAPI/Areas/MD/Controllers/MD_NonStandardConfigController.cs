using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;
using HZ.Core.Http;
using HZ.Core.Office;
using HZ.WMS.Application.MD;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.MD.Import;
using HZ.WMS.WebAPI.Controllers;

namespace HZ.WMS.WebAPI.Areas.MD.Controllers
{
    /// <summary>
    /// 非标参数配置
    /// </summary>
    public class MD_NonStandardConfigController : ApiBaseController
    {
        private MD_NonStandardConfigApp _app = new MD_NonStandardConfigApp();

        #region 查询分页列表

        /// <summary>
        /// 查询分页列表 
        /// </summary>
        /// <param name="page">分页</param>
        /// <param name="req">查询参数</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]MD_NonStandardConfigListReq req)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetPageList(page, x =>
                    (string.IsNullOrEmpty(req.AcceptNo) || x.AcceptNo.Contains(req.AcceptNo))
                    && (string.IsNullOrEmpty(req.SeriesModel) || x.SeriesModel.Contains(req.SeriesModel))
                    && (string.IsNullOrEmpty(req.SAPPartNo) || x.SAPPartNo.Contains(req.SAPPartNo))
                    && (string.IsNullOrEmpty(req.SAPProductModel) || x.SAPProductModel.Contains(req.SAPProductModel))
                    && (string.IsNullOrEmpty(req.TractionRatio) || x.TractionRatio.Contains(req.TractionRatio)));
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取单个实体

        /// <summary>
        /// 获取单个实体
        /// </summary>
        /// <param name="id">主键</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetEntity([FromUri]string id)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.GetEntityByKey(id);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 根据接受编号获取

        /// <summary>
        /// 根据接受编号获取配置信息
        /// </summary>
        /// <param name="AcceptNo">接受编号</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetByAcceptNo([FromUri]string AcceptNo)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.GetByAcceptNo(AcceptNo);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 添加

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="entity">实体</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Insert([FromBody]MD_NonStandardConfig entity)
        {
            var result = new ResponseData();
            try
            {
                // 验证必填字段
                if (string.IsNullOrEmpty(entity.AcceptNo))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "接受编号不能为空";
                    return Json(result);
                }

                if (string.IsNullOrEmpty(entity.SeriesModel))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "系列型号不能为空";
                    return Json(result);
                }

                // 检查接受编号是否已存在
                if (_app.IsAcceptNoExists(entity.AcceptNo))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = $"接受编号 {entity.AcceptNo} 已存在";
                    return Json(result);
                }

                entity.CUser = GetCurrentUser().LoginAccount;
                entity.CTime = DateTime.Now;
                
                result.Data = _app.Insert(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 修改

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="entity">实体</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Update([FromBody]MD_NonStandardConfig entity)
        {
            var result = new ResponseData();
            try
            {
                // 验证必填字段
                if (string.IsNullOrEmpty(entity.AcceptNo))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "接受编号不能为空";
                    return Json(result);
                }

                if (string.IsNullOrEmpty(entity.SeriesModel))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "系列型号不能为空";
                    return Json(result);
                }

                // 检查接受编号是否已存在（排除当前记录）
                if (_app.IsAcceptNoExists(entity.AcceptNo, entity.Id))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = $"接受编号 {entity.AcceptNo} 已存在";
                    return Json(result);
                }

                entity.MUser = GetCurrentUser().LoginAccount;
                entity.MTime = DateTime.Now;
                
                result.Data = _app.Update(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="ids">主键数组</param>
        /// <returns></returns>
        [HttpDelete]
        public IHttpActionResult Delete([FromBody]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.DeleteByKeys(ids, GetCurrentUser().LoginAccount);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 导出

        /// <summary>
        /// 导出模板
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelModel()
        {
            var result = new ResponseData();
            try
            {
                var columns = ExcelService.FetchDefaultColumnList<MD_NonStandardConfig>();
                string modelName = "非标参数配置-导入模板";
                //排除不需要的列名称 字符串
                string[] ignoreField = new string[]
                {
                    "Id", "IsDelete", "CUser", "CTime",
                    "MUser", "MTime", "DUser", "DTime"
                };
                var itemsData = new List<MD_NonStandardConfig>() { };
                var ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<MD_NonStandardConfig> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }

                return ExportToExcelFile<MD_NonStandardConfig>(itemsData, columns,
                    GetCurrentUser().UserName + "_" + modelName);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        /// <summary>
        /// 导出数据
        /// </summary>
        /// <param name="req">查询参数</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] MD_NonStandardConfigListReq req)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetList(x =>
                    (string.IsNullOrEmpty(req.AcceptNo) || x.AcceptNo.Contains(req.AcceptNo))
                    && (string.IsNullOrEmpty(req.SeriesModel) || x.SeriesModel.Contains(req.SeriesModel))
                    && (string.IsNullOrEmpty(req.SAPPartNo) || x.SAPPartNo.Contains(req.SAPPartNo))
                    && (string.IsNullOrEmpty(req.SAPProductModel) || x.SAPProductModel.Contains(req.SAPProductModel))
                    && (string.IsNullOrEmpty(req.TractionRatio) || x.TractionRatio.Contains(req.TractionRatio))).ToList();
                List<ExcelColumn<MD_NonStandardConfig>> columns = ExcelService.FetchDefaultColumnList<MD_NonStandardConfig>();
                string[] ignoreField = new string[] { "IsDelete", "DTime", "DUser", "MUser", "MTime" };
                List<ExcelColumn<MD_NonStandardConfig>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<MD_NonStandardConfig> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }

                return ExportToExcelFile<MD_NonStandardConfig>(itemsData, columns,
                    GetCurrentUser().UserName + "_非标参数配置");
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #region 导入

        /// <summary>
        /// 导入
        /// </summary>
        /// <param name="entitys">接收的导入集合</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult ImportExcelToData([FromBody]List<MD_NonStandardConfigImport> entitys)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                if (_app.ImportExcelToBaseData(entitys, GetCurrentUser().LoginAccount, out error_message))
                {
                    result.Data = true;
                    result.Code = (int)WMSStatusCode.Success;
                }
                else
                {
                    result.Data = false;
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
            return Json(result);
        }

        #endregion
    }
}
