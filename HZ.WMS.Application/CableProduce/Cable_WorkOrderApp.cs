using System;
using System.Collections.Generic;
using System.Linq;
using AOS.Core.Utilities;
using AOS.OMS.Entity.Basic;
using AOS.OMS.Entity.Sale;
using AOS.WMS.Entity.CableProduce;
using HZ.Core.Http;
using HZ.WMS.Entity;
using LinqKit;
using SqlSugar;

namespace HZ.WMS.Application.PP
{
    /// <summary>
    /// 生产订单主表
    /// </summary>
    public class Cable_WorkOrderApp : BaseApp<Cable_WorkOrder>
    {
        private BaseApp<BaseEntity> _baseApp = new BaseApp<BaseEntity>();

        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public Cable_WorkOrderApp() : base("DbConnectionForOMS")
        {
        }

        #endregion

        /**
         * 获取 计划订单列表
         */
        public List<Cable_WorkOrder> GetPageList(Pagination page, Cable_WorkOrderListReq workOrderListReq)
        {
            var searchCondition = PredicateBuilder.New<Cable_WorkOrder>(t => (
                (string.IsNullOrEmpty(workOrderListReq.ContractNo) ||
                 t.ContractNo.Equals(workOrderListReq.ContractNo)) &&
                (string.IsNullOrEmpty(workOrderListReq.SapNo) || t.SapNo.Equals(workOrderListReq.SapNo)) &&
                (string.IsNullOrEmpty(workOrderListReq.CustomerName) ||
                 t.CustomerName.Contains(workOrderListReq.CustomerName)) &&
                (workOrderListReq.WorkOrderPrintStatus == null ||
                 t.WorkOrderPrintStatus == workOrderListReq.WorkOrderPrintStatus) &&
                (string.IsNullOrEmpty(workOrderListReq.OrderTypeName) ||
                 t.OrderTypeName == workOrderListReq.OrderTypeName) &&
                (workOrderListReq.LabelPrintStatus == null ||
                 t.LabelPrintStatus == workOrderListReq.LabelPrintStatus) &&
                (workOrderListReq.ShippingMarkPrintStatus == null ||
                 t.ShippingMarkPrintStatus == workOrderListReq.ShippingMarkPrintStatus) &&
                (workOrderListReq.SerialNo == null ||
                 t.SerialNo.Contains(workOrderListReq.SerialNo)) &&
                (workOrderListReq.ElevatorType == null ||
                 t.ElevatorType.Contains(workOrderListReq.ElevatorType)) &&
                (workOrderListReq.ScanStatus == null ||
                 t.ScanStatus == workOrderListReq.ScanStatus) &&
                (workOrderListReq.HoistwayStatus == null ||
                 t.HoistwayStatus == workOrderListReq.HoistwayStatus)
            ));
            if (workOrderListReq.CreateDate != null && workOrderListReq.CreateDate.Length > 1)
            {
                var startTime = DateUtil.GetStartTime(workOrderListReq.CreateDate[0]);
                var endTime = DateUtil.GetEndTime(workOrderListReq.CreateDate[1]);
                var additionalCondition = PredicateBuilder.New<Cable_WorkOrder>(order =>
                    order.CTime >= startTime && order.CTime <= endTime);
                searchCondition = searchCondition.And(additionalCondition);
            }

            if (workOrderListReq.DeliveryDate != null && workOrderListReq.DeliveryDate.Length > 1)
            {
                var startTime = DateUtil.GetStartTime(workOrderListReq.DeliveryDate[0]);
                var endTime = DateUtil.GetEndTime(workOrderListReq.DeliveryDate[1]);
                var additionalCondition = PredicateBuilder.New<Cable_WorkOrder>(order =>
                    order.DeliveryDate >= startTime && order.DeliveryDate <= endTime);
                searchCondition = searchCondition.And(additionalCondition);
            }

            if (workOrderListReq.ShipmentDate != null && workOrderListReq.ShipmentDate.Length > 1)
            {
                var startTime = DateUtil.GetStartTime(workOrderListReq.ShipmentDate[0]);
                var endTime = DateUtil.GetEndTime(workOrderListReq.ShipmentDate[1]);
                var additionalCondition = PredicateBuilder.New<Cable_WorkOrder>(order =>
                    order.SetShipmentDate >= startTime && order.SetShipmentDate <= endTime);
                searchCondition = searchCondition.And(additionalCondition);
            }

            var list = GetPageList(page, searchCondition);

            var ids = list.Select(t => t.Id).ToList();
            var detailsList = DbContextForOMS.Queryable<SD_Cable_Sale_OrderDetails>()
                .Where(t => ids.Contains(t.Pid) && t.IsDelete == false).OrderBy(t => t.SerialNo).ToList();
            var parameterList = DbContextForOMS.Queryable<SD_Cable_Sale_Parameter>()
                .Where(t => ids.Contains(t.Pid) && t.IsDelete == false).ToList();
            var orderFloorList = DbContextForOMS.Queryable<SD_Cable_Sale_OrderFloor>()
                .Where(t => ids.Contains(t.Pid) && t.IsDelete == false).OrderBy(t => t.SerialNo).ToList();
            var orderFloorMap = orderFloorList.GroupBy(t => t.Pid).ToDictionary(t => t.Key, t => t.ToList());
            var detailMap = detailsList.GroupBy(t => t.Pid).ToDictionary(t => t.Key, t => t.ToList());
            var parameterMap = parameterList.GroupBy(t => t.Pid).ToDictionary(t => t.Key, t => t.FirstOrDefault());
            foreach (var cableWorkOrder in list)
            {
                if (detailMap.ContainsKey(cableWorkOrder.Id))
                {
                    cableWorkOrder.OrderDetailList = detailMap[cableWorkOrder.Id];
                }

                if (parameterMap.ContainsKey(cableWorkOrder.Id))
                {
                    cableWorkOrder.SaleParameter = parameterMap[cableWorkOrder.Id];
                }

                if (cableWorkOrder.SaleParameter != null && orderFloorMap.ContainsKey(cableWorkOrder.Id))
                {
                    cableWorkOrder.SaleParameter.OrderFloorList = orderFloorMap[cableWorkOrder.Id];
                }
            }

            return list;
        }

        public void GenerateProductNo(string[] ids)
        {
            // 查询排序配置
            var productionNoRuleList = DbContextForOMS.Queryable<ProductionNoRule>().Where(t => t.IsDelete == false)
                .OrderBy(t => t.SortNo).ToList();
            var orderList = DbContextForOMS.Queryable<SD_Cable_Sale_OrderInfo>().Where(t => ids.Contains(t.Id))
                .ToList();
            var orderDetailList = DbContextForOMS.Queryable<SD_Cable_Sale_OrderDetails>()
                .Where(t => ids.Contains(t.Pid)).ToList();
            var orderDetailMap = orderDetailList.GroupBy(t => t.Pid).ToDictionary(t => t.Key, t => t.ToList());
            foreach (var order in orderList)
            {
                if (!orderDetailMap.ContainsKey(order.Id))
                {
                    continue;
                }

                var customerPartNos = orderDetailMap[order.Id].Select(t => t.CustomerPartNo).ToList();
                order.ProductionNo = "99999";
                foreach (var productionNoRule in productionNoRuleList)
                {
                    var productionNoRules = productionNoRule.SortGroup.Split('/').ToList();
                    if (productionNoRules.All(item => customerPartNos.Contains(item)))
                    {
                        order.ProductionNo = productionNoRule.SortNo.ToString();
                        break;
                    }
                }
            }

            var sortedList = orderList.OrderBy(t => decimal.Parse(t.ProductionNo)).ToList();
            int num = 1;
            foreach (var sorted in sortedList)
            {
                sorted.ProductionNo = num.ToString();
                num++;
            }

            DbContextForOMS.Updateable<SD_Cable_Sale_OrderInfo>(sortedList).ExecuteCommand();
        }

        /**
         * 更新打印
         */
        public String UpdatePrint(Cable_PrintUpdate cablePrintUpdate)
        {
            try
            {
                DbContextForOMS.Ado.BeginTran();
                var orderInfos = DbContextForOMS.Queryable<SD_Cable_Sale_OrderInfo>()
                    .Where(t => cablePrintUpdate.Ids.Contains(t.Id)).ToList();
                foreach (var order in orderInfos)
                {
                    if (cablePrintUpdate.Identification == 1)
                    {
                        // 工单
                        order.WorkOrderPrintStatus = 1;
                    }

                    if (cablePrintUpdate.Identification == 2)
                    {
                        // 标签
                        order.LabelPrintStatus = 1;
                    }

                    if (cablePrintUpdate.Identification == 3)
                    {
                        // 唛头
                        order.ShippingMarkPrintStatus = 1;
                    }
                }

                DbContextForOMS.Updateable<SD_Cable_Sale_OrderInfo>(orderInfos).ExecuteCommand();
                DbContextForOMS.Ado.CommitTran();
                return "操作成功";
            }
            catch (Exception e)
            {
                DbContextForOMS.Ado.RollbackTran();
                return e.Message;
            }
        }

        /**
         * 替换字符串中特定位置的字符
         */
        private string ReplaceCharacterAt(string str, int position, char newChar)
        {
            if (str == null || str.Length != 16)
            {
                // 复位
                str = "0000000000000000";
            }

            // 检查位置是否合法
            if (position < 0 || position > str.Length)
            {
                throw new ArgumentOutOfRangeException(nameof(position), "Position is out of range.");
            }

            // 将字符串转为字符数组
            char[] charArray = str.ToCharArray();
            // 替换指定位置的字符
            charArray[position - 1] = newChar;
            // 将字符数组转回字符串
            return new string(charArray);
        }
    }
}