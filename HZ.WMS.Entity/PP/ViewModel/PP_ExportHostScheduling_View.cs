using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.PP.ViewModel
{
    /// <summary>
    /// 主机排产单
    /// </summary>
    public class PP_ExportHostScheduling_View
    {
        /// <summary>
        /// 序号
        /// </summary>
        [Description("序号")]
        public int? SequenceNo { get; set; }
        /// <summary>
        /// 线体描述
        /// </summary>
        [Description("装配线")]
        public string AssemblyLineNo { get; set; }
        /// <summary>
        /// OMS的客户订单号
        /// </summary>
        [Description("订单号")]
        public string OrderNo { get; set; }
        /// <summary>
        /// 合同号
        /// </summary>
        [Description("合同号")]
        public string ContractNo { get; set; }
        /// <summary>
        /// 发货单位
        /// </summary>
        [Description("发货单位")]
        public string Shipper { get; set; }
        /// <summary>
        /// 交货时间
        /// </summary>
        [Description("交货时间")]
        public string DevliyerDate { get; set; }
        /// <summary>
        /// 产品型号(物料名称)
        /// </summary>
        [Description("产品型号")]
        public string MaterialName { get; set; }
        /// <summary>
        /// 产品件号(物料编码)
        /// </summary>
        [Description("产品件号")]
        public string MaterialNo { get; set; }
        /// <summary>
        /// 富沃德机型
        /// </summary>
        [Description("富沃德机型")]
        public string MachineType { get; set; }
        /// <summary>
        /// 盘车齿轮
        /// </summary>
        [Description("盘车齿轮")]
        public string BarringGear { get; set; }
        /// <summary>
        /// 制动器电压
        /// </summary>
        [Description("制动器电压")]
        public string BrakeVoltage { get; set; }
        /// <summary>
        /// 连接方式
        /// </summary>
        [Description("连接方式")]
        public string ConnectMethod { get; set; }
        /// <summary>
        /// 编码器型号
        /// </summary>
        [Description("编码器型号")]
        public string EncoderModel { get; set; }
        /// <summary>
        /// 编码器线长
        /// </summary>
        [Description("编码器线长/m")]
        public string EncoderLength { get; set; }
        /// <summary>
        /// 软管
        /// </summary>
        [Description("软管")]
        public string Hose { get; set; }
        /// <summary>
        /// 变频器
        /// </summary>
        [Description("变频器")]
        public string FreConverter { get; set; }
        /// <summary>
        /// 护罩
        /// </summary>
        [Description("护罩")]
        public string Shield { get; set; }
        /// <summary>
        /// 远程松闸
        /// </summary>
        [Description("远程松闸")]
        public string RemoteBrake { get; set; }
        /// <summary>
        /// 松闸线长
        /// </summary>
        [Description("松闸线长/m")]
        public string BrakeLength { get; set; }
        /// <summary>
        /// 油漆
        /// </summary>
        [Description("油漆")]
        public string Paint { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量")]
        public int? Qty { get; set; }
        /// <summary>
        /// 额定载重
        /// </summary>
        [Description("额定载重")]
        public string RatedLoad { get; set; }
        /// <summary>
        /// 额定速度
        /// </summary>
        [Description("额定速度")]
        public string RatedSpeed { get; set; }
        /// <summary>
        /// 额定电压
        /// </summary>
        [Description("额定电压")]
        public string RatedVoltage { get; set; }
        /// <summary>
        /// 铭牌要求
        /// </summary>
        [Description("铭牌要求")]
        public string NameReq { get; set; }
        /// <summary>
        /// 曳引比
        /// </summary>
        [Description("曳引比")]
        public string TractionRatio { get; set; }
        /// <summary>
        /// 客户型号
        /// </summary>
        [Description("客户型号")]
        public string CustomerModel { get; set; }
        /// <summary>
        /// 箱板刷字
        /// </summary>
        [Description("箱板刷字")]
        public string CaseBrush { get; set; }
        /// <summary>
        /// 是否是出口梯
        /// </summary>
        [Description("是否是出口梯")]
        public string IsExport { get; set; }
        /// <summary>
        /// 项目名称
        /// </summary>
        [Description("项目名称")]
        public string ProjectName { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        [Description("备注")]
        public string Remark { get; set; }
        /// <summary>
        /// 接受编号
        /// </summary>
        [Description("接受编号")]
        public string AcceptanceNo { get; set; }
        /// <summary>
        /// 入库时间
        /// </summary>
        [Description("入库时间")]
        public DateTime? StorageTime { get; set; }
        /// <summary>
        /// 出厂编号
        /// </summary>
        [Description("出厂编号")]
        public string SerialNo { get; set; }
        /// <summary>
        /// 批次
        /// </summary>
        [Description("批次")]
        public string Batch { get; set; }
        /// <summary>
        /// 主机未完成原因
        /// </summary>
        [Description("主机未完成原因")]
        public string UnfinishedReason { get; set; }
        /// <summary>
        /// 部件代码
        /// </summary>
        [Description("部件代码")]
        public string PartCode { get; set; }
        /// <summary>
        /// 上行超速保护代码
        /// </summary>
        [Description("上行超速保护代码")]
        public string UplinkCode { get; set; }
        /// <summary>
        /// 意外移动保护代码
        /// </summary>
        [Description("意外移动保护代码")]
        public string DownCode { get; set; }

        [Description("EAP出厂编号")]
        public string EapSerialNo { get; set; }
        /// <summary>
        /// 开始日期(装配日期)
        /// </summary>
        public DateTime? StartTime { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CTime { get; set; }
        /// <summary>
        /// 生产调度员,101是主机，组件
        /// </summary>
        public string ProductionScheduler { get; set; }
        /// <summary>
        /// 工作中心
        /// </summary>
        public string ProductionLineCode { get; set; }
        /// <summary>
        /// 生产订单
        /// </summary>
        public string ProductionOrderNo { get; set; }
    }
}
