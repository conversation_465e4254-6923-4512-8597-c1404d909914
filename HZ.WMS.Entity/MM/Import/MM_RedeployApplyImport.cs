using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.MM.Import
{
    /// <summary>
    /// 借出单导入
    /// </summary>
    public class MM_RedeployApplyImport
    {
        ///// <summary>
        ///// 调拨申请行号
        ///// </summary>
        //public int? 行号 { get; set; }

        /// <summary>
        /// 物料编码
        /// </summary>
        public string 物料编码 { get; set; }

        /// <summary>
        /// 出厂编号
        /// </summary>
        public string 出厂编号 { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public decimal? 数量 { get; set; }

        /// <summary> 
        /// "转出仓库编码
        /// </summary> 
        public string 转出仓库编码 { get; set; }

        /// <summary> 
        /// 转入仓库编号
        /// </summary> 
        public string 转入仓库编码 { get; set; }

        /// <summary> 
        /// 销售订单
        /// </summary> 
        public string 销售订单 { get; set; }

        /// <summary> 
        /// 销售订单项目
        /// </summary> 
        public int? 销售订单项目 { get; set; }


        /// <summary> 
        /// 备注
        /// </summary> 
        public string 备注 { get; set; }
    }
}
