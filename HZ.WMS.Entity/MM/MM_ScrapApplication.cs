using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.MM
{
    /// <summary>
    /// 报废申请
    /// </summary>
    public class MM_ScrapApplication : BaseEntity
    {
        /// <summary>
        /// 主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Description("ID")]
        public string ID { get; set; }

        /// <summary>
        /// 调拨申请单号
        /// </summary>
        [Description("调拨申请单号")]
        public string DocNum { get; set; }


        /// <summary>
        /// 办理人员编号
        /// </summary>
        [Description("办理人员编号")]
        public string HandlenCode { get; set; }

        /// <summary>
        /// 办理人员
        /// </summary>
        [Description("办理人员")]
        public string HandlenName { get; set; }

        /// <summary>
        /// 状态 0:未审核 1：审核中 2：已审核 3：已取消
        /// </summary>
        [Description("状态")]
        public int? Status { get; set; }
        /// <summary> 
        /// 是否作废
        /// </summary> 
        [Description("是否作废")]
        public bool? IsCancel { get; set; }
        /// <summary> 
        /// 是否过账 1：已过帐 0：未过账
        /// </summary> 
        [Description("是否过账")]
        public bool? IsPosted { get; set; }

        /// <summary>
        /// 过账人
        /// </summary>
        [Description("过账人")]
        public string PostUser { get; set; }

        /// <summary>
        /// 过账日期
        /// </summary>
        [Description("过账日期")]
        public DateTime? PostTime { get; set; }

        /// <summary>
        /// 手动过账日期
        /// </summary>
        [Description("手动过账日期")]
        public DateTime? ManualPostTime { get; set; }

        /// <summary>
        /// SAP过账标示
        /// </summary>
        [Description("SAP过账标示")]
        public string SAPmark { get; set; }

        /// <summary>
        /// SAP过账消息
        /// </summary>
        [Description("SAP过账消息")]
        public string SAPmessage { get; set; }


        /// <summary>
        /// 部门
        /// </summary>
        [Description("部门")]
        public string Department { get; set; }


        /// <summary>
        /// 报废原因
        /// </summary>
        [Description("报废原因")]
        public string ScrapReason { get; set; }
    }
}
