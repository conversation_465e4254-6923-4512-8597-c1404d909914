using System.Web.Mvc;

namespace HZ.WMS.WebAPI.Areas.PUB
{
    public class PUBAreaRegistration : AreaRegistration 
    {
        public override string AreaName 
        {
            get 
            {
                return "PUB";
            }
        }

        public override void RegisterArea(AreaRegistrationContext context) 
        {
            context.MapRoute(
                "PUB_default",
                "PUB/{controller}/{action}/{id}",
                new { action = "Index", id = UrlParameter.Optional }
            );
        }
    }
}