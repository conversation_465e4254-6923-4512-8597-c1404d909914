using System;
using System.ComponentModel;
using SqlSugar;
//Chloe框架


namespace HZ.WMS.Entity.SD
{
    /// <summary>
    /// 销售退货
    /// </summary>
    [SugarTable("SD_ReturnScan")]
    public class SD_ReturnScan:BaseEntity
    {
        /// <summary> 
        /// 销售退货ID
        /// </summary> 
        [SugarColumn(IsPrimaryKey = true)]
        [Description("销售退货ID")]
        public string ReturnScanID { get;set;}

        /// <summary> 
        /// wms单号
        /// </summary> 
        [Description("wms单号")]
        public string DocNum {get;set;}

        /// <summary> 
        /// wms行号
        /// </summary> 
        [Description("wms行号")]
        public int? Line { get; set; }

        /// <summary> 
        /// 销售订单编号
        /// </summary> 
        [Description("销售订单编号")]
        public string BaseEntry {get;set;}

        /// <summary> 
        /// 销售订单号
        /// </summary> 
        [Description("销售订单号")]
        public string BaseNum {get;set;}

        /// <summary> 
        /// 销售订单行号
        /// </summary> 
        [Description("销售订单行号")]
        public int? BaseLine { get; set; }

        /// <summary> 
        /// 批次
        /// </summary> 
        [Description("批次")]
        public string BatchNum { get; set; }

        /// <summary> 
        /// 序列号
        /// </summary> 
        [Description("序列号")]
        public string BarCode { get; set; }

        /// <summary> 
        /// 客户编号
        /// </summary> 
        [Description("客户编号")]
        public string CustomerCode {get;set;}

        /// <summary> 
        /// 客户名称
        /// </summary> 
        [Description("客户名称")]
        public string CustomerName {get;set;}

        /// <summary> 
        /// 物料编号
        /// </summary> 
        [Description("物料编号")]
        public string ItemCode {get;set;}

        /// <summary> 
        /// 物料名称
        /// </summary> 
        [Description("物料名称")]
        public string ItemName {get;set;}

        /// <summary> 
        /// 物料组编号
        /// </summary> 
        [Description("物料组编号")]
        public string ItmsGrpCode {get;set;}

        /// <summary> 
        /// 物料组名称
        /// </summary> 
        [Description("物料组名称")]
        public string ItmsGrpName {get;set;}

        /// <summary> 
        /// 数量
        /// </summary> 
        [Description("数量")]
        public decimal? Qty {get;set;}

        /// <summary> 
        /// 库存单位
        /// </summary> 
        [Description("库存单位")]
        public string Unit {get;set;}

        /// <summary> 
        /// 仓库编号
        /// </summary> 
        [Description("仓库编号")]
        public string WhsCode {get;set;}

        /// <summary> 
        /// 仓库名称
        /// </summary> 
        [Description("仓库名称")]
        public string WhsName {get;set;}

        /// <summary> 
        /// 区域编号
        /// </summary> 
        [Description("区域编号")]
        public string RegionCode {get;set;}

        /// <summary> 
        /// 区域名称
        /// </summary> 
        [Description("区域名称")]
        public string RegionName {get;set;}

        /// <summary> 
        /// 库位编号
        /// </summary> 
        [Description("库位编号")]
        public string BinLocationCode {get;set;}

        /// <summary> 
        /// 库位名称
        /// </summary> 
        [Description("转入库位名称")]
        public string BinLocationName {get;set;}

        /// <summary> 
        /// 是否已过账
        /// </summary> 
        [Description("是否已过账")]
        public bool? IsPosted {get;set;}

        /// <summary> 
        /// 过账人
        /// </summary> 
        [Description("过账人")]
        public string PostUser {get;set;}

        /// <summary> 
        /// 过账时间
        /// </summary> 
        [Description("过账时间")]
        public DateTime? PostTime {get;set;}

        /// <summary>
        /// Sap销售凭证单号
        /// </summary>
        [Description("Sap销售凭证单号")]
        public string SapDocNum { get; set; }

        /// <summary>
        /// Sap销售凭证行号
        /// </summary>
        [Description("Sap销售凭证行号")]
        public int? SapLine { get; set; }

        /// <summary>
        /// 手动过账时间
        /// </summary>
        [Description("手动过账时间")]
        public DateTime? ManualPostTime { get; set; }

        /// <summary>
        /// SAP过账标示
        /// </summary>
        [Description("SAP过账标示")]
        public string SAPmark { get; set; }

        /// <summary>
        /// SAP过账信息
        /// </summary>
        [Description("SAP过账信息")]
        public string SAPmessage { get; set; }

    }
}


