//Chloe框架
using SqlSugar;
using System;
using System.ComponentModel;

namespace HZ.WMS.Entity.MM
{

    public class MM_TakeStockScanImport 
    {

    
        /// <summary>
        /// 盘点计划单号
        /// </summary>
        [Description("盘点计划单号")]
        public string 盘点计划单号 { get; set; }

        /// <summary>
        /// 出厂编号
        /// </summary>
        [Description("出厂编号")]
        public string 出厂编号 { get; set; }


        /// <summary>
        /// 物料编号
        /// </summary>
        [Description("物料编号")]
        public string 物料编号 { get; set; }

        /// <summary>
        /// 库存数量
        /// </summary>
        [Description("库存数量")]
        public decimal? 库存数量 { get; set; }


        /// <summary>
        /// 盘点数量
        /// </summary>
        [Description("盘点数量")]
        public decimal? 盘点数量 { get; set; }
      
        /// <summary>
        /// 仓库编号
        /// </summary>
        [Description("仓库编号")]
        public string 仓库编号 { get; set; }
       
        /// <summary>
        /// 供应商编号
        /// </summary>
        [Description("供应商编号")]
        public string 供应商编号 { get; set; }


        /// <summary>
        /// 销售单号
        /// </summary>
        [Description("销售单号")]
        public string 销售单号 { get; set; }

        /// <summary>
        /// 销售行号
        /// </summary>
        [Description("销售行号")]
        public int? 销售行号 { get; set; }

        /// <summary>
        /// 评估类型
        /// </summary>
        [Description("评估类型")]
        public string 评估类型 { get; set; }


        /// <summary>
        /// 特殊库存 O:带供应商代码的,E:带销售订单的,T: 是在途库存
        /// </summary>
        [Description("特殊库存")]
        public string 特殊库存 { get; set; }



    }
}


