//Chloe框架
using SqlSugar;


namespace HZ.WMS.Entity.MM
{
    [SugarTable("MM_TakeStockPlan")]
    public class MM_TakeStockPlan : BaseEntity
    {
        /// <summary> 
        /// 计划ID
        /// </summary> 
        [SugarColumn(IsPrimaryKey = true)]
        public string PlanID { get; set; }
        /// <summary> 
        /// 计划单号
        /// </summary> 
        public string DocNum { get; set; }
        /// <summary> 
        /// 责任人
        /// </summary> 
        public string PUser { get; set; }
        /// <summary> 
        /// 状态(未开始/已开始/已完成)
        /// 默认 1=未开始
        /// </summary> 
        public int Status { get; set; } = 1;

    }
}


