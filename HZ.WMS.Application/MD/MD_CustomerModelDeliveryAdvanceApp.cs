using System;
using System.Collections.Generic;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.MD.Import;

namespace HZ.WMS.Application.MD
{
    /// <summary>
    /// 客户机型交期提前管理
    /// </summary>
    public class MD_CustomerModelDeliveryAdvanceApp : BaseApp<MD_CustomerModelDeliveryAdvance>
    {
        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public MD_CustomerModelDeliveryAdvanceApp() : base(){}

        #endregion

        #region 导入

        /// <summary>
        /// 导入
        /// </summary>
        /// <param name="excelList">接收的集合</param>
        /// <param name="opUser">当前用户</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool ImportExcelToBaseData(List<MD_CustomerModelDeliveryAdvanceImport> excelList, string opUser, out string error_message)
        {
            error_message = "";
            var flag = true;
            DateTime date = DateTime.Now;
            var entityList = new List<MD_CustomerModelDeliveryAdvance>();

            try
            {
                foreach (var item in excelList)
                {
                    if (string.IsNullOrEmpty(item.CustomerName))
                    {
                        error_message = "客户名称不能为空";
                        return false;
                    }

                    if (string.IsNullOrEmpty(item.CustomerCode))
                    {
                        error_message = "客户代码不能为空";
                        return false;
                    }

                    if (string.IsNullOrEmpty(item.ProduceModel))
                    {
                        error_message = "生产序号不能为空";
                        return false;
                    }

                    var entity = new MD_CustomerModelDeliveryAdvance
                    {
                        Id = Guid.NewGuid().ToString(),
                        CustomerName = item.CustomerName,
                        CustomerCode = item.CustomerCode,
                        ProduceModel = item.ProduceModel,
                        AdvanceDays = item.AdvanceDays,
                        CUser = opUser,
                        CTime = date,
                        IsDelete = false
                    };

                    entityList.Add(entity);
                }

                if (entityList.Count > 0)
                {
                    Insert(entityList);
                }

                return flag;
            }
            catch (Exception ex)
            {
                error_message = ex.Message;
                return false;
            }
        }

        #endregion
    }
}

