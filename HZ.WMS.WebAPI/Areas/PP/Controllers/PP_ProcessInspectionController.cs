using HZ.Core.Http;
using HZ.Core.Office;
using HZ.WMS.Application;
using HZ.WMS.Application.MD;
using HZ.WMS.Application.PP;
using HZ.WMS.Entity.PP;
using HZ.WMS.Entity.PP.Dto;
using HZ.WMS.Entity.Sys;
using HZ.WMS.WebAPI.Controllers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;

namespace HZ.WMS.WebAPI.Areas.PP.Controllers
{
    /// <summary>
    /// 生产过程检验
    /// </summary>
    public class PP_ProcessInspectionController : ApiBaseController
    {
        private PP_ProcessInspectionApp _app = new PP_ProcessInspectionApp();
        private PP_ProcessInspectionDetailApp detailApp = new PP_ProcessInspectionDetailApp();
        private PP_ProductionOrderApp orderApp = new PP_ProductionOrderApp();
        private MD_ProcessInspectionApp InspectionApp = new MD_ProcessInspectionApp();

        #region 查询分页列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]string keyword, [FromUri]DateTime[] dateValue, [FromUri]string ProductionScheduler)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];

                var itemsData = _app.GetPageViewList(page, keyword, fromTime, toTime, ProductionScheduler);
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 查询子表分页列表

        /// <summary>
        /// 查询子表分页列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetDetailPageList([FromUri]Pagination page, [FromUri]string SerialNo, [FromUri]string ProductionOrderNo)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = detailApp.GetPageList(page, w => (w.SerialNo + w.ProductionOrderNo) == (SerialNo + ProductionOrderNo)).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 查询子表列表

        /// <summary>
        /// 查询子表列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetDetailList([FromUri]string SerialNo, [FromUri]string ProductionOrderNo)
        {
            var result = new ResponseData();
            try
            {
                result.Data = detailApp.GetList(w => (w.SerialNo + w.ProductionOrderNo) == (SerialNo + ProductionOrderNo)).ToList();
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取详情

        /// <summary>
        /// 获取详情
        /// </summary>
        /// <param name="ID"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetEntity([FromUri]string ID)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.GetEntityByKey(ID);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 根据序列号查询生产订单

        /// <summary>
        /// 根据序列号查询生产订单
        /// </summary>
        /// <param name="SerialNo"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetOrderBySerialNo([FromUri]string SerialNo)
        {
            var result = new ResponseData();
            try
            {
                var order = orderApp.GetFirstEntity(w => w.SerialNo == SerialNo);
                if (order != null)
                {
                    var ins = _app.GetFirstEntity(w => (w.SerialNo + w.ProductionOrderNo) == (order.SerialNo + order.ProductionOrderNo));
                    if (ins != null)
                    {
                        result.Message = "该订单已添加检查项不能重复添加！";
                        result.Code = (int)WMSStatusCode.Failed;
                        return Json(result);
                    }
                }
                result.Data = order;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 根据订单号查询生产订单

        /// <summary>
        /// 根据订单号查询生产订单
        /// </summary>
        /// <param name="ProductionOrderNo"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetOrderByOrderNo([FromUri]string ProductionOrderNo)
        {
            var result = new ResponseData();
            try
            {
                var order = orderApp.GetSapOrderList(w => w.ProductionOrderNo == ProductionOrderNo).ToList().FirstOrDefault();
                if(order != null)
                {
                    var ins = _app.GetFirstEntity(w => (w.SerialNo + w.ProductionOrderNo) == (order.SerialNo + order.ProductionOrderNo));
                    if (ins != null)
                    {
                        result.Message = "该订单已添加检查项不能重复添加！";
                        result.Code = (int)WMSStatusCode.Failed;
                        return Json(result);
                    }
                }
                result.Data = order;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 根据关联订单号查询生产订单

        /// <summary>
        /// 根据关联订单号查询生产订单
        /// </summary>
        /// <param name="ProductionOrderNo"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetOrderByRelevant([FromUri]string ProductionOrderNo)
        {
            var result = new ResponseData();
            try
            {
                var order = _app.GetFirstEntity(w => w.ProductionOrderNo == ProductionOrderNo);
                if(order != null)
                {
                    var inslist = detailApp.GetList(w => (w.SerialNo + w.ProductionOrderNo) == (order.SerialNo + order.ProductionOrderNo)).ToList();
                    inslist.ForEach(item =>
                    {
                        item.ID = string.Empty;
                        item.SerialNo = string.Empty;
                        item.ProductionOrderNo = string.Empty;
                        item.CUser = string.Empty;
                        item.CTime = null;
                        item.MUser = string.Empty;
                        item.MTime = null;
                    });
                    result.Data = inslist;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取生产线

        /// <summary>
        /// 获取生产线
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetProductionLine()
        {
            var result = new ResponseData();
            try
            {
                result.Data = InspectionApp.GetList().Select(s => new { s.ProductionLineCode ,s.ProductionLineDes}).Distinct().ToList();
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取工序

        /// <summary>
        /// 获取工序
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetWorkingProcedure(string LineNo)
        {
            var result = new ResponseData();
            try
            {
                result.Data = InspectionApp.GetList(w => w.ProductionLineCode == LineNo, "WorkingProcedureCode").Select(s => new { s.WorkingProcedureCode, s.WorkingProcedureDes }).Distinct().ToList();
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取检验项

        /// <summary>
        /// 获取检验项
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetInspection(string lineNo,string procedureCode)
        {
            var result = new ResponseData();
            try
            {
                result.Data = InspectionApp.GetList(w => w.ProductionLineCode == lineNo && w.WorkingProcedureCode == procedureCode, "InspectionItem").Select(s => s.InspectionItem).Distinct().ToList();
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 提交检验

        /// <summary>
        /// 提交检验
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult SubmitCheck([FromBody]PP_ProcessInspection_Dto entity)
        {
            var result = new ResponseData();
            try
            {
                var list = entity.DetailList.Select(s => new { s.ProductionLineCode, s.WorkingProcedureCode }).Distinct().ToList();
                foreach (var item in list)
                {
                    var mdList = InspectionApp.GetList(w => w.ProductionLineCode == item.ProductionLineCode && w.WorkingProcedureCode == item.WorkingProcedureCode).ToList();
                    var piList = entity.DetailList.Where(w => w.ProductionLineCode == item.ProductionLineCode && w.WorkingProcedureCode == item.WorkingProcedureCode);
                    if (piList.Count() < mdList?.Count())
                    {
                        result.Message = $"装配线{item.ProductionLineCode}的工序{item.WorkingProcedureCode}维护的检测项有{mdList?.Count()}条，但是本次只录入{piList.Count()}条，是否继续保存";
                        return Json(result);
                    }
                }

                foreach (var item in entity.DetailList)
                {
                    var ins = InspectionApp.GetFirstEntity(w => w.ProductionLineCode == item.ProductionLineCode && w.WorkingProcedureCode == item.WorkingProcedureCode && w.InspectionItem == item.InspectionItem);
                    if (item.Measurements < ins.LowerLimit || item.Measurements > ins.UpperLimit)
                    {
                        result.Message = $"装配线{item.ProductionLineCode}的工序{item.WorkingProcedureCode}工序检测项{item.InspectionItem}测量值不在设置的合格值区间内，是否继续提交！";
                        return Json(result);
                    }
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 添加

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Add([FromBody]PP_ProcessInspection_Dto entity)
        {
            var result = new ResponseData();
            try
            {
                Sys_User currLoginUser = GetCurrentUser();
                if (!_app.Add(entity, currLoginUser.LoginAccount))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Update([FromBody]PP_ProcessInspection_Dto entity)
        {
            var result = new ResponseData();
            try
            {
                Sys_User user = GetCurrentUser();
                if (!_app.Edit(entity, user.LoginAccount))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 作废（包括批量）

        /// <summary>
        /// 作废
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Invalid([FromBody]List<PP_ProcessInspectionDetail> Details)
        {
            var result = new ResponseData();
            try
            {
                foreach (var item in Details)
                {
                    item.MUser = GetCurrentUser().LoginAccount;
                    item.IsInvalid = true;
                }
                detailApp.UpdateWithTran(Details);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除（包括批量）

        /// <summary>
        /// 删除
        /// </summary>
        /// <returns></returns>
        [HttpDelete]
        public IHttpActionResult Delete([FromBody]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.DeleteByKeys(ids, GetCurrentUser().LoginAccount);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 导出

        /// <summary>
        /// 导出
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri]string keyword, [FromUri]DateTime[] dateValue, [FromUri]string ProductionScheduler)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];

                var list = _app.GetList(
                    t => (string.IsNullOrEmpty(keyword)
                        || t.SerialNo.Contains(keyword)
                        || t.ProductionOrderNo.Contains(keyword)
                        || t.MaterialNo.Contains(keyword)
                        || t.MaterialName.Contains(keyword)
                        || t.ContractNo.Contains(keyword)
                        || t.SalesOrderNo.Contains(keyword)
                        || t.Shippers.Contains(keyword))
                        && (string.IsNullOrEmpty(ProductionScheduler) || t.ProductionScheduler.Contains(ProductionScheduler))
                    && (t.StartTime >= fromTime && t.StartTime < toTime))
                    .Select(s => s.SerialNo + s.ProductionOrderNo)
                    .ToList();

                var itemsData = detailApp.GetList(t => list.Contains(t.SerialNo + t.ProductionOrderNo)).ToList();

                List<ExcelColumn<PP_ProcessInspectionDetail>> columns = ExcelService.FetchDefaultColumnList<PP_ProcessInspectionDetail>();
                string[] ignoreField = new string[]
                {
                    "ID"
                    ,"IsDelete"
                    ,"MUser"
                    ,"MTime"
                    ,"DUser"
                    ,"DTime"
                };
                List<ExcelColumn<PP_ProcessInspectionDetail>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<PP_ProcessInspectionDetail> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                //columns.ForEach((column) =>
                //{
                //    if (column.ColumnName == "IsPosted")
                //    {
                //        column.Formattor = ExcelExportFormatter.IsOrNoFormatter;
                //    }
                //});

                return ExportToExcelFile<PP_ProcessInspectionDetail>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #region 打印

        /// <summary>
        /// 打印
        /// </summary>
        /// <param name="docNums">工单号</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult Print([FromUri]string[] docNums)
        {
            var result = new ResponseData();
            try
            {
                //var docStr = string.Join(",", docNums);
                //DevExpress.XtraReports.UI.XtraReport report = DevExpress.XtraReports.UI.XtraReport.FromFile(HttpContext.Current.Server.MapPath("~/") + ConfigurationManager.AppSettings["LabelTemplateUploadPath"]?.ToString() + "转子身份卡.repx", true);
                //DevExpress.DataAccess.Sql.SqlDataSource dataSource = report.DataSource as DevExpress.DataAccess.Sql.SqlDataSource;
                //DevExpress.DataAccess.Sql.QueryParameter parameter = new DevExpress.DataAccess.Sql.QueryParameter()
                //{
                //    Name = "@docNums",
                //    Type = typeof(string),
                //    Value = docStr
                //};
                //dataSource.Queries[0].Parameters[0] = parameter;
                //return base.PrintToPDF(report);
                result.Code = (int)WMSStatusCode.Failed;
                result.Message = "未提供打印模板！";
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion
    }
}
