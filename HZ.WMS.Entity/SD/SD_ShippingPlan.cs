using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.SD
{
    /// <summary>
    /// 发运计划
    /// </summary>
    [SugarTable("SD_ShippingPlan")]
    public class SD_ShippingPlan : BaseEntity
    {
        /// <summary> 
        /// ID
        /// </summary> 
        [SugarColumn(IsPrimaryKey = true)]
        [Description("ID")]
        public string ShippingPlanID { get; set; }

        /// <summary> 
        /// wms单号
        /// </summary> 
        [Description("wms单号")]
        public string DocNum { get; set; }

        /// <summary> 
        /// 订单类型
        /// </summary> 
        [Description("订单类型")]
        public string SalesOrderType { get; set; }

        /// <summary> 
        /// 是否下载
        /// </summary> 
        [Description("是否下载")]
        public string PSStatus { get; set; }

        /// <summary> 
        /// 状态 0：未发运1：已同步 2：发运中 3：已完成
        /// </summary> 
        [Description("状态")]
        public int? ShippingPlanStatus { get; set; }

        /// <summary> 
        /// 发货日期
        /// </summary> 
        [Description("发货日期")]
        public DateTime? DeliveryDate { get; set; }

        /// <summary> 
        /// 实际日期
        /// </summary> 
        [Description("实际日期")]
        public DateTime? ActualDate { get; set; }
        

    }
}
