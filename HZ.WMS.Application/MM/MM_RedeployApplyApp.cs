using SqlSugar;
using HZ.WMS.Application.MD;
using HZ.WMS.Application.SAP;
using HZ.WMS.Application.Sys;
using HZ.WMS.Entity;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.MM;
using HZ.WMS.Entity.MM.Import;
using HZ.WMS.Entity.MM.Parameters;
using HZ.WMS.Entity.MM.ViewModel;
using HZ.WMS.Entity.SAP;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Application.MM
{
    /// <summary>
    /// 调拨申请单
    /// </summary>
    public class MM_RedeployApplyApp : BaseApp<MM_RedeployApply>
    {
        #region 初始化

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public MM_RedeployApplyApp() : base(){ }

        MM_RedeployApplyDetailApp _detailApp = new MM_RedeployApplyDetailApp();

        #endregion

        #region 保存

        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="Parameters">参数类</param>
        /// <param name="user">用户</param>
        /// <param name="error_message">错误信息</param>
        /// <returns></returns>
        public bool Save(MM_RedeployApplyParameters Parameters, string user, out string error_message)
        {
            error_message = "提交成功";
            try
            {
                #region 属性初始化

                DateTime time = DateTime.Now;
                var list = new List<MM_RedeployApply>();

                #endregion

                #region 逻辑处理

                //主表信息
                list.Add(new MM_RedeployApply
                {
                    ManualPostTime = Parameters.ManualPostTime,
                    DocNum = Parameters.DocNum,
                    IsPosted = false,
                    IsDelete = false,
                    IsCancel = false,
                    CUser = user,
                    CTime = time,
                    HandlenCode = Parameters.HandlenCode,
                    HandlenName = Parameters.HandlenName,
                    Remark = Parameters.Remark,
                    Department = Parameters.Department,
                    Status = 0
                });
                int i = 0;
                //明细表信息
                Parameters.DetailedList.ForEach(x =>
                {
                    i += 1;
                    x.Line = i;
                    x.CompanyCode = "001";
                    x.FactoryCode = Parameters.FactoryCode;
                    x.IsPosted = false;
                    x.DocNum = Parameters.DocNum;
                    x.IsDelete = false;
                    x.CUser = user;
                    x.CTime = time;
                    x.MovementType = "311";
                });

                #endregion

                #region 事务操作

                DbContext.Ado.BeginTran();//开启事务
                _detailApp.DbContext = this.DbContext;

                //主表插入
                Insert(list);

                //明细插入
                _detailApp.Insert(Parameters.DetailedList);

                DbContext.Ado.CommitTran();//提交事务

                #endregion

                return true;
            }
            catch (Exception ex)
            {
                if (DbContext.Ado.IsAnyTran())//失败回滚
                    DbContext.Ado.RollbackTran();

                error_message = ex.Message;
                return false;
            }
        }

        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="Parameters">参数类</param>
        /// <param name="user">用户</param>
        /// <param name="error_message">错误信息</param>
        /// <returns></returns>
        public bool Update(MM_RedeployApplyParameters Parameters, string user, out string error_message)
        {
            error_message = "更新成功";

            try
            {
                #region 属性初始化

                DateTime time = DateTime.Now;
                var AddmsDetailList = new List<MM_RedeployApplyDetail>();
                var UpdatemsDetailList = new List<MM_RedeployApplyDetail>();

                #endregion

                #region 逻辑处理

                //根据主键ID查询采购退料明细信息
                var entities = _detailApp.GetListByKeys(Parameters.deldetailArray);

                //根据申请单号查询采购退料主信息
                var query = GetList(x => x.DocNum == Parameters.DocNum)?.ToList();

                int i = 0;
                //插入子表信息
                foreach (var x in Parameters.DetailedList)
                {
                    i += 1;
                    x.Line = i;
                    if (string.IsNullOrEmpty(x.DetailedID))
                    {
                        x.DocNum = Parameters.DocNum;
                        x.CompanyCode = "001";
                        x.FactoryCode = "2002";
                        x.MovementType = "311";
                        x.IsPosted = false;
                        x.IsDelete = false;
                        x.CUser = user;
                        x.CTime = time;
                        AddmsDetailList.Add(x);
                    }
                    else
                    {
                        x.MUser = user;
                        x.MTime = time;
                        UpdatemsDetailList.Add(x);
                    }
                }

                #endregion

                #region 事务操作

                DbContext.Ado.BeginTran();//开启事务
                _detailApp.DbContext = DbContext;

                //删除明细信息
                if (entities.Count > 0)
                {
                    _detailApp.Delete(entities, user);
                }
                //更新主信息
                if (query != null && query.Count > 0)
                {

                    query[0].HandlenCode = Parameters.HandlenCode;
                    query[0].HandlenName = Parameters.HandlenName;
                    query[0].Department = Parameters.Department;
                    query[0].Remark = Parameters.Remark;
                    query[0].MUser = user;
                    query[0].MTime = time;
                    query[0].ManualPostTime = Parameters.ManualPostTime;
                    Update(query);
                }
                //插入明细信息
                //this.Update(listms);
                if (AddmsDetailList.Count > 0)
                {
                    _detailApp.Insert(AddmsDetailList);
                }
                //更新明细信息
                if (UpdatemsDetailList.Count > 0)
                {
                    _detailApp.Update(UpdatemsDetailList);
                }

                DbContext.Ado.CommitTran();//提交事务

                #endregion

                return true;
            }
            catch (Exception ex)
            {
                if (DbContext.Ado.IsAnyTran())//失败回滚
                    DbContext.Ado.RollbackTran();

                error_message = ex.Message;
                return false;
            }
        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除校验
        /// </summary>
        /// <param name="entities">集合</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        private bool CheckDelete(List<MM_DepRequisition> entities, out string error_message)
        {
            bool isPass = true;
            error_message = "";
            foreach (var x in entities)
            {
                if (x.IsPosted == true)
                {
                    error_message = "ui.Message.PODeletePostingWarning"; // "已过账数据不允许删除!";
                    isPass = false;
                    break;
                }
            }

            return isPass;
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="DocNums">单号数组</param>
        /// <param name="opUser">当前用户</param>
        /// <param name="error_message">错误消息</param>
        public bool Delete(List<string> DocNums, string opUser, out string error_message)
        {
            error_message = "";
            bool bDeleted = true;

            var flag = GetList(d => DocNums.Contains(d.DocNum)).ToList();
            if (flag.Any(x => x.IsPosted == true))
            {
                error_message = "已过帐数据不允许删除";
                return false;
            }
            var mark = _detailApp.GetList(d => DocNums.Contains(d.DocNum)).ToList();

            try
            {
                DbContext.Ado.BeginTran();//开启事务
                _detailApp.DbContext = DbContext;

                //删除主信息
                Delete(flag, opUser);

                //删除明细信息
                _detailApp.Delete(mark, opUser);

                DbContext.Ado.CommitTran();//提交事务
            }
            catch (Exception ex)
            {
                bDeleted = false;
                error_message = ex.Message;
                if (DbContext.Ado.IsAnyTran())//失败回滚
                    DbContext.Ado.RollbackTran();
            }
            return bDeleted;

        }

        #endregion

        #region 过账

        /// <summary>
        /// 过账
        /// </summary>
        /// <param name="entities">主信息</param>
        /// <param name="entitieDetails">明细信息</param>
        /// <param name="opUser">当前过账用户</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool DoPost(List<MM_RedeployApply> entities, List<MM_RedeployApplyDetail> entitieDetails, string opUser, out string error_message)
        {
            error_message = "";

            foreach (var x in entities)
            {
                if (x.IsPosted == true)
                {
                    error_message = "信息已过帐，不允许重复过账";
                    return false;
                }
                else if (x.Status == 0)
                {
                    error_message = "单号["+x.DocNum+"]还未审核，不允许过账";
                    return false;
                }
                else if (x.Status == 3)
                {
                    error_message = "单号[" + x.DocNum + "],请先审核后再过账";
                    return false;
                }
              
            }

            Sys_SAPCompanyInfoApp _SAPCompanyInfoApp = new Sys_SAPCompanyInfoApp();
            MD_StockApp _stockApp = new MD_StockApp();

            #region 库存校验

            //var docnums = entities.Select(x => x.DocNum).Distinct().ToArray();
            //var list1 = _detailApp.GetList(x => docnums.Contains(x.DocNum))?.ToList();
            //foreach (var x in list1)
            //{
            //    string message = "";
            //    if (!string.IsNullOrEmpty(x.SaleNum))
            //    {
            //        //库存校验
            //        bool saleStock = _stockApp.ValidateStockOut(x.ItemCode, x.OutBinLocationCode, x.SaleNum, Convert.ToInt32(x.SaleLine), x.Qty, out message, null);
            //        if (!saleStock)
            //        {
            //            error_message = message;
            //            return false;
            //        }
            //    }
            //    else
            //    {
            //        bool stock = _stockApp.ValidateStockOut(x.ItemCode, x.OutBinLocationCode, x.Qty, out message, null, null);
            //        if (!stock)
            //        {
            //            error_message = message;
            //            return false;
            //        }
            //    }

            //}

            #endregion

     
            try
            {
                //多条数据后根据单号进行分组
                var mainInfoList = entities.GroupBy(g => new
                {
                    g.DocNum
                }).Select(q => new
                {
                    DocNum = q.Key.DocNum
                });

                //按单号分组
                foreach (var mainInfo in mainInfoList)
                {
                    var list = new List<ZFGWMS014>();
                    int Line = 0;
                    var conditionList = new List<MM_RedeployApplyDetail>();
                    if (entitieDetails != null && entitieDetails.Count > 0)//手持端添加信息后自动过账，不需要在查询一次
                    {
                        //查询明细信息
                        conditionList = entitieDetails.Where(x => x.DocNum == mainInfo.DocNum && x.IsPosted == false)?.ToList();
                    }
                    else//PC端过账
                    {
                        conditionList = _detailApp.GetList(x => x.DocNum == mainInfo.DocNum && x.IsPosted == false)?.ToList();
                    }
                    conditionList.ForEach(x =>
                    {
                        Line = Line + 1;
                        x.Line = Line;
                        var y = new ZFGWMS014();
                        y.ZNUM = Line; //condition.BaseLine;领料单行号
                        y.MATNR = x.ItemCode;//
                        y.WERKS = x.FactoryCode ?? "2002";//eq.FactoryCode
                        y.BWART = x.MovementType ?? "311";//移动类型(待定)
                        y.MENGE = Convert.ToDecimal(x.Qty);
                        y.MEINS = x.Unit;
                        y.LGORT = x.OutWhsCode;
                        y.UMLGO =x.InWhsCode;
                        if (!string.IsNullOrEmpty(string.IsNullOrEmpty(x.SaleNum) ? "" : x.SaleNum.Trim()))
                        {
                            y.KDAUF = x.SaleNum;//如果特殊库存等于E，则必填
                            y.KDPOS = Convert.ToInt32(x.SaleLine);//如果特殊库存等于E，则必填
                            y.SOBKZ = x.SpecialStock ?? "E";
                        }
                        else
                        {
                            y.KDAUF = "";
                            y.KDPOS = 0;
                            y.SOBKZ = "";
                        }
                        y.BWTAR = x.AssessType;//如果物料启用分割评估，此字段必填(待定)
                        y.SGTXT = x.Remark;
                        list.Add(y);
                    });

                    bool ispost = false;
                    DateTime time = DateTime.Now;
                    //string rtnErrMsg = "";
                    //purchaseReceipt.CompanyCode 公司代码
                    //查询Sap账期时间
                    DateTime ManualPostTime = new SAPApp().GetSAPpostTime(Convert.ToDateTime(entities[0].ManualPostTime));
                    List<SAPRETURN> saplist = _SAPCompanyInfoApp.ZFGWMS014(conditionList[0].CompanyCode ?? "001", mainInfo.DocNum, "", ManualPostTime, list, out ispost, out error_message);
                    if (saplist != null && saplist.Count > 0)
                    {
                        //List<MM_EquipmentPicking> queryList = new List<MM_EquipmentPicking>();
                        foreach (var sap in saplist)
                        {
                            //&& x.BaseNum == sap.basenum && x.BaseLine == sap.baseline
                            var querydetail = conditionList.Where(x => x.DocNum == mainInfo.DocNum && x.Line == sap.line).ToList().FirstOrDefault();
                            if (querydetail != null)
                            {
                                querydetail.IsPosted = true;
                                querydetail.PostUser = opUser;
                                querydetail.PostTime = time;
                                querydetail.MUser = opUser;
                                querydetail.MTime = time;
                                querydetail.SapDocNum = sap.sapDocNum;
                                querydetail.SapLine = sap.sapline;

                                if (_detailApp.Update(querydetail) > 0)//更新成功后更新库存
                                {
                                    #region 更新库存
                                    //插入普通库存信息
                                    string InstockMsg = string.Empty;
                                    MD_Stock stock = new MD_Stock();
                                    stock.BarCode = querydetail.BarCode ?? "";
                                    stock.BatchNum = "";
                                    stock.WhsCode = querydetail.InWhsCode;
                                    stock.WhsName = querydetail.InWhsName;
                                    stock.RegionCode = querydetail.InRegionCode;
                                    stock.RegionName = querydetail.InRegionName;
                                    stock.BinLocationCode = querydetail.InBinLocationCode;
                                    stock.BinLocationName = querydetail.InBinLocationName;
                                    //stock.BoqueryBarCode = querydetail.BoxBarCode;
                                    stock.ItemCode = querydetail.ItemCode.Trim();
                                    stock.ItemName = querydetail.ItemName.Trim();
                                    stock.ItmsGrpCode = "";
                                    stock.ItmsGrpName = "";
                                    //stock.PTime = querydetail.PTime;
                                    stock.Qty = querydetail.Qty;
                                    stock.SaleNum = querydetail.SaleNum ?? "";
                                    stock.SaleLine = querydetail.SaleLine ?? 0;
                                    if (!string.IsNullOrEmpty(string.IsNullOrEmpty(querydetail.SaleNum) ? "" : querydetail.SaleNum.Trim()))
                                        stock.SpecialStock = "E";//销售库存
                                    stock.Unit = querydetail.Unit;
                                    stock.AssessType = querydetail.AssessType ?? "";


                                    //如果评估类型为空，走正常的库存逻辑
                                    if (string.IsNullOrEmpty(string.IsNullOrEmpty(querydetail.AssessType) ? "" : querydetail.AssessType.Trim()))
                                    {
                                        string stockMsg = string.Empty;
                                        //销售库存出库
                                        if (!string.IsNullOrEmpty(querydetail.BarCode))
                                        {
                                            if (!_stockApp.StockOutByBarCode(querydetail.BarCode, querydetail.OutBinLocationCode.Trim(), querydetail.Qty, opUser, out stockMsg))
                                            {
                                                //error_message = stockMsg;
                                                //return false;
                                            }
                                            else
                                            {

                                                if (!_stockApp.StockIn(stock, opUser, out InstockMsg))
                                                {
                                                    error_message = InstockMsg;
                                                    //return false;
                                                }
                                            }
                                        }
                                        else if (!string.IsNullOrEmpty(string.IsNullOrEmpty(querydetail.SaleNum) ? "" : querydetail.SaleNum.Trim()))
                                        {
                                            if (!_stockApp.StockOut(querydetail.ItemCode.Trim(), querydetail.OutBinLocationCode.Trim(), querydetail.SaleNum.Trim(), Convert.ToInt32(querydetail.SaleLine), querydetail.Qty, opUser, out stockMsg))
                                            {
                                                //error_message = stockMsg;
                                                //return false;
                                            }
                                            else
                                            {
                                         
                                                if (!_stockApp.StockIn(stock, opUser, out InstockMsg))
                                                {
                                                    //error_message = InstockMsg;
                                                    //return false;
                                                }
                                            }

                                        }
                                        else//正常库存出库
                                        {
                                            if (!_stockApp.StockOut(querydetail.ItemCode, querydetail.OutBinLocationCode, querydetail.Qty, opUser, out stockMsg))
                                            {
                                                //error_message = stockMsg;
                                                //return false;
                                            }
                                            else
                                            {
                                             
                                                if (!_stockApp.StockIn(stock, opUser, out InstockMsg))
                                                {
                                                    //error_message = InstockMsg;
                                                    //return false;
                                                }
                                            }
                                        }
                                    }
                                    else//如果评估类型不为空，走评估类型 库存逻辑
                                    {
                                        string stockMsg = string.Empty;
                                        //评估类型-销售库存出库
                                        if (!string.IsNullOrEmpty(string.IsNullOrEmpty(querydetail.SaleNum) ? "" : querydetail.SaleNum.Trim()))
                                        {
                                            if (!_stockApp.StockOutForAssmentType(querydetail.ItemCode.Trim(), querydetail.OutBinLocationCode.Trim(), querydetail.SaleNum.Trim(), Convert.ToInt32(querydetail.SaleLine), querydetail.Qty,querydetail.AssessType, opUser, out stockMsg))
                                            {
                                                //error_message = stockMsg;
                                                //return false;
                                            }
                                            else
                                            {
                                               
                                                if (!_stockApp.StockIn(stock, opUser, out InstockMsg))
                                                {
                                                    //error_message = InstockMsg;
                                                    //return false;
                                                }
                                            }

                                        }
                                        else//评估类型-正常库存出库
                                        {
                                            if (!_stockApp.StockOutForAssmentType(querydetail.ItemCode, querydetail.OutBinLocationCode, querydetail.Qty,querydetail.AssessType, opUser, out stockMsg))
                                            {
                                                //error_message = stockMsg;
                                                //return false;
                                            }
                                            else
                                            {
                                               
                                                if (!_stockApp.StockIn(stock, opUser, out InstockMsg))
                                                {
                                                    //error_message = InstockMsg;
                                                    //return false;
                                                }
                                            }
                                        }
                                    }
                                    #endregion
                                }
                                //queryList.Add(query);
                            }
                        }

                        var query = entities.Where(x => x.DocNum == mainInfo.DocNum).ToList().FirstOrDefault();
                        if (query != null)
                        {
                            query.IsPosted = true;
                            query.PostUser = opUser;
                            query.PostTime = time;
                            query.MUser = opUser;
                            query.MTime = time;
                            query.ManualPostTime = ManualPostTime;
                            query.SAPmark = "S";
                        }
                        //更新
                        Update(query);
                    }
                    else
                    {
                        var query = entities.Where(x => x.DocNum == mainInfo.DocNum).ToList().FirstOrDefault();
                        if (query != null)
                        {
                            query.SAPmark = "E";
                            query.SAPmessage = error_message;
                        }
                        //更新
                        Update(query);

                        error_message = "单号：" + mainInfo.DocNum + "，过账失败，失败原因：" + error_message;
                        return false;
                    }
                }
                if (string.IsNullOrEmpty(error_message))
                {
                    error_message = "过账成功";
                }
                return true;
            }
            catch (Exception ex)
            {
                error_message = ex.Message;
                return false;
            }
        }

        #endregion

        #region 冲销过账

        /// <summary>
        /// 冲销过账
        /// </summary>
        /// <param name="entities">主信息</param>
        /// <param name="entitieDetails">明细信息</param>
        /// <param name="opUser">当前过账用户</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool PassPost(List<MM_RedeployApply> entities, string opUser, out string error_message)
        {
            error_message = "";

            foreach (var x in entities)
            {
                if (x.IsPosted == false)
                {
                    error_message = "信息未过帐，不允许冲销过账";
                    return false;
                }

            }

            Sys_SAPCompanyInfoApp _SAPCompanyInfoApp = new Sys_SAPCompanyInfoApp();
            MD_StockApp _stockApp = new MD_StockApp();

            try
            {

                var DocNums = entities.Select(x => x.DocNum).Distinct().ToArray();
                var conditionList1 =  _detailApp.GetList(x => DocNums.Contains(x.DocNum) && x.IsPosted == true)?.ToList();

                //多条数据后根据单号进行分组
                var mainInfoList = conditionList1.GroupBy(g => new
                {
                    g.SapDocNum
                }).Select(q => new
                {
                    SapDocNum = q.Key.SapDocNum
                });

                //按订单号分组
                foreach (var mainInfo in mainInfoList)
                {
                    int Line = 0;

                    List<string> list = new List<string>();
                    var conditionList = conditionList1.Where(x => x.SapDocNum == mainInfo.SapDocNum)?.ToList();
                    conditionList.ForEach(x =>
                    {
                        list.Add(x.SapLine.ToString());

                    });

                    bool ispost = false;
                    DateTime time = DateTime.Now;
                    DateTime? ManualPostTime1 =entities.Where(x => x.DocNum == conditionList[0].DocNum)?.ToList()[0].ManualPostTime;
                    //查询Sap账期时间
                    DateTime ManualPostTime = new SAPApp().GetSAPpostTime(Convert.ToDateTime(ManualPostTime1));
                    //purchaseReceipt.CompanyCode 公司代码
                    var saplist = _SAPCompanyInfoApp.ZFGWMS021(conditionList[0].CompanyCode ?? "001", mainInfo.SapDocNum, list, ManualPostTime, out ispost, out error_message);
                    if (string.IsNullOrEmpty(error_message))
                    {
                        foreach (var query1 in conditionList)
                        {

                            query1.IsDelete = true;
                            query1.DUser = opUser;
                            query1.DTime = time;
                            query1.Remark = query1.Remark+"已冲销";

                                if (_detailApp.Update(query1) > 0)//更新成功后更新库存
                                {
                                    #region 更新库存
                                    //插入普通库存信息
                                    string InstockMsg = string.Empty;
                                    MD_Stock stock = new MD_Stock();
                                    stock.BarCode = query1.BarCode ?? "";
                                    stock.BatchNum = "";
                                    stock.WhsCode = query1.OutWhsCode;
                                    stock.WhsName = query1.OutWhsName;
                                    stock.RegionCode = query1.OutRegionCode;
                                    stock.RegionName = query1.OutRegionName;
                                    stock.BinLocationCode = query1.OutBinLocationCode;
                                    stock.BinLocationName = query1.OutBinLocationName;
                                    //stock.BoqueryBarCode = querydetail.BoxBarCode;
                                    stock.ItemCode = query1.ItemCode.Trim();
                                    stock.ItemName = query1.ItemName.Trim();
                                    stock.ItmsGrpCode = "";
                                    stock.ItmsGrpName = "";
                                    //stock.PTime = querydetail.PTime;
                                    stock.Qty = query1.Qty;
                                    stock.SaleNum = query1.SaleNum ?? "";
                                    stock.SaleLine = query1.SaleLine ?? 0;
                                    if (!string.IsNullOrEmpty(string.IsNullOrEmpty(query1.SaleNum) ? "" : query1.SaleNum.Trim()))
                                        stock.SpecialStock = "E";//销售库存
                                    stock.Unit = query1.Unit;
                                    stock.AssessType = query1.AssessType ?? "";


                                    //如果评估类型为空，走正常的库存逻辑
                                    if (string.IsNullOrEmpty(string.IsNullOrEmpty(query1.AssessType) ? "" : query1.AssessType.Trim()))
                                    {
                                        string stockMsg = string.Empty;
                                        //销售库存出库
                                        if (!string.IsNullOrEmpty(query1.BarCode))
                                        {
                                            if (!_stockApp.StockOutByBarCode(query1.BarCode, query1.InBinLocationCode.Trim(), query1.Qty, opUser, out stockMsg))
                                            {
                                                error_message = stockMsg;
                                                //return false;
                                            }
                                            else
                                            {

                                                if (!_stockApp.StockIn(stock, opUser, out InstockMsg))
                                                {
                                                    error_message = InstockMsg;
                                                    //return false;
                                                }
                                            }
                                        }
                                        else if (!string.IsNullOrEmpty(string.IsNullOrEmpty(query1.SaleNum) ? "" : query1.SaleNum.Trim()))
                                        {
                                            if (!_stockApp.StockOut(query1.ItemCode.Trim(), query1.InBinLocationCode.Trim(), query1.SaleNum.Trim(), Convert.ToInt32(query1.SaleLine), query1.Qty, opUser, out stockMsg))
                                            {
                                                error_message = stockMsg;
                                                //return false;
                                            }
                                            else
                                            {

                                                if (!_stockApp.StockIn(stock, opUser, out InstockMsg))
                                                {
                                                    error_message = InstockMsg;
                                                    //return false;
                                                }
                                            }

                                        }
                                        else//正常库存出库
                                        {
                                            if (!_stockApp.StockOut(query1.ItemCode, query1.InBinLocationCode, query1.Qty, opUser, out stockMsg))
                                            {
                                                error_message = stockMsg;
                                                //return false;
                                            }
                                            else
                                            {

                                                if (!_stockApp.StockIn(stock, opUser, out InstockMsg))
                                                {
                                                    error_message = InstockMsg;
                                                    //return false;
                                                }
                                            }
                                        }
                                    }
                                    else//如果评估类型不为空，走评估类型 库存逻辑
                                    {
                                        string stockMsg = string.Empty;
                                        //评估类型-销售库存出库
                                        if (!string.IsNullOrEmpty(string.IsNullOrEmpty(query1.SaleNum) ? "" : query1.SaleNum.Trim()))
                                        {
                                            if (!_stockApp.StockOutForAssmentType(query1.ItemCode.Trim(), query1.InBinLocationCode.Trim(), query1.SaleNum.Trim(), Convert.ToInt32(query1.SaleLine), query1.Qty, query1.AssessType, opUser, out stockMsg))
                                            {
                                                error_message = stockMsg;
                                                //return false;
                                            }
                                            else
                                            {

                                                if (!_stockApp.StockIn(stock, opUser, out InstockMsg))
                                                {
                                                    error_message = InstockMsg;
                                                    //return false;
                                                }
                                            }

                                        }
                                        else//评估类型-正常库存出库
                                        {
                                            if (!_stockApp.StockOutForAssmentType(query1.ItemCode, query1.InBinLocationCode, query1.Qty, query1.AssessType, opUser, out stockMsg))
                                            {
                                                error_message = stockMsg;
                                                //return false;
                                            }
                                            else
                                            {

                                                if (!_stockApp.StockIn(stock, opUser, out InstockMsg))
                                                {
                                                    error_message = InstockMsg;
                                                    //return false;
                                                }
                                            }
                                        }
                                    }
                                    #endregion
                                
                                //queryList.Add(query);
                            }
                        }

                        var query = entities.Where(x => x.DocNum == conditionList[0].DocNum).ToList().FirstOrDefault();
                        if (query != null)
                        {
                            query.IsDelete = true;
                            query.DUser = opUser;
                            query.DTime = time;
                            query.SAPmark = "已冲销";
                        }
                        //更新
                        Update(query);
                    }
                    else
                    {
                        //var query = entities.Where(x => x.DocNum == conditionList[0].DocNum).ToList().FirstOrDefault();
                        //if (query != null)
                        //{
                        //    query.SAPmark = "E";
                        //    query.SAPmessage = error_message;
                        //}
                        ////更新
                        //Update(query);

                        error_message = "单号：" + conditionList[0].DocNum + "，冲销过账失败，失败原因：" + error_message;
                        return false;
                    }
                }
                if (string.IsNullOrEmpty(error_message))
                {
                    error_message = "冲销过账成功";
                }
                return true;
            }
            catch (Exception ex)
            {
                error_message = ex.Message;
                return false;
            }
        }

        #endregion

        #region 审核

        /// <summary>
        /// 审核
        /// </summary>
        /// <param name="DocNums">单号数组</param>
        /// <param name="opUser">当前用户</param>
        /// <param name="error_message">错误消息</param>
        /// <param name="type">类型 1:提交失败 2：过账失败</param>
        public bool Audits(string[] DocNums, string opUser, out string error_message, out string type)
        {
            error_message = "";
            type = "";
            bool bDeleted = true;
            Sys_SwithConfigApp _switchApp = new Sys_SwithConfigApp();
            //List<MM_DepRequisition> entities = GetListByKeys(ids);

            var flag = GetList(d => DocNums.Contains(d.DocNum)).ToList();
            foreach (var x in flag)
            {
                if (x.Status == 2)
                {
                    error_message = "[已审核]的信息,请勿重复审核";
                    type = "1";
                    return false;
                }
                //else if (x.Status == 3)
                //{
                //    error_message = "[未审核]的信息，请先进行审核后才可进行取消操作";
                //    type = "1";
                //    return false;
                //}
            }

            #region 库存校验

            //foreach (var x in Parameters.detailed)
            //{
            //    string message = "";
            //    //库存校验
            //    bool result = _stockApp.ValidateStockOut(x.ItemCode, Parameters.BinLocationCode, x.SalesOrderNum, Convert.ToInt32(x.SalesOrderLine), x.ReturnScanQty, out message, null);
            //    bool result = _stockApp.ValidateStockOut(x.ItemCode, Parameters.BinLocationCode, x.ReturnScanQty, out message, null, null);
            //    if (!result)
            //    {
            //        error_message = message;
            //        type = "1";
            //        return false;
            //    }
            //}

            #endregion

            try
            {
                flag.ForEach(x =>
                {
                    x.Status = 2;
                    x.MUser = opUser;
                    x.MTime = DateTime.Now;
                });
                DbContext.Ado.BeginTran();//开启事务

                Update(flag);

                DbContext.Ado.CommitTran();//提交事务

                #region 是否自动过账

                //是否自动过账判定
                if (_switchApp.IsRedeployApplyAutoPost)
                {
                    string postMsg = "";
                    bool bpost = DoPost(flag, null, opUser, out postMsg);
                    if (!bpost)
                    {
                        error_message = "审核成功," + postMsg;
                        type = "2";
                        return false;
                    }
                    else
                    {
                        error_message = "审核并" + postMsg;
                    }
                }

                #endregion

            }
            catch (Exception ex)
            {
                bDeleted = false;
                type = "1";
                error_message = ex.Message;
                if (DbContext.Ado.IsAnyTran())
                    DbContext.Ado.RollbackTran();//失败回滚
            }
            return bDeleted;
        }

        #endregion

        #region 取消

        /// <summary>
        /// 取消
        /// </summary>
        /// <param name="DocNums">单号数组</param>
        /// <param name="opUser">当前用户</param>
        /// <param name="error_message">错误消息</param>
        public bool Rejects(string[] DocNums, string opUser, out string error_message)
        {
            error_message = "";
            bool bRejects = true;

            var flag = GetList(d => DocNums.Contains(d.DocNum)).ToList();
            foreach(var x in flag)
            {
                if (x.Status == 0)
                {
                    error_message = "[未审核]的信息，请先进行审核后才可进行取消操作";
                    return false;
                }
                else if (x.Status == 3)
                {
                    error_message = "[已取消]的信息,请勿重复取消";
                    return false;
                }
            } ;

            try
            {
                flag.ForEach(x =>
                {
                    x.Status = 3;
                    x.MUser = opUser;
                    x.MTime = DateTime.Now;
                });
                DbContext.Ado.BeginTran();//开启事务

                Update(flag);

                DbContext.Ado.CommitTran();//提交事务
            }
            catch (Exception ex)
            {
                bRejects = false;
                error_message = ex.Message;
                if (DbContext.Ado.IsAnyTran())
                    DbContext.Ado.RollbackTran();//失败回滚
            }
            if (string.IsNullOrEmpty(error_message))
                error_message = "取消成功";
            return bRejects;
        }

        /// <summary>
        /// 取消校验
        /// </summary>
        /// <param name="entities"></param>
        /// <param name="error_message"></param>
        /// <returns></returns>
        private bool CheckReject(List<MM_DepRequisition> entities, out string error_message)
        {
            bool isPass = true;
            error_message = "";
            foreach (MM_DepRequisition po in entities)
            {
                if (po.Status == 2)
                {
                    error_message = "请选择状态为[已审核]的信息进行取消"; // "已过账数据不允许删除!";
                    isPass = false;
                    break;
                }
                //    // 入库校验
                //    //if(_stockApp.CheckStockLocked(po.ItemCode, po.OutBinLocationCode, po.OutRegionCode))
                //    //{
                //    //    //throw new Exception("Common.error", new Exception("Common.StockIsLocked"));
                //    //    error_message = "Common.StockIsLocked"; //库存锁定，不允许出入库
                //    //    return false;
                //    //}
            }

            return isPass;
        }


        #endregion

        #region 导入

        /// <summary>
        /// 导入
        /// </summary>
        /// <param name="excelList">接收的集合</param>
        /// <param name="opUser">当前用户</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool ImprotExcelToBaseData(List<MM_RedeployApplyImport> excelList, string opUser, out string error_message)
        {
            error_message = "";

            if (!ValidateCheck(excelList))
            {
                return false;
            }

            var flag = true;
            DateTime date = DateTime.Now;
            BaseApp<BaseEntity> _baseApp = new BaseApp<BaseEntity>();
            String DocNum = _baseApp.GetNewDocNum(DocType.MM, DocFixedNumDef.MM_RedeployApply);
            var query = new MM_RedeployApply();
            var querydetail = new List<MM_RedeployApplyDetail>();

            //主表信息
            query.DocNum = DocNum;
            query.Status = 0;
            query.IsPosted = false;
            query.IsCancel= false;
            query.ManualPostTime = date;
            query.IsDelete = false;
            query.CUser = opUser;
            query.CTime = date;

            //物料
            var ItemCodes = excelList.Select(x => x.物料编码.Trim()).Distinct().ToArray();
            var itemcodes = DbContextForSAP.Queryable<XZ_SAP_MARC>().Where(x => ItemCodes.Contains(x.MATNR))?.ToList();

            //转入仓库编码
            var InWhsCodes = excelList.Select(x => x.转入仓库编码).Distinct().ToArray();
            var inwhscodes = new MD_BinLocationApp().GetList(x => InWhsCodes.Contains(x.WhsCode))?.ToList();

            //转出仓库编码
            var OutWhsCodes = excelList.Select(x => x.转出仓库编码).Distinct().ToArray();
            var outwhscodes = new MD_BinLocationApp().GetList(x => OutWhsCodes.Contains(x.WhsCode))?.ToList();

            //var list = excelList.GroupBy(g => new
            //{
            //    g.物料编码,
            //    g.出厂编号,
            //    g.转出仓库编码,
            //    g.转入仓库编码,
            //    g.销售订单,
            //    g.销售订单项目,
            //    g.备注
            //}).Select(q => new
            //{
            //    物料编码 = q.Key.物料编码,
            //    出厂编号 = q.Key.出厂编号,
            //    转出仓库编码 = q.Key.转出仓库编码,
            //    转入仓库编码 = q.Key.转入仓库编码,
            //    销售订单 = q.Key.销售订单,
            //    销售订单项目 = q.Key.销售订单项目,
            //    备注 = q.Key.备注,
            //    数量 = q.Sum(s => s.数量)
            //}).ToList();
            int i = 0;
            try
            {
                //暂时只进行插入操作，有需要在做更新操作
                foreach (var x in excelList)
                {
                    ////校验是否存在 客户编号+客户件号
                    //var isHaveData = _detailApp.GetFirstEntity(v => v.CustomerCode == item.CustomerCode &&
                    //                                          v.CustomerItemCode == item.CustomerItemCode);
                    i += 1;
                    //明细信息
                    var z = new MM_RedeployApplyDetail();
                    z.DocNum = DocNum;
                    z.Line = i;
                    z.BarCode = x.出厂编号;
                    z.ItemCode = x.物料编码.Trim();
                   // z.ItemName = string.IsNullOrEmpty(x.物料编码) ? "" : itemcodes.Where(v => v.MATNR == x.物料编码)?.ToList().FirstOrDefault().MAKTX; //x.物料名称;
                    z.CompanyCode = "001";
                    z.FactoryCode = "2002";
                    if (x.数量 == 0)
                    {
                        error_message = "数量不能为0!";
                        return false;
                    }
                    z.Qty = x.数量;
                    var item = itemcodes.Where(v => v.MATNR == z.ItemCode)?.ToList().FirstOrDefault();
                    if (item == null)
                    {
                        error_message = "物料编号【" + z.ItemCode + "】无效!";
                        return false;
                    }
                    z.ItemName = item == null ? "" : item.MAKTX;
                    z.Unit = item == null ? "" : item.MEINS;
                    
                    z.OutWhsCode = x.转出仓库编码;
                    var outwhs = outwhscodes.Where(v => v.WhsCode == x.转出仓库编码)?.ToList().FirstOrDefault();
                    z.OutWhsName = outwhs == null ? "" : outwhs.WhsName;
                    z.OutRegionCode = outwhs == null ? "" : outwhs.RegionCode;
                    z.OutRegionName = outwhs == null ? "" : outwhs.RegionName;
                    z.OutBinLocationCode = outwhs == null ? "" : outwhs.BinLocationCode;
                    z.OutBinLocationName = outwhs == null ? "" : outwhs.BinLocationName;

                    z.InWhsCode = x.转入仓库编码;
                    var inwhs = inwhscodes.Where(v => v.WhsCode == x.转入仓库编码)?.ToList().FirstOrDefault();
                    z.InWhsName = inwhs == null ? "" : inwhs.WhsName;
                    z.InRegionCode = inwhs == null ? "" : inwhs.RegionCode;
                    z.InRegionName = inwhs == null ? "" : inwhs.RegionName;
                    z.InBinLocationCode = inwhs == null ? "" : inwhs.BinLocationCode;
                    z.InBinLocationName = inwhs == null ? "" : inwhs.BinLocationName;
                    z.SaleNum= x.销售订单;
                    z.SaleLine = x.销售订单项目;
                    if (!string.IsNullOrEmpty(z.SaleNum))
                        z.SpecialStock = "E";
                    z.Remark = x.备注;
                    z.MovementType = "311";
                    z.SpecialStock = "";
                    z.IsPosted = false;
                    z.IsDelete = false;
                    z.CUser = opUser;
                    z.CTime = date;
                    querydetail.Add(z);
                };


                this.DbContext.Ado.BeginTran();
                _detailApp.DbContext = this.DbContext;

                this.Insert(query);
                _detailApp.Insert(querydetail);

                this.DbContext.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                if (this.DbContext.Ado.IsAnyTran())
                    this.DbContext.Ado.RollbackTran();
                throw ex;
            }
            return flag;
        }

        /// <summary>
        /// 导入校验规则
        /// </summary>
        /// <param name="excelList">导入的集合</param>
        /// <returns></returns>
        public bool ValidateCheck(List<MM_RedeployApplyImport> excelList)
        {
            if (excelList == null || excelList.Count <= 0)
            {
                throw new Exception("没有可以导入的数据");
            }

            SAPApp _SAPApp = new SAPApp();
            MD_BinLocationApp _binApp = new MD_BinLocationApp();
            MD_StockApp _stockApp = new MD_StockApp();

            var flag = true;

            //校验空值
            //校验导入数据 是否符合导入条件 用数据校验 空值/是否存在
            _SAPApp.IsHaveItem(excelList.Select(x => x.物料编码).Distinct().ToArray());//物料信息
            //_SAPApp.IsHaveItemName(excelList.Select(x => x.物料名称).Distinct().ToArray());//物料名称信息
            _binApp.IsHaveWhsCode(excelList.Select(x => x.转出仓库编码).Distinct().ToArray(), "outWhsCode");//仓库
            _binApp.IsHaveWhsCode(excelList.Select(x => x.转入仓库编码).Distinct().ToArray(), "inWhsCode");

            excelList.ForEach(x =>
            {
                if (x.数量 <= 0)
                {
                    throw new Exception(string.Format("导入数据中[物料编号:{0},转入仓库编码:{1},转出仓库编码{2}]的数量值需大于零！", x.物料编码, x.转入仓库编码, x.转出仓库编码));
                }
                if (x.转入仓库编码 == x.转出仓库编码)
                {
                    throw new Exception(string.Format("导入数据中[物料编号:{0},转入仓库编码:{1},转出仓库编码{2}]不能为同一仓库！", x.物料编码, x.转入仓库编码, x.转出仓库编码));
                }

                //暂时废弃-校验物料名称
                //var item=_SAPApp.DbContextForSAP.Queryable<XZ_SAP_MARC>().Where(v => v.MAKTX == x.物料名称 && v.Status == false && v.LVORA == "" && v.LVORM == "")?.ToList().FirstOrDefault();
                //if (item == null)
                //{
                //    throw new Exception(string.Format("导入数据中[物料名称信息:{0}]在系统中不存在！", x.物料名称));
                //}

                //先导入，后处理库存不足问题
                //var stock = _stockApp.GetList(v => v.ItemCode == x.物料编码 && v.WhsCode == x.转出仓库编码).ToList().FirstOrDefault();
                //if (stock == null)
                //{
                //    throw new Exception(string.Format("导入数据中[物料编号:{0},转出仓库编码{1},在系统中没有库存信息]！", x.物料编码, x.转出仓库编码));
                //}
                //else
                //{
                //    if (stock.Qty < x.数量)
                //    {
                //        throw new Exception(string.Format("导入数据中[物料编号:{0},转出仓库编码{1},在系统中的库存数量不足]！", x.物料编码, x.转出仓库编码));
                //    }
                //}
            });

            return flag;
        }

        #endregion

        #region 作废

        /// <summary>
        /// 作废
        /// </summary>
        /// <param name="DocNums">单号数组</param>
        /// <param name="opUser">当前用户</param>
        /// <param name="error_message">错误消息</param>
        public bool Cancel(string[] DocNums, string opUser, out string error_message)
        {
            error_message = "";


            var flag = GetList(d => DocNums.Contains(d.DocNum)).ToList();
            if (flag.Any(x => x.IsPosted == true))
            {
                error_message = "请选择未过账数据进行作废";
                return false;
            }
            try
            {
                flag.ForEach(x =>
                {
                    x.IsCancel = true;
                    x.MUser = opUser;
                    x.MTime = DateTime.Now;
                });
                DbContext.Ado.BeginTran();

                Update(flag);

                DbContext.Ado.CommitTran();//提交事务

                if (string.IsNullOrEmpty(error_message))
                    error_message = "作废成功";
                return true;
            }
            catch (Exception ex)
            {
                if (DbContext.Ado.IsAnyTran())
                    DbContext.Ado.RollbackTran();//失败回滚
                error_message = ex.Message;
                return false;
            }
        }

        #endregion

        #region 查询导出数据信息

        /// <summary>
        /// 查询导出数据信息
        /// </summary>
        /// <param name="page"></param>
        /// <param name="condition"></param>
        /// <returns></returns>
        public ISugarQueryable<MM_RedeployApplyExport_View> GetExportView(Expression<Func<MM_RedeployApplyExport_View, bool>> condition)
        {
            var query = DbContext.Queryable<MM_RedeployApplyExport_View>()
                .Where(condition)
                .OrderBy(t => t.DocNum,OrderByType.Desc)
                .OrderBy(t => t.Line);
            return query;
        }

        #endregion

    }
}
