using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.QM.Parameters
{
    /// <summary>
    /// 接收参数的类视图
    /// </summary>
    public class QM_InspectionDto
    {
        /// <summary> 
        /// 采购入库检验主键ID
        /// </summary> 
        [SugarColumn(IsPrimaryKey = true)]
        public string InspectionID { get; set; }

        /// <summary> 
        /// 检验单号
        /// </summary> 
        public string InspectionNum { get; set; }

        /// <summary> 
        /// 检验单行号
        /// </summary> 
        public int? InspectionLine { get; set; }

        /// <summary> 
        /// wms单号
        /// </summary> 
        public string DocNum { get; set; }

        /// <summary> 
        /// wms行号
        /// </summary> 
        public int? Line { get; set; }

        /// <summary> 
        /// 检验申请编号
        /// </summary> 
        public string BaseEntry { get; set; }

        /// <summary> 
        /// 单号
        /// </summary> 
        public string BaseNum { get; set; }

        /// <summary> 
        /// 行号
        /// </summary> 
        public int? BaseLine { get; set; }

        /// <summary> 
        /// 单号
        /// </summary> 
        public string BaseType { get; set; }

        /// <summary> 
        /// 批次
        /// </summary> 
        public string Batch { get; set; }

        /// <summary> 
        /// 条码
        /// </summary> 
        public string BarCode { get; set; }

        /// <summary> 
        /// 检验项判定
        /// </summary> 
        public int? InspectionItem { get; set; }

        /// <summary>
        /// 报检数量
        /// </summary>
        public decimal? InspectionQty { get; set; }

        /// <summary> 
        /// 报检日期
        /// </summary> 
        public DateTime? InspectionTime { get; set; }


        /// <summary>
        /// 仓库编号
        /// </summary>
        public string WhsCode { get; set; }

        /// <summary>
        /// 仓库名称
        /// </summary>
        public string WhsName { get; set; }

        /// <summary>
        /// 区域编号
        /// </summary>
        public string RegionCode { get; set; }

        /// <summary>
        /// 区域名称
        /// </summary>
        public string RegionName { get; set; }

        /// <summary>
        /// 库位编号
        /// </summary>
        public string BinLocationCode { get; set; }

        /// <summary>
        /// 库位名称
        /// </summary>
        public string BinLocationName { get; set; }

        /// <summary> 
        /// 物料编号
        /// </summary> 
        public string ItemCode { get; set; }

        /// <summary> 
        /// 物料名称
        /// </summary> 
        public string ItemName { get; set; }

        /// <summary> 
        /// 单位
        /// </summary> 
        public string Unit { get; set; }

        /// <summary> 
        /// 采购数量
        /// </summary> 
        public decimal? PurchaseQty { get; set; }

        /// <summary> 
        /// 合格数量
        /// </summary> 
        public decimal? QualifiedQty { get; set; }

        /// <summary> 
        /// 不合格数量
        /// </summary> 
        public decimal? UnqualifiedQty { get; set; }

        /// <summary> 
        /// 供应商编号
        /// </summary> 
        public string SupplierCode { get; set; }

        /// <summary> 
        /// 供应商名称
        /// </summary> 
        public string SupplierName { get; set; }

        /// <summary> 
        /// 是否过账
        /// </summary> 
        public bool? IsPosted { get; set; }

        /// <summary> 
        /// 过账人
        /// </summary> 
        public string PostUser { get; set; }

        /// <summary> 
        /// 过账时间
        /// </summary> 
        public DateTime? PostTime { get; set; }

        /// <summary>
        /// 销售订单号
        /// </summary>
        public string SalesOrderNum { get; set; }

        /// <summary>
        /// 销售订单行号
        /// </summary>
        public int? SalesOrderLine { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }

        /// <summary>
        /// 采购订单交期
        /// </summary>
        public DateTime? DeliveryDate { get; set; }

    }
}
