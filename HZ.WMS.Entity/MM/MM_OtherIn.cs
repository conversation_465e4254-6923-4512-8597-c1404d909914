using SqlSugar;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.MM
{
    /// <summary>
    /// 其他入库
    /// </summary>
    [SugarTable("MM_OtherIn")]
    public class MM_OtherIn : BaseEntity
    {
        /// <summary>
        /// 主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Description("ID")]
        public string DepRequisitionID { get;set; }

        /// <summary>
        /// 领料单号
        /// </summary>
        [Description("入库单号")]
        public string DocNum { get; set; }


        /// <summary>
        /// 办理人员编号
        /// </summary>
        [Description("办理人员编号")]
        public string HandlenCode { get; set; }

        /// <summary>
        /// 办理人员
        /// </summary>
        [Description("办理人员")]
        public string HandlenName { get; set; }

        /// <summary> 
        /// 是否过账
        /// </summary> 
        [Description("是否过账")]
        public bool? IsPosted { get; set; }

        /// <summary>
        /// 过账人
        /// </summary>
        [Description("过账人")]
        public string PostUser { get; set; }

        /// <summary>
        /// 过账日期
        /// </summary>
        [Description("过账日期")]
        public DateTime? PostTime { get; set; }

        /// <summary>
        /// 手动过账日期
        /// </summary>
        [Description("手动过账日期")]
        public DateTime? ManualPostTime { get; set; }

        /// <summary>
        /// 状态 0:未审核 1：审核中 2：已审核 3：已取消
        /// </summary>
        [Description("状态")]
        public int? Status { get; set; }
        /// <summary> 
        /// 是否作废
        /// </summary> 
        [Description("是否作废")]
        public bool? IsCancel { get; set; }
        /// <summary>
        /// SAP过账标示
        /// </summary>
        [Description("SAP过账标示")]
        public string SAPmark { get; set; }

        /// <summary>
        /// SAP过账消息
        /// </summary>
        [Description("SAP过账消息")]
        public string SAPmessage { get; set; }


        /// <summary>
        /// 移动类型
        /// </summary>
        [Description("移动类型")]
        public string MovementType { get; set; }

        /// <summary>
        /// 移动类型名称
        /// </summary>
        [Description("移动类型名称")]
        public string MovementTypeName { get; set; }

        /// <summary>
        /// 报废单号
        /// </summary>
        [Description("报废单号")]
        public string ScrapNum { get; set; }

    }
}
