using System;
using System.Collections.Generic;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.MD.Import;

namespace HZ.WMS.Application.MD
{
    /// <summary>
    /// 打印参数模板管理
    /// </summary>
    public class MD_PrintTemplateParameterApp : BaseApp<MD_PrintTemplateParameter>
    {
        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public MD_PrintTemplateParameterApp() : base() { }

        #endregion

        #region 导入

        /// <summary>
        /// 导入
        /// </summary>
        /// <param name="excelList">接收的集合</param>
        /// <param name="opUser">当前用户</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool ImportExcelToBaseData(List<MD_PrintTemplateParameterImport> excelList, string opUser, out string error_message)
        {
            error_message = "";
            var flag = true;
            DateTime date = DateTime.Now;
            var entityList = new List<MD_PrintTemplateParameter>();

            try
            {
                foreach (var item in excelList)
                {
                    if (string.IsNullOrEmpty(item.TemplateName))
                    {
                        error_message = "模板名称不能为空";
                        return false;
                    }

                    // 检查模板名称是否重复
                    var existTemplate = GetFirstEntityByFieldValue("TemplateName", item.TemplateName);
                    if (existTemplate != null)
                    {
                        error_message = $"模板名称 '{item.TemplateName}' 已存在";
                        return false;
                    }

                    var entity = new MD_PrintTemplateParameter
                    {
                        Id = Guid.NewGuid().ToString(),
                        TemplateName = item.TemplateName,
                        ParameterJson = item.ParameterJson,
                        Enable = item.Enable,
                        TemplateKey = item.TemplateKey,
                        Remark = item.Remark,
                        CUser = opUser,
                        CTime = date,
                        IsDelete = false
                    };

                    entityList.Add(entity);
                }

                if (entityList.Count > 0)
                {
                    Insert(entityList);
                }

                return flag;
            }
            catch (Exception ex)
            {
                error_message = ex.Message;
                return false;
            }
        }

        #endregion
    }
}
