using System;
using System.Collections.Generic;
using System.Linq;
using AOS.WMS.Application.Store;
using AOS.WMS.Entity.Sys;
using HZ.WMS.Application.MD;
using HZ.WMS.Entity;
using HZ.WMS.Entity.MM;
using HZ.WMS.Entity.MM.ViewModel;
using HZ.WMS.Entity.SAP;

namespace HZ.WMS.Application.MM
{
    /// <summary>
    /// 设备领料
    /// </summary>
    public class MM_StockTodoPickingApp : BaseApp<MM_StockTodoPicking>
    {
        #region 初始化

        BaseApp<BaseEntity> _baseApp = new BaseApp<BaseEntity>();
        MD_StockApp _stockApp = new MD_StockApp();
        MM_StockTodoApp _stockTodoApp = new MM_StockTodoApp();

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public MM_StockTodoPickingApp() : base()
        {
        }

        #endregion

        #region PC

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="opUser"></param>
        /// <param name="error_message"></param>
        public bool Deletes(string[] ids, string opUser, out string error_message)
        {
            bool bDeleted = true;
            var entities = GetListByKeys(ids);
            if (CheckDelete(entities, out error_message))
            {
                try
                {
                    DbContext.Ado.BeginTran();
                    Delete(entities, opUser);
                    DbContext.Ado.CommitTran(); //提交事务
                }
                catch (Exception ex)
                {
                    bDeleted = false;
                    error_message = ex.Message;
                    if (DbContext.Ado.IsAnyTran()) //如果事务开启，执行下部操作
                        DbContext.Ado.RollbackTran(); //失败回滚
                }
            }
            else
            {
                bDeleted = false;
            }

            return bDeleted;
        }

        /// <summary>
        /// 删除校验
        /// </summary>
        /// <param name="entities"></param>
        /// <param name="error_message"></param>
        /// <returns></returns>
        private bool CheckDelete(List<MM_StockTodoPicking> entities, out string error_message)
        {
            bool isPass = true;
            error_message = "";
            foreach (MM_StockTodoPicking po in entities)
            {
                if (po.AuditStatus == 2)
                {
                    error_message = "已审核数据不允许删除";
                    isPass = false;
                    break;
                }
            }

            return isPass;
        }

        #endregion

        #region 审核

        /// <summary>
        /// 审核
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="opUser"></param>
        /// <param name="error_message"></param>
        public bool Audits(string[] ids, string opUser, out string error_message, out string type)
        {
            error_message = "";
            type = "";
            var entities = GetListByKeys(ids);
            foreach (var x in entities)
            {
                if (x.AuditStatus == 2) //通过
                {
                    error_message = "请选择状态为“待审核”的信息进行审核";
                    type = "1";
                    return false;
                }
            }
            try
            {
                foreach (var x in entities)
                {
                    x.AuditStatus = 2; // 1:未复合 2：通过 3：取消
                    x.MUser = opUser;
                    x.MTime = DateTime.Now;
                }
                DbContext.Ado.BeginTran();
                Update(entities);
                foreach (var stockTodoPicking in entities)
                {
                    MM_StockTodoDetail stockTodoDetail = new MM_StockTodoDetail();
                    stockTodoDetail.ItemCode = stockTodoPicking.ItemCode;
                    stockTodoDetail.ItemName = stockTodoPicking.ItemName;
                    stockTodoDetail.OperateNo = stockTodoPicking.Quantity;
                    stockTodoDetail.Operator = opUser;
                    stockTodoDetail.OperateType = 2;
                    stockTodoDetail.OperateTime = DateTime.Now;
                    _stockTodoApp.Warehousing(stockTodoDetail, opUser);
                }
                DbContext.Ado.CommitTran();
                if (string.IsNullOrEmpty(error_message))
                    error_message = "审核成功";
                return true;
            }
            catch (Exception ex)
            {
                if (DbContext.Ado.IsAnyTran())
                    DbContext.Ado.RollbackTran(); //失败回滚
                error_message = ex.Message;
                type = "1";
                return false;
            }
        }

        #endregion

        #region 取消

        /// <summary>
        /// 取消
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="opUser"></param>
        /// <param name="error_message"></param>
        public bool Rejects(string[] ids, string opUser, out string error_message)
        {
            error_message = "";

            var entities = GetListByKeys(ids);
            foreach (var x in entities)
            {
                if (x.AuditStatus != 2) //
                {
                    error_message = "请选择审核状态为“通过”的信息进行驳回"; // "已过账数据不允许删除!";
                    return false;
                }
            }

            try
            {
                foreach (MM_StockTodoPicking mq in entities)
                {
                    mq.AuditStatus = 3; //1：待审核，2：已审核，3：取消
                    mq.MUser = opUser;
                    mq.MTime = DateTime.Now;
                }

                DbContext.Ado.BeginTran();
                Update(entities);
                foreach (var stockTodoPicking in entities)
                {
                    MM_StockTodoDetail stockTodoDetail = new MM_StockTodoDetail();
                    stockTodoDetail.ItemCode = stockTodoPicking.ItemCode;
                    stockTodoDetail.ItemName = stockTodoPicking.ItemName;
                    stockTodoDetail.OperateNo = stockTodoPicking.Quantity;
                    stockTodoDetail.Operator = opUser;
                    stockTodoDetail.OperateType = 1;
                    stockTodoDetail.OperateTime = DateTime.Now;
                    _stockTodoApp.Warehousing(stockTodoDetail, opUser);
                }
                DbContext.Ado.CommitTran(); //提交事务

                if (string.IsNullOrEmpty(error_message))
                    error_message = "取消成功";
                return true;
            }
            catch (Exception ex)
            {
                if (DbContext.Ado.IsAnyTran())
                    DbContext.Ado.RollbackTran(); //失败回滚
                error_message = ex.Message;
                return false;
            }
        }

        /// <summary>
        /// 驳回校验
        /// 
        /// </summary>
        /// <param name="entities"></param>
        /// <param name="error_message"></param>
        /// <returns></returns>
        private bool CheckReject(List<MM_EquipmentPicking> entities, out string error_message)
        {
            bool isPass = true;
            error_message = "";
            foreach (MM_EquipmentPicking po in entities)
            {
                if (po.EquipmentPickingStatus == "1") //1:未复合 2：通过 3：驳回
                {
                    error_message = "请选择状态为“通过”的信息进行驳回"; // "已过账数据不允许删除!";
                    isPass = false;
                    break;
                }
            }

            return isPass;
        }

        #endregion

        #endregion

        #region Mobile

        #region 校验序列号、库存

        /// <summary>
        /// 校验序列号、库存
        /// </summary>
        /// <param name="ItemCode">物料编号</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool CheckDate(string ItemCode, out string error_message)
        {
            error_message = "";
            var query = _stockApp.GetList(x =>
                x.ItemCode == ItemCode && (x.SaleNum == "" || x.SaleNum == null) &&
                (x.SupplierCode == "" || x.SupplierCode == null) && x.Qty > 0).ToList().FirstOrDefault();
            if (query == null)
            {
                error_message = "物料编号[" + ItemCode + "]没有库存";
                return false;
            }

            return true;
        }

        #endregion

        #region 查询库存信息

        /// <summary>
        /// 查询库存信息
        /// </summary>
        /// <param name="ItemCode">物料编号</param>
        /// <returns></returns>
        public MD_StockForEquipmentPicking_View GetStockInfo(string ItemCode, out string error_message)
        {
            error_message = "";
            var query = DbContext.Queryable<MD_StockForEquipmentPicking_View>().Where(x => x.ItemCode == ItemCode)
                .ToList().FirstOrDefault();
            if (query == null)
            {
                error_message = "物料编号[" + ItemCode + "],库存数量不足或者检查设备领料信息是否存在未过账记录";
            }

            return query;
        }

        #endregion

        #region 保存

        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="user">用户信息</param>
        /// <param name="parameters">错误消息返回</param>
        /// <param name="message">手动过账时间</param>
        /// <param name="docNum">类型 1:提交失败 2：过账失败</param>
        public bool Save(List<MM_StockTodoPicking> parameters, string user, out string message, out string docNum)
        {
            message = "提交成功";
            docNum = "";
            //type = "";
            try
            {
                docNum = _baseApp.GetNewDocNum(DocType.MM, DocFixedNumDef.MM_EquipmentPicking);
                // 编码维护校验
                var itemCodeArr = parameters.Select(x => x.ItemCode.Trim()).Distinct().ToArray();
                var itemCodes = DbContextForSAP.Queryable<XZ_SAP_MARC>().Where(x => itemCodeArr.Contains(x.MATNR)).ToList();
                foreach (MM_StockTodoPicking x in parameters)
                {
                    x.AuditStatus = 1; // 审核状态 1：待审核，2：已审核，3：取消
                    if (string.IsNullOrEmpty(x.DocNum))
                        x.DocNum = docNum;
                    x.IsDelete = false;
                    x.CUser = user;
                    x.CTime = DateTime.Now;
                    x.ItemCode = x.ItemCode.Trim();

                    if (string.IsNullOrEmpty(x.ItemCode))
                    {
                        message = "物料编号【" + x.ItemCode + "】无效!";
                        return false;
                    }

                    if (string.IsNullOrEmpty(x.ItemName))
                    {
                        var item = itemCodes.Where(v => v.MATNR == x.ItemCode)?.FirstOrDefault();
                        if (item == null)
                        {
                            message = "物料编号【" + x.ItemCode + "】无效!";
                            return false;
                        }

                        x.ItemName = item.MAKTX;
                    }
                }

                DbContext.Ado.BeginTran(); //开启事务
                //批量插入
                Insert(parameters);
                DbContext.Ado.CommitTran(); //提交事务
                return true;
            }
            catch (Exception ex)
            {
                if (DbContext.Ado.IsAnyTran())
                    DbContext.Ado.RollbackTran(); //失败回滚
                message = ex.Message;
                return false;
            }
        }

        #endregion

        #endregion
    }
}