using SqlSugar;
using HZ.WMS.Entity.MM;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Application.MM
{
    /// <summary>
    /// 部门领料单详情
    /// </summary>
    public class MM_DepReqDetailedApp : BaseApp<MM_DepReqDetailed>
    {
        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public MM_DepReqDetailedApp() : base(){}

        #endregion

        #region 更新

        /// <summary>
        /// 
        /// </summary>
        public int Update(List<MM_DepReqDetailed> addlist, List<MM_DepReqDetailed> deletelist, List<MM_DepReqDetailed> updatelist)
        {
            int iCount = 0;
            try
            {
                DbContext.Ado.BeginTran();
                if (addlist != null && addlist.Count > 0) base.Insert(addlist);
                if (deletelist != null && deletelist.Count > 0) base.Delete(deletelist);
                if (updatelist != null && updatelist.Count > 0) base.Update(updatelist);
                iCount = 1;
                DbContext.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                DbContext.Ado.RollbackTran();
                iCount = 0;
                throw ex; 
            }
            return iCount;
        }

        #endregion

        #region SAP查物料

        /// <summary>
        /// 
        /// </summary>
        public IEnumerable<MM_DepReqDetailed> GetMaterialList()
        {
            try
            {
                string sqlStr = "select WERKS FactoryCode,MATNR ItemCode,MAKTX ItemName,MEINS Unit from XZ_SAP.dbo.XZ_SAP_MARC ";
                return DbContext.Ado.SqlQuery<MM_DepReqDetailed>(sqlStr);
            }
            catch (Exception ex)
            {

                throw ex;
            }
        }

        #endregion
    }
}
