using SqlSugar;
using HZ.Core.Http;
using HZ.WMS.Application.MD;
using HZ.WMS.Entity.MM;
using HZ.WMS.Entity.MM.ViewModel;
using HZ.WMS.Entity.SAP;
using HZ.WMS.Entity.SAP.View;
using HZ.WMS.Entity.Sys;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HZ.WMS.Application.MM
{
    /// <summary>
    /// 委外领料申请单
    /// 没有out_message消息的函数，意味着只在最前端做未处理异常捕捉
    /// </summary>
    public class MM_PickingApplyApp : BaseApp<MM_PickingApply>
    {
        #region 初始化

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public MM_PickingApplyApp() : base(){}

        MM_PickingApplyDetailApp detail_app = new MM_PickingApplyDetailApp();

        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="DocNums"></param>
        /// <param name="opUser"></param>
        /// <param name="error_message"></param>
        public bool Deletes(string[] DocNums, string opUser, out string error_message)
        {
            error_message = "";
            bool bDeleted = true;

            var mslist = new List<MM_PickingApply>();
            var msDetailList = new List<MM_PickingApplyDetail>();
            foreach (string key in DocNums)
            {
                var item=GetList(x=>(string.IsNullOrEmpty(key) || x.DocNum.Contains(key) )).ToList();
                //MM_SubcontractingApplication item = DbContext.Queryable<MM_SubcontractingApplication>().
                //                                    Where(t => !t.IsDelete && t.DocNum==key.ToString()).ToList().FirstOrDefault();
                if (item != null)
                {
                    mslist.AddRange(item);
                }
                var itemdetail = detail_app.GetList(x => (string.IsNullOrEmpty(key) || x.DocNum.Contains(key))).ToList();
                //MM_SubcontractingApplicationDetail itemdetail = DbContext.Queryable<MM_SubcontractingApplicationDetail>().
                //                                   Where(t => !t.IsDelete && t.DocNum == key.ToString()).ToList().FirstOrDefault();
                if (itemdetail != null)
                {
                    msDetailList.AddRange(itemdetail);
                }
            }
            //List<MM_SubcontractingApplication> entities = GetListByKeys(ids);

            //List<MM_SubcontractingApplicationDetail> msDetailList = new List<MM_SubcontractingApplicationDetail>();
            //foreach (MM_SubcontractingApplication ms in entities)
            //{
            //    MM_SubcontractingApplicationDetail msdetail = new MM_SubcontractingApplicationDetail();
            //    msdetail = ms;
            //}
            //if (CheckDelete(mslist, out error_message)) //删除校验
            //{
                try
                {
                    DbContext.Ado.BeginTran();
                    detail_app.DbContext = this.DbContext;

                    //删除主表信息
                    Delete(mslist, opUser);

                    //删除明细表信息
                    detail_app.Delete(msDetailList);

                    DbContext.Ado.CommitTran();//提交事务
                }
                catch (Exception ex)
                {
                    bDeleted = false;
                    error_message = ex.Message;

                    if(DbContext.Ado.IsAnyTran())
                        DbContext.Ado.RollbackTran();//失败回滚
                }
            //}
            //else
            //{
            //    bDeleted = false;
            //}
            return bDeleted;
        }

        /// <summary>
        /// 删除校验
        /// 
        /// </summary>
        /// <param name="Type">类型</param>
        /// <param name="keyword">参数</param>
        /// <param name="DocNums"></param>
        /// <param name="error_message"></param>
        /// <returns></returns>
        public bool CheckDeleteAndUpdate(string Type,string[] DocNums , out string error_message,out string[] strArray)
        {
            bool isPass = true;
            error_message = "";
            MM_DispatchApp od_app = new MM_DispatchApp();
            strArray = new string[] { };
            if (Type == "update")
            {
                string DocNum = DocNums[0];
                var itemsData = detail_app.GetList(t => (string.IsNullOrEmpty(DocNum)
                        || t.DocNum.Contains(DocNum)
                        || t.ItemCode.Contains(DocNum) || t.SupplierCode.Contains(DocNum)
                        || t.SupplierName.Contains(DocNum)
                        || t.ItemName.Contains(DocNum)
                      )).ToList();

                //校验编辑时所选择的明细信息是否已经发料
                var odList = new List<MM_Dispatch>();
                foreach (var ms in itemsData)
                {
                    //var query=od_app.GetList(x=>(x.SubcontractingApplicationDetailID==ms.DetailID)).ToList();
                    odList.AddRange(od_app.GetList(x => (x.SubcontractingApplicationDetailID == ms.DetailID)).ToList());
                }
                if (odList.Count > 0)
                {
                    error_message = "所选择的信息中包含已发料的信息，请重新选择";
                    isPass = false;
                }
            }
            if(Type == "delete")
            {
                //校验所选择的单号是否已经发料
                var odList = new List<MM_Dispatch>();
                List<string> strList = new List<string>();
                foreach (string key in DocNums)
                {
                    var query=od_app.GetList(x => (x.SubcontractingApplicationNum == key)).ToList();
                    if (query.Count > 0)
                    {
                        strList.Add(key);
                    }
                    odList.AddRange(query);
                    
                }
                strArray = strList.ToArray();//strArray=[str0,str1,str2]
                if (odList.Count > 0)
                {
                    error_message = "所删除的信息中包含已发料的信息，请重新选择信息";
                    isPass = false;
                }
            }

            //foreach (MM_SubcontractingApplication po in entities)
            //{
            //    if (po.IsPosted == true)
            //    {
            //        error_message = "ui.Message.PODeletePostingWarning"; // "已过账数据不允许删除!";
            //        isPass = false;
            //        break;
            //    }

            //    // 入库校验
            //    //if(_stockApp.CheckStockLocked(po.ItemCode, po.OutBinLocationCode, po.OutRegionCode))
            //    //{
            //    //    //throw new Exception("Common.error", new Exception("Common.StockIsLocked"));
            //    //    error_message = "Common.StockIsLocked"; //库存锁定，不允许出入库
            //    //    return false;
            //    //}
            //}

            return isPass;
        }

        /// <summary>
        /// 删除校验
        /// 
        /// </summary>
        /// <param name="DocNums"></param>
        /// <param name="error_message"></param>
        /// <param name="strArray">数组</param>
        /// <returns></returns>
        public bool CheckDelete(string[] DocNums, out string error_message, out string[] strArray)
        {
            bool isPass = true;
            error_message = "";
            MM_DispatchApp od_app = new MM_DispatchApp();
            strArray = new string[] { };
            //校验所选择的单号是否已经发料
            var odList = new List<MM_Dispatch>();
            List<string> strList = new List<string>();
            foreach (string key in DocNums)
            {
                var query = od_app.GetList(x => (x.SubcontractingApplicationNum == key)).ToList();
                if (query.Count > 0)
                {
                    strList.Add(key);
                }
                odList.AddRange(query);

            }
            strArray = strList.ToArray();//strArray=[str0,str1,str2]
            if (odList.Count > 0)
            {
                error_message = "所删除的信息中包含已发料的信息，请重新选择信息";
                isPass = false;
            }

            //foreach (MM_SubcontractingApplication po in entities)
            //{
            //    if (po.IsPosted == true)
            //    {
            //        error_message = "ui.Message.PODeletePostingWarning"; // "已过账数据不允许删除!";
            //        isPass = false;
            //        break;
            //    }

            //    // 入库校验
            //    //if(_stockApp.CheckStockLocked(po.ItemCode, po.OutBinLocationCode, po.OutRegionCode))
            //    //{
            //    //    //throw new Exception("Common.error", new Exception("Common.StockIsLocked"));
            //    //    error_message = "Common.StockIsLocked"; //库存锁定，不允许出入库
            //    //    return false;
            //    //}
            //}

            return isPass;
        }


        #endregion

        #region 明细删除-废弃

        /// <summary>
        /// 明细删除
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="opUser"></param>
        /// <param name="error_message"></param>
        public bool DeletesDetail(string[] ids, string opUser, out string error_message)
        {
            bool bDeleted = true;
            error_message="";
            List<MM_PickingApplyDetail> entities = detail_app.GetListByKeys(ids);
            //if (CheckDelete(entities, out error_message))
            //{
                try
                {
                    DbContext.Ado.BeginTran();

                    detail_app.Delete(entities, opUser);

                    DbContext.Ado.CommitTran();//提交事务
                }
                catch (Exception ex)
                {
                    bDeleted = false;
                    error_message = ex.Message;
                    DbContext.Ado.RollbackTran();//失败回滚
                }
            //}
            //else
            //{
            //    bDeleted = false;
            //}
            return bDeleted;
        }

        /// <summary>
        /// 删除校验
        /// 
        /// </summary>
        /// <param name="entities"></param>
        /// <param name="error_message"></param>
        /// <returns></returns>
        //private bool CheckDelete(List<MM_SubcontractingApplication> entities, out string error_message)
        //{
        //    bool isPass = true;
        //    error_message = "";
        //    //foreach (MM_SubcontractingApplication po in entities)
        //    //{
        //    //    if (po.IsPosted == true)
        //    //    {
        //    //        error_message = "ui.Message.PODeletePostingWarning"; // "已过账数据不允许删除!";
        //    //        isPass = false;
        //    //        break;
        //    //    }

        //    //    // 入库校验
        //    //    //if(_stockApp.CheckStockLocked(po.ItemCode, po.OutBinLocationCode, po.OutRegionCode))
        //    //    //{
        //    //    //    //throw new Exception("Common.error", new Exception("Common.StockIsLocked"));
        //    //    //    error_message = "Common.StockIsLocked"; //库存锁定，不允许出入库
        //    //    //    return false;
        //    //    //}
        //    //}

        //    return isPass;
        //}


        #endregion

        #region 保存

        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="DocNum">单据号</param>
        /// <param name="Remark">备注</param>
        /// <param name="entities">添加明细</param>
        /// <param name="user">用户</param>
        /// <param name="error_message">错误信息</param>
        /// <returns></returns>
        public bool Save(string DocNum ,string Remark, List<MM_PickingApplyDetail> entities, string user, out string error_message)
        {
            error_message = "";
            DateTime time = DateTime.Now;
            try
            {
                //detailed.Where((x, i) => detailed.FindIndex(z => z.DocNum == x.DocNum) == i) 去除某一列重复值
                //detailed.Distinct() 去重
                var query = new MM_PickingApply
                {
                    DocNum = DocNum,
                    IsDelete = false,
                    CUser = user,
                    CTime = time,
                    Remark = Remark,
                    Status= 0//0:未开始 1:进行中 2：已完成
                };

                entities.ForEach(x=>
                {
                    x.DocNum = DocNum;
                    x.IsDelete = false;
                    x.CUser = user;
                    x.CTime = time;
                    x.Status = 0; //0:未开始 1:进行中 2：已完成
                });

                DbContext.Ado.BeginTran();
                detail_app.DbContext = this.DbContext;

                //主表插入
                this.Insert(query);

                //明细表插入
                detail_app.Insert(entities);

                DbContext.Ado.CommitTran();

                return true;
            }
            catch (Exception ex)
            {
                if(DbContext.Ado.IsAnyTran())
                    DbContext.Ado.RollbackTran();

                error_message = ex.Message;
                return false;
            }
        }

        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="DocNum">单据号</param>
        /// <param name="Remark">备注</param>
        /// <param name="deletedetail">删除数组</param>
        /// <param name="msDetailList">添加明细</param>
        /// <param name="user">用户</param>
        /// <param name="error_message">错误信息</param>
        /// <returns></returns>
        public bool Update(string DocNum,string Remark,string[] deletedetail, List<MM_PickingApplyDetail> msDetailList, string user, out string error_message)
        {
            error_message = "";
            DateTime time = DateTime.Now;
            try
            {
                //根据主键ID查询委外申请单明细信息
                var entities = detail_app.GetListByKeys(deletedetail);

                //根据申请单号查询委外申请单主表信息
                var query = GetList(x => (string.IsNullOrEmpty(DocNum) || x.DocNum == DocNum)).ToList().FirstOrDefault();

                var AddmsDetailList = new List<MM_PickingApplyDetail>();
                var UpdatemsDetailList = new List<MM_PickingApplyDetail>();
                //插入子表信息 podetailed
                foreach (var msdetail in msDetailList)
                {
                    if (string.IsNullOrEmpty(msdetail.DetailID) && msdetail.DetailID==null)
                    {
                        msdetail.DocNum = DocNum;
                        msdetail.IsDelete = false;
                        msdetail.CUser = user;
                        msdetail.CTime = time;
                        msdetail.Status = 0; //0:未开始 1:进行中 2：已完成
                        AddmsDetailList.Add(msdetail);
                    }
                    else
                    {
                        msdetail.MUser = user;
                        msdetail.MTime = time;
                        UpdatemsDetailList.Add(msdetail);
                    }
                    //listmsdetail.Add(podetailed);
                }

                DbContext.Ado.BeginTran();
                detail_app.DbContext = this.DbContext;

                //删除委外申请单明细信息
                if (entities.Count>0)
                {
                    detail_app.Delete(entities, user);
                }
                //更新委外申请单信息
                if (query !=null)
                {
                    query.Remark = Remark;
                    base.Update(query);
                }
                //插入委外申请单明细信息
                //this.Update(listms);
                if (AddmsDetailList.Count > 0)
                {
                    detail_app.Insert(AddmsDetailList);
                }
                //更新委外申请单明细信息
                if (UpdatemsDetailList.Count > 0)
                {
                    detail_app.Update(UpdatemsDetailList);
                }

                DbContext.Ado.CommitTran();

                //是否自动过账判定
                //if (_switchApp.IsPoReturnScanAutoPost)
                //{
                //    return DoPost(entities, opUser, out error_message);
                //}

                return true;
            }
            catch (Exception ex)
            {
                if(DbContext.Ado.IsAnyTran())
                    DbContext.Ado.RollbackTran();

                error_message = ex.Message;
                return false;
            }
        }

        #endregion

        #region 校验库存

        /// <summary>
        /// 校验库存
        /// </summary>
        /// <param name="ItemCodes"></param>
        /// <param name="error_message"></param>
        public bool ValidateStockOut(string[] ItemCodes, out string error_message)
        {
            error_message = "";
            bool bStock = true;
            MD_StockApp _stockApp = new MD_StockApp();
            foreach (string ItemCode in ItemCodes)
            {
                var query = _stockApp.GetList(x => x.ItemCode== ItemCode && (x.SaleNum==null || x.SaleNum=="") && (x.SupplierCode==null || x.SupplierCode=="")).ToList();
                if (query != null && query.Count>0)
                {
                    error_message = "当前物料:[" + ItemCode + "]没有查询到库存！"; ;
                    return false;
                }
            }
            return bStock;
        }

        #endregion

        #region 查询明细分页列表

        /// <summary>
        /// 查询明细分页列表
        /// </summary>
        /// <param name="page">分页</param>
        /// <param name="condition">参数</param>
        /// <returns></returns>
        public List<MM_PickingApplyDetailForDispatch_View> GetPageDetailList(Pagination page, Expression<Func<MM_PickingApplyDetailForDispatch_View, bool>> condition)
        {
            if (string.IsNullOrEmpty(page.Sort))
            {
                page.Sort = "CTime desc";
            }
            var query = DbContext.Queryable<MM_PickingApplyDetailForDispatch_View>()
                .Where(condition)
                .OrderBy(page.Sort);

            int total = query.Count();
            page.Total = total;
            if (page.PageSize > total)
            {
                return query.ToPageList(page.PageNumber, total);
            }
            return query.ToPageList(page.PageNumber, page.PageSize);
        }

        #endregion

    }
}
