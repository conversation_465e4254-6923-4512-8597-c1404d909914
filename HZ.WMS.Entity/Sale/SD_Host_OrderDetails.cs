using System;
using System.Collections.Generic;
using System.ComponentModel;
using AOS.OMS.Entity.Basic;
using HZ.WMS.Entity;
using SqlSugar;

namespace AOS.OMS.Entity.Sale
{
    /// <summary>
    /// 订单明细类
    /// </summary>
    public class SD_Host_OrderDetails : BaseEntity
    {
        /// <summary>
        /// 数据主键ID
        /// </summary>
        [Description("数据主键ID")]
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }
        
        /// <summary>
        /// 父ID
        /// </summary>
        [Description("父ID")]
        public string Pid { get; set; }

        /// <summary>
        /// 订单编号
        /// </summary>
        [Description("订单编号")]
        public string OmsNo { get; set; }

        /// <summary>
        /// 订单分录行号
        /// </summary>
        [Description("订单分录行号")]
        public int? OmsLine { get; set; }
        
        /// <summary>
        /// SAP过账单号
        /// </summary>  
        [Description("SAP过账单号")]
        public string SapNo { get; set; }
        
        /// <summary>
        /// SAP行号
        /// </summary> 
        [Description("SAP行号")]
        public int? SapLine { get; set; }
        
        /// <summary>
        /// 项目名称
        /// </summary> 
        [Description("项目名称")]
        public string ProjectName { get; set; }
        
        /// <summary>
        /// 行类别,1-销售 2-otis首次 3-试用 4-借用 5-赠送
        /// </summary>  
        [Description("订单类型")]
        public int OrderType { get; set; }
        
        /// <summary>
        /// 梯型
        /// </summary>
        [Description("梯型")]
        public string ElevatorType { get; set; }
        
        /// <summary>
        /// 产品系列
        /// </summary>
        [Description("产品系列")]
        public string ProductSeries { get; set; }
        
        /// <summary>
        /// 产品型号
        /// </summary>
        [Description("产品型号")]
        public string ProductModel { get; set; }
        
        /// <summary>
        /// 客户编码
        /// </summary>
        [Description("客户编码")]
        public string CustomerCode { get; set; }
        
        /// <summary>
        /// 客户名称
        /// </summary>
        [Description("客户名称")]
        public string CustomerName { get; set; }
        
        /// <summary>
        /// 客户发货地址
        /// </summary>  
        [Description("客户地址")]
        public string CustomerAddress { get; set; }
        
        /// <summary>
        /// 结算地址
        /// </summary>  
        [Description("结算地址")]
        public string SettlementAdd { get; set; }
        
        /// <summary>
        /// 客户件号
        /// </summary>
        [Description("客户件号")]
        public string CustomerPart { get; set; }
        
        /// <summary>
        /// 客户型号
        /// </summary>  
        [Description("客户型号")]
        public string CustomerModel { get; set; }
        
        /// <summary>
        /// SAP件号
        /// </summary>  
        [Description("SAP件号")]
        public string SapPart { get; set; }
        
        /// <summary>
        /// SAP型号
        /// </summary>  
        [Description("SAP型号")]
        public string SapProductModel { get; set; }
        
        /// <summary>
        /// 物料类型
        /// </summary>  
        [Description("物料类型")]
        public string MaterialType { get; set; }
        
        /// <summary>
        /// 物料编码
        /// </summary>  
        [Description("物料编码")]
        public string MaterialCode { get; set; }
        
        /// <summary>
        /// 物料描述
        /// </summary>  
        [Description("物料描述")]
        public string MaterialDesc { get; set; }
        
        /// <summary>
        /// 客户订单号
        /// </summary>
        [Description("客户订单号")]
        public string CustomerOrderNo { get; set; }

        /// <summary>
        /// 合同号
        /// </summary>
        [Description("合同号")]
        public string ContractNo { get; set; }
        
        /// <summary>
        /// 非标接收编号
        /// </summary>  
        [Description("非标接收编号")]
        public string NonstandardNo { get; set; }
        
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量")]
        public int? Quantity { get; set; }
        
        /// <summary>
        /// 价格
        /// </summary>
        [Description("价格")]
        public decimal? Price { get; set; }

        /// <summary>
        /// 含税价格
        /// </summary>  
        [Description("含税价格")]
        public decimal? TaxRatePrice { get; set; }

        /// <summary>
        /// 运费
        /// </summary>
        [Description("运费")]
        public decimal? FreightPrice { get; set; }
        
        /// <summary>
        /// 货币
        /// </summary>
        [Description("货币")]
        public string Currency { get; set; }
        
        /// <summary>
        /// 1需确认参数 2未过账 3已过帐 4已下达 5已交货
        /// </summary>  
        [Description("1需确认参数 2未过账 3已过帐 4已下达 5已交货")]
        public int? Status { get; set; }
        
        /// <summary>
        /// 是否出口
        /// </summary> 
        [Description("是否出口")]
        public string IsExport { get; set; }
        
        /// <summary>
        /// 有无机房
        /// </summary>
        [Description("有无机房")]
        public string ExistRoom { get; set; }
        
        /// <summary>
        /// 机房布置方向
        /// </summary>
        [Description("机房布置方向")]
        public string RoomLayout { get; set; }
        
        /// <summary>
        /// 出厂编号
        /// </summary> 
        [Description("出厂编号")]
        public string FactoryNo { get; set; }
        
        /// <summary>
        /// 送货批次
        /// </summary>
        [Description("送货批次")]
        public string BatchNo { get; set; }
        
        /// <summary>
        /// 交货日期
        /// </summary>
        [Description("交货日期")]
        public DateTime? DeliveryDate { get; set; }
        
        /// <summary>
        /// 发运日期
        /// </summary>
        [Description("发运日期")]
        public DateTime? ShipmentDate { get; set; }
        
        /// <summary>
        /// 主机颜色
        /// </summary>
        [Description("主机颜色")]
        public string HostColor { get; set; }
        
        /// <summary>
        /// OEM客户
        /// </summary> 
        [Description("OEM客户")]
        public string OemCustomer { get; set; }
        
        /// <summary>
        /// 订单到达日期
        /// </summary>
        [Description("订单到达日期")]
        public DateTime? ArriveDate { get; set; }

        /// <summary>
        /// 催货日期
        /// </summary>
        [Description("催货日期")]
        public DateTime? ExpeditingDate { get; set; }

        /// <summary>
        /// 巨通催货
        /// </summary>
        [Description("巨通催货")]
        public DateTime? JtExpeditingDate { get; set; }

        /// <summary>
        /// 催货更新
        /// </summary>
        [Description("催货更新")]
        public DateTime? ExpeditingUpdateDate { get; set; }
        
        /// <summary>
        /// 履行备注
        /// </summary>
        [Description("履行备注")]
        public string PerformRemark { get; set; }
        
        /// <summary>
        /// 订单备注
        /// </summary>
        [Description("订单备注")]
        public string OrderRemark { get; set; }
        
        /// <summary>
        /// 明细备注
        /// </summary>
        [Description("明细备注")]
        public string DetailRemark { get; set; }
        
        /// <summary>
        /// 曳引比
        /// </summary>
        [Description("曳引比")]
        public string TractionRatio { get; set; }
        
        /// <summary>
        /// 单复绕
        /// </summary> 
        [Description("单复绕")]
        public string SingleMultipleWind { get; set; }
        
        /// <summary>
        /// 额定载重
        /// </summary>
        [Description("额定载重")]
        public string RatedLoad { get; set; }
        
        /// <summary>
        /// 铭牌载重
        /// </summary>
        [Description("铭牌载重")]
        public string NameplateLoad { get; set; }
        
        /// <summary>
        /// 额定梯速
        /// </summary>
        [Description("额定梯速")]
        public string RatedElevatorSpeed { get; set; }
        
        /// <summary>
        /// 铭牌速度
        /// </summary>
        [Description("铭牌速度")]
        public string NameplateSpeed { get; set; }
        
        /// <summary>
        /// 额定转速Currency
        /// </summary>
        [Description("额定转速")]
        public string RatedSpeed { get; set; }
        
        /// <summary>
        /// 额定转矩Currency
        /// </summary>
        [Description("额定转矩")]
        public string RatedTorque { get; set; }
        
        /// <summary>
        /// 许用径向载荷Currency
        /// </summary>
        [Description("许用径向载荷")]
        public string AllowableRadialLoad { get; set; }
        
        /// <summary>
        /// 主机重量Currency
        /// </summary>
        [Description("主机重量")]
        public string Weight { get; set; }
        
        /// <summary>
        /// 额定功率Currency
        /// </summary>
        [Description("额定功率")]
        public string RatedPower { get; set; }
        
        /// <summary>
        /// 额定电压
        /// </summary>
        [Description("额定电压")]
        public string RatedVoltage { get; set; }
        
        /// <summary>
        /// 额定电流
        /// </summary>
        [Description("额定电流")]
        public string RatedCurrent { get; set; }
        
        /// <summary>
        /// 相电阻Currency
        /// </summary>
        [Description("相电阻")]
        public string PhaseResistance { get; set; }
        
        /// <summary>
        /// 电感Currency
        /// </summary>
        [Description("电感")]
        public string Inductance { get; set; }
        
        /// <summary>
        /// 极数Currency
        /// </summary>
        [Description("极数")]
        public string PolesNo { get; set; }
        
        /// <summary>
        /// 额定频率Currency
        /// </summary>
        [Description("额定频率")]
        public string RatedFrequency { get; set; }
        
        /// <summary>
        /// 电机效率Currency
        /// </summary>
        [Description("电机效率")]
        public string MotorEfficiency { get; set; }
        
        /// <summary>
        /// 防护等级Currency
        /// </summary>
        [Description("防护等级")]
        public string ProtectionGrade { get; set; }
        
        /// <summary>
        /// 启动次数Currency
        /// </summary>
        [Description("启动次数")]
        public string StartNo { get; set; }
        
        /// <summary>
        /// 绝缘等级
        /// </summary>
        [Description("绝缘等级")]
        public string InsulationGrade { get; set; }
        
        /// <summary>
        /// 工作制
        /// </summary>
        [Description("工作制")]
        public string WorkSystem { get; set; }
        
        /// <summary>
        /// 接线方式
        /// </summary>
        [Description("接线方式")]
        public string JunctionMethod { get; set; }
        
        /// <summary>
        /// 轴承型号1
        /// </summary>
        [Description("轴承型号1")]
        public string BearingModelOne { get; set; }
        
        /// <summary>
        /// 轴承型号2
        /// </summary>
        [Description("轴承型号2")]
        public string BearingModelTwo { get; set; }
        
        /// <summary>
        /// 功率因子
        /// </summary>
        [Description("功率因子")]
        public string PowerFactor { get; set; }
        
        /// <summary>
        /// 额定激励电压
        /// </summary>
        [Description("额定激励电压")]
        public string RatedExcitationVoltage { get; set; }
        
        /// <summary>
        /// 维持电压
        /// </summary>
        [Description("维持电压")]
        public string HoldVoltage { get; set; }
        
        /// <summary>
        /// 激励功率
        /// </summary>
        [Description("激励功率")]
        public string ExcitationPower { get; set; }
        
        /// <summary>
        /// 工作间隙
        /// </summary>
        [Description("工作间隙")]
        public string WorkGap { get; set; }
        
        /// <summary>
        /// 最大工作间隙
        /// </summary>
        [Description("最大工作间隙")]
        public string MaxWorkGap { get; set; }
        
        /// <summary>
        /// 曳引轮序号
        /// </summary>
        [Description("曳引轮序号")]
        public string TractionSheaveSortNo { get; set; }
        
        /// <summary>
        /// 曳引轮件号
        /// </summary>
        [Description("曳引轮件号")]
        public string TractionSheaveCode { get; set; }
        
        /// <summary>
        /// 曳引轮描述
        /// </summary>
        [Description("曳引轮描述")]
        public string TractionSheaveDesc { get; set; }
        
        /// <summary>
        /// 曳引轮节径
        /// </summary>
        [Description("曳引轮节径")]
        public string TractionSheavePitchDiameter { get; set; }
        
        /// <summary>
        /// 曳引轮槽数
        /// </summary>
        [Description("曳引轮槽数")]
        public string TractionSheaveSlotsNo { get; set; }
        
        /// <summary>
        /// 钢带数
        /// </summary> 
        [Description("钢带数")]
        public string SteelStripsNo { get; set; }
        
        /// <summary>
        /// 曳引轮绳槽直径
        /// </summary>
        [Description("曳引轮绳槽直径")]
        public string TractionSheaveRopeRaceDiameter { get; set; }
        
        /// <summary>
        /// 曳引轮槽距
        /// </summary>
        [Description("曳引轮槽距")]
        public string SlotPitch { get; set; }
        
        /// <summary>
        /// 曳引轮槽型
        /// </summary>
        [Description("曳引轮槽型")]
        public string SlotType { get; set; }
        
        /// <summary>
        /// 曳引轮R(°)
        /// </summary> 
        [Description("曳引轮R(°)")]
        public string TractionSheaveRadius { get; set; }
        
        /// <summary>
        /// 曳引轮β
        /// </summary>
        [Description("曳引轮β")]
        public string TractionSheaveBeta { get; set; }
        
        /// <summary>
        /// 曳引轮γ
        /// </summary>
        [Description("曳引轮γ")]
        public string TractionSheaveGamma { get; set; }
        
        /// <summary>
        /// 绳槽上公差
        /// </summary>
        [Description("绳槽上公差")]
        public string RopeRaceUpPublicErrand { get; set; }
        
        /// <summary>
        /// 绳槽下公差
        /// </summary>
        [Description("绳槽下公差")]
        public string RopeRaceDownPublicErrand { get; set; }
        
        /// <summary>
        /// 中心距离
        /// </summary>
        [Description("中心距离")]
        public string CenterDistance { get; set; }
        
        /// <summary>
        /// 曳引轮材质
        /// </summary>
        [Description("曳引轮材质")]
        public string TractionSheaveMaterial { get; set; }
        
        /// <summary>
        /// 曳引轮硬度
        /// </summary>
        [Description("曳引轮硬度")]
        public string TractionSheaveStiffness { get; set; }
        
        /// <summary>
        /// 曳引轮备注
        /// </summary> 
        [Description("曳引轮备注")]
        public string TractionSheaveRemark { get; set; }
        
        /// <summary>
        /// 制动器型号
        /// </summary>
        [Description("制动器型号")]
        public string BrakeModel { get; set; }
        
        /// <summary>
        /// 制动器电流
        /// </summary>
        [Description("制动器电流")]
        public string BrakeCurrent { get; set; }
        
        /// <summary>
        /// 制动器电压DC
        /// </summary>
        [Description("制动器电压DC")]
        public string BrakeVoltage { get; set; }
        
        /// <summary>
        /// 制动力矩
        /// </summary>
        [Description("制动力矩")]
        public string BrakeTorque { get; set; }
        
        /// <summary>
        /// 微动开关
        /// </summary>
        [Description("微动开关")]
        public string MicroSwitch { get; set; }
        
        /// <summary>
        /// 开关型号
        /// </summary>
        [Description("开关型号")]
        public string SwitchModel { get; set; }
        
        /// <summary>
        /// 制动器功率
        /// </summary>
        [Description("制动器功率")]
        public string BrakePower { get; set; }
        
        /// <summary>
        /// 编码器
        /// </summary>
        [Description("编码器")]
        public string Encoder { get; set; }
        
        /// <summary>
        /// 编码器前置
        /// </summary>
        [Description("编码器前置")]
        public string EncoderPre { get; set; }
        
        /// <summary>
        /// 编码器接头
        /// </summary> 
        [Description("编码器接头")]
        public string EncoderConnector { get; set; }
        
        /// <summary>
        /// 编码器线长
        /// </summary>
        [Description("编码器线长")]
        public string EncoderLineLength { get; set; }
        
        /// <summary>
        /// 软管
        /// </summary>
        [Description("软管")]
        public string Hose { get; set; }
        
        /// <summary>
        /// 变频器
        /// </summary>
        [Description("变频器")]
        public string FrequencyTransformer { get; set; }
        
        /// <summary>
        /// 盘车装置
        /// </summary>
        [Description("盘车装置")]
        public string TurningGear { get; set; }
        
        /// <summary>
        /// 盘车位置
        /// </summary>
        [Description("盘车位置")]
        public string TurningPosition { get; set; }
        
        /// <summary>
        /// 手动松闸Currency
        /// </summary>
        [Description("手动松闸")]
        public string ManualOperationDeclutch { get; set; }
        
        /// <summary>
        /// 远程松闸
        /// </summary>
        [Description("远程松闸")]
        public string RemoteDeclutch { get; set; }
        
        /// <summary>
        /// 松闸线长
        /// </summary>
        [Description("松闸线长")]
        public string DeclutchLineLength { get; set; }
        
        /// <summary>
        /// 接线盒位置
        /// </summary>
        [Description("接线盒位置")]
        public string JunctionBoxPosition { get; set; }
        
        /// <summary>
        /// 接线盒盖
        /// </summary>
        [Description("接线盒盖")]
        public string JunctionBoxCover { get; set; }
        
        /// <summary>
        /// 接线盒端子
        /// </summary>
        [Description("接线盒端子")]
        public string JunctionBoxTerminal { get; set; }
        
        /// <summary>
        /// 挡绳杆
        /// </summary>
        [Description("挡绳杆")]
        public string RopeStopRod { get; set; }
        
        /// <summary>
        /// 主机护罩
        /// </summary>
        [Description("主机护罩")]
        public string HostCover { get; set; }
        
        /// <summary>
        /// 制动器护罩
        /// </summary> 
        [Description("制动器护罩")]
        public string BrakeCover { get; set; }
        
        /// <summary>
        /// 主机铭牌
        /// </summary>
        [Description("主机铭牌")]
        public string HostNameplate { get; set; }
        
        /// <summary>
        /// 制动器铭牌
        /// </summary>
        [Description("制动器铭牌")]
        public string BrakeNameplate { get; set; }
        
        /// <summary>
        /// 铭牌位置
        /// </summary>
        [Description("铭牌位置")]
        public string NameplateLocation { get; set; }
        
        /// <summary>
        /// 减震垫
        /// </summary>
        [Description("减震垫")]
        public string ShockPad { get; set; }
        
        /// <summary>
        /// 打包件
        /// </summary>
        [Description("打包件")]
        public string PackPart { get; set; }
        
        /// <summary>
        /// 接线标签
        /// </summary>
        [Description("接线标签")]
        public string LineLabel { get; set; }
        
        /// <summary>
        /// 防触电标签
        /// </summary>
        [Description("防触电标签")]
        public string ProtectingElectrifyingLabel { get; set; }
        
        /// <summary>
        /// 吊具
        /// </summary>
        [Description("吊具")]
        public string Sling { get; set; }
        
        /// <summary>
        /// 干燥剂
        /// </summary>
        [Description("干燥剂")]
        public string Desiccant { get; set; }
        
        /// <summary>
        /// 防锈袋
        /// </summary>
        [Description("防锈袋")]
        public string RustProofBag { get; set; }
        
        /// <summary>
        /// 说明书
        /// </summary>
        [Description("说明书")]
        public string Instructions { get; set; }
        
        /// <summary>
        /// 包装防护警示标签
        /// </summary>
        [Description("包装防护警示标签")]
        public string PackProtectionWarnLabel { get; set; }
        
        /// <summary>
        /// 能效标签
        /// </summary>
        [Description("能效标签")]
        public string EnergyLabel { get; set; }
        
        /// <summary>
        /// 制动器警示标签
        /// </summary>
        [Description("制动器警示标签")]
        public string BrakeWarnLabel { get; set; }
        
        /// <summary>
        /// 制动器侧方警示标签
        /// </summary>
        [Description("制动器侧方警示标签")]
        public string BrakeSideWarn { get; set; }
        
        /// <summary>
        /// 指定品牌
        /// </summary>
        [Description("指定品牌")]
        public string DesignatedBrand { get; set; }
        
        /// <summary>
        /// 接地线
        /// </summary>
        [Description("接地线")]
        public string GroundWire { get; set; }
        
        /// <summary>
        /// 接地线标识
        /// </summary>
        [Description("接地线标识")]
        public string GroundWireIdentification { get; set; }
        
        /// <summary>
        /// 波纹管
        /// </summary>
        [Description("波纹管")]
        public string CorrugatedTube { get; set; }
        
        /// <summary>
        /// 能效等级
        /// </summary> 
        [Description("能效等级")]
        public string EnergyEfficiencyGrade { get; set; }
        
        /// <summary>
        /// 线缆
        /// </summary>
        [Description("线缆")]
        public string Cable { get; set; }
        
        /// <summary>
        /// 外包尺寸
        /// </summary>
        [Description("外包尺寸")]
        public string PackSize { get; set; }
        
        /// <summary>
        /// 包装清单
        /// </summary>
        [Description("包装清单")]
        public string PackList { get; set; }
        
        /// <summary>
        /// 特殊要求
        /// </summary>
        [Description("特殊要求")]
        public string SpecialRequire { get; set; }
        
        /// <summary>
        /// 包装箱件号
        /// </summary>
        [Description("包装箱件号")]
        public string PackPartNo { get; set; }
        
        /// <summary>
        /// 包装箱材质
        /// </summary>
        [Description("包装箱材质")]
        public string PackMaterial { get; set; }
        
        /// <summary>
        /// 包装箱尺寸
        /// </summary>
        [Description("包装箱尺寸")]
        public string PackBoxSize { get; set; }
        
        /// <summary>
        /// 包装箱重量
        /// </summary>
        [Description("包装箱重量")]
        public string PackBoxWeight { get; set; }
        
        /// <summary>
        /// 刷字件号
        /// </summary>
        [Description("刷字件号")]
        public string BrushPart { get; set; }
        
        /// <summary>
        /// 刷字
        /// </summary>
        [Description("刷字")]
        public string Brush { get; set; }
        
        /// <summary>
        /// 范围
        /// </summary>
        [Description("范围")]
        public string Range { get; set; }
        
        /// <summary>
        /// 报告编号
        /// </summary>
        [Description("报告编号")]
        public string HostReportNo { get; set; }
        
        /// <summary>
        /// 主机证书编号
        /// </summary>
        [Description("主机证书编号")]
        public string HostCertificateNo { get; set; }
        
        /// <summary>
        /// 制动器品牌
        /// </summary>
        [Description("制动器品牌")]
        public string BrakeBrand { get; set; }
        
        /// <summary>
        /// 主机试验机构
        /// </summary>
        [Description("主机试验机构")]
        public string HostInstitution { get; set; }
        
        /// <summary>
        /// 截止日期
        /// </summary>
        [Description("截止日期")]
        public DateTime? HostLastDate { get; set; }
        
        /// <summary>
        /// ACOP允许系统质量范围
        /// </summary>
        [Description("ACOP允许系统质量范围")]
        public string AcopAllowSystemQualityRange { get; set; }
        
        /// <summary>
        /// ACOP允许额定载重范围
        /// </summary>
        [Description("ACOP允许额定载重范围")]
        public string AcopAllowRatedLoadRange { get; set; }
        
        /// <summary>
        /// ACOP动作速度范围
        /// </summary>
        [Description("ACOP动作速度范围")]
        public string AcopActionSpeedRange { get; set; }
        
        /// <summary>
        /// ACOP报告编号
        /// </summary>
        [Description("ACOP报告编号")]
        public string AcopReportNo { get; set; }
        
        /// <summary>
        /// ACOP证书编号
        /// </summary>
        [Description("ACOP证书编号")]
        public string AcopCertificateNo { get; set; }
        
        /// <summary>
        /// ACOP试验机构
        /// </summary>
        [Description("ACOP试验机构")]
        public string AcopInstitution { get; set; }
        
        /// <summary>
        /// ACOP截止日期
        /// </summary>
        [Description("ACOP截止日期")]
        public DateTime? AcopLastDate { get; set; }
        
        /// <summary>
        /// UCMP轿厢意外型号
        /// </summary>
        [Description("UCMP轿厢意外型号")]
        public string UcmpCarAccidentModel { get; set; }
        
        /// <summary>
        /// UCMP轿厢减速前最高速度
        /// </summary>
        [Description("UCMP轿厢减速前最高速度")]
        public string UcmpMaxSpeed { get; set; }
        
        /// <summary>
        /// UCMP报告编号
        /// </summary>
        [Description("UCMP报告编号")]
        public string UcmpReportNo { get; set; }
        
        /// <summary>
        /// UCMP证书编号
        /// </summary>
        [Description("UCMP证书编号")]
        public string UcmpCertificateNo { get; set; }
        
        /// <summary>
        /// UCMP试验机构
        /// </summary>
        [Description("UCMP试验机构")]
        public string UcmpInstitution { get; set; }
        
        /// <summary>
        /// UCMP截止日期
        /// </summary>
        [Description("UCMP截止日期")]
        public DateTime? UcmpLastDate { get; set; }

        /// <summary>
        /// 最大触发速
        /// </summary>
        [Description("最大触发速")]
        public string MaxTriggerSpeed { get; set; }
        
        /// <summary>
        /// 发运设置用户
        /// </summary>
        [Description("发运设置用户")]
        public string ShipmentSetUser { get; set; }
        
        /// <summary>
        /// 发运设置时间
        /// </summary>
        [Description("发运设置时间")]
        public DateTime? ShipmentSetDate { get; set; }
        
        /// <summary>
        /// 发运地址
        /// </summary>
        [Description("发运地址")]
        public string ShipmentAdd { get; set; }
        
        /// <summary>
        /// 发运联系电话
        /// </summary>
        [Description("发运联系电话")]
        public string ShipmentTelephone { get; set; }
        
        /// <summary>
        /// 发运联系人
        /// </summary>
        [Description("发运联系人")]
        public string ShipmentContacts { get; set; }
        
        /// <summary>
        /// PCS状态
        /// </summary>
        [Description("PCS状态")]
        public int? PcsStatus { get; set; }
        
        /// <summary>
        /// SAP状态
        /// </summary>
        [Description("SAP状态")]
        public int? SapStatus { get; set; }
        
        /// <summary>
        /// 发运状态
        /// </summary>
        [Description("发运状态")]
        public int? ShipmentStatus { get; set; }
        
        /// <summary>
        /// 生产状态
        /// </summary>
        [Description("生产状态")]
        public int? ProduceStatus { get; set; }
        
        /// <summary>
        /// SAP过账用户
        /// </summary>  
        [Description("SAP过账用户")]
        public string SapSetUser { get; set; }
        
        /// <summary>
        /// SAP过账时间
        /// </summary>  
        [Description("SAP过账时间")]
        public DateTime? SapSetDate { get; set; }
        
        /// <summary>
        /// 库存地点
        /// </summary>  
        [Description("库存地点")]
        public string StockLocation { get; set; }
        
        /// <summary>
        /// 复绕轮组件
        /// </summary>
        [Description("复绕轮组件")]
        public string SecondarySheaveComponent { get; set; }
        
        /// <summary>
        /// 轮轴圆弧
        /// </summary>
        [Description("轮轴圆弧")]
        public string AxleArc { get; set; }
        
        /// <summary>
        /// 地址类型
        /// </summary>
        [Description("地址类型")]
        public string AddressType { get; set; }
        
        /// <summary>
        /// 铭牌电压
        /// </summary>
        [Description("铭牌电压")]
        public string NameplateVoltage { get; set; }
        
        /// <summary>
        /// 铭牌功率
        /// </summary>
        [Description("铭牌功率")]
        public string NameplatePower { get; set; }
        
        /// <summary>
        /// 唛头
        /// </summary>
        [Description("唛头")]
        public string ShippingMark { get; set; }
        
        /// <summary>
        /// 邮件状态  0默认  1已执行0.5小时提醒  2 已执行下班前提醒
        /// </summary>
        [Description("邮件状态")]
        public int? EmailStatus { get; set; }
        
        /// <summary>
        /// 排产完成时间
        /// </summary>
        [Description("排产完成时间")]
        public DateTime? ProduceSchedulingCompleteTime { get; set; }
        
        /// <summary>
        /// 报工完成时间
        /// </summary>
        [Description("报工完成时间")]
        public DateTime? ReportCompleteTime { get; set; }
        
        /// <summary>
        /// 发运完成时间
        /// </summary>
        [Description("发运完成时间")]
        public DateTime? ShippingCompleteTime { get; set; }

    }
}
