using System.ComponentModel;
using HZ.WMS.Entity;
using SqlSugar;

namespace AOS.OMS.Entity.Basic
{
    /// <summary>
    /// 客户件号属性参数
    /// </summary>
    [SugarTable("Cable_Basic_Customer_Part_Params")]
    public class CustomerPartParams : BaseEntity
    {
        /// <summary>
        /// ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Description("Id")]
        public string Id { get; set; }
        
        /// <summary>
        /// Pid
        /// </summary>
        [Description("Pid")]
        public string Pid { get; set; }
        
        /// <summary>
        /// 件号
        /// </summary>
        [Description("件号")]
        public string PartNo { get; set; }
        
        /// <summary>
        /// 四线组
        /// </summary>
        [Description("四线组")]
        public string Quad { get; set; }
        
        /// <summary>
        /// 机房属性
        /// </summary>
        [Description("机房属性")]
        public string ComputerRoomProp { get; set; }
        
        /// <summary>
        /// 机房侧
        /// </summary>
        [Description("机房侧")]
        public string MachineRoomSide { get; set; }
        
        /// <summary>
        /// 中间段
        /// </summary>
        [Description("中间段")]
        public string IntermediateSection { get; set; }
        
        /// <summary>
        /// 底坑侧
        /// </summary>
        [Description("底坑侧")]
        public string PitSide { get; set; }
        
        /// <summary>
        /// 中间开线长度
        /// </summary>
        [Description("中间开线长度")]
        public decimal? MiddleOpeningLength { get; set; }
        
        /// <summary>
        /// 电缆型号
        /// </summary>
        [Description("电缆型号")]
        public string CableType { get; set; }
        
        /// <summary>
        /// 数量索引
        /// </summary>
        [Description("数量索引")]
        public int QuantityIndex { get; set; }
        
        /// <summary>
        /// 观光梯
        /// </summary>
        [Description("观光梯")]
        public string SightseeingLift { get; set; }
        
        /// <summary>
        /// 载重
        /// </summary>
        [Description("载重")]
        public string Load { get; set; }
        
        /// <summary>
        /// 控制系统
        /// </summary>
        [Description("控制系统")]
        public string ControlSystem { get; set; }
        
        /// <summary>
        /// 电缆件号
        /// </summary>
        [Description("电缆件号")]
        public string CablePartCode { get; set; }
        
        /// <summary>
        /// 电梯类型
        /// </summary>
        [Description("电梯类型")]
        public string ElevatorType { get; set; }
        
    }
}