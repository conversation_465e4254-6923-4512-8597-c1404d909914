using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.PP.ViewModel
{
    /// <summary>
    /// 生产物料配送单子表
    /// </summary>
    public class PP_DistributionDetail_View: BaseEntity
    {
        /// <summary>
        /// 开始日期(装配日期)
        /// </summary>
        [Description("装配日期")]
        public DateTime? StartTime { get; set; }
        /// <summary>
        /// 线体编码
        /// </summary>
        [Description("装配线编码")]
        public string ProductionLineCode { get; set; }
        /// <summary>
        /// 线体描述
        /// </summary>
        [Description("装配线")]
        public string ProductionLineDes { get; set; }
        /// <summary>
        /// 组件编码
        /// </summary>
        [Description("物料件号")]
        public string ComponentCode { get; set; }
        /// <summary>
        /// 物料描述
        /// </summary>
        [Description("物料描述")]
        public string MaterialName { get; set; }
        /// <summary>
        /// 需求数量
        /// </summary>
        [Description("配送数量")]
        public decimal DemandQty { get; set; }
        /// <summary>
        /// 供应商数量
        /// </summary>
        [Description("供应商数量")]
        public string Supplier { get; set; }
        /// <summary>
        /// 自制数量
        /// </summary>
        [Description("自制数量")]
        public decimal? HomemadeQty { get; set; }
        /// <summary>
        /// 员工号
        /// </summary>
        public string EmployeeNumber { get; set; }
        /// <summary>
        /// 员工姓名
        /// </summary>
        [Description("员工姓名")]
        public string EmployeeName { get; set; }

        /// <summary>
        /// 组件单位
        /// </summary>
        [Description("单位")]
        public string ComponentUnit { get; set; }
        /// <summary>
        /// 转出仓库
        /// </summary>
        [Description("转出仓库")]
        public string OutWarehouse { get; set; }
        /// <summary>
        /// 发料库存地
        /// </summary>
        [Description("转入仓库")]
        public string DeliverLocation { get; set; }
        /// <summary>
        /// 配置描述
        /// </summary>
        [Description("配置描述")]
        public string ItemName { get; set; }
        /// <summary>
        /// 物料组
        /// </summary>
         [Description("物料组编码")]
        public string MaterialGroupCode { get; set; }
        /// <summary>
        /// 物料组描述
        /// </summary>
        [Description("物料组")]
        public string MaterialGroupDes { get; set; }
        /// <summary>
        /// 评估类别，为B时评估类型为01自制02外购
        /// </summary>
        public string AssessmentCategory { get; set; }
    }
}
