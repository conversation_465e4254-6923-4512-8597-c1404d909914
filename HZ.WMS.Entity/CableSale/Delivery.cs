using System;
using System.ComponentModel;
using HZ.WMS.Entity;
using SqlSugar;

namespace AOS.WMS.Entity.CableProduce
{
    [SugarTable("Cable_Delivery")]
    public class Delivery : BaseEntity
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Description("主键ID")]
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }
        
        /// <summary>
        /// 交货单号
        /// </summary>
        [Description("交货单号")]
        public string DocNum { get; set; }
        
        /// <summary>
        /// 发运单号
        /// </summary>
        [Description("发运单号")]
        public string ShippingNo { get; set; }
        
        /// <summary>
        /// 父主键ID
        /// </summary>
        [Description("父主键ID")]
        public string Pid { get; set; }

        /// <summary>
        /// 订单编号
        /// </summary>
        [Description("订单编号")]
        public string OmsOrderNum { get; set; }

        /// <summary>
        /// 订单行号
        /// </summary>
        [Description("订单行号")]
        public int? OmsLineNum { get; set; }

        /// <summary>
        /// 订单状态
        /// </summary>
        [Description("订单状态")]
        public int Status { get; set; }

        /// <summary>
        /// 订单类型(1:主机订单 2:部件订单 3:售后部件 4:售后主机)
        /// </summary>
        [Description("订单类型")]
        public int OrderType { get; set; }

        /// <summary>
        /// 下载标记
        /// </summary>
        [Description("下载标记")]
        public bool DownFlag { get; set; }

        /// <summary>
        /// 销售单号
        /// </summary>
        [Description("销售单号")]
        public string SapNo { get; set; }
        
        /// <summary>
        /// 销售单号
        /// </summary>
        [Description("销售行号")]
        public int SapLine { get; set; }

        /// <summary>
        /// 物料编码
        /// </summary>
        [Description("物料编码")]
        public string MaterialCode { get; set; }

        /// <summary>
        /// 客户件号
        /// </summary>
        [Description("客户件号")]
        public string CustomerPartNo { get; set; }

        /// <summary>
        /// 规格型号
        /// </summary>
        [Description("规格型号")]
        public string SpecificationModel { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量")]
        public int Quantity { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [Description("单位")]
        public string Unit { get; set; }

        /// <summary>
        /// 单根数量
        /// </summary>
        [Description("单根数量")]
        public int SingleQuantity { get; set; }

        /// <summary>
        /// 四线组
        /// </summary>
        [Description("四线组")]
        public string FourLineGroup { get; set; }

        /// <summary>
        /// 分类
        /// </summary>
        [Description("分类")]
        public string Classify { get; set; }

        /// <summary>
        /// 物料描述
        /// </summary>
        [Description("物料描述")]
        public string MaterialDes { get; set; }

        /// <summary>
        /// 合同号
        /// </summary>
        [Description("合同号")]
        public string ContractNo { get; set; }

        /// <summary>
        /// 过账状态
        /// </summary>
        [Description("过账状态")]
        public bool IsPost { get; set; }

        /// <summary>
        /// 过账时间
        /// </summary>
        [Description("过账时间")]
        public DateTime? PostTime { get; set; }

        /// <summary>
        /// 过账用户
        /// </summary>
        [Description("过账用户")]
        public string PostUser { get; set; }

        /// <summary>
        /// 过账错误消息
        /// </summary>
        [Description("过账错误消息")]
        public string PostErrMsg { get; set; }

        /// <summary>
        /// 过账消息
        /// </summary>
        [Description("过账消息")]
        public string PostMsg { get; set; }

        /// <summary>
        /// 交货单号
        /// </summary>
        [Description("交货单号")]
        public string DeliveryNo { get; set; }

        /// <summary>
        /// 处理状态
        /// </summary>
        [Description("处理状态")]
        public string HandleStatus { get; set; }

        /// <summary>
        /// 仓库代码
        /// </summary>
        [Description("仓库代码")]
        public string StoreCode { get; set; }

        /// <summary>
        /// 客户代码
        /// </summary>
        [Description("客户代码")]
        public string CustomerCode { get; set; }

        /// <summary>
        /// 客户名称
        /// </summary>
        [Description("客户名称")]
        public string CustomerName { get; set; }

        /// <summary>
        /// 客户地址
        /// </summary>
        [Description("客户地址")]
        public string CustomerAddress { get; set; }

        /// <summary>
        /// 结算地址
        /// </summary>
        [Description("结算地址")]
        public string SettlementAdd { get; set; }
    }
}