using SqlSugar;
using HZ.Core.Http;
using HZ.Core.Office;
using HZ.WMS.Application;
using HZ.WMS.Application.MD;
//
using HZ.WMS.Application.MM;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.MM;
using HZ.WMS.Entity.MM.ViewModel;
using HZ.WMS.Entity.Sys;
using HZ.WMS.WebAPI.Controllers;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Web;
using System.Web.Http;

namespace HZ.WMS.WebAPI.Areas.MM.Controllers
{
    /// <summary>
    /// 盘点计划
    /// </summary>
    public class MM_TakeStockPlanController : ApiBaseController
    {
        private MM_TakeStockPlanApp _app = new MM_TakeStockPlanApp();

        private MD_LabelTempleteApp _labelTemplateApp = new MD_LabelTempleteApp();


        #region 查询分页列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="pagination"></param>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]string keyword)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetPageList(page, x => string.IsNullOrEmpty(keyword)
                        || x.DocNum.Contains(keyword)
                        || x.PUser.Contains(keyword)
                        || x.Remark.Contains(keyword))?.ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 添加
        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Add([FromBody]MM_TakeStockPlan entity)
        {
            var result = new ResponseData();
            try
            {
                Sys_User currLoginUser = GetCurrentUser();
                entity.CUser = currLoginUser.LoginAccount;
                entity.MUser = currLoginUser.LoginAccount;
                entity.CTime = DateTime.Now;
                entity.MTime = DateTime.Now;
                result.Data = _app.Insert(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取详情
        /// <summary>
        /// 获取详情
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetEntity([FromUri]string ID)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.GetEntityByKey(ID);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Update([FromBody]MM_TakeStockPlan entity)
        {
            var result = new ResponseData();
            try
            {
                entity.MUser = GetCurrentUser().LoginAccount;
                entity.MTime = DateTime.Now;
                result.Data = _app.Update(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除（包括批量）

        /// <summary>
        /// 删除
        /// </summary>
        /// <returns></returns>
        [HttpDelete]
        public IHttpActionResult Delete([FromBody]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                //主从表级联删除，需要事务
                result.Data = _app.DeleteParentAndChildren(ids, GetCurrentUser().LoginAccount);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        //#region 标签打印

        ///// <summary>
        ///// 删除
        ///// </summary>
        ///// <returns></returns>
        //[HttpGet]
        //[AllowAnonymous]
        //public IHttpActionResult Print([FromUri] string DocNum)
        //{
        //    var result = new ResponseData();
        //    try
        //    {
        //        MD_LabelTemplete template = _labelTemplateApp.GetFirstEntity(x => x.TempleteType == 12);     //采购标签
        //        // Create a report instance.
        //        DevExpress.XtraReports.UI.XtraReport report =
        //            DevExpress.XtraReports.UI.XtraReport.FromFile(HttpContext.Current.Server.MapPath("~/") + ConfigurationManager.AppSettings["LabelTemplateUploadPath"]?.ToString() + template.TempleteFile, true);

        //        DevExpress.DataAccess.Sql.SqlDataSource dataSource = report.DataSource as DevExpress.DataAccess.Sql.SqlDataSource;
        //        DevExpress.DataAccess.Sql.QueryParameter parameter = new DevExpress.DataAccess.Sql.QueryParameter()
        //        {
        //            Name = "TakeStockDocNum",
        //            Type = typeof(string),
        //            Value = DocNum
        //        };
        //        dataSource.Queries[0].Parameters[0] = parameter;
        //        dataSource.Fill();

        //        return base.PrintToPDF(report);
        //    }
        //    catch (Exception ex)
        //    {
        //        result.Code = (int)WMSStatusCode.UnHandledException;
        //        result.Message = ex.InnerException?.Message ?? ex.Message;
        //    }
        //    return Json(result);


        //}

        //#endregion

        #region 标签打印

        /// <summary>
        /// 标签打印
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult Print([FromUri]string[] docNums)
        {
            var result = new ResponseData();
            try
            {
                string templateCode = "盘点计划.repx";

                if (docNums == null || docNums.Length == 0 || string.IsNullOrEmpty(templateCode))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                }
                else
                {
                    // Create a report instance.
                    DevExpress.XtraReports.UI.XtraReport report =
                        DevExpress.XtraReports.UI.XtraReport.FromFile(HttpContext.Current.Server.MapPath("~/") + ConfigurationManager.AppSettings["LabelTemplateUploadPath"]?.ToString() + templateCode, true);

                    DevExpress.DataAccess.Sql.SqlDataSource dataSource = report.DataSource as DevExpress.DataAccess.Sql.SqlDataSource;

                    DevExpress.DataAccess.Sql.QueryParameter parameter = new DevExpress.DataAccess.Sql.QueryParameter()
                    {
                        Name = "docNum",
                        Type = typeof(List<string>),
                        Value = docNums.ToList()
                    };
                    dataSource.Queries[0].Parameters[0] = parameter;

                    return base.PrintToPDF(report);
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion


        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <param name="fromTime"></param>
        /// <param name="toTime"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPage([FromUri]Pagination page, [FromUri]string keyword, [FromUri]DateTime[] dateRangeValue, [FromUri]int Status)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateRangeValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];
                //if (!string.IsNullOrEmpty(fromTime))
                //{
                //    query = _app.GetPageList(page, x => x.CTime >= Convert.ToDateTime(fromTime).Date
                //    && (string.IsNullOrEmpty(keyword)
                //        || x.DocNum.Contains(keyword)
                //        || x.PUser.Contains(keyword)
                //        || x.Remark.Contains(keyword)));
                //    if (Status > 0)
                //    {
                //        query = _app.GetPageList(page, x => x.CTime >= Convert.ToDateTime(fromTime).Date
                //        && (string.IsNullOrEmpty(keyword)
                //        || x.DocNum.Contains(keyword)
                //        || x.PUser.Contains(keyword)
                //        || x.Remark.Contains(keyword)) && x.Status == Status);
                //    }
                //}
                //else
                //{
                //    return GetPageList(page, keyword);
                //}

                //if (!string.IsNullOrEmpty(toTime))
                //{
                //    query = _app.GetPageList(page, x => x.CTime >= Convert.ToDateTime(fromTime).Date && x.CTime < Convert.ToDateTime(toTime).Date.AddDays(1)
                //        && (string.IsNullOrEmpty(keyword)
                //        || x.DocNum.Contains(keyword)
                //        || x.PUser.Contains(keyword)
                //        || x.Remark.Contains(keyword)));
                //    if (Status > 0)
                //    {
                //        query = _app.GetPageList(page, x => x.CTime >= Convert.ToDateTime(fromTime).Date && x.CTime < Convert.ToDateTime(toTime).Date.AddDays(1)
                //        && (string.IsNullOrEmpty(keyword)
                //        || x.DocNum.Contains(keyword)
                //        || x.PUser.Contains(keyword)
                //        || x.Remark.Contains(keyword)) && x.Status == Status);
                //    }
                //}
                var itemsData = _app.GetPageList(page, x => (string.IsNullOrEmpty(keyword)
                                                             || x.DocNum.Contains(keyword)
                                                             || x.PUser.Contains(keyword)
                                                             || x.Remark.Contains(keyword)) && x.CTime >= fromTime && x.CTime <= toTime
                                                            && (Status == 0 || x.Status == Status));
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }


        /// <summary>
        /// 获取系统生成的单号
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetDocNum()
        {
            var result = new ResponseData();
            try
            {
                result.Data = base.GenerateDocNum(MM_FixedNumDef.DocType, MM_FixedNumDef.MM_TakeStockPlan);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }


        /// <summary>
        /// 删除
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult FinishTakeStockPlan([FromBody]FinishPlanDto dto)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.FinishPlan(dto.PlanID, GetCurrentUser().LoginAccount);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 根据单号获取实体
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetEntityByDocNum([FromUri]string docNum)
        {
            var result = new ResponseData();
            try
            {
                var model = _app.GetEntityByDocNum(docNum);
                result.Data = model;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }


        /// <summary>
        /// 复核
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult ReviewTakeStockPlan([FromBody]ReviewPlanDto dto)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.ReviewPlan(dto.Data, GetCurrentUser().LoginAccount);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 确认
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult ConfirmTakeStockPlan([FromBody]ReviewPlanDto dto)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.ConfirmPlan(dto.Data, GetCurrentUser().LoginAccount);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #region 导出

        /// <summary>
        /// 
        /// </summary>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] string keyword, [FromUri] DateTime[] dateTimes, [FromUri] int state)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateTimes);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];
                // var itemsData = _app.GetList().Where(t => string.IsNullOrEmpty(keyword)).ToList();
                var itemsData = _app.GetListView(keyword, fromTime, toTime, state).ToList();
                List<ExcelColumn<MM_TakeStockPlan_View>> columns = ExcelService.FetchDefaultColumnList<MM_TakeStockPlan_View>();
                string[] ignoreField = new string[] { "Status", "RegionCode", "RegionName" , "BinLocationCode", "BinLocationName"};

                List<ExcelColumn<MM_TakeStockPlan_View>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<MM_TakeStockPlan_View> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                //columns.ForEach((column) =>
                //{
                //    if (column.ColumnName == "Status")
                //    {
                //        column.Formattor = ExcelExportFormatter.StatusFormatter_PO003;
                //    }
                //});

                return ExportToExcelFile<MM_TakeStockPlan_View>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }
        #endregion

        public class ReviewPlanDto
        {
            public List<MM_TakeStockScan> Data { get; set; }
        }
        public class FinishPlanDto
        {
            public string PlanID { get; set; }
        }
    }
}

