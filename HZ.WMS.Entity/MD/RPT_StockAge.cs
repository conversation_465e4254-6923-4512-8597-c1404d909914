using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.MD
{
    public class RPT_StockAge
    {
        public Int64? RowID { get; set; }
        public Int32? TotalCount { get; set; }
        public string StockID { get; set; }
        public string BarCode { get; set; }
        public string ItemCode { get; set; }
        public string ItemName { get; set; }
        public string ItmsGrpCode { get; set; }
        public string ItmsGrpName { get; set; }
        public string RegionCode { get; set; }
        public string RegionName { get; set; }
        public string BinLocationCode { get; set; }
        public string BinLocationName { get; set; }
        public decimal? Qty { get; set; }
        public string Unit { get; set; }
        public string SupplierCode { get; set; }
        public string SupplierName { get; set; }
        public string BoxBarCode { get; set; }
        public string BatchNum { get; set; }
        public string SupplierBatch { get; set; }
        public DateTime? PTime { get; set; }
        public string Remark { get; set; }
        public string CUser { get; set; }
        public DateTime? CTime { get; set; }
        public Int32? AgeQty { get; set; }
        public Int32? StockAge { get; set; }

        public Int32? productType { get; set; }
    }
}
