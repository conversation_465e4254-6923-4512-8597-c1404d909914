using System;
using System.ComponentModel;
using SqlSugar;

namespace HZ.WMS.Entity.Produce
{
    /// <summary>
    /// 生产报工明细
    /// </summary>
    public class Produce_ReportDetail : BaseEntity
    {
        /// <summary>
        /// 主键编号
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 父ID
        /// </summary>
        [Description("父ID")]
        public string Pid { get; set; }

        /// <summary>
        /// 物料编码
        /// </summary>
        [Description("物料编码")]
        public string MaterialCode { get; set; }

        /// <summary>
        /// 物料描述
        /// </summary>
        [Description("物料描述")]
        public string MaterialDesc { get; set; }

        /// <summary>
        /// 工作中心名称
        /// </summary>
        [Description("工作中心名称")]
        public string WorkCenterName { get; set; }

        /// <summary>
        /// 工作中心编码
        /// </summary>
        [Description("工作中心编码")]
        public string WorkCenterCode { get; set; }

        /// <summary>
        /// 人工工时
        /// </summary>
        [Description("人工工时")]
        public decimal? LaborHour { get; set; }

        /// <summary>
        /// 机器工时
        /// </summary>
        [Description("机器工时")]
        public decimal? MachineHour { get; set; }

        /// <summary>
        /// 可控制费工时
        /// </summary>
        [Description("可控制费工时")]
        public decimal? ControllerCostLaborHour { get; set; }

        /// <summary>
        /// 一级分摊工时
        /// </summary>
        [Description("一级分摊工时")]
        public decimal? FirstLevelSharedWorkHour { get; set; }

        /// <summary>
        /// 确认时间
        /// </summary>
        [Description("确认时间")]
        public DateTime? ConfirmTime { get; set; }

        /// <summary>
        /// 状态 1正常 2等待报工
        /// </summary>
        [Description("状态")]
        public int? Status { get; set; }

        /// <summary>
        /// 员工编号
        /// </summary>
        [Description("员工编号")]
        public string EmployeeCode { get; set; }

        /// <summary>
        /// 员工名称
        /// </summary>
        [Description("员工名称")]
        public string EmployeeName { get; set; }

        /// <summary>
        /// 站点名称
        /// </summary>
        [Description("站点名称")]
        public string StationName { get; set; }

        /// <summary>
        /// 站点代码
        /// </summary>
        [Description("站点代码")]
        public string StationCode { get; set; }

        /// <summary>
        /// 站点序号
        /// </summary>
        [Description("站点序号")]
        public decimal? StationSort { get; set; }
    }
} 