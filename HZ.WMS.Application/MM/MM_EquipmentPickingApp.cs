using SqlSugar;
using HZ.WMS.Application.MD;
using HZ.WMS.Application.SAP;
using HZ.WMS.Application.Sys;
using HZ.WMS.Entity;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.MM;
using HZ.WMS.Entity.MM.ViewModel;
using HZ.WMS.Entity.SAP;
using HZ.WMS.Entity.Sys;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Application.MM
{
    /// <summary>
    /// 设备领料
    /// </summary>
    public class MM_EquipmentPickingApp : BaseApp<MM_EquipmentPicking>
    {
        #region 初始化

        BaseApp<BaseEntity> _baseApp = new BaseApp<BaseEntity>();
        MD_StockApp _stockApp = new MD_StockApp();

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public MM_EquipmentPickingApp() : base(){ }

        #endregion

        #region PC

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="opUser"></param>
        /// <param name="error_message"></param>
        public bool Deletes(string[] ids, string opUser, out string error_message)
        {
            bool bDeleted = true;
            List<MM_EquipmentPicking> entities = GetListByKeys(ids);
            if (CheckDelete(entities, out error_message))
            {
                try
                {
                    DbContext.Ado.BeginTran();

                    Delete(entities, opUser);

                    DbContext.Ado.CommitTran();//提交事务
                }
                catch (Exception ex)
                {
                    bDeleted = false;
                    error_message = ex.Message;
                    if(DbContext.Ado.IsAnyTran())//如果事务开启，执行下部操作
                        DbContext.Ado.RollbackTran();//失败回滚
                }
            }
            else
            {
                bDeleted = false;
            }
            return bDeleted;
        }

        /// <summary>
        /// 删除校验
        /// </summary>
        /// <param name="entities"></param>
        /// <param name="error_message"></param>
        /// <returns></returns>
        private bool CheckDelete(List<MM_EquipmentPicking> entities, out string error_message)
        {
            bool isPass = true;
            error_message = "";
            foreach (MM_EquipmentPicking po in entities)
            {
                if (po.IsPosted == true)
                {
                    error_message = "ui.Message.PODeletePostingWarning"; // "已过账数据不允许删除!";
                    isPass = false;
                    break;
                }

                //    // 入库校验
                //    //if(_stockApp.CheckStockLocked(po.ItemCode, po.OutBinLocationCode, po.OutRegionCode))
                //    //{
                //    //    //throw new Exception("Common.error", new Exception("Common.StockIsLocked"));
                //    //    error_message = "Common.StockIsLocked"; //库存锁定，不允许出入库
                //    //    return false;
                //    //}
           }

                return isPass;
        }


        #endregion

        #region 审核

        /// <summary>
        /// 审核
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="opUser"></param>
        /// <param name="error_message"></param>
        public bool Audits(string[] ids, string opUser, out string error_message, out string type)
        {
            error_message = "";
            type = "";

            var entities = GetListByKeys(ids);
            foreach (var x in entities)
            {
                if (x.EquipmentPickingStatus == "2")//通过
                {
                    error_message = "请选择状态为“未复核”的信息进行审核"; // "已过账数据不允许删除!";
                    type = "1";
                    return false;
                }

                ////出库校验
                //string stockMsg = "";
                //if (!_stockApp.ValidateStockOut(x.ItemCode, x.BinLocationCode, x.EquipmentPickingQty, out stockMsg, null, null))
                //{
                //    error_message = stockMsg;
                //    type = "1";
                //    return false;
                //}
            }

            try
            {
                foreach (var x in entities)
                {
                    x.EquipmentPickingStatus = "2";//1:未复合 2：通过 3：取消
                    x.MUser = opUser;
                    x.MTime = DateTime.Now;
                }
                DbContext.Ado.BeginTran();

                Update(entities);

                DbContext.Ado.CommitTran();//提交事务

                #region 是否自动过账

                Sys_SwithConfigApp _switchApp = new Sys_SwithConfigApp();
                if (_switchApp.IsMMEquipmentPickingAutoPost)
                {
                    string postMsg = "";
                    bool bpost = DoPost(entities, opUser, out postMsg);
                    if (!bpost)
                    {
                        error_message = "审核成功," + postMsg;
                        type = "2";
                        return false;
                    }
                    else
                    {
                        error_message = "审核并" + postMsg;
                    }
                }

                #endregion

                if (string.IsNullOrEmpty(error_message))
                    error_message = "审核成功";
                return true;
            }
            catch (Exception ex)
            {
                if (DbContext.Ado.IsAnyTran())
                    DbContext.Ado.RollbackTran();//失败回滚
                error_message = ex.Message;
                type = "1";
                return false;
            }
        }

        #endregion

        #region 取消

        /// <summary>
        /// 取消
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="opUser"></param>
        /// <param name="error_message"></param>
        public bool Rejects(string[] ids, string opUser, out string error_message)
        {
            error_message = "";

            var entities = GetListByKeys(ids);
            foreach (var x in entities)
            {
                if (x.EquipmentPickingStatus == "1")//1:未复合 2：通过 3：驳回
                {
                    error_message = "请选择状态为“通过”的信息进行驳回"; // "已过账数据不允许删除!";
                    return false;
                }
            }

            try
            {
                foreach (MM_EquipmentPicking mq in entities)
                {
                    mq.EquipmentPickingStatus = "3";//1:未复合 2：通过 3：驳回
                    mq.MUser = opUser;
                    mq.MTime = DateTime.Now;
                }
                DbContext.Ado.BeginTran();

                Update(entities);

                DbContext.Ado.CommitTran();//提交事务

                if (string.IsNullOrEmpty(error_message))
                    error_message = "取消成功";
                return true;

            }
            catch (Exception ex)
            {
                if (DbContext.Ado.IsAnyTran())
                    DbContext.Ado.RollbackTran();//失败回滚
                error_message = ex.Message;
                return false;
            }
        }

        /// <summary>
        /// 驳回校验
        /// 
        /// </summary>
        /// <param name="entities"></param>
        /// <param name="error_message"></param>
        /// <returns></returns>
        private bool CheckReject(List<MM_EquipmentPicking> entities, out string error_message)
        {
            bool isPass = true;
            error_message = "";
            foreach (MM_EquipmentPicking po in entities)
            {
                if (po.EquipmentPickingStatus == "1")//1:未复合 2：通过 3：驳回
                {
                    error_message = "请选择状态为“通过”的信息进行驳回"; // "已过账数据不允许删除!";
                    isPass = false;
                    break;
                }
            }

            return isPass;
        }


        #endregion

        #region 过账

        /// <summary>
        /// 过账
        /// </summary>
        /// <param name="entities">设备领料集合</param>
        /// <param name="opUser">当前过账用户</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool DoPost(List<MM_EquipmentPicking> entities, string opUser, out string error_message)
        {
            error_message = "";
            Sys_SAPCompanyInfoApp _SAPCompanyInfoApp = new Sys_SAPCompanyInfoApp();
            try
            {
                //多条数据后根据单号进行分组
                var mainInfoList = entities.GroupBy(g => new
                {
                    g.DocNum
                }).Select(q => new
                {
                    DocNum = q.Key.DocNum
                });

                //按单号分组
                foreach (var mainInfo in mainInfoList)
                {
                    var list = new List<ZFGWMS008>();
                    int Line = 0;
                    var conditionList = entities.Where(x => x.DocNum == mainInfo.DocNum)?.ToList();
                    conditionList.ForEach(x=> 
                    {
                        Line = Line + 1;
                        x.Line = Line;
                        var y = new ZFGWMS008();
                        y.ZNUM = Line; //condition.BaseLine;领料单行号
                        y.MATNR = x.ItemCode;//
                        y.WERKS = x.FactoryCode ?? "2002";//eq.FactoryCode
                        y.MENGE = x.EquipmentPickingQty;
                        y.MEINS = x.Unit;
                        y.LGORT = x.WhsCode;
                        y.BWART = x.MovementType;                                               //移动类型
                        y.KOSTL = (y.BWART == "Z19" || y.BWART == "Z23") ? x.CostCenter : "";   //Z19 非生产性领料
                        y.SAKTO = y.BWART == "Z23" ? x.LedgerAccount : "";                      //Z23 其他领料
                        y.AUFNR = x.Order ?? "";
                        y.ANLN1 = x.AssetCard ?? "";
                        y.SOBKZ = x.SpecialInventory;
                        y.KDAUF = y.SOBKZ == "E" ? x.SalesOrderNum : "";//如果特殊库存等于E，则必填
                        y.KDPOS = y.SOBKZ == "E" ? x.SalesOrderLine : 0;//如果特殊库存等于E，则必填
                        y.BWTAR = x.EvaluationType ?? "";//如果物料启用分割评估，此字段必填
                        y.SGTXT = x.Remark;
                        list.Add(y);
                    });

                    bool ispost = false;
                    DateTime time = DateTime.Now;
                    //string rtnErrMsg = "";
                    //purchaseReceipt.CompanyCode 公司代码
                    //查询Sap账期时间
                    DateTime ManualPostTime = new SAPApp().GetSAPpostTime(Convert.ToDateTime(conditionList[0].ManualPostTime));
                    List<SAPRETURN> saplist = _SAPCompanyInfoApp.ZFGWMS008(conditionList[0].CompanyCode ?? "001", mainInfo.DocNum, "", ManualPostTime, list, out ispost, out error_message);
                    if (saplist != null && saplist.Count > 0)
                    {
                        //List<MM_EquipmentPicking> queryList = new List<MM_EquipmentPicking>();
                        foreach (var sap in saplist)
                        {
                            //&& x.BaseNum == sap.basenum && x.BaseLine == sap.baseline
                            var query = conditionList.Where(x => x.DocNum == mainInfo.DocNum&&x.Line==sap.line ).ToList().FirstOrDefault();
                            if (query != null)
                            {
                                query.IsPosted = true;
                                query.PostUser = opUser;
                                query.PostTime = time;
                                query.MUser = opUser;
                                query.MTime = time;
                                query.SapDocNum = sap.sapDocNum;
                                query.SapLine = sap.sapline;
                                query.ManualPostTime = ManualPostTime;
                                query.SAPmark = "S";

                                if (Update(query) >0 )//更新成功后更新库存
                                {
                                    string stockMsg = string.Empty;
                                    if (!_stockApp.StockOut(query.ItemCode, query.BinLocationCode,query.EquipmentPickingQty, opUser, out stockMsg))
                                    {
                                        error_message = stockMsg;
                                        //return false;
                                    }
                                }
                                //queryList.Add(query);
                            }
                        }

                        //更新
                        //Update(queryList);
                    }
                    else
                    {
                        var query = conditionList.Where(x => x.DocNum == mainInfo.DocNum).ToList().ToList();
                        foreach(var x in query)
                        {
                            x.SAPmark = "E";
                            x.SAPmessage = error_message;
                        };
                        Update(query);

                        error_message = "单号：" + mainInfo.DocNum + "，过账失败，失败原因：" + error_message;
                        return false ;
                    }
                }
                if (string.IsNullOrEmpty(error_message))
                {
                    error_message = "过账成功";
                }
                return true;
            }
            catch (Exception ex)
            {
                error_message = ex.Message;
                return false;
            }
        }


        #endregion

        #region 作废

        /// <summary>
        /// 作废
        /// </summary>
        /// <param name="DocNums">单号数组</param>
        /// <param name="opUser">当前用户</param>
        /// <param name="error_message">错误消息</param>
        public bool Cancel(string[] ids, string opUser, out string error_message)
        {
            error_message = "";


            var flag = GetListByKeys(ids);
            if (flag.Any(x => x.IsPosted == true))
            {
                error_message = "请选择未过账数据进行作废";
                return false;
            }
            try
            {
                flag.ForEach(x =>
                {
                    x.IsCancel = true;
                    x.MUser = opUser;
                    x.MTime = DateTime.Now;
                });
                DbContext.Ado.BeginTran();

                Update(flag);

                DbContext.Ado.CommitTran();//提交事务

                if (string.IsNullOrEmpty(error_message))
                    error_message = "作废成功";
                return true;
            }
            catch (Exception ex)
            {
                if (DbContext.Ado.IsAnyTran())
                    DbContext.Ado.RollbackTran();//失败回滚
                error_message = ex.Message;
                return false;
            }
        }

        #endregion

        #endregion

        #region Mobile

        #region 校验序列号、库存

        /// <summary>
        /// 校验序列号、库存
        /// </summary>
        /// <param name="ItemCode">物料编号</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool CheckDate(string ItemCode, out string error_message)
        {
            error_message = "";
            var query = _stockApp.GetList(x => x.ItemCode == ItemCode && (x.SaleNum == "" || x.SaleNum == null) && (x.SupplierCode == "" || x.SupplierCode == null) && x.Qty>0).ToList().FirstOrDefault();
            if (query==null)
            {
                error_message = "物料编号["+ ItemCode + "]没有库存";
                return false;
            }
            return true;

        }

        #endregion

        #region 查询库存信息

        /// <summary>
        /// 查询库存信息
        /// </summary>
        /// <param name="ItemCode">物料编号</param>
        /// <returns></returns>
        public MD_StockForEquipmentPicking_View GetStockInfo(string ItemCode,out string error_message)
        {
            error_message = "";
            var query = DbContext.Queryable<MD_StockForEquipmentPicking_View>().Where(x => x.ItemCode == ItemCode).ToList().FirstOrDefault();
            if (query == null)
            {
                error_message = "物料编号[" + ItemCode + "],库存数量不足或者检查设备领料信息是否存在未过账记录";
            }
            return query;
        }

        #endregion

        #region 保存

        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="MoList">设备领料集合</param>
        /// <param name="user">用户信息</param>
        /// <param name="error_message">错误消息返回</param>
        /// <param name="ManualPostTime">手动过账时间</param>
        /// <param name="type">类型 1:提交失败 2：过账失败</param>
        public bool Save(List<MM_EquipmentPicking> MoList, DateTime ManualPostTime, string user, out string error_message/*, out string type*/)
        {
            error_message = "提交成功";
            //type = "";
            try
            {
                string DocNum = _baseApp.GetNewDocNum(DocType.MM, DocFixedNumDef.MM_EquipmentPicking);

                //转入仓库编码
                var WhsCodes = MoList.Select(x => x.WhsCode).Distinct().ToArray();
                var whscodes = new MD_BinLocationApp().GetList(x => WhsCodes.Contains(x.WhsCode))?.ToList();

                //转入仓库编码
                var ItemCode = MoList.Select(x => x.ItemCode.Trim()).Distinct().ToArray();
                var itemcodes = DbContextForSAP.Queryable<XZ_SAP_MARC>().Where(x => ItemCode.Contains(x.MATNR))?.ToList();

                foreach (MM_EquipmentPicking x in MoList)
                {
                    x.ManualPostTime = ManualPostTime;
                    x.EquipmentPickingStatus = "1";//1:未复合 2：通过 3：驳回
                    if (string.IsNullOrEmpty(x.DocNum))
                        x.DocNum = DocNum;
                    x.IsDelete = false;
                    x.IsPosted = false;
                    x.IsCancel = false;
                    x.CUser = user;
                    x.CTime = DateTime.Now;
                    x.FactoryCode = "2002";
                    x.CompanyCode = "001";
                    x.ItemCode = x.ItemCode.Trim();
                    if (string.IsNullOrEmpty(x.RegionCode))
                    {
                        var WHS = whscodes.Where(v => v.WhsCode == x.WhsCode)?.FirstOrDefault();
                        if (WHS == null)
                        {
                            error_message = "仓库编号【" + x.WhsCode + "】无效!";
                            return false;
                        }
                        x.WhsName = WHS.WhsName;
                        x.RegionCode = WHS.RegionCode;
                        x.RegionName = WHS.RegionName;
                        x.BinLocationCode = WHS.BinLocationCode;
                        x.BinLocationName = WHS.BinLocationName;
                    }

                    if (string.IsNullOrEmpty(x.ItemCode))
                    {

                        error_message = "物料编号【" + x.ItemCode + "】无效!";
                        return false;

                    }

                    if (string.IsNullOrEmpty(x.ItemName))
                    {
                        var item = itemcodes.Where(v => v.MATNR == x.ItemCode)?.FirstOrDefault();
                        if (item == null)
                        {
                            error_message = "物料编号【" + x.ItemCode + "】无效!";
                            return false;
                        }

                        x.ItemName = item.MAKTX;
                    }
                }

                DbContext.Ado.BeginTran();//开启事务

                //批量插入
                Insert(MoList);

                DbContext.Ado.CommitTran();//提交事务

                return true;
            }
            catch (Exception ex)
            {
                if (DbContext.Ado.IsAnyTran())
                    DbContext.Ado.RollbackTran();//失败回滚

                error_message = ex.Message;
                //type = "1";
                return false;
            }
        }

        #endregion

        #endregion

    }
}
