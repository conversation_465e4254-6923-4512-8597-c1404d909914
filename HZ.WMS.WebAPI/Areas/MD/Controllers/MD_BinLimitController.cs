using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;
using HZ.Core.Http;
using HZ.Core.Office;
using HZ.WMS.Application;
using HZ.WMS.Application.MD;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.Sys;
using HZ.WMS.Entity.Sys.ViewModel;
using HZ.WMS.WebAPI.Controllers;

namespace HZ.WMS.WebAPI.Areas.MD.Controllers
{
    /// <summary>
    /// 库位限制
    /// </summary>
    public class MD_BinLimitController : ApiBaseController
    {
        private MD_BinLimitApp _app = new MD_BinLimitApp();

        #region 查询分页列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <param name="selectedModule"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]string keyword, [FromUri] string selectedModule)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetPageList(page, x =>
                (string.IsNullOrEmpty(keyword)
                || x.MenuCode.Contains(keyword)
                || x.MenuName.Contains(keyword)
                || x.RegionCode.Contains(keyword)
                || x.RegionName.Contains(keyword)
                || x.BinLocationCode.Contains(keyword)
                || x.BinLocationName.Contains(keyword)) && string.IsNullOrEmpty(selectedModule) || x.MenuCode == selectedModule).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取已选择模块已有的库位
        /// <summary>
        /// 获取已选择模块已有的库位
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetExistBinFromBinLocation([FromUri]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetExistBinFromBinLocation(ids);
                result.Data = itemsData;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 根据ResourceID(即BinLimit表中的MenuCode)查询已存在的库位限制
        /// <summary>
        /// 根据MenuCode查询已存在的库位限制
        /// </summary>
        /// <param name="menuCode"></param>
        /// <returns></returns>

        [HttpGet]
        public IHttpActionResult GetBinLimitByMenuCode([FromUri]string menuCode)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetBinLimitByMenuCode(menuCode);
                result.Data = itemsData;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 添加
        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Add([FromBody]MD_BinLimit entity)
        {
            var result = new ResponseData();
            try
            {
                Sys_User currLoginUser = GetCurrentUser();
                entity.CUser = currLoginUser.LoginAccount;
                entity.MUser = currLoginUser.LoginAccount;
                entity.CTime = DateTime.Now;
                entity.MTime = DateTime.Now;
                result.Data = _app.Insert(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取详情
        /// <summary>
        /// 获取详情
        /// </summary>
        /// <param name="ID"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetEntity([FromUri]string ID)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.GetEntityByKey(ID);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Update([FromBody]MD_BinLimit entity)
        {
            var result = new ResponseData();
            try
            {
                entity.MUser = GetCurrentUser().LoginAccount;
                entity.MTime = DateTime.Now;
                result.Data = _app.Update(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除（包括批量）

        /// <summary>
        /// 删除
        /// </summary>
        /// <returns></returns>
        [HttpDelete]
        public IHttpActionResult Delete([FromBody]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.DeleteByKeys(ids, GetCurrentUser().LoginAccount);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion


        #region 保存菜单限制库位设置
        /// <summary>
        /// 保存菜单限制库位设置
        /// </summary>
        /// <param name="rsrpBase">id:角色ID ids:库位ID {id:'1',ids:[1,2,3]}</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult SaveBinLimitSetting([FromBody]BatchSettingRequestParmsBase rsrpBase)
        {
            var result = new ResponseData();
            try
            {
                Sys_User currLoginUser = GetCurrentUser();
                string error_message = "";
                bool bExec = _app.SaveBinLimitSetting(rsrpBase.id, rsrpBase.ids, currLoginUser.LoginAccount, out error_message);
                if (!bExec)
                {
                    result.Message = error_message;
                    result.Code = (int)WMSStatusCode.Failed;
                }

            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        /// <summary>
        /// 新增库位限制
        /// </summary>
        /// <param name="rsrpBase"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult AddBinLimitSetting([FromBody]BatchSettingRequestParmsBase rsrpBase)
        {
            var result = new ResponseData();
            try
            {
                Sys_User currLoginUser = GetCurrentUser();
                string error_message = "";
                bool bExec = _app.AddBinLimitSetting(rsrpBase.id, rsrpBase.ids, currLoginUser.LoginAccount, out error_message);
                if (!bExec)
                {
                    result.Message = error_message;
                    result.Code = (int)WMSStatusCode.Failed;
                }

            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion


        #region 导出

        /// <summary>
        /// 
        /// </summary>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] string keyword, [FromUri]string selectedModule)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetList(x => (string.IsNullOrEmpty(keyword)
                || x.MenuCode.Contains(keyword)
                || x.MenuName.Contains(keyword)
                || x.RegionCode.Contains(keyword)
                || x.RegionName.Contains(keyword)
                || x.BinLocationCode.Contains(keyword)
                || x.BinLocationName.Contains(keyword)) && string.IsNullOrEmpty(selectedModule) || x.MenuCode == selectedModule).ToList();
                List<ExcelColumn<MD_BinLimit>> columns = ExcelService.FetchDefaultColumnList<MD_BinLimit>();
                string[] ignoreField = new string[] {
                    "IsDelete", "DTime", "DUser","limitID","Remark","MenuCode",
                    "MUser",
                    "MTime",};

                List<ExcelColumn<MD_BinLimit>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<MD_BinLimit> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                columns.ForEach((column) =>
                {
                    if (column.ColumnName == "IsPosted")
                    {
                        column.Formattor = ExcelExportFormatter.IsOrNoFormatterForBoolean;
                    }
                });

                return ExportToExcelFile<MD_BinLimit>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }
        #endregion
    }
}
