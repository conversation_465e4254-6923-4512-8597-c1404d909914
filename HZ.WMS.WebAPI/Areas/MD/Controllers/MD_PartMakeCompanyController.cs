using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;
using HZ.Core.Http;
using HZ.Core.Office;
using HZ.WMS.Application;
using HZ.WMS.Application.MD;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.MD.Import;
using HZ.WMS.WebAPI.Controllers;

namespace HZ.WMS.WebAPI.Areas.MD.Controllers
{
    /// <summary>
    /// 安全部件制造单位编号
    /// </summary>
    public class MD_PartMakeCompanyController : ApiBaseController
    {
        private MD_PartMakeCompanyApp _app = new MD_PartMakeCompanyApp();

        #region 查询分页列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="page">分页</param>
        /// <param name="keyword">参数</param>
        /// <param name="StockManWay">库存管理方式</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]MD_PartMakeCompanyListReq req)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetPageList(page, x => (string.IsNullOrEmpty(req.CompanyName)
                || x.CompanyName.Contains(req.CompanyName)));
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取详情
        /// <summary>
        /// 获取详情
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetEntity([FromUri]string ID)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.GetEntityByKey(ID);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Update([FromBody]MD_PartMakeCompany entity)
        {
            var result = new ResponseData();
            try
            {
                entity.MUser = GetCurrentUser().LoginAccount;
                entity.MTime = DateTime.Now;

                result.Data = _app.Update(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 添加

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Insert([FromBody]MD_PartMakeCompany entity)
        {
            var result = new ResponseData();
            try
            {
                entity.CUser = GetCurrentUser().LoginAccount;
                entity.CTime = DateTime.Now;

                result.Data = _app.Insert(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 批量删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <returns></returns>
        [HttpDelete]
        public IHttpActionResult Delete([FromBody]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.DeleteByKeys(ids, GetCurrentUser().LoginAccount);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 导出模板

        /// <summary>
        /// 导出模板
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelModel()
        {
            var result = new ResponseData();
            try
            {
                var columns = ExcelService.FetchDefaultColumnList<MD_PartMakeCompany>();
                string modelName = "安全部件制造单位编号-导入模板";
                //排除不需要的列名称 字符串
                string[] ignoreField = new string[]
                {
                    "Id", "Remark", "IsDelete", "CUser", "CTime",
                    "MUser", "MTime", "DUser", "DTime"
                };
                var itemsData = new List<MD_PartMakeCompany>() { };
                var ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<MD_PartMakeCompany> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }

                return ExportToExcelFile<MD_PartMakeCompany>(itemsData, columns,
                    GetCurrentUser().UserName + "_" + modelName);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        /// <summary>
        /// 导出数据
        /// </summary>
        /// <param name="req">查询参数</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] MD_PartMakeCompanyListReq req)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetList(x => (string.IsNullOrEmpty(req.CompanyName) || x.CompanyName.Contains(req.CompanyName))
                    && (string.IsNullOrEmpty(req.CompanyCode) || x.CompanyCode.Contains(req.CompanyCode))
                    && (string.IsNullOrEmpty(req.SuperCompanyName) || x.SuperCompanyName.Contains(req.SuperCompanyName))).ToList();
                List<ExcelColumn<MD_PartMakeCompany>> columns = ExcelService.FetchDefaultColumnList<MD_PartMakeCompany>();
                string[] ignoreField = new string[] { "IsDelete", "DTime", "DUser", "MUser", "MTime" };

                List<ExcelColumn<MD_PartMakeCompany>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<MD_PartMakeCompany> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }

                return ExportToExcelFile<MD_PartMakeCompany>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #region 导入

        /// <summary>
        /// 导入
        /// </summary>
        /// <param name="entitys">接收的导入集合</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult ImportExcelToData([FromBody]List<MD_PartMakeCompanyImport> entitys)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                if (_app.ImportExcelToBaseData(entitys, GetCurrentUser().LoginAccount, out error_message))
                {
                    result.Data = true;
                    result.Code = (int)WMSStatusCode.Success;
                }
                else
                {
                    result.Data = false;
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
            return Json(result);
        }

        #endregion
    }
}