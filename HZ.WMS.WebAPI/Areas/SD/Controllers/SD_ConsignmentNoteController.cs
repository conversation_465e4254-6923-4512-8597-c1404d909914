using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;

//
using HZ.Core.Security;
using HZ.WMS.Application.MD;
using HZ.WMS.Application.SD;
using HZ.WMS.Entity.SD;
using HZ.WMS.Entity.Sys;
using HZ.Core.Http;
using HZ.WMS.WebAPI.Controllers;
using Newtonsoft.Json;
using HZ.Core.Vue;
using SqlSugar;
using HZ.Core.Office;
using HZ.WMS.Application;
using static HZ.WMS.Application.SD.SD_ConsignmentNoteApp;
using System.Web;
using System.Configuration;
using HZ.WMS.Entity.SD.Parameters;
using HZ.WMS.Entity.SD.ViewModel;

namespace HZ.WMS.WebAPI.Areas.SD.Controllers
{
    /// <summary>
    /// 托运单
    /// </summary>
    public class SD_ConsignmentNoteController : ApiBaseController
    {
        #region 初始化

        private SD_ConsignmentNoteApp _app = new SD_ConsignmentNoteApp();
        private SD_ConsignmentNoteDetailApp _detailapp = new SD_ConsignmentNoteDetailApp();

        #endregion

        #region 查询分页列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="page">分页信息</param>
        /// <param name="keyword">参数</param>
        /// <param name="dateValue">日期区间</param>
        /// <param name="Status">-1：全部; 0、1：未发货； 2：已发货</param>
        /// <param name="Type">0：全部 1：计划内 2：计划外</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]string keyword, [FromUri]int Status, [FromUri]int Type, [FromUri] DateTime[] dateValue)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];
                var itemsData = new List<SD_ConsignmentNote>();
                if (Type == 0)
                {
                    itemsData = _app.GetPageList(page, t => (string.IsNullOrEmpty(keyword)
                                                   || t.DocNum.Contains(keyword) || t.ShippingPlanNum.Contains(keyword)
                                                   || t.CustomerCode.Contains(keyword) || t.CustomerName.Contains(keyword)
                                                   || t.CustomerAdd.Contains(keyword) || t.CustomerRegion.Contains(keyword)
                                                   || t.LogisticsSupplierCode.Contains(keyword) || t.LogisticsSupplierName.Contains(keyword)
                                                   || t.ShippingDepar.Contains(keyword) || t.ShippingType.Contains(keyword)
                                                   || t.Shipper.Contains(keyword) || t.CarNum.Contains(keyword))
                                           && (t.CTime >= fromTime && t.CTime <= toTime)).ToList();
                }
                else
                {
                    itemsData = _app.GetPageList(page, t => (string.IsNullOrEmpty(keyword)
                                                  || t.DocNum.Contains(keyword) || t.ShippingPlanNum.Contains(keyword)
                                                  || t.CustomerCode.Contains(keyword) || t.CustomerName.Contains(keyword)
                                                  || t.CustomerAdd.Contains(keyword) || t.CustomerRegion.Contains(keyword)
                                                  || t.LogisticsSupplierCode.Contains(keyword) || t.LogisticsSupplierName.Contains(keyword)
                                                  || t.ShippingDepar.Contains(keyword) || t.ShippingType.Contains(keyword)
                                                  || t.Shipper.Contains(keyword) || t.CarNum.Contains(keyword))
                                          && (t.CTime >= fromTime && t.CTime <= toTime) && (t.Type==Type)).ToList();
                }

                List<SD_ConsignmentNote> list = new List<SD_ConsignmentNote>();
                if (Status == 2)
                {
                    list = itemsData.Where(x => x.Status == Status)?.ToList();
                    page.Total = list.Count();
                }
                else if (Status == -1)
                {
                    list = itemsData;
                }
                else
                {
                    list = itemsData.Where(x => x.Status != Status)?.ToList();
                    page.Total = list.Count();
                }
                result.Data = new ResponsePageData { total = page.Total, items = list };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 查询明细分页列表

        /// <summary>
        /// 查询明细分页列表
        /// </summary>
        /// <param name="page">分页信息</param>
        /// <param name="keyword">参数</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageDetailList([FromUri]Pagination page, [FromUri]string keyword)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _detailapp.GetPageList(page, t => t.DocNum == keyword).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 编辑根据单号查询明细信息

        /// <summary>
        /// 编辑根据单号查询明细信息
        /// </summary>
        /// <param name="keyword">参数</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetList([FromUri]string keyword)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _detailapp.GetList(t => t.DocNum == keyword).ToList();
                result.Data = itemsData;//new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="DocNums">单号数组</param>
        /// <returns></returns>
        [HttpDelete]
        public IHttpActionResult Delete([FromBody]string[] DocNums)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                bool bDelete = _app.Delete(DocNums, GetCurrentUser().LoginAccount, out error_message);
                if (!bDelete)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 导出

        /// <summary>
        /// 导出
        /// </summary>
        /// <param name="keyword">参数</param>
        /// <param name="dateTimes">日期区间</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] string keyword, [FromUri] DateTime[] dateTimes)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateTimes);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];
                var itemsData = _app.GetConsignmentNote_ViewInfo(keyword).Where(x => x.CTime >= fromTime && x.CTime <= toTime).ToList();
                List<ExcelColumn<SD_ConsignmentNote_View>> columns = ExcelService.FetchDefaultColumnList<SD_ConsignmentNote_View>();
                string[] ignoreField = new string[]
                {
                    "Status", "CustomerID", "CustomerCode", "LogisticsSupplierCode",
                    "Type","Line","BaseEntry","MTime","BaseEntry","BatchNum","ItmsGrpName",
                    "DeliverDate","Mileage","MileageRate","WeightRate","WeightUnit","CUser"
                };

                List<ExcelColumn<SD_ConsignmentNote_View>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<SD_ConsignmentNote_View> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                columns.ForEach((column) =>
                {
                    if (column.ColumnName == "IsDelete")
                    {
                        column.Formattor = ExcelExportFormatter.IsOrNoFormatterForBoolean;
                    }
                    if (column.ColumnName == "IsPosted")
                    {
                        column.Formattor = ExcelExportFormatter.IsOrNoFormatterForBoolean;
                    }
                });

                return ExportToExcelFile<SD_ConsignmentNote_View>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #region 打印

        /// <summary>
        /// 打印
        /// </summary>
        /// <param name="docNums">单号数组</param>
        /// <param name="templateCode">标签模板</param>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IHttpActionResult Print([FromUri]string[] docNums, [FromUri]string templateCode)
        {
            var result = new ResponseData();

            if (docNums == null || docNums.Length == 0 || string.IsNullOrEmpty(templateCode))
            {
                result.Code = (int)WMSStatusCode.Failed;
            }
            else
            {
                //MD_LabelTempleteApp _labelTemplateApp = new MD_LabelTempleteApp();
                //MD_LabelTemplete template = _labelTemplateApp.GetFirstEntity(x => x.Remark == templateCode);
                //if (template == null)
                //{
                //    result.Code = (int)WMSStatusCode.Failed;
                //}
                //else
                //{
                DevExpress.XtraReports.UI.XtraReport report =
                    DevExpress.XtraReports.UI.XtraReport.FromFile(HttpContext.Current.Server.MapPath("~/") + ConfigurationManager.AppSettings["LabelTemplateUploadPath"]?.ToString() + templateCode, true);
                DevExpress.DataAccess.Sql.SqlDataSource dataSource = report.DataSource as DevExpress.DataAccess.Sql.SqlDataSource;
                //switch (templateCode)
                //{
                //    case "PP_BarCodeReturn":
                //    case "PP_BarCode":
                //    case "MM_BarCode":
                DevExpress.DataAccess.Sql.QueryParameter parameter = new DevExpress.DataAccess.Sql.QueryParameter()
                {
                    Name = "docNums",
                    Type = typeof(string[]),
                    Value = docNums
                };

                dataSource.Queries[0].Parameters[0] = parameter;
                //dataSource.Fill();
                return base.PrintToPDF(report);

                //default:
                //    break;


            }
            return Json(result);
        }

        #endregion


        #region 完成

        /// <summary>
        /// 完成
        /// </summary>
        /// <param name="DocNums">手动完成</param>
        /// <returns></returns>
        [HttpGet]
        //[AllowAnonymous]
        public IHttpActionResult Finish([FromUri]string[] DocNums)
        {
            //[FromBody]string[] ids
            var result = new ResponseData();
            try
            {
                string error_message = "";
                //GetCurrentUser().LoginAccount
                bool bFinish = _app.Finish(DocNums, GetCurrentUser().LoginAccount, out error_message);
                if (!bFinish || !string.IsNullOrEmpty(error_message))
                {
                    result.Code = (int)WMSStatusCode.UnHandledException;
                    result.Message = error_message;
                }

            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 自动保存

        /// <summary>
        /// 自动保存
        /// </summary>
        /// <param name="Parameters">参数汇总</param>
        /// <returns></returns>
        //[HttpGet]
        //[AllowAnonymous]
        //public IHttpActionResult AutoSave([FromUri]string DocNum)
        //{
        //    var result = new ResponseData();
        //    try
        //    {
        //        string error_message = "";
        //        //Sys_User currentUser = GetCurrentUser();
        //        bool bSubmit = _app.AutoSave(DocNum, "admin", out error_message);

        //        if (!bSubmit || !string.IsNullOrEmpty(error_message))
        //        {
        //            result.Code = (int)WMSStatusCode.Failed;
        //            result.Message = error_message;
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        result.Code = (int)WMSStatusCode.UnHandledException;
        //        result.Message = ex.InnerException?.Message ?? ex.Message;
        //    }
        //    return Json(result);

        //}

        #endregion

        #region 手动保存

        /// <summary>
        /// 手动保存
        /// </summary>
        /// <param name="Parameters">参数汇总</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Save([FromBody]SD_ConsignmentNoteParameters Parameters)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                Sys_User currentUser = GetCurrentUser();
                bool bSubmit = _app.Save(Parameters, currentUser.LoginAccount, out error_message);

                if (!bSubmit || !string.IsNullOrEmpty(error_message))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }

        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="Parameters">参数汇总</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Update([FromBody]SD_ConsignmentNoteParameters Parameters)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                Sys_User currentUser = GetCurrentUser();
                bool bSubmit = _app.Update(Parameters, currentUser.LoginAccount, out error_message);

                if (!bSubmit || !string.IsNullOrEmpty(error_message))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }

        #endregion

        #region 获取系统生成的单号

        /// <summary>
        /// 获取系统生成的单号
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IHttpActionResult GetDocNum()
        {
            var result = new ResponseData();
            try
            {
                result.Data = base.GenerateDocNum(DocType.SD, DocFixedNumDef.SD_ConsignmentNote);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

    }
}

