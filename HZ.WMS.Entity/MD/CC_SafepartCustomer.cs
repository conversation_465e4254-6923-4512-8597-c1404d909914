using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HZ.WMS.Entity.MD
{
    /// <summary>
    /// 安全部件代码-客户规则对照
    /// </summary>
    public class CC_SafepartCustomer : BaseEntity
    {
        /// <summary>
        /// 主键-编号
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string id { get; set; }
        /// <summary>
        /// 客户编码
        /// </summary>
        public string ccode { get; set; }
        /// <summary>
        /// 客户名称
        /// </summary>
        public string cname { get; set; }
        /// <summary>
        /// 规则编码
        /// </summary>
        public string ruleid { get; set; }
       
    }
}
