using SqlSugar;
using HZ.Core.Http;
using HZ.WMS.Application.MD;
using HZ.WMS.Application.SAP;
using HZ.WMS.Application.Sys;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.MM;
using HZ.WMS.Entity.MM.Parameters;
using HZ.WMS.Entity.MM.ViewModel;
using HZ.WMS.Entity.SAP;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Linq.Expressions;

namespace HZ.WMS.Application.MM
{
    /// <summary>
    /// 归还明细
    /// </summary>
    public class MM_LendingOrder_ReturnApp : BaseApp<MM_LendingOrder_Return>
    {
        #region 初始化

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public MM_LendingOrder_ReturnApp() : base(){ }

        #endregion


        #region 保存

        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="Parameters">参数类</param>
        /// <param name="user">用户</param>
        /// <param name="error_message">错误信息</param>
        /// <returns></returns>
        public bool Save(List<MM_LendingOrder_Return> Parameters, string user, out string error_message)
        {
            error_message = "";
            try
            {
                #region 属性初始化

                DateTime time = DateTime.Now;
  
                #endregion

                #region 逻辑处理


                //明细表信息
                Parameters.ForEach(x =>
                {
                    x.IsDelete = false;
                    x.CUser = user;
                    x.CTime = time;
                });

                #endregion

                #region 事务操作

                DbContext.Ado.BeginTran();

                //信息插入
                Insert(Parameters);

                DbContext.Ado.CommitTran();

                #endregion

                //更新归还数量部分信息
                base.SqlQuery("PROC_UpdateLendingOrder_Auto", CommandType.StoredProcedure);

                if (string.IsNullOrEmpty(error_message))
                {
                    error_message = "提交成功";
                }
                return true;
            }
            catch (Exception ex)
            {
                if (DbContext.Ado.IsAnyTran())
                    DbContext.Ado.RollbackTran();

                error_message = ex.Message;
                return false;
            }
        }

        #endregion



        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="DocNums">单号数组</param>
        /// <param name="opUser">当前用户</param>
        /// <param name="error_message">错误消息</param>
        public bool Delete(List<string> ids, string opUser, out string error_message)
        {
            error_message = "";
            bool bDeleted = true;
            //var list = new List<MM_DepRequisition>();
            //var detailList = new List<MM_DepReqDetailed>();
            //if (flag.Any(x => x.IsPosted == true))
            var flag = GetList(d => ids.Contains(d.ID)).ToList();

            try
            {
                DbContext.Ado.BeginTran();
          

                //删除主表信息
                Delete(flag, opUser);

                //删除明细表信息
        

                DbContext.Ado.CommitTran();

                //更新归还数量部分信息
                base.SqlQuery("PROC_UpdateLendingOrder_Auto", CommandType.StoredProcedure);

            }
            catch (Exception ex)
            {
                bDeleted = false;
                error_message = ex.Message;
                if (DbContext.Ado.IsAnyTran())
                    DbContext.Ado.RollbackTran();
            }
            if (string.IsNullOrEmpty(error_message))
            {
                error_message = "删除成功";
            }
            return bDeleted;

        }

        #endregion


        /// <summary>
        /// 查询物料信息
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <returns></returns>
        public List<MM_LendingOrderReturnItem_View> GetItem(Pagination page, string keyword)
        {
            Expression<Func<MM_LendingOrderReturnItem_View, bool>> condition = x => (string.IsNullOrEmpty(keyword)
                                                                  || x.ItemCode.Contains(keyword) || x.ItemName.Contains(keyword) || x.LendingOrderNum.Contains(keyword)
                                                                 );
            var query = DbContext.Queryable<MM_LendingOrderReturnItem_View>()
                .Where(condition);
            int total = query.Count();
            page.Total = total;
            if (page.PageSize > total)
            {
                return query.ToPageList(page.PageNumber, total);
            }
            return query.ToPageList(page.PageNumber, page.PageSize);
        }

    }
}
