using SqlSugar;
using System;
using System.Collections.Generic;

using HZ.Core.Security;
using HZ.WMS.Entity.Sys;
using HZ.Core.Http;

namespace HZ.WMS.Application.Sys
{
    /// <summary>
    /// 没有out_message消息的函数，意味着只在最前端做未处理异常捕捉
    /// </summary>

    public class Sys_ApiLogConfigApp : BaseApp<Sys_ApiLogConfig>
    {
		#region 默认构造函数
        
        /// <summary>
        /// 默认构造函数
        /// </summary>
        public Sys_ApiLogConfigApp() : base()
        {
        }

        
        

        #endregion
        
    }
}

