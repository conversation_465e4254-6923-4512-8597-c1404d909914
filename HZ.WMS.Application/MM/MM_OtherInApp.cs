using SqlSugar;
using HZ.Core.Http;
using HZ.WMS.Application.MD;
using HZ.WMS.Application.SAP;
using HZ.WMS.Application.Sys;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.MM;
using HZ.WMS.Entity.MM.Parameters;
using HZ.WMS.Entity.MM.ViewModel;
using HZ.WMS.Entity.SAP;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using AOS.OMS.Application.Util;
using HZ.WMS.Entity.Produce;
using HZ.WMS.Entity.SAP.Req;
using Newtonsoft.Json;

namespace HZ.WMS.Application.MM
{
    /// <summary>
    /// 其他入库部门领料
    /// </summary>
    public class MM_OtherInApp : BaseApp<MM_OtherIn>
    {
        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public MM_OtherInApp() : base(){ }

        MM_OtherInDetailApp _detailApp = new MM_OtherInDetailApp();

        #endregion


        #region 保存

        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="Parameters">参数类</param>
        /// <param name="user">用户</param>
        /// <param name="error_message">错误信息</param>
        /// <param name="type">类型 1:提交失败 2：过账失败</param>
        /// <returns></returns>
        public bool Save(MM_OtherInParameters Parameters, string user, out string error_message)
        {
            error_message = "提交成功";
            try
            {
                #region 属性初始化

                DateTime time = DateTime.Now;
                var queryList = new List<MM_OtherIn>();

                #endregion

                #region 逻辑处理

                //主表信息
                queryList.Add(new MM_OtherIn
                {
                    ManualPostTime = Parameters.ManualPostTime,
                    DocNum = Parameters.DocNum,
                    IsPosted = false,
                    IsDelete = false,
                    IsCancel = false,
                    CUser = user,
                    CTime = time,
                    HandlenCode= Parameters.HandlenCode,
                    HandlenName = Parameters.HandlenName,
                    MovementType = Parameters.MovementType,
                    MovementTypeName = Parameters.MovementTypeName,
                    Remark = Parameters.Remark,
                    Status=0,
                    ScrapNum= Parameters.ScrapNum
                });

                //转入仓库编码
                var WhsCodes = Parameters.DetailedList.Select(x => x.WhsCode).Distinct().ToArray();
                var whscodes = new MD_BinLocationApp().GetList(x => WhsCodes.Contains(x.WhsCode))?.ToList();

                //转入仓库编码
                var ItemCode = Parameters.DetailedList.Select(x => x.ItemCode.Trim()).Distinct().ToArray();
                var itemcodes = DbContextForSAP.Queryable<XZ_SAP_MARC>().Where(x => ItemCode.Contains(x.MATNR))?.ToList();
                int i = 0;
                //WhsCodes
                foreach (MM_OtherInDetail x in Parameters.DetailedList)
                {
                    i += 1;
                    x.Line = i;
                    x.CompanyCode = "001";
                    x.FactoryCode = "2002";
                    x.IsPosted = false;
                    x.DocNum = Parameters.DocNum;
                    x.IsDelete = false;
                    x.CUser = user;
                    x.CTime = time;
                    x.ItemCode = x.ItemCode.Trim();
                    if (string.IsNullOrEmpty(x.RegionCode))
                    {
                        x.WhsName = whscodes.Where(v => v.WhsCode == x.WhsCode).FirstOrDefault().WhsName;
                        x.RegionCode = whscodes.Where(v => v.WhsCode == x.WhsCode).FirstOrDefault().RegionCode;
                        x.RegionName = whscodes.Where(v => v.WhsCode == x.WhsCode).FirstOrDefault().RegionName;
                        x.BinLocationCode = whscodes.Where(v => v.WhsCode == x.WhsCode).FirstOrDefault().BinLocationCode;
                        x.BinLocationName = whscodes.Where(v => v.WhsCode == x.WhsCode).FirstOrDefault().BinLocationName;
                    }
                    if (string.IsNullOrEmpty(x.ItemName))
                    {
                        var item = itemcodes.Where(v => v.MATNR == x.ItemCode)?.FirstOrDefault();
                        if (item == null)
                        {
                            error_message = "物料编号【" + x.ItemCode + "】无效!";
                            return false;
                        }

                        x.ItemName = item.MAKTX;
                    }

                }

                #endregion

                #region 事务操作

                DbContext.Ado.BeginTran();
                _detailApp.DbContext = this.DbContext;

                //批量插入
                Insert(queryList);

                _detailApp.Insert(Parameters.DetailedList);

                DbContext.Ado.CommitTran();

                #endregion

                return true;
            }
            catch (Exception ex)
            {
                if (DbContext.Ado.IsAnyTran())
                    DbContext.Ado.RollbackTran();

                error_message = ex.Message;
                return false;
            }
        }

        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="Parameters">参数类</param>
        /// <param name="user">用户</param>
        /// <param name="error_message">错误信息</param>
        /// <returns></returns>
        public bool Update(MM_OtherInParameters Parameters, string user, out string error_message)
        {
            error_message = "更新成功";
            
            try
            {
                #region 属性初始化

                DateTime time = DateTime.Now;
                var AddmsDetailList = new List<MM_OtherInDetail>();
                var UpdatemsDetailList = new List<MM_OtherInDetail>();

                #endregion

                #region 逻辑处理

                //根据主键ID查询采购退料明细信息
                var entities = _detailApp.GetListByKeys(Parameters.deldetailArray);

                //根据申请单号查询采购退料主信息
                var query = GetList(x => x.DocNum == Parameters.DocNum)?.ToList();


                //插入子表信息 podetailed
                int i = 0;
                foreach (var rsdetail in Parameters.DetailedList)
                {
                    i += 1;
                    rsdetail.Line = i;
                    if (string.IsNullOrEmpty(rsdetail.DepReqDetailedID))
                    {
                        rsdetail.DocNum = Parameters.DocNum;
                        rsdetail.IsDelete = false;
                        rsdetail.CUser = user;
                        rsdetail.CTime = time;
                        rsdetail.IsPosted = false;
                        rsdetail.CompanyCode = "001";
                        rsdetail.FactoryCode = "2002";
                        //rsdetail.WhsCode = Parameters.WhsCode;
                        //rsdetail.WhsName = Parameters.WhsName;
                        //rsdetail.RegionCode = Parameters.RegionCode;
                        //rsdetail.RegionName = Parameters.RegionName;
                        //rsdetail.BinLocationCode = Parameters.BinLocationCode;
                        //rsdetail.BinLocationName = Parameters.BinLocationName;
                        AddmsDetailList.Add(rsdetail);
                    }
                    else
                    {
                        rsdetail.MUser = user;
                        rsdetail.MTime = time;
                        //rsdetail.WhsCode = Parameters.WhsCode;
                        //rsdetail.WhsName = Parameters.WhsName;
                        //rsdetail.RegionCode = Parameters.RegionCode;
                        //rsdetail.RegionName = Parameters.RegionName;
                        //rsdetail.BinLocationCode = Parameters.BinLocationCode;
                        //rsdetail.BinLocationName = Parameters.BinLocationName;
                        UpdatemsDetailList.Add(rsdetail);
                    }
                    //listmsdetail.Add(podetailed);
                }

                #endregion

                #region 事务操作

                DbContext.Ado.BeginTran();
                _detailApp.DbContext = DbContext;

                //删除采购退料明细信息
                if (entities.Count > 0)
                {
                    _detailApp.Delete(entities, user);
                }
                //更新采购退料信息
                if (query != null && query.Count > 0)
                {

                    query[0].HandlenCode = Parameters.HandlenCode;
                    query[0].HandlenName = Parameters.HandlenName;
                    query[0].MovementType = Parameters.MovementType;
                    query[0].MovementTypeName = Parameters.MovementTypeName;
                    query[0].ScrapNum = Parameters.ScrapNum;

                    query[0].ManualPostTime= Parameters.ManualPostTime;
                    query[0].Remark = Parameters.Remark;
                    query[0].MUser = user;
                    query[0].MTime = time;
                    Update(query);
                }
                //插入采购退料明细信息
                //this.Update(listms);
                if (AddmsDetailList.Count > 0)
                {
                    _detailApp.Insert(AddmsDetailList);
                }
                //更新采购退料明细信息
                if (UpdatemsDetailList.Count > 0)
                {
                    _detailApp.Update(UpdatemsDetailList);
                }

                DbContext.Ado.CommitTran();

                #endregion

                return true;
            }
            catch (Exception ex)
            {
                if (DbContext.Ado.IsAnyTran())
                    DbContext.Ado.RollbackTran();

                error_message = ex.Message;
                return false;
            }
        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除校验
        /// </summary>
        /// <param name="entities">集合</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        private bool CheckDelete(List<MM_OtherIn> entities, out string error_message)
        {
            bool isPass = true;
            error_message = "";
            foreach (var x in entities)
            {
                if (x.IsPosted == true)
                {
                    error_message = "ui.Message.PODeletePostingWarning"; // "已过账数据不允许删除!";
                    isPass = false;
                    break;
                }
            }

            return isPass;
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="DocNums">单号数组</param>
        /// <param name="opUser">当前用户</param>
        /// <param name="error_message">错误消息</param>
        public bool Delete(List<string> DocNums, string opUser, out string error_message)
        {
            error_message = "";
            bool bDeleted = true;
            //var list = new List<MM_OtherIn>();
            //var detailList = new List<MM_OtherInDetail>();

            var flag = GetList(d => DocNums.Contains(d.DocNum)).ToList();
            if (flag.Any(x => x.IsPosted == true))
            {
                error_message = "已过帐数据不允许删除";
                return false;
            }
            var mark = _detailApp.GetList(d => DocNums.Contains(d.DocNum)).ToList();

            //foreach (string key in DocNums)
            //{
            //    //var item2 = GetList(x => x.IsPosted.an).ToList();
            //    //item2.Any(x=>x.IsPosted==true);
            //    var item = GetList(x => (string.IsNullOrEmpty(key) || x.DocNum.Contains(key))).ToList();
            //    if (item != null)
            //    {
            //        list.AddRange(item);
            //    }
            //    var itemdetail = _detailApp.GetList(x => (string.IsNullOrEmpty(key) || x.DocNum.Contains(key))).ToList();
            //    if (itemdetail != null)
            //    {
            //        detailList.AddRange(itemdetail);
            //    }
            //}
            //List<PO_ReturnScan> entities = GetListByKeys(ids);
            //if (CheckDelete(list, out error_message))
            //{
                try
                {
                    DbContext.Ado.BeginTran();
                    _detailApp.DbContext = DbContext;

                    //删除主表信息
                    Delete(flag, opUser);

                    //删除明细表信息
                    _detailApp.Delete(mark, opUser);

                    DbContext.Ado.CommitTran();
                }
                catch (Exception ex)
                {
                    bDeleted = false;
                    error_message = ex.Message;
                    if (DbContext.Ado.IsAnyTran())
                        DbContext.Ado.RollbackTran();
                }
            //}
            //else
            //{
            //    bDeleted = false;
            //}
            return bDeleted;

        }

        #endregion

        #region 过账

        /// <summary>
        /// 过账
        /// </summary>
        /// <param name="entities">设备领料集合</param>
        /// <param name="opUser">当前过账用户</param>
        /// <param name="error_message">错误消息</param>
        /// <returns></returns>
        public bool DoPost(List<MM_OtherIn> entities, List<MM_OtherInDetail> entitieDetails, string opUser, out string error_message)
        {
            error_message = "";

            foreach (var x in entities)
            {
                if (x.Status == 0)
                {
                    error_message = "信息还未审核，不允许过账";
                    return false;
                }
                if (x.IsPosted == true)
                {
                    error_message = "信息已过帐，不允许重复过账";
                    return false;
                }
            }
            
            if (entitieDetails == null || entitieDetails.Count == 0) //手持端添加信息后自动过账，不需要在查询一次
            {
                //查询明细信息
                entitieDetails = _detailApp.GetList(x => entities.Select(t => t.DocNum).Contains(x.DocNum) && x.IsPosted == false)?.ToList();
            }
            List<CheckMatnrPrice.Table001089> checkMatnrPriceList = entitieDetails.Select(otherDetail => new CheckMatnrPrice.Table001089
            {
                WERKS = "2002",
                MATNR = otherDetail.ItemCode,
            }).ToList().Where(t => !string.IsNullOrEmpty(t.MATNR)).ToList();

            string checkMatnrPriceUri = ConfigurationManager.AppSettings["SapUrl"] + ConfigurationManager.AppSettings["CheckMatnrPrice"];
            string token = ConfigurationManager.AppSettings["SapToken"];
            string checkMatnrPriceRes = HttpUtil.HttpPost($"{checkMatnrPriceUri}?token={token}", JsonConvert.SerializeObject(checkMatnrPriceList), "POST");

            var checkMatnrPrice = JsonConvert.DeserializeObject<CheckMatnrPrice>(checkMatnrPriceRes);

            if (checkMatnrPrice.ETYPE != "S")
            {
                error_message = checkMatnrPrice.EMSG;
                return false;
            }

            Sys_SAPCompanyInfoApp _SAPCompanyInfoApp = new Sys_SAPCompanyInfoApp();
            MD_StockApp _stockApp = new MD_StockApp();

            try
            {
                //多条数据后根据单号进行分组
                var mainInfoList = entities.GroupBy(g => new
                {
                    g.DocNum
                }).Select(q => new
                {
                    DocNum = q.Key.DocNum
                });

                //按单号分组
                foreach (var mainInfo in mainInfoList)
                {
                    var list = new List<ZFGWMS010>();
                    int Line = 0;
                    var conditionList = new List<MM_OtherInDetail>();
                    MM_OtherIn mod=GetList(x => x.DocNum == mainInfo.DocNum )?.ToList().FirstOrDefault();
                    conditionList = entitieDetails.Where(x => x.DocNum == mainInfo.DocNum && x.IsPosted == false)?.ToList();
                    conditionList.ForEach(x =>
                    {
                        Line = Line + 1;
                        x.Line = Line;
                        var y = new ZFGWMS010();
                        y.ZNUM = Line; //condition.BaseLine;领料单行号
                        y.MATNR = x.ItemCode;//
                        y.WERKS = x.FactoryCode ?? "2002";//eq.FactoryCode
                        y.BWART = x.MovementType;//移动类型
                        y.MENGE = x.Qty;
                        y.MEINS = x.Unit;
                        y.LGORT = x.WhsCode;
                        y.KOSTL = x.CostCenter ?? "";
                        y.SAKTO = x.LedgerType ?? "";
                        y.SOBKZ = x.SpecialStock ?? "";//E:销售订单库存
                        y.KDAUF = x.SpecialStock == "E" ? x.SaleNum : "";
                        y.KDPOS = x.SpecialStock == "E" ? x.SaleLine : 0;
                        y.AUFNR= x.OrderNum ?? "";
                        y.BWTAR = x.AssessType?? "";//如果物料启用分割评估，此字段必填(待定)
                        y.SGTXT = x.Remark ?? "";
                        list.Add(y);
                      
                    });

                    bool ispost = false;
                    DateTime time = DateTime.Now;
                    //string rtnErrMsg = "";
                    //purchaseReceipt.CompanyCode 公司代码
                    //查询Sap账期时间
                    DateTime ManualPostTime = new SAPApp().GetSAPpostTime(Convert.ToDateTime(entities[0].ManualPostTime));
                    List<SAPRETURN> saplist = _SAPCompanyInfoApp.ZFGWMS010(conditionList[0].CompanyCode ?? "001", mainInfo.DocNum, mod.Remark, ManualPostTime, list, out ispost, out error_message);
                    if (saplist != null && saplist.Count > 0)
                    {
                        //List<MM_EquipmentPicking> queryList = new List<MM_EquipmentPicking>();
                        foreach (var sap in saplist)
                        {
                            //&& x.BaseNum == sap.basenum && x.BaseLine == sap.baseline
                            var querydetail = conditionList.Where(x => x.DocNum == mainInfo.DocNum && x.Line == sap.line).ToList().FirstOrDefault();
                            if (querydetail != null)
                            {
                                querydetail.IsPosted = true;
                                querydetail.PostUser = opUser;
                                querydetail.PostTime = time;
                                querydetail.MUser = opUser;
                                querydetail.MTime = time;
                                querydetail.SapDocNum = sap.sapDocNum;
                                querydetail.SapLine = sap.sapline;

                                if (_detailApp.Update(querydetail) > 0)//更新成功后更新库存
                                {
                                    if (querydetail.MovementType == "Z02" || querydetail.MovementType == "Z04"
                                        || querydetail.MovementType == "Z06" || querydetail.MovementType == "Z08"
                                        || querydetail.MovementType == "Z26" || querydetail.MovementType == "532")
                                    {
                                        #region 出库
                                        //评估类型为空
                                        if (string.IsNullOrEmpty(string.IsNullOrEmpty(querydetail.AssessType) ? "" : querydetail.AssessType.Trim()))
                                        {
                                            string stockMsg = string.Empty;
                                            //销售库存出库
                                            if (!string.IsNullOrEmpty(string.IsNullOrEmpty(querydetail.SaleNum) ? "" : querydetail.SaleNum.Trim()))
                                            {
                                                if (!_stockApp.StockOut(querydetail.ItemCode, querydetail.BinLocationCode, querydetail.SaleNum, Convert.ToInt32(querydetail.SaleLine), querydetail.Qty, opUser, out stockMsg))
                                                {
                                                    error_message = stockMsg;
                                                    //return false;
                                                }
                                            }
                                            else//正常库存出库
                                            {
                                                if (!_stockApp.StockOut(querydetail.ItemCode, querydetail.BinLocationCode, querydetail.Qty, opUser, out stockMsg))
                                                {
                                                    error_message = stockMsg;
                                                    //return false;
                                                }
                                            }
                                        }
                                        else//评估类型不为空的话 走评估类型的出库方法
                                        {
                                            string stockMsg = string.Empty;
                                            //评估类型-销售库存出库
                                            if (!string.IsNullOrEmpty(string.IsNullOrEmpty(querydetail.SaleNum) ? "" : querydetail.SaleNum.Trim()))
                                            {
                                                if (!_stockApp.StockOutForAssmentType(querydetail.ItemCode, querydetail.BinLocationCode, querydetail.SaleNum, Convert.ToInt32(querydetail.SaleLine), querydetail.Qty, querydetail.AssessType, opUser, out stockMsg))
                                                {
                                                    error_message = stockMsg;
                                                    //return false;
                                                }
                                            }
                                            else//评估类型-正常库存出库
                                            {
                                                if (!_stockApp.StockOutForAssmentType(querydetail.ItemCode, querydetail.BinLocationCode, querydetail.Qty, querydetail.AssessType, opUser, out stockMsg))
                                                {
                                                    error_message = stockMsg;
                                                    //return false;
                                                }
                                            }
                                        }
                                        #endregion
                                    }
                                    else
                                    {
                                        #region 入库
                                        // 插入收货记录成功才能更新库存
                                        string stockMsg = string.Empty;
                                        MD_Stock stock = new MD_Stock();
                                        stock.BarCode =  "";
                                        stock.BatchNum = "";
                                        stock.ItemCode = querydetail.ItemCode.Trim();
                                        stock.ItemName = querydetail.ItemName.Trim();
                                        stock.WhsCode = querydetail.WhsCode;
                                        stock.WhsName = querydetail.WhsName;
                                        stock.RegionCode = querydetail.RegionCode;
                                        stock.RegionName = querydetail.RegionName;
                                        stock.BinLocationCode = querydetail.BinLocationCode;
                                        stock.BinLocationName = querydetail.BinLocationName;
                                        stock.Qty = querydetail.Qty;
                                        stock.Unit = querydetail.Unit;
                                        stock.IsDelete = false;
                                        stock.AssessType = querydetail.AssessType;

                                        stock.SaleNum = querydetail.SaleNum ?? "";
                                        stock.SaleLine = querydetail.SaleLine ?? 0;
                                        if (!string.IsNullOrEmpty(string.IsNullOrEmpty(querydetail.SaleNum) ? "" : querydetail.SaleNum.Trim()))
                                            stock.SpecialStock = "E";//销售库存

                                        if (!_stockApp.StockIn(stock, opUser, out stockMsg))
                                        {
                                            error_message = stockMsg;
                                            //return false;
                                        }


                                        #endregion
                                    }
                                }
                                //queryList.Add(query);
                            }
                        }

                        var query = entities.Where(x => x.DocNum == mainInfo.DocNum).ToList().FirstOrDefault();
                        if (query != null)
                        {
                            query.IsPosted = true;
                            query.PostUser = opUser;
                            query.PostTime = time;
                            query.MUser = opUser;
                            query.MTime = time;
                            query.ManualPostTime = ManualPostTime;
                            query.SAPmark = "S";
                        }
                        //更新
                        Update(query);
                    }
                    else
                    {
                        var query = entities.Where(x => x.DocNum == mainInfo.DocNum).ToList().FirstOrDefault();
                        if (query != null)
                        {
                            query.SAPmark = "E";
                            query.SAPmessage = error_message;
                        }
                        //更新
                        Update(query);

                        error_message = "单号：" + mainInfo.DocNum + "，过账失败，失败原因：" + error_message;
                        return false;
                    }
                }
                if(string.IsNullOrEmpty(error_message))
                    error_message = "过账成功";
                return true;
            }
            catch (Exception ex)
            {
                error_message = ex.Message;
                return false;
            }
        }


        #endregion

        #region 审核

        /// <summary>
        /// 审核
        /// </summary>
        /// <param name="DocNums">单号数组</param>
        /// <param name="opUser">当前用户</param>
        /// <param name="error_message">错误消息</param>
        /// <param name="type">类型 1:提交失败 2：过账失败</param>
        public bool Audits(string[] DocNums, string opUser, out string error_message, out string type)
        {
            error_message = "";
            type = "";
            Sys_SwithConfigApp _switchApp = new Sys_SwithConfigApp();
            //List<MM_OtherIn> entities = GetListByKeys(ids);

            var flag = GetList(d => DocNums.Contains(d.DocNum)).ToList();
            if (flag.Any(x=>x.Status==2))
            {
                error_message = "请选择状态为[未审核]的信息进行审核";
                type = "1";
                return false;
            }

            #region 库存校验

            //foreach (var x in Parameters.detailed)
            //{
            //    string message = "";
            //    //库存校验
            //    bool result = _stockApp.ValidateStockOut(x.ItemCode, Parameters.BinLocationCode, x.SalesOrderNum, Convert.ToInt32(x.SalesOrderLine), x.ReturnScanQty, out message, null);
            //    bool result = _stockApp.ValidateStockOut(x.ItemCode, Parameters.BinLocationCode, x.ReturnScanQty, out message, null, null);
            //    if (!result)
            //    {
            //        error_message = message;
            //        type = "1";
            //        return false;
            //    }
            //}

            #endregion

            try
            {
                flag.ForEach(x=>
                {
                    x.Status = 2;//0:未审核 1：审核中 2:已审核 3：已取消
                    x.MUser = opUser;
                    x.MTime = DateTime.Now;
                });
                DbContext.Ado.BeginTran();

                Update(flag);

                DbContext.Ado.CommitTran();//提交事务

                #region 是否自动过账

                //是否自动过账判定
                if (_switchApp.IsDeqRequisitionAutoPost)
                {
                    string postMsg = "";
                    bool bpost = DoPost(flag, null, opUser, out postMsg);
                    if (!bpost)
                    {
                        error_message = "审核成功"+postMsg;
                        type = "2";
                        return false;
                    }
                    else
                    {
                        error_message = "审核并" + postMsg;
                    }
                }

                #endregion

                if(string.IsNullOrEmpty(error_message))
                    error_message = "审核成功";
                return true;
            }
            catch (Exception ex)
            {
                if (DbContext.Ado.IsAnyTran())
                    DbContext.Ado.RollbackTran();//失败回滚
                error_message = ex.Message;
                type = "1";
                return false;
            }
        }

        #endregion

        #region 取消

        /// <summary>
        /// 取消
        /// </summary>
        /// <param name="DocNums">单号数组</param>
        /// <param name="opUser">当前用户</param>
        /// <param name="error_message">错误消息</param>
        public bool Rejects(string[] DocNums, string opUser, out string error_message)
        {
            error_message = "";

            var flag = GetList(d => DocNums.Contains(d.DocNum)).ToList();
            if (flag.Any(x => x.Status != 2))
            {
                error_message = "请选择状态为[已审核]的信息进行取消";
                return false;
            }

            try
            {
                flag.ForEach(x =>
                {
                    x.Status = 3;
                    x.MUser = opUser;
                    x.MTime = DateTime.Now;
                });

                DbContext.Ado.BeginTran();

                Update(flag);

                DbContext.Ado.CommitTran();//提交事务

                if (string.IsNullOrEmpty(error_message))
                    error_message = "取消成功";
                return true;
            }
            catch (Exception ex)
            {
                if (DbContext.Ado.IsAnyTran())
                    DbContext.Ado.RollbackTran();//失败回滚
                error_message = ex.Message;
                return false;
            }
        }

        #endregion

        #region 作废

        /// <summary>
        /// 作废
        /// </summary>
        /// <param name="DocNums">单号数组</param>
        /// <param name="opUser">当前用户</param>
        /// <param name="error_message">错误消息</param>
        public bool Cancel(string[] DocNums, string opUser, out string error_message)
        {
            error_message = "";


            var flag = GetList(d => DocNums.Contains(d.DocNum)).ToList();
            if (flag.Any(x => x.IsPosted == true))
            {
                error_message = "请选择未过账数据进行作废";
                return false;
            }
            try
            {
                flag.ForEach(x =>
                {
                    x.IsCancel = true;
                    x.MUser = opUser;
                    x.MTime = DateTime.Now;
                });
                DbContext.Ado.BeginTran();

                Update(flag);

                DbContext.Ado.CommitTran();//提交事务

                if (string.IsNullOrEmpty(error_message))
                    error_message = "作废成功";
                return true;
            }
            catch (Exception ex)
            {
                if (DbContext.Ado.IsAnyTran())
                    DbContext.Ado.RollbackTran();//失败回滚
                error_message = ex.Message;
                return false;
            }
        }

        #endregion

        #region 查询导出数据信息

        /// <summary>
        /// 查询导出数据信息
        /// </summary>
        /// <param name="page"></param>
        /// <param name="condition"></param>
        /// <returns></returns>
        public ISugarQueryable<MM_OtherInExport_View> GetExportView(Expression<Func<MM_OtherInExport_View, bool>> condition)
        {
            var query = DbContext.Queryable<MM_OtherInExport_View>()
                .Where(condition)
                .OrderBy(t => t.DocNum,OrderByType.Desc)
                .OrderBy(t => t.Line);
            return query;
        }

        #endregion
    }
}
