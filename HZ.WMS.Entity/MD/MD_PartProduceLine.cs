using System.ComponentModel;
using SqlSugar;

namespace HZ.WMS.Entity.MD
{
    /// <summary>
    /// 安全部件生产线对照
    /// </summary>
    [SugarTable("MD_PartProduceLine")]
    public class MD_PartProduceLine : BaseEntity
    {
        /// <summary>
        /// ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Description("ID")]
        public string Id { get; set; }

        /// <summary>
        /// 部件名称
        /// </summary>
        [Description("部件名称")]
        public string PartName { get; set; }

        /// <summary>
        /// 部件编号
        /// </summary>
        [Description("部件编号")]
        public string PartCode { get; set; }

        /// <summary>
        /// 生产线名称
        /// </summary>
        [Description("生产线名称")]
        public string ProduceLineName { get; set; }

        /// <summary>
        /// 轿厢上行超速保护装置
        /// </summary>
        [Description("轿厢上行超速保护装置")]
        public string UpOverSpeedCode { get; set; }

        /// <summary>
        /// 轿厢意外移动保护装置
        /// </summary>
        [Description("轿厢意外移动保护装置")]
        public string AccidentMoveCode { get; set; }

    }
}