using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Linq.Expressions;
using AOS.OMS.Application.Util;
using AOS.OMS.Entity.Sale;
using HZ.Core.Http;
using HZ.Core.Utilities;
using HZ.WMS.Application.MD;
using HZ.WMS.Application.Produce;
using HZ.WMS.Application.Sys;
using HZ.WMS.Entity.MD;
using HZ.WMS.Entity.Produce;
using HZ.WMS.Entity.Produce.Req;
using HZ.WMS.Entity.SD;
using HZ.WMS.Entity.SD.ViewModel;
using HZ.WMS.Entity.Sys;
using LinqKit;
using Newtonsoft.Json;
using SqlSugar;
using DateUtil = AOS.Core.Utilities.DateUtil;
using TreeNode = AOS.OMS.Entity.Sale.OrderTree.TreeNode;

namespace HZ.WMS.Application.SD
{
    /// <summary>
    /// 发运计划
    /// 没有out_message消息的函数，意味着只在最前端做未处理异常捕捉
    /// </summary>
    public class Produce_ShedulingApp : BaseApp<Produce_Scheduling>
    {
        #region 初始化

        Sys_DictionaryApp _dictionaryApp = new Sys_DictionaryApp();
        MD_CustomerProduceApp _customerProduceApp = new MD_CustomerProduceApp();
        MD_LineBatchApp _lineBatchApp = new MD_LineBatchApp();
        MD_PartMakeCompanyApp _partMakeCompanyApp = new MD_PartMakeCompanyApp();
        MD_PartProduceLineApp _partProduceLineApp = new MD_PartProduceLineApp();
        MD_ProduceLineCapacityApp _produceLineCapacityApp = new MD_ProduceLineCapacityApp();
        Produce_SchedulingDetailApp _schedulingDetailApp = new Produce_SchedulingDetailApp();
        Produce_PurchaseOrderApp _purchaseOrderApp = new Produce_PurchaseOrderApp();
        MD_ReportStationApp _reportStationApp = new MD_ReportStationApp();

        #endregion

        #region 分页查询

        public object GetPageList(Pagination page, Produce_SchedulingListReq req)
        {
            if (string.IsNullOrEmpty(page.Sort))
            {
                page.Sort = "CTime desc";
            }
            else
            {
                page.Sort = page.Sort.Replace("ascending", "asc").Replace("descending", "desc");
            }

            var condition = GetTreeCondition(req);

            return GetPageList(page, condition).ToList();
        }

        #endregion

        #region 查询发运计划

        /// <summary>
        /// 查询发运计划
        /// </summary>
        /// <param name="InspectionNum">报检单号</param>
        /// <param name="errorMessage">错误消息</param>
        public List<SD_DeliveryScan_View> GetShippingPlanDetail(string DocNum, out string errorMessage)
        {
            errorMessage = "";
            var query = DbContext.Queryable<SD_DeliveryScan_View>().Where(x => x.DocNum == DocNum).ToList();
            if (query == null || query.Count <= 0)
            {
                errorMessage = "发运单号[" + DocNum + "]已发料完成！";
            }

            return query;
        }

        #endregion

        #region 查询导出数据

        /// <summary>
        /// 查询导出数据
        /// </summary>
        /// <param name="keyword">参数</param>
        /// <param name="fromTime">开始时间</param>
        /// <param name="toTime">结束时间</param>
        public List<Produce_Scheduling> GetExportInfo(Produce_SchedulingListReq req)
        {
            return GetList(GetTreeCondition(req)).ToList();
        }

        #endregion

        #region 获取树条件

        private Expression<Func<Produce_Scheduling, bool>> GetTreeCondition(Produce_SchedulingListReq req)
        {
            var condition = GetCondition(req);
            var conditionChild = PredicateBuilder.New<Produce_Scheduling>();
            if (req.TreeReqList != null && req.TreeReqList.Count > 0)
            {
                foreach (var treeReq in req.TreeReqList)
                {
                    if (req.Status == 0)
                    {
                        if (treeReq.Level == 0)
                        {
                            conditionChild = conditionChild.Or(PredicateBuilder.New<Produce_Scheduling>(t =>
                                t.PlanAssemblyDate >= DateUtil.GetStartTime(DateTime.Parse(treeReq.Value))
                                && t.PlanAssemblyDate <= DateUtil.GetEndTime(DateTime.Parse(treeReq.Value))
                            ));
                        }
                        else if (treeReq.Level == 1)
                        {
                            var arr = treeReq.Value.Split('&');
                            string customerName = arr[1];
                            string deliveryDate = arr[0];
                            conditionChild = conditionChild.Or(PredicateBuilder.New<Produce_Scheduling>(t =>
                                t.PlanAssemblyDate >= DateUtil.GetStartTime(DateTime.Parse(deliveryDate))
                                && t.PlanAssemblyDate <= DateUtil.GetEndTime(DateTime.Parse(deliveryDate))
                                && t.CustomerName == customerName
                            ));
                        }
                        else if (treeReq.Level == 2)
                        {
                            conditionChild = conditionChild.Or(PredicateBuilder.New<Produce_Scheduling>(t =>
                                t.Id == treeReq.Value
                            ));
                        }
                    }
                    else if (req.Status == 3)
                    {
                        if (treeReq.Level == 0)
                        {
                            // 第0级：基于排产日期
                            conditionChild = conditionChild.Or(PredicateBuilder.New<Produce_Scheduling>(t =>
                                t.DeliveryDate >= DateUtil.GetStartTime(DateTime.Parse(treeReq.Value))
                                && t.DeliveryDate <= DateUtil.GetEndTime(DateTime.Parse(treeReq.Value))
                            ));
                        }
                        else if (treeReq.Level == 1)
                        {
                            // 第1级：基于排产日期和生产线，格式：排产日期|生产线
                            var parts = treeReq.Value.Split('|');
                            if (parts.Length == 2)
                            {
                                string schedulingDate = parts[0];
                                string produceLine = parts[1];
                                conditionChild = conditionChild.Or(PredicateBuilder.New<Produce_Scheduling>(t =>
                                    t.DeliveryDate >= DateUtil.GetStartTime(DateTime.Parse(schedulingDate))
                                    && t.DeliveryDate <= DateUtil.GetEndTime(DateTime.Parse(schedulingDate))
                                    && t.ProduceLine == produceLine
                                ));
                            }
                        }
                        else if (treeReq.Level == 2)
                        {
                            // 第2级：基于排产日期、生产线和客户名称，格式：排产日期|生产线|客户名称
                            var parts = treeReq.Value.Split('|');
                            if (parts.Length == 3)
                            {
                                string schedulingDate = parts[0];
                                string produceLine = parts[1];
                                string customerName = parts[2];
                                conditionChild = conditionChild.Or(PredicateBuilder.New<Produce_Scheduling>(t =>
                                    t.DeliveryDate >= DateUtil.GetStartTime(DateTime.Parse(schedulingDate))
                                    && t.DeliveryDate <= DateUtil.GetEndTime(DateTime.Parse(schedulingDate))
                                    && t.ProduceLine == produceLine
                                    && t.CustomerName == customerName
                                ));
                            }
                        }
                        else if (treeReq.Level == 3)
                        {
                            // 第3级：基于ID
                            conditionChild = conditionChild.Or(PredicateBuilder.New<Produce_Scheduling>(t =>
                                t.Id == treeReq.Value
                            ));
                        }
                    }
                    else
                    {
                        if (treeReq.Level == 0)
                        {
                            conditionChild = conditionChild.Or(PredicateBuilder.New<Produce_Scheduling>(t =>
                                t.ProduceLine == treeReq.Value
                            ));
                        }
                        else if (treeReq.Level == 1)
                        {
                            conditionChild = conditionChild.Or(PredicateBuilder.New<Produce_Scheduling>(t =>
                                t.PlanAssemblyDate >= DateUtil.GetStartTime(DateTime.Parse(treeReq.Value))
                                && t.PlanAssemblyDate <= DateUtil.GetEndTime(DateTime.Parse(treeReq.Value))
                            ));
                        }
                        else if (treeReq.Level == 2)
                        {
                            var arr = treeReq.Value.Split('&');
                            string customerName = arr[1];
                            string deliveryDate = arr[0];
                            conditionChild = conditionChild.Or(PredicateBuilder.New<Produce_Scheduling>(t =>
                                t.PlanAssemblyDate >= DateUtil.GetStartTime(DateTime.Parse(deliveryDate))
                                && t.PlanAssemblyDate <= DateUtil.GetEndTime(DateTime.Parse(deliveryDate))
                                && t.CustomerName == customerName
                            ));
                        }
                        else if (treeReq.Level == 3)
                        {
                            conditionChild = conditionChild.Or(PredicateBuilder.New<Produce_Scheduling>(t =>
                                t.Id == treeReq.Value
                            ));
                        }
                    }
                }

                condition = condition.And(conditionChild);
            }

            return condition;
        }

        #endregion

        #region 获取条件

        private Expression<Func<Produce_Scheduling, bool>> GetCondition(Produce_SchedulingListReq req)
        {
            var searchCondition = PredicateBuilder.New<Produce_Scheduling>(x =>
                (req.OrderType == null || x.OrderType.Equals(req.OrderType))
                && (string.IsNullOrEmpty(req.CustomerName) || x.CustomerName.Contains(req.CustomerName))
                && (string.IsNullOrEmpty(req.ContractNo) || x.ContractNo.Contains(req.ContractNo))
                && (string.IsNullOrEmpty(req.CustomerOrderNo) || x.CustomerOrderNo.Contains(req.CustomerOrderNo))
                && (string.IsNullOrEmpty(req.CUser) || x.CUser.Contains(req.CUser))
                && (string.IsNullOrEmpty(req.PlanStatus) || x.PlanStatus.Contains(req.PlanStatus))
                && (string.IsNullOrEmpty(req.PurchaseApplyStatus) || x.PurchaseApplyStatus.Contains(req.PurchaseApplyStatus))
                && (string.IsNullOrEmpty(req.ProduceStatus) || x.ProduceStatus.Contains(req.ProduceStatus))
                && (string.IsNullOrEmpty(req.PurchaseStatus) || x.PurchaseStatus.Contains(req.PurchaseStatus))
                && (string.IsNullOrEmpty(req.SaleSapNo) || x.SaleSapNo.Contains(req.SaleSapNo))
                && (req.SaleSapLine == null || x.SaleSapLine == req.SaleSapLine)
                && (req.Status == null || x.Status == req.Status)
            );
            if (req.CTime != null && req.CTime.Length > 1)
            {
                var startDate = req.CTime[0];
                var endDate = req.CTime[1];
                searchCondition = searchCondition.And(PredicateBuilder.New<Produce_Scheduling>(order =>
                    order.CTime >= DateUtil.GetStartTime(startDate) &&
                    order.CTime <= DateUtil.GetEndTime(endDate)));
            }

            if (req.ProduceSchedulingDate != null && req.ProduceSchedulingDate.Length > 1)
            {
                var startDate = req.ProduceSchedulingDate[0];
                var endDate = req.ProduceSchedulingDate[1];
                searchCondition = searchCondition.And(PredicateBuilder.New<Produce_Scheduling>(order =>
                    order.ProduceSchedulingDate >= DateUtil.GetStartTime(startDate) &&
                    order.ProduceSchedulingDate <= DateUtil.GetEndTime(endDate)));
            }

            if (req.PlanAssemblyDate != null && req.PlanAssemblyDate.Length > 1)
            {
                var startDate = req.PlanAssemblyDate[0];
                var endDate = req.PlanAssemblyDate[1];
                searchCondition = searchCondition.And(PredicateBuilder.New<Produce_Scheduling>(order =>
                    order.PlanAssemblyDate >= DateUtil.GetStartTime(startDate) &&
                    order.PlanAssemblyDate <= DateUtil.GetEndTime(endDate)));
            }

            return searchCondition;
        }

        #endregion

        #region 获取统计树

        public List<TreeNode> GetTree(Produce_SchedulingListReq req, HostOrderTreeReq treeReq)
        {
            var condition = GetCondition(req);
            var resList = new List<TreeNode>();

            if (treeReq.Level == 0)
            {
                var countList = DbContext.Queryable<Produce_Scheduling>()
                    .Where(condition)
                    .Where(t => t.IsDelete == false)
                    .GroupBy(t => t.ProduceLine)
                    .Select(t => new
                    {
                        ProduceLine = t.ProduceLine,
                        TotalCount = SqlFunc.AggregateCount(t.ProduceLine) // 或者 SqlFunc.Count()
                    })
                    .ToList();
                foreach (var count in countList)
                {
                    TreeNode node = new TreeNode();
                    node.Label = count.ProduceLine + " (" + count.TotalCount + ")";
                    node.Value = count.ProduceLine;
                    node.Leaf = count.TotalCount == 0;
                    node.Level = 0;
                    node.Branch = treeReq.Value;
                    resList.Add(node);
                }
            }
            else if (treeReq.Level == 1)
            {
                var countList = DbContext.Queryable<Produce_Scheduling>()
                    .Where(condition)
                    .Where(t => t.IsDelete == false && t.ProduceLine == treeReq.Value)
                    .GroupBy(t => t.DeliveryDate)
                    .Select(t => new
                    {
                        t.DeliveryDate,
                        TotalCount = SqlFunc.AggregateCount(1) // ✅
                    })
                    .ToList();
                foreach (var count in countList)
                {
                    TreeNode node = new TreeNode();
                    node.Label = DateUtil.ParseStr(count.DeliveryDate) + " (" + count.TotalCount + ")";
                    node.Value = DateUtil.ParseStr(count.DeliveryDate);
                    node.Leaf = count.TotalCount == 0;
                    node.Level = 1;
                    node.Branch = DateUtil.ParseStr(count.DeliveryDate);
                    resList.Add(node);
                }
            }
            else if (treeReq.Level == 2)
            {
                var countList2 = DbContext.Queryable<Produce_Scheduling>()
                    .Where(condition)
                    .Where(t => t.IsDelete == false
                                && t.DeliveryDate >= DateUtil.GetStartTime(DateTime.Parse(treeReq.Value))
                                && t.DeliveryDate <= DateUtil.GetEndTime(DateTime.Parse(treeReq.Value)))
                    .GroupBy(t => t.CustomerName)
                    .Select(t => new
                    {
                        t.CustomerName,
                        TotalCount = SqlFunc.AggregateCount(t.CustomerName) // ✅ 按字段统计（自动去重）
                    })
                    .ToList();
                foreach (var count in countList2)
                {
                    TreeNode node = new TreeNode();
                    node.Label = count.CustomerName + " (" + count.TotalCount + ")";
                    node.Value = treeReq.Value + "&" + count.CustomerName;
                    node.Leaf = count.TotalCount == 0;
                    node.Level = 2;
                    node.Branch = treeReq.Value;
                    resList.Add(node);
                }
            }
            else if (treeReq.Level == 3)
            {
                var arr = treeReq.Value.Split('&');
                string customerName = arr[1];
                string deliveryDate = arr[0];
                var detailList = DbContext.Queryable<Produce_Scheduling>()
                    .Where(condition)
                    .Where(t => t.IsDelete == false
                                && t.CustomerName == customerName
                                && t.DeliveryDate >= DateUtil.GetStartTime(DateTime.Parse(deliveryDate))
                                && t.DeliveryDate <= DateUtil.GetEndTime(DateTime.Parse(deliveryDate))).ToList();
                foreach (var detail in detailList)
                {
                    TreeNode node = new TreeNode();
                    node.Label = detail.ContractNo;
                    node.Value = detail.Id;
                    node.Leaf = true;
                    node.Level = 3;
                    node.Branch = arr[0];
                    resList.Add(node);
                }
            }

            return resList;
        }

        #endregion

        #region 查询统计树 (不含线体)

        public List<TreeNode> GetTreeNotLine(Produce_SchedulingListReq req, HostOrderTreeReq treeReq)
        {
            var condition = GetCondition(req);
            var resList = new List<TreeNode>();

            if (treeReq.Level == 0)
            {
                var countList = DbContext.Queryable<Produce_Scheduling>()
                    .Where(condition)
                    .Where(t => t.IsDelete == false)
                    .GroupBy(t => t.DeliveryDate)
                    .Select(t => new
                    {
                        t.DeliveryDate,
                        TotalCount = SqlFunc.AggregateCount(1) // ✅
                    })
                    .ToList();
                foreach (var count in countList)
                {
                    TreeNode node = new TreeNode();
                    node.Label = DateUtil.ParseStr(count.DeliveryDate) + " (" + count.TotalCount + ")";
                    node.Value = DateUtil.ParseStr(count.DeliveryDate);
                    node.Leaf = count.TotalCount == 0;
                    node.Level = 0;
                    node.Branch = DateUtil.ParseStr(count.DeliveryDate);
                    resList.Add(node);
                }
            }
            else if (treeReq.Level == 1)
            {
                var countList = DbContext.Queryable<Produce_Scheduling>()
                    .Where(condition)
                    .Where(t => t.IsDelete == false
                                && t.DeliveryDate >= DateUtil.GetStartTime(DateTime.Parse(treeReq.Value))
                                && t.DeliveryDate <= DateUtil.GetEndTime(DateTime.Parse(treeReq.Value)))
                    .GroupBy(t => t.CustomerName)
                    .Select(t => new
                    {
                        t.CustomerName,
                        TotalCount = SqlFunc.AggregateCount("*") // ✅ 直接传星号表达式
                    })
                    .ToList();
                foreach (var count in countList)
                {
                    TreeNode node = new TreeNode();
                    node.Label = count.CustomerName + " (" + count.TotalCount + ")";
                    node.Value = treeReq.Value + "&" + count.CustomerName;
                    node.Leaf = count.TotalCount == 0;
                    node.Level = 1;
                    node.Branch = treeReq.Value;
                    resList.Add(node);
                }
            }
            else if (treeReq.Level == 2)
            {
                var arr = treeReq.Value.Split('&');
                string customerName = arr[1];
                string deliveryDate = arr[0];
                var detailList = DbContext.Queryable<Produce_Scheduling>()
                    .Where(condition)
                    .Where(t => t.IsDelete == false
                                && t.CustomerName == customerName
                                && t.DeliveryDate >= DateUtil.GetStartTime(DateTime.Parse(deliveryDate))
                                && t.DeliveryDate <= DateUtil.GetEndTime(DateTime.Parse(deliveryDate))).ToList();
                foreach (var detail in detailList)
                {
                    TreeNode node = new TreeNode();
                    node.Label = detail.ContractNo;
                    node.Value = detail.Id;
                    node.Leaf = true;
                    node.Level = 2;
                    node.Branch = arr[0];
                    resList.Add(node);
                }
            }

            return resList;
        }

        #endregion

        #region 保存PCS信息

        /// <summary>
        /// 保存PCS
        /// </summary>
        /// <param name="produceSchedulingList"></param>
        /// <param name="errorMessage"></param>
        /// <returns></returns>
        public bool SaveForPcs(Produce_SchedulingPcsReq schedulingPcsReq, out string errorMessage)
        {
            errorMessage = "保存成功";
            // 校验账号密码
            var dicts = _dictionaryApp.GetList(t => t.TypeCode == "PcsConfig").ToList();
            if (dicts.Count == 0 || dicts[0].EnumValue != schedulingPcsReq.Account || dicts[0].EnumValue1 != schedulingPcsReq.Password)
            {
                errorMessage = "账号密码校验失败";
                return false;
            }

            var produceSchedulingList = schedulingPcsReq.ProduceSchedulingList;
            foreach (var produceScheduling in produceSchedulingList)
            {
                produceScheduling.Status = 0;
                produceScheduling.CTime = DateTime.Now;
            }

            var orderIds = produceSchedulingList.Select(t => t.OrderId).ToList();
            var existSchedulingList = GetList(t => orderIds.Contains(t.OrderId)).ToList();
            if (existSchedulingList.Count > 0)
            {
                orderIds = existSchedulingList.Select(t => t.OrderId).ToList();
                produceSchedulingList = produceSchedulingList.Where(t => !orderIds.Contains(t.OrderId)).ToList();
            }

            Insert(produceSchedulingList);
            return true;
        }

        #endregion

        #region 设定排产日期

        /// <summary>
        /// 设定排产日期
        /// </summary>
        /// <param name="req"></param>
        /// <param name="res"></param>
        /// <returns></returns>
        public bool SetProduceSchedulingDate(SetProduceSchedulingDateReq req, out string res)
        {
            res = "";
            var list = GetList(t => req.Ids.Contains(t.Id)).ToList();
            // var errCount = list.Where(t => t.Status > 1).Count();
            // if (errCount > 0)
            // {
            //     res = "有" + errCount + "条发运计划已经完成，不能修改 !";
            //     return false;
            // }

            foreach (var scheduling in list)
            {
                scheduling.PlanAssemblyDate = req.ProduceSchedulingDate;
            }
            
            DbContext.Ado.BeginTran();
            Update(list);

            
            // 调用更新计划订单接口
            List<EditPlanOrder.Table001086> editPlanOrderList = new List<EditPlanOrder.Table001086>();
            foreach (var produce in list)
            {
                if (produce.Status == 2)
                {
                    EditPlanOrder.Table001086 table = new EditPlanOrder.Table001086
                    {
                        WERKS = "2002",
                        VBELN = produce.SaleSapNo,
                        POSNR = produce.SaleSapLine?.ToString("D6"),
                        PSTTR = produce.PlanAssemblyDate != null ? produce.PlanAssemblyDate.Value.ToString("yyyyMMdd") : "",
                        VERID = produce.ProduceVersion
                    };
                    editPlanOrderList.Add(table);
                }
            }
            string editPlanOrderUri = ConfigurationManager.AppSettings["SapUrl"] + ConfigurationManager.AppSettings["EditAufnr"];
            string token = ConfigurationManager.AppSettings["SapToken"];
            string editPlanOrderResStr = HttpUtil.HttpPost($"{editPlanOrderUri}?token={token}", JsonConvert.SerializeObject(editPlanOrderList), "POST");
            var editPlanOrderRes = JsonConvert.DeserializeObject<EditPlanOrder>(editPlanOrderResStr);

            if (editPlanOrderRes.ETYPE != "S")
            {
                DbContext.Ado.RollbackTran();
                res = editPlanOrderRes.EMSG;
                return false;
            }
            
            DbContext.Ado.CommitTran();
            return true;
        }

        #endregion

        #region 设定生产线

        /// <summary>
        /// 设定生产线
        /// </summary>
        /// <param name="req"></param>
        /// <param name="resMsg"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public bool SetProduceLine(SetProduceLineReq req, out string resMsg)
        {
            try
            {
                resMsg = "";
                var list = GetList(t => req.Ids.Contains(t.Id)).ToList();
                // var errCount = list.Where(t => t.Status > 1).Count();
                // if (errCount > 0)
                // {
                //     resMsg = "有" + errCount + "条发运计划已经完成，不能修改!";
                //     return false;
                // }

                foreach (var scheduling in list)
                {
                    var produceLines = req.ProduceLine.Split('&');
                    scheduling.ProduceLine = produceLines[2];
                    scheduling.ProduceLineCode = produceLines[1];
                    scheduling.ProduceVersion = produceLines[0];
                }
                DbContext.Ado.BeginTran();
                Update(list);

                // 调用更新计划订单接口
                List<EditPlanOrder.Table001086> editPlanOrderList = new List<EditPlanOrder.Table001086>();
                foreach (var produce in list)
                {
                    if (produce.Status == 2)
                    {
                        EditPlanOrder.Table001086 table = new EditPlanOrder.Table001086
                        {
                            WERKS = "2002",
                            VBELN = produce.SaleSapNo,
                            POSNR = produce.SaleSapLine?.ToString("D6"),
                            PSTTR = produce.PlanTime != null ? produce.PlanTime.Value.ToString("yyyyMMdd") : "",
                            VERID = produce.ProduceVersion
                        };
                        editPlanOrderList.Add(table);
                    }
                }
                string editPlanOrderUri = ConfigurationManager.AppSettings["SapUrl"] + ConfigurationManager.AppSettings["EditAufnr"];
                string token = ConfigurationManager.AppSettings["SapToken"];
                string editPlanOrderResStr = HttpUtil.HttpPost($"{editPlanOrderUri}?token={token}", JsonConvert.SerializeObject(editPlanOrderList), "POST");
                var editPlanOrderRes = JsonConvert.DeserializeObject<EditPlanOrder>(editPlanOrderResStr);

                if (editPlanOrderRes.ETYPE != "S")
                {
                    DbContext.Ado.RollbackTran();
                    resMsg = editPlanOrderRes.EMSG;
                    return false;
                }
            
                DbContext.Ado.CommitTran();
            
                return true;
            }
            catch (Exception e)
            {
                if (DbContext.Ado.IsAnyTran())
                {
                    DbContext.Ado.RollbackTran();
                }
                Console.WriteLine(e);
                throw;
            }
        }

        #endregion

        #region 完成预排产

        /// <summary>
        /// 完成预排产
        /// </summary>
        /// <param name="req">预排产请求参数</param>
        /// <param name="user">当前用户</param>
        /// <param name="out_message">输出消息</param>
        /// <returns>是否成功</returns>
        public bool CompletePreProduce(CompletePreProduceReq req, Sys_User user, out string out_message)
        {
            out_message = string.Empty;

            try
            {
                // 1. 获取需要处理的订单列表并验证
                var produceList = GetList(t => req.Ids.Contains(t.Id)).ToList();
                if (produceList.Count == 0)
                {
                    out_message = "未找到需要处理的订单";
                    return false;
                }
                
                foreach (var produceScheduling in produceList)
                {
                    if (string.IsNullOrEmpty(produceScheduling.ProduceLine) || string.IsNullOrEmpty(produceScheduling.ProduceLineCode))
                    {
                        out_message = "合同号：" + produceScheduling.ContractNo + "，未设定生产线";
                        return false;
                    }
                }

                // 2. 初始化全局排除ID列表，确保彻底避免重叠计算
                var allCurrentProcessingIds = req.Ids.ToList();

                // 3. 处理排产序号和批次计算
                ProcessProductionSortingAndBatching(produceList, allCurrentProcessingIds);

                // 4. 调用SAP创建计划订单接口
                var createPlanOrder = CallSapCreatePlanOrder(produceList, req.PlanTime);

                // 5. 处理SAP接口响应结果并更新状态
                var resultMessages = ProcessPreProductionSapResponseAndUpdateStatus(produceList, createPlanOrder, user, out bool isSuccess);

                if (!isSuccess)
                {
                    out_message = "预排产失败\n" + string.Join("\n", resultMessages);
                    return true;
                }

                // 6. 执行数据库事务保存
                ExecutePreProductionDatabaseTransaction(produceList);

                // 7. 返回处理结果
                out_message = "预排产完成\n" + string.Join("\n", resultMessages);
                return true;
            }
            catch (Exception ex)
            {
                // 确保事务回滚
                if (DbContext.Ado.IsAnyTran())
                {
                    DbContext.Ado.RollbackTran();
                }

                out_message = ex.Message;
                return false;
            }
        }

        #endregion

        #region 排产序号生成

        /// <summary>
        /// 排产序号生成
        /// </summary>
        /// <param name="req">排产序号生成请求</param>
        /// <param name="resMsg">返回消息</param>
        /// <returns>是否成功</returns>
        public bool GenerationProduceSort(GenerationProduceSortReq req, out string resMsg)
        {
            resMsg = "";

            try
            {
                // 1. 参数验证
                if (req.ProduceSchedulingDate == null || req.ProduceSchedulingDate.Length != 2)
                {
                    resMsg = "请选择排产日期范围";
                    return false;
                }

                var startDate = req.ProduceSchedulingDate[0];
                var endDate = req.ProduceSchedulingDate[1];

                // 2. 构建查询条件
                var condition = PredicateBuilder.New<Produce_Scheduling>(t =>
                    t.IsDelete == false
                    && t.Status >= 1 // 只处理已设定生产线的数据
                    && t.PlanAssemblyDate >= startDate.Date
                    && t.PlanAssemblyDate <= endDate.Date);

                // 如果指定了生产线，添加生产线过滤条件
                if (!string.IsNullOrEmpty(req.ProduceLine))
                {
                    condition = condition.And(t => t.ProduceLine == req.ProduceLine);
                }

                // 3. 获取需要处理的排产数据
                var produceList = GetList(condition).ToList().OrderBy(t => t.PlanAssemblyDate)
                    .ThenBy(t => t.ProduceLine)
                    .ThenBy(t => t.CustomerName)
                    .ThenBy(t => t.SapProductModel ?? t.ProduceModel)
                    .ToList();

                if (produceList.Count == 0)
                {
                    resMsg = "未找到符合条件的排产数据";
                    return false;
                }

                // 4. 验证生产线配置
                foreach (var produce in produceList)
                {
                    if (string.IsNullOrEmpty(produce.ProduceLine) || string.IsNullOrEmpty(produce.ProduceLineCode))
                    {
                        resMsg = $"合同号：{produce.ContractNo}，未设定生产线";
                        return false;
                    }
                }

                // 5. 执行排产序号和批次计算
                ProcessProductionSortingAndBatchingForGeneration(produceList, req.IsRegenerate);

                // 6. 生成出厂编号
                GenerateFactoryNumbers(produceList);

                // 7. 保存数据
                DbContext.Ado.BeginTran();
                try
                {
                    Update(produceList);
                    DbContext.Ado.CommitTran();

                    resMsg = $"成功生成 {produceList.Count} 条排产序号、批次和出厂编号";
                    return true;
                }
                catch
                {
                    DbContext.Ado.RollbackTran();
                    throw;
                }
            }
            catch (Exception ex)
            {
                if (DbContext.Ado.IsAnyTran())
                {
                    DbContext.Ado.RollbackTran();
                }
                resMsg = ex.Message;
                return false;
            }
        }

        #endregion

        #region 设置序号&批次

        public bool SetSortAndBatch(SetSortAndBatchReq[] req, out string resMsg)
        {
            resMsg = "";
            var ids = req.Select(t => t.Id).ToArray();
            var list = GetListByKeys(ids);
            if (list.Count == 0)
            {
                resMsg = "数据不存在";
                return false;
            }

            var dictionary = req.ToDictionary(t => t.Id, t => t);
            foreach (var produceScheduling in list)
            {
                produceScheduling.ProduceSort = dictionary[produceScheduling.Id].ProduceSort;
                produceScheduling.ProduceBatch = dictionary[produceScheduling.Id].ProduceBatch;
            }

            Update(list);
            return true;
        }

        #endregion

        #region 预排产相关辅助方法

        /// <summary>
        /// 处理排产序号和批次计算
        /// </summary>
        /// <param name="produceList">排产列表</param>
        /// <param name="allCurrentProcessingIds">当前操作的所有数据ID</param>
        private void ProcessProductionSortingAndBatching(List<Produce_Scheduling> produceList, List<string> allCurrentProcessingIds)
        {
            // 获取客户优先级配置
            var customerCodes = produceList.Select(t => t.CustomerCode).Distinct().ToList();
            var customerProduces = _customerProduceApp.GetList(t => customerCodes.Contains(t.CustomerCode)).ToList();
            var customerProduceMap = customerProduces.ToDictionary(t => t.CustomerCode, t => t.ProduceSort);

            // 按排产日期分组处理
            var schedulingDateGroups = produceList.GroupBy(t => t.PlanAssemblyDate).OrderBy(g => g.Key);

            foreach (var group in schedulingDateGroups)
            {
                ProcessAssemblyDateGroup(group.ToList(), customerProduceMap, allCurrentProcessingIds);
            }
        }

        /// <summary>
        /// 处理装配日期分组的排产逻辑
        /// </summary>
        /// <param name="ordersInGroup">分组内的订单列表</param>
        /// <param name="customerProduceMap">客户优先级映射</param>
        /// <param name="allCurrentProcessingIds">当前操作的所有数据ID</param>
        private void ProcessAssemblyDateGroup(List<Produce_Scheduling> ordersInGroup,
            Dictionary<string, decimal> customerProduceMap, List<string> allCurrentProcessingIds)
        {
            // 设置客户优先级
            foreach (var order in ordersInGroup)
            {
                if (customerProduceMap.TryGetValue(order.CustomerCode, out var priority))
                {
                    order.ProduceSort = priority;
                }
            }

            // 按优先级、客户名称、机型排序
            ordersInGroup = ordersInGroup.OrderBy(t => t.ProduceSort)
                .ThenBy(t => t.CustomerName)
                .ThenBy(t => t.SapProductModel ?? t.ProduceModel)
                .ToList();

            // 获取线体批次配置
            var lineBatchConfigs = _lineBatchApp.GetList(t => ordersInGroup.Select(o => o.ProduceLine).Contains(t.LineName))
                .ToList()
                .ToDictionary(t => t.LineName, t => t);

            // 按线体分组处理
            var lineGroups = ordersInGroup.GroupBy(t => t.ProduceLine);
            foreach (var lineGroup in lineGroups)
            {
                ProcessProductionLineGroup(lineGroup.ToList(), lineGroup.Key, lineBatchConfigs, allCurrentProcessingIds);
            }
        }

        /// <summary>
        /// 处理生产线分组的排产逻辑
        /// </summary>
        /// <param name="lineOrders">生产线内的订单列表</param>
        /// <param name="lineName">生产线名称</param>
        /// <param name="lineBatchConfigs">线体批次配置</param>
        /// <param name="allCurrentProcessingIds">当前操作的所有数据ID</param>
        private void ProcessProductionLineGroup(List<Produce_Scheduling> lineOrders, string lineName,
            Dictionary<string, MD_LineBatch> lineBatchConfigs, List<string> allCurrentProcessingIds)
        {
            var lineConfig = lineBatchConfigs.ContainsKey(lineName) ? lineBatchConfigs[lineName] : null;
            var batchSize = lineConfig?.BatchNo ?? 10;
            var startTime = lineConfig?.StartTime ?? 0;

            // 按装配日期分组
            var assemblyDateGroups = lineOrders.GroupBy(t => t.PlanAssemblyDate?.Date ?? DateTime.Now.Date);

            foreach (var assemblyDateGroup in assemblyDateGroups)
            {
                ProcessAssemblyDateOrdersWithinLine(assemblyDateGroup.ToList(), assemblyDateGroup.Key,
                    lineName, batchSize, startTime, allCurrentProcessingIds);
            }
        }

        /// <summary>
        /// 处理生产线内按装配日期分组的订单序号和入库时间计算
        /// </summary>
        /// <param name="assemblyDateOrders">装配日期内的订单列表</param>
        /// <param name="assemblyDate">装配日期</param>
        /// <param name="lineName">生产线名称</param>
        /// <param name="batchSize">批次大小</param>
        /// <param name="startTime">开始时间（分钟）</param>
        /// <param name="allCurrentProcessingIds">当前操作的所有数据ID</param>
        private void ProcessAssemblyDateOrdersWithinLine(List<Produce_Scheduling> assemblyDateOrders, DateTime assemblyDate,
            string lineName, int batchSize, int startTime, List<string> allCurrentProcessingIds)
        {
            // 获取该装配日期在该生产线的所有已排产记录（排除当前操作的所有数据）
            var existingAssemblyOrders = GetList(t => t.Status > 0
                                                      && t.ProduceLine == lineName
                                                      && t.PlanAssemblyDate.HasValue
                                                      && t.PlanAssemblyDate.Value.Date == assemblyDate
                                                      && !allCurrentProcessingIds.Contains(t.Id)
                                                      && t.IsDelete == false).ToList();

            // 计算该装配日期在该生产线的起始序号、批次和入库时间基准
            var assemblyMaxSort = 0m;
            var assemblyMaxBatch = 0;
            var maxInStoreTimeToday = DateTime.MinValue;

            if (existingAssemblyOrders.Any())
            {
                // 基于排除当前操作数据后的历史记录获取最大值
                assemblyMaxSort = existingAssemblyOrders.Max(t => t.ProduceSort ?? 0);
                assemblyMaxBatch = existingAssemblyOrders.Max(t => t.ProduceBatch ?? 0);

                // 获取当天该生产线的最大入库时间作为基准
                if (existingAssemblyOrders.Any(t => t.InStoreTime.HasValue))
                {
                    maxInStoreTimeToday = existingAssemblyOrders.Where(t => t.InStoreTime.HasValue)
                        .Max(t => t.InStoreTime.Value);
                }
            }

            // 计算批次开始时间 - 基于装配日期和线体配置
            var batchStartTime = assemblyDate;
            if (startTime > 0)
            {
                batchStartTime = batchStartTime.AddMinutes(startTime);
            }

            // 如果当天没有已排产记录，使用线体配置的开始时间作为基准
            if (maxInStoreTimeToday == DateTime.MinValue)
            {
                maxInStoreTimeToday = batchStartTime;
            }

            // 设置序号、批次和入库时间
            CalculateOrderSequenceAndInStoreTime(assemblyDateOrders, assemblyMaxSort, assemblyMaxBatch,
                maxInStoreTimeToday, batchSize, existingAssemblyOrders.Any());
        }

        /// <summary>
        /// 计算订单序号、批次和入库时间
        /// </summary>
        /// <param name="assemblyDateOrders">装配日期内的订单列表</param>
        /// <param name="assemblyMaxSort">当前最大序号</param>
        /// <param name="assemblyMaxBatch">当前最大批次</param>
        /// <param name="maxInStoreTimeToday">当天最大入库时间</param>
        /// <param name="batchSize">批次大小</param>
        /// <param name="hasExistingOrders">是否存在历史订单</param>
        private void CalculateOrderSequenceAndInStoreTime(List<Produce_Scheduling> assemblyDateOrders,
            decimal assemblyMaxSort, int assemblyMaxBatch, DateTime maxInStoreTimeToday,
            int batchSize, bool hasExistingOrders)
        {
            for (int i = 0; i < assemblyDateOrders.Count; i++)
            {
                var order = assemblyDateOrders[i];

                // 序号累加 - 基于历史数据的最大序号继续增加
                assemblyMaxSort++;
                order.ProduceSort = assemblyMaxSort;

                // 计算批次 - 基于该装配日期的现有最大批次
                var batchIndex = i / batchSize;
                order.ProduceBatch = assemblyMaxBatch + batchIndex + 1;

                // 计算入库时间
                order.InStoreTime = CalculateInStoreTime(i, assemblyDateOrders, maxInStoreTimeToday, batchSize, hasExistingOrders);
            }
        }

        /// <summary>
        /// 计算单个订单的入库时间
        /// </summary>
        /// <param name="orderIndex">订单在列表中的索引</param>
        /// <param name="assemblyDateOrders">装配日期内的订单列表</param>
        /// <param name="maxInStoreTimeToday">当天最大入库时间</param>
        /// <param name="batchSize">批次大小</param>
        /// <param name="hasExistingOrders">是否存在历史订单</param>
        /// <returns>计算得出的入库时间</returns>
        private DateTime CalculateInStoreTime(int orderIndex, List<Produce_Scheduling> assemblyDateOrders,
            DateTime maxInStoreTimeToday, int batchSize, bool hasExistingOrders)
        {
            // 根据线体批次配置计算每台设备的时间间隔（分钟）
            // 假设每批次在1小时内完成，平均分配给每台设备
            var minutesPerUnit = batchSize > 0 ? 60.0 / batchSize : 10.0; // 默认每台设备10分钟

            // 限制时间间隔的合理范围（1-60分钟）
            if (minutesPerUnit < 1) minutesPerUnit = 1;
            if (minutesPerUnit > 60) minutesPerUnit = 60;

            // 基于当天最大入库时间累加计算新的入库时间
            if (orderIndex == 0)
            {
                // 第一台设备：如果当天有历史记录，从最大时间后的下一个时间间隔开始
                // 如果当天没有历史记录，使用配置的开始时间
                if (hasExistingOrders)
                {
                    return maxInStoreTimeToday.AddMinutes(minutesPerUnit);
                }
                else
                {
                    return maxInStoreTimeToday;
                }
            }
            else
            {
                // 后续设备基于前一台设备的时间累加
                var previousOrder = assemblyDateOrders[orderIndex - 1];
                return previousOrder.InStoreTime?.AddMinutes(minutesPerUnit) ??
                       maxInStoreTimeToday.AddMinutes((orderIndex + 1) * minutesPerUnit);
            }
        }

        /// <summary>
        /// 调用SAP创建计划订单接口
        /// </summary>
        /// <param name="produceList">排产列表</param>
        /// <param name="planTime">计划时间</param>
        /// <returns>创建计划订单响应结果</returns>
        private EditPlanOrder CallSapCreatePlanOrder(List<Produce_Scheduling> produceList, DateTime planTime)
        {
            List<EditPlanOrder.Table001086> editPlanOrderList = produceList.Select(produce => new EditPlanOrder.Table001086
            {
                WERKS = "2002",
                VBELN = produce.SaleSapNo,
                POSNR = produce.SaleSapLine?.ToString("D6"),
                PSTTR = planTime.ToString("yyyyMMdd"),
                VERID = produce.ProduceVersion
            }).ToList().Where(t => !string.IsNullOrEmpty(t.VBELN)).ToList();

            string editPlanOrderUri = ConfigurationManager.AppSettings["SapUrl"] + ConfigurationManager.AppSettings["EditPlanOrder"];
            string token = ConfigurationManager.AppSettings["SapToken"];
            string createPlanOrderRes = HttpUtil.HttpPost($"{editPlanOrderUri}?token={token}", JsonConvert.SerializeObject(editPlanOrderList), "POST");

            if (createPlanOrderRes == "无匹配数据")
            {
                throw new Exception(createPlanOrderRes);
            }

            return JsonConvert.DeserializeObject<EditPlanOrder>(createPlanOrderRes);
        }

        /// <summary>
        /// 处理预排产SAP接口响应结果并更新状态
        /// </summary>
        /// <param name="produceList">排产列表</param>
        /// <param name="createPlanOrder">计划订单创建结果</param>
        /// <param name="user">当前用户</param>
        /// <returns>处理结果详细信息</returns>
        private List<string> ProcessPreProductionSapResponseAndUpdateStatus(List<Produce_Scheduling> produceList,
            EditPlanOrder createPlanOrder, Sys_User user, out bool isSuccess)
        {
            var resultMessages = new List<string>();
            isSuccess = true;

            foreach (var produce in produceList)
            {
                int step = 0;
                string resultMsg = $"合同号: {produce.ContractNo ?? "无"}, ";

                if (createPlanOrder.ETYPE != "S")
                {
                    produce.PlanMsg = createPlanOrder.EMSG;
                    produce.PlanStatus = "E";
                    resultMsg += $"状态: 失败, 失败消息: {produce.PlanMsg}";
                    isSuccess = false;
                }
                // else
                // {
                //     var createPlanOrders = createPlanOrder.DataOut.Where(t => t.VBELN == produce.SaleSapNo && t.POSNR == produce.SaleSapLine?.ToString("D6")).ToList();
                //     var createPlanErr = createPlanOrders.Where(t => t.ETYPE != "S").ToList();
                //     if (createPlanErr.Count > 0)
                //     {
                //         produce.PlanMsg = string.Join(",", createPlanErr.Select(t => t.VBELN + t.POSNR + t.EMSG));
                //         produce.PlanStatus = "E";
                //         resultMsg += $"状态: 失败, 失败消息: {produce.PlanMsg}";
                //     }
                //     else
                //     {
                //         step++;
                //         produce.PlanStatus = "S";
                //         resultMsg += "计划订单: 成功";
                //     }
                // }

                produce.PlanTime = DateTime.Now;
                produce.Status = 2; // 预排产状态
                produce.MUser = user.LoginAccount;
                produce.MTime = DateTime.Now;

                resultMessages.Add(resultMsg + "<br>");
            }

            return resultMessages;
        }

        /// <summary>
        /// 执行预排产数据库事务保存
        /// </summary>
        /// <param name="produceList">排产列表</param>
        private void ExecutePreProductionDatabaseTransaction(List<Produce_Scheduling> produceList)
        {
            DbContext.Ado.BeginTran();
            try
            {
                Update(produceList);
                DbContext.Ado.CommitTran();
            }
            catch
            {
                DbContext.Ado.RollbackTran();
                throw;
            }
        }

        #endregion

        #region 正式排产确认相关辅助方法

        /// <summary>
        /// 检查生产线产能上限
        /// </summary>
        /// <param name="produceSchedulingList">排产列表</param>
        /// <param name="capacityWarnings">产能警告信息</param>
        /// <returns>是否通过产能检查</returns>
        private bool CheckLineCapacity(List<Produce_Scheduling> produceSchedulingList, out List<string> capacityWarnings)
        {
            capacityWarnings = new List<string>();

            // 获取生产线产能配置
            var lineGroups = produceSchedulingList.GroupBy(t => t.ProduceLine).Where(g => !string.IsNullOrEmpty(g.Key)).ToList();
            var lineCapacities = _produceLineCapacityApp.GetList(t => lineGroups.Select(g => g.Key).Contains(t.WorkCenterName)).ToList();
            var lineCapacityMap = lineCapacities.ToDictionary(t => t.WorkCenterName, t => t.Capacity);

            // 按生产线和排产日期分组检查产能
            var lineDateGroups = produceSchedulingList.Where(t => t.ProduceSchedulingDate.HasValue && !string.IsNullOrEmpty(t.ProduceLine))
                .GroupBy(t => new { t.ProduceLine, t.ProduceSchedulingDate });

            foreach (var group in lineDateGroups)
            {
                var lineName = group.Key.ProduceLine;
                var scheduleDate = group.Key.ProduceSchedulingDate.Value;
                var lineCount = group.Count();

                // 获取该生产线在该排产日期当前已排产的数量
                var existingCount = GetList(t => t.ProduceLine == lineName
                                                 && t.ProduceSchedulingDate == scheduleDate
                                                 && t.Status >= 3
                                                 && t.IsDelete == false).Count();

                // 检查是否超过产能上限
                if (lineCapacityMap.ContainsKey(lineName))
                {
                    var capacity = lineCapacityMap[lineName];
                    if (existingCount + lineCount > capacity)
                    {
                        capacityWarnings.Add($"生产线 {lineName} 在排产日期 {scheduleDate:yyyy-MM-dd} 的产能上限为 {capacity}，当前已排产 {existingCount}，本次新增 {lineCount}，将超出产能上限！");
                    }
                }
            }

            return capacityWarnings.Count == 0;
        }

        /// <summary>
        /// 处理部件代码生成
        /// </summary>
        /// <param name="produceSchedulingList">排产列表</param>
        /// <param name="produceMax">生产编号配置</param>
        private void ProcessPartCodeGeneration(List<Produce_Scheduling> produceSchedulingList, Sys_Dictionary produceMax)
        {
            var partMakeCompanyList = _partMakeCompanyApp.GetList().ToList();
            var partProduceLineList = _partProduceLineApp.GetList().ToList();
            var partMakeCompanyMap = partMakeCompanyList.ToDictionary(t => t.CompanyName, t => t.CompanyCode);

            foreach (var produceScheduling in produceSchedulingList)
            {
                // 符合条件的客户对部件代码处理
                if (partMakeCompanyMap.ContainsKey(produceScheduling.CustomerName))
                {
                    var companyCode = partMakeCompanyMap[produceScheduling.CustomerName];
                    var partProduceLineResList = partProduceLineList.Where(t => t.ProduceLineName == produceScheduling.ProduceLine).ToList();
                    if (partProduceLineResList.Count > 0)
                    {
                        produceMax.EnumValue1 = ThirtyThreeRadixUtil.IncrementCustomBase(produceMax.EnumValue);
                        var partProduceLine = partProduceLineResList[0];
                        var part = partProduceLine.PartCode + companyCode + produceMax.EnumValue + "K32" + produceMax.EnumValue1;
                        produceScheduling.PartCode = PartCodeUtil.GetCode(part, partProduceLine.PartCode);
                        produceScheduling.UpOverSpeedCode = PartCodeUtil.GetCode(part, partProduceLine.UpOverSpeedCode);
                        produceScheduling.AccidentMoveCode = PartCodeUtil.GetCode(part, partProduceLine.AccidentMoveCode);
                    }
                }
            }
        }

        /// <summary>
        /// 调用SAP创建生产订单接口
        /// </summary>
        /// <param name="produceSchedulingList">排产列表</param>
        /// <returns>创建生产订单响应结果</returns>
        private EditAufnr CallSapCreateProductionOrder(List<Produce_Scheduling> produceSchedulingList)
        {
            string createProdOrderUri = ConfigurationManager.AppSettings["SapUrl"] + ConfigurationManager.AppSettings["EditAufnr"];
            string token = ConfigurationManager.AppSettings["SapToken"];

            List<EditAufnr.Table001080> createProdReqList = produceSchedulingList.Select(produce => new EditAufnr.Table001080
            {
                WERKS = "2002",
                VBELN = produce.SaleSapNo,
                POSNR = produce.SaleSapLine?.ToString("D6"),
                PSTTR = produce.PlanAssemblyDate.Value.ToString("yyyyMMdd"),
                VERID = produce.ProduceVersion,
            }).ToList();

            string createProdOrderRst = HttpUtil.HttpPost($"{createProdOrderUri}?token={token}", JsonConvert.SerializeObject(createProdReqList), "POST");
            if (createProdOrderRst == "无匹配数据")
            {
                throw new Exception(createProdOrderRst);
            }

            return JsonConvert.DeserializeObject<EditAufnr>(createProdOrderRst);
        }

        /// <summary>
        /// 调用SAP创建采购订单接口
        /// </summary>
        /// <param name="produceSchedulingList">排产列表</param>
        /// <returns>创建采购订单响应结果</returns>
        private CreatePruchase0rder CallSapCreatePurchaseOrder(List<Produce_Scheduling> produceSchedulingList)
        {
            string createPruchaseOrderUri = ConfigurationManager.AppSettings["SapUrl"] + ConfigurationManager.AppSettings["CreatePruchaseOrder"];
            string token = ConfigurationManager.AppSettings["SapToken"];

            List<CreatePruchase0rder.Table001061> createPurchaseReqList = produceSchedulingList.Select(produce => new CreatePruchase0rder.Table001061()
            {
                WERKS = "2002",
                VBELN = produce.SaleSapNo,
                POSNR = produce.SaleSapLine?.ToString("D6"),
                LFDAT = produce.PlanAssemblyDate.Value.ToString("yyyyMMdd"),
                XT = produce.ProduceLine,
                PC = produce.ProduceBatch.ToString()
            }).ToList();

            string createPurchaseReqRst = HttpUtil.HttpPost($"{createPruchaseOrderUri}?token={token}", JsonConvert.SerializeObject(createPurchaseReqList), "POST");
            if (createPurchaseReqRst == "无匹配数据")
            {
                throw new Exception(createPurchaseReqRst);
            }

            return JsonConvert.DeserializeObject<CreatePruchase0rder>(createPurchaseReqRst);
        }

        /// <summary>
        /// 处理SAP接口响应结果并更新排产数据状态
        /// </summary>
        /// <param name="produceSchedulingList">排产列表</param>
        /// <param name="createProdOrderRes">生产订单创建结果</param>
        /// <param name="createPruchase0rderRes">采购订单创建结果</param>
        /// <returns>处理结果详细信息</returns>
        private (List<string> resultMessages, List<Produce_SchedulingDetail> schedulingDetails, List<Produce_PurchaseOrder> purchaseOrders) ProcessSapResponseAndUpdateStatus(
            List<Produce_Scheduling> produceSchedulingList,
            EditAufnr createProdOrderRes,
            CreatePruchase0rder createPruchase0rderRes)
        {
            var resultMessages = new List<string>();
            var schedulingDetails = new List<Produce_SchedulingDetail>();
            var purchaseOrders = new List<Produce_PurchaseOrder>();

            foreach (var produce in produceSchedulingList)
            {
                string resultMsg = $"合同号: {produce.ContractNo ?? "无"}, ";
                int step = 0;

                // 处理生产订单结果
                var dataOut = createProdOrderRes.DataOut;
                var createProdOrders = dataOut.Where(t => t.VBELN == produce.SaleSapNo && t.POSNR == produce.SaleSapLine?.ToString("D6")).ToList();
                if (createProdOrderRes.ETYPE == "E")
                {
                    produce.PurchaseMsg = "生产订单失败：" + createPruchase0rderRes.EMSG;
                    produce.PurchaseStatus = "E";
                    resultMsg += $", 生产订单失败: " + createPruchase0rderRes.EMSG;
                }
                // 处理生产订单详情
                ProcessProductionOrderDetails(produce, createProdOrders, schedulingDetails, ref step, ref resultMsg);

                // 处理采购订单结果
                ProcessPurchaseOrderResults(produce, createPruchase0rderRes, purchaseOrders, ref step, ref resultMsg);

                // 根据处理结果更新状态
                if (step == 2)
                {
                    produce.Status = 3; // 排产完成
                    produce.MUser = "System";
                    produce.MTime = DateTime.Now;
                }

                resultMessages.Add(resultMsg + "<br>");
            }

            return (resultMessages, schedulingDetails, purchaseOrders);
        }

        /// <summary>
        /// 处理生产订单详情
        /// </summary>
        private void ProcessProductionOrderDetails(Produce_Scheduling produce, List<EditAufnr.Table001080> createProdOrders,
            List<Produce_SchedulingDetail> schedulingDetails, ref int step, ref string resultMsg)
        {
            foreach (var createProdOrder in createProdOrders)
            {
                var schedulingDetail = new Produce_SchedulingDetail
                {
                    Id = Guid.NewGuid().ToString(),
                    SapNo = createProdOrder.VBELN,
                    SapLine = int.Parse(createProdOrder.POSNR),
                    MaterialCode = createProdOrder.MATNR,
                    Status = createProdOrder.ETYPE,
                    Pid = produce.Id,
                    ProduceOrderNo = createProdOrder.AUFNR,
                    Massage = createProdOrder.EMSG
                };

                schedulingDetails.Add(schedulingDetail);
            }

            if (createProdOrders.Count == 0)
            {
                produce.ProduceStatus = "E";
                produce.ProduceMsg = "生产订单失败 空";
                resultMsg += $"生产订单: 失败消息 {produce.ProduceMsg}";
            }
            else
            {
                var createProds = createProdOrders.Where(t => t.ETYPE != "S").ToList();
                if (createProds.Count > 0)
                {
                    produce.ProduceStatus = "E";
                    produce.ProduceMsg = string.Join(",", createProds.Select(t => t.EMSG));
                    resultMsg += $"生产订单: 失败消息 {produce.ProduceMsg}";
                }
                else
                {
                    step++;
                    produce.ProduceStatus = "S";
                    produce.ProduceMsg = "";
                    resultMsg += "生产订单: 成功";
                }
            }
        }

        /// <summary>
        /// 处理采购订单结果
        /// </summary>
        private void ProcessPurchaseOrderResults(Produce_Scheduling produce, CreatePruchase0rder createPruchase0rderRes,
            List<Produce_PurchaseOrder> purchaseOrders, ref int step, ref string resultMsg)
        {
            if (createPruchase0rderRes.ETYPE == "E")
            {
                produce.PurchaseMsg = "采购订单失败：" + createPruchase0rderRes.EMSG;
                produce.PurchaseStatus = "E";
                resultMsg += $", 采购订单失败: " + createPruchase0rderRes.EMSG;
            }
            else
            {
                step++;
                produce.PurchaseMsg = "";
                produce.PurchaseStatus = "S";
            }
            
            // var createPruchase = createPruchase0rderRes.DataOut.Where(t => t.VBELN == produce.SaleSapNo && t.POSNR == produce.SaleSapLine?.ToString("D6")).ToList();
            
            // 校验返回内容大于0才正常
            // if (createPruchase.Count > 0)
            // {
            //     foreach (var pruchase in createPruchase)
            //     {
            //         var producePurchaseOrder = new Produce_PurchaseOrder
            //         {
            //             PurchaseOrderLine = int.Parse(pruchase.EBELP),
            //             PurchaseOrderNo = pruchase.EBELN,
            //             Quantity = decimal.Parse(pruchase.MENGE),
            //             Status = pruchase.ETYPE,
            //             Massage = pruchase.EMSG,
            //             Pid = produce.Id,
            //             SapNo = pruchase.VBELN,
            //             SapLine = int.Parse(pruchase.POSNR),
            //             MaterialCode = pruchase.IDNRK
            //         };
            //         purchaseOrders.Add(producePurchaseOrder);
            //     }
            //
            //     var createPruchaseErr = createPruchase.Where(t => t.ETYPE != "S").ToList();
            //     if (createPruchaseErr.Count > 0)
            //     {
            //         produce.PurchaseMsg = string.Join(",", createPruchaseErr.Select(t => t.EMSG));
            //         produce.PurchaseStatus = "E";
            //         resultMsg += $", 采购申请失败: {produce.PurchaseMsg}";
            //     }
            //     else
            //     {
            //         step++;
            //         produce.PurchaseMsg = "";
            //         produce.PurchaseStatus = "S";
            //     }
            // }
            
        }

        /// <summary>
        /// 创建报工数据
        /// </summary>
        /// <param name="produceSchedulingList">排产列表</param>
        /// <param name="schedulingDetails">排产详情列表</param>
        /// <param name="currentUser">当前用户</param>
        /// <returns>报工数据和报工详情数据</returns>
        private (List<Produce_Report> reports, List<Produce_ReportDetail> reportDetails) CreateReportData(
            List<Produce_Scheduling> produceSchedulingList,
            List<Produce_SchedulingDetail> schedulingDetails,
            Sys_User currentUser)
        {
            var reports = new List<Produce_Report>();
            var reportDetails = new List<Produce_ReportDetail>();

            // 只处理成功的生产订单（状态为3）
            var schedulings = produceSchedulingList.Where(t => t.Status == 3).ToList();
            var schedulingDict = schedulings.ToDictionary(t => t.Id, t => t);

            // 获取物料版本信息
            var (getMatnrVersionDict, reportStationDict, caViewDict) = GetMaterialVersionAndStationInfo(schedulingDetails);

            // 根据工单获取工单组件数据
            var aufnrCompDict = GetAufnrCompList(schedulingDetails);

            foreach (var schedulingDetail in schedulingDetails)
            {
                if (!string.IsNullOrEmpty(schedulingDetail.MaterialCode))
                {
                    if (!schedulingDict.ContainsKey(schedulingDetail.Pid) ||
                        !getMatnrVersionDict.ContainsKey(schedulingDetail.MaterialCode))
                    {
                        continue;
                    }
                    
                    var scheduling = schedulingDict[schedulingDetail.Pid];
                    var matnrVersion = getMatnrVersionDict[schedulingDetail.MaterialCode];

                    // 处理CA状态标记
                    int caStatus = 0;
                    string caSequenceNo = "";
                    if (aufnrCompDict.ContainsKey(schedulingDetail.ProduceOrderNo))
                    {
                        (caStatus, caSequenceNo) = ProcessCaStatus(scheduling.InStoreTime, caViewDict, aufnrCompDict[schedulingDetail.ProduceOrderNo]);
                    }

                    // 创建报工基础信息
                    var report = CreateReportBasicInfo(scheduling, schedulingDetail, matnrVersion, schedulings, currentUser, caStatus, caSequenceNo);

                    // 创建报工站点明细
                    CreateReportStationDetails(report, schedulingDetail, reportStationDict, reportDetails, currentUser);

                    reports.Add(report);
                }
            }

            return (reports, reportDetails);
        }

        private Dictionary<string, List<AufnrComp.Table001078>> GetAufnrCompList(List<Produce_SchedulingDetail> schedulingDetails)
        {
            // 获取物料版本信息
            string getAufnrComp = ConfigurationManager.AppSettings["SapUrl"] + ConfigurationManager.AppSettings["GetAufnrComp"];
            string token = ConfigurationManager.AppSettings["SapToken"];
            var lineQueries = schedulingDetails.Select(detail => new AufnrComp.Table001078()
            {
                AUFNR = detail.ProduceOrderNo
            }).ToList();
            lineQueries = lineQueries.Where(t => !string.IsNullOrEmpty(t.AUFNR)).ToList();
            string postRst = HttpUtil.HttpPost(getAufnrComp + "?token=" + token, JsonConvert.SerializeObject(lineQueries), "POST");
            var result = JsonConvert.DeserializeObject<AufnrComp>(postRst);
            if (result.ETYPE == "E")
            {
                throw new Exception();
            }

            // 转为Dict
            return result.DataOut.GroupBy(t => t.AUFNR).ToDictionary(t => t.Key, t => t.ToList());
        }

        /// <summary>
        /// 获取物料版本和站点信息
        /// </summary>
        private (Dictionary<string, GetMatnrVersionRes> getMatnrVersionDict,
            Dictionary<string, List<MD_ReportStation>> reportStationDict,
            Dictionary<string, CaView> caViewDict) GetMaterialVersionAndStationInfo(List<Produce_SchedulingDetail> schedulingDetails)
        {
            var materialCodeList = schedulingDetails.Select(t => t.MaterialCode).Distinct().ToList();

            // 获取物料版本信息
            string queryMatnrVersionUri = ConfigurationManager.AppSettings["SapUrl"] + ConfigurationManager.AppSettings["GetMatnrVersion"];
            string token = ConfigurationManager.AppSettings["SapToken"];
            var lineQueries = materialCodeList.Select(material => new GetMatnrVersionReq
            {
                WERKS = "2002",
                MATNR = material,
                VERID = "0001",
                ADATU = DateTime.Now.ToString("yyyyMMdd")
            }).ToList();
            string postRst = HttpUtil.HttpPost(queryMatnrVersionUri + "?token=" + token, JsonConvert.SerializeObject(lineQueries), "POST");
            var result = JsonConvert.DeserializeObject<List<GetMatnrVersionRes>>(postRst);
            var getMatnrVersionDict = result.GroupBy(t => t.MATNR).ToDictionary(t => t.Key, t => t.ToList().FirstOrDefault());

            // 获取站点信息
            var stationList = result.Select(t => t.ARBPL).ToList();
            var reportStationList = _reportStationApp.GetList(t => stationList.Contains(t.WorkCenterCode)).ToList();
            var reportStationDict = reportStationList.GroupBy(t => t.WorkCenterCode).ToDictionary(t => t.Key, t => t.ToList());

            // 获取CA变更信息
            var caViewList = DbContextForOracle.Queryable<CaView>().Where(t => materialCodeList.Contains(t.MaterialCode) && (t.CaStatus == 0 || t.CaStatus == null)).ToList();
            var caHpViewList = DbContextForOracle.Queryable<CaView>().AS("CLOUDPIVOT.CaHpView").Where(t => materialCodeList.Contains(t.MaterialCode) && (t.CaStatus == 0 || t.CaStatus == null)).ToList();

            // 合并两个查询结果
            var allCaViewList = new List<CaView>();
            allCaViewList.AddRange(caViewList);
            allCaViewList.AddRange(caHpViewList);

            // 对合并后的数据进行处理
            foreach (var caView in allCaViewList)
            {
                if (caView.ChangeType == "会评")
                {
                    caView.CompleteDate = caView.MeetingCompleteDate;
                }
            }

            // 创建字典时，如果同一个MaterialCode在两个数据源中都存在，优先使用第一个（caViewList中的数据）
            var caViewDict = allCaViewList
                .GroupBy(t => t.MaterialCode)
                .ToDictionary(g => g.Key, g => g.ToList().FirstOrDefault());
            return (getMatnrVersionDict, reportStationDict, caViewDict);
        }

        /// <summary>
        /// 处理CA状态标记
        /// </summary>
        private (int, string) ProcessCaStatus(DateTime? inStoreTime, Dictionary<string, CaView> caViewDict, List<AufnrComp.Table001078> aufnrCompList)
        {
            int caStatus = 0;
            string caSequenceNo = "";
            foreach (var table001078 in aufnrCompList)
            {
                if (caViewDict.ContainsKey(table001078.MATNR))
                {
                    var caView = caViewDict[table001078.MATNR];
                    if (inStoreTime > caView.CompleteDate)
                    {
                        caStatus = 1;
                        caSequenceNo = caView.SequenceNo;
                        caViewDict.Remove(table001078.MATNR);
                        return (caStatus, caSequenceNo);
                    }
                }
            }

            return (caStatus, caSequenceNo);
        }

        /// <summary>
        /// 创建报工基础信息
        /// </summary>
        private Produce_Report CreateReportBasicInfo(Produce_Scheduling scheduling, Produce_SchedulingDetail schedulingDetail,
            GetMatnrVersionRes matnrVersion, List<Produce_Scheduling> schedulings, Sys_User currentUser, int caStatus, string caSequenceNo)
        {
            return new Produce_Report
            {
                Id = Guid.NewGuid().ToString(),
                ProduceSchedulingId = scheduling.Id,
                ProduceReportNo = "RI" + DateTime.Now.ToString("yyyyMMddHHmmss") + "_" + schedulings.IndexOf(scheduling),
                SerialNo = scheduling.SerialNo,
                ProduceOrderNo = schedulingDetail.ProduceOrderNo,
                MaterialCode = schedulingDetail.MaterialCode,
                ProduceScheduler = currentUser.LoginAccount,
                OrderType = scheduling.OrderType,
                OrderQty = scheduling.Quantity,
                Unit = scheduling.Unit,
                ReportTotal = 0,
                QualifiedQty = 0,
                UnqualifiedQty = 0,
                AssemblDate = scheduling.ProduceSchedulingDate,
                CustomerName = scheduling.CustomerName,
                SapNo = scheduling.SaleSapNo,
                SapLine = scheduling.SaleSapLine.ToString(),
                WorkCenterName = matnrVersion.KTEXT,
                WorkCenterCode = matnrVersion.ARBPL,
                IsCompleted = false,
                IsPosted = false,
                IsDelete = false,
                CaStatus = caStatus,
                CaSequenceNo = caSequenceNo,
                CUser = currentUser.LoginAccount,
                CTime = DateTime.Now
            };
        }

        /// <summary>
        /// 创建报工站点明细
        /// </summary>
        private void CreateReportStationDetails(Produce_Report report, Produce_SchedulingDetail schedulingDetail,
            Dictionary<string, List<MD_ReportStation>> reportStationDict, List<Produce_ReportDetail> reportDetails, Sys_User currentUser)
        {
            if (reportStationDict.ContainsKey(report.WorkCenterCode))
            {
                var reportStations = reportStationDict[report.WorkCenterCode];
                foreach (var reportStation in reportStations)
                {
                    var reportDetail = new Produce_ReportDetail
                    {
                        Id = Guid.NewGuid().ToString(),
                        Pid = report.Id,
                        MaterialCode = schedulingDetail.MaterialCode,
                        WorkCenterName = reportStation.WorkCenterName,
                        WorkCenterCode = reportStation.WorkCenterCode,
                        Status = 2, // 待报工状态
                        EmployeeName = currentUser.LoginAccount,
                        StationName = reportStation.StationName,
                        StationCode = reportStation.StationCode,
                        StationSort = reportStation.StationSort,
                        IsDelete = false,
                        CUser = currentUser.LoginAccount,
                        CTime = DateTime.Now
                    };
                    reportDetails.Add(reportDetail);
                }
            }
        }

        /// <summary>
        /// 执行数据库事务保存
        /// </summary>
        /// <param name="ids">排产ID数组</param>
        /// <param name="produceSchedulingList">排产列表</param>
        /// <param name="schedulingDetails">排产详情列表</param>
        /// <param name="purchaseOrders">采购订单列表</param>
        /// <param name="reports">报工列表</param>
        /// <param name="reportDetails">报工详情列表</param>
        /// <param name="produceMax">生产编号配置</param>
        private void ExecuteDatabaseTransaction(string[] ids, List<Produce_Scheduling> produceSchedulingList,
            List<Produce_SchedulingDetail> schedulingDetails, List<Produce_PurchaseOrder> purchaseOrders,
            List<Produce_Report> reports, List<Produce_ReportDetail> reportDetails, Sys_Dictionary produceMax)
        {
            DbContext.Ado.BeginTran();

            try
            {
                // 设置DbContext
                _dictionaryApp.DbContext = this.DbContext;
                _schedulingDetailApp.DbContext = this.DbContext;
                _purchaseOrderApp.DbContext = this.DbContext;

                // 删除旧的排产详情并插入新的
                _schedulingDetailApp.HardDelete(t => ids.Contains(t.Pid));
                _schedulingDetailApp.Insert(schedulingDetails);

                // 插入采购订单
                _purchaseOrderApp.Insert(purchaseOrders);

                // 插入报工数据
                DbContext.Insertable(reports).ExecuteCommand();
                DbContext.Insertable(reportDetails).ExecuteCommand();

                // 更新排产和配置
                Update(produceSchedulingList);
                _dictionaryApp.Update(produceMax);

                // 更新OMS排产完成时间
                UpdateOmsProduceSchedulingCompleteTime(produceSchedulingList);

                DbContext.Ado.CommitTran();
            }
            catch
            {
                DbContext.Ado.RollbackTran();
                throw;
            }
        }

        /// <summary>
        /// 更新OMS排产完成时间
        /// </summary>
        /// <param name="produceSchedulingList">排产列表</param>
        private void UpdateOmsProduceSchedulingCompleteTime(List<Produce_Scheduling> produceSchedulingList)
        {
            try
            {
                // 获取所有需要更新的OrderId
                var orderIds = produceSchedulingList
                    .Where(p => p.Status == 3 && !string.IsNullOrEmpty(p.OrderId))
                    .Select(p => p.OrderId)
                    .Distinct()
                    .ToList();

                if (orderIds.Count == 0)
                {
                    return;
                }

                // 使用OMS数据库连接更新SD_Host_OrderDetails表
                DbContextForOMS.Updateable<AOS.OMS.Entity.Sale.SD_Host_OrderDetails>()
                    .SetColumns(t => new AOS.OMS.Entity.Sale.SD_Host_OrderDetails
                    {
                        ProduceSchedulingCompleteTime = DateTime.Now
                    })
                    .Where(t => orderIds.Contains(t.Id))
                    .ExecuteCommand();
            }
            catch (Exception ex)
            {
                // 记录错误但不中断主流程，因为这是一个附加功能
                Console.WriteLine($"更新OMS排产完成时间失败: {ex.Message}");
            }
        }

        #endregion

        #region 正式排产确认

        /// <summary>
        /// 正式排产确认
        /// </summary>
        /// <param name="ids">排产ID数组</param>
        /// <param name="resMsg">返回消息</param>
        /// <param name="currentUser">当前用户</param>
        /// <returns>是否成功</returns>
        public bool ConfirmFormalProduce(string[] ids, out string resMsg, Sys_User currentUser)
        {
            resMsg = string.Empty;

            try
            {
                // 1. 获取需要处理的排产数据
                var list = GetListByKeys(ids);
                if (list.Count == 0)
                {
                    resMsg = "未找到需要处理的排产数据";
                    return false;
                }

                // 2. 检查生产线产能上限
                if (!CheckLineCapacity(list, out var capacityWarnings))
                {
                    resMsg = string.Join("\n", capacityWarnings);
                    return false;
                }

                // 3. 获取生产编号配置并初始化
                var produceMax = _dictionaryApp.GetList(t => t.TypeCode == "MD020" && t.EnumKey == 82).ToList()[0];
                produceMax = IsInitProduceMax(produceMax);

                // 4. 处理部件代码生成
                ProcessPartCodeGeneration(list, produceMax);

                // 5. 调用SAP接口创建生产订单和采购订单
                Console.WriteLine("开始调用SAP创建生产订单" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                var createProdOrderRes = CallSapCreateProductionOrder(list);
                Console.WriteLine("结束调用SAP创建生产订单" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                var createPruchase0rderRes = CallSapCreatePurchaseOrder(list);
                Console.WriteLine("结束调用SAP创建采购订单" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));

                // 6. 处理SAP接口响应结果并更新状态
                var (resultMessages, schedulingDetails, purchaseOrders) = ProcessSapResponseAndUpdateStatus(
                    list, createProdOrderRes, createPruchase0rderRes);

                // 7. 创建报工数据
                var (reports, reportDetails) = CreateReportData(list, schedulingDetails, currentUser);
                
                // 更新OMS排产完成时间

                // 8. 执行数据库事务保存
                ExecuteDatabaseTransaction(ids, list, schedulingDetails, purchaseOrders, reports, reportDetails, produceMax);

                // 9. 返回处理结果
                resMsg = string.Join("\n", resultMessages);
                return true;
            }
            catch (Exception ex)
            {
                // 确保事务回滚
                if (DbContext.Ado.IsAnyTran())
                {
                    DbContext.Ado.RollbackTran();
                }

                resMsg = ex.Message;
                return false;
            }
        }

        public Sys_Dictionary IsInitProduceMax(Sys_Dictionary dictionary)
        {
            if (new DateTime().ToString("yyyy") != dictionary.EnumValue)
            {
                dictionary.EnumValue = new DateTime().ToString("yyyy");
                dictionary.EnumValue1 = "00000";
            }

            return dictionary;
        }

        #endregion

        #region Pcs状态更新

        public bool UpdatePcsStatus(PcsStatusUpdateReq req, out string errorMessage)
        {
            errorMessage = "";
            var list = GetList(t => req.Id == t.OrderId).ToList();
            if (list.Count == 0)
            {
                errorMessage = "数据不存在";
                return false;
            }

            var detail = list[0];
            detail.Status = req.Status;
            Update(detail);
            return true;
        }

        #endregion

        #region 查询统计树（排产日期开始）

        public List<TreeNode> GetTreeBySchedulingDate(Produce_SchedulingListReq req, HostOrderTreeReq treeReq)
        {
            var condition = GetCondition(req);
            var resList = new List<TreeNode>();

            if (treeReq.Level == 0)
            {
                var countList = DbContext.Queryable<Produce_Scheduling>()
                    .Where(condition)
                    .Where(t => t.IsDelete == false)
                    .GroupBy(t => t.DeliveryDate)
                    .Select(t => new
                    {
                        t.DeliveryDate,
                        TotalCount = SqlFunc.AggregateCount(1)
                    })
                    .ToList();

                foreach (var count in countList)
                {
                    if (count.DeliveryDate.HasValue)
                    {
                        TreeNode node = new TreeNode();
                        node.Label = DateUtil.ParseStr(count.DeliveryDate) + " (" + count.TotalCount + ")";
                        node.Value = DateUtil.ParseStr(count.DeliveryDate);
                        node.Leaf = count.TotalCount == 0;
                        node.Level = 0;
                        node.Branch = DateUtil.ParseStr(count.DeliveryDate); // 记录日期值
                        resList.Add(node);
                    }
                }
            }
            else if (treeReq.Level == 1)
            {
                // 第1级：按照生产线分组，使用第0级传入的日期值作为筛选条件
                var schedulingDate = treeReq.Value; // 这是排产日期值

                var countList = DbContext.Queryable<Produce_Scheduling>()
                    .Where(condition)
                    .Where(t => t.IsDelete == false
                                && t.Status == 3
                                && t.DeliveryDate >= DateUtil.GetStartTime(DateTime.Parse(schedulingDate))
                                && t.DeliveryDate <= DateUtil.GetEndTime(DateTime.Parse(schedulingDate)))
                    .GroupBy(t => t.ProduceLine)
                    .Select(t => new
                    {
                        t.ProduceLine,
                        TotalCount = SqlFunc.AggregateCount(1)
                    })
                    .ToList();

                foreach (var count in countList)
                {
                    if (!string.IsNullOrEmpty(count.ProduceLine))
                    {
                        TreeNode node = new TreeNode();
                        node.Label = count.ProduceLine + " (" + count.TotalCount + ")";
                        node.Value = schedulingDate + "|" + count.ProduceLine; // 组合日期和生产线
                        node.Leaf = count.TotalCount == 0;
                        node.Level = 1;
                        node.Branch = schedulingDate; // 保存日期信息
                        resList.Add(node);
                    }
                }
            }
            else if (treeReq.Level == 2)
            {
                // 解析日期和生产线信息
                var parts = treeReq.Value.Split('|');
                if (parts.Length != 2) return resList;

                var schedulingDate = parts[0]; // 排产日期
                var produceLine = parts[1]; // 生产线

                var countList = DbContext.Queryable<Produce_Scheduling>()
                    .Where(condition)
                    .Where(t => t.IsDelete == false
                                && t.Status == 3
                                && t.DeliveryDate >= DateUtil.GetStartTime(DateTime.Parse(schedulingDate))
                                && t.DeliveryDate <= DateUtil.GetEndTime(DateTime.Parse(schedulingDate))
                                && t.ProduceLine == produceLine)
                    .GroupBy(t => t.CustomerName)
                    .Select(t => new
                    {
                        t.CustomerName,
                        TotalCount = SqlFunc.AggregateCount(1)
                    })
                    .ToList();

                foreach (var count in countList)
                {
                    TreeNode node = new TreeNode();
                    node.Label = count.CustomerName + " (" + count.TotalCount + ")";
                    // 组合日期、生产线和客户名称
                    node.Value = schedulingDate + "|" + produceLine + "|" + count.CustomerName;
                    node.Leaf = count.TotalCount == 0;
                    node.Level = 2;
                    node.Branch = schedulingDate + "|" + produceLine; // 保存日期和生产线信息
                    resList.Add(node);
                }
            }
            else if (treeReq.Level == 3)
            {
                // 解析日期、生产线和客户名称信息
                var parts = treeReq.Value.Split('|');
                if (parts.Length != 3) return resList;

                var schedulingDate = parts[0]; // 排产日期
                var produceLine = parts[1]; // 生产线
                var customerName = parts[2]; // 客户名称

                var detailList = DbContext.Queryable<Produce_Scheduling>()
                    .Where(condition)
                    .Where(t => t.IsDelete == false
                                && t.Status == 3
                                && t.CustomerName == customerName
                                && t.ProduceLine == produceLine
                                && t.DeliveryDate >= DateUtil.GetStartTime(DateTime.Parse(schedulingDate))
                                && t.DeliveryDate <= DateUtil.GetEndTime(DateTime.Parse(schedulingDate)))
                    .ToList();

                foreach (var detail in detailList)
                {
                    TreeNode node = new TreeNode();
                    node.Label = detail.ContractNo;
                    node.Value = detail.Id;
                    node.Leaf = true;
                    node.Level = 3;
                    node.Branch = schedulingDate + "|" + produceLine + "|" + customerName;
                    resList.Add(node);
                }
            }

            return resList;
        }

        #endregion

        #region 出厂编号生成相关辅助方法

        /// <summary>
        /// 生成出厂编号
        /// </summary>
        /// <param name="produceList">排产列表</param>
        private void GenerateFactoryNumbers(List<Produce_Scheduling> produceList)
        {
            // 获取出厂编号配置
            var factoryNoConfig = _dictionaryApp.GetList(t => t.TypeCode == "FactoryNoConfig").ToList().FirstOrDefault();
            if (factoryNoConfig == null)
            {
                // 如果没有配置，创建默认配置
                factoryNoConfig = new Sys_Dictionary
                {
                    DictionaryID = Guid.NewGuid().ToString(),
                    TypeCode = "FactoryNoConfig",
                    EnumValue = DateTime.Now.Year.ToString(),
                    EnumValue1 = "0001",
                    Remark = "出厂编号生成配置",
                    IsDelete = false,
                    CTime = DateTime.Now,
                    CUser = "System"
                };
                _dictionaryApp.Insert(factoryNoConfig);
            }

            // 按装配日期分组生成出厂编号
            var assemblyDateGroups = produceList.GroupBy(t => t.PlanAssemblyDate?.Date ?? DateTime.Now.Date).OrderBy(g => g.Key);

            foreach (var group in assemblyDateGroups)
            {
                var assemblyDate = group.Key;
                var ordersInGroup = group.OrderBy(t => t.ProduceSort).ToList();

                // 生成当天的出厂编号前缀：年份 + 月日（YYYYMMDD格式的后6位）
                var datePrefix = assemblyDate.ToString("yyMMdd");

                // 获取当天已存在的最大出厂编号序号
                var existingFactoryNos = GetList(t => t.PlanAssemblyDate.HasValue
                                                      && t.PlanAssemblyDate.Value.Date == assemblyDate
                                                      && !string.IsNullOrEmpty(t.FactoryNo)
                                                      && t.FactoryNo.StartsWith(datePrefix)
                                                      && t.IsDelete == false)
                    .Select(t => t.FactoryNo)
                    .ToList();

                var maxSequence = 0;
                if (existingFactoryNos.Any())
                {
                    foreach (var factoryNo in existingFactoryNos)
                    {
                        if (factoryNo.Length >= 10) // datePrefix(6位) + sequence(4位)
                        {
                            var sequencePart = factoryNo.Substring(6, 4);
                            if (int.TryParse(sequencePart, out var sequence))
                            {
                                maxSequence = Math.Max(maxSequence, sequence);
                            }
                        }
                    }
                }

                // 为当前分组的订单生成出厂编号
                for (int i = 0; i < ordersInGroup.Count; i++)
                {
                    var order = ordersInGroup[i];
                    if (string.IsNullOrEmpty(order.FactoryNo)) // 只为没有出厂编号的订单生成
                    {
                        maxSequence++;
                        order.FactoryNo = $"{datePrefix}{maxSequence:D4}";
                    }
                }
            }
        }

        #endregion

        #region 排产序号生成相关辅助方法

        /// <summary>
        /// 处理排产序号和批次计算（用于排产序号生成接口）
        /// </summary>
        /// <param name="produceList">排产列表</param>
        /// <param name="isRegenerate">是否重新生成序号</param>
        private void ProcessProductionSortingAndBatchingForGeneration(List<Produce_Scheduling> produceList, bool isRegenerate)
        {
            // 获取客户优先级配置
            var customerCodes = produceList.Select(t => t.CustomerCode).Distinct().ToList();
            var customerProduces = _customerProduceApp.GetList(t => customerCodes.Contains(t.CustomerCode)).ToList();
            var customerProduceMap = customerProduces.ToDictionary(t => t.CustomerCode, t => t.ProduceSort);

            // 按装配日期分组处理
            var assemblyDateGroups = produceList.GroupBy(t => t.PlanAssemblyDate?.Date ?? DateTime.Now.Date).OrderBy(g => g.Key);

            foreach (var group in assemblyDateGroups)
            {
                ProcessAssemblyDateGroupForGeneration(group.ToList(), customerProduceMap, isRegenerate);
            }
        }

        /// <summary>
        /// 处理装配日期分组的排产逻辑（用于排产序号生成接口）
        /// </summary>
        /// <param name="ordersInGroup">分组内的订单列表</param>
        /// <param name="customerProduceMap">客户优先级映射</param>
        /// <param name="isRegenerate">是否重新生成序号</param>
        private void ProcessAssemblyDateGroupForGeneration(List<Produce_Scheduling> ordersInGroup,
            Dictionary<string, decimal> customerProduceMap, bool isRegenerate)
        {
            // 设置客户优先级
            foreach (var order in ordersInGroup)
            {
                if (customerProduceMap.TryGetValue(order.CustomerCode, out var priority))
                {
                    order.ProduceSort = priority;
                }
            }

            // 按优先级、客户名称、机型排序
            ordersInGroup = ordersInGroup.OrderBy(t => t.ProduceSort)
                .ThenBy(t => t.CustomerName)
                .ThenBy(t => t.SapProductModel ?? t.ProduceModel)
                .ToList();

            // 获取线体批次配置
            var lineBatchConfigs = _lineBatchApp.GetList(t => ordersInGroup.Select(o => o.ProduceLine).Contains(t.LineName))
                .ToList()
                .ToDictionary(t => t.LineName, t => t);

            // 按线体分组处理
            var lineGroups = ordersInGroup.GroupBy(t => t.ProduceLine);
            foreach (var lineGroup in lineGroups)
            {
                ProcessProductionLineGroupForGeneration(lineGroup.ToList(), lineGroup.Key, lineBatchConfigs, isRegenerate);
            }
        }

        /// <summary>
        /// 处理生产线分组的排产逻辑（用于排产序号生成接口）
        /// </summary>
        /// <param name="lineOrders">生产线内的订单列表</param>
        /// <param name="lineName">生产线名称</param>
        /// <param name="lineBatchConfigs">线体批次配置</param>
        /// <param name="isRegenerate">是否重新生成序号</param>
        private void ProcessProductionLineGroupForGeneration(List<Produce_Scheduling> lineOrders, string lineName,
            Dictionary<string, MD_LineBatch> lineBatchConfigs, bool isRegenerate)
        {
            var lineConfig = lineBatchConfigs.ContainsKey(lineName) ? lineBatchConfigs[lineName] : null;
            var batchSize = lineConfig?.BatchNo ?? 10;
            var startTime = lineConfig?.StartTime ?? 0;

            // 按装配日期分组
            var assemblyDateGroups = lineOrders.GroupBy(t => t.PlanAssemblyDate?.Date ?? DateTime.Now.Date);

            foreach (var assemblyDateGroup in assemblyDateGroups)
            {
                ProcessAssemblyDateOrdersWithinLineForGeneration(assemblyDateGroup.ToList(), assemblyDateGroup.Key,
                    lineName, batchSize, startTime, isRegenerate);
            }
        }

        /// <summary>
        /// 处理生产线内按装配日期分组的订单序号和入库时间计算（用于排产序号生成接口）
        /// </summary>
        /// <param name="assemblyDateOrders">装配日期内的订单列表</param>
        /// <param name="assemblyDate">装配日期</param>
        /// <param name="lineName">生产线名称</param>
        /// <param name="batchSize">批次大小</param>
        /// <param name="startTime">开始时间（分钟）</param>
        /// <param name="isRegenerate">是否重新生成序号</param>
        private void ProcessAssemblyDateOrdersWithinLineForGeneration(List<Produce_Scheduling> assemblyDateOrders, DateTime assemblyDate,
            string lineName, int batchSize, int startTime, bool isRegenerate)
        {
            // 获取当前处理订单的ID列表
            var currentProcessingIds = assemblyDateOrders.Select(t => t.Id).ToList();

            // 获取该装配日期在该生产线的所有已排产记录（排除当前操作的数据）
            var existingAssemblyOrders = GetList(t => t.Status > 0
                                                      && t.ProduceLine == lineName
                                                      && t.PlanAssemblyDate.HasValue
                                                      && t.PlanAssemblyDate.Value.Date == assemblyDate
                                                      && !currentProcessingIds.Contains(t.Id)
                                                      && t.IsDelete == false).ToList();

            // 计算该装配日期在该生产线的起始序号、批次和入库时间基准
            var assemblyMaxSort = 0m;
            var assemblyMaxBatch = 0;
            var maxInStoreTimeToday = DateTime.MinValue;

            if (!isRegenerate && existingAssemblyOrders.Any())
            {
                // 如果不是重新生成，基于现有数据计算起始值
                assemblyMaxSort = existingAssemblyOrders.Max(t => t.ProduceSort ?? 0);
                assemblyMaxBatch = existingAssemblyOrders.Max(t => t.ProduceBatch ?? 0);

                // 获取当天该生产线的最大入库时间作为基准
                if (existingAssemblyOrders.Any(t => t.InStoreTime.HasValue))
                {
                    maxInStoreTimeToday = existingAssemblyOrders.Where(t => t.InStoreTime.HasValue)
                        .Max(t => t.InStoreTime.Value);
                }
            }

            // 计算批次开始时间 - 基于装配日期和线体配置
            var batchStartTime = assemblyDate;
            if (startTime > 0)
            {
                batchStartTime = batchStartTime.AddMinutes(startTime);
            }

            // 如果当天没有已排产记录，使用线体配置的开始时间作为基准
            if (maxInStoreTimeToday == DateTime.MinValue)
            {
                maxInStoreTimeToday = batchStartTime;
            }

            // 设置序号、批次和入库时间
            CalculateOrderSequenceAndInStoreTimeForGeneration(assemblyDateOrders, assemblyMaxSort, assemblyMaxBatch,
                maxInStoreTimeToday, batchSize, existingAssemblyOrders.Any() && !isRegenerate);
        }

        /// <summary>
        /// 计算订单序号、批次和入库时间（用于排产序号生成接口）
        /// </summary>
        /// <param name="assemblyDateOrders">装配日期内的订单列表</param>
        /// <param name="assemblyMaxSort">当前最大序号</param>
        /// <param name="assemblyMaxBatch">当前最大批次</param>
        /// <param name="maxInStoreTimeToday">当天最大入库时间</param>
        /// <param name="batchSize">批次大小</param>
        /// <param name="hasExistingOrders">是否存在历史订单</param>
        private void CalculateOrderSequenceAndInStoreTimeForGeneration(List<Produce_Scheduling> assemblyDateOrders,
            decimal assemblyMaxSort, int assemblyMaxBatch, DateTime maxInStoreTimeToday,
            int batchSize, bool hasExistingOrders)
        {
            for (int i = 0; i < assemblyDateOrders.Count; i++)
            {
                var order = assemblyDateOrders[i];

                // 序号累加 - 基于历史数据的最大序号继续增加
                assemblyMaxSort++;
                order.ProduceSort = assemblyMaxSort;

                // 计算批次 - 基于该装配日期的现有最大批次
                var batchIndex = i / batchSize;
                order.ProduceBatch = assemblyMaxBatch + batchIndex + 1;

                // 计算入库时间
                order.InStoreTime = CalculateInStoreTimeForGeneration(i, assemblyDateOrders, maxInStoreTimeToday, batchSize, hasExistingOrders);
            }
        }

        /// <summary>
        /// 计算单个订单的入库时间（用于排产序号生成接口）
        /// </summary>
        /// <param name="orderIndex">订单在列表中的索引</param>
        /// <param name="assemblyDateOrders">装配日期内的订单列表</param>
        /// <param name="maxInStoreTimeToday">当天最大入库时间</param>
        /// <param name="batchSize">批次大小</param>
        /// <param name="hasExistingOrders">是否存在历史订单</param>
        /// <returns>计算得出的入库时间</returns>
        private DateTime CalculateInStoreTimeForGeneration(int orderIndex, List<Produce_Scheduling> assemblyDateOrders,
            DateTime maxInStoreTimeToday, int batchSize, bool hasExistingOrders)
        {
            // 根据线体批次配置计算每台设备的时间间隔（分钟）
            // 假设每批次在1小时内完成，平均分配给每台设备
            var minutesPerUnit = batchSize > 0 ? 60.0 / batchSize : 10.0; // 默认每台设备10分钟

            // 限制时间间隔的合理范围（1-60分钟）
            if (minutesPerUnit < 1) minutesPerUnit = 1;
            if (minutesPerUnit > 60) minutesPerUnit = 60;

            // 基于当天最大入库时间累加计算新的入库时间
            if (orderIndex == 0)
            {
                // 第一台设备：如果当天有历史记录，从最大时间后的下一个时间间隔开始
                // 如果当天没有历史记录，使用配置的开始时间
                if (hasExistingOrders)
                {
                    return maxInStoreTimeToday.AddMinutes(minutesPerUnit);
                }
                else
                {
                    return maxInStoreTimeToday;
                }
            }
            else
            {
                // 后续设备基于前一台设备的时间累加
                var previousOrder = assemblyDateOrders[orderIndex - 1];
                return previousOrder.InStoreTime?.AddMinutes(minutesPerUnit) ??
                       maxInStoreTimeToday.AddMinutes((orderIndex + 1) * minutesPerUnit);
            }
        }

        #endregion
    }
}