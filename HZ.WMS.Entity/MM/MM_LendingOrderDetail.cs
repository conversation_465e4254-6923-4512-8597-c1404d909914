using SqlSugar;
using System;
using System.ComponentModel;

namespace HZ.WMS.Entity.MM
{
    /// <summary>
    /// 借出单明细
    /// </summary>
    public class MM_LendingOrderDetail : BaseEntity
    {
        /// <summary>
        /// 明细主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Description("明细ID")]
        public string DetailID { get; set; }

        /// <summary>
        /// 借出单号
        /// </summary>
        [Description("借出单号")]
        public string DocNum { get; set; }

        /// <summary>
        /// 借出单行号
        /// </summary>
        [Description("借出单行号")]
        public int? Line { get; set; }

        /// <summary>
        /// 公司代码
        /// </summary>
        [Description("公司代码")]
        public string CompanyCode { get; set; }

        /// <summary>
        /// 工厂代码
        /// </summary>
        [Description("工厂代码")]
        public string FactoryCode { get; set; }

        /// <summary>
        /// 移动类型
        /// </summary>
        [Description("移动类型")]
        public string MovementType { get; set; }

        /// <summary>
        /// 移动类型名称
        /// </summary>
        [Description("移动类型名称")]
        public string MovementTypeName { get; set; }

        /// <summary>
        /// 成本中心
        /// </summary>
        [Description("成本中心")]
        public string CostCenter { get; set; }

        /// <summary>
        /// 成本中心名称
        /// </summary>
        [Description("成本中心名称")]
        public string CostCenterName { get; set; }

        /// <summary>
        /// 总账科目
        /// </summary>
        [Description("总账科目")]
        public string LedgerType { get; set; }

        /// <summary>
        /// 总账科目名称
        /// </summary>
        [Description("总账科目名称")]
        public string LedgerTypeName { get; set; }

        /// <summary>
        /// 出厂编号
        /// </summary>
        [Description("出厂编号")]
        public string BarCode { get; set; }


        /// <summary>
        /// 物料编码
        /// </summary>
        [Description("物料编码")]
        public string ItemCode { get; set; }

        /// <summary>
        /// 物料描述
        /// </summary>
        [Description("物料描述")]
        public string ItemName { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量")]
        public decimal? Qty { get; set; }

        /// <summary>
        /// 归还数量
        /// </summary>
        [Description("归还数量")]
        public decimal? ReturnQty { get; set; }
        /// <summary> 
        /// 单位
        /// </summary> 
        [Description("单位")]
        public string Unit { get; set; }

        /// <summary> 
        /// 借出仓库编号
        /// </summary> 
        [Description("借出仓库编号")]
        public string WhsCode { get; set; }

        /// <summary> 
        /// 借出仓库
        /// </summary> 
        [Description("借出仓库")]
        public string WhsName { get; set; }

        /// <summary> 
        /// 区域编号
        /// </summary> 
        [Description("区域编号")]
        public string RegionCode { get; set; }

        /// <summary> 
        /// 区域
        /// </summary> 
        [Description("区域")]
        public string RegionName { get; set; }

        /// <summary> 
        /// 库位编号
        /// </summary> 
        [Description("库位编号")]
        public string BinLocationCode { get; set; }

        /// <summary> 
        /// 库位
        /// </summary> 
        [Description("库位")]
        public string BinLocationName { get; set; }

        /// <summary> 
        /// 销售订单
        /// </summary> 
        [Description("销售订单")]
        public string SaleNum { get; set; }

        /// <summary> 
        /// 销售订单项目
        /// </summary> 
        [Description("销售订单项目")]
        public int? SaleLine { get; set; }

        /// <summary> 
        /// 是否过账 1:已过帐 0：未过账
        /// </summary> 
        [Description("是否过账")]
        public bool? IsPosted { get; set; }

        /// <summary> 
        /// 过账人
        /// </summary> 
        [Description("过账人")]
        public string PostUser { get; set; }

        /// <summary> 
        /// 过账时间
        /// </summary> 
        [Description("过账时间")]
        public DateTime? PostTime { get; set; }

        /// <summary> 
        /// SAP物料凭证单号
        /// </summary> 
        [Description("SAP物料凭证单号")]
        public string SapDocNum { get; set; }

        /// <summary> 
        /// SAP物料凭证行号
        /// </summary> 
        [Description("SAP物料凭证行号")]
        public int? SapLine { get; set; }

        /// <summary>
        /// 评估类型
        /// </summary>
        [Description("评估类型")]
        public string AssessType { get; set; }

        /// <summary>
        /// 归还日期
        /// </summary>
        [Description("归还日期")]
        public DateTime? ReturnDate { get; set; }

        /// <summary> 
        /// 归还仓库编号
        /// </summary> 
        [Description("归还仓库编号")]
        public string ReturnWhsCode { get; set; }

        /// <summary> 
        /// 归还仓库
        /// </summary> 
        [Description("归还仓库")]
        public string ReturnWhsName { get; set; }




    }
}
