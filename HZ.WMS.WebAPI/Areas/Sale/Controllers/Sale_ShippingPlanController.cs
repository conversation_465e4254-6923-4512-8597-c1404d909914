using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Runtime.Remoting;
using System.Threading;
using System.Web;
using System.Web.Http;
using AOS.OMS.Entity.Sale;
using DevExpress.DataAccess.Sql;
using DevExpress.XtraReports.UI;
using HZ.Core.Http;
using HZ.Core.Office;
using HZ.WMS.Application;
using HZ.WMS.Application.SD;
using HZ.WMS.Entity.SD;
using HZ.WMS.Entity.Sys;
using HZ.WMS.WebAPI.Controllers;

namespace HZ.WMS.WebAPI.Areas.SD.Controllers
{
    /// <summary>
    /// 发运计划
    /// </summary>
    public class Sale_ShippingPlanController : ApiBaseController
    {
        
        #region 初始化

        private static readonly ConcurrentDictionary<string, object> resourceLocks = new ConcurrentDictionary<string, object>();
        private Sale_ShippingPlanApp _app = new Sale_ShippingPlanApp();

        #endregion

        #region 查询分页列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="page"></param>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult GetPageList([FromUri] Pagination page, [FromBody] Sale_ShippingPlanListReq req)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetPageList(page, req);
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 查询统计树

        [HttpGet]
        public IHttpActionResult GetTree([FromUri] Sale_ShippingPlanListReq req, [FromUri] HostOrderTreeReq treeReq)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.GetTree(req, treeReq);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="DocNums">根据单号进行删除</param>
        /// <returns></returns>
        [HttpDelete]
        //[AllowAnonymous]
        public IHttpActionResult Delete([FromBody] string[] ids)
        {
            //[FromBody]string[] ids
            var result = new ResponseData();
            try
            {
                string error_message = "";
                //GetCurrentUser().LoginAccount
                int delCount = _app.DeleteByKeys(ids, GetCurrentUser().LoginAccount);
                if (delCount == 0)
                {
                    result.Code = (int)WMSStatusCode.UnHandledException;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 完成

        /// <summary>
        /// 完成
        /// </summary>
        /// <param name="DocNums">手动完成</param>
        /// <returns></returns>
        [HttpGet]
        //[AllowAnonymous]
        public IHttpActionResult Finish([FromUri] string[] DocNums)
        {
            //[FromBody]string[] ids
            var result = new ResponseData();
            try
            {
                string error_message = "";
                //GetCurrentUser().LoginAccount
                bool bFinish = _app.Finish(DocNums, GetCurrentUser().LoginAccount, out error_message);
                if (!bFinish || !string.IsNullOrEmpty(error_message))
                {
                    result.Code = (int)WMSStatusCode.UnHandledException;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 导出

        /// <summary>
        /// 导出
        /// </summary>
        /// <param name="keyword"></param>
        /// <param name="dateValue"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] Sale_ShippingPlanListReq req, [FromUri] int type)
        {
            var result = new ResponseData();
            try
            {
                string modelName = "";
                var itemsDatas = _app.GetExportInfo(req);
                var columns = ExcelService.FetchDefaultColumnList<Sale_ShippingPlan>();
                string[] ignoreField = new string[]
                {
                    "ShippingPlanDetailID", "Line", "SalesType", "SalesOrganization",
                    "CreationDate", "Time", "VoucherDate", "DistributionChannels",
                    "ProductGroup", "Project", "ProjectCategory",
                    "IsDelete", "MUser", "MTime", "DUser",
                    "DTime", "Unit", "WhsCode", "WhsName", "DeliveryUser", "DeliveryUserName"
                };
                var ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (var userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                if (type == 2)
                {
                    // 按物流供应商导出
                    var dictionary = itemsDatas.GroupBy(t => t.CustomerCode).ToDictionary(t => t.Key, t => t.ToList());
                    return ExportToExcelFileByGroup(dictionary, columns);
                }
                if (type == 3)
                {
                    // 按结算地址导出
                    var dictionary = itemsDatas.GroupBy(t => t.SettlementAdd).ToDictionary(t => t.Key, t => t.ToList());
                    return ExportToExcelFileByGroup(dictionary, columns);
                }
                // 全部导出
                return ExportToExcelFile(itemsDatas, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #region 打印

        /// <summary>
        /// 打印
        /// </summary>
        /// <param name="docNums">单号数组</param>
        /// <param name="templateCode">模板</param>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IHttpActionResult Print([FromUri] string[] docNums, [FromUri] string PrintType)
        {
            var result = new ResponseData();
            string templateCode = "销售发运计划.repx";
            if (PrintType == "物流供应商")
                templateCode = "销售发运计划(物流).repx";
            if (docNums == null || docNums.Length == 0 || string.IsNullOrEmpty(templateCode))
            {
                result.Code = (int)WMSStatusCode.Failed;
            }
            else
            {
                //MD_LabelTempleteApp _labelTemplateApp = new MD_LabelTempleteApp();
                //MD_LabelTemplete template = _labelTemplateApp.GetFirstEntity(x => x.Remark == templateCode);
                //if (template == null)
                //{
                //    result.Code = (int)WMSStatusCode.Failed;
                //}
                //else
                //{
                XtraReport report =
                    XtraReport.FromFile(HttpContext.Current.Server.MapPath("~/") + ConfigurationManager.AppSettings["LabelTemplateUploadPath"]?.ToString() + templateCode, true);
                SqlDataSource dataSource = report.DataSource as SqlDataSource;
                //switch (templateCode)
                //{
                //    case "PP_BarCodeReturn":
                //    case "PP_BarCode":
                //    case "MM_BarCode":
                QueryParameter parameter = new QueryParameter()
                {
                    Name = "docNum",
                    Type = typeof(List<string>),
                    Value = docNums.ToList()
                };

                dataSource.Queries[0].Parameters[0] = parameter;
                //dataSource.Fill();
                return base.PrintToPDF(report);

                //default:
                //    break;
            }

            return Json(result);
        }

        #endregion

        #region 获取系统生成的单号

        /// <summary>
        /// 获取系统生成的单号
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public IHttpActionResult GetDocNum()
        {
            var result = new ResponseData();
            try
            {
                result.Data = base.GenerateDocNum(DocType.SD, DocFixedNumDef.SD_ShippingPlan);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion
 
        #region 查询没有发货的销售发运计划信息

        /// <summary>
        /// 查询销售发运计划信息
        /// </summary>
        /// <param name="DocNum">计划单号</param>
        /// <returns></returns>
        [HttpGet]
        //[AllowAnonymous]
        public IHttpActionResult GetNoDeliveryShippingPlan([FromUri] string DeliveryUser)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetNoDeliveryShippingPlan(DeliveryUser);
                if (itemsData != null && itemsData.Count > 0)
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Data = itemsData;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = "";
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region OMS同步WMS发运计划保存

        /// <summary>
        /// OMS同步WMS发运计划保存
        /// </summary>
        /// <param name="ShiPingList">参数汇总</param>
        /// <returns></returns>
        [HttpPost]
        //[AllowAnonymous]
        public IHttpActionResult SaveForOMS([FromBody] List<Sale_ShippingForOms> ShiPingList)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                List<string> salesOrderNumbers = ShiPingList.Select(w => w.SaleSapNo).Distinct().ToList();
                List<object> lockedResources = new List<object>();
                try
                {
                    // 批量锁定资源
                    foreach (string resource in salesOrderNumbers)
                    {
                        var resourceLock = LockResource(resource);
                        if (resourceLock != null)
                        {
                            lockedResources.Add(resourceLock);
                        }
                        else
                        {
                            // 处理无法获取锁的情况
                            throw new ServerException("无法获取锁");
                        }
                    }

                    // 在批量锁定之后，执行需要锁定资源的操作
                    bool bSubmit = _app.SaveForOMS(ShiPingList, out error_message);
                    if (!bSubmit)
                    {
                        result.Code = (int)WMSStatusCode.Failed;
                        result.Message = error_message;
                    }
                    else
                    {
                        result.Code = (int)WMSStatusCode.Success;
                        result.Message = error_message;
                    }
                }
                finally
                {
                    // 解锁资源
                    foreach (object resourceLock in lockedResources)
                    {
                        UnlockResource(resourceLock);
                    }
                }

                // 在资源使用完成后，释放不再需要的锁对象
                foreach (string resource in salesOrderNumbers)
                {
                    ReleaseResourceLock(resource);
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        private static object LockResource(string resourceName)
        {
            object resourceLock = resourceLocks.GetOrAdd(resourceName, key => new object());

            bool lockAcquired = false;
            lock (resourceLock)
            {
                while (!lockAcquired)
                {
                    Monitor.TryEnter(resourceLock, TimeSpan.FromMilliseconds(100), ref lockAcquired);
                    if (!lockAcquired)
                    {
                        // 无法获取锁，等待
                        Monitor.Wait(resourceLock);
                    }
                }

                Console.WriteLine($"Locking resource: {resourceName}");
            }

            return resourceLock;
        }

        private static void UnlockResource(object resourceLock)
        {
            if (resourceLock != null)
            {
                Console.WriteLine($"Unlocking resource: {resourceLock.GetHashCode()}");
                Monitor.Exit(resourceLock);
            }
        }

        private static void ReleaseResourceLock(string resourceName)
        {
            resourceLocks.TryRemove(resourceName, out _);
        }

        #endregion

        #region 设置交货仓库

        /// <summary>
        /// 设置交货仓库
        /// </summary>
        /// <param name="req">参数汇总</param>
        /// <returns></returns>
        [HttpPost]
        //[AllowAnonymous]
        public IHttpActionResult SyncSaleDelivery([FromBody] SyncSaleDeliveryReq req)
        {
            var result = new ResponseData();
            try
            {
                string errorMessage = "";
                bool bSubmit = _app.SyncSaleDelivery(req, GetCurrentUser(), out errorMessage);
                if (!bSubmit)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = errorMessage;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = errorMessage;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 过账

        /// <summary>
        /// 过账
        /// </summary>
        /// <param name="DocNums">交货单号</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult DoPost([FromBody] string[] ids)
        {
            var result = new ResponseData();
            try
            {
                Sys_User sysUser = GetCurrentUser();
                string error_message = "";
                var details = _app.GetList(x => ids.Contains(x.Id) && x.IsPosted == false).ToList();
                bool postResult = _app.DoPost(details, sysUser.LoginAccount, out error_message);
                if (!postResult) //过账失败==2000，前台处理过账失败后重新刷新界面信息
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                    result.MessageParam = 2010;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2000;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 冲销过账

        /// <summary>
        /// 冲销过账
        /// </summary>
        /// <param name="entities">接收的集合</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult PassPost([FromBody] string[] ids)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                Sys_User currentUser = GetCurrentUser();

                bool bPost = _app.PassPost(ids, currentUser.LoginAccount, out error_message);
                if (!bPost) //过账失败==2000，前台处理过账失败后重新刷新界面信息
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                    result.MessageParam = 2010;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = error_message;
                    result.MessageParam = 2000;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion
        
        #region 上传SRM

        /// <summary>
        /// 上传SRM
        /// </summary>
        /// <param name="entities">集合</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult UploadSRM([FromBody]List<Sale_ShippingPlan> entities)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                Sys_User currentUser = GetCurrentUser();
                bool bSubmit = _app.UploadSRM(entities, currentUser.LoginAccount, out error_message);
                if (!bSubmit || !string.IsNullOrEmpty(error_message))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }

        #endregion
        
    }
}